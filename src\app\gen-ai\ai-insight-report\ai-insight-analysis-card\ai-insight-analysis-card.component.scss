@use "../../../../assets/ifp-styles/abstracts/index" as *;
:host {
  display: flex;
}

.ifp-ai-insight-card {

  width: 100%;
  background-color: $ifp-color-grey-18;
  position: relative;
  padding: $spacer-4;
  border: 1px solid $ifp-color-grey-7;
  border-radius: 20px;
  display: flex;
  flex-direction: column;
  &__title {
    font-weight: $fw-bold;
    margin-bottom: $spacer-2;
    font-size:$ifp-fs-4 ;
  }
  &__sec-2  {
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: $ifp-color-green-lite ;
    color:$ifp-color-green-normal;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    min-width: 40px;
    font-weight: $fw-bold;
    &--red {
      background-color: $ifp-color-red-lite  ;
      color:$ifp-color-red-normal;
      .ifp-ai-insight-card__icon-arrow {
        transform: rotateX(180deg);
      }

    }
  }
  &__headi
  &__icon-arrow  {
    font-weight: $fw-bold;
    font-size: $ifp-fs-6;
  }
  &__sec-3 {
    background-color: $ifp-color-green-light;
    position: absolute;
    font-weight: $fw-bold;
    padding: $spacer-2;
    border-radius: 30px 0 0 30px;
    padding-inline-end: $spacer-3;
    top:10px;
    right: 0;
  }
  &__card {
    background-color: $ifp-color-section-white;
    box-shadow: 0 35px 50px -23px rgb(229 229 229);
    min-height: 200px;
    border-radius: 20px;
    margin-top: auto;
    padding: $spacer-2;
  }
  &__desc {
    margin-bottom: $spacer-4;
    font-size:$ifp-fs-4 ;
  }
  &__chart {
    display: block;
  }
  &--badge {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  &--popup {
    background-color: $ifp-color-transparent;
    display: block;
    .ifp-ai-insight-card {
    &__card {
      width: 50%;
      float: right;
      margin-inline-start: $spacer-2;
    }
  }
  }
  &--shadow {
    border: 0;
  }
}

@include desktop-sm {
  .ifp-ai-insight-card{
    &--popup {
      .ifp-ai-insight-card {
      &__card {
        width: 100%;
        float: none;
        margin-inline-start: $spacer-0;
        margin-bottom: $spacer-3;
      }
    }
  }
  }
}

:host-context(.ifp-dark-theme) {
  .ifp-ai-insight-card {
    background-color: $ifp-color-black-global;
    &__card {
      box-shadow: 0 35px 50px -23px #707b9c14;
    }
  }
}
