import { DomSanitizer, SafeUrl } from '@angular/platform-browser';
import { Component, inject, input, InputSignal, model, OnChanges, OnDestroy, output, SimpleChanges } from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';
import { DxpAccordianBadgeComponent } from "../dxp-accordian-badge/dxp-accordian-badge.component";
import { SubSink } from 'subsink';
import { ApiService } from 'src/app/scad-insights/core/services/api.service';
import { dxpApi } from '../../dxp.constants';
import { Assets } from '../../dxp.interface';

@Component({
  selector: 'ifp-dxp-accordian',
  imports: [TranslateModule, DxpAccordianBadgeComponent],
  templateUrl: './dxp-accordian.component.html',
  styleUrl: './dxp-accordian.component.scss'
})
export class DxpAccordianComponent implements OnChanges, On<PERSON><PERSON>roy {
  private readonly _apiService =  inject(ApiService);
  private readonly _sanitizer: DomSanitizer = inject(DomSanitizer);

  public getSelectedProduct = output<SelectedProduct>();

  public title = input('');
  public id: InputSignal<string> = input.required();
  public entity = input('');
  public image = input('');
  public minimize = model(true);
  public assets: InputSignal<Assets[]> = input.required();
  public dxpImageUrl: SafeUrl | undefined = undefined;

  private readonly _subs: SubSink = new SubSink();

  ngOnChanges(changes: SimpleChanges): void {
    // if(changes['image'] && this.image()) {
    //   this._subs.add(
    //     this._apiService.getMethodRequest(`${dxpApi.image}${this.image()}`).subscribe({
    //       next: (imageResult) => {
    //         this.convertBlobToBase64(imageResult);
    //       },
    //       error: error => {
    //         //
    //       }
    //     })
    //   )
    // }
  }

  convertBlobToBase64(blob: Blob) {
    const reader = new FileReader();
    reader.onloadend = () => {
      this.dxpImageUrl = reader.result as string;
    };
    reader.readAsDataURL(blob);
  }

  onSelectAsset(assetId: string) {
    const data = {
      sourceAssetId: assetId,
      sourceProductId: this.id(),
      title: this.title()
    }
    this.getSelectedProduct.emit(data);
  }

  ngOnDestroy(): void {
    this._subs.unsubscribe();
  }
}

export interface SelectedProduct {
  sourceAssetId: string;
  sourceProductId: string;
  title: string;
}
