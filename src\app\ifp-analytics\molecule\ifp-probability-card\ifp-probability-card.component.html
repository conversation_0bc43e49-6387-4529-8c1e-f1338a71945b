<div class="ifp-pro-card">
  <div class="ifp-pro-card__header-wrapper">
    <h3 class="ifp-pro-card__header-txt">{{'Data type Probabilities' | translate}}</h3>
    <div class="ifp-pro-card__guage-wrapper">
      <ifp-gauge-chart [uniqueColor]="uniqueColor" [uniqueName]="uniqueName" [value]="uniqueValues" [isShowText]="true"></ifp-gauge-chart>
    </div>
  </div>
  <div class="ifp-pro-card__body-wrapper">
    <p class="ifp-pro-card__body-txt">{{"Select a feature to view the probability it's a certain data type." | translate}}</p>
    @if (name) {
    <p class="ifp-pro-card__body-name">{{"Selected Column" | translate}} : {{name}}</p>
    }
    <app-ifp-range-chart [height]="150" class="ifp-dashboard-card__chart" [category]="catogory" [chartValues]="chartValues" ></app-ifp-range-chart>
  </div>
</div>
