import { ChangeDetectionStrategy, Component, Input } from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';
import { IfpGaugeChartComponent } from '../../../scad-insights/ifp-widgets/ifp-molecules/ifp-gauge-chart/ifp-gauge-chart.component';
import { IfpRangeChartComponent } from '../../../scad-insights/ifp-widgets/ifp-molecules/ifp-range-chart/ifp-range-chart.component';

@Component({
    selector: 'ifp-probability-card',
    templateUrl: './ifp-probability-card.component.html',
    styleUrl: './ifp-probability-card.component.scss',
    changeDetection: ChangeDetectionStrategy.OnPush,
    imports: [TranslateModule, IfpGaugeChartComponent, IfpRangeChartComponent]
})
export class IfpProbabilityCardComponent {
  @Input({required: true}) catogory: string[] = [];
  @Input({required: true})  chartValues: number[] = [];
  @Input({required: true}) name!: string;
  @Input({required: true})  uniqueValues!: number;
  @Input({required: true}) uniqueName!: string;
  @Input() uniqueColor = '';
}
