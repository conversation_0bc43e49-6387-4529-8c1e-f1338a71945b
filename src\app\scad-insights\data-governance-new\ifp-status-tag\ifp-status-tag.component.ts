import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TitleCasePipe } from '@angular/common';
import { ChangeDetectionStrategy, Component, Input } from '@angular/core';

@Component({
  selector: 'ifp-status-tag',
  imports: [<PERSON><PERSON><PERSON><PERSON>, Ng<PERSON>lass, TitleCasePipe],
  templateUrl: './ifp-status-tag.component.html',
  styleUrl: './ifp-status-tag.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush

})
export class IfpStatusTagComponent {

  @Input({required: true}) name!: string;
  @Input({required: true}) color!: string;
  @Input({required: false}) enableDot:boolean=true;

  getColorWithOpacity(colorCode: string, opacity: number = 0.16): string {
    // Convert hex to RGB
    const r = parseInt(colorCode.slice(1, 3), 16);
    const g = parseInt(colorCode.slice(3, 5), 16);
    const b = parseInt(colorCode.slice(5, 7), 16);

    // Return the RGBA format with dynamic opacity
    return `rgba(${r}, ${g}, ${b}, ${opacity})`;
  }
}
