import { buttonClass, buttonIconPosition } from './../../../scad-insights/core/constants/button.constants';
import { Component, EventEmitter, Input, Output } from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';
import { IfpButtonComponent } from '../../../scad-insights/ifp-widgets/ifp-atoms/ifp-button/ifp-button.component';
import { CardData, IfpToolCardComponent } from '../../../shared/molecule/ifp-tool-card/ifp-tool-card.component';
import { popupButtonList } from '../../data-prep/ifp-data-prep/ifp-data-prep.constant';
import { Router } from '@angular/router';
@Component({
    selector: 'ifp-prep-popup',
    templateUrl: './ifp-prep-popup.component.html',
    styleUrl: './ifp-prep-popup.component.scss',
    imports: [TranslateModule, IfpButtonComponent, IfpToolCardComponent]
})
export class IfpPrepPopupComponent {

  @Input() heading = '';
  @Input() desc= '';
  @Input() fileName = '';
  @Input() subTitle = '';
  @Input() subDesc = '';
  @Input() buttonText = '';
  @Input() type!:string;
  @Input() popupButtonList: CardData[] = popupButtonList;
  @Input() orButton = false;
  @Input() processButton = false;
  @Input() processText = '';
  @Input() processIcon = 'ifp-icon-rightarrow';
  @Input() processButtonDisabled = true;
  @Input() imageUrl ='../../../../../../assets/images/prep-popup/summery-prop.svg';
  @Input() imageText = 'View Data Exploratory Summary';
  @Input() imageTitlePosition = 'top';
  @Input() processedIcon = true;

  @Output() closePopUp = new EventEmitter();
  @Output() buttonEventValue = new EventEmitter<CardData>();

  public buttonClass=buttonClass;
  public position = buttonIconPosition;


  constructor(private _route:Router) {

  }


  close() {
    this.closePopUp.emit();
  }

  buttonEvent(event: CardData) {
    if (event.url) {
      this._route.navigateByUrl(event.url);
    }
    this.buttonEventValue.emit(event);
  }

  exportClick() {
    this.buttonEventValue.emit( {
      key: 'exportClick',
      name: 'exportClick',
      icon: '',
      url: ''
    });
  }

  processClick() {
    this.buttonEventValue.emit( {
      key: 'processClick',
      name: 'processClick',
      icon: '',
      url: ''
    });
  }

  summery() {
    this.buttonEventValue.emit( {
      key: 'summery',
      name: 'summery',
      icon: '',
      url: ''
    });
  }

  continuesPrep() {
    this.buttonEventValue.emit( {
      key: 'continuesPrep',
      name: 'continuesPrep',
      icon: '',
      url: ''
    });
  }


}

