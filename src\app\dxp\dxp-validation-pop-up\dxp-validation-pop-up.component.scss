@use "../../../assets/ifp-styles/abstracts/index" as *;
.ifp-dxp-validation-pop-up {
  background-color: $ifp-color-white;
  padding: $spacer-3;
  border-radius:10px ;
&__icons-wrapper {
  display: flex;
  justify-content: center;
}
&__text {
  background-color: $ifp-color-white;
  border-radius: 10px;
  padding: $spacer-3;
  @include ifp-scroll-y($ifp-color-grey-11, $ifp-color-grey-1, 8px, 16px);
}
  &__icons {
    display: flex;
    justify-content: center;
    background-color: $ifp-color-orange;
    margin-bottom: $spacer-3;
    border-radius: 50%;
    height: 80px;
    width: 80px;
    align-items: center;
    color: $ifp-color-white;
  }
  &__text-wrapper {
    border-radius: 10px;
    padding: $spacer-4;
    background-color: $ifp-color-pale-grey;
  }
  &__icons-cross {
    display: flex;
    justify-content: end;
  }
  &__title {
    font-size: $ifp-fs-8;
    font-weight: $fw-bold;
    text-align: center;
    margin-bottom: $spacer-3;
  }
  &__desc {
    font-size: $ifp-fs-5;
    color: $ifp-color-grey-14;
    text-align: center;
    margin: $spacer-0 80px;
    margin-bottom: $spacer-4;
  }
  &__btns {
    margin-top: $spacer-4;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  &__btn {
    margin-inline-start: $spacer-3;
  }
  &__save-as-Draft {
    color: $ifp-color-blue-menu;
    cursor: pointer;
  }
  &__success-btns {
    display: flex;
    justify-content: center;
  }
  &--successPopup {
    .ifp-dxp-validation-pop-up {
      &__icons {
         background-color: $ifp-color-green-dark-3;
      }
    }
  }
}
