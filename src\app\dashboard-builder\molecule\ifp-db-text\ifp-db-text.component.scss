@use "../../../../assets/ifp-styles/abstracts/index" as *;

:host {
  display: block;
}

.ifp-db-text {
  &__title-h3 {
    color: $ifp-color-black;
    font-weight: 500;
    margin-bottom: $spacer-2;
    font-size: $ifp-fs-4;
  }

  &__text-area {
    color: $ifp-color-black;
    background-color: $ifp-color-white;
    width: 100%;
    font-size: $ifp-fs-5;
    border-radius: 5px;
    border: 1px solid $ifp-color-grey-7;
    padding: $spacer-2;
    resize: none;
  }
  &__props {
    margin-top: $spacer-4;
    margin-bottom: $spacer-4;
  }
  &__props-item {
    margin-bottom: $spacer-2;
    &:last-child {
      margin-bottom: $spacer-2;
    }
  }
  &__props-heading {
    color: $ifp-color-black;
    font-weight: $fw-semi-bold;
    margin-bottom: $spacer-3;
  }
  &__color-picker {
    background-color: $ifp-color-white;
    cursor: pointer;
  }
  .ql-editor {
    min-height: 100px;
  }
}

.draggable-text-area {
  border: 1px solid $ifp-color-grey-7;
  padding: 10px;
  margin: 10px;
  cursor: grab;
}
