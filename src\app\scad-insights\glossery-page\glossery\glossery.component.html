<div class="ifp-container"><app-ifp-breadcrumbs [pageData]="pageData"></app-ifp-breadcrumbs></div>
<div class="ifp-glossery ifp-module-padding">
  <div class="ifp-container">
    <div class="ifp-glossery__title">
      <h1 class="ifp-glossery__heading"><em class="ifp-glossery__heading-img ifp-icon ifp-icon-glossary"></em>{{'Glossary' |translate}}</h1>
    </div>
    <!-- <ifp-indicator-card-filter-and-search [searchDisable]="true" [buttonsClear]="false" [buttonsApply]="false" [boxTypeSearch]="true"
      [selectedDomainSingle]="isSelectedDomain" [multi]="true" [isInnovative]="false" [type]="'Glossery'"
      [(search)]="onSearch" [clearDisabled]="true" [applyDisabled]="true" (changeDomain)="changeDomain($event)"
      (changeTheme)="changeTheme($event)" (searchChange)="searchResult($event)" [subThemeName]="'Sub Domain'" [themeName]="'Domain'"
      [enableTheme]="true" [domainList]="domainList
      " [themeList]="themeList" [enableSubtheme]="true" [themeListSingle]="selectedTheme" [isSort]="true" [searchPlaceHolder]="'Glossary Search'"
      [sortValue]="sortValue" (sortClicked)="sortClick($event)" [sort]="sort" [selectAllBox]="true" [selectAll]="false"></ifp-indicator-card-filter-and-search> -->


    <div class="ifp-glossery__filter">
      <app-ifp-dropdown [singleDefaultSelect]="true" [selectedValue]="sortValue" [appIfpTooltip]="'Sort by'| translate"
        [extraSpaceTop]="0" [isMulti]="false" [key]="'label'" [iconClass]="'ifp-icon-double-arrow'"
        class="ifp-glossery__filter-item" (dropDownItemClicked)="sortClick($event)" [dropDownItems]="sort"
        [iconEnable]="true"></app-ifp-dropdown>


      <app-ifp-dropdown [boarderBottom]="true" [checkBoxKey]="'domain'" *ngIf="domainList?.length > 0 && isFilter"
        (dropDownItemMultiClicked)="changeDomain($event)" [selectedValues]="isSelectedDomain" [isMulti]="true"
        class="ifp-glossery__filter-item" [placeHolder]="'Select Domain' | translate" [dropDownItems]="domainList"
        [selectAllBox]="true" [isExtense]="true" [isEmitEventAll]="true" [selectAll]="checkSelectAll('domain')"></app-ifp-dropdown>


      <app-ifp-dropdown [boarderBottom]="true" [checkBoxKey]="'theme'" [dropDownItems]="themeList" *ngIf="isFilter"
        [singleDefaultSelect]='false'
        (dropDownItemMultiClicked)="changeTheme($event)" [selectedValues]="selectedTheme" [isMulti]="true"
        class="ifp-glossery__filter-item" [placeHolder]="'Select Sub Domain' | translate" [selectAllBox]="true" [isEmitEventAll]="true" [selectAll]="checkSelectAll('theme')" [isCheckedClear]="isCheckedClear"> </app-ifp-dropdown>


      <ifp-search class="ifp-glossery__filter-item ifp-glossery__filter-item--search" [isKeypress]="true" (searchEvent)="searchResult($event)"
        [onSearch]="searchString" [boxType]="true" [placeholderText]="'Search glossary'| translate"></ifp-search>


    </div>
    <div class="ifp-glossery__charecter">
      <span *ngFor="let dataKey of objectKeys(characters)" class="ifp-glossery__letter" (click)="selectChar(dataKey === selectedChar ? '' : dataKey)"
        [ngClass]="{'ifp-glossery__letter--active' : dataKey === selectedChar , 'ifp-glossery__letter--disabled' : characters[dataKey] <= 0 }">{{dataKey
        | titlecase}}</span>
    </div>

    <div class="ifp-glossery__list">
      <ng-container *ngIf="glosseryList?.length > 0; else noData">
        <app-list-with-switch *ngFor="let item of glosseryList" [listData]="item"
          class="ifp-glossery__item"></app-list-with-switch>
      </ng-container>

      <ng-template #noData>
        <app-ifp-no-data [message]="'No Glossary' | translate"></app-ifp-no-data>
      </ng-template>
    </div>

    <div class="ifp-glossery__pagination">
      <app-pagination (pageChange)="onPageChange($event)" [customPagination]="true" [offset]="offset"
        [size]="totalCount"></app-pagination>
    </div>
  </div>
</div>
