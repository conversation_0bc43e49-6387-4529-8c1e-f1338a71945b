import { Component, EventEmitter, Input, OnChanges, Output } from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';
import { IfpButtonComponent } from '../../../scad-insights/ifp-widgets/ifp-atoms/ifp-button/ifp-button.component';
import { buttonClass, buttonIconPosition } from 'src/app/scad-insights/core/constants/button.constants';
import { AsyncPipe, NgClass } from '@angular/common';
import { ThemeService } from 'src/app/scad-insights/core/services/theme/theme.service';

@Component({
    selector: 'ifp-help',
    templateUrl: './ifp-help.component.html',
    styleUrl: './ifp-help.component.scss',
    imports: [TranslateModule, IfpButtonComponent, NgClass, AsyncPipe]
})
export class IfpHelpComponent implements OnChanges {
  @Output() closeHelp = new EventEmitter();
  @Input() mainHead: string = 'Discover What Data Prep Can Do';
  @Input() helpData: HelpData[] = [];

  public currentContent!: HelpData;
  public curretIndex = 0;
  public button = buttonClass;
  public position = buttonIconPosition;

  constructor(public _themeService: ThemeService) {
  }


  ngOnChanges() {
    this.currentContent = this.helpData[0];
  }

  close() {
    this.curretIndex = 0;
    this.currentContent = this.helpData[this.curretIndex];
    this.closeHelp.emit();
  }

  back() {
    this.curretIndex--;
    this.currentContent = this.helpData[this.curretIndex];
  }

  next() {
    this.curretIndex++;
    this.currentContent = this.helpData[this.curretIndex];
  }

  helpSelection(index: number) {
    this.currentContent = this.helpData[index];
    this.curretIndex = index;
  }

}

interface HelpData {
  img: string;
  darkImg: string;
  title: string;
  desc: string[];
  content?: Content[];
}

interface Content {
  title: string;
  desc: string;
}
