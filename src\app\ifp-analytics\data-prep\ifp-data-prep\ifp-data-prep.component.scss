@use "../../../../assets/ifp-styles/abstracts/index" as *;
.ifp-prep {
  display: flex;
  min-height: calc(100vh - 263px);

  &__toolbar {
    display: flex;
  }
  &__toolbar-wrapper {
    display: flex;
    border-inline-end: 1px solid $ifp-color-grey-3;
  }
  &__content{
    width: calc(100% - 285px);
    position: relative;
  }
  &__body-content {
    width: calc(100% - 36px);
  }
  &__header {
    display: block;
  }
  &__body {
    display: flex;
    @include ifp-scroll-y($ifp-color-dropdown-select, $ifp-color-grey-1, 8px, 8px);
  }
  &__body-content {
    padding: $spacer-4;
  }
  &__preview {
    background-color: $ifp-color-white;
    padding: $spacer-4;
    position: relative;
    border-top: 1px solid $ifp-color-grey-7;
    display: flex;
  }
  &__preview-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: $spacer-4;
    position: relative;
    z-index: 2;
  }
  &__preview-tools {
    display: flex;
    align-items: center;
    margin: $spacer-0 (-$spacer-3);
  }
  &__preview-tool,
  &__preview-duration,
  &__preview-dropdown {
    margin: $spacer-0 $spacer-3;
  }
  &__preview-tool,
  &__preview-dropdown {
    font-size: $ifp-fs-6;
  }
  &__preview-tool {
    transition: 0.3s;
    cursor: pointer;
    &:hover,
    &--active {
      color: $ifp-color-blue-hover;
    }
  }
  &__probability{
    margin-left: $spacer-4;
  }
  &__preview-title {
    font-size: $ifp-fs-5;
    font-weight: $fw-bold;
  }
  &__preview-resize {
    background-color: $ifp-color-grey-2;
    color: $ifp-color-white;
    padding: 2px $spacer-6;
    border-radius: 30px;
    cursor: n-resize;
    position: absolute;
    top: 0;
    left: 50%;
    transform: translate(-50%, -50%);
  }

  &__preview-inner {
    position: relative;
    height: 250px;
    padding-inline-end: $spacer-2;
    @include ifp-scroll-y($ifp-color-dropdown-select, $ifp-color-grey-1, 8px, 8px);
  }
  &__body-sidebar {
    z-index: 2;
    margin-top: 2px;
    background-color: $ifp-color-section-white;
    border-left: 1px solid $ifp-color-grey-7;
    min-height: calc(100vh - 324px);
    &--normal-width{
      min-height: 100%
    }
  }
&__process-no-data {
  font-weight: $fw-bold;
  text-align: center;
}
}


:host-context(.ifp-header__fixed) {
  .ifp-prep {
    &__header {
      position: sticky;
      top: $ifp-header-height-sticky;
      left: 0;
      z-index: 9;
      box-shadow: 0 4px 16px $ifp-color-black-16;
      @include slide-down-below-header();
    }
  }
}

:host::ng-deep {

  ifp-data-prep-save{

    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;

  }
  .ifp-prep__toggle-sidebar {
    .ifp-toggle-sidebar__content {
      padding: $spacer-4 ;
    }

    .ifp-prep {
      &__heading {
        font-size: $ifp-fs-6;
        font-weight: $fw-bold;
        margin-bottom: $spacer-5;
      }
      &__process-item {
        position: relative;
        padding-bottom: $spacer-4;
        padding-left: $spacer-4;

        &::before,
        &::after {
          content: "";
          position: absolute;
          top: 2px;
        }
        &::after {
          width: 11px;
          height: 11px;
          border-radius: 50%;
          background-color: $ifp-color-green-dark-1;
          left: 0;
        }
        &::before {
          width: 1px;
          height: 100%;
          background-color: $ifp-color-grey-8;
          left: 5px;
        }
        &:last-child {
          padding-bottom: $spacer-0;
          &::before {
            content: none;
          }
        }
        &--process-item-blue {
          &::after {
            background-color: $ifp-color-violet;
          }
        }
      }
      &__process {
        height: calc(100vh - 400px);
        @include ifp-scroll-y(transparent, $ifp-color-grey-7, 8px, 8px);
      }
      &__process-name {
        font-weight: $fw-medium;
        margin-bottom: $spacer-2;
      }
      &__process-time {
        color: $ifp-color-grey-9;
        .ifp-icon {
          margin-inline-end: $spacer-2;
        }
      }
    }
    &--submenu {
      .ifp-toggle-sidebar__content {
      padding: $spacer-4 14px;
      margin-top: 60px;
      }
      .ifp-prep {
        &__process-item {
          height: $spacer-5;
        }}
    }
  }
}

.ifp-prep-data__preview-resize {
  background-color: $ifp-color-grey-2;
  color: $ifp-color-white;
  padding: 2px $spacer-6;
  border-radius: 30px;
  cursor: n-resize;
  position: absolute;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 3;
}

@include desktop-sm {
  .ifp-prep {
    position: relative;
    overflow: hidden;

    &__body {
      padding: $spacer-0 70px;
    }
    &__body-content {
      width: 100% !important;
    }
    &__body-sidebar {
      position: absolute;
      right: 0;
      height: 100% !important;
    }
    &-data__preview-resize {
      z-index: 7;
    }
    ::ng-deep {
      .ifp-data-preview {
        z-index: 6;
        width: calc(100% - 70px);
        float: right;
      }
    }
  }
}

:host-context([dir="rtl"]) {
  @include desktop-sm {
    .ifp-prep {
      &__body-sidebar {
        left: 0;
        right: auto;
      }
      ::ng-deep {
        .ifp-data-preview {
          float: left;
        }
      }
    }
  }
}
