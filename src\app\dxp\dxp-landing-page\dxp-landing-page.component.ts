import { Component,  inject,  <PERSON><PERSON><PERSON>roy,  OnInit,  signal  } from '@angular/core';

import { LabelData } from 'src/app/scad-insights/core/interface/atom/ifp-category-label.interface';
import { buttonClass } from '../../scad-insights/core/constants/button.constants';
import { IfpHorizontalTabComponent } from '../../scad-insights/ifp-widgets/ifp-molecules/ifp-horizontal-tab/ifp-horizontal-tab.component';
import { Router, RouterOutlet, ActivatedRoute } from '@angular/router';

import { IfpButtonComponent } from '../../scad-insights/ifp-widgets/ifp-atoms/ifp-button/ifp-button.component';
import { IfpBreadcrumbsComponent } from '../../scad-insights/ifp-widgets/ifp-molecules/ifp-breadcrumbs/ifp-breadcrumbs.component';
import { TranslateModule } from '@ngx-translate/core';
import { IfpTabComponent } from '../../scad-insights/ifp-widgets/ifp-molecules/ifp-tab/ifp-tab.component';
import { IfpSearchComponent } from '../../scad-insights/ifp-widgets/ifp-atoms/ifp-search/ifp-search.component';
import { IfpDropdownComponent } from "../../scad-insights/ifp-widgets/ifp-atoms/ifp-dropdown/ifp-dropdown.component";
import { NgClass } from '@angular/common';
import { SubSink } from 'subsink';
import { ApiService } from 'src/app/scad-insights/core/services/api.service';
import { dxpApi } from '../dxp.constants';
import { ListingPageData, ListingPageItem } from '../dxp.interface';
import { dxpTextConstants, USER_ROLES } from '../dxp-constants-text';
import { DxpTabCommunicationService } from '../services/dxp-tab-communication.service';
import { DxpTabDataService } from '../services/dxp-tab-data.service';

@Component({
  selector: 'ifp-dxp-landing-page',
  imports: [IfpBreadcrumbsComponent,
    TranslateModule,
    IfpTabComponent,
    IfpSearchComponent,
    IfpButtonComponent,
    IfpHorizontalTabComponent,
    RouterOutlet, IfpDropdownComponent, NgClass],
  templateUrl: './dxp-landing-page.component.html',
  styleUrl: './dxp-landing-page.component.scss',
})
export class DxpLandingPageComponent implements OnInit, OnDestroy {
  public _router = inject(Router);
  public _apiService = inject(ApiService);
  private _dxpTabDataService = inject(DxpTabDataService);
  private _dxpTabCommunicationService = inject(DxpTabCommunicationService);
  public searchString = signal('');
  public dxpTextConstants = dxpTextConstants; // Make constants available in template
  public USER_ROLES = USER_ROLES; // Make user roles constants available
  public pageData = signal([
    {
      title: 'Home',
      route: '/home',
    },
    {
      title: 'Abu Dhabi Executive Office (ADEO)',
      route: '',
    },
  ]);
  public dashboardTabs = signal<LabelData[]>([
    {
      name: 'KPIs',
      key: 'kpis',
      iconClass: 'ifp-icon-kpi',
      route: '/dxp'
    },
    {
      name: 'Dashboards',
      key: 'dashboards',
      iconClass: 'ifp-icon-desktop-chart',
      route: '/dxp'
    },
    {
      name: 'DXP Catalogue',
      key: 'catalogue',
      iconClass: 'ifp-icon-catelogue',
      route: '/dxp/catalogue'
    },
  ]);

  public subs: SubSink = new SubSink();
  public subTabSelected = 0;
  public sideMenu = signal<any[]>([]);
  public buttonClass = buttonClass;
  public entity = signal([]);
  public selectedTabView = signal<LabelData>({ name: 'KPIs', key: 'kpi'});
  public _route = inject(ActivatedRoute);
  public sideMenuStatus:string = '';
  public ListingPageData: ListingPageData | null = null;
  private readonly _subs: SubSink = new SubSink();
  public dxpListingData: ListingPageItem[] = [];

  // API response data properties
  public currentUserRole: string = '';
  public creatorsFilterData: any[] = [];
  public entitiesFilterData: any[] = [];
  public selectedTabParam: string = '';
  public sideMenuApiData: any = null;

  constructor() {
    this.subs.add(
      this._router.events.subscribe({
        next: ()=> {
          const tabIndex = this.dashboardTabs().findIndex((item: LabelData) => item.route === this._router.url);
          this.selectedTabView.set(tabIndex >= 0 ? this.dashboardTabs()[tabIndex] : this.dashboardTabs()[0]);
        }
      })
    )
  }

  ngOnInit(): void {
    this.getSideMenuList();
    this.getEntityList();
    this._route.queryParams.subscribe(params => {
      if (params['subTab']) {
        this.subTabSelected = +params['subTab'];
        this.sideMenuStatus ='pending';
        this.getIndicatorList(this.sideMenuStatus);
        this.getIndicatorList('new-requests');
      }
    });
  }

  getIndicatorList(val?:string) {
    this._subs.add(
      this._apiService.getMethodRequest(dxpApi.listEntityKpi, { status:val?val: 'pending' }).subscribe({
        next: (resp: ListingPageData) => {
          this.dxpListingData = resp?.data ?? [];

          // Update side menu counts if needed
          this.updateSideMenuCounts(val, resp?.totalCount || 0);

          console.log('Indicator list updated for status:', val, 'Count:', resp?.totalCount);
        },
        error: (error) => {
          console.error('Error fetching indicator list:', error);
        }
      })
    );
  }

  updateSideMenuCounts(status: string | undefined, count: number) {
    // Update counts for specific menu items if they show counts
    const currentSideMenu = this.sideMenu();
    const updatedSideMenu = currentSideMenu.map(item => {
      // Update count for items that match the status and show counts
      if (item.showCount && item.param === status) {
        return {
          ...item,
          value: count
        };
      }
      return item;
    });

    this.sideMenu.set(updatedSideMenu);
  }

  getEntityList() {
    this.subs.add(
      this._apiService
        .getMethodRequest(dxpApi.listEntityKpi, { status: 'pending' })
        .subscribe({
          next: (resp: ListingPageData) => {
            this.ListingPageData = resp;
            console.log(resp);
          },
        })
    );
  }

  searchResult(value: string) {
    this.searchString.set(value);
  }
  selectSideBar(value: { item: any; index: number }) {
    this.subTabSelected = value.index;

    // Get the selected main tab from the menu
    const sideMenuItems = this.sideMenu();
    if (sideMenuItems && sideMenuItems.length > 0 && value.index < sideMenuItems.length) {
      const selectedMenuItem = sideMenuItems[value.index];

      if (selectedMenuItem) {
        // Enhanced parameter extraction logic for manual selection
        let paramToUse = '';
        let subMenuKey = '';

        // Check if the clicked tab has submenus
        if (selectedMenuItem.subMenus && selectedMenuItem.subMenus.length > 0) {
          // If tab has submenus, automatically use the first submenu item's param
          paramToUse = selectedMenuItem.subMenus[0].param;
          subMenuKey = selectedMenuItem.subMenus[0].param; // Store submenu key
          console.log('Tab has submenus - using first submenu param:', paramToUse);
          console.log('Available sub-menu options:');
          selectedMenuItem.subMenus.forEach((subMenu: any, index: number) => {
            console.log(`${index + 1}. ${subMenu.label_en} (param: ${subMenu.param})`);
          });
        } else {
          // If tab has no submenus, use the tab's own param
          paramToUse = selectedMenuItem.param;
          console.log('Tab has no submenus - using tab param:', paramToUse);
        }

        // Store the selected param locally
        this.selectedTabParam = paramToUse;

        // Emit the tab selection data to the indicator component with enhanced data
        this._dxpTabCommunicationService.emitTabSelection({
          param: paramToUse,
          subMenus: selectedMenuItem.subMenus,
          selectedMenuItem: selectedMenuItem,
          tabKey: selectedMenuItem.key,        // Pass the main tab key
          subMenuKey: subMenuKey,              // Pass the submenu key (if any)
          userRole: this.currentUserRole       // Pass the user role
        });

        console.log('Selected main tab:', selectedMenuItem.name);
        console.log('Final param being used:', paramToUse);
      }
    } else {
      console.warn('Invalid tab selection or empty menu');
    }
  }

  changeTabView(event: { event: LabelData, index: number }) {
    this.selectedTabView.set(event.event);
    this._router.navigateByUrl(event.event.route ?? '');
  }

  gotToKpiPage() {
    this._router.navigateByUrl('/dxp/visualization-wizard')
  }

  // Methods to provide access to filter data for child components
  getCurrentUserRole(): string {
    return this.currentUserRole;
  }

  getCreatorsFilterData(): any[] {
    return this.creatorsFilterData;
  }

  getEntitiesFilterData(): any[] {
    return this.entitiesFilterData;
  }

  getSelectedTabParam(): string {
    return this.selectedTabParam;
  }

  getSideMenuList() {
    this._subs.add(
      this._apiService.getMethodRequest(dxpApi.getSideMenuList, {}, true).subscribe({
        next: (resp: any) => {
          console.log('Sidebar API Response:', resp);
          this.sideMenuApiData = resp;

          // Extract user role
          this.currentUserRole = resp?.user?.generalizedRole || '';

          // Extract filter data
          this.creatorsFilterData = resp?.filters?.creators || [];
          this.entitiesFilterData = resp?.filters?.entities || [];

          // Emit filter data to indicator component
          this._dxpTabCommunicationService.emitFilterData({
            entitiesFilterData: this.entitiesFilterData,
            creatorsFilterData: this.creatorsFilterData
          });

          // Process tabs data
          this.processSideMenuFromApi(resp?.tabs || {});
        },
        error: (error) => {
          console.error('Error fetching side menu list:', error);
          // Set default menu and perform auto-selection even on error
          this.setDefaultSideMenu();
        }
      })
    );
  }

  processSideMenuFromApi(tabsData: any) {
    const menuItems: any[] = [];

    // Process only main tabs (not sub-menus)
    Object.keys(tabsData).forEach(tabKey => {
      const tab = tabsData[tabKey];

      // Add only main tab with name, param, key, and subMenus reference
      menuItems.push({
        name: tab.label_en,           // Display name: "KPI's", "Approval Status", "My KPI's"
        param: tab.param,             // Parameter: "kpis", "approval-status", "my-kpis"
        key: tabKey,                  // Store the tab key for later use
        showCount: tab.showCount || false,
        subMenus: tab.subMenus || []  // Keep reference to sub-menus for later use
      });
    });

    // Set the processed menu items (only main tabs)
    this.sideMenu.set(menuItems);

    // Implement automatic default selection on initial load
    if (menuItems.length > 0) {
      this.performAutoSelection(menuItems);
    }

    console.log('Processed main tabs only:', menuItems);
  }

  /**
   * Performs automatic default selection on page load
   * Part 1: Initial Load Behavior (Auto-selection on page load)
   */
  private performAutoSelection(menuItems: any[]) {
    // Automatically select the first item from the sideMenu() array
    const firstTab = menuItems[0];

    if (!firstTab) {
      console.warn('No menu items available for auto-selection');
      return;
    }

    // Extract the appropriate param value based on submenu presence
    let paramToUse = '';

    if (firstTab.subMenus && firstTab.subMenus.length > 0) {
      // If the first tab has submenus, use the first submenu item's param
      paramToUse = firstTab.subMenus[0].param;
      console.log('Auto-selection: First tab has submenus - using first submenu param:', paramToUse);
    } else {
      // If the first tab has no submenus, use the tab's own param
      paramToUse = firstTab.param;
      console.log('Auto-selection: First tab has no submenus - using tab param:', paramToUse);
    }

    // Set the selected tab index and param
    this.subTabSelected = 0; // First tab index
    this.selectedTabParam = paramToUse;

    // Programmatically trigger the same selection logic as manual user clicks
    // by calling selectSideBar() with the appropriate event object
    const autoSelectionEvent = {
      item: firstTab,
      index: 0
    };

    console.log('Auto-selection: Triggering selectSideBar with:', autoSelectionEvent);
    console.log('Auto-selection: Selected tab will be:', firstTab.name);
    console.log('Auto-selection: Final param will be:', paramToUse);

    // Call selectSideBar to ensure all the same logic is executed
    this.selectSideBar(autoSelectionEvent);
  }

  setDefaultSideMenu() {
    // Fallback default menu in case API fails
    const defaultMenu = [
      {
        name: 'KPIs',
        param: 'kpis',
        key: 'kpis',                  // Add key property for consistency
        showCount: false,
        subMenus: [] // Ensure subMenus property exists
      }
    ];

    this.sideMenu.set(defaultMenu);
    console.log('Set default side menu due to API error');

    // Perform auto-selection even for default menu
    this.performAutoSelection(defaultMenu);
  }

  ngOnDestroy() {
    this.subs.unsubscribe();
  }

}
