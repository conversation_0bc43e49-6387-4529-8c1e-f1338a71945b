export const dxpPreviewWizardTabMenu = [
  {
    key: 'filter',
    title: 'Filter',
    description: 'Interactive filter into your visualization by choosing a column from the dataset, assigning a label name, and setting a default value for the filter.',
    icon: 'ifp-icon-filter-2',
    disabled: true,
    content: []
  },
  {
    key: 'chart',
    title: 'Charts',
    description: 'Customize your chart visualization',
    icon: 'ifp-icon-pie-chart',
    disabled: false,
    content: [
      {
        title: 'Chart Types',
        key: 'chartsType',
        isExpand: true
      }
    ]
  },
  {
    key: 'text',
    title: 'Text',
    description: 'Change title and description',
    icon: 'ifp-icon-font1',
    disabled: false,
    content: [
      {
        title: 'Title',
        key: 'title',
        isExpand: true
      },
      {
        title: 'Description',
        key: 'description',
        isExpand: true
      }
    ]
  },
  {
    key: 'series',
    title: 'Legend',
    description: 'Select a legend for your chart series.',
    icon: 'ifp-icon-filter-2', // Use an appropriate icon class if available
    disabled: false,
    content: [
      {
        title: 'Legend',
        key: 'legend',
        isExpand: true
      }
    ]
  }
];














