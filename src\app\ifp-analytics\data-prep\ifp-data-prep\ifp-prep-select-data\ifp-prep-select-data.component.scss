@use "../../../../../assets/ifp-styles/abstracts/index" as *;
.ifp-select-data {
  margin-bottom: $spacer-5;
  &__table {
    margin-bottom: $spacer-4;
    // max-height: calc(100vh - 400px);
    // @include ifp-scroll-y($ifp-color-dropdown-select, $ifp-color-grey-1, 8px, 8px);
  }
  &__header-wrapper {
    display: flex;
    justify-content: space-between;
    margin-bottom: $spacer-5;
    align-items: end;
  }
  &__title {
    font-size: $ifp-fs-6;
    font-weight: $fw-semi-bold;
    margin-bottom: $spacer-1;
  }
  &__sub-title {
    font-size: $ifp-fs-4;
    font-weight: $fw-medium;
    color: $ifp-color-grey-2;
  }
}
:host::ng-deep {
  .ifp-select-data__table {
    .ifp-db-dropdown {
      margin: -5px;
    }
  }
}
