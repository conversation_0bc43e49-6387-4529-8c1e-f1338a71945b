@use "../../../../assets/ifp-styles/abstracts/index" as *;
:host::ng-deep {
  .ifp-prep-toolbar__search {
    .ifp-search-box--box-type {
      height: 40px;
      border: none;
      &__input {
        background-color: transparent;
      }
    }
    .ifp-search-box--box-type,
    .ifp-search-box--box-type:hover{
      background-color: $ifp-color-grey-4;
    }
  }
  .ifp-prep-toolbar__tool-basic--active {
    .ifp-prep-toolbar__tool-info {
      svg {
        fill: rgb(255 255 255) !important;
      }

    }
    app-ifp-info {
      em {
        color: $ifp-color-white-global !important;
      }
    }
  }
  .ifp-prep-toolbar__tool-basic {
    .ifp-info .ifp-tooltip {
      min-width: 200px !important;
    }
  }

  .ifp-prep-toolbar--collapse-basic .ifp-prep-toolbar__tool-info {
    display: none;
  }

}
.ifp-prep-toolbar {
  background-color: $ifp-color-section-white;
  padding: $spacer-5;
  max-width: 320px;
  min-width: 320px;
  transition: 0.4s;
  position: relative;
  &__head-sec {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  &__title {
    font-size: $ifp-fs-6;
    font-weight: $fw-bold;
    margin-inline-end: $spacer-3;
    color: $ifp-color-black;
  }
  &__toggle {
    width: 25px;
    height: 25px;
    text-align: center;
    color: $ifp-color-white;
    border-radius: 50%;
    background-color: $ifp-color-secondary-blue-dark;
    position: absolute;
    top: 135px;
    right: -14px;
    transition: 0.3s;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    z-index: 3;
    &:before {
      position: relative;
      left: -1px;
      height: 18px;
      top:.5px;
    }
  }
  &__search {
    display: block;
    margin-bottom: $spacer-4;
  }
  &__icon {
    font-size: $ifp-fs-6;
  }
  &__tools-list {
    max-width: 100%;
    transition: 0.4s;

  }
  &__tool {
    font-size: $ifp-fs-4;
    color: $ifp-color-primary-grey;
    font-weight: $fw-medium;
    display: flex;
    align-items: center;
    padding: 10px $spacer-3;
    border-radius: 40px;
    transition: background-color 0.3s;
    margin-bottom: $spacer-2;
    cursor: pointer;
    white-space: nowrap;
    &:last-child {
      margin-bottom: $spacer-0;
    }
    &:hover {
      background-color: $ifp-color-dropdown-select;
    }
    &--active {
      background-color: $ifp-color-active-blue;
      color: $ifp-color-white-global;
      .ifp-prep-toolbar__icon {
        color: $ifp-color-white-global !important;
      }
    }
    &--drag-overflow {
      padding-inline-start: 20px;
      cursor: move;
      -webkit-user-select: none;
      user-select: none;
    }
  }
  &__tool-name {
    font-size: inherit;
    color: inherit;
    padding-inline-start: $spacer-3;
  }
  &__group-inner {
    &--basic {
      border: 1px solid #D5D5D5;
      border-radius: $spacer-2;
      background-color: $ifp-color-pale-grey;
    }
  }
  &__group-name-basic {
    font-weight: $fw-bold;
    display: inline-block;
    margin-bottom: $spacer-2;
    color: #707070;
  }
  &__tool-name-basic {
    font-weight: $fw-medium;
    // color: $ifp-color-primary-grey;
    margin-inline-start: $spacer-1;
    margin-inline-end: $spacer-1;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
    color: $ifp-color-black;
  }
  &__tool-basic {
    border-bottom: 1px solid #D5D5D5;
    border-radius: $spacer-2;
    background-color: $ifp-color-white;
    font-size: $ifp-fs-4;
    color: $ifp-color-grey-9;
    font-weight: $fw-medium;
    display: flex;
    align-items: center;
    padding: $spacer-2 $spacer-2;
    transition: background-color 0.3s;
    cursor: pointer;
    white-space: nowrap;
    &:last-child {
      margin-bottom: $spacer-0;
      border-bottom: none;
    }
    &:not(:last-child):not(:first-child) {
      border-radius: 0;
    }
    &:not(:last-child):first-child {
      border-radius: 8px 8px 0 0;
    }
    &:last-child:not(:first-child) {
      border-radius: 0 0 8px 8px;
    }
    &:hover {
      background-color: $ifp-color-dropdown-select;
    }
    .ifp-prep-toolbar__icon {
      margin-inline-start: $spacer-1;
      margin-inline-end:$spacer-1 ;
    }
    &--active {
      background-color: $ifp-color-blue-menu;
      color: $ifp-color-white-global;
      .ifp-prep-toolbar__icon {
        color: $ifp-color-white-global !important;
      }
      .ifp-prep-toolbar__tool-name-basic {
        color: $ifp-color-white-global;
      }
      &:hover {
        background-color: $ifp-color-blue-menu;
      }
    }
    &--drag-overflow {
      /* border-bottom: 1px solid $ifp-color-grey-bg-2; */
      padding-inline-start: 20px;
      cursor: move;
      user-select: none;
    }

  }
  &__tool-info {
     margin-inline-start: auto;
     white-space: normal;
  }
  &--accordian {
    .ifp-prep-toolbar {
      &__group-wrapper {
        display: flex;
        align-items: center;
        margin: $spacer-2 $spacer-0;
        cursor: pointer;
        position: relative;
        &::before,
        &::after {
          content: "";
          background-color: $ifp-color-black;
          position: absolute;
          transition: 0.3s;
          top: 50%;
          transform: translateY(-50%);
        }
        &::before {
          width: 1px;
          height: 15px;
          right: 7px;
          opacity: 1;
        }
        &::after {
          width: 15px;
          height: 1px;
          right: 0;
        }
        .ifp-prep-toolbar__icon {
          transform: translateX(-50%);
          display: inline-block;
        }
      }
      &__group-inner {
        max-height: 0;
        overflow: hidden;
        transition: 0.3s;
        padding-inline-start: $spacer-1;
        border-inline-start: 1px solid $ifp-color-grey-7;
      }
      &__group-name {
        font-weight: $fw-medium;
      }
      &__group {
        &--active {
          .ifp-prep-toolbar {
            &__group-wrapper {
              &::before {
                opacity: 0;
              }
            }
            &__group-inner {
              max-height: 1000px;
              overflow: visible;
            }
          }
        }
      }
      &__tools-list {
        padding-inline-start: $spacer-2;
        padding-inline-end: $spacer-2;
        max-height: 100vh;
        min-height: calc(100vh - 356px);
        @include ifp-scroll-y(transparent, $ifp-color-grey-7, 8px, 8px);
      }
      &__tool {
        margin-bottom: $spacer-0;
      }
      &__tool-name {
        font-weight: $fw-regular;
      }
    }
  }
  &--collapse {
    max-width: 115px;
    min-width: 70px;
    padding: 6px;
    .ifp-prep-toolbar {
      &__tools-list {
        max-width: 60px;
        overflow: hidden;
      }
      &__title {
        max-width: 0;
        overflow: hidden;
      }
      &__toggle {
        transform: rotate(180deg);
      }
      &__tool--drag-overflow{
        padding-inline-start: 8px !important;
      }
      &__tool-name {
        display: none;
      }
    }
    &.ifp-prep-toolbar--accordian {
      .ifp-prep-toolbar {
        &__group-wrapper {
          &::before,
          &::after {
            content: none;
          }
        }
        &__group-name {
          opacity: 0;
          visibility: hidden;
        }
        &__tool--drag-overflow {
          padding-inline-start: $spacer-3;
        }
        &__search {
          display: none;
        }
        &__group-wrapper {
          display: none;
        }
        &__group-inner {
          border-inline-start: none;
        }
      }
    }
  }
  &--collapse-basic {
    min-width: 70px;
    padding: 12px;
    .ifp-prep-toolbar {
      &__tools-list {
        max-width: 100px;
        overflow: hidden;
      }
      &__title {
        max-width: 0;
        overflow: hidden;
        margin: 0;
      }
      &__toggle {

    transform: translateX(-50%);
        transform: rotate(180deg);
      }
      &__search {
        display: none;
      }
      &__tool-name-basic {
        display: none;
      }
      &__group-name-basic {
        display: none;
      }
      &__group-inner--basic {
        margin-bottom: 16px;
      }
    }
  }
  &--disable {
    .ifp-prep-toolbar {
      &__tool:not(.ifp-prep-toolbar__tool--active),&__tool-basic:not(.ifp-prep-toolbar__tool-basic--active) {
        cursor: not-allowed;
        &:hover {
          background-color: $ifp-color-white;
        }
        .ifp-prep-toolbar {
          &__tool-name-basic,
          &__icon {
            opacity: 0.6;
          }
        }
      }
    }
  }

  &__group {
    margin-top: 24px;
  }
  &__collapse {
    border: 1px solid #808A9D;
    height: 16px;
    width: 16px;
    border-radius: 4px;
    border-inline-start: 1px solid #808A9D;
    border-inline-end: 11px solid #808A9D;
    transition: all 0.2s;
    cursor: pointer;
    &--collapse {
      border-inline-start: 11px solid #808A9D;
      border-inline-end: 1px solid #808A9D;
    }
  }
  &__tool-basic--enabled:not(.ifp-prep-toolbar__tool-basic--active) {
    cursor: pointer !important;
    &:hover {
      background-color: $ifp-color-dropdown-select !important;
    }
    .ifp-prep-toolbar {
      &__tool-name-basic,
      &__icon {
        opacity: 1 !important;
      }
    }
  }
}

:host-context(.ifp-dark-theme) {
  .ifp-prep-toolbar__tool-basic{
       color:  $ifp-color-white-global;
    }
}

:host-context([dir="rtl"]) {
  .ifp-prep-toolbar {
    &__toggle {
      left: -14px;
      right: auto;
      &::before {
        right: -1px;
        left: auto;
      }
    }
    &--accordian {
      .ifp-prep-toolbar__group-wrapper {
        &::before {
          left: 7px;
          right: auto;
        }
        &::after {
          left: 0;
          right: auto;
        }
        .ifp-prep-toolbar__icon {
          transform: translateX(50%);
        }
      }
    }
    &--collapse-basic {
      .ifp-prep-toolbar__toggle {
        transform: translateX(50%);
      }
    }
  }
}

@include desktop-sm {
  .ifp-prep-toolbar {
    position: absolute;
    z-index: 9;
    height: 100%;
  }
  .toolbar-overlay {
    content: "";
    display: block;
    width: 100vw;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    background-color: rgba(0, 0, 0, 0.5); /* Same as $ifp-color-black-50 */
    z-index: 8; /* Make sure it's behind the toolbar */
  }
}




