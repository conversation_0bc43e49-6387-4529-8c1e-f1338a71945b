@use "../../../../assets/ifp-styles/abstracts/index" as *;
:host {
  display: block;
}
.ifp-auto-ml-cl {

  &__bar-wrapper , &__number-wrapper{
    border-radius: 10px;
    border: 1px solid $ifp-color-grey-13;
    overflow: hidden;
  }
&__bar, &__number{
  padding: $spacer-2 $spacer-3;
  border-bottom: 1px solid $ifp-color-grey-13;

  &:last-child {
    border-bottom:  unset
  }
  height: 40px;
}
&__bar {
  position: relative;
}
&__bar-overlay {
  position: absolute;
  width: 100%;
  left: 0;
  height: 100%;
  top: 0;
}
&__number {
  display: flex;
  align-items: center;
  text-align: center;
  justify-content: center;
  color: $ifp-color-primary-grey;
  font-size: $ifp-fs-3;
  font-weight: $fw-bold;
  &--black {
    color: $ifp-color-black-global;
  }
}
&__number-wrapper{
  &--selection {
  border: 1px solid $ifp-color-blue-menu;
  }
}
}

:host::ng-deep {
  .ifp-auto-ml-cl {
    &__bar {
      .ifp-progress-value {
        width: 100%;
      }
    }
  }
}
