<div class="ifp-status-stepper">
  <ul class="ifp-status-stepper__process">
    @for (item of statuses(); track item;let index= $index) {
    <li class="ifp-status-stepper__process-item"
      [ngClass]="{
        'ifp-status-stepper--completed': item.statusKey === 'completed',
        'ifp-status-stepper--pending': item.statusKey === 'pending',
        'ifp-status-stepper--inprogress': item.statusKey === 'inprogress',
        'ifp-status-stepper--failed': item.statusKey === 'failed'
      }">
      <span class="ifp-status-stepper__process-icon ">
        @if ( item.statusKey === 'completed') {
          <em class="ifp-icon ifp-icon-tick"></em>
        } @else if(item.statusKey === 'inprogress') {
          <em class="ifp-icon ifp-icon-clock"></em>
        } @else if(item.statusKey === 'failed') {
          <em class="ifp-icon ifp-icon-cross"></em>
        } @else {
          <div class="ifp-status-stepper__box"></div>
        }

      </span>
      <div class="ifp-status-stepper__data">
        <p class="ifp-status-stepper__process-stage ">{{'stage' | translate}} {{index +1}}</p>
        <p class="ifp-status-stepper__process-name">{{ item.name | translate}}</p>
        <span class="ifp-status-stepper__process-status ">
          {{item.status| translate}}
        </span>
      </div>
    </li>
    } @empty {
    <p class="ifp-status-stepper__process-no-data">{{'No steps are available' | translate}}</p>
    }
  </ul>
</div>
