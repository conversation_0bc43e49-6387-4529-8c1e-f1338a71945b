@use "../../../../assets/ifp-styles/abstracts/index" as *;

.ifp-about {
  background-color: $ifp-color-section-white;
  .ifp-module-heading {
    margin-bottom: $spacer-3;
  }
  &__video-wrapper {
    border-radius: 10px;
    margin-bottom: $spacer-5;
    overflow: hidden;
    height: 490px;
  }
  &__video {
    width: 100%;
    height: 100%;
    cursor: pointer;
  }
  &__content {
    margin-bottom: 54px;
    line-height: 1.7;
  }
  &__title {
    font-size: $ifp-fs-8;
    margin-bottom: $spacer-4;
    font-weight: $fw-medium;
  }
  &__desc {
    font-weight: $fw-medium;
    margin-bottom: $spacer-6;
    &:last-child {
      margin-bottom: $spacer-0;
    }
  }
  &__short-desc {
    font-size: $ifp-fs-3;
  }
  &__img-sec {
    display: flex;
    flex-wrap: wrap;
    margin: $spacer-0 (-$spacer-2);
  }
  &__tile {
    width: calc(33.33% - 16px);
    margin: $spacer-2;
  }
  &__tile-img {
    // padding: $spacer-3;
    // box-shadow: 0 0 4px rgba(0, 0, 0, 0.16);
    border-radius: 7px;
    img {
      width: 100%;
    }
  }
  &__container-sm {
    text-align: center;
    max-width: 750px;
    margin: $spacer-0 auto;
  }
}
