import { ChangeDetector<PERSON><PERSON>, Component, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, OnInit, Renderer2, signal, ViewChild } from '@angular/core';
import { IfpDataSummaryComponent, TableSettings, tableTypes } from '../../organism/ifp-data-summary/ifp-data-summary.component';
import { MetadataColumnPrep, PreivewPrep } from '../../organism/ifp-data-preview/interface/ifp-data-prep.intreface';
import { SubSink } from 'subsink';
import { prepsApiEndpoints } from '../../data-prep/ifp-data-prep/constants/if-preps.constants';
import { IfpIconSelectorComponent } from '../../../dashboard-builder/atom/ifp-icon-selector/ifp-icon-selector.component';
import { dataExploratoryTypes } from 'src/app/dashboard-builder/organism/ifp-chart-toolbar/ifp-chart-toolbar.constants';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { NgClass } from '@angular/common';
import { IfpDataToolAccordianComponent } from '../../molecule/ifp-data-tool-accordian/ifp-data-tool-accordian.component';
import { IfpCheckBoxComponent } from '../../../scad-insights/ifp-widgets/ifp-atoms/ifp-check-box/ifp-check-box.component';
import { DbToolbarIcon } from 'src/app/dashboard-builder/organism/ifp-chart-toolbar/ifp-chart-toolbar.interface';
import { IfpAnalyticLineChartComponent } from '../../../scad-insights/ifp-widgets/ifp-molecules/ifp-analytic-line-chart/ifp-analytic-line-chart.component';
import { buttonClass } from 'src/app/scad-insights/core/constants/button.constants';
import { IfpDropdownComponent } from '../../../scad-insights/ifp-widgets/ifp-atoms/ifp-dropdown/ifp-dropdown.component';
import { IfpTableComponent } from '../../../scad-insights/ifp-widgets/ifp-molecules/ifp-table/ifp-table.component';
import { IFPHighChartsComponent } from '../../../scad-insights/ifp-widgets/charts/ifp-highcharts.component';
import { FormControl, FormGroup, ReactiveFormsModule } from '@angular/forms';
import  { ColorType } from 'highcharts';
import { ApiService } from 'src/app/scad-insights/core/services/api.service';
import { environment } from 'src/environments/environment';
import { DownLoadService } from 'src/app/scad-insights/core/services/download-service/download.service';
import html2canvas from 'html2canvas';
import { IfpPdfTemplateComponent } from '../../../scad-insights/ifp-widgets/ifp-molecules/ifp-pdf-template/ifp-pdf-template.component';
import { SubscriptionLike } from 'rxjs';
import { StreamingService } from 'src/app/scad-insights/core/services/http/streaming.service';
import { IfpTagComponent } from '../../atom/ifp-tag/ifp-tag.component';
import { CorrelogramComponent } from '../../molecule/correlogram/correlogram.component';
import { IfpPrepPopupComponent } from '../../organism/ifp-prep-popup/ifp-prep-popup.component';
import { IfpModalComponent } from '../../../scad-insights/ifp-widgets/ifp-organism/ifp-modal/ifp-modal.component';
import { ActivatedRoute, Params, Router } from '@angular/router';
import { ifpColors } from 'src/app/scad-insights/core/constants/color.constants';
import { CardData } from 'src/app/shared/molecule/ifp-tool-card/ifp-tool-card.component';
import { IfpExploratoryService } from '../services/ifp-exploratory.service';
import { IfpModalService } from 'src/app/scad-insights/ifp-widgets/ifp-organism/ifp-modal/ifp-modal.service';
import { IfpNoDataComponent } from '../../../scad-insights/ifp-widgets/ifp-molecules/ifp-no-data/ifp-no-data.component';
import { IfpExploratoryNumericChartComponent } from '../ifp-exploratory-numeric-chart/ifp-exploratory-numeric-chart.component';
import { cloneDeep } from 'lodash';
import { ShortNumberPipe } from 'src/app/scad-insights/core/pipes/shortNumber.pipe';
import { IfpSpinnerComponent } from '../../../scad-insights/ifp-widgets/ifp-molecules/ifp-spinner/ifp-spinner.component';
@Component({
    selector: 'ifp-exploratory-data-analysis',
    templateUrl: './ifp-exploratory-data-analysis.component.html',
    styleUrl: './ifp-exploratory-data-analysis.component.scss',
    imports: [IfpDataSummaryComponent, IfpIconSelectorComponent, TranslateModule, NgClass, IfpDataToolAccordianComponent, IfpCheckBoxComponent, IfpAnalyticLineChartComponent,
    IfpDropdownComponent,
    IfpTableComponent, IFPHighChartsComponent,
    ReactiveFormsModule, IfpPdfTemplateComponent,
    IfpTagComponent, CorrelogramComponent, IfpPrepPopupComponent, IfpModalComponent, IfpNoDataComponent, IfpExploratoryNumericChartComponent, IfpSpinnerComponent]
})
export class IfpExploratoryDataAnalysisComponent implements OnDestroy, OnInit{
  @ViewChild('elementChart') elementChart!: ElementRef;
  @ViewChild('modalSucess') modalSucess!: IfpModalComponent;
  public summeryTableSettings :TableSettings = {
    withId: true,
    setting: [
      {
        key: 'name',
        name: 'Variables'
      },
      {
        key: 'type',
        name: 'Data Type',
        type: tableTypes.bracket,
        secondKey: 'type_description'
      },
      {
        key: 'Stats / Values',
        name: 'Stats / Values',
        type: tableTypes.table,
        tableSettings: [{
          key: 'min_value',
          funName: (column, row) => row.type === 'string' ?   'Total count': 'Min',
          name: '',
          fun: (column, row) => row.type === 'string' ?   row.null_count + row.valid_count : row.min_value
        },
        {
          key: 'median',
          funName: (column, row) => row.type === 'string' ?   'Unique values count' : 'Median',
          name: '',
          fun: (column, row) =>  row.type === 'string' ?   row.unique_count : row.median
        },
        {
          key: 'max_value',
          funName: (column, row) => row.type === 'string' ?   'Unique values percentage' : 'Max',
          name: '',
          fun: (column, row) =>  row.type === 'string' ?  (row.unique_percentage ? `${row.unique_percentage}%` : null) : row.max_value
        }
        ]
      },
      {
        key: 'valid_count',
        name: 'Non-null Values',
        type: tableTypes.progress,
        precentageKey: 'valid_percentage',
        color: ifpColors.green
      },
      {
        key: 'null_count',
        name: 'Null Values',
        type: tableTypes.progress,
        precentageKey: 'null_percentage',
        reverse: true

      }
    ]
  };

  public popupView = [
    {
      key: 'dataExploration',
      name: 'Data Exploration',
      icon: 'ifp-icon-desktop-chart',
      color: ifpColors.yellowLight
    },
    {
      key: 'chartTypes',
      name: 'Explore Visually',
      icon: 'ifp-icon-line-and-bar',
      color: ifpColors.green
    },
    {
      key: 'correlation',
      name: 'Correlation Analysis',
      icon: 'ifp-icon-correlation',
      color: ifpColors.redLight
    }
  ];

  public tableData: TableDataExploratory[] = [];
  public selectedTableData:TabularDatumExploratory[] = [];
  public showValuePrint = signal(false);
  public toggle = signal(false);

  public dataName = '';

  public data: ChartTypes= {
    variables: [],
    data: []
  };

  public dropdown = new FormControl<string[]>([]);

  public scatterForm =  new FormGroup({
    scatterYValue: new FormControl<ApiBoxChart>({
      max: 0,
      min: 0,
      column: '',
      quantiles: [],
      lower_outliers: [],
      upper_outliers: []
    }),
    scatterXValue: new FormControl<ApiBoxChart>({
      max: 0,
      min: 0,
      column: '',
      quantiles: [],
      lower_outliers: [],
      upper_outliers: []
    })
  });

  public chartKeys = {
    histogram: 'histogram',
    box: 'box',
    table: 'table',
    column: 'column',
    scatter: 'scatter'
  };



  public dropdownBoxChart = new FormControl<ApiBoxChart>({
    max: 0,
    min: 0,
    column: '',
    quantiles: [],
    lower_outliers: [],
    upper_outliers: []
  });

  public dropdownHistogramChart = new FormControl<ApiBoxChart>({
    max: 0,
    min: 0,
    column: '',
    quantiles: [],
    lower_outliers: [],
    upper_outliers: []
  });

  public tableDropdown = new FormControl<TableDataExploratory | null>(null);

  public dropdownCorrelation = new FormControl<string[]>([]);

  public chartType = 'column';

  public subs =  new SubSink();
  public summeryId!: string;

  public summery:MetadataColumnPrep[] = [];
  public customChartTypes = signal(cloneDeep( dataExploratoryTypes)) ;

  public summeryActive = true;
  public chartOpen = false;
  public correlationValue = false;
  public buttonClass = buttonClass;
  public xAxis:string[] = [];
  public yAxis:Record<string, any>[] = [];
  public bubbleData: {
    colorAxis:  number;
    colorKey: string;
    data: {name: string; y: number; z?:number;}[] ;
    marker?: Marker;
    name: string;
  } [] =  [];

  public showDownloadOptions =  signal(false);

  public summeryStreemSubscription!: SubscriptionLike;

  public controller!:AbortController;

  public loadedSummery = false;
  public summeryLoader = true;

  public download = signal(false);
  public selectedValues: ScoreList[] = [];
  public pearson = false;
  public spearman = false;
  public pearsonValue =  {};
  public spearmanValue = false;
  public correlogramData!: Correlogram;
  public correlogram: string[] = [];
  public correlogramDataChart: {
    pearson:    Record<string, Record<string, {
      color?: string;
      value:number;
      name: string;
    }> >,
    spearman:    Record<string, Record<string, {
      color?: string;
      value:number;
      name: string;
    }> >} = {
      pearson: {},
      spearman: {}
    };

  public boxChart: ApiBoxChart[] = [];
  public scatterChartDropdownX: ApiBoxChart[] = [];
  public scatterChartDropdownY: ApiBoxChart[] = [];

  public chartData:ChartData[] =[];
  public histogramData?: ChartData[] = [];

  public boxPlotCategory: string[] = [];
  public histogramPlotCategory: string[] = [];
  public boxPlotOuter: number[][] = [];
  public boxPlot: number[][] = [];
  public correlationDisable = signal(true);
  public totalRow?: number;
  public totalColumn?: number;
  public histogramLoader = signal(true);
  constructor(private _exploratoryService:ApiService,
    private _downloadService : DownLoadService,
    private _render: Renderer2,
    private _cdr: ChangeDetectorRef,
    private _stremService: StreamingService,
    private _activatedRoute: ActivatedRoute,
    public _exploratory:IfpExploratoryService,
    private _router: Router,
    private _modalService: IfpModalService,
    private _translate: TranslateService,
    private _shortNumber : ShortNumberPipe
  ) {

  }


  ngOnInit(): void {
    this._exploratory.processIcon.set('ifp-icon-rightarrow');
    this._exploratory.processDisable.set(false);
    this._exploratory.secondaryBtn.set(false);
    this._exploratory.processName.set('Proceed to Advanced Analytics');
    this.subs.add(this._exploratory.processEvent.subscribe(data => {
      if (data !== '') {
        this.processClick();
      }
    }));
    this.subs.add(
      this._activatedRoute.queryParams.subscribe((event: Params) => {
        this.summeryId = event['id'];
      })
    );
    this.callSummery();
  }


  processClick() {
    this._router.navigateByUrl(`analytics/exploratory/run?id=${this.summeryId}`);
  }

  callTabularData() {
    this.subs.add(this._exploratoryService.getMethodRequest(prepsApiEndpoints.libraryDataset  +this.summeryId + prepsApiEndpoints.exploratoryTabular, {}, true).subscribe((item: TableDataExploratory[])=> {
      this.tableData = item;
      this.tableDropdown.setValue(item[0]);
      this.customChartTypes.update(value => {
        value[1].disabled = this.tableData.length===0;
        return value;
      });
      this.setTableData();
    }));
  }

  // user to select value from category dropdown
  setTableData() {
    const data = this.tableDropdown.value;
    if (data) {
      this.selectedTableData = data.tabular_data;
    }

  }

  // used to setup y axis of scatter plot
  CallScatterX() {

    this.scatterChartDropdownY.forEach(element => {
      element['disabled']= false;
      if (element.column ===  this.scatterForm.value.scatterXValue?.column) {
        element['disabled']= true;
      }
    });
  }

  // used to setup x axis of scatter plot
  CallScatterY() {
    this.scatterChartDropdownX.forEach(element => {
      element['disabled']= false;
      if (element.column ===  this.scatterForm.value.scatterYValue?.column) {
        element['disabled']= true;
      }
    });

  }

  // call api for histogram Data
  callHistogram(filter: string = this.dropdownHistogramChart.value?.column ?? '') {
    this.histogramLoader.set(true);
    const subs = this._exploratoryService.getMethodRequest(prepsApiEndpoints.exploratoryDataset  +this.summeryId + prepsApiEndpoints.exploratoryGetChart, {type: 'histogram', filter: filter }, true).subscribe({next: (item:{data: {
      min_range: number;
      max_range: number;
      value_count: number;
  }[]})=> {
      this.histogramPlotCategory = [];
      const dataValues: number[] = [];
      item.data?.forEach(element => {
        this.histogramPlotCategory.push(`${element.min_range} - ${element.max_range}`);
        dataValues.push(element.value_count);
      });
      this.histogramData = [
        {
          name: filter,
          data: dataValues,
          color: '#3BD6AD'
        }
      ];
      this.histogramLoader.set(false);
      subs.unsubscribe();
    }, error: () =>{
      this.histogramLoader.set(false);
      this.histogramPlotCategory = [];
    }});

  }


  // used to set gradient to the correlation chart
  perc2color(perc: number) {
    let  r= 0;
    let  g= 0;
    let  b = 0;
    if (perc === 50 ) {
      r = 162;
      g = 162;
      b = 162;
    } else if (perc < 50) {
      r = 255;
      g = this.mapRange(perc, 50, 0, 180, 50 );
      b = this.mapRange(perc,  50, 0, 180, 50);
    } else {
      g = 227;
      // r=  Math.round(152 -( 1.52 * (perc*2)));
      r =this.mapRange(perc, 50, 100, 145, 50);
      b = this.mapRange(perc, 50, 100, 145, 50 );
    }
    return `rgb(${r}, ${g}, ${b})`;
  }

  // used to return color with respect to gradient
  mapRange(value: number, inMin: number, inMax: number, outMin: number, outMax: number) {
    return (((value - inMin) * (outMax - outMin)) / (inMax - inMin)) + outMin;

  }

  // process pop in exploratory
  processedPopup() {
    // this.modalSucess.createElement();
  }

  // used to switch between chart
  chartSelection(event: DbToolbarIcon) {
    this.pearson = false;
    this.spearman =  false;
    this.correlationValue = false;
    this.chartOpen = true;
    this.summeryActive = false;
    this.toggle.set(false);
    this.customChartTypes.update(value => {
      value.forEach(data => {
        data.selected =false;
      });
      return value;
    })
    event.selected = true;
    this.chartType = event.key;
    this.dropdown.setValue(this.data.variables.slice(0, 5));
  }

  /**
   * used to set unique value data to column chart and table
   *
   */
  setChartData() {
    this.bubbleData = [];
    const value = this.selectedValues.map(values => values['No of unique values'] );
    this.xAxis = this.selectedValues.map(values => values.variable);

    /**
   * bubble chart code
   */
    // this.bubbleData.push({
    //   name: 'Variables',
    //   colorAxis: 1,
    //   colorKey: 'x',
    //   data: [
    //   ],
    //   marker: {
    //     lineColor: this._filterService.getColor(0),
    //     fillColor: {
    //       radialGradient: { cx: 0.4, cy: 0.3, r: 0.7 },
    //       stops: [
    //         [0, 'rgba(255,255,255,0.5)'],
    //         [
    //           1,
    //           Highcharts.color(
    //             this._filterService.getColor(0)
    //           ).setOpacity(0.5).get('rgba')
    //         ]
    //       ]
    //     }
    //   }
    // } );
    // this.selectedValues.forEach((data)=> {
    //   this.bubbleData[0].data.push( {name: data.variable, y: data['No of unique values'] ?? 0, z: data['No of unique values'] ?? 0});
    // });
    this.yAxis =  [{
      name: this._translate.instant( 'Unique values'),
      'data': value,
      'dashStyle': 'Solid',
      'type': 'column',
      'zIndex': 1,
      'lineWidth': 0,
      'color': '#3BD6AD',
      'marker': {
        'fillColor': '#3BD6AD',
        'lineWidth': 0,
        'lineColor': '#3BD6AD',
        'symbol': 'circle',
        'enabled': true
      }
    }];
  }

  // click event trigger while clicking on summery
  summeryClick() {
    this.pearson = false;
    this.spearman =  false;
    this.correlationValue = false;
    this.chartOpen = false;
    this.summeryActive = true;
    this.toggle.set(false);
    this.customChartTypes.update(value => {
      value.forEach(data => {
        data.selected =false;
      });
      return value;
    })
  }

  // dropdown click for excel file download
  outsideClick() {
    if (this.showDownloadOptions()) {
      this.showDownloadOptions.set(false);
    }

  }

  // correlation chart click event from side bar
  correlation() {
    this.correlationValue = true;
    this.chartOpen = false;
    this.summeryActive = false;
    this.toggle.set(false);
    this.dropdown.setValue(this.data.variables.slice(0, 5));
    this.customChartTypes.update(value => {
      value.forEach(data => {
        data.selected =false;
      });
      return value;
    })
  }

  // correlation Pearson chart click event from side bar
  correlationPearson () {
    this.correlation();
    this.pearson = true;
    this.spearman =  false;
  }

  // correlation Spearman chart click event from side bar
  correlationSpearman() {
    this.correlation();
    this.pearson = false;
    this.spearman = true;
  }

  toggleData() {
    this.toggle.set(!this.toggle());
  }

  // close the modal pop that indicates what all are the value in the exploratory
  closeSucessModal() {
    this.modalSucess.removeModal();
  }

  // used to switch tab and process value that getting from the popup
  buttonClick(event: CardData) {
    if (event.key === this.popupView[0].key) {
      this.summeryClick();
    } else if (event.key === this.popupView[1].key) {
      this.chartSelection(this.customChartTypes()[0]);
    } else if (event.key === this.popupView[2].key) {
      this.correlationPearson();
    } else if (event.key === 'processClick') {
      this.processClick();
    }
    this.modalSucess?.removeModal();
  }


  // used to call summery api and summery stream
  callSummery() {

    this.summeryLoader = true;
    this.summeryStreemSubscription = this._stremService.connectToServerSentEvents(`${environment.prepbaseUrl+ prepsApiEndpoints.summeryStream +this.summeryId}/`)
      .subscribe({
        next: (data: {data: string, ctrl: AbortController}) => {
          const dataValue = JSON.parse(data.data);
          this.controller  = data.ctrl;
          if ('completed'== dataValue.status) {
            data.ctrl.abort();
            this.summeryStreemSubscription.unsubscribe();
            this.subs.add(
              this._exploratoryService.getMethodRequest(prepsApiEndpoints.libraryDataset  +this.summeryId + prepsApiEndpoints.libraryPreview, {limit: 1, offset: 1, page: 1}, true).subscribe((item: PreivewPrep)=> {
                this.dataName = item.name;
                this.summery = item.metadata?.columns ?? [];
                this.summeryLoader = false;
                this.totalRow = item.total_rows;
                this.totalColumn = item.total_columns;
              })
            );

            // this.modalSucess.createElement();
            this.callChart();
            this.callCorrelation();
            this.boxPlotAiCall();
            this.callTabularData();
          }
        },
        error: () => {
          this._stremService.close();
          this.summeryStreemSubscription.unsubscribe();
        }
      }
      );
  }

  // used to call unique value bubble chart and bar chart
  callChart() {
    this.subs.add(
      this._exploratoryService.getMethodRequest(prepsApiEndpoints.libraryDataset  +this.summeryId + prepsApiEndpoints.libraryChartType, {}, true).subscribe((item:ChartTypes)=> {
        this.data = item;
        this.dropdown.setValue(item.variables.slice(0, 5));
        this.getFilterData(this.dropdown.value ?? []);
      })
    );
  }


  // used to call box-plot api call
  boxPlotAiCall() {
    this.subs.add(
      this._exploratoryService.getMethodRequest(prepsApiEndpoints.exploratoryDataset  +this.summeryId + prepsApiEndpoints.exploratoryGetChart, {type: 'box'}, true).subscribe((item:ApiBoxChart[])=> {
        this.boxChart = item;
        this.customChartTypes.update(value => {
          value[0].disabled = this.boxChart.length===0;
          return value;
        });
        this.scatterChartDropdownX =  cloneDeep(item);
        this.scatterChartDropdownY =  cloneDeep(item);
        if ( this.scatterChartDropdownY.length > 1) {
          this.customChartTypes.update((value) => {
          value.push({
              title: 'Scatter',
              key: 'scatter',
              icon: 'ifp-icon-bubble-chart',
              disabled: false,
              selected: false
            });
            return value;
          });

        }
        if ( item?.length !== 0) {
          this.customChartTypes.update((value) => {
            value.push({
              title: 'Histogram',
              key: 'histogram',
              icon: 'ifp-icon-bar-chart',
              disabled: false,
              selected: false
            });
              return value;
            })
          this.callHistogram( this.boxChart[0].column);
        }

        this.scatterForm.setValue({
          scatterYValue: this.boxChart[1],
          scatterXValue: this.boxChart[0]
        });
        this.CallScatterX();
        this.CallScatterY();
        this.dropdownHistogramChart.setValue(this.boxChart[0]);
        this.dropdownBoxChart.setValue(this.boxChart[0]);
        this.CallBoxPlot();
      })
    );
  }


  // used to plot  box-plot value
  CallBoxPlot() {
    this.boxPlotCategory = [];
    this.boxPlotOuter = [];
    this.boxPlot = [];
    const data = this.dropdownBoxChart.value;
    if (data) {
      this.boxPlotCategory.push(data.column);
      const value = [data.min, ...data.quantiles, data.max];
      this.boxPlot.push(value);
      if (data.lower_outliers.length !== 0) {
        data.lower_outliers.forEach(dataValue=> {
          this.boxPlotOuter.push([0, dataValue]);
        });
      }
      if ( data.upper_outliers.length !==0) {
        data.upper_outliers.forEach(dataValue=> {
          this.boxPlotOuter.push([0, dataValue]);
        });
      }
    }


    const chartData = [{
      name: 'inlayer',
      data: this.boxPlot,
      pointWidth: 100, // Change the width of the boxes
      whiskerColor: '#a6c0ed', // Change the color of the whiskers
      medianColor: '#44b535',
      lineColor: '#a6edd1',
      lineWidth: 3,
      medianWidth: 3,
      fillColor: '#a6edd1',
      whiskerWidth: 3,
      stemColor: '#a6edd1'
    },
    {
      name: 'outlayer',
      type: 'scatter',
      data: this.boxPlotOuter,

      marker: {
        fillColor: '#b5433f', // Change the color of the outliers
        radius: 4 // You can also adjust the radius of the outlier markers
      }
    }];
    this.chartData = chartData;
  }

  // correlation chart api call
  callCorrelation() {
    this.subs.add( this._exploratoryService.getMethodRequest(prepsApiEndpoints.libraryDataset  +this.summeryId + prepsApiEndpoints.correlation, {}, true).subscribe((item:Correlogram)=> {
      this.correlogram = Object.keys(item.pearson);
      this.dropdownCorrelation.setValue(this.correlogram.slice(0, 5));
      this.correlogramData = item;
      if (this.correlogram.length > 1) {
        this.correlationDisable.set(false);
      }
      this.correlogram?.forEach(data=> {
        this.correlogramDataChart.spearman[data] = {};
        this.correlogramDataChart.pearson[data] = {};
        this.correlogram?.forEach(element => {
          // const value = this.correlogramData.spearman[data][element];
          // this.correlogramDataChart.spearman[data][element] =  { value: value, color: this.perc2color((value + 1) * 50), name: element};
          const valuePr = this.correlogramData.pearson[data][element];
          this.correlogramDataChart.pearson[data][element]=  { value: valuePr, color: this.perc2color((valuePr + 1) * 50), name: element};
        });

      });
    })
    );
  }

  getFilterData(currentData: string[]) {
    this.selectedValues = [];
    this.data.data.forEach(data=> {
      if (currentData.includes(data.variable)) {
        this.selectedValues.push({
          variable: data.variable,
          'No of unique values': data.score
        });
      }
    }
    );
    this.setChartData();
  }

  downloadExcelOpen() {
    this.showDownloadOptions.set(!this.showDownloadOptions());
  }

  downloadExcel(selected: boolean) {
    let selectedValue: string[] = [];
    if (selected) {
      selectedValue = this.dropdown.value?.map(data=>  `"${data}"`) ?? [];
    }
    this.subs.add(this._exploratoryService.getDownloadRequest(`${environment.prepbaseUrl}${prepsApiEndpoints.libraryDataset}${this.summeryId}${prepsApiEndpoints.expChartDownload}excel/?selected_columns=[${encodeURIComponent(selectedValue.toString())}]`).subscribe( {next: value=> {
      const matches = (value.headers.get('Content-Disposition'))?.match(/"(.*?)"/);
      const nameValue = matches ? matches[1] : '.xlsx';
      this._downloadService.downloadFiles(value.body, nameValue);
    },
    error: err => {
      const error = err?.error;
      this._exploratoryService.errorHandler(error);
    }
    }) );
  }


  // download image from chart
  downloadImage() {
    this.showValuePrint.set(true);
    this._cdr.detectChanges();
    this._render.removeClass(this.elementChart.nativeElement, 'ifp-exp__download-display');
    this.canvasRender(this.elementChart.nativeElement).then(data=> {
      this._downloadService.downloadFiles(this.convertBase64ToFile(data, `${this.getFileNameWithoutExtension(this.dataName)}.jpg`), `${this.getFileNameWithoutExtension(this.dataName)}.jpg`);
      this.showValuePrint.set(false);
      this._cdr.detectChanges();
    });
    this._render.addClass(this.elementChart.nativeElement, 'ifp-exp__download-display');
  }

  getFileNameWithoutExtension(filename: string) {
    // Split the filename at the last dot
    const lastDotIndex = filename.lastIndexOf('.');

    // If there's no dot, return the original filename
    if (lastDotIndex === -1) {
      return filename;
    }

    // Return the substring before the last dot
    return filename.substring(0, lastDotIndex);
  }

  // convert base64 to image
  convertBase64ToFile(base64Data: any, filename: string) {
    const base64String = base64Data.split(',')[1]; // Assuming the base64Data format is "data:image/png;base64,<actual_base64_data>"
    const byteCharacters = atob(base64String);
    const byteNumbers = new Array(byteCharacters.length);
    for (let i = 0; i < byteCharacters.length; i++) {
      byteNumbers[i] = byteCharacters.charCodeAt(i);
    }
    const byteArray = new Uint8Array(byteNumbers);
    const blob = new Blob([byteArray], { type: 'image/png' });
    return new File([blob], filename, { type: 'image/png' });
  }

  canvasRender(element: HTMLElement) {
    return new Promise((resolve: (value: string) => void) =>{
      html2canvas(element).then(canvas => {
        resolve(canvas.toDataURL('image/png'));
      }
      );
    });
  }

  dropdownChange() {
    this.getFilterData(this.dropdown.value ?? []);
  }



  chartOpenFun() {
    this.chartOpen =  !this.chartOpen;
  }

  ngOnDestroy(): void {
    this.subs.unsubscribe();
    this._modalService.removeAllModal();
    this.controller?.abort();
    this._exploratory.processEvent.next('');
    this.summeryStreemSubscription.unsubscribe();
    this.customChartTypes.update(value => {
      value.forEach(data => {
        data.selected =false;
      });
      return value;
    })
  }
}
interface  ChartTypes {
  variables: string[];
  data: ScoreList[];
}

interface ScoreList {
 score?: number;
  variable: string;
  'No of unique values'?: number;
}

interface Marker {
  fillColor: FillColor;
  lineColor: string;
}

interface FillColor {
  radialGradient: RadialGradient;
  stops: (number | ColorType)[][];
}

interface RadialGradient {
  cx: number;
  cy: number;
  r: number;
}

interface Correlogram {
  pearson: Record< string, Record<string, number>>,
  spearman: Record< string, Record<string, number>>
}


interface ApiBoxChart {
  max: number;
  min: number;
  column: string;
  quantiles: number[];
  lower_outliers: number[];
  upper_outliers: number[];
  disabled?: boolean;
}
interface ChartData {
  name: string;
  data: any[];
  type?: string;
  tooltip?: Tooltip;
  color?: string;
  dataLabels?: {
    enabled: boolean;
    format: string;
    style: {
      fontSize: string;
    }
}
}

interface Tooltip {
  pointFormat: string;
}
interface TableDataExploratory {
  name: string;
  tabular_data: TabularDatumExploratory[];
}

interface TabularDatumExploratory {
  count: number;
  value: null | string | string;
  percentage: number;
}
