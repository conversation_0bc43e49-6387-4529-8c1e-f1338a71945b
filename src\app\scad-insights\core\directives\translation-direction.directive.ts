import { Directive, ElementRef, Input, OnInit, Renderer2 } from '@angular/core';
import { ThemeService } from '../services/theme/theme.service';

@Directive({
  selector: '[appIfpTranslationDirection]',
  standalone: true
})
export class TranslationDirectionDirective implements OnInit{

  constructor(private elementRef: ElementRef, private _themeService: ThemeService, private _render:Renderer2) {

  }


  @Input('appIfpTranslationDirection') string: string ='';

  private arabic = /[\u0600-\u06FF]/;

  ngOnInit(): void {
    if (this.checkArabic(this.string)) {
      if (this._themeService.defaultLang === 'ar') {

      } else {

      }
    } else if (this._themeService.defaultLang === 'ar') {

    } else {

    }

  }

  checkArabic(string:string) {
    return this.arabic.test(string);
  }

}
