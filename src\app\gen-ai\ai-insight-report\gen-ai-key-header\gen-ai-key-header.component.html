<div class="ifp-ai-report__key-drivers ifp-ai-report__side-spacing">
  <div class="ifp-ai-report__module-header">
    <h2 class="ifp-ai-report__module-title">{{'Key Drivers' | translate}}</h2>
    @if (date()!== '') {
    <div class="ifp-ai-report__date">{{date()}}</div>
    }

  </div>
  <div class="ifp-ai-report__drivers-desc">

    <p class="ifp-ai-report__desc">{{summery()| translate}}</p>
    @if (preview()) {
    <ifp-ai-button  [disableTranslate]="true" (ifpClick)="openSumeryEdit()" [iconClass]="'ifp-icon-edit '" [label]="'Edit'"
      [theme]="'ifp-ai-button--round-white ifp-ai-button--round'"></ifp-ai-button>
    }

  </div>
  <div class="ifp-ai-report__cards-wrapper">
    @for (item of drivers(); track $index) {
    <ifp-ai-insight-indicator-card [firstPeroid]="item.curr_period?? ''" [secondPeroid]="item.pre_period ?? ''"
      [id]="item?.domain?.id ?? ''" [name]="item?.domain?.name ?? ''" [consolidated]="consolidated()"
      [arrowDirection]="item.whether_positive_trend" [colorDirection]="item.whether_positive_trend"
      [percentage]="item.value" [indicatorName]="item.indicator_name" [title]="item.heading"
      [iconClass]="'ifp-icon-verifyed-tick'" class="ifp-ai-report__indicator-card"></ifp-ai-insight-indicator-card>
    }
  </div>
</div>
