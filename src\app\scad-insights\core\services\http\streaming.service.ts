import { Injectable, NgZone } from '@angular/core';
import { Observable, Subscriber } from 'rxjs';
import { CookieService } from '../cookie.service';
import { ThemeService } from '../theme/theme.service';
import { IFPMsalService } from '../IFP-msal.service';
import { fetchEventSource, EventSourceMessage } from '@microsoft/fetch-event-source';
import { HttpClient } from '@angular/common/http';
import { authTypes, localStorageKeys } from '../../constants/auth.constants';

@Injectable({
  providedIn: 'root'
})
export class StreamingService {
  private ctrl = new AbortController();
  private header!: {
    'Authorization': string,
    'Accept-Language': string,
    'Connection': string,
    'WSession'?: string,
    'Auth-Type'?: string
  };

  constructor(private zone: NgZone, private _cookie: CookieService, private _theme: ThemeService, private _msalService: IFPMsalService,
    private http: HttpClient
  ) {
  }

  connectToServerSentEvents(url: string): Observable<{ data: string, ctrl: AbortController }> {
    const authtoken = this._msalService.getToken;
    const lang = this._cookie.getCookie('lang') !== '' ? this._cookie.getCookie('lang') : this._theme.defaultLang;
    this.header = {
      'Authorization': `Bearer ${authtoken}`,
      'Accept-Language': lang,
      'Connection': 'keep-alive',
      'Auth-Type': this._msalService.authMethod == authTypes.uaePass ? 'upass' : 'msal'
      // 'WSession': wSessionId
    };

    return new Observable((subscriber: Subscriber<{ data: string, ctrl: AbortController }>) => {
      const ctrl = new AbortController();
      this.ctrl = ctrl;
      const { signal } = ctrl;
      const zone = this.zone;
      fetchEventSource(url, {
        method: 'GET',
        headers: this.header,
        onmessage(msg: EventSourceMessage) {
          zone.run(() => {
            subscriber.next({ data: msg.data, ctrl: ctrl });
          });
        },
        onerror(err: any) {
          zone.run(() => {
            subscriber.error(err);
            ctrl.abort();
            throw Error('');
          });
        },
        signal: signal
      });
    });
  }

  connectToServerSentEventsPost(url: string, data: any): Observable<{ msg: EventSourceMessage, ctrl: AbortController }> {
    const authtoken = this._msalService.getToken;
    const lang = this._cookie.getCookie('lang') !== '' ? this._cookie.getCookie('lang') : this._theme.defaultLang;
    this.header = {
      'Authorization': `Bearer ${authtoken}`,
      'Accept-Language': lang,
      'Connection': 'keep-alive',
      'Auth-Type': this._msalService.authMethod == authTypes.uaePass ? 'upass' : 'msal'
    };
    return new Observable((subscriber: Subscriber<{ msg: EventSourceMessage, ctrl: AbortController }>) => {
      const ctrl = new AbortController();
      this.ctrl = ctrl;
      const { signal } = ctrl;
      const zone = this.zone;
      fetchEventSource(url, {
        openWhenHidden: true,
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'text/event-stream',
          ...this.header
        },
        body: JSON.stringify(data),
        onmessage(msg: EventSourceMessage) {
          zone.run(() => {
            subscriber.next({ msg: msg, ctrl: ctrl });
          });
        },
        onerror(err: any) {
          zone.run(() => {
            subscriber.error(err);
            throw Error('');
          });
        },
        signal
      });
    });
  }


  connectToServerSentEventsPatch(url: string, data: any): Observable<{ msg: EventSourceMessage, ctrl: AbortController }> {
    const authtoken = this._msalService.getToken;
    const lang = this._cookie.getCookie('lang') !== '' ? this._cookie.getCookie('lang') : this._theme.defaultLang;
    this.header = {
      'Authorization': `Bearer ${authtoken}`,
      'Accept-Language': lang,
      'Connection': 'keep-alive',
      'Auth-Type': this._msalService.authMethod == authTypes.uaePass ? 'upass' : 'msal'
    };
    return new Observable((subscriber: Subscriber<{ msg: EventSourceMessage, ctrl: AbortController }>) => {
      const ctrl = new AbortController();
      this.ctrl = ctrl;
      const { signal } = ctrl;
      const zone = this.zone;
      fetchEventSource(url, {
        openWhenHidden: true,
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'text/event-stream',
          ...this.header
        },
        body: JSON.stringify(data),
        onmessage(msg: EventSourceMessage) {
          zone.run(() => {
            subscriber.next({ msg: msg, ctrl: ctrl });
          });
        },
        onerror(err: any) {
          zone.run(() => {
            subscriber.error(err);
            throw Error('');
          });
        },
        signal
      });
    });
  }


  /**
      * Method for closing the connection
      */
  close(): void {
    // this.ctrl.abort();
  }

  convertToFile(filePath: string) {
    return new Promise(resolve => {
      this.http.get(filePath, { responseType: 'blob' }).subscribe((blob: any) => {
        const fileName = 'dashboard.png';
        const file = new File([blob], fileName, { type: blob.type });
        resolve(file);
      });
    });
  }

  getPrompts(url: string) {
    return this.http.get(url);
  }

  postMethodRequest(url: string, data?: any) {
    return this.http.post(`${url}`, data);
  }
}
