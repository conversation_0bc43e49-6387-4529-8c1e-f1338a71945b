@if (!workFlowRunning() && !emptyConnection() && currentConection() && sourceData() && columnList && columnList.length !==0) {
  <div class="ifp-adv-tool__action">
    <div class="ifp-adv-tool__action-inner">
      <ifp-button [label]="'cancel'" (ifpClick)="onCancel()" class="ifp-adv-tool__button ifp-adv-tool__action-btn" [buttonClass]="buttonClass.secondary"></ifp-button>
      <ifp-button [label]="'Save'" (ifpClick)="onSave()" class="ifp-adv-tool__button ifp-adv-tool__action-btn" [buttonClass]="buttonClass.primary"></ifp-button>
    </div>
  </div>
  <div class="ifp-adv-tool__content">
  <div class="ifp-adv-sort__reset">
    <ifp-back-button [isGoBack]="false" [buttonText]="'Reset'" [icon]="'ifp-icon-refresh-round'" (buttonClick)="resetSortOrder()"></ifp-back-button>
  </div>
<div  cdkDropList (cdkDropListDropped)="drop($event)">
  @for (column of columnListView; track column) {
    <ifp-data-tool-accordian  [enableCheckBox]="false" [title]="column.name" [isDraggable]="true" class="ifp-adv-tool__accordian" cdkDrag>
      <div class="ifp-accord-box__content">
        <div class="ifp-adv-tool__edit-outer">
          <p class="ifp-adv-tool__label">{{'Data type' | translate}}: <span class="ifp-adv-tool__label-value">{{column.type | translate}}</span></p>
        </div>
        <div class="ifp-adv-tool__edit-outer">
          <p class="ifp-adv-tool__label">{{'Sort Order' | translate}}</p>

          <app-ifp-db-dropdown [formControl]="formGroup.controls[column.name]?.controls.type" class="ifp-adv-tool__dropdown" [isMultiSelect]="false" [options]="sortOrder" [defaultSelect]="false" [key]="'name'"></app-ifp-db-dropdown>

        </div>
      </div>
    </ifp-data-tool-accordian>
  }
</div>
</div>

<!-- <div class="ifp-adv-tool__content">
  <div class="ifp-adv-tool__checkbox-dropdown" [ngClass]="{'ifp-adv-tool__checkbox-dropdown--active': enableCaseSensitive}">
    <app-ifp-check-box class="ifp-adv-tool__checkbox" [label]="'Enable Case Sensitivity' | translate" [checkedData]="enableCaseSensitive" (checked)="onEnableCaseSensitive($event)"></app-ifp-check-box>
    <em class="ifp-icon ifp-icon-down-arrow"></em>
  </div>
  <div class="ifp-adv-tool__dropdown-list">
    <app-ifp-check-box class="ifp-adv-tool__checkbox ifp-adv-tool__checkbox--radio" [label]="'Uppercase letters first' | translate" [type]="'radio'" [name]="'isUpperFirst'" (checked)="setCaseSensitive($event)" [checkedData]="isUpperFirst"></app-ifp-check-box>
    <app-ifp-check-box class="ifp-adv-tool__checkbox ifp-adv-tool__checkbox--radio" [label]="'Lowercase letters first' | translate" [type]="'radio'" [name]="'isUpperFirst'" (checked)="setCaseSensitive($event)" [checkedData]="!isUpperFirst"></app-ifp-check-box>
  </div>
</div> -->

} @else {
  @if(workFlowRunning()) {
   <div class="ifp-center-loader">
      <app-ifp-spinner></app-ifp-spinner>
      <p>     {{'Workflow Running'}} </p>
    </div>

  } @else if (emptyConnection()){
    <p class="ifp-adv-tool__error"> <em class="ifp-icon ifp-icon-exclamation-round">

    </em>
    {{'Source must have a connection!' | translate}}
    </p>
  } @else if (!sourceData()) {
    <p class="ifp-adv-tool__error"> <em class="ifp-icon ifp-icon-exclamation-round">

    </em>
    {{'You must upload at least one source!' | translate}}
    </p>
  } @else if(!currentConection()){
    <p class="ifp-adv-tool__error"> <em class="ifp-icon ifp-icon-exclamation-round">

    </em>
      {{'Node must have atleast one connection' | translate}}
    </p>
  }
   @else {
    <app-ifp-spinner></app-ifp-spinner>
  }

}

