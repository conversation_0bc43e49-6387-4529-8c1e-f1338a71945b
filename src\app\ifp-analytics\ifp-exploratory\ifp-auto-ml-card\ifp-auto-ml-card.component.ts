import { IfpAnalysisCardHeaderComponent } from '../../../scad-insights/ifp-widgets/ifp-molecules/ifp-analysis-card-header/ifp-analysis-card-header.component';
import { IfpCardComponent } from '../../../scad-insights/ifp-widgets/ifp-atoms/ifp-card/ifp-card.component';
import { ChangeDetectorRef, Component, ElementRef, Input, signal, Renderer2, OnChanges, Output, EventEmitter, ViewChild } from '@angular/core';
import { NgClass } from '@angular/common';
import { TranslateModule } from '@ngx-translate/core';
import { IfpTooltipDirective } from 'src/app/scad-insights/core/directives/ifp-tooltip.directive';
import { QuotRemove } from 'src/app/scad-insights/core/pipes/quotsRemove.pipe';
import { IfpIconTextComponent } from '../../../scad-insights/ifp-widgets/ifp-molecules/ifp-icon-text/ifp-icon-text.component';
import { IfpButtonComponent } from '../../../scad-insights/ifp-widgets/ifp-atoms/ifp-button/ifp-button.component';
import { IfpCardLoaderComponent } from '../../../scad-insights/ifp-widgets/ifp-atoms/ifp-card-loader/ifp-card-loader.component';
import { Router } from '@angular/router';
import { autoMlApiEndpoints } from '../constants/auto-ml.constant';
import { ChartDataScenario, Scenario } from '../ifp-scenario-details/ifp-scenario-details.component';
import { SubSink } from 'subsink';
import { ApiService } from 'src/app/scad-insights/core/services/api.service';
import { IfpAnalyticLineChartComponent } from '../../../scad-insights/ifp-widgets/ifp-molecules/ifp-analytic-line-chart/ifp-analytic-line-chart.component';
import { IfpModalComponent } from '../../../scad-insights/ifp-widgets/ifp-organism/ifp-modal/ifp-modal.component';
import { IfpRemoveCardComponent } from '../../../scad-insights/ifp-widgets/ifp-atoms/ifp-remove-card/ifp-remove-card.component';
import { IfpModalService } from 'src/app/scad-insights/ifp-widgets/ifp-organism/ifp-modal/ifp-modal.service';

@Component({
    selector: 'ifp-auto-ml-card',
    imports: [IfpAnalysisCardHeaderComponent, IfpCardComponent, NgClass, IfpTooltipDirective, TranslateModule, QuotRemove, IfpIconTextComponent, IfpButtonComponent, IfpCardLoaderComponent, IfpAnalyticLineChartComponent, IfpModalComponent, IfpRemoveCardComponent],
    templateUrl: './ifp-auto-ml-card.component.html',
    styleUrl: './ifp-auto-ml-card.component.scss'
})
export class IfpAutoMlCardComponent implements OnChanges {
  @ViewChild('modal') modal!: IfpModalComponent;
  @Input() index!: number;
  @Input() delay = 300;
  @Input() name = '';
  @Input() id !: string;
  @Input() publish: string = '';
  @Input() tagName = 'Scenario Drivers';
  @Input()  subTitle = '';
  @Output() delete = new EventEmitter();

  textLimit: any;
  value: any;
  data: any;

  public small = signal(true);
  public chart = signal(false);
  public loading = signal(true);
  public chartData: ChartDataScenario[] = [];
  public subs = new SubSink();


  constructor(private _cdr: ChangeDetectorRef, private _render: Renderer2, private _elementRef: ElementRef, private _router: Router, private _autoMlService :ApiService, private _modalService: IfpModalService ) {

  }


  ngOnChanges(): void {
    if (this.id) {
      this. chartScenario();
    }
  }

  /**
 * used to add resize class
 *
 * @param {boolean} value
 * @memberof IfpWhatsNewCardComponent
 */
  resize(value: boolean) {
    this.small.set(value);

    if (value) {
      setTimeout(() => {
        this.chart.set(false);
        this._cdr.detectChanges();
      }, this.delay);
      this._render.removeClass(this._elementRef.nativeElement.parentNode, 'ifp-active-card');
    } else {
      setTimeout(() => {
        this.chart.set(true);
        this._cdr.detectChanges();
      }, this.delay);
      this._render.addClass(this._elementRef.nativeElement.parentNode, 'ifp-active-card');
    }
  }

  // runScenario while api call
  chartScenario(ColumnData:  {column: string, value: number }[]  = []) {
    this.loading.set(true);
    this.subs.add(
      this._autoMlService.postMethodRequest(autoMlApiEndpoints.scenarioDetail + this.id +autoMlApiEndpoints.scenarioChart, {data: {driver: ColumnData}}, {}, true).subscribe((data: Scenario) => {

        const chartData: ChartDataScenario =  {
          'name': data.y_axis,
          'data': [


          ],
          // legendPositions: {isLegend: false},
          'actualValue': [],
          'monthValues': [

          ],
          'yearValues': [

          ],
          'dashStyle': 'Solid',
          'type': 'line',
          'zIndex': 1,
          'identifier': data.x_axis,
          'lineWidth': 2,
          'color': '#3BD6AD',
          'marker': {
            'fillColor': '#3BD6AD',
            'lineWidth': 2,
            'lineColor': '#3BD6AD',
            'symbol': 'circle',
            'enabled': true,
            radius: 2
          }
        };
        const chartDataForecast: ChartDataScenario = {
          'name': `${data.y_axis} -forecast`,
          // legendPositions: {isLegend: false},
          'data': [

          ],
          'monthValues': [

          ],
          'yearValues': [

          ],
          'actualValue': [],
          'type': 'line',
          'dashStyle': 'Dash',
          'identifier': data.x_axis,
          'zIndex': 1,
          'lineWidth': 2,
          'color': '#3BD6AD',
          'marker': {
            'enabled': true,
            'fillColor': '#3BD6AD',
            'lineWidth': 2,
            'lineColor': '#3BD6AD',
            'symbol': 'square',
            radius: 2
          },
          'pointStart': 11
        };

        const chartDataForecaster: ChartDataScenario =   {
          // legendPositions: {isLegend: false},
          'name': data.y_axis,
          'data': [

          ],
          'actualValue': [],
          'monthValues': [

          ],
          'yearValues': [

          ],
          'dashStyle': 'Dash',
          'type': 'arearange',
          'zIndex': 0,
          'identifier': data.x_axis,
          'lineWidth': 0,
          'color': '#3BD6AD',
          'marker': {
            'enabled': true,
            'symbol': 'square',
            radius: 2
          },
          'zones': [
            {
              'value': 0,
              'color': '#3BD6AD'
            },
            {
              'color': '#3BD6AD'
            }
          ],
          'zoneAxis': 'x',
          'linkedTo': ':previous',
          'fillOpacity': 0.3,
          'pointStart': 11
        };
        const dataNowCast: number[][] = [];
        data.data.forEach(dataValue => {
          // Create a Date object
          dataNowCast.push([this.getConvertDate( dataValue.date), dataValue.value]);
        });
        // Create a Date object
        const chartDataValue = dataNowCast.slice(-12);
        const date = data.data[data.data.length-1].date;
        chartData.data = chartDataValue;
        chartDataForecast.data.push([this.getConvertDate(date), data.data[data.data.length-1].value]);
        chartDataForecaster.data.push([this.getConvertDate(date), data.data[data.data.length-1].value, data.data[data.data.length-1].value]);
        data.forcast.forEach(dataValue => {
          const convertedDate = this.getConvertDate(dataValue.date);
          chartDataForecast.data.push([convertedDate, dataValue.forcast]);
          chartDataForecaster.data.push([convertedDate, dataValue.forcast_ll, dataValue.forcast_ul]);
        });
        this.chartData = [
          chartData,
          chartDataForecast,
          chartDataForecaster
        ];
        this.loading.set(false);
      })
    );
  }

  // used to remove scenario
  removeScenario() {
    this.subs.add(
      this._autoMlService.getDeleteRequest(autoMlApiEndpoints.scenarioDelete + this.id +autoMlApiEndpoints.scenarioDeleteTag, true).subscribe(() => {
        this.delete.emit();
      }));
  }

  // modal to open scenario
  removeScenarioModal() {
    this.modal.createElement();
  }

  closeModalEvent(event: boolean) {
    this.modal.removeModal();
    this._modalService.removeAllModal();
    if (event) {
      this.removeScenario();
    }
  }

  // used to convert date to utc format
  getConvertDate(dateValue: string) {
    const date = new Date(dateValue);

    // Get the UTC timestamp
    return Date.UTC(date.getUTCFullYear(), date.getUTCMonth(), date.getUTCDate(),
      date.getUTCHours(), date.getUTCMinutes(), date.getUTCSeconds());
  }

  // redirect to detail-page
  urlRedirect() {
    this._router.navigateByUrl(`/scenario-details?id=${this.id}&editDisable=true`);
  }
}
