import { Observable } from 'rxjs';
import { Injectable } from '@angular/core';
import { commonApi } from '../../apiConstants/common-api.constants';
import { HttpService } from '../http/http.service';
import { Header<PERSON><PERSON> } from '../../apiConstants/header.api.constants';

@Injectable({
  providedIn: 'root'
})
export class NotificationService {

  constructor(private _http: HttpService) { }

  getNotificationMapped() {
    return this._http.get(`${commonApi.notificationMappedData}`);
  }

  setNotificationMapped(data: any, enableEmail: boolean = false) {
    // return this._http.post(`${commonApi.setNotificationMappedData}?isEmail=${enableEmail}&=compare=${data.compare}`, {
    //   id: data.id,
    //   contentType: data.contentType != '' ? data.contentType : undefined,
    //   appType: data.appType != '' ? data.appType : undefined
    // });
    return this._http.post(`${commonApi.setNotificationMappedData}?isEmail=${enableEmail}&=compare=${data.compare}`, data);
  }

  unsubscribeNotificationMapped(id: string) {
    return this._http.post(`${commonApi.unsubscribeNotificationMappedData}`, {
      'id': id
    });
  }

  readNotification(id: number) {
    return this._http.post(`${commonApi.notificationRead}`, { 'id': id });
  }

  getNotificationAll(pageNumber: number = 1, status: string = '') {
    return status === '' ? this._http.get(`${HeaderApi.notification}?page=${pageNumber}`) : this._http.get(`${HeaderApi.notification}?page=${pageNumber}&status=${status}`);
  }

  setEmailMapped(data: { nodeId: string }) {
    return this._http.post(commonApi.setEmailMappedData, {
      id: data.nodeId
    });
  }

  unsubscribeEmailMapped(nodeId: string) {
    return this._http.post(`${commonApi.unsubscribeEmailMappedData}`, {
      'id': nodeId
    });
  }

  getEnabledNotification(): Observable<any> {
    return this._http.get(commonApi.enabledNotification);
  }
}
