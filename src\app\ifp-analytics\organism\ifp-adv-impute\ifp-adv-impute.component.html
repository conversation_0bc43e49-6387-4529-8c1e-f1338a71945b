@if (!workFlowRunning() && !emptyConnection() && currentConection() && sourceData() && columnList && columnList?.length&& !errorMessageForTools) {
  <!-- <div class="ifp-adv-tool__action">
    <div class="ifp-adv-tool__action-inner">
      <ifp-button [label]="'cancel'" (ifpClick)="onCancel()" class="ifp-adv-tool__button ifp-adv-tool__action-btn" [buttonClass]="buttonClass.secondary"></ifp-button>
      <ifp-button [label]="'Save'" (ifpClick)="onSave()" class="ifp-adv-tool__button ifp-adv-tool__action-btn" [buttonClass]="isConfigUpdated() ?  buttonClass.primary: buttonClass.disabled"></ifp-button>
    </div>
  </div> -->
  <form [formGroup]="imputeForm" class="ifp-adv-tool__content">
    <ifp-data-tool-accordian [title]="'New Impute'" [enableCheckBox]="false" [isAccordianExpanded]="true" class="ifp-adv-tool__accordian ifp-adv-tool__accordian--tool-box">
      <div class="ifp-accord-box__content">
        <div class="ifp-adv-tool__content-inner">
        @if (columnList && columnList.length) {
          <div class="ifp-adv-tool__edit-outer">
            <p class="ifp-adv-tool__label ifp-adv-tool__label-imp">{{'Select Column' | translate}}</p>
            <app-ifp-dropdown class="ifp-adv-tool__dropdown" [singleDefaultSelect]="false" [selectedValue]="selectedColumn" [key]="'name'" [dropDownItems]="columnList"
            (dropDownItemClicked)="selectColumn($event)" formControlName="column" [disableSingleValue]="true" [disableTranslation]="false"></app-ifp-dropdown>
            @if ((submitted()) && getControls['column'].invalid) {
              <p class="ifp-adv-tool__error"><em class="ifp-icon ifp-icon-exclamation-round"></em>{{'Please select a column' | translate}}</p>
            }
          </div>
        }
        <p class="ifp-adv-tool__label">{{'Data Type' | translate}}: <span class="ifp-adv-tool__label-value">{{ selectedDatatype | translate}}</span></p>
        <p class="ifp-adv-tool__sub-title">{{'Value to replace' | translate}}</p>
        <app-ifp-check-box class="ifp-adv-tool__checkbox ifp-adv-tool__checkbox--radio"
        [label]="'Null' | translate"
        [type]="'radio'"
        [name]="'incomingValue'"
        [id]="'null'"
        (checked)="valueToReplace(true)"
        [checkedData]="isNull"></app-ifp-check-box>

        <app-ifp-check-box class="ifp-adv-tool__checkbox ifp-adv-tool__checkbox--radio"
        [label]="'User-specified value' | translate"
        [type]="'radio'"
        [name]="'incomingValue'"
        [id]="'specified'"
        (checked)="valueToReplace(false)"
        [checkedData]="!isNull"></app-ifp-check-box>

        <div class="ifp-adv-tool__edit-outer" [ngClass]="{'ifp-adv-tool__edit-outer--disabled': isNull}">
          <p class="ifp-adv-tool__label">{{'Enter Value' | translate}}</p>
          <input type="number" class="ifp-adv-tool__input" formControlName="user_specified" [decimal]="true" appNumberOnly>
          @if (imputeForm.controls['user_specified'].invalid) {
            <p class="ifp-adv-tool__error"><em class="ifp-icon ifp-icon-exclamation-round"></em>{{'Please specify a field' | translate}}!</p>
          }
        </div>
        </div>
        <div class="ifp-adv-tool__content-inner">
          <div class="ifp-adv-tool__edit-outer">
            <p class="ifp-adv-tool__label">{{'Replace with Value' | translate}}</p>
            <app-ifp-dropdown class="ifp-adv-tool__dropdown" [singleDefaultSelect]="false" [selectedValue]="selectedImpute" [key]="'label'" [dropDownItems]="imputeOptsDropdown"
            (dropDownItemClicked)="onSelectMethod($event)" formControlName="method" [disableSingleValue]="true" ></app-ifp-dropdown>

            @if (imputeForm.controls['method'].invalid && submitted()) {
              <p class="ifp-adv-tool__error"><em class="ifp-icon ifp-icon-exclamation-round"></em>{{'Please select an impute method' | translate}}</p>
            }
          </div>
          @if (imputeForm.value.method.method === 'custom') {
            <div class="ifp-adv-tool__edit-outer">
              <p class="ifp-adv-tool__label">{{'New Value' | translate}}</p>
              <input type="number" class="ifp-adv-tool__input" formControlName="value" [decimal]="true" appNumberOnly>
              @if (imputeForm.controls['value'].invalid) {
                <p class="ifp-adv-tool__error"><em class="ifp-icon ifp-icon-exclamation-round"></em>{{'Please provide a new value' | translate}}!</p>
              }
            </div>
          }
          <app-ifp-check-box class="ifp-adv-tool__checkbox ifp-adv-tool__checkbox--radio"
          [label]="'Populate the existing column' | translate"
          [type]="'radio'"
          [name]="'isFillColumn'"
          [id]="'fillColumn'"
          (checked)="onSetOutput(true)"
          [checkedData]="isFillCol"></app-ifp-check-box>

          <app-ifp-check-box class="ifp-adv-tool__checkbox ifp-adv-tool__checkbox--radio"
          [label]="'Create a new column' | translate"
          [type]="'radio'"
          [name]="'isFillColumn'"
          [id]="'newCol'"
          (checked)="onSetOutput(false)"
          [checkedData]="!isFillCol"></app-ifp-check-box>
          <div class="ifp-adv-tool__edit-outer" [ngClass]="{'ifp-adv-tool__edit-outer--disabled': isFillCol}">
            <p class="ifp-adv-tool__label">{{'New column Name' | translate}}</p>
            <input type="text" class="ifp-adv-tool__input" formControlName="new_column">
            @if (imputeForm.controls['new_column'].invalid) {
              <p class="ifp-adv-tool__error"><em class="ifp-icon ifp-icon-exclamation-round"></em>{{'Please provide a new column name' | translate}}!</p>
            }
          </div>
        </div>
        @if (isSelectActive) {
          <div class="ifp-adv-tool__update">
            <div class="ifp-adv-tool__action-inner">
              <ifp-button [label]="'cancel'" (ifpClick)="resetForm()" class="ifp-adv-tool__button ifp-adv-tool__action-btn" [buttonClass]="buttonClass.secondary"></ifp-button>
              <ifp-button [label]="'Update'" (ifpClick)="addImpute()" class="ifp-adv-tool__button ifp-adv-tool__action-btn" [buttonClass]="buttonClass.primary"></ifp-button>
            </div>
          </div>
        } @else {
          <ifp-button [label]="'Add impute'" (ifpClick)="addImpute()" class="ifp-adv-tool__button"
          [buttonClass]="(imputeForm.valid ? buttonClass.secondary : buttonClass.disabled)+ ' '+ buttonClass.large" [iconClass]="'ifp-icon-plus'"></ifp-button>
        }
      </div>
    </ifp-data-tool-accordian>

  </form>

    @if (imputeList.length) {
    <div class="ifp-adv-tool__content ifp-adv-tool__content--grey">
      <p class="ifp-adv-tool__sub-title">{{'Added Imputes' | translate}}</p>
      @for (imp of imputeList; track imp) {
        <div class="ifp-adv-tool__card" (click)="selectimpute(imp, $index)">
        <div class="ifp-adv-tool__card-head">
          <p class="ifp-adv-tool__card-title">{{(imp.new_column && imp.new_column !== '' ? imp.new_column : imp.column) | translate}}</p>
          <em class="ifp-icon ifp-icon-minus-round ifp-adv-tool__remove" (click)="removeImpute($index)"></em>
        </div>
        <p class="ifp-adv-tool__card-text" >{{'Replaced' | translate}} <span
            class="ifp-adv-tool__card-bold">{{imp?.user_specified !== '' ? imp.user_specified : 'Null' | translate}}</span> {{'with' | translate}}
            @if (imp.value && imp.value !== '') {
              <span class="ifp-adv-tool__card-bold">{{imp.value}}</span>
            } @else {
              <span class="ifp-adv-tool__card-bold">{{imp.method.method | translate}}</span>
            }
          </p>
      </div>
      }
    </div>
    }
  } @else {
    @if(errorMessageForTools) {
      {{errorMessageForTools}}
    } @else if(workFlowRunning()) {
      <div class="ifp-center-loader">
          <app-ifp-spinner></app-ifp-spinner>
          <p>{{'Workflow Running' | translate}} </p>
        </div>
      } @else if (emptyConnection()){
        <p class="ifp-adv-tool__error"><em class="ifp-icon ifp-icon-exclamation-round"></em>
        {{'Source must have a connection!' | translate}}
        </p>
      } @else if (!sourceData()) {
        <p class="ifp-adv-tool__error"><em class="ifp-icon ifp-icon-exclamation-round"></em>
        {{'You must upload at least one source!' | translate}}
        </p>
      } @else if(!currentConection()){
        <p class="ifp-adv-tool__error"><em class="ifp-icon ifp-icon-exclamation-round"></em>
          {{'Node must have atleast one connection' | translate}}
        </p>
      }
      @else if (isNoImputeColumn) {
        <app-ifp-no-data [message]="'No Impute Columns are available!' | translate"></app-ifp-no-data>
      }
      @else {
        <app-ifp-spinner></app-ifp-spinner>
      }
  }
