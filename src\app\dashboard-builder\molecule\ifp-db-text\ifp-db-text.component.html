<div class="ifp-db-text">
  @if (!isDescription) {
  <div class="ifp-db-text__title">
    <h3 class="ifp-db-text__title-h3">{{textAreaTitle}}</h3>
    <textarea class="ifp-db-text__text-area" rows="3" [value]="titleValue" (keyup)="updateTitle($event)"></textarea>
  </div>

  <div class="ifp-db-text__props">
    <h4 class="ifp-db-text__props-heading">{{'Text Settings' | translate}}</h4>
    <ifp-db-chart-props [propName]="'Color'" class="ifp-legend-props__props-item">
      <div class="ifp-chart-props__type">
        <input type="color" [value]="selectedTextColor" (change)="getColor($event)" class="ifp-chart-props__color-picker">
      </div>
    </ifp-db-chart-props>
  </div>

  <div class="ifp-chart-style__props">
    <ifp-db-chart-props [propName]="'Font Size'" class="ifp-chart-style__props-item">
      <div class="ifp-chart-props__type">
        <ifp-input-counter [value]="selectedTextSize" [key]="'fontSize'"
          (updateSpacingValue)="updateSpacingValue($event)"></ifp-input-counter>
      </div>
    </ifp-db-chart-props>
  </div>
  } @else {
    <div class="ifp-db-text__title">
      <textarea class="ifp-db-text__text-area" rows="3" [value]="textAreaData" (keyup)="onContentChanged($event)"></textarea>
    </div>

    <div class="ifp-db-text__props">
      <h4 class="ifp-db-text__props-heading">{{'Text Settings' | translate}}</h4>
      <ifp-db-chart-props [propName]="'Color'" class="ifp-legend-props__props-item">
        <div class="ifp-chart-props__type">
          <input type="color" [value]="selectedTextColor" (change)="getTextAreaColor($event)" class="ifp-chart-props__color-picker">
        </div>
      </ifp-db-chart-props>
    </div>

    <div class="ifp-chart-style__props">
      <ifp-db-chart-props [propName]="'Font Size'" class="ifp-chart-style__props-item">
        <div class="ifp-chart-props__type">
          <ifp-input-counter [value]="selectedTextSize" [key]="'fontSize'"
            (updateSpacingValue)="updateDescriptionFontValue($event)"></ifp-input-counter>
        </div>
      </ifp-db-chart-props>
    </div>
  }

</div>
