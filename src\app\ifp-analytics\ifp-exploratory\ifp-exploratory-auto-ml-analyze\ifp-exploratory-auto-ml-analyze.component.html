
  <div class="ifp-ml-analyzes ifp-container">
    <h1  class="ifp-ml-analyzes__heading">
      {{'Selected variable for analysis' | translate }} <span class="ifp-ml-analyzes__heading-sub"> {{metaData?.target_variable ??'' | translate}}</span>
    </h1>
    @if (loaderIteration || loaderIterationColumn || iterationLoader) {
      <app-ifp-spinner class=" ifp-ml-analyzes__loader"></app-ifp-spinner>
    } @else  {
    <div class=" ifp-ml-analyzes__sec">
      <div  class="ifp-ml-analyzes__sec-left">
        @for (item of iterationList; track item.name; let index = $index) {
          <ifp-auto-ml-iteration-card (deleteEventEmit)="deleteEventEmit($event, index)" [iterationItem]="item" (expandedEventEmit)="expandedEventEmit($event, item)" class="ifp-ml-analyzes__accordion" [chartValueName]="'Accuracy'" [badgeData]="item?.metadata?.['iteration variables'] ?? []"  [chartValue]="(item?.metadata?.score ?? 0)/100"
          [chartList]="[
            {name: ('MAE' | translate) +' ', value: item?.metadata?.['model metrics']?.MAE ?? 0},
            {name: ('MSE'| translate) + ' ', value: item?.metadata?.['model metrics']?.MSE ?? 0},
            {name: ('R-SQUARED'| translate )+' ', value: item?.metadata?.['model metrics']?.R2 ?? 0},
            {name: ('Model'| translate) + ' ', value: 'AutoML', string: true},
          ]"
          [aiText]="item?.metadata?.['ai explanation'] ?? ''"
          [isAccordianExpanded]="item.object_id === selectedAccordion"
          (valueChangeEvent)="valueChange($event, item.object_id, index)"
          [chartTitle]="item.name"
          [deleteIcon]="item.initial_iteration ?  false: 'ifp-icon-trash'"
          ></ifp-auto-ml-iteration-card>
        }

        <div  class="ifp-ml-analyzes__legends">
          <h6 class="ifp-ml-analyzes__legends-head">{{'What does it indicate ?' | translate}} </h6>
          @for (item of legend; track item) {
            <div class="ifp-ml-analyzes__legend">
              <div class="ifp-ml-analyzes__legend-badge" [ngClass]="{
                'ifp-ml-analyzes__legend-badge--selected': item.value === legend[0].value,
                'ifp-ml-analyzes__legend-badge--un-selected ': item.value === legend[1].value,
                'ifp-ml-analyzes__legend-badge--ignore': item.value === legend[2].value,
              }">
              {{item.name | translate}}</div>

            </div>
          }

        </div>
      </div>
      <div  class="ifp-ml-analyzes__sec-right">
      @if(selection) {
          <div  class="ifp-ml-analyzes__run-model">
            <ifp-auto-ml-variable-selection (moveBackEvent)="reAnalyzeEvent(false)" (runModel)="runModel()" (selectedEvent)="selections($event)"  class="ifp-ml-analyzes__model-selection" [timeStamp]="metaData?.time_stamp_variable ??''" [targetValue]="metaData?.target_variable ??''" [badgeData]="correlation"></ifp-auto-ml-variable-selection>
            <ifp-auto-ml-correlation-chart  [barData]="correlation" class="ifp-ml-analyzes__model-correlation"></ifp-auto-ml-correlation-chart>
          </div>
        }@else {
            <ifp-auto-ml-importance-score-card  class="ifp-ml-analyzes__importance-score-card" (runScenarioEvent)="runScenario()" [selectedIteration]="selectedIteration" (selectedIterationEvent)="selectedIterationEvent($event)" (reAnalyzeEvent)="reAnalyzeEvent(true)" [columnList]="columnList" [iterationList]="iterationList"></ifp-auto-ml-importance-score-card>

        }

      </div>
    </div>
  }
  </div>

