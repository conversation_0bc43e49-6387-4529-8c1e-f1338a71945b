import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, ElementRef, EventEmitter, Input, Output, Renderer2 } from '@angular/core';
import { Subject, debounceTime } from 'rxjs';
import { SubSink } from 'subsink';
import { IfpDbChartPropsComponent } from '../ifp-db-chart-props/ifp-db-chart-props.component';
import { TranslateModule } from '@ngx-translate/core';
import { IfpInputCounterComponent } from '../../atom/ifp-input-counter/ifp-input-counter.component';
import { FormsModule } from '@angular/forms';




@Component({
    selector: 'ifp-ifp-db-text',
    templateUrl: './ifp-db-text.component.html',
    styleUrl: './ifp-db-text.component.scss',
    changeDetection: ChangeDetectionStrategy.OnPush,
    imports: [
        CommonModule,
        IfpDbChartPropsComponent,
        TranslateModule,
        IfpInputCounterComponent,
        FormsModule
    ]
})
export class IfpDbTextComponent {

  @Input() textAreaTitle!: string;
  @Input() titleValue!: string;
  @Input() selectedTextColor!:string;
  @Input() selectedTextSize!: number;
  @Input() textAreaData: string = '';
  @Input() isDescription: boolean = false;

  @Output() updateIndicatorTitle: EventEmitter<string> = new EventEmitter<string>();
  @Output() updateTextColor: EventEmitter<string> = new EventEmitter<string>();
  @Output() updateDescriptionColor: EventEmitter<string> = new EventEmitter<string>();
  @Output() updateTextFontSize: EventEmitter<string> = new EventEmitter<string>();
  @Output() updateDescription: EventEmitter<string> = new EventEmitter<string>();
  @Output() updateDescriptionFontSize: EventEmitter<string> = new EventEmitter<string>();

  headerTitle = new Subject<string>();
  public subs = new SubSink();
  public isClone: boolean = false;

  public atValues = [
    { id: 1, value: 'Fredrik Sundqvist', link: 'https://google.com' },
    { id: 2, value: 'Patrik Sjölin' }
  ];

  public hashValues = [
    { id: 3, value: 'Fredrik Sundqvist 2' },
    { id: 4, value: 'Patrik Sjölin 2' }
  ];

  public quillConfig = {
    // toolbar: '.toolbar',
    toolbar: {
      container: [
        ['bold', 'italic', 'underline', 'strike'],        // toggled buttons
        [{ 'header': 1 }, { 'header': 2 }],               // custom button values
        [{ 'list': 'ordered' }],
        // [{ 'script': 'sub'}, { 'script': 'super' }],      // superscript/subscript
        [{ 'indent': '-1'}, { 'indent': '+1' }],          // outdent/indent
        [{ 'direction': 'rtl' }],                         // text direction
        [{ 'size': ['small', false, 'large', 'huge'] }],  // custom dropdown
        [{ 'header': [1, 2, 3, 4, 5, 6, false] }],
        [{ 'align': [] }]
      ]

    },
    keyboard: {
      bindings: {
        shiftEnter: {
          key: 13,
          shiftKey: true,
          handler: (_range: any, _context: any) => {
            // Handle shift+enter
            // console.log("shift+enter")
          }
        },
        enter: {
          key: 13,
          handler: (_range: any) => {
            // console.log('enter');
            return true;
          }
        }
      }
    }
  };

  constructor(private el: ElementRef, private renderer: Renderer2) {
    this.subs.add(this.headerTitle
      .pipe(debounceTime(800))
      .subscribe((value) => {
        if (!this.isDescription) {
          this.updateIndicatorTitle.emit(value);
        } else {
          this.updateDescription.emit(value);
        }
      }));
  }


  updateTitle(event: any) {
    this.headerTitle.next(event.target.value);
    // if (title) {
    //   this._dashboardService.updateIndicatorTitle(title, this.contentType, this.selectedId);
    // }
  }

  getColor(event: any) {
    this.selectedTextColor = event.target.value;
    this.updateTextColor.emit(event.target.value);
  }

  getTextAreaColor(event:any){
    this.selectedTextColor = event.target.value;
    this.updateDescriptionColor.emit(event.target.value);
  }

  updateSpacingValue(event: any) {
    this.selectedTextSize = event.value;
    this.updateTextFontSize.emit(event);
  }

  updateDescriptionFontValue(event: any) {
    this.selectedTextSize = event.value;
    this.updateDescriptionFontSize.emit(event);
  }

  onSelectionChanged = (_event: any) => {

  }

  onContentChanged = (event: any) => {
    this.textAreaData = event.target.value;
    this.headerTitle.next(this.textAreaData);
  };

}
