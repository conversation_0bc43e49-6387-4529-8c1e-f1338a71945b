import { ChangeDetectionStrategy, Component, ElementRef, EventEmitter, Input, Output, ViewChild } from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';
import { buttonClass } from 'src/app/scad-insights/core/constants/button.constants';

@Component({
    selector: 'ifp-advance-right-panel',
    imports: [TranslateModule],
    templateUrl: './ifp-advance-right-panel.component.html',
    styleUrl: './ifp-advance-right-panel.component.scss',
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class IfpAdvanceRightPanelComponent {

  @ViewChild('rightPanel') rightPanel!: ElementRef;

  @Input({ required: true }) name!: string;
  @Input() description: string = '';

  @Output() cancel = new EventEmitter();
  @Output() save: EventEmitter<string> = new EventEmitter<string>();

  public buttonClass = buttonClass;
  public noteCmmnt: string = 'Clicking the Run button will automatically download the output by default. If you prefer other options, select the desired option before running the process.';


  onCancel() {
    this.cancel.emit(true);
  }

  onSave() {
    this.save.emit(this.name);
  }

  getNativeElement() {
    return this.rightPanel;
  }
}
