import { Component, inject, OnDestroy, output, signal } from '@angular/core';
import { FileData, IfpDbFileUploaderComponent,  } from '../../dashboard-builder/molecule/ifp-db-file-uploader/ifp-db-file-uploader.component';
import { IfpUploadBarComponent } from '../../ifp-analytics/molecule/ifp-upload-bar/ifp-upload-bar.component';
import { fileFormats } from 'src/app/dashboard-builder/molecule/ifp-db-file-uploader/ifp-db-file-uploader.constants';
import { TranslateModule } from '@ngx-translate/core';
import { DownLoadService } from 'src/app/scad-insights/core/services/download-service/download.service';
import { SubSink } from 'subsink';
import { environment } from 'src/environments/environment';
import { genAiTestingApi } from '../constents/gen-ai-testing.constant';
import { ApiGenAiService } from 'src/app/scad-insights/core/services/api-gen-ai.service';
import { ToasterService } from 'src/app/scad-insights/core/services/tooster/ToastrService.service';

@Component({
  selector: 'ifp-gen-ai-dashboard-upload',
  imports: [IfpDbFileUploaderComponent, IfpUploadBarComponent, TranslateModule],
  templateUrl: './gen-ai-dashboard-upload.component.html',
  styleUrl: './gen-ai-dashboard-upload.component.scss'
})
export class GenAiDashboardUploadComponent implements OnDestroy{
  public allowedExtensions = ['.xlsx'];
  public _apiService = inject(ApiGenAiService);
  public _downloadService = inject(DownLoadService);
  public runId = output<string>();
  public fileRemoved = output();
  public fileData = signal<File>(new File([], ''));
  public fileName = signal('');
  public fileProgress = signal(0);
  public fileError = signal(false);
  public runIdValue = signal<string>('');
  public subs = new  SubSink();
  private _toaster = inject(ToasterService);
  downloadFile() {
   this.subs.add(this._apiService.getDownloadRequest(`${genAiTestingApi.templateDownload}`).subscribe({
      next: data => {
        const matches = (data.headers.get('Content-Disposition'))?.match(/"(.*?)"/);
        const nameValue = matches ? matches[1] : '.xlsx';
        this._downloadService.downloadFiles(data.body, nameValue);
      },
      error: err => {
        const error = err?.error;
        this._apiService.errorHandler(error);
      }
    }));
  }

  uploadFile(fileData: FileData) {
    const formData = new FormData()
    this.fileData.set(fileData.file[0]);
    this.fileName.set(this.fileData().name);
    formData.append('file',fileData.file[0] );
    this.fileProgress.set(0);
    this.fileError.set(false);
    this.subs.add(this._apiService.getUploadData(genAiTestingApi.uploadFile,formData).subscribe({next: data => {
     this.fileProgress.set( Math.floor((data?.loaded/ data?.total)*100));
     if(data.type === 4) {
      this.runIdValue.set(data.body.run_id);
      this.runId.emit(data.body.run_id);
     }
    },
    error:() => {
      this.fileError.set(true);
      this._toaster.error('This file is unsupported')
    }
  }))
  }

  removeFile() {
    this.fileData.set(new File([], ''));
    this.fileName.set('');
    this.fileProgress.set(0);
    this.fileError.set(false);
    this.fileRemoved.emit();
  }

  ngOnDestroy(): void {
    this.subs.unsubscribe();
  }
}
