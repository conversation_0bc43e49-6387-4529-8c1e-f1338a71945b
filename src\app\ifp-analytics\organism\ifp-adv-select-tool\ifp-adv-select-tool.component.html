@if (!workFlowRunning() && !emptyConnection() && currentConection() && sourceData() && columnList && columnList.length
!==0 && !errorMessageForTools) {
<!-- <div class="ifp-adv-tool__action">
  <div class="ifp-adv-tool__action-inner">
    <ifp-button [label]="'cancel'" (ifpClick)="onCancel()" class="ifp-adv-tool__button ifp-adv-tool__action-btn"
      [buttonClass]="buttonClass.secondary"></ifp-button>
    <ifp-button [label]="'Save'" (ifpClick)="onSave()" class="ifp-adv-tool__button ifp-adv-tool__action-btn"
      [buttonClass]="isConfigUpdated() ? buttonClass.primary : buttonClass.disabled"></ifp-button>
  </div>
</div> -->

<form [formGroup]="formGroup">

  <ifp-data-tool-accordian [title]="'Change data type/rename column'" [enableCheckBox]="false" [isAccordianExpanded]="true"
    class="ifp-adv-tool__accordian ifp-adv-tool__accordian--tool-box">
    <div class="ifp-accord-box__content">
      <div class="ifp-adv-tool__edit-outer">
        <p class="ifp-adv-tool__label">{{'Select Column' | translate}}</p>
        @if (columnList && columnList.length) {
        <app-ifp-dropdown class="ifp-adv-tool__dropdown" [key]="'name'" [dropDownItems]="columnList"
          (dropDownItemClicked)="selectColumn($event)" formControlName="column"
          [disableSingleValue]="true" [disableTranslation]="true"></app-ifp-dropdown>
        @if (f['column'].errors?.['required'] && submitted()) {
        <p class="text-danger">
          {{'*Please choose a specific column.' | translate}}
        </p>
        }
        }

      </div>

      <div class="ifp-adv-tool__edit-outer">
        <p class="ifp-adv-tool__label">{{'Select Data Type' | translate}}</p>
        <app-ifp-dropdown class="ifp-adv-tool__dropdown" [dropDownItems]="allowedTypes"
          (dropDownItemClicked)="selectType($event)" formControlName="dataType"
          [disableDropdown]="(allowedTypes && allowedTypes.length <= 0) || disableCoumnDropDown()"
          [disableSingleValue]="true" [disableTranslation]="false">
        </app-ifp-dropdown>
        @if (f['dataType'].errors?.['required'] && submitted()) {
        <p class="text-danger">
          {{'*Please choose a specific data type.' | translate}}
        </p>
        }
      </div>

      <div class="ifp-adv-tool__edit-outer">
        <p class="ifp-adv-tool__label">{{'Rename' | translate}}</p>
        <input type="text" class="ifp-adv-tool__input" [placeholder]="'Enter here' | translate" formControlName="rename" (keyup)="renameColumn($event)">
      </div>

      <ifp-button [label]="'Change Data Type'" (ifpClick)="addDataType()" class="ifp-adv-tool__button"
        [buttonClass]="disableChange ? buttonClass.disabled+ ' '+ buttonClass.large : buttonClass.secondary+ ' '+ buttonClass.large" [iconClass]="'ifp-icon-plus'"></ifp-button>
    </div>
  </ifp-data-tool-accordian>

  @if (dataTypeList.length ) {
  <div class="ifp-adv-tool__content ifp-adv-tool__content--grey">
    <p class="ifp-adv-tool__sub-title">{{'Modified Data Types' | translate}}</p>
    @for (type of dataTypeList; let i = $index; track type) {
    <div class="ifp-adv-tool__card" >
      <div class="ifp-adv-tool__card-head">
        <p class="ifp-adv-tool__card-title">{{'Data type updated for' | translate}}</p>
        <em class="ifp-icon ifp-icon-minus-round ifp-adv-tool__remove" (click)="removeType(i)"></em>
      </div>
      @if (type.column && type.type) {
      <p class="ifp-adv-filter__filter-text">{{'Column' | translate}}: <strong>{{type.column}}</strong>;
        @if (type.type && type.initiaType !== type.type) {
          {{'Changed to' | translate}}: <strong>{{type.type | translate}}</strong>,
        }
        @if (type.rename && type.column !== type.rename) {
          {{'new column name is ' | translate}}<strong>{{type.rename}}</strong>
        }</p>
      }

    </div>
    }
  </div>
  }



  <div class="ifp-adv-tool__content ifp-adv-select__list">
    <div class="ifp-adv-tool__text-wrapper">
      <app-ifp-check-box class="ifp-adv-tool__checkbox" [label]="'Select All' | translate"
        [checkedData]="checkSelection()" (checked)="onSelectAll($event)"></app-ifp-check-box> <em
        class="ifp-adv-tool__em-text">{{'(Drag and Drop to reorder columns)' | translate}}</em>
    </div>
    <div cdkDropList (cdkDropListDropped)="drop($event)" formArrayName="columns">

      @for (item of columnArray.controls; let i = $index; track item) {
      <!-- @if (!checkDisable(item.value.name)) { -->
      <div [formGroup]="item" class="ifp-adv-tool__accordian-wrapper"
        [ngClass]="{'ifp-adv-tool__accordian-wrapper--disabled': checkDisable(item.value.name)}">
        <ifp-data-tool-accordian [title]="item.value.name" [isChecked]="item.value.checked" [isDraggable]="true"
          class="ifp-adv-tool__accordian" (selectItem)="selectedItem($event, i)" formControlName="checked"
          [isdisableCheckbox]="checkDisable(item.value.name)" cdkDrag>
          <div class="ifp-accord-box__content">
            <div class="ifp-adv-tool__edit-outer">
              <p class="ifp-adv-tool__label">{{'Data type' | translate}}: <span
                  class="ifp-adv-tool__label-value">{{item.value.dataType| translate}}</span></p>
            </div>
          </div>
        </ifp-data-tool-accordian>
      </div>
      <!-- } -->
      }
    </div>
  </div>
</form>

} @else {
  @if(errorMessageForTools) {
    {{errorMessageForTools}}
  } @else if(workFlowRunning()) {
<div class="ifp-center-loader">
  <app-ifp-spinner></app-ifp-spinner>
  <p> {{'Workflow Running' | translate}} </p>
</div>

} @else if (emptyConnection()){
<p class="ifp-adv-tool__error"> <em class="ifp-icon ifp-icon-exclamation-round">

  </em>
  {{'Source must have a connection!' | translate}}
</p>
} @else if (!sourceData()) {
<p class="ifp-adv-tool__error"> <em class="ifp-icon ifp-icon-exclamation-round">

  </em>
  {{'You must upload at least one source!' | translate}}
</p>
} @else if(!currentConection()){
<p class="ifp-adv-tool__error"> <em class="ifp-icon ifp-icon-exclamation-round">

  </em>
  {{'Node must have at least one connection' | translate}}
</p>
}
@else {
<app-ifp-spinner></app-ifp-spinner>
}

}
