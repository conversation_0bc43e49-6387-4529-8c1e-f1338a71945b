
<div class="ifp-library ifp-module-padding" [ngClass]="{'ifp-library--modal': isModal}">
  <div  [ngClass]="{'ifp-container': !isModal}">
    <div class="ifp-library__head">
      @if (!isModal) {
        <span (click)="onBack()" class="ifp-link ifp-library__head-back" [title]="'Back' | translate"><em class="ifp-icon ifp-icon-left-arrow fp-link ifp-library__head-icon"></em> <span class="ifp-library__head-back-text">{{'Back' | translate}}</span></span>

      }
      @if(isAdvance) {
        <h1 class="ifp-module-heading">{{'My Bayaan Workflows' | translate}}</h1>
      }@else {
        <h1 class="ifp-module-heading">{{'My Bayaan Library' | translate}}</h1>
      }

    </div>

    <div class="ifp-library__table">
      @if (viewTab) {
        <app-ifp-tab [tabData]="tabs" [transparent]="true" [position]="'center'"
        [selectionType]="'event'" [selectedTab]="currentSelection" class="ifp-library__tab" [showIcon]="false"  (selectedTabEvent)="selectedTabEvent($event)"></app-ifp-tab>
      }

      <div class="ifp-library__title-sec" [ngClass]="{'ifp-library__title-sec--top-mr': !viewTab}">
        <ifp-search [boxType]="true" class="ifp-library__search" (searchEvent)="searchEvent($event)" [onSearch]="search" [isKeypress]="true"></ifp-search>
      </div>

      <div class="ifp-library__row ifp-library__row--head">
        <div class="ifp-library__col ifp-library__col--1"><span class="ifp-library__col-name">{{'File Name' | translate}}</span>
          <div>
            <em [appIfpTooltip]="'Ascending' | translate"  [extraSpaceTop]="30" [zIndex]="1203" [ngClass]="{'ifp-library__sort-icon--active': acceding == false && sortValues.name == this.sortValueString}"  class="ifp-icon ifp-icon-up-arrow ifp-library__sort-icon" (click)="sort(sortValues.name, false)"></em>
            <em [appIfpTooltip]="'Descending' | translate"  [extraSpaceTop]="30"  [zIndex]="1203" [ngClass]="{'ifp-library__sort-icon--active': acceding == true && sortValues.name == this.sortValueString}"  class="ifp-icon ifp-icon-down-arrow ifp-library__sort-icon"  (click)="sort(sortValues.name, true)"></em>

          </div>
        </div>
        <div class="ifp-library__col ifp-library__col--2">{{(currentSelection === 'dataset' ? 'Created On' : 'Description') | translate}}
          @if (currentSelection === 'dataset') {
            <div>
              <em [appIfpTooltip]="'Ascending' | translate" [extraSpaceTop]="30" [zIndex]="1203" [ngClass]="{'ifp-library__sort-icon--active': acceding == false && sortValues.create == this.sortValueString}"  class="ifp-icon ifp-icon-up-arrow ifp-library__sort-icon" (click)="sort(sortValues.create, false)"></em>
              <em  [appIfpTooltip]="'Descending' | translate"  [extraSpaceTop]="30" [zIndex]="1203" [ngClass]="{'ifp-library__sort-icon--active':  acceding == true && sortValues.create == this.sortValueString}"  class="ifp-icon ifp-icon-down-arrow ifp-library__sort-icon" (click)="sort(sortValues.create, true)"></em>


            </div>
          }


        </div>
        <p class="ifp-library__col ifp-library__col--3"></p>
      </div>
      <div class="ifp-library__table-body">
        @for (item of table; track item; let index = $index;) {
          <div class="ifp-library__row">
            <div class="ifp-library__col ifp-library__col--1">
              @if(item?.metadata?.type) {
                <div  class="ifp-library__col-img-wrapper">
                  <img src="../../../../../assets/images/spread-sheet-icon.svg" [alt]="item.metadata?.type" class="ifp-library__col-img">
                  <p  class="ifp-library__file-type">{{item?.metadata?.type}}</p>
                </div>
              }
              <div class="ifp-library__item">
                @if (editInput && editIndex === index) {
                 <app-ifp-input-edit [small]="true" [light]="true" [length]="60" (cancelEdit)="cancelEdit()" (validValueChange)="validValueChange($event)" [data]="item.name"></app-ifp-input-edit>
                } @else {
                  <p class="ifp-library__col-name" [appIfpTooltip]="item.name" [zIndex]="2000" [extraSpaceTop]="20">{{item.name}}</p>
                }
                <!-- <p class="ifp-library__name">{{'Lorem ipsum dolor sit amet consectetur. Accumsan turpis.' | translate}}</p> -->
              </div>
            </div>
            <div class="ifp-library__col ifp-library__col--2">
              <div class="ifp-library__item">
                <p class="ifp-library__date">
                  @if (currentSelection === 'dataset') {
                    <em class="ifp-icon ifp-icon-clock"></em>
                  }
                  {{currentSelection === 'dataset' ? (item.created_at | date:'dd/MM/yyyy') : item.description}}</p>
              </div>

            </div>
            <div class="ifp-library__col ifp-library__col--3">
              <div class="ifp-library__btn-sec">
                <ifp-button [label]="currentSelection === 'dataset' ? 'Use this file': 'Go To Workflow'" class="ifp-library__button"
                [buttonClass]="isLibrary && currentSelection !== 'dataset' ? buttonClass.disabled : buttonClass.secondary" [iconClass]="'ifp-icon-rightarrow'" (ifpClick)="openFile(item.object_id, item)"
                ></ifp-button>
                <ifp-kebab-menu class="ifp-library__options" (optionSelected)="onSelectOptions($event, item, index)" [options]="currentSelection === 'dataset' ?kebabOptions:kebabOptionsWorkflow" [position]="'left'"></ifp-kebab-menu>
              </div>
            </div>

          </div>


        } @empty {
          @if(loader) {
            <div class="ifp-center-loader">
              <app-ifp-spinner></app-ifp-spinner>
            </div>

          } @else {
            <app-ifp-no-data></app-ifp-no-data>
          }
        }
      </div>
  @if(table.length !==0 ) {
    <app-pagination class="ifp-library__pagination" [offset]="offset" [limit]="limit" [customPagination]="true"
     [size]="size" (pageChange)="onPageChange($event)"
     (limitChange)="limitChanged($event)"></app-pagination>
   }
    </div>
  </div>
</div>

<app-ifp-modal [zIndex]="1205" [overlayIndex]="1204" #modal>
  <app-ifp-remove-card [text]="'Do you want to remove?'" (firstButtonEvent)="closeModalEvent($event)"
    (secondButtonEvent)="closeModalEvent($event)">
  </app-ifp-remove-card>
</app-ifp-modal>
