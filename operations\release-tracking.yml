parameters:
 jiraBaseUrl: ''
 jiraUsername: ''
 jiraPassword: ''
 projectKey: ''
 issueType: ''
 summaryPrefix: ''
 description: ''
 labels: ''
steps:
 - script: |
     # Convert labels string to a JSON array
     IFS=',' read -r -a LabelsArray <<< "${{ parameters.labels }}"
     # Base64 encode Jira credentials
     base64AuthInfo=$(echo -n "${{ parameters.jiraUsername }}:${{ parameters.jiraPassword }}" | base64)
     # Prepare JSON data for creating the Jira issue
     issueData=$(jq -n \
                 --arg pk "${{ parameters.projectKey }}" \
                 --arg summary "${{ parameters.summaryPrefix }}" \
                 --arg desc "${{ parameters.description }}" \
                 --arg issueType "${{ parameters.issueType }}" \
                 --argjson labels "$(printf '%s\n' "${LabelsArray[@]}" | jq -R . | jq -s .)" \
                 '{
                   fields: {
                     project: { key: $pk },
                     summary: $summary,
                     description: $desc,
                     issuetype: { name: $issueType },
                     labels: $labels
                   }
                 }')
     echo "$issueData"
        # Create Jira issue
        createIssueResponse=$(curl -s -X POST "${{ parameters.jiraBaseUrl }}/rest/api/2/issue/" \
                            -H "Authorization: Basic $base64AuthInfo" \
                            -H "Content-Type: application/json" \
                            -d "$issueData")
        issueKey=$(echo "$createIssueResponse" | jq -r .key)
        echo "Created Jira Issue Key: $issueKey"
   displayName: 'Create Jira Ticket and Attach File'