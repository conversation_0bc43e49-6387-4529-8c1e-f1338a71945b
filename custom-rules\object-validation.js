module.exports = {
 create:(context) => {
  return {
    BinaryExpression(node) {
     if (node.right && node.right.properties){
      if (node.right.type === 'ObjectExpression' && node.right.properties.length === 0) {
      context.report({
        node: node,
        hasSuggestions: true,
        message: 'This condition will always return false since JavaScript compares objects by reference, not value',
      });
      }}
    }
  };
}
}
