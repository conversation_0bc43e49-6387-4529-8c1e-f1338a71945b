export interface ColumnList {
  id: number;
  name: string;
  dataType: string;
  isSelected: boolean;
  filter_options?:FilterOpts[];
  type?:string;
}

export interface FilterOpts {
  case_sensitive_flag: boolean;
  label: string;
  operator: string;
}

export interface Formula {
  category: string;
  functions: FormulaFunction[];
}

export interface FormulaFunction {
  name: string;
  syntax: string;
  parameter_count: number;
  example: string;
  description: string;
  event?: string;
  keyword: string;
}
