<div class="ifp-scenario">
  <div class="ifp-container">
    <div class="ifp-scenario__heading-outer">
      <ifp-back-button ></ifp-back-button>
      <h1 class="ifp-scenario__heading">
        {{"My Bayaan Scenarios" | translate}}
      </h1>
    </div>
    <div class="ifp-scenario__sub-heading">
      {{'Recent'| translate}} <ifp-search [isKeypress]="true" (clearSearch)="clearSearch()" (searchEvent)="searchEvent($event)" [boxType]="true"></ifp-search>
    </div>
    <div class="ifp-scenario__card-wrapper">
      @for (item of scenarioList; track item.object_id) {
        <div  class="ifp-scenario__card">
          <ifp-auto-ml-card [publish]="(item.updated_at | date:' M/d/yy') ?? ''" [name]="item.name" [id]="item.object_id" [subTitle]="item.description ?? ''" (delete)="deleteEvent()"></ifp-auto-ml-card>
        </div>
      } @empty {
        @if (this.loader()) {
          <div  class="ifp-scenario__no-data">
            <app-ifp-spinner></app-ifp-spinner>
          </div>
        }@else {
          <app-ifp-no-data class="ifp-scenario__no-data"></app-ifp-no-data>
        }

      }



    </div>
    @if (scenarioList.length !== 0) {
      <div  class="ifp-scenario__pagination">
        <app-pagination [offset]="offset" [limit]="limit" [size]="size" [limitOptions]="limitOptions"
        (pageChange)="onPageChange($event)" (limitChange)="limitChanged($event)"></app-pagination>
      </div>

    }
  </div>

</div>
