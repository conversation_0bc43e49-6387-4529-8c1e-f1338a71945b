<div class="ifp-chart-toolbar__action-box">
  <h4 class="ifp-chart-toolbar__right-title">{{'Download' | translate}}</h4>
  <div class="ifp-chart-toolbar__download-wrapper" [ngClass]="{'disabled': termsAndConditions && !tncState}">
    <div class="ifp-chart-toolbar__download-item" (click)="download('pdf')">
      <img src="../../../../assets/images/pdf-icon.svg" class="ifp-chart-toolbar__download-icon" alt="PDF">
    </div>
    <div class="ifp-chart-toolbar__download-item" (click)="download('XL')">
      <img src="../../../../assets/images/xls-icon.svg" class="ifp-chart-toolbar__download-icon" alt="Excel">
    </div>
    <div class="ifp-chart-toolbar__download-item" (click)="download('png')">
      <img src="../../../../assets/images/img-icon.svg" class="ifp-chart-toolbar__download-icon" alt="PNG">
    </div>
  </div>
  @if (termsAndConditions) {
    <div class="ifp-chart-toolbar__terms">
      <app-ifp-check-box class="ifp-chart-toolbar__tnc-checkbox" [disabled]="tncState" [hideLabel]="true" [checkedData]="tncState" (checked)="termsResponse($event)"></app-ifp-check-box>
      <p class="ifp-chart-toolbar__tnc-text">{{'I accept the ' | translate}} <span class="ifp-link" (click)="showTnC()">{{'terms and conditions ' | translate}}</span> {{'for downloading the documents' |
        translate}}</p>
    </div>
  }
</div>
<!-- @if (showValuePrint()) { -->
  <div class="ifp-chart-toolbar__download-display ifp-chart-toolbar__download ifp-light-theme"  #elementChart  >
  <app-ifp-pdf-template >
    <div class="ifp-node__left-actions"  >
      <div class="ifp-node__group-one">
        <h2 class="ifp-chart-toolbar__title">{{name}}</h2>
      </div>


      <div class="ifp-node__chart">
        <ng-content #name></ng-content>
      </div>
    </div>




  </app-ifp-pdf-template>
  </div>
  <!-- } -->

<app-ifp-modal #tncModal [modalClass]="'ifp-modal__template-certificate'">
  <app-ifp-tnc-modal (termsResponse)="termsResponse($event)" [isAccepted]="tncState"></app-ifp-tnc-modal>
</app-ifp-modal>
