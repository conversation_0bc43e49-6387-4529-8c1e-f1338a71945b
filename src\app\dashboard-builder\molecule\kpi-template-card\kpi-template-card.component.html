<div class="ifp-kpi-temp">

  <ng-container [formGroup]="kpiCardForm">
    @if(kpiCardForm) {
      <div class="ifp-kpi-temp__head-sec">
        @if(isIcon()) {
          <p class="ifp-kpi-temp__label">{{kpiCardForm.value.label}}</p>
        } @else {
          <input type="text" name="" class="ifp-input ifp-kpi-temp__label" formControlName="label">
        }
        @if (!isIcon()) {
          <ifp-kebab-menu class="ifp-kpi-temp__options" (optionSelected)="onSelectOptions($event)" [options]="kebabOptions" [position]="isRight() ? 'right' : 'left'"></ifp-kebab-menu>
        }
      </div>
      @if(kpiCardForm.controls['valueSecondary']) {
        <p class="ifp-kpi-temp__label-sub ifp-kpi-temp__label--secondary">{{kpiCardForm.value.valueSecondary}}</p>
      }
      <div class="ifp-kpi-temp__value-wrapper">
        <p class="ifp-kpi-temp__value">{{kpiCardForm.value.value}}
          @if (!isIcon()){
          {{unit()}}
        }</p>
        @if (kpiCardForm.controls['compareValue']) {
          <div class="ifp-kpi-temp__compare-value" [ngClass]="{
            'ifp-kpi-temp__compare-value--invert': kpiCardForm.value.compareValue.isInverted,
            'ifp-kpi-temp__compare-value--red': (kpiCardForm.value.compareValue.value < 0 && !kpiCardForm.value.compareValue.isInverted) || (kpiCardForm.value.compareValue.value > 0 && kpiCardForm.value.compareValue.isInverted),
            'ifp-kpi-temp__compare-value--green': (kpiCardForm.value.compareValue.value > 0 && !kpiCardForm.value.compareValue.isInverted) || (kpiCardForm.value.compareValue.value < 0 && kpiCardForm.value.compareValue.isInverted)
          }"><em class="ifp-icon ifp-icon-up-arrow ifp-kpi-temp__compare-arrow ifp-kpi-temp__compare-arrow--up"></em>
          <em class="ifp-icon ifp-icon-down-arrow ifp-kpi-temp__compare-arrow ifp-kpi-temp__compare-arrow--down"></em><p class="ifp-kpi-temp__compare-text">{{kpiCardForm.value.compareValue.value}}%</p></div>
        }
      </div>
      @if(kpiCardForm.controls['valueTertiary']) {
        <p class="ifp-kpi-temp__label-sub ifp-kpi-temp__label--tertiary">{{kpiCardForm.value.valueTertiary}}</p>
      }
    }
  </ng-container>
</div>
