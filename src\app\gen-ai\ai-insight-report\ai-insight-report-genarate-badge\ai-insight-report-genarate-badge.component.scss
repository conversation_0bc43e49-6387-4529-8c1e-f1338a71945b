@use "../../../../assets/ifp-styles/abstracts/index" as *;
:host{
  display: block;
}
.ifp-ai-insight-report-genarate{
  &__badge {
    background-image: radial-gradient(ellipse farthest-side  at top left, rgba(18, 109, 239, 0.15) 0% ,rgba(62, 191, 242, 0.15) 31%, rgba(138, 125, 250, .15) 60%,rgba(1, 84, 233, .15) 100%);

    padding: $spacer-3 $spacer-4;
    border-radius: 5px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    &--card {
      border-radius: 0 0 20px 20px;
      margin: $spacer-0 (-$spacer-4) (-$spacer-4);
    }
}
&__animation {
  background-size: 200% 200%;
  background-position: 0% 0%, 100% 100%;
  animation: gradientShift 600ms ease-in-out infinite;
}
&--disable {
  pointer-events: none;
  opacity: .8;
}
&__ai-icon-wrapper {
  display: flex;
  margin: $spacer-2 $spacer-0;
}
&__regenrate {
  margin-top: $spacer-2;
  color: $ifp-color-blue-menu;
  .ifp-icon-refresh {
    margin-inline-end: $spacer-1;
  }
}
&__ai-icon {
  cursor: pointer;
  margin-left: auto;
  background-image: radial-gradient(ellipse at top, #126DEF, #3EBEF2, #8A7DFA 50%, #0154E9 84%);
  background-size: cover;
  color: #8A7DFA;
  -webkit-text-fill-color: transparent;
  text-fill-color: transparent;
  -webkit-background-clip: text;
  background-clip: text;
  display: inline-block;
  border: 1px solid transparent;
  border-radius: 10px;
  object-fit: cover;
  font-size: 23px;
  background-size: cover;
  background-repeat: no-repeat;
  &--center {
    bottom: 50%;
    transform: translateY(50%);
  }
}
&__input {
  width: calc(100% - (180px + $spacer-4));
}
&__btn {
  margin-inline-start: $spacer-4;
  width: 180px;
}
}
@keyframes gradientShift {
  0% {
    background-position: 0% 0%, 100% 100%;
    background-size: 200% 200%;
  }
  50% {
    background-position: 50% 50%, 50% 50%;
    background-size: 250% 250%;
  }
  100% {
    background-position: 0% 0%, 100% 100%;
    background-size: 200% 200%;
  }
}
