@if (!workFlowRunning() && !emptyConnection() && currentConection() && sourceData() && columnList && columnList.length
!==0 && !errorMessageForTools) {

<ifp-data-tool-accordian [title]="'New Filter'" [enableCheckBox]="false" [isAccordianExpanded]="true" class="ifp-adv-tool__accordian ifp-adv-tool__accordian--tool-box">
  <div class="ifp-accord-box__content">
    <form [formGroup]="filterForm">
      <div class="ifp-adv-tool__edit-outer">
        <p class="ifp-adv-tool__label">{{'Select Column' | translate}}</p>
        @if (columnList && columnList.length) {
        <app-ifp-dropdown  class="ifp-adv-tool__dropdown" [key]="'name'" [dropDownItems]="columnList"
          (dropDownItemClicked)="selectColumn($event)" formControlName="column" [disableTranslation]="true" [disableSingleValue]="true"></app-ifp-dropdown>
        @if ((f['column'].touched || submitted()) && f['column'].errors?.['required']) {
        <p class="text-danger">
          {{'*Please choose a specific column.' | translate}}
        </p>
        }
        }

      </div>

      <div class="ifp-adv-tool__edit-outer"
        [ngClass]="{'ifp-adv-tool__dropdown--disable': compareOpts && compareOpts.length <= 0}">
        <p class="ifp-adv-tool__label">{{'Select Comparator' | translate}}</p>
        <app-ifp-dropdown [singleDefaultSelect]="false" class="ifp-adv-tool__dropdown" [key]="'label'" [dropDownItems]="compareOpts"
          (dropDownItemClicked)="selectComparator($event)" formControlName="comparator" [disableSingleValue]="true">
        </app-ifp-dropdown>

        @if ((f['comparator'].touched || submitted()) && f['comparator'].errors?.['required']) {
        <p class="text-danger">
          {{'*Please choose a specific comparator.' | translate}}
        </p>
        }
      </div>

      <div class="ifp-adv-tool__edit-outer">
        <p class="ifp-adv-tool__label">{{'Value' | translate}}</p>
        <input [type]="inputType()" class="ifp-adv-tool__input"  [placeholder]="'Enter here' | translate"
          formControlName="value">
        @if ((f['value'].touched || submitted()) && f['value'].errors?.['required']) {
        <p class="text-danger">
          {{'*'+('Please provide a specific value.' | translate)}}
        </p>
        }
      </div>

      @if (selectedComparator && selectedComparator.case_sensitive_flag) {
      <app-ifp-check-box class="ifp-adv-tool__checkbox" [label]="'Ignore Case' | translate" [checkedData]="ignoreCase"
        formControlName="case" [type]="'checkbox'" [changeCheckedDataValue]="true"></app-ifp-check-box>
      }

      @if (isEditFilter) {
        <div class="ifp-adv-tool__update">
          <div class="ifp-adv-tool__action-inner">
            <ifp-button [label]="'cancel'" (ifpClick)="resetForm()" class="ifp-adv-tool__button ifp-adv-tool__action-btn" [buttonClass]="buttonClass.secondary"></ifp-button>
            <ifp-button [label]="'Update'" (ifpClick)="addFilter()" class="ifp-adv-tool__button ifp-adv-tool__action-btn" [buttonClass]="buttonClass.primary"></ifp-button>
          </div>
        </div>
      } @else {
        <ifp-button [label]="'Add Filter'" (ifpClick)="addFilter()" class="ifp-adv-tool__button"
        [buttonClass]="buttonClass.secondary+ ' '+ buttonClass.large" [iconClass]="'ifp-icon-plus'"></ifp-button>
      }
    </form>
  </div>
</ifp-data-tool-accordian>


@if (filterList.length) {
<div class="ifp-adv-tool__content ifp-adv-tool__content--grey">
  <p class="ifp-adv-tool__sub-title ifp-adv-tool__side-title">{{'Added Filter' | translate}}</p>
  <p class="ifp-adv-tool__desc">{{'Filters will be combined using AND operator' | translate}}</p>
  @for (filter of filterList; track $index) {
  <!-- <div class="ifp-adv-filter__card" (click)="updateFormData(filter)"> -->
  <div class="ifp-adv-filter__card" (click)="updateFiter(filter, $index)">
    <div class="ifp-adv-filter__card-head">
      <p class="ifp-adv-filter__card-title">{{'Filter' | translate}} {{$index + 1}}</p>
      <em class="ifp-icon ifp-icon-minus-round ifp-adv-filter__remove" (click)="removeFilter($index)"></em>
    </div>
    <p class="ifp-adv-filter__filter-text">{{'The entry in column' | translate}} <span
        class="ifp-adv-filter__filter-bold">{{filter.column.name}}</span><span class="ifp-adv-filter__filter-bold">
        {{filter.comparator.label | translate}}</span> {{'the value' | translate}}<span class="ifp-adv-filter__filter-bold">
        {{filter.value}}</span></p>
  </div>
  @if ($index !== filterList.length-1) {
    <p class="ifp-adv-tool__sub-title">{{'AND' | translate}}</p>
  }
  }
</div>
}

} @else {
  @if(errorMessageForTools) {
    {{errorMessageForTools}}
  } @else if(workFlowRunning()) {
  <div class="ifp-center-loader">
    <app-ifp-spinner></app-ifp-spinner>
    <p> {{'Workflow Running' | translate}} </p>
  </div>

  } @else if (emptyConnection()){
  <p class="ifp-adv-tool__error"> <em class="ifp-icon ifp-icon-exclamation-round">

    </em>
    {{'Source must have a connection!' | translate}}
  </p>
  } @else if (!sourceData()) {
  <p class="ifp-adv-tool__error"> <em class="ifp-icon ifp-icon-exclamation-round">

    </em>
    {{'You must upload at least one source!' | translate}}
  </p>
  } @else if(!currentConection()){
  <p class="ifp-adv-tool__error"> <em class="ifp-icon ifp-icon-exclamation-round">

    </em>
    {{'Node must have atleast one connection' | translate}}
  </p>
  }
  @else {
  <app-ifp-spinner></app-ifp-spinner>
  }

  }
