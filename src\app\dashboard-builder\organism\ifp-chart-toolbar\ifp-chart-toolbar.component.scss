@use "../../../../assets/ifp-styles/abstracts/index" as *;

:host {
  display: block;
}

.ifp-db-toolbar {
  height: calc(100vh - $ifp-header-height-inner);
  width: 100%;
  // max-width: 480px;
  @include ifp-scroll-y(transparent, $ifp-color-grey-7, 8px, 8px);
  background-color: $ifp-color-white;
  position: relative;

  &__tab {
    display: flex;
    position: sticky;
    top: 0;
    left: 0;
    z-index: 1;
    border-top: 1px solid $ifp-color-dropdown-select;

    &::after {
      content: "";
      width: 100%;
      height: 20px;
      background-color: $ifp-color-dropdown-select;
      position: absolute;
      top: 0;
      left: 0;
    }
  }

  &__tab-title {
    font-weight: $fw-semi-bold;
  }

  &__tab-item {
    font-size: $ifp-fs-4;
    padding: $spacer-3;
    flex: 1;
    position: relative;
    text-align: center;
    cursor: pointer;
    background-color: $ifp-color-dropdown-select;
    z-index: 1;

    &::after {
      content: "";
      width: 1px;
      height: 50%;
      background-color: $ifp-color-grey-3;
      position: absolute;
      top: 50%;
      right: 0;
      transform: translateY(-50%);
    }

    .ifp-icon {
      font-size: $ifp-fs-8;
      margin-bottom: $spacer-1;
    }

    &:last-child {
      &::after {
        content: none;
      }
    }

    &--disabled {
      pointer-events: none;

      .ifp-icon,
      .ifp-db-toolbar__tab-title {
        color: $ifp-color-grey-disabled;
      }
    }

    &--active {
      background-color: $ifp-color-white;
      border-radius: 20px 20px 0 0;

      .ifp-icon {
        color: $ifp-color-secondary-blue-dark;
      }

      &::after {
        width: 66.66%;
        height: 2px;
        top: 100%;
        right: 50%;
        transform: translate(50%, -2px);
        background-color: $ifp-color-secondary-blue-dark;
      }

      &+.ifp-db-toolbar__tab-item {
        border-radius: 0 0 0 20px;
      }

      &:first-child {
        border-radius: 0 20px 0 0;
      }

      &:last-child {
        border-radius: 20px 0 0 0;

        &::after {
          content: "";
        }
      }
    }

    &--active-prev {
      border-radius: 0 0 20px 0;

      &::after {
        content: none;
      }
    }
  }

  &__chart-set {
    display: flex;
    flex-wrap: wrap;
    margin: $spacer-0 (
      -$spacer-3
    );
}

&__head {
  padding: $spacer-4 $spacer-3;
  position: relative;
  margin-left: $spacer-3;
  display: flex;
  align-items: center;
  justify-content: space-between;

  .ifp-icon-triangle {
    margin-left: $spacer-3;
    font-size: $ifp-fs-2;
  }

  .ifp-icon-dockside-left,
  .ifp-icon-dockside-right,
  .ifp-icon-dockside-pop {
    font-size: $ifp-fs-5;
    color: $ifp-color-blue-hover;
  }

}

&__right {
  display: flex;
}

&__pin {
  font-weight: $fw-semi-bold;
  font-size: $ifp-fs-5;
}

&__head-title {
  font-size: 1.6rem;
  font-weight: $fw-semi-bold;
}

&__pin {
  padding: $spacer-0 $spacer-4;
  cursor: pointer;

  .ifp-icon {
    font-size: $ifp-fs-6;
    font-weight: $fw-semi-bold;
  }

  &--active {
    color: $ifp-color-blue-hover;
  }

  &--disabled {
    color: $ifp-color-grey-disabled;
    cursor: default;
  }
}

&__ul {
  position: absolute;
  z-index: 999;
  background-color: $ifp-color-white;
  box-shadow: 2px 2px 6px rgba(0, 0, 0, 0.16);
  min-width: 100px;
  transition: 0.3s;
  top: 30px;
  right: 0;
}

&__key {
  margin-left: $spacer-2;
}

&__item {
  padding: $spacer-2 $spacer-3;

  &:hover {
    background-color: $ifp-color-violet-light;
    color: $ifp-color-blue-hover;
  }
}

&__selected-wrapper {
  display: flex;
  align-items: center;
  position: relative;
}

&__data-upload {
  display: block;
  margin-bottom: $spacer-2;
}

&__table-outer {
  // width: 100%;
  // @include ifp-scroll-x(transparent, $ifp-color-grey-7, 8px, 8px);
}

&__table-button-wrapper {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: $spacer-4 $spacer-1;
}

&__accordion {
  &--disabled {
    pointer-events: none;
    color: $ifp-color-grey-disabled;
  }
}
}

:host::ng-deep ifp-accordion {
  .ifp-db-toolbar {
    &__chart-icon {
      display: block;
      margin: $spacer-2 $spacer-3;
      width: calc(25% - (2 * $spacer-3));
    }

    &__lib-search {
      margin-bottom: $spacer-3;

      .ifp-search-box__input {
        height: auto;
      }
    }
  }

  &:first-child {
    .ifp-accordion__head {
      border-top: 0;
    }
  }
}

:host-context(.ifp-header__fixed) {
  .ifp-db-toolbar {
    height: calc(100vh - ($ifp-header-height-sticky));
  }
}

:host-context([dir="rtl"]) {
  .ifp-db-toolbar {
    &__tab {
      right: 0;
      left: auto;

      &::after {
        right: 0;
        left: auto;
      }
    }

    &__tab-item {
      &::after {
        left: 0;
        right: auto;

        &--active {
          &::after {
            left: 50%;
            right: auto;
            transform: translate(-50%, -2px);
          }

          &+.ifp-db-toolbar__tab-item {
            border-radius: 0 0 20px 0;
          }

          &:first-child {
            border-radius: 20px 0 0 0;
          }

          &:last-child {
            border-radius: 0 20px 0 0;
          }
        }

        &--active-prev {
          border-radius: 0 0 0 20px;
        }
      }
    }

    &__head {
      margin-right: $spacer-3;
      margin-left: $spacer-0;

      .ifp-icon-triangle {
        margin-right: $spacer-3;
        margin-left: $spacer-0;
      }
    }

    &__ul {
      box-shadow: -2px 2px 6px rgba(0, 0, 0, 0.16);
      left: 0;
      right: auto;
    }

    &__key {
      margin-right: $spacer-2;
      margin-left: $spacer-0;
    }
  }
}
