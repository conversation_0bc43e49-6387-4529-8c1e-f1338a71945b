@use "../../../../assets/ifp-styles/abstracts/index" as *;

.ifp-prep-upload {
  width: 100%;
  max-width: 1050px;
  text-align: center;
  margin-bottom: $spacer-4;
  &__heading,
  &__content-title {
    font-weight: $fw-bold;
  }
  &__heading {
    font-size: $ifp-fs-9;
    margin-bottom: $spacer-5;
  }
  &__content {
    background-color: $ifp-color-section-white;
    border-radius: 20px;
  }
  &__content-header,
  &__content-body {

    padding: $spacer-5;
  }

  &__content-header {
    border-bottom: 1px solid $ifp-color-grey-7;
  }
  &__content-title {
    font-size: $ifp-fs-7;
    margin-bottom: $spacer-2;
  }
  &__content-desc {
    font-size: $ifp-fs-5;
    color: $ifp-color-grey-9;
  }
  &__library-text {
    font-size: $ifp-fs-4;
    padding: $spacer-3;
    border: 1px solid $ifp-color-grey-7;
    border-radius: 5px;
    margin-top:$spacer-5;
    display: flex;
    align-items: center;
    cursor: pointer;
    width: 100%;
    &:last-child {
      margin-bottom: $spacer-5;
    }
  }
  &__library-icon {
    display: inline-block;
    border-radius: 10px;
    background-color: $ifp-color-cyan;
    padding: $spacer-2 ($spacer-2 + 2px);
    margin: $spacer-0 $spacer-3;
    &--bg-yellow {
      background-color: $ifp-color-yellow-menu;
    }
    .ifp-icon {
      font-size: $ifp-fs-6;
      color: $ifp-color-white-global;
    }
  }
  &__btn-wrapper {
    display: flex;
    padding: $spacer-0 $spacer-5 $spacer-5 $spacer-5;
    justify-content: center;
}


&__library-vertical-divider {
	display: table;
	text-align: center;

	height: 100%; // CHANGE ME!
	width: 100%;


}
&__upload-bar{
  margin-top: 12px;
  display: block;
}

&__dropdown-sec {
  margin-top: 12px;
  text-align: start;
  display: flex;
  justify-content: space-between;
  &--button {
    justify-content: flex-end;
  }
}
&__label {
  color: $ifp-color-tertiary-text;
  margin-bottom: $spacer-2;
}

&--rotate {
  margin: auto;
  max-width: 650px;
  .ifp-prep-upload {

   &__content-header {
    padding: $spacer-3 $spacer-5;
   }
   &__content-body {
    padding: $spacer-0 $spacer-5 $spacer-5;
   }
   &__library-divider-wrapper {
    margin-bottom: $spacer-5;
    padding: $spacer-0;
   }
   &__library-line-wrapper {
    background-color: $ifp-color-section-white;
    position: relative;
    z-index: 4;
    padding: $spacer-0 $spacer-3;
    color:  $ifp-color-grey-14;
   }
   &__library-text{
    justify-content: center;

   }
   &__library-content-wrapper {
    width:100%;
   }
   &__btn-wrapper {
    padding: $spacer-5 $spacer-5 $spacer-3 $spacer-5;
}
   &__library-line {
    position: relative;
    display: table-cell;
    vertical-align: middle;

    &:before {
      z-index: 0;
      position: absolute;
      content: "";
      width: 100%;
      top: 50%;
      left: 0;
      border-left: none;
      border-top: 1px solid $ifp-color-grey-7 ;
    }
    &:after {
      content: none;
    }
  }
}
}
&__desc {
  text-align: left;
  padding: $spacer-5 $spacer-6 $spacer-0;
  margin-inline-start: $spacer-2;
}
&__inner{
  list-style: disc;
}
&__library-upload {
  border: 1px solid $ifp-color-grey-7;
  padding: 4px;
  border-radius: 5px;;
}
}
