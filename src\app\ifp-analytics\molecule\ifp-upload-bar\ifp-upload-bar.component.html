


<div class="ifp-upload-box"  [ngClass]="{'ifp-upload-box__error-light': error}">
  <div class="ifp-upload-box__sec-wrapper">
    <svg width="34" height="34" viewBox="0 0 34 34" fill="none" xmlns="http://www.w3.org/2000/svg">

      <rect width="34" height="34" rx="5" fill="#659B5C"/>
      <path d="M13.3954 8H7V26H13.3954V8Z" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
      <path d="M27 8H13.4097V13.9957H27V8Z" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
      <path d="M19.8574 8V25.9696" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
      <path d="M27 14.0088H13.4097V20.0044H27V14.0088Z" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
      <path d="M27 20.0039H13.4097V25.9996H27V20.0039Z" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
      </svg>
      <div  class="ifp-upload-box__text-wrapper">
        <P class="ifp-upload-box__text" [maxWithNone]="true" [appIfpTooltip]="fileName()" [extraSpaceTop]="20">{{fileName()}}</P>
        <span  class="ifp-upload-box__size">{{size | bytesConverter}}</span>
      </div>
      @if(!loader) {
        <em class="ifp-icon ifp-icon-cross ifp-upload-box__cross" (click)="cross()"></em>
      }
  </div>
  <div class="ifp-upload-box__bar" [ngClass]="{'ifp-upload-box__error': error}" [style.width]="width() +'%'"></div>
</div>
<app-ifp-modal #modal>
  <app-ifp-remove-card [text]="'Do you want to remove?'" (firstButtonEvent)="closeModal($event)"
    (secondButtonEvent)="closeModal($event)">
  </app-ifp-remove-card>
</app-ifp-modal>
