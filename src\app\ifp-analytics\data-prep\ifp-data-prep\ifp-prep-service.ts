import { Injectable, WritableSignal, signal } from '@angular/core';
import { BehaviorSubject } from 'rxjs';
import { FileResponePrep, PrepNode } from './interface/ifp-data-prep.interface';
import { HttpService } from 'src/app/scad-insights/core/services/http/http.service';
import { WorkFlowStatusStream } from './ifp-prep-library/intreface/ifp-prep-library.interface';

@Injectable()
export class IfpPrepService {

  public isShowPreview: boolean = false;
  public disableSidebar =  signal(true);
  public showPreview =  signal(false);
  public uploadedFileResponse:WritableSignal<FileResponePrep| null> =  signal(null);
  public fileName:WritableSignal<string> =  signal('');
  public currentNodeData = new BehaviorSubject<PrepNode| null>(null);
  public currentNodeDataNow:WritableSignal<PrepNode> = signal({
    name: '',
    description: '',
    nodes: [],
    connections: []
  });

  public previewWidth = signal(310);
  public currentNodeId :WritableSignal<string| null> = signal(null);
  public previousNodeId :WritableSignal<string| null> = signal(null);
  public currentNodeIdBeforeWorkFlow :WritableSignal<string| null> = signal(null);
  public workflowStarted :WritableSignal<boolean> = signal(false);
  public previewTableData: {heading: string[]; data: any} = {heading: [], data: []};
  public workFlowID: WritableSignal<string| null>= signal(null);
  public processActive = signal(false);
  public processStatus = new BehaviorSubject<null | WorkFlowStatusStream>(null);
  public currentdataSetObjectId = signal<Record<number, string>>({});
  public isSummary = signal(false);
  public processDisable = signal(false);
  public showProcessBtn= signal(true);
  public loaderProcess= signal(false);
  public processEvent =  new BehaviorSubject('');
  public logs: WritableSignal<{
    key: string;
    title: string;
    value: string;
  }[][]> = signal([]);
  // public sheet = false;
  // public sheetDetails!: { file: UploadedFile, path: string, sheet: string };

  constructor( private _http: HttpService) { }

  getParams(params: any) {
    return params ? Object.entries(params).map(([key, value]) => `${key}=${value}`).join('&') : '';
  }

  getMethodRequest(url: string, params?: any) {
    const query = this.getParams(params);
    return this._http.get(`${url}${query === '' ? query : `?${query}`}`, true);
  }

  getMethodStreamRequest(url: string, params?: any) {
    const query = this.getParams(params);
    return this._http.getSream(`${url}${query === '' ? query : `?${query}`}`, true);
  }

  getDeleteRequest(url: string, params?: any) {
    const query = this.getParams(params);
    return this._http.delete(`${url}${query === '' ? query : `?${query}`}`, true);
  }

  postMethodRequest(url: string, data?: any) {
    return this._http.post(`${url}`, data, true);
  }

  putMethodRequest(url: string, data?: any) {
    return this._http.put(`${url}`, data, true);
  }


  getDownloadRequest(url: string, params?: any) {
    const query = this.getParams(params);
    return  this._http.fileDownloadWithHeader(`${url}${query === '' ? query : `?${query}`}`);
  }

  getDataSetId(url: string, params?: any){
    const query = this.getParams(params);
    return this._http.get(`${url}${query === '' ? query : `?${query}`}`, true);
  }

  getDataSet(url: string) {
    return this._http.get(`${url}`, true);
  }

  clearAllValues() {
    this.currentdataSetObjectId.set({});
    this.disableSidebar.set(true);
    this.showPreview.set(false);
    this.uploadedFileResponse.set(null);
    this.fileName.set('');
    this.currentNodeData.next(null);
    this.isShowPreview = false;
    this.currentNodeDataNow.set({
      name: '',
      description: '',
      nodes: [],
      connections: []
    });
    this.currentNodeId.set(null);
    this.workFlowID.set(null);
    this.processActive.set(false);
    this.processStatus.next(null);
  }
}
