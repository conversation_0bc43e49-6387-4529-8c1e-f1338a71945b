<div class="ifp-prep">
  <!-- toolbar start -->
   <div  #prepToolBar class="ifp-prep__toolbar-wrapper">
    <ifp-prep-toolbar  [isDisabled]="_prepService.disableSidebar()" class="ifp-prep__toolbar"  [tools]="tools" (toolSelected)="getSelectedTool($event)" [changeToolBar]="basic"
    [selectedTool]="selectedTool()"></ifp-prep-toolbar>
   </div>

  <!-- toolbar end -->

  <div class="ifp-prep__content" #content>
    <!-- header start -->
    <ifp-prep-header class="ifp-prep__header" [title]="selectedTool().name"
      [description]="selectedTool().description ? selectedTool().description : ''" (changeMode)="goToExpertMode()" [isExpertMode]="isExpertMode"
      [tabItems]="tabItems" [selectedTab]="selectedTab"></ifp-prep-header>
    <!-- header end -->

    <!-- body start -->
    <div class="ifp-prep__body" #resizableDiv>
      <div class="ifp-prep__body-content" #prepcontent>
        <router-outlet></router-outlet>
      </div>
      <div class="ifp-prep__body-sidebar" [ngClass]="{'ifp-prep__body-sidebar--normal-width': _prepService.showPreview()}" #processFlow >
        <ifp-toggle-sidebar [submenu]="true" class="ifp-prep__toggle-sidebar" [ngClass]="{'ifp-prep__toggle-sidebar--submenu': !sideBar.isToolbarExpanded}" #sideBar>
          <div class="ifp-toggle-sidebar__content">
            @if (sideBar.isToolbarExpanded) {
            <h3 class="ifp-prep__heading">{{'Steps' | translate}}</h3>

              <ul class="ifp-prep__process">
                @for (item of nodeData.data.nodes; track item; let last = $last; let index = $index;) {
                  <li class="ifp-prep__process-item"  [ngClass]="{'ifp-prep__process-item--process-item-blue': item?.status == 'pending'}">
                    <p class="ifp-prep__process-name">{{item.name| translate}}</p>
                    <p class="ifp-prep__process-time"><em class="ifp-icon ifp-icon-clock"></em>{{item.time | date: 'h:mm a,d MMM'}}</p>
                  </li>
                }
                @empty {
                  <p class="ifp-prep__process-no-data">{{'No steps are available' | translate}}</p>
                }
              </ul>
            } @else {
              <ul class="ifp-prep__process">
                @for (item of nodeData.data.nodes; track item; let last = $last; let index = $index;) {
                  <li class="ifp-prep__process-item" [appIfpTooltip]="item.name | translate" [ngClass]="{'ifp-prep__process-item--process-item-blue': last && index !==0}">
                  </li>
                }
              </ul>
            }

          </div>
        </ifp-toggle-sidebar>
      </div>

    </div>

    @if (_prepService.showPreview()) {
      <em class="ifp-icon ifp-icon-vertical-arrows ifp-prep-data__preview-resize" (mousedown)="onMouseDown($event, resizableDiv)" (touchstart)="onTouchStart($event, resizableDiv)"></em>
      <ifp-data-preview [showProbabilty]="false" class="ifp-prep-data__preview" #advPreview></ifp-data-preview>
    }
    <!-- body end -->

    <!-- view uploaded data start -->
    <!-- @if (prepService.isShowPreview) { -->
    <!-- <div class="ifp-prep__preview">
      <div class="ifp-prep__table">
        <em class="ifp-icon ifp-icon-vertical-arrows ifp-prep__preview-resize"></em>
        <div class="ifp-prep__preview-toolbar">
          <h3 class="ifp-prep__preview-title">{{'Preview' | translate}}</h3>
          <div class="ifp-prep__preview-tools">
            <span class="ifp-prep__preview-duration">{{'Loaded in' | translate}} 387 ms</span>
            <ifp-panel-dropdown class="ifp-prep__preview-dropdown" [options]="columListDropdown"
              [key]="'value'"></ifp-panel-dropdown>
            <em class="ifp-icon ifp-icon-col-view ifp-prep__preview-tool"
              [ngClass]="{'ifp-prep__preview-tool--active': tableView === 'normal'}" (click)="setTableView('normal')"
              [appIfpTooltip]="'View Data' | translate"></em>
            <em class="ifp-icon ifp-icon-detial-view ifp-prep__preview-tool"
              [ngClass]="{'ifp-prep__preview-tool--active': tableView === 'detail'}" (click)="setTableView('detail')"
              [appIfpTooltip]="'Column details' | translate"></em>
            <em class="ifp-icon ifp-icon-download-line ifp-prep__preview-tool"></em>
          </div>
        </div>
        <div class="ifp-prep__preview-inner">
          <ifp-data-table [tableHead]="tableData.heading" [tableData]="tableData.data"
            class="ifp-prep__preview-table"></ifp-data-table>
        </div>
      </div>
      @if (selectedTool.key == 'select') {
      <ifp-probability-card class="ifp-prep__probability"></ifp-probability-card>
      }
    </div> -->
    <!-- } -->
    <!-- view uploaded data end -->
  </div>
</div>



<app-ifp-modal #saveWorkflowModal>
  <app-ifp-remove-card [text]="'Proceeding with exit will cause data loss. Do you still want to continue?'"
    [firstButton]="'Cancel'" [secondButton]="'Continue'"
    (firstButtonEvent)="closeSaveModel()" (secondButtonEvent)="goToNextPage()">
  </app-ifp-remove-card>
</app-ifp-modal>

