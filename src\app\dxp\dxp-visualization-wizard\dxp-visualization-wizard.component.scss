@use "../../../assets/ifp-styles/abstracts/index" as *;
.ifp-dxp-visualization-wizard {
  &__header-wrap {
    background-color: $ifp-color-white;
    display: flex;
    border-radius:10px ;
    justify-content: space-between;
    padding: $spacer-5;
    margin-top:$spacer-5;
  }
  &__header {
    font-size: $ifp-fs-8;
    font-weight: $fw-semi-bold;
  }
  &__header-btns {
    display: flex;
    justify-content: space-between;

  }
  &__header-btn {
    margin-inline-start: $spacer-4;
  }
  &__section {
    margin-top: $spacer-5;
    display: flex;
  }
  &__title {
    font-size: $ifp-fs-5;
    font-weight: $fw-semi-bold;
    margin-bottom: $spacer-3;
  }

  &__heading-right {
    font-size: $ifp-fs-5;
    font-weight: $fw-semi-bold;
  }
  &__section-left {
    margin-inline-end: $spacer-4;
    background-color: $ifp-color-white;
    border-radius:10px ;
    padding: $spacer-3;
    min-width: 288px
  }
  &__section-right{
    width: calc(100% - 312px);
    background-color: $ifp-color-white;
    border-radius: 10px;
    padding: $spacer-5;
  }
  &__sub-sec {
    background-color: $ifp-color-grey-18;
    border-radius: 10px;
    margin-top: $spacer-3;
    padding: $spacer-3;
  }
  &__dropdown-wrapper {
    display: flex;
    align-items: flex-end;

  }
  &__dropdown-list {
    display: flex;
    width: calc(100% );
    align-items: center;
    margin:  $spacer-0 (- $spacer-2);
   }
  &__dropdown {
    padding:  $spacer-0 $spacer-2;
    width:  33.33%;
  }
  &__btn-sec {
    display: flex;
  }
  &__filter-btn {
    display: block;
    margin-inline-start: $spacer-2;
  }

  &__filter {
    display: inline-block;
    margin-top: $spacer-2;
  }
  &__filter-list {
    margin: $spacer-3 (-$spacer-2) $spacer-0;
    display: flex;
    flex-wrap: wrap;
  }
  &__filter-item {
    margin: $spacer-2;
    width: calc(50% - (2 * $spacer-2));
  }
  &__heading-source {
    display: flex;
    align-items: center;
  }
  &__head-info {
    margin-inline-start: $spacer-2;
  }
  &__box {
    margin-top: $spacer-5;
    border: 1px solid $ifp-color-grey-13;
    border-radius: 10px;
    &--sub-sec {
      padding: $spacer-3;
    }
  }
  &__axis-lebal-wrap {
    display: flex;
    margin: $spacer-0 (- $spacer-2);
    margin-top: $spacer-3;
    align-items: center;
  }
  &__axis-lebal,   &__axis-labe-drag ,  &__axis-input {
    margin: $spacer-0 $spacer-2;
  }
  &__axis-lebal{
    color: $ifp-color-grey-14;
  }
  &__axis-labe-drag {
    width: calc(100% - 314px);
  }
  &__axis-input {
    width: 200px;
    font-size: $ifp-fs-4;
    padding: $spacer-3 - 2px;
  }

  &__no-data{
    height: calc(100% - 64px);
    align-items: center;
    justify-content: center;
    display: flex;
    color: $ifp-color-grey-14;
    font-size: $ifp-fs-4;
    text-align: center;
  }
  &__add-btn  {
    width: 100%;
  }
  &__input-label {
    margin: $spacer-2 $spacer-0;
    font-size:$ifp-fs-2;
    color: $ifp-color-grey-2;
  }
  &__dxp-user {
    margin-top: $spacer-4;
    box-shadow: 0px 4px 29px 0px rgb(159, 169, 200, 0.07);
    margin-bottom: $spacer-4;
  }
  &--diplay-none {
    display: none;
  }
}
:host::ng-deep {
.ifp-dxp-visualization-wizard__add-btn {
  width: 100%;
  .ifp-btn{
    width: 100%;
  }
}
  .ifp-dropdown {
    max-width:none  !important ;
    &__title-text {
      font-size:$ifp-fs-4 ;
      font-weight: $fw-regular;
    }
  }
}
