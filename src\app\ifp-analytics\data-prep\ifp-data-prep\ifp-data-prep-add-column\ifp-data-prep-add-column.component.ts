
import {  ChangeDetector<PERSON><PERSON>, Component,  <PERSON>ement<PERSON><PERSON>,  <PERSON><PERSON><PERSON><PERSON>,  OnInit, ViewChild, signal } from '@angular/core';
import { FormBuilder, FormControl, FormGroup, ReactiveFormsModule, ValidationErrors, Validators } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';
import { IfpBackButtonComponent } from '../../../../ifp-widgets/atoms/ifp-back-button/ifp-back-button.component';
import { IfpButtonComponent } from '../../../../scad-insights/ifp-widgets/ifp-atoms/ifp-button/ifp-button.component';
import { buttonClass, buttonIconPosition } from 'src/app/scad-insights/core/constants/button.constants';
import { NavigationEnd, Router } from '@angular/router';
import { ThemeService } from 'src/app/scad-insights/core/services/theme/theme.service';
import { NgClass } from '@angular/common';
import { IfpSearchComponent } from 'src/app/scad-insights/ifp-widgets/ifp-atoms/ifp-search/ifp-search.component';
import { Subject, debounceTime, filter } from 'rxjs';
import { CodemirrorModule, CodemirrorComponent} from '@ctrl/ngx-codemirror';

import 'codemirror/addon/hint/show-hint';
import 'codemirror/addon/hint/anyword-hint';
import 'codemirror/addon/lint/lint';



import CodeMirror, { Editor, EditorFromTextArea} from 'codemirror';
import { IfpPrepService } from '../ifp-prep-service';
import { Store } from '@ngrx/store';
import { SubSink } from 'subsink';
import { prepStatusStore, workFlowState } from '../constants/ifp-state.contants';
import { loadCreateNodeSuccessColumnCreate, loadCreateNodeSuccessColumnRemove, loadCreateNodeSuccessColumnUpdate, loadNode } from '../store/node-store/node-store.actions';
import { selectNodePrepResponse } from '../store/node-store/node-store.selectors';
import { prepsApiEndpoints } from '../constants/if-preps.constants';
import { ColumnUpstream } from '../ifp-prep-select-data/interface/prep-selection.interface';
import { Node, PrepNode } from '../interface/ifp-data-prep.interface';
import { cloneDeep } from 'lodash';
import { IfpSpinnerComponent } from '../../../../scad-insights/ifp-widgets/ifp-molecules/ifp-spinner/ifp-spinner.component';
import { IfpModalComponent } from '../../../../scad-insights/ifp-widgets/ifp-organism/ifp-modal/ifp-modal.component';
import { IfpPrepPopupComponent } from '../../../organism/ifp-prep-popup/ifp-prep-popup.component';
import { CardData } from 'src/app/shared/molecule/ifp-tool-card/ifp-tool-card.component';
import { ValidationService } from 'src/app/scad-insights/core/services/validation/validation.service';
import { ToasterService } from 'src/app/scad-insights/core/services/tooster/ToastrService.service';
import { NodeState } from '../store/node-store/node-store.state';
import { AbstractControl } from '@angular/forms';

@Component({
    selector: 'ifp-data-prep-add-column',
    templateUrl: './ifp-data-prep-add-column.component.html',
    styleUrl: './ifp-data-prep-add-column.component.scss',
    imports: [TranslateModule, IfpBackButtonComponent, IfpButtonComponent, ReactiveFormsModule, NgClass, IfpSearchComponent, CodemirrorModule, IfpSpinnerComponent, IfpModalComponent, IfpPrepPopupComponent]
})
export class IfpDataPrepAddColumnComponent implements OnInit, OnDestroy {
  @ViewChild('modalSucess') modalSucess!: IfpModalComponent;


  public columnName: string = '';
  public formulasCurrent!: Formula[];
  public formulas: FormulaFunction[] = [];
  public formulasToDisplay: FormulaFunction[] = [];
  public buttonIconPosition = buttonIconPosition;
  public buttonClass = buttonClass;
  public selectedFormula!: FormulaFunction | null;
  public selectedField!: string | null;
  public fieldList: string[] = [];
  public fieldsToDisplay = this.fieldList;
  public expressionForm!: FormGroup;
  public draggingItem: any = null;
  public draggingItemPosition = { x: 0, y: 0 };
  public showDescription: boolean = false;
  public currentPosition: number | null = null;
  public editor?:EditorFromTextArea;
  public textArea!: ElementRef;
  public editorView!: Editor;
  public fuctionList: string[] = [];
  public isFormulaClicked: boolean = false;
  public currenPostsition = { line: 0, ch: 0 };
  public  customLint = (text: string, updateLinting:any) => {
    this._prepService.processDisable.set(true);
    this.expresstionChange.next(updateLinting);
  };

  public formulaValidated = signal(false);

  public formulaLoader = signal(false);
  public fieldLoader =  signal(false);
  public workFlowRunning=  signal(false);
  public loader=  signal(false);

  public configCodemirror = {
    mode: 'cypher',
    lint: {
      getAnnotations: this.customLint,
      async: true
    },
    indentWithTabs: true,
    smartIndent: true,
    lineNumbers: false,
    autofocus: true,
    showHint: true,
    dragDrop: true,
    autoCloseBrackets: true,
    lineWrapping: true,

    theme: this._themeService.defaultTheme === 'light' ? 'xq-light' : 'xq-dark'

  };

  public nodeData!: PrepNode;
  public objectId = '';
  public processed = false;

  public subs =  new SubSink();
  public updateLinting!:unknown;
  private expresstionChange =  new Subject();
  public setWorkFlow =signal(false);

  constructor(private _router: Router, private _themeService: ThemeService, private _formBuilder: FormBuilder, public _prepService: IfpPrepService, private _store: Store, private _cdr:ChangeDetectorRef, private _validationService: ValidationService, private _toaster: ToasterService) {
    this._router.events
      .pipe(filter((rs): rs is NavigationEnd => rs instanceof NavigationEnd))
      .subscribe((event: { id: number; url: any; urlAfterRedirects: any; }) => {
        if (
          event?.id === 1 &&        event?.url === event?.urlAfterRedirects
        ) {
          // Your code here for when the page is refreshd
          this._router.navigateByUrl('analytics/data-preparation/upload-data');
        }
      });
  }

  ngOnInit() {
    this._prepService.processDisable.set(true);
    this.subs.add( this._prepService.processEvent.subscribe((data: string) => {
      if (data !== '') {
        if (!this._prepService.processDisable()) {
          this.proceed();
        }
      }

    }));
    this.subs.add(this.expresstionChange.pipe(debounceTime(800)).subscribe((updateLinting)=> {
      this.updateLinting = updateLinting;
      this.validationApi(updateLinting);
    }));

    this.initForm();
    // this.expressionForm.controls['fieldName'].valueChanges.subscribe(() => {
    //   this.validationApi(this.updateLinting);
    // });
    if (!this._prepService.uploadedFileResponse()) {
      this._router.navigate(['analytics/data-preparation/upload-data']);
    } else {
      this._prepService.processActive.set(true);
      this._prepService.showPreview.set(true);
      let init = true;
      this._store.dispatch(loadNode[loadCreateNodeSuccessColumnCreate]({config: { columns: []}}));
      this.subs.add(
        this._store.select(selectNodePrepResponse).subscribe(dataState => {

          if (dataState.currentStatus === prepStatusStore.formulaAdded ) {
            this.nodeData = dataState.data;
            const nodeLength = dataState.data.nodes.length;
            const lastValue = dataState.data.nodes[nodeLength - 1];
            this.objectId = lastValue.object_id;
            this._prepService.processStatus.next(null);

            if (init) {
              init = false;
              const progress = this._prepService.processStatus.subscribe(dataWorkFlow=> {
                if (dataWorkFlow?.workflow_status === workFlowState.completed) {
                  this.workFlowRunning.set(true);
                  this._prepService.disableSidebar.set(false);
                  this.callUpStream(lastValue, dataState);
                  progress.unsubscribe();
                }
              });
            } else if (!init && this.setWorkFlow()) {
              this.setWorkFlow.set(false);
              this.callUpStream(lastValue, dataState);

            }


          }
        })
      );
    }
    this.loadFormulaApi();
    this.subs.add(
      this._themeService.defaultTheme$ .subscribe((data)=> {
        this.configCodemirror = {
          ...this.configCodemirror,
          theme: data === 'dark' ? 'xq-dark' : 'xq-light'
        };
        this._cdr.detectChanges();
      })
    );

  }

  /**
   * used to call column api upstream for listing columns
   * @param lastValue
   */
  callUpStream(lastValue: Node, dataState: NodeState) {
    const sub = this._prepService.postMethodRequest(
      `${prepsApiEndpoints.selectNode}${lastValue.object_id}${prepsApiEndpoints.coloumUpstrem}`,
      dataState.data
    ).subscribe((value: Record<string, ColumnUpstream>) => {
      const columns = Object.values(value)[0]?.columns_1 ?? [];
      this.fieldList = columns.map(col => col.name);
      this.fieldsToDisplay = [...this.fieldList];
      this.defineCodeMirror();

      const fieldNameControl = this.expressionForm.controls['fieldName'];
      fieldNameControl.clearValidators();
      fieldNameControl.setValidators([
        Validators.required,
        this._validationService.noWhitespaceValidator,
        this.checkColumnNameValidator(this.fieldList)
      ]);
      fieldNameControl.updateValueAndValidity();

      this.fieldLoader.set(true);
      sub.unsubscribe();
    });
  }

  loadFormulaApi () {
    this.formulaLoader.set(false);
    this.subs.add(this._prepService.getMethodRequest(prepsApiEndpoints.selectNode +prepsApiEndpoints.formulaList + prepsApiEndpoints.baisc).subscribe((data:Formula[]) => {
      this.formulasCurrent = data;
      this.loadFormulaData();
    }));
  }

  dataCheck() {
    if (this.expressionForm.valid && this.formulaValidated()) {
      this._prepService.processDisable.set(false);
    } else {
      this._prepService.processDisable.set(true);
    }
  }

  updateCursorPosition(event: any) {
    const coords = {left: event.clientX, top: event.clientY};
    const position = this.editor?.coordsChar(coords, 'window');
    return {line: position?.line ?? 0, ch: position?.ch ?? 0};
  }



  codeMirrorLoaded(event: CodemirrorComponent) {
    this.editor = event.codeMirror;
    this.textArea = event.ref;

    this.editor?.on('inputRead', (editor: Editor, change) => {
      this.editorView = editor;
      const tokenKey = editor.getTokenAt(editor.getCursor());

      const hintFn = () => {
        if (tokenKey.string === '[') {
          const  fieldList =  this.fieldList.map((data)=> `[${data}]`);
          return {
            from: {ch: tokenKey.start, line: editor.getCursor().line},
            to: {ch: tokenKey.end, line: editor.getCursor().line},
            list: fieldList
          };
        }
        const value  = this.formulasToDisplay.filter(data => (data.name.toLowerCase()).startsWith(tokenKey.string.toLowerCase()));
        const list = value.map((data)=> `${data.keyword}()`);
        const fieldListValue = this.fieldList.filter(data => (data.toLowerCase()).startsWith(tokenKey.string.toLowerCase()));
        const  fieldList =  fieldListValue.map((data)=> `[${data}]`);
        return {
          from: {ch: tokenKey.start, line: editor.getCursor().line},
          to: {ch: tokenKey.end, line: editor.getCursor().line},
          list: [...list, ...fieldList]
        };
      };
      editor?.showHint({
        completeSingle: false,
        hint: hintFn
      });
      if (change.text[0] === '.') {
        editor?.showHint({
          hint: hintFn
        });
      }
    });
  }

  drop(event: [Editor, DragEvent]) {
    if (event[1]?.dataTransfer?.effectAllowed != 'copyMove') {
      event[1]?.preventDefault();
    }

    this.expresstionChange.next(this.expressionForm.value);
    // Function to provide auto-completion hints
  }

  loadFormulaData() {
    this.fuctionList = [];
    this.formulasCurrent.forEach(formulaData => {
      formulaData.functions.forEach( data => {
        this.formulasToDisplay.push({...data, event: `${data.keyword}()`});
        this.formulas.push({...data, event: `${data.keyword}()`});
        this.fuctionList.push(data.keyword);
      });
    });
    this.defineCodeMirror();
    this.formulaLoader.set(true);
  }

  resetFormula() {
    this.expressionForm.get('expression')?.setValue('');
  }

  applyOperations() {
    // this._router.navigate(['analytics/data-preparation/save']);
    this. proceed();
  }

  // used to set form
  initForm() {
    this.expressionForm = this._formBuilder.group({
      expression: new FormControl('', [Validators.required, this._validationService.noWhitespaceValidator]),
      fieldName: new FormControl('', [Validators.required, this._validationService.noWhitespaceValidator])
    });
  }





  // Dragging a field
  onItemDrag(item: any, type: 'field' | 'formula') {
    this.selectedField = null;
    this.selectedFormula = null;
    this.isFormulaClicked = false;
    if (type === 'field') {
      this.selectedField = item;
    } else {
      this.selectedFormula = item;
    }
  }

  // function used to prevent drag hover
  onDragOver(event: MouseEvent) {
    event.preventDefault();
    event.stopPropagation();
  }

  // function used to drop fields and functions
  onDrop(event: MouseEvent | any) {
    // this.currenPostsition=  this.updateCursorPosition(event);
    // this.addValueTolocation();
    if (this.selectedFormula && event.type === 'drop') {
      this.selectOperation(this.selectedFormula);
      this.isFormulaClicked = true;
      this.selectedField = null;
    } else if (this.selectedField && event.type === 'drop') {
      this.selectField(this.selectedField);
      this.selectedFormula = null;
      this.isFormulaClicked = false;
    }
    // this.selectedFormula = null;
    // this.selectedField = null;
  }

  addValueTolocation() {
    const lines = this.expressionForm.get('expression')?.value.split('\n');
    const line = this.currenPostsition.line;
    const ch = this.currenPostsition.ch;
    // Ensure the specified line exists
    while (lines.length < line) {
      lines.push('');
    }
    let valueToAdd = '';
    if (this.selectedFormula ) {
      valueToAdd = this.selectedFormula.event ?? '';
    } else if (this.selectedField ) {
      valueToAdd = `[${this.selectedField}]`;
    }


    // Get the specified line and ensure the character position exists
    const targetLine = lines[line];
    const newLine = targetLine.slice(0, ch ) + valueToAdd + targetLine.slice(ch);

    // Update the line with the new content
    lines[line] = newLine;
    const currentValue = lines.join('\n');
    this.expressionForm.get('expression')?.setValue(currentValue);
  }

  selectOperationClick(formula: FormulaFunction){
    if(window.matchMedia('(max-width: 1366px) and (-webkit-min-device-pixel-ratio: 2)').matches) {
      this.selectOperation(formula);
    }
  }

  // Select an operation
  selectOperation(formula: FormulaFunction) {
    const value = this.expressionForm.get('expression')?.value;
    this.expressionForm.get('expression')?.setValue(`${value}${formula?.event} `);
    // this.editor.setValue(this.expressionForm.get('expression')?.value);
    // this.selectedFormula = null;
  }

  validationApi(updateLinting: any) {
    const nodeValue = cloneDeep(this.nodeData);
    const lastNode = this.nodeData?.nodes?.length -1;
    if (lastNode) {
      nodeValue.nodes[lastNode].configuration = { columns: [{
        name: this.expressionForm.value.fieldName,
        formula: this.expressionForm.value.expression?.trim()
      }]};
    }
    const formula = {
      formula: this.expressionForm.value.expression?.trim(),
      column: this.expressionForm.value.fieldName,
      workflow: nodeValue
    };
    if ( this.expressionForm.value.expression.trim() === '') {
      return;
    }
    const checkValidation = this._prepService.postMethodRequest(`${prepsApiEndpoints.selectNode}formula/${this.objectId}/validate`, formula ).subscribe({
      next: ()=> {
        this.formulaValidated.set(true);
        if (this.expressionForm.valid && this.formulaValidated()) {
          this._prepService.processDisable.set(false);
        } else {
          this._prepService.processDisable.set(true);
        }
        const annotations: [] = [];
        updateLinting(annotations);
        checkValidation.unsubscribe();
      },
      error: (error)=> {
        this._prepService.processDisable.set(true);
        this.formulaValidated.set(false);
        checkValidation.unsubscribe();
        const annotations = [];
        if ( error.error.column_number) {
          annotations.push({
            message: error.error.error,
            severity: 'error',
            from: { line: 0, ch: error.error.column_number - 1},
            to: { line: 0, ch: error?.error?.highlighted_formula?.length ?? 1 }
          });
          updateLinting(annotations);
        } else {
          this._toaster.error(error.error.error);
        }
      }
    } );
  }

  selectFieldClick(field: string) {
    if (window.matchMedia('(max-width: 1366px) and (-webkit-min-device-pixel-ratio: 2)').matches) {
      this.selectField(field);
    }
  }

  // Select a field
  selectField(field: string) {
    const value = this.expressionForm.get('expression')?.value;
    const index = value.lastIndexOf(')');
    const index2 = value.lastIndexOf('(');
    const diff = index  - index2;
    const cursorPostion = index2 !== -1 ? (index !== -1 ? index : 0):value.length;
    const newValue = `${value.slice(0, cursorPostion)}${diff<=1 ? '': ','}[${field}]${value.slice(cursorPostion)}`;

    this.expressionForm.get('expression')?.setValue(newValue);
  }

  toggleFormulaInfo(formula: FormulaFunction | null) {
    this.showDescription = true;
    this.selectedFormula = formula;
    this.isFormulaClicked = true;
    // if (formula?.event === this.selectedFormula?.event && this.isFormulaClicked) {
    //   this.isFormulaClicked = false;
    //   this.showDescription = false;
    //   this.selectedFormula = null;
    // } else {
    //   this.selectedFormula = formula;
    //   this.isFormulaClicked = true;
    // }

  }

  onMouseMove(item: FormulaFunction | null) {
    if (!this.isFormulaClicked || !this.selectedFormula) {
      this.selectedFormula = item;
      this.showDescription = true;
    }
  }

  onMouseLeave(): void {
    if (!this.isFormulaClicked) {
      this.selectedFormula = null;
      this.showDescription = false;
    }
  }

  onTouchEnd(event: TouchEvent) {
    event.preventDefault();
    const touch = event.changedTouches[0];
    const touchX = touch.clientX;
    const touchY = touch.clientY;
    const elementAtTouchEnd = document.elementFromPoint(touchX, touchY) as HTMLElement;
    const codeEditorContainer = elementAtTouchEnd.closest('.ifp-prep-add__code-editor');
    if (codeEditorContainer) {
      this.onDrop({'type': 'drop'});
    }
  }

  proceed() {
    this.loader.set(true);
    this._prepService.loaderProcess.set(true);
    const configration = {columns: [{
      'name': this.expressionForm.value.fieldName,
      'formula': this.expressionForm.value.expression
    }]};
    this._prepService.disableSidebar.set(true);
    this._prepService.processActive.set(true);
    this._store.dispatch(loadNode[loadCreateNodeSuccessColumnUpdate]({config: configration, objectId: this.objectId }));
    const process = this._prepService.processStatus.subscribe(data=> {
      if (data?.workflow_status === workFlowState.completed) {
        this.isFormulaClicked = false;
        this.selectedFormula = null;
        this._prepService.isShowPreview = true;
        this._prepService.disableSidebar.set(false);
        // this.processed = true;
        this.setWorkFlow.set(true);
        this._store.dispatch(loadNode[loadCreateNodeSuccessColumnCreate]({config: { columns: []}}));
        this.fieldLoader.set(false);
        this.expressionForm.controls['fieldName'].reset();
        this.expressionForm.controls['fieldName'].setValue('');
        this.expressionForm.controls['expression'].setValue('');
        this.modalSucess.createElement();
        this._cdr.detectChanges();
        this.loader.set(false);
        this._prepService.loaderProcess.set(false);
        process.unsubscribe();
      } else if (data?.workflow_status === workFlowState.error) {
        this._prepService.isShowPreview = false;
        this._prepService.disableSidebar.set(false);
        this.loader.set(false);
        this._prepService.loaderProcess.set(false);
        process.unsubscribe();
      }
    });
  }
  checkColumnNameValidator(fieldList: string[]) {
    return  (control: AbstractControl): ValidationErrors | null => {
      const index = (fieldList ?? []).findIndex((item: string) => item.toLowerCase() === (control.value ?? '').trim().toLowerCase());
      if (index !== -1 && control.value && (control.value ?? '').trim() !== '') {
        return { inList: true };
      }
      return null;
    };
  }


  searchFormula(key: string) {
    if (key !== '' && key) {
      this.formulasToDisplay = this.formulas.filter((item: FormulaFunction) => item?.name?.toLowerCase()?.includes(key.toLowerCase()));
    } else {
      this.formulasToDisplay = this.formulas;
    }
  }

  searchField(key: string) {
    if (key !== '' && key) {
      this.fieldsToDisplay = this.fieldList.filter((item: string) => item.toLowerCase().includes(key.toLowerCase()));
    } else {
      this.fieldsToDisplay = this.fieldList;
    }
  }

  defineCodeMirror() {
    const wordRegexp = (words: any[]) =>{
      return new RegExp(`^(?:${words.join('|')})$`, 'i');
    };
    CodeMirror.defineMode('cypher', (config) => {
      const tokenBase = (stream: { next: () => any; match: (arg0: RegExp) => void; eat: (arg0: string) => any; skipToEnd: () => void; eatWhile: (arg0: RegExp) => void; current: () => any; }/* , state*/) => {
        curPunc = null;
        const ch = stream.next();
        if (ch ==='"') {
          stream.match(/^[^"]*"/);
          return 'string';
        }
        if (ch === '\'') {
          stream.match(/^[^']*'/);
          return 'string';
        }
        if (ch === '[') {
          let ch2;
          while ((ch2 = stream.next()) != null) {
            if (ch2 == ']') {
              const newStr = stream.current().replace(/[\[\]]/g, '');
              if (funcs.test(newStr)) {
                return 'builtin';
              }
              return 'variable';
            }
          }
        }
        if ((/[{}\\(\\),\\.;\\[\]]/).test(ch)) {
          curPunc = ch;
          return 'node';
        } else if (ch === '/' && stream.eat('/')) {
          stream.skipToEnd();
          return 'comment';
        } else if (operatorChars.test(ch)) {
          stream.eatWhile(operatorChars);
          return null;
        }
        stream.eatWhile(/[_\w\d]/);
        if (stream.eat(':')) {
          stream.eatWhile(/[\w\d_\\-]/);
          return 'atom';
        }
        const word = stream.current();
        if (funcs.test(word)) {
          return 'builtin';
        }
        if (preds.test(word)) {
          return 'def';
        }
        if (keywords.test(word) || systemKeywords.test(word)) {
          return 'keyword';
        }
        return 'variable';

      };
      const pushContext = (state: { context: { prev: any; indent: any; col: any; type: any; }; indent: any; }, type: string, col: number)=> {
        return state.context = {
          prev: state.context,
          indent: state.indent,
          col: col,
          type: type
        };
      };
      const popContext = (state: { indent: any; context: { indent: any; prev: any; }; })=> {
        state.indent = state.context.indent;
        return state.context = state.context.prev;
      };
      const indentUnit = config.indentUnit;
      let curPunc: any;
      const funcs = wordRegexp( this.fieldList);
      const preds = wordRegexp([]);
      const keywords = wordRegexp(this.fuctionList);
      const systemKeywords = wordRegexp([]);
      const operatorChars = /[*+\-<>=&|~%^/]/;
      return {
        startState: (/* base*/) =>{
          return {
            tokenize: tokenBase,
            context: null,
            indent: 0,
            col: 0
          };
        },
        token: (stream: any, state: any) =>{
          if (stream.sol()) {
            if (state.context && (state.context.align == null)) {
              state.context.align = false;
            }
            state.indent = stream.indentation();
          }
          // if (stream.eatSpace()) {
          //   return null;
          // }
          const style = state.tokenize(stream, state);
          if (style !== 'comment' && state.context && (state.context.align == null) && state.context.type !== 'pattern') {
            state.context.align = true;
          }
          if (curPunc === '(') {
            pushContext(state, ')', stream.column());
          } else if (curPunc === '[') {
            pushContext(state, ']', stream.column());
          } else if (curPunc === '{') {
            pushContext(state, '}', stream.column());
          } else if ((/[\]\\}\\)]/).test(curPunc)) {
            while (state.context && state.context.type === 'pattern') {
              popContext(state);
            }
            if (state.context && curPunc === state.context.type) {
              popContext(state);
            }
          } else if (curPunc === '.' && state.context && state.context.type === 'pattern') {
            popContext(state);
          } else if ((/atom|string|variable/).test(style) && state.context) {
            if ((/[\\}\]]/).test(state.context.type)) {
              pushContext(state, 'pattern', stream.column());
            } else if (state.context.type === 'pattern' && !state.context.align) {
              state.context.align = true;
              state.context.col = stream.column();
            }
          }
          return style;
        },
        indent: (state:any, textAfter: any) => {
          const firstChar = textAfter?.charAt(0);
          let context = state.context;
          if ((/[\]\\}]/).test(firstChar)) {
            while (context && context.type === 'pattern') {
              context = context.prev;
            }
          }
          const closing = context && firstChar === context.type;
          if (!context) {
            return 0;
          }
          if (context.type === 'keywords') {
            return CodeMirror.commands.newlineAndIndent;
          }
          if (context.align) {
            return context.col + (closing ? 0 : 1);
          }
          return context.indent + (closing ? 0 : indentUnit);
        }
      };
    });



    CodeMirror.defineMIME('application/x-cypher-query', 'cypher');


  }

  closeSucessModal() {
    this.modalSucess.removeModal();
  }

  buttonClick(_event: CardData) {
    this.modalSucess?.removeModal();
  }


  ngOnDestroy(): void {
    if ( !this.processed) {
      this._store.dispatch(loadNode[loadCreateNodeSuccessColumnRemove]({ objectId: this.objectId}));
    }
    this.subs.unsubscribe();
    this.modalSucess.removeModal();
    this._prepService.showPreview.set(false);
    this._prepService.processEvent.next('');
  }
}



export interface Formula {
  category: string;
  functions: FormulaFunction[];
}

export interface  FormulaFunction {
  name: string;
  syntax: string;
  parameter_count: number;
  example: string;
  description: string;
  event?: string;
  keyword: string;
}


export interface FormulaDisplay {
  event: string;
  name: string;
  icon: string;
  description: string;
  syntax: string;
  example: string;
}


