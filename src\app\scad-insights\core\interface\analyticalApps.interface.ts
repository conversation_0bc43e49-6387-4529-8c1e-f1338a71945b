export interface AnalyticalApps {
  id: string;
  items: AnalyticalAppsItem [];
}



export interface AnalyticalAppsItem  {
  id: string;
  type: string;
  showSecondLevel:boolean;
  component_subtitle: string;
  component_title: string;
  domains: any[];
  application_url: string;
  imgSrc: string;
  attachment: string;
  policy_guide: string;
  note: string;
  search_tags: any[];
  narrative: string;
  Indicator: string;
  indicator_list: string;
  indicatorValues_subtitle: string;
  indicatorValues_title: string;
  visualization_subtitle: string;
  visualization_title: string;
  enableDynamicPanel: boolean;
  listofDyanmicPanelContent: any[];
  highlightsMeta: string;
  infogramUrl: string;
  confidenceIntervalMeta: string;
  enableConfidenceInterval: boolean;
  endpoint_label: string;
  endpoint_title: string;
  endpoint_url: string;
  default_layer: string;
  show_on_legend: string;
  defaultDistrictId: string;
  endpointType: string;
  nodeId: string;
  summaryCardId: string;
  endpoint_icon_id: string;
  cardDate: string;
  dashboardUrl: string;
  enablePointToggle: boolean;
  maxPointLimit: string;
  minLimitYAxis: string;
  publication_date: string;
  updated: string;
  tagName: string;
  tagColorCode: string;
  showInsights: string;
  height: string;
  host_url: string;
  embedded_code_version: string;
  site_root: string;
  external_name: string;
  tabs: string;
  toolbar: string;
  showAppBanner: string;
  compare_data: string;
  language: string;
  indicatorTools: IndicatorTool[];
  indicatorDrivers: IndicatorDriver[];
  indicatorFilters: IndicatorFilter[];
  indicatorValues?: IndicatorValues;
  indicatorVisualizations: IndicatorVisualizations;
  indicatorType: string;
}

interface IndicatorVisualizations {
  visualizationsMeta: VisualizationsMeta[];
  visualizationDefault: string;
}

interface VisualizationsMeta {
  id: string;
  type: string;
  comboIdTable: string;
  viewName: string;
  seriesMeta: SeriesMeta[];
  markersMeta: MarkersMeta[];
  showInterval: boolean;
  showQuarterlyIntervals: boolean;
  showPointLabels: boolean;
  xAxisLabel?: any;
  yAxisLabel?: any;
  yAxisExtraStepMin: number;
  yAxisExtraStepMax: number;
  xAxisFormat: string;
  yAxisFormat: string;
  tooltipTitleFormat: string;
  tooltipValueFormat: string;
  accuracyMetrics: AccuracyMetrics;
}

interface AccuracyMetrics {
  title: string;
  viewName: string;
  dbColumn: string;
  dateFormat: string;
}

interface MarkersMeta {
  id: string;
  color: string;
  type: string;
  labelText: string;
  axis: string;
  accessor: XAccessor;
}

interface SeriesMeta {
  id: string;
  label: string;
  color: string;
  type: string;
  dimension: Dimension2;
  xAccessor: XAccessor;
  yAccessor: YAccessor;
}

interface YAccessor {
  type: string;
  path: string;
}

interface XAccessor {
  type: string;
  path: string;
  specifier: string;
}

interface Dimension2 {
  TYPE: string;
  SECTOR: string;
  OIL_NONOIL: string;
  INDUSTRY: string;
}

interface IndicatorValues {
  overviewValuesMeta?: OverviewValuesMeta[];
  valuesMeta?: ValuesMeta[];
}

interface ValuesMeta {
  id?: string;
  title?: string;
  type?: string;
  valueFormat?: string;
  templateFormat?: string;
}

interface OverviewValuesMeta {
  id: string;
  type: string;
  valueFormat: string;
  templateFormat: string;
  viewName: string;
  comboIdTable?: string;
  dimension: Dimension;
  dateFormat?: string;
  dateStart: string;
  value: number | string;
  title?: string;
  color?: string;
  unit?: string;
  period?: number;
  dateEnd?: string;
}

interface Dimension {
  TYPE?: string;
  SECTOR: string;
  OIL_NONOIL: string;
  INDUSTRY: string;
}

interface IndicatorFilter {
  id: string;
  options: Option2[];
}

interface Option2 {
  id: string;
  label: string;
  value?: number;
  unit?: string;
  isSelected?: boolean;
}

interface IndicatorDriver {
  title: string;
  type: string;
  id: string;
  subtitle: string;
  options: Option[];
  note?: string;
}

interface Option {
  label: string;
  value: string;
  isSelected: boolean;
}

interface IndicatorTool {
  id: string;
  disabled?: boolean;
  label: string;
}
