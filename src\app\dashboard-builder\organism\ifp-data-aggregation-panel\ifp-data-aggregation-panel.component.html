<div class="ifp-data-agg">
  <p class="ifp-data-agg__desc">{{'Filter the source data, then drag and drop the X and Y coordinates along with any desired aggregations to build your visualization.'| translate }}</p>

  <form [formGroup]="aggregationForm">
  <label class="ifp-data-agg__input-label">{{'X-Variable Label' | translate}}</label>
  <input type="text" class="ifp-data-agg__input ifp-input" formControlName='xAxisLabel'>

  <label class="ifp-data-agg__input-label">{{'Select X-Variable' | translate}}</label>

  <ifp-multi-tag-dropdown [options]="possibleAggregationColumns()" [multiSelect]="false" 
  [optionArrayKey]="'x_aggregations'" (dropDownItemClicked)="selectXaxis($event)"
    formControlName='xAxisValue'></ifp-multi-tag-dropdown>


  <label class="ifp-data-agg__input-label">{{'Y-Variable Label' | translate}}</label>
  <input type="text" class="ifp-data-agg__input ifp-input" formControlName='yAxisLabel'>

  <label class="ifp-data-agg__input-label">{{'Select Y-Variable' | translate}}</label>

  <ifp-multi-tag-dropdown [options]="possibleAggregationColumns()" [multiSelect]="true"
  [optionArrayKey]="'y_aggregations'" (dropDownItemClicked)="selectYaxis($event)" formControlName='yAxisValue'></ifp-multi-tag-dropdown>
</form>
</div>

  <div class="ifp-data-agg__footer-wrapper">
    <ifp-button [label]="'Preview' | translate" (ifpClick)="preview()" [buttonClass]="buttonClass.secondary"
      class="ifp-data-agg__cancel-btn"></ifp-button>

    <ifp-button [label]="'Save' | translate" (ifpClick)="save()" [buttonClass]="buttonClass.primary"
      class="ifp-data-agg__apply-btn"></ifp-button>
  </div>
