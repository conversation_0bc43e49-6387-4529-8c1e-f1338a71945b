@use "../../../assets/ifp-styles/abstracts/index" as *;
.ifp-dashboard-publish {
  &__title {
    font-size: $ifp-fs-13;
    font-weight: $fw-bold;
  }
  &__desc {
    font-size:$ifp-fs-4;
    color: $ifp-color-grey-14;
  }
  &__card {
    margin-top:$spacer-4;
    border-radius: 20px;
    background-color: $ifp-color-white;
  }
  &__card-inner {
    display: flex;

  }
    &__sec-1 {
      padding: $spacer-5;
    }
  &__sec-1 ,&__sec-2{
      width: 50%;
  }

  &__header-wrapper {
    margin-top:$spacer-6;
     text-align: center;
  }
  &__desc {
    margin-top: $spacer-3;
  }
  &__input-label {
    margin-bottom: $spacer-2;
    color: $ifp-color-grey-14;
      font-size: $ifp-fs-4;
  }
     &__dropdown {
      margin-top: $spacer-2;
     }
     &__input {
      margin-top: $spacer-3;
     }
     &__user {
          margin-top: $spacer-3;
    text-align: left;
  }

  &__user-list {
    display: block;
  }
  &__user-group {
    display: block;
    margin-top: $spacer-3;
  }
  &__user-icon {
    font-size: $ifp-fs-8;
  }
  &__wrapper {
    height:calc(100% - ($spacer-2 + $spacer-7));
    width: calc(100% - $spacer-3);
     background-color: $ifp-color-grey-bg;
     margin: $spacer-2 $spacer-2 $spacer-7;
     border-radius: 10px;
     display: flex;
     flex-direction: column;
  }
  &__preview-title {
    text-align: center;
    padding-top: $spacer-5;
      font-size: $ifp-fs-4;
    font-weight: $fw-semi-bold;
  }
  &__note-text {

 font-size: $ifp-fs-3;
 font-weight: $fw-bold;
 margin: $spacer-0 $spacer-2;
  }
  &__note{
     margin-top: auto;
        font-size: $ifp-fs-3;
        text-align: center;
        margin-bottom: $spacer-5;
  }

  &__note-icon {
    font-weight: $fw-bold;
  }
  &__img {
    margin: $spacer-7;
  }
  &__footer {
    border-top: 1px solid $ifp-color-grey-7;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    padding: $spacer-0 $spacer-3;
    flex-wrap: wrap;
  }
  &__btn {
    margin: $spacer-3 $spacer-3;
  }

}
:host::ng-deep {
  .ifp-panel-dropdown__label,.ifp-dropdown__title-text  {
    font-size: $ifp-fs-4 !important;
    font-weight: $fw-regular;
     color: $ifp-color-grey-14;
  }
  .ifp-dashboard-publish {

    &__dropdown {
      .ifp-dropdown__title {
        width: 100%;
      }
      .ifp-dropdown {

      max-width: 100%;
      }
    }
  }

}
