import { ChangeDetectionStrategy, ChangeDetectorRef, Component, EventEmitter, inject, Input, OnChanges, On<PERSON><PERSON>roy, OnInit, Output, signal, WritableSignal } from '@angular/core';
import { DbDropDown } from 'src/app/dashboard-builder/molecule/ifp-db-dropdown/ifp-db-dropdown.interface';
import { ColumnPrep } from '../../data-prep/ifp-data-prep/ifp-prep-select-data/interface/prep-selection.interface';
import { IfpButtonComponent } from '../../../scad-insights/ifp-widgets/ifp-atoms/ifp-button/ifp-button.component';
import { buttonClass } from 'src/app/scad-insights/core/constants/button.constants';
import { IfpDataToolAccordianComponent } from '../../molecule/ifp-data-tool-accordian/ifp-data-tool-accordian.component';
import { TranslateModule } from '@ngx-translate/core';
import { IfpSpinnerComponent } from '../../../scad-insights/ifp-widgets/ifp-molecules/ifp-spinner/ifp-spinner.component';
import { selectNodeStoreAdvance } from '../../data-prep/ifp-advance-prep/store/advance-node-store/advance-node.store';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { IfpAdvancePrepService } from '../../data-prep/ifp-advance-prep/service/ifp-advance-prep.service';
import { ToasterService } from 'src/app/scad-insights/core/services/tooster/ToastrService.service';
import { connectionType } from '../../data-prep/ifp-data-prep/constants/if-preps.constants';
import { IfpDropdownComponent } from 'src/app/scad-insights/ifp-widgets/ifp-atoms/ifp-dropdown/ifp-dropdown.component';


@Component({
    selector: 'ifp-adv-sort',
    imports: [IfpButtonComponent, IfpDataToolAccordianComponent, TranslateModule, IfpSpinnerComponent, ReactiveFormsModule, IfpDropdownComponent],
    templateUrl: './ifp-adv-sort.component.html',
    styleUrl: './ifp-adv-sort.component.scss',
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class IfpAdvSortComponent implements OnInit, OnChanges, OnDestroy {
  @Output() cancel = new EventEmitter();
  @Output() save = new EventEmitter();
  @Input() sortOrder: SortOption[] = [
    {
      name: 'Ascending',
      value: 'asc'
    },
    {
      name: 'Descending',
      value: 'desc'

    }
  ];

  @Input({ required: true }) columnList!: ColumnPrep[];
  @Input() dataTypes: DbDropDown[] = [];
  @Input() workFlowRunning = signal(false);
  @Input() emptyConnection = signal(true);
  @Input() currentNodeId?: string = '';
  @Input() currentConection: WritableSignal<boolean> = signal(false);
  @Input() sourceData: WritableSignal<boolean> = signal(false);
  @Input() isConfigUpdated: WritableSignal<boolean> = signal(false);
  @Input() errorMessageForTools?: string;
  public buttonClass = buttonClass;
  public readonly advanceStore = inject(selectNodeStoreAdvance);
  public sortForm!: FormGroup;
  public selectedColumn!: ColumnPrep;
  public selectedSort!: SortOption;
  public sortList: SelectedSort[] = [];
  public submitted: WritableSignal<boolean> = signal(false);
  public savedConfig: WritableSignal<boolean> = signal(true);
  public isSelectActive: boolean = false;
  public selectedDatatype!: string;
  public selectedIndex!: number;

  constructor(private _formbuilder: FormBuilder, private _prepService: IfpAdvancePrepService, private _toaster: ToasterService,
    private _cdr: ChangeDetectorRef
  ) { }


  ngOnInit() {

  }

  ngOnChanges() {
    if (this.columnList?.length > 0) {
      this.sortForm = this._formbuilder.group({
        column: ['', Validators.required],
        order: ['', Validators.required],
        priority: ['']
      });
      this.sortList = [];
      this.patchStoreData();
    }
  }

  patchStoreData() {
    this.resetForm();
    // setTimeout(() => {
    this.sortList = [];
    const nodeData = this.advanceStore.selectNodeValue().nodeValue(this.currentNodeId ?? '');
    const columns = nodeData?.configuration?.columns ?? {};
    for (const key in columns) {
      if (Object.hasOwn(columns, key)) {
        const order = this.sortOrder?.find(x => x.value === columns[key].order);
        this.sortList.push({
          column: key,
          order: order ?? this.sortOrder[0],
          priority: columns[key].priority
        });
      }
    }
    this.sortList?.sort((a, b) => (a.priority ?? 0) - (b.priority ?? 0));
    this._cdr.detectChanges();
    // }, 300);
  }

  selectColumn(event: ColumnPrep | any) {
    this.selectedColumn = event;
    this.selectedDatatype = event.type;
    this.sortForm.controls['column'].setValue(this.selectedColumn.name);
  }

  resetForm() {
    this.isSelectActive = false;
    this.selectedIndex = -1;
    this.sortForm?.patchValue({
      column: '',
      order: '',
      priority: ''
    });
  }

  selectOrder(event: SortOption | any) {
    this.selectedSort = event;
  }

  onSave() {
    const config = {
      columns: this.getSortOpts()
    };
    this.savedConfig.set(true);
    this.advanceStore.updateNode(this.currentNodeId ?? '', 'Sort Tool updated', connectionType.sortTool, config, 'Sort Tool updated');
    this._prepService.nodeChangeDetect.next('node updated');
    // this._toaster.success('Data Saved Successfully');
    this.save.emit();
  }

  getSortOpts() {
    let selectedOptions: any = {};
    if (this.sortList?.length) {
      this.sortList.forEach((element: SelectedSort, index: number) => {
        const data = {
          [element.column]: {
            order: element.order.value,
            priority: index + 1
          }
        };
        selectedOptions = { ...selectedOptions, ...data };
      });
    }
    return selectedOptions;
  }

  addSort() {
    if (this.sortForm.invalid) {
      this.submitted.set(true);
      return;
    }
    this.submitted.set(false);
    if (this.isSelectActive) {
      this.sortList[this.selectedIndex] = this.sortForm.value;
      this.selectedIndex = -1;
    } else {
      const isAdded = this.sortList.findIndex((item: { column: string }) => this.sortForm.value.column === item.column);
      if (isAdded === -1) {
        this.sortList.push(this.sortForm.value);
      } else {
        this.sortList.splice(isAdded, 1);
        this.sortList.push(this.sortForm.value);
      }
    }
    this.resetForm();
  }

  removeSort(index: number) {
    this.sortList.splice(index, 1);
  }

  onCancel() {
    this.cancel.emit();
  }

  selectSort(sort: SelectedSort, index: number) {
    this.isSelectActive = true;
    this.selectedIndex = index;
    const selectedCol = this.columnList.find((col: ColumnPrep) => col.name === sort.column);
    if (selectedCol) {
      this.selectedColumn = selectedCol;
      this.selectedSort = sort.order;
    }
    this.sortForm.setValue({
      column: sort.column,
      order: sort.order,
      priority: sort.priority
    });
  }

  get getControls() {
    return this.sortForm.controls;
  }



  ngOnDestroy(): void {
    if (!this._prepService.isConfigSave() && this.columnList?.length > 0) {
      this.onSave();
    }
  }
}

interface SelectedSort {
  column: string;
  order: SortOption;
  priority: number;
}

interface SortOption {
  name: string;
  value: string;
}

