import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, ChangeDetectorRef, Component, EventEmitter, Input, OnChanges, Output, SimpleChanges } from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';
import { IfpButtonComponent } from '../../../scad-insights/ifp-widgets/ifp-atoms/ifp-button/ifp-button.component';
import { buttonClass } from 'src/app/scad-insights/core/constants/button.constants';
import { DashboardService } from 'src/app/scad-insights/core/services/create-dashboard/dashboard.service';
import { IfpDbDropdownComponent } from '../../molecule/ifp-db-dropdown/ifp-db-dropdown.component';
import { chartConstants, checkAll } from 'src/app/scad-insights/core/constants/chart.constants';
import { loadAnalyticalSuccess } from 'src/app/scad-insights/home/<USER>/Analytical apps/analyticalApps.action';
import { Store } from '@ngrx/store';
import { cloneDeep } from 'lodash';
import { PagesService } from 'src/app/scad-insights/core/services/pages/pages.service';
import { selectIndicatorGetById } from 'src/app/scad-insights/store/incidicator-list/incidicator-list.selector';

@Component({
    selector: 'ifp-ifp-chart-card-filter',
    templateUrl: './ifp-chart-card-filter.component.html',
    styleUrl: './ifp-chart-card-filter.component.scss',
    changeDetection: ChangeDetectionStrategy.OnPush,
    imports: [
    CommonModule,
    TranslateModule,
    IfpButtonComponent,
    IfpDbDropdownComponent
]
})
export class IfpChartCardFilterComponent implements OnChanges {


  @Input() filters: any = [];
  @Input() selectedCard!: any;
  @Input() cntType!: string;
  @Input() visualizationData: any = [];
  @Input() visualDataNames: any = [];
  @Input() allChartData: any = [];
  @Output() closeFilter: EventEmitter<boolean> = new EventEmitter<boolean>();

  public buttonClass = buttonClass;
  public chartConstants = chartConstants;
  filterKeys: any = [];
  public timePeriodOptions: string[] = [];


  constructor(private _dashboardService: DashboardService, private store: Store, private _pageService: PagesService, private cdr: ChangeDetectorRef) { }




  ngOnChanges(changes: SimpleChanges): void {
    if (changes['filters']) {
      this.filterKeys = [];
      if (this.filters?.length > 0 && !this.checkFilterAvailableOrNot()) {
        this.createInitialFilter();
      }
    }
    if (changes['selectedCard']) {
      this.visualDataNames = changes['visualDataNames']?.currentValue ? changes['visualDataNames']?.currentValue : [];
    }
  }

  createInitialFilter() {
    this.filterKeys = [];
    this.filters.forEach((element: any, index: number) => {
      element.isDisabled = false;
      element.filterOptions[0].checked = true;
      const obj = {
        label: element.label,
        value: [element.options[0]],
        index: index,
        path: element.path,
        defualtOpts: [{ name: element.options[0], checked: true }]
      };
      this.filterKeys.push(obj);
      if (obj.path == chartConstants.TIME_PERIOD) {
        this.timePeriodOptions = element.options;
      }
    });
  }

  closeModel() {
    const index = this._dashboardService.chartSettings[this.cntType]?.findIndex((x: { id: string; }) => x.id == this.selectedCard);
    this.filterKeys = cloneDeep(this._dashboardService.chartSettings[this.cntType][index].selectedFilter);
    this.closeFilter.emit(true);
  }

  applyFilter(event: any, label: string) {
    if (!Array.isArray(event)) {
      event = [event];
    }
    const selectedOptions: string[] = [];
    if (event?.length > 0) {
      event.forEach((element: { name: string; }) => {
        selectedOptions.push(element.name);
      });
    }
    this.filterKeys.find((x: { label: any; }) => x.label == label).value = selectedOptions;
    this.filterKeys.find((x: { label: any; }) => x.label == label).defualtOpts = event;
  }

  checkRadio() {
    let isRadio = true;
    if (this.filterKeys.length > 0) {
      if (this.filterKeys.some((x: { path: string; value: string | any[]; }) => x.value?.length > 1 && x?.path != this.chartConstants.TIME_PERIOD)) {
        isRadio = false;
      }
    }
    return isRadio;
  }

  checkMultiSelectIndex() {
    return this.filterKeys.findIndex((x: { path: string; value: string | any[]; }) => x.value.length > 1 && x.path != this.chartConstants.TIME_PERIOD);
  }

  apply() {
    this.setFilter();
    if (this._dashboardService.chartSettings[this.cntType]?.find((x: { id: string; }) => x.id == this.selectedCard)) {
      const index = this._dashboardService.chartSettings[this.cntType]?.findIndex((x: { id: string; }) => x.id == this.selectedCard);
      this._dashboardService.chartSettings[this.cntType][index].selectedFilter = cloneDeep(this.filterKeys);
      this._dashboardService.chartSettings[this.cntType][index].selectedVisualId = this.visualizationData.id;
    } else {
      const data: any = {
        id: this.selectedCard,
        selectedFilter: this.filterKeys
      };
      this._dashboardService.chartSettings[this.cntType].push(data);
    }

    if (this.cntType == chartConstants.ANALYTICAL_APPS) {
      this.callInsightDiscoveryFilter();
    } else {
      this._dashboardService.settingsChanged.next({ type: this.cntType, id: this.selectedCard, tools: this._dashboardService.cardFilter });
    }
    this.closeFilter.emit(true);
  }

  checkFilterAvailableOrNot() {
    let isFilterAvailable: boolean = false;
    if (this._dashboardService.chartSettings[this.cntType]?.find((x: { id: string; }) => x.id == this.selectedCard)?.selectedFilter) {
      isFilterAvailable = true;
      const index = this._dashboardService.chartSettings[this.cntType]?.findIndex((x: { id: string; }) => x.id == this.selectedCard);
      this.filterKeys = cloneDeep(this._dashboardService.chartSettings[this.cntType][index].selectedFilter);
      if (this.filterKeys?.length > 0) {
        this.filterKeys.forEach((element: any, keyIndex: number) => {
          if (element.defualtOpts?.length > 0) {
            element.defualtOpts.forEach((key: { name: any; }) => {
              const optIndex = this.filters[keyIndex].filterOptions.findIndex((x: { name: any; }) => x.name == key.name);
              this.filters[keyIndex].filterOptions[optIndex].checked = true;
            });
          }
          if (element.path == chartConstants.TIME_PERIOD) {
            this.timePeriodOptions = element.options;
          }
        });
      }
    }
    return isFilterAvailable;
  }

  getSelectedSingleItem(index: number) {
    let selectedItem: any;
    if (this.filterKeys[index].defualtOpts?.[0]) {
      selectedItem = this.filterKeys[index]?.defualtOpts?.[0];
    }
    return selectedItem;
  }

  getSelectedMultipleItem(index: number) {
    let selectedItem: any = [];
    if (this.filterKeys?.length > 0 && this.filterKeys[index]?.defualtOpts) {
      selectedItem = this.filterKeys[index]?.defualtOpts;
      if (this.filterKeys[index].defualtOpts) {
        selectedItem = this.filterKeys?.[index].defualtOpts;
      }
    }
    return selectedItem;
  }

  setFilter() {
    if (this.filterKeys?.length > 0) {
      if (this.filterKeys.some((z: { value: string | any[]; }) => z.value.length > 1)) {
        this.filterKeys.map((x: any) => {
          x.checkbox = x.value.length > 1 && x.path != this.chartConstants.TIME_PERIOD ? true : false;
        });
        if (this.filterKeys.every((y: any) => !y.checkbox)) {
          this.filterKeys[this.filterKeys[0].path != chartConstants.TIME_PERIOD ? 0 : 1].checkbox = true;
        }
      }
      if (!this.filterKeys.some((z: { value: string | any[]; }) => z.value.length > 1)) {
        this.filterKeys.forEach((element: any, index: number) => {
          const selectIndex = this.filterKeys[0].path == this.chartConstants.TIME_PERIOD ? 1 : 0;
          element.checkbox = index == selectIndex ? true : false;
        });
      }
      if (this.filterKeys.find((x: { path: string; }) => x.path == chartConstants.TIME_PERIOD)) {
        const timeIndex = this.filterKeys.findIndex((y: { path: string; }) => y.path == chartConstants.TIME_PERIOD);
        if (checkAll(this.filterKeys[timeIndex].value[0])) {
          this.filterKeys[timeIndex].value = this.timePeriodOptions;
        }
      }
    }
  }

  callInsightDiscoveryFilter() {
    if (this.filterKeys?.length > 0) {
      const filterBy: any = {};
      this.filterKeys.forEach((element: { value: any; path: string | number; }) => {
        if (element.path == chartConstants.TIME_PERIOD && element.value?.length == 1 && element.value[0] == 'All') {
          element.value = this.timePeriodOptions;
        }
        filterBy[element.path == chartConstants.TIME_PERIOD ? chartConstants.YEAR_PERIOD : element.path] = element.value.map((x: string) => x) && element.value?.length == 1 ? element.value.toString() : (element.path == chartConstants.TIME_PERIOD ? this.getOptions(element.value) : element.value.map((x: string) => x));
      });
      const data = {
        meta: [
          {
            dbColumn: this.visualizationData.indicatorVisualizations?.visualizationsMeta[0].dbColumn,
            dbIndicatorId: this.visualizationData.indicatorVisualizations?.visualizationsMeta[0].seriesMeta[0].dbIndicatorId,
            viewName: this.visualizationData.indicatorVisualizations?.visualizationsMeta[0].viewName,
            filterBy: filterBy
          }
        ]
      };
      const index = this._dashboardService.chartSettings[this.cntType]?.findIndex((x: { id: string; }) => x.id == this.selectedCard);
      this._dashboardService.chartSettings[this.cntType][index].filterPayload = data.meta;
      this._pageService.getFilterCall(data.meta).subscribe(dataValue => {
        let updatedResponse: any = [];
        this.store.select(selectIndicatorGetById(this.selectedCard.toString())).subscribe(resp => {
          updatedResponse = [];
          updatedResponse = cloneDeep(resp);
          if (updatedResponse.body && updatedResponse.body.visualizations?.length > 0) {
            const dataIndex = updatedResponse.body.visualizations.findIndex((x: { id: number; }) => x.id == this.visualizationData.id);
            const metaIndex = updatedResponse.body.visualizations[dataIndex].indicatorVisualizations.visualizationsMeta.findIndex((y: { id: any; }) => y.id == updatedResponse.body.visualizations[dataIndex].indicatorVisualizations.visualizationDefault)
            updatedResponse.body.visualizations[dataIndex].indicatorVisualizations.visualizationsMeta[metaIndex].seriesMeta[0].data = dataValue[0].data;
          }
        });
        const id = this.selectedCard;
        const initialization = dataValue.type !== 'insights-discovery' ? updatedResponse.body : updatedResponse.body.visualizations?.[0];
        const postData = { data: { ...updatedResponse.body, id }, status: { status: true, errorMessage: '', loader: false }, initialData: initialization, isRender: true };
        this.store.dispatch(loadAnalyticalSuccess({ data: postData }));

        // setTimeout(() => {
        this._dashboardService.settingsChanged.next({ type: this.cntType, id: this.selectedCard, tools: this._dashboardService.cardFilter });
        // }, 3000);
      });
    }
  }

  getOptions(options: any) {
    const opt = cloneDeep(options);
    if (opt.includes('All')) {
      opt.splice(opt.indexOf('All'), 1);
    }
    return opt;
  }

  selectViews(event: any) {
    this.filters = [];
    if (event.name) {
      setTimeout(() => {
        this.visualizationData = this.allChartData?.visualizations.find((x: { component_title: any; }) => x?.component_title == event.name);
        if (this.visualizationData?.filterPanel?.properties?.length > 0) {
          this.filters = this.visualizationData?.filterPanel?.properties;
          this.filters.forEach((element: { filterOptions: { name: string; }[]; options: string[]; }) => {
            element.filterOptions = this.setOptions(element.options);
          });
        }
        if (this._dashboardService.chartSettings[this.cntType]?.find((x: { id: string; }) => x.id == this.selectedCard)?.selectedFilter) {
          const index = this._dashboardService.chartSettings[this.cntType]?.findIndex((x: { id: string; }) => x.id == this.selectedCard);
          this._dashboardService.chartSettings[this.cntType][index].selectedFilter = undefined;
        }
        this.createInitialFilter();
        this.cdr.detectChanges();
      }, 300);

    }
  }


  setOptions(options: string[]) {
    const filterOptions: { name: string }[] = [];
    if (options?.length > 0) {
      options.forEach(element => {
        const data = {
          name: element
        };
        filterOptions.push(data);
      });
    }
    return filterOptions;
  }

  getDefualtView() {
    return { name: this.visualizationData.component_title };
  }

  checkVisualId() {

  }

}
