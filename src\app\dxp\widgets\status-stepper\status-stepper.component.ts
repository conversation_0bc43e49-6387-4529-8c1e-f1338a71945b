import { NgClass } from '@angular/common';
import { Component, input } from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';

@Component({
  selector: 'ifp-status-stepper',
  imports: [NgClass, TranslateModule],
  templateUrl: './status-stepper.component.html',
  styleUrl: './status-stepper.component.scss'
})
export class StatusStepperComponent {
  public statuses =  input<{
    status: string;
    statusKey: string;
    name: string;
}[]>([]);

}
