import { ComponentFixture, TestBed } from '@angular/core/testing';

import { DxpLabelDragComponent } from './dxp-label-drag.component';

describe('DxpLabelDragComponent', () => {
  let component: DxpLabelDragComponent;
  let fixture: ComponentFixture<DxpLabelDragComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [DxpLabelDragComponent]
    })
    .compileComponents();

    fixture = TestBed.createComponent(DxpLabelDragComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
