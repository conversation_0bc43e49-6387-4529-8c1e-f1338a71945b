import { NgModule, inject } from '@angular/core';
import { ActivatedRouteSnapshot, RouterModule, RouterStateSnapshot, Routes } from '@angular/router';
import { IfpPagesComponent } from './ifp-pages.component';
import { AuthGuardService } from 'src/app/scad-insights/core/guard/auth.guard';
import { PrintLayoutComponent } from '../print-layout/print-layout.component';
import { UsageDashboardGuardService } from 'src/app/scad-insights/core/guard/usagedashboard.guard';
import { insightGuardService } from 'src/app/scad-insights/core/guard/insight.guards';
import { TestingtoolGuardService } from 'src/app/scad-insights/core/guard/testing-tool.guard';
const routes: Routes = [{
  path: '', component: IfpPagesComponent,
  children: [
    {
      path: 'home', loadChildren: () => import('src/app/scad-insights/home/<USER>').then((m: typeof import('src/app/scad-insights/home/<USER>')) => m.HomeModule),
      canActivate: [(route: ActivatedRouteSnapshot, state: RouterStateSnapshot) => inject(AuthGuardService).canActivate(route, state)]
    },
    {
      path: 'my-apps', loadChildren: () => import('src/app/scad-insights/my-apps/my-apps.module').then((m: typeof import('src/app/scad-insights/my-apps/my-apps.module')) => m.MyAppsModule),
      canActivate: [(route: ActivatedRouteSnapshot, state: RouterStateSnapshot) => inject(AuthGuardService).canActivate(route, state)]
    },
    {
      path: 'my-apps/view', loadChildren: () => import('src/app/scad-insights/my-apps/my-apps.module').then((m: typeof import('src/app/scad-insights/my-apps/my-apps.module')) => m.MyAppsModule),
      canActivate: [(route: ActivatedRouteSnapshot, state: RouterStateSnapshot) => inject(AuthGuardService).canActivate(route, state)]
    },
    {
      path: 'domain-exploration', loadChildren: () => import('src/app/scad-insights/domains/domains.module').then((m: typeof import('src/app/scad-insights/domains/domains.module')) => m.DomainsModule),
      canActivate: [(route: ActivatedRouteSnapshot, state: RouterStateSnapshot) => inject(AuthGuardService).canActivate(route, state)]
    },
    {
      path: 'search',
      loadComponent: () => import('src/app/scad-insights/ifp-search-result/ifp-search-result.component').then((m: typeof import('src/app/scad-insights/ifp-search-result/ifp-search-result.component')) => m.IfpSearchResultComponent),
      canActivate: [(route: ActivatedRouteSnapshot, state: RouterStateSnapshot) => inject(AuthGuardService).canActivate(route, state)]
    },
    {
      path: 'geospatial',
      loadComponent: () => import('src/app/scad-insights/home/<USER>/geospatial-alone.component').then((m: typeof import('src/app/scad-insights/home/<USER>/geospatial-alone.component')) => m.GeospatialAloneComponent),
      canActivate: [(route: ActivatedRouteSnapshot, state: RouterStateSnapshot) => inject(AuthGuardService).canActivate(route, state)]
    },
    {
      path: 'geospatial-app',
      loadComponent: () => import('src/app/scad-insights/home/<USER>/geospatial-app.component').then((m: typeof import('src/app/scad-insights/home/<USER>/geospatial-app.component')) => m.GeospatialAppComponent),
      canActivate: [(route: ActivatedRouteSnapshot, state: RouterStateSnapshot) => inject(AuthGuardService).canActivate(route, state)]
    },
    {
      path: 'geospatial-single',
      loadComponent: () => import('src/app/scad-insights/home/<USER>/geo-single-app.component').then((m: typeof import('src/app/scad-insights/home/<USER>/geo-single-app.component')) => m.GeoSingleAppComponent),
      canActivate: [(route: ActivatedRouteSnapshot, state: RouterStateSnapshot) => inject(AuthGuardService).canActivate(route, state)]
    },
    {
      path: '404',
      loadComponent: () => import('src/app/scad-insights/authentication/not-found/not-found.component').then((m: typeof import('src/app/scad-insights/authentication/not-found/not-found.component')) => m.NotFoundComponent)
    },
    {
      path: 'notifications',
      loadComponent: () => import('src/app/scad-insights/ifp-notification-list/ifp-notification-list.component').then((m: typeof import('src/app/scad-insights/ifp-notification-list/ifp-notification-list.component')) => m.IfpNotificationListComponent),
      canActivate: [(route: ActivatedRouteSnapshot, state: RouterStateSnapshot) => inject(AuthGuardService).canActivate(route, state)]
    },
    {
      path: 'dashboard/pmi-automatic',
      loadComponent: () => import('src/app/scad-insights/analytical-apps/dashboards/ifp-pmi-automatic/ifp-pmi-automatic.component').then((m: typeof import('src/app/scad-insights/analytical-apps/dashboards/ifp-pmi-automatic/ifp-pmi-automatic.component')) => m.IfpPmiAutomaticComponent)
    },
    {
      path: 'dashboard/pmi-fixed',
      loadComponent: () => import('src/app/scad-insights/analytical-apps/dashboards/ifp-pmi-fixed/ifp-pmi-fixed.component').then((m: typeof import('src/app/scad-insights/analytical-apps/dashboards/ifp-pmi-fixed/ifp-pmi-fixed.component')) => m.IfpPmiFixedComponent)
    },
    {
      path: 'common-dashboard/:id',
      loadComponent: () => import('src/app/scad-insights/ifp-widgets/ifp-molecules/iframe-loader/iframe-loader.component').then((m: typeof import('src/app/scad-insights/ifp-widgets/ifp-molecules/iframe-loader/iframe-loader.component')) => m.IframeLoaderComponent),
      canActivate: [(route: ActivatedRouteSnapshot, state: RouterStateSnapshot) => inject(AuthGuardService).canActivate(route, state)]
    },
    {
      path: 'dashboard/dashboard',
      loadComponent: () => import('src/app/scad-insights/analytical-apps/dashboards/ifp-dashboard-general/ifp-dashboard-general.component').then((m: typeof import('src/app/scad-insights/analytical-apps/dashboards/ifp-dashboard-general/ifp-dashboard-general.component')) => m.IfpDashboardGeneralComponent),
      canActivate: [(route: ActivatedRouteSnapshot, state: RouterStateSnapshot) => inject(AuthGuardService).canActivate(route, state)]
    },
    // {
    //   path: 'dashboard/pmi-automatic',
    //   loadComponent: () => import('src/app/scad-insights/analytical-apps/dashboards/ifp-pmi-automatic/ifp-pmi-automatic.component').then((m: typeof import('src/app/scad-insights/analytical-apps/dashboards/ifp-pmi-automatic/ifp-pmi-automatic.component')) => m.IfpPmiAutomaticComponent)
    // },
    {
      path: 'scenario-driver/:id',
      loadComponent: () => import('src/app/scad-insights/analytical-apps/ifp-scenario-driver/ifp-scenario-driver.component').then((m: typeof import('src/app/scad-insights/analytical-apps/ifp-scenario-driver/ifp-scenario-driver.component')) => m.IfpScenarioDriverComponent),
      canActivate: [(route: ActivatedRouteSnapshot, state: RouterStateSnapshot) => inject(AuthGuardService).canActivate(route, state)]
    },
    {
      path: 'dashboard/:contentType/:id',
      loadComponent: () => import('src/app/scad-insights/analytical-apps/ifp-dashboard-page/ifp-dashboard-page.component').then((m: typeof import('src/app/scad-insights/analytical-apps/ifp-dashboard-page/ifp-dashboard-page.component')) => m.IfpDashboardPageComponent),
      canActivate: [(route: ActivatedRouteSnapshot, state: RouterStateSnapshot) => inject(AuthGuardService).canActivate(route, state)]
    },
    {
      path: 'statistics-insights/:contentType/:id',
      loadComponent: () => import('src/app/scad-insights/statistics-insights/ifp-indicator-detail/ifp-indicator-detail.component').then((m: typeof import('src/app/scad-insights/statistics-insights/ifp-indicator-detail/ifp-indicator-detail.component')) => m.IfpIndicatorDetailComponent),
      canActivate: [(route: ActivatedRouteSnapshot, state: RouterStateSnapshot) => inject(AuthGuardService).canActivate(route, state)]
    },
    {
      path: 'insight-discovery/:id',
      loadComponent: () => import('src/app/scad-insights/analytical-apps/ifp-insight-discovery/ifp-insight-discovery.component').then((m: typeof import('src/app/scad-insights/analytical-apps/ifp-insight-discovery/ifp-insight-discovery.component')) => m.IfpInsightDiscoveryComponent),
      canActivate: [(route: ActivatedRouteSnapshot, state: RouterStateSnapshot) => inject(AuthGuardService).canActivate(route, state)]
    },
    {
      path: 'products',
      loadComponent: () => import('src/app/scad-insights/footer-pages/ifp-product-page/ifp-product-page.component').then((m: typeof import('src/app/scad-insights/footer-pages/ifp-product-page/ifp-product-page.component')) => m.IfpProductPageComponent),
      canActivate: [(route: ActivatedRouteSnapshot, state: RouterStateSnapshot) => inject(AuthGuardService).canActivate(route, state)]
    },
    {
      path: 'about-us',
      loadComponent: () => import('src/app/scad-insights/footer-pages/ifp-about-us/ifp-about-us.component').then((m: typeof import('src/app/scad-insights/footer-pages/ifp-about-us/ifp-about-us.component')) => m.IfpAboutUsComponent),
      canActivate: [(route: ActivatedRouteSnapshot, state: RouterStateSnapshot) => inject(AuthGuardService).canActivate(route, state)]
    },
    {
      path: 'terms',
      loadComponent: () => import('src/app/scad-insights/footer-pages/terms-n-conditions/terms-n-conditions.component').then((m: typeof import('src/app/scad-insights/footer-pages/terms-n-conditions/terms-n-conditions.component')) => m.TermsNConditionsComponent),
      canActivate: [(route: ActivatedRouteSnapshot, state: RouterStateSnapshot) => inject(AuthGuardService).canActivate(route, state)]
    },
    {
      path: 'privacy-policy',
      loadComponent: () => import('src/app/scad-insights/footer-pages/ifp-privacy-page/ifp-privacy-page.component').then((m: typeof import('src/app/scad-insights/footer-pages/ifp-privacy-page/ifp-privacy-page.component')) => m.IfpPrivacyPageComponent),
      canActivate: [(route: ActivatedRouteSnapshot, state: RouterStateSnapshot) => inject(AuthGuardService).canActivate(route, state)]
    },
    {
      path: 'contact',
      loadComponent: () => import('src/app/scad-insights/footer-pages/ifp-contact-page/ifp-contact-page.component').then((m: typeof import('src/app/scad-insights/footer-pages/ifp-contact-page/ifp-contact-page.component')) => m.IfpContactPageComponent),
      canActivate: [(route: ActivatedRouteSnapshot, state: RouterStateSnapshot) => inject(AuthGuardService).canActivate(route, state)]
    },
    {
      path: 'compare-chart',
      loadComponent: () => import('src/app/scad-insights/analytical-apps/ifp-compare-chart/ifp-compare-chart.component').then((m: typeof import('src/app/scad-insights/analytical-apps/ifp-compare-chart/ifp-compare-chart.component')) => m.IfpCompareChartComponent),
      canActivate: [(route: ActivatedRouteSnapshot, state: RouterStateSnapshot) => inject(AuthGuardService).canActivate(route, state)]
    },
    {
      path: 'glossary',
      loadComponent: () => import('src/app/scad-insights/glossery-page/glossery/glossery.component').then((m: typeof import('src/app/scad-insights/glossery-page/glossery/glossery.component')) => m.GlosseryComponent),
      canActivate: [(route: ActivatedRouteSnapshot, state: RouterStateSnapshot) => inject(AuthGuardService).canActivate(route, state)]
    },
    {
      path: 'whatif/:id',
      loadComponent: () => import('src/app/scad-insights/what-if/what-if.component').then((m: typeof import('src/app/scad-insights/what-if/what-if.component')) => m.WhatIfComponent),
      canActivate: [(route: ActivatedRouteSnapshot, state: RouterStateSnapshot) => inject(AuthGuardService).canActivate(route, state)]
    },
    {
      path: 'enabled-notification',
      loadComponent: () => import('src/app/scad-insights/ifp-enable-notification-parent/ifp-enable-notification-parent.component').then((m: typeof import('src/app/scad-insights/ifp-enable-notification-parent/ifp-enable-notification-parent.component')) => m.IfpEnableNotificationParentComponent),
      canActivate: [(route: ActivatedRouteSnapshot, state: RouterStateSnapshot) => inject(AuthGuardService).canActivate(route, state)]
    },
    {
      path: 'store',
      loadComponent: () => import('src/app/scad-insights/ifp-store-landing/ifp-store-landing.component').then((m: typeof import('src/app/scad-insights/ifp-store-landing/ifp-store-landing.component')) => m.IfpStoreLandingComponent),
      canActivate: [(route: ActivatedRouteSnapshot, state: RouterStateSnapshot) => inject(AuthGuardService).canActivate(route, state)]
    },
    // {
    //   path: 'analytics',
    //   loadComponent: () => import('src/app/ifp-analytics/ifp-analytics-landing-page/ifp-analytics-landing-page.component').then((m: typeof import('src/app/ifp-analytics/ifp-analytics-landing-page/ifp-analytics-landing-page.component')) => m.IfpAnalyticsLandingPageComponent),
    //   canActivate: [(route: ActivatedRouteSnapshot, state: RouterStateSnapshot) => inject(AuthGuardService).canActivate(route, state)]
    // },
    {
      path: 'liveability-dashboard/:id',
      loadComponent: () => import('src/app/scad-insights/analytical-apps/ifp-liveability-dashboard/ifp-liveability-dashboard.component').then((m: typeof import('src/app/scad-insights/analytical-apps/ifp-liveability-dashboard/ifp-liveability-dashboard.component')) => m.IfpLiveabilityDashboardComponent),
      canActivate: [(route: ActivatedRouteSnapshot, state: RouterStateSnapshot) => inject(AuthGuardService).canActivate(route, state)]
    },
    {
      path: 'control-panel',
      loadComponent: () => import('src/app/scad-insights/user-onboarding/control-panel/ifp-access-control/ifp-access-control.component').then((m: typeof import('src/app/scad-insights/user-onboarding/control-panel/ifp-access-control/ifp-access-control.component')) => m.IfpAccessControlComponent)
      // canActivate: [
      //   (route: ActivatedRouteSnapshot, state: RouterStateSnapshot) => inject(AuthGuardService).canActivate(route, state),
      //   () => {
      //     return inject(IsAdminService).canActivate();
      //   }
      // ]
    },
    {
      path: 'news-letters',
      loadComponent: () => import('src/app/scad-insights/ifp-news-letter-list/ifp-news-letter-list.component').then((m: typeof import('src/app/scad-insights/ifp-news-letter-list/ifp-news-letter-list.component')) => m.IfpNewsLetterListComponent),
      canActivate: [(route: ActivatedRouteSnapshot, state: RouterStateSnapshot) => inject(AuthGuardService).canActivate(route, state)]
    },
    {
      path: 'print',
      outlet: 'print',
      component: PrintLayoutComponent
    },
    {
      path: 'bayaan-dashboard',
      loadChildren: () => import('src/app/usage-dashboard/usage-dashboard.routes').then(m=> m.routesBayaanDashboard),
      canActivate: [(route: ActivatedRouteSnapshot, state: RouterStateSnapshot) => inject(AuthGuardService).canActivate(route, state)],
      resolve: [() => {
        return inject(UsageDashboardGuardService).resolve();
      }
      ]
    },
    {
      path: 'correlation/:id',
      loadComponent: () => import('src/app/scad-insights/analytical-apps/ifp-correlation/ifp-correlation.component').then((m: typeof import('src/app/scad-insights/analytical-apps/ifp-correlation/ifp-correlation.component')) => m.IfpCorrelationComponent),
      canActivate: [(route: ActivatedRouteSnapshot, state: RouterStateSnapshot) => inject(AuthGuardService).canActivate(route, state)]
    },
    {
      path: 'insight-report-generator/:id',
      loadComponent: () => import('src/app/gen-ai/ai-insight-report/ai-insight-report.component').then((m: typeof import('src/app/gen-ai/ai-insight-report/ai-insight-report.component')) => m.AiInsightReportComponent),
      canActivate: [(route: ActivatedRouteSnapshot, state: RouterStateSnapshot) => inject(AuthGuardService).canActivate(route, state)],
      resolve: [() => {
        return inject(insightGuardService).resolve();
      }
      ]
    },
    {
      path: 'consolidated-insight-report-generator/:id',
      loadComponent: () => import('src/app/gen-ai/ai-insight-report/gen-ai-consolidated/gen-ai-consolidated.component').then((m: typeof import('src/app/gen-ai/ai-insight-report/gen-ai-consolidated/gen-ai-consolidated.component')) => m.GenAiConsolidatedComponent),
      canActivate: [(route: ActivatedRouteSnapshot, state: RouterStateSnapshot) => inject(AuthGuardService).canActivate(route, state)]
    },
    {path: 'gen-ai-testing-dashboard',
      loadComponent: () => import('src/app/gen-ai-dashboard/gen-ai-dashboard-home/gen-ai-dashboard-home.component').then(m=> m.GenAiDashboardHomeComponent),
      canActivate: [(route: ActivatedRouteSnapshot, state: RouterStateSnapshot) => inject(AuthGuardService).canActivate(route, state)],
       resolve: [() => inject(TestingtoolGuardService).resolve()]
    },
    {path: 'insight-report-list',
      loadComponent: () => import('src/app/gen-ai/ai-insight-report/ai-insight-report-genarator/ai-insight-report-genarator.component').then((m: typeof import('src/app/gen-ai/ai-insight-report/ai-insight-report-genarator/ai-insight-report-genarator.component')) => m.AiInsightReportGenaratorComponent),
      canActivate: [(route: ActivatedRouteSnapshot, state: RouterStateSnapshot) => inject(AuthGuardService).canActivate(route, state)],
      resolve: [() => {
        return inject(insightGuardService).resolve();
      }
      ]
    },
    {path: 'data-governance',
      loadComponent: () => import('src/app/scad-insights/data-governance-new/data-governance-new.component').then((m: typeof import('src/app/scad-insights/data-governance-new/data-governance-new.component')) => m.DataGovernanceNewComponent),
      canActivate: [(route: ActivatedRouteSnapshot, state: RouterStateSnapshot) => inject(AuthGuardService).canActivate(route, state)]
    },
    {path: 'dxp',
      loadChildren: () => import('src/app/dxp/dxp.routing').then((m: typeof import('src/app/dxp/dxp.routing')) => m.dxpRoutes),
    },
    {path: 'power-bi/:id',
      loadComponent: () => import('src/app/scad-insights/ifp-widgets/ifp-molecules/ifp-power-bi-loader/ifp-power-bi-loader.component').then((m: typeof import('src/app/scad-insights/ifp-widgets/ifp-molecules/ifp-power-bi-loader/ifp-power-bi-loader.component')) => m.IfpPowerBiLoaderComponent),
      canActivate: [(route: ActivatedRouteSnapshot, state: RouterStateSnapshot) => inject(AuthGuardService).canActivate(route, state)]
    }
  ]
}];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class IfpPagesRoutingModule { }
