@use "../../../../assets/ifp-styles/abstracts/index" as *;

.ifp-sla {
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  background-size: cover;
  background-position: center;

  &__card {
    margin-left: $spacer-2 ;
    margin-right: $spacer-2;
    background-color: $ifp-color-white;
    border-radius: 10px;
    width: 100%;
    padding: $spacer-6;
    border: solid $ifp-color-grey-7 1px;
    overflow: hidden;
    max-height: 700px;
    max-width: 600px;
    position: relative;
    box-shadow: 0 0 30px #00000029;
  }

  &__btn-wrapper {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 50px;

  }

  &__bottom {
    display: flex;
    justify-content: center;
    align-items: flex-end;
    position: absolute;
    text-align: center;
    bottom: 0;
    width: 110%;
    height: 40vh;
    background-color: $ifp-color-white;
    border-top-left-radius: 50% 60%;
    border-top-right-radius: 50% 60%;

  }

  &__bottom-value {
    margin-bottom: 50px;
  }

  &__btn-login {
    width: 100%;
    margin: $spacer-2 $spacer-4;
  }

  &__title,
  &__sub-title {
    text-align: center;
    margin: $spacer-2 $spacer-7;
  }

  &__sub-title {

    margin-bottom: $spacer-7;
  }

  &__icon {
    width: 100%;
    max-width: 300px;
    display: block;
    margin: 0 auto $spacer-5;
  }

  &__heading {
    font-size: $ifp-fs-8;
    font-weight: $fw-bold;
    margin-bottom: $spacer-4;
    text-align: center;
  }

  &__description {
    font-size: $ifp-fs-3;
    //color: $ifp-color-white;
    margin-top: 0;
    margin-bottom: $spacer-2;
    text-align: center;

    &:last-child {
      margin-bottom: 0;
    }
  }

  &__link {
    text-decoration: none;
    color: $ifp-color-link;

    &:hover {
      text-decoration: underline;
    }
  }

  &__title {
    font-size: $ifp-fs-5;
    font-weight: $fw-bold;
    margin-bottom: $spacer-2;
    text-align: center;
    display: block;
  }

  &__duration {
    display: flex;
    align-items: center;
    justify-content: center;

    .ifp-icon {
      margin-inline-end: $spacer-3;
    }
  }

  &__time {
    font-weight: $fw-semi-bold;
  }

  &__date-time {
    padding: $spacer-3;

  }
  &__diff {
    margin: $spacer-0 $spacer-2;
    font-weight: $fw-semi-bold;
  }
}
