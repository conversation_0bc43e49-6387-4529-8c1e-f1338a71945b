import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, Input } from '@angular/core';

@Component({
  selector: 'ifp-dynamic-html',
  standalone: true,
  imports: [
    CommonModule
  ],
  templateUrl: './ifp-dynamic-html.component.html',
  styleUrl: './ifp-dynamic-html.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class IfpDynamicHtmlComponent {
  @Input() icon!:string;
  @Input() headerText!:string;
  @Input() description!:string;
}
