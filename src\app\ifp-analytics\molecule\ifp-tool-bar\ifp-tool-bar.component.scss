@use "../../../../assets/ifp-styles/abstracts/index" as *;
.ifp-tool-bar {
  background-color: $ifp-color-chart-sidebar;
  height: 100%;
  border-inline-start: 1px solid $ifp-color-grey-13;
  &__download {
    display: block;
    border-bottom: 1px solid $ifp-color-grey-13;
  }
  &__stepper {
    margin-bottom: $spacer-1;
    display: block;
  }
  &__edit {
    text-align: end;
    padding: $spacer-3 $spacer-3 $spacer-0;
    background-color: $ifp-color-section-white;
  }
  &__edit-list-text {
    color: $ifp-color-black;
    font-weight: $fw-medium;
    font-size: $ifp-fs-3;
  }
  &__edit-list {
    padding: $spacer-0 $spacer-3;
    background-color: $ifp-color-section-white;
  }
  &__edit-icon {
    cursor: pointer;
  }
  &__no-data {
    text-align: center;
    margin-top: $spacer-2;

  }
&--editor {
  .ifp-tool-bar {
    &__edit {
      color: $ifp-color-blue-menu;
    }
    &__edit-icon {
      background-color: $ifp-color-blue-1;
      border-radius: 5px;
      padding: $spacer-1;
      cursor: pointer;
    }
  }
}
}
@include mobile-tablet {
  .ifp-tool-bar {
    border-inline-start:none;
    border-radius: 0 0 20px 20px;
  }
  .ifp-chart-toolbar__action-box {
    border-radius: 0 0 20px 20px;
  }
}
