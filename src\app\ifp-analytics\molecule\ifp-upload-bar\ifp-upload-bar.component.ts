import { Component, EventEmitter, Input, Output, signal, ViewChild, WritableSignal } from '@angular/core';
import { BytesConverterPipe } from './bytes-converter.pipe';
import { IfpTooltipDirective } from 'src/app/scad-insights/core/directives/ifp-tooltip.directive';
import { IfpModalComponent } from '../../../scad-insights/ifp-widgets/ifp-organism/ifp-modal/ifp-modal.component';
import { IfpRemoveCardComponent } from '../../../scad-insights/ifp-widgets/ifp-atoms/ifp-remove-card/ifp-remove-card.component';
import { NgClass } from '@angular/common';

@Component({
    selector: 'ifp-upload-bar',
    imports: [BytesConverterPipe, IfpTooltipDirective, IfpModalComponent, IfpRemoveCardComponent, NgClass],
    templateUrl: './ifp-upload-bar.component.html',
    styleUrl: './ifp-upload-bar.component.scss'
})
export class IfpUploadBarComponent {
  @ViewChild('modal') modal!: IfpModalComponent;


 @Input() width = signal(0);
 @Input() fileName:WritableSignal<string> =  signal('');
 @Input() size?:number;
 @Input() loader = false;
 @Input() error = false;

 @Output() closeEvent =  new EventEmitter();


 cross() {
   this.modal.createElement();
 }

 closeModal(event: Event) {
   if (event) {
     this.closeEvent.emit();
   }
   this.modal.removeModal();
 }
}
