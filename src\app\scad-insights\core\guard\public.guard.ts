import { Injectable } from '@angular/core';
import {  Router } from '@angular/router';
import { IFPMsalService } from '../services/IFP-msal.service';
@Injectable({
  providedIn: 'root'
})
export class PublicService {

  constructor( private _router: Router, private _msal: IFPMsalService) {
  }

  canActivate(): boolean {
    if (this._msal.getToken === null) {
      return true;
    }
    this._router.navigate(['/home']);
    return false;
  }


}
