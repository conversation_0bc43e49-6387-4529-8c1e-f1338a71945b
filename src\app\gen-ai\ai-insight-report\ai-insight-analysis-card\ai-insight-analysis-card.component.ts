import { NgClass } from '@angular/common';
import { Component, input } from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';
import { IfpAiChartCardComponent } from "../../../scad-insights/ifp-chat-bot/ifp-ai-chart-card/ifp-ai-chart-card.component";
import { IFPHighChartsComponent } from "../../../scad-insights/ifp-widgets/charts/ifp-highcharts.component";

@Component({
  selector: 'ifp-ai-insight-analysis-card',
  imports: [TranslateModule, NgClass, IfpAiChartCardComponent, IFPHighChartsComponent],
  templateUrl: './ai-insight-analysis-card.component.html',
  styleUrl: './ai-insight-analysis-card.component.scss'
})
export class AiInsightAnalysisCardComponent {
  public title = input<string>('Reduced Consumer Spending:');
  public description = input<string>('Retail sales growth slowed from 5.2% to 2.1%, indicating lower discretionary spending.');
  public badgeSide = input(false);
  public badge = input(false);
  public statusDown = input(true);
  public shadow = input(false);
  public popup = input(false);
  public customChartData: any = input();
  public enableAnimation = input(true);
}
