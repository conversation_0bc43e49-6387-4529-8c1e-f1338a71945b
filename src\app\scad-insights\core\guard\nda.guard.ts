
import { NdaCheckService } from '../services/nda-check.service';
import { Injectable } from '@angular/core';


@Injectable({
  providedIn: 'root'
})
export class NdaGuardService {

  constructor( private _ndaService: NdaCheckService) {
    this._ndaService.ndaStatus.set(false);
  }

  resolve() {
    if (!this._ndaService.ndaStatus()){
      return this._ndaService.isNdaAccepted();
    }
    return this._ndaService.ndaStatus();
  }
}
