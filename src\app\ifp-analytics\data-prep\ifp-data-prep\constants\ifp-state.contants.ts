export const prepStatusStore = {
  initial: 'Inital',
  datasetAdd: 'Dataset Add',
  selectDataLoaded: 'selectDataLoaded',
  nodeRemoved: 'nodeRemoved',
  imputeDataLoadeded: 'imputeDataLoaded',
  cleaningDataLoaded: 'cleaningDataLoaded',
  unionAdded: 'unionAdded',
  unionAddedUpdated: 'unionUpdated',
  destination: 'destinationCreate',
  formulaAdded: 'formulaAdded',
  formulaUpdate: 'formulaUpdate'
};

export const workFlowState = {
  running: 'RUNNING',
  completed: 'COMPLETED',
  error: 'ERROR',
  force_stop: 'FORCE_STOP'
};

export const toolName = {
  source: 'source'
};

