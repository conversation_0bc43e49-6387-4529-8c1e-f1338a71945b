@use "../../../../../assets/ifp-styles/abstracts/index" as *;

.ifp-clean {
  margin-bottom: $spacer-5;

  &__header-wrapper {
    display: flex;
    justify-content: space-between;
    margin-bottom: $spacer-4;
    align-items: end;
  }

  &__title {
    font-size: $ifp-fs-6;
    font-weight: $fw-semi-bold;
    margin-bottom: $spacer-2;
  }

  &__sub-title {
    font-size: $ifp-fs-4;
    font-weight: $fw-medium;
    color: $ifp-color-grey-2;
  }

  &__tabs {
    display: flex;
    padding: $spacer-5;
    border-radius: 10px;
    background-color: $ifp-color-section-white;
  }

  &__item {
    width: 50%;
    position: relative;

    &:first-child {
      padding-inline-end: $spacer-4;
      margin-inline-end: $spacer-4;
      border-inline-end: 1px solid $ifp-color-grey-3;
    }
  }

  &__flex {
    display: flex;
    padding: $spacer-2 $spacer-3;
    background-color: $ifp-color-grey-4;
    border-radius: 10px;
    margin-bottom: $spacer-2;
  }

  &__info-box {
    min-width: 360px;
    background-color: $ifp-color-black-50;
    color: $ifp-color-white;
    backdrop-filter: blur(30px);
    -webkit-backdrop-filter: blur(30px);
    border-radius: 10px;
    padding: $spacer-3;
    position: absolute;
    top: 100%;
    right: 0;
    z-index: 3;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s;
  }

  &__info-text {
    display: flex;
    font-weight: $fw-semi-bold;
    margin-bottom: $spacer-2;
  }

  &__info-title {
    font-weight: $fw-regular;
    min-width: 90px;
    margin-inline-end: $spacer-2;
  }

  &__info {
    position: relative;
    overflow: hidden;
    min-width: 16px;
    margin-inline-start: auto;

    &>.ifp-icon {
      font-size: $ifp-fs-4;
      transition: 0.3s;
      margin-inline-end: $spacer-0;
    }

    &:hover {
      overflow: visible;

      &>.ifp-icon {
        color: $ifp-color-blue-hover;
      }

      .ifp-clean {
        &__info-box {
          opacity: 1;
          visibility: visible;
        }
      }
    }
  }

  &__option-box {
    &--disable {
      pointer-events: none;
      opacity: .5;
    }
  }

  &__label {
    font-size: $ifp-fs-4;
    font-weight: $fw-medium;
  }

  &__sub-label {
    font-size: $ifp-fs-3;
    font-style: italic;
  }

  &__arrow {
    position: absolute;
    top: 50%;
    right: 0;
    transform: translate(50%, -50%);
    background-color: $ifp-color-section-white;
    padding: $spacer-3 $spacer-0;
  }
}

:host::ng-deep {
  .ifp-option-box {
    &::-webkit-scrollbar {
      width: $spacer-2 !important;
    }

    &::-webkit-scrollbar-track {
      background-color: $ifp-color-grey-11 !important;
    }

    &__opt-wrapper {
      padding: $spacer-2 $spacer-3 !important;
    }
  }
}

:host-context(.ifp-dark-theme) {
  .ifp-clean{
    &__info-box {
      background-color: $ifp-color-secondary-grey;
    }
  }
}

:host-context([dir="rtl"]) {
  .ifp-clean {
    &__arrow {
      left: 0;
      right: auto;
    }
    &__info-box {
      left: 0;
      right: auto;
    }
  }
}
