<div class="ifp-container"><app-ifp-breadcrumbs [pageData]="pageData"></app-ifp-breadcrumbs></div>
<div class="ifp-insight-discovery">
  <div class="ifp-container">
    <div class="ifp-insight-discovery__logo-grp">
      <h2 class="ifp-node__title">{{chartTitle | quotRemove}}</h2>
      @if (chartTitle == chartConstants.VISA_TITLE) {
      <img class="ifp-insight-discovery__logo"
        src="{{logoPath}}">
      }

    </div>
    <p class="ifp-node__subtitle" *ngIf="response?.component_subtitle">{{response?.component_subtitle | quotRemove }}
    </p>
    <h3 class="ifp-insight-discovery__title"
      *ngIf="response?.ifp_indicators?.length > 0 && response?.ifp_indicators[0] !== ''">
      {{classifications.ExperimentalStatistics|
      translate}}</h3>
    <ifp-carousel *ngIf="response?.ifp_indicators?.length > 0 && response?.ifp_indicators[0] !==''"
      (slideEvent)="slideClicked()" [viewButton]="false" [label]="''" [customOption]="customOptions"
      [class]="carousalClass.sixCol" #leftContainer>
      <ng-template *ngFor="let item of response.ifp_indicators; let index =index" ifp-carouselItem>
        <ifp-whats-new-card *ngIf="item && item !==''" [title]="'Experimental Statistics' | translate" #leftCard
          [small]="!activeLeftSlider" [id]="(+item).toString()" [contentType]="'statistics-insights'"
          (resized)="resizedLeft($event,index)" [index]="index+1"
          [ngClass]="{'ifp-whats-new__display-none': index+1 !== activeLeft && activeLeft}"
          class="ifp-indicators__card"></ifp-whats-new-card>
      </ng-template>
    </ifp-carousel>
    <app-ifp-insight-discovery-card [title]="visualizationData?.component_title" #cardComponent
      [filterPanel]="visualaizationfilterPanel" [periodFilter]="visualaizationFilters" [getChartData]="chartData"
      [chartValues]="visualaizationValues" [description]="response?.narrative" [chartDropDownData]="allCharts"
      (chartChanged)="changeChartData($event)" [appType]="appType" [indicatorId]="id" [isRender]="isRender"
      [isLoader]="loader" [tableLabels]="tableData"
      [publishDate]="response?.publication_date ? response.publication_date : response?.updated"
      [source]="visualizationData?.data_source && visualizationData?.data_source !== '' ? visualizationData?.data_source : dataSource"
      [domain]="response?.domain" [disclaimerDetails]="disclaimerDetails" (filterChanged)="changeFilterData($event)" [isFilterApplied]="isFilterSelected"
      [allChartsData]="response" [hideSuffix]="hideSuffix" [security]="security" [hideChartTypes]="hideChartTypes" [subNodeId]="visulaizationId"></app-ifp-insight-discovery-card>


    <ifp-carousel *ngIf="relatedInsightDiscovery?.length > 0" (slideEvent)="slideClicked()" [viewButton]="false"
      [label]="'Related Insights Discovery'" [customOption]="customOptions" [class]="carousalClass.dynamic"
      #leftContainer>
      <ng-template *ngFor="let item of relatedInsightDiscovery; let index =index" ifp-carouselItem>
        <ifp-analysis-card [addMyApps]="false" [firstLoader]="true" [id]="item" [contentType]="'analytical-apps'"
          [delay]="300" [isRedirectTop]="true" (resizedEvent)="resizedEvent($event, index)"></ifp-analysis-card>
      </ng-template>
    </ifp-carousel>
  </div>
</div>
