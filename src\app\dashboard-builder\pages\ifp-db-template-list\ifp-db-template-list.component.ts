import { ChangeDetectionStrategy, Component, output } from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';
import { IfpButtonComponent } from "../../../scad-insights/ifp-widgets/ifp-atoms/ifp-button/ifp-button.component";
import { buttonClass } from 'src/app/scad-insights/core/constants/button.constants';
import { NgClass } from '@angular/common';

@Component({
  selector: 'ifp-db-template-list',
  imports: [TranslateModule, IfpButtonComponent, NgClass],
  templateUrl: './ifp-db-template-list.component.html',
  styleUrl: './ifp-db-template-list.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class IfpDbTemplateListComponent {
  public closeModal = output();
  public buttonClass = buttonClass;
  public selectedTemplateIndex: number = -1;

  apply() {
    this.onCloseModal();
  }


  onCloseModal() {
    this.closeModal.emit();
  }
}
