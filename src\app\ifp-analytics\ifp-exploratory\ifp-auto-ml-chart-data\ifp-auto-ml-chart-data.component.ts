import { Component, Input, OnChanges, ViewChild, ElementRef } from '@angular/core';
import { IfpGaugeChartComponent } from '../../../scad-insights/ifp-widgets/ifp-molecules/ifp-gauge-chart/ifp-gauge-chart.component';
import { AsyncPipe, DecimalPipe, NgClass } from '@angular/common';
import { TranslateModule } from '@ngx-translate/core';
import { IfpInfoComponent } from '../../../scad-insights/ifp-widgets/ifp-molecules/ifp-heading-with-info/ifp-info.component';
import { ThemeService } from 'src/app/scad-insights/core/services/theme/theme.service';

@Component({
    selector: 'ifp-auto-ml-chart-data',
    imports: [IfpGaugeChartComponent, NgClass, DecimalPipe, TranslateModule, IfpInfoComponent, AsyncPipe],
    templateUrl: './ifp-auto-ml-chart-data.component.html',
    styleUrl: './ifp-auto-ml-chart-data.component.scss'
})
export class IfpAutoMlChartDataComponent implements OnChanges{
  @ViewChild('chart') chartData!: ElementRef;
  @Input() chartValueName: string = '';
  @Input({required: true}) chartHeading: string = 'Accuracy';
  @Input({required: true})  chartValue: number = 0;
  @Input() removeBorderAndBackground = false;
  @Input() topLabel!: string;
  @Input() selection = false;
  @Input() chartListValue = false;
  @Input() chartList: { name: string;
    value:  number | string;  string?: boolean;} [] = [];

  @Input() centerValue!: string;
  @Input() centerName!: string;
  @Input() number!:number;
  public chartColor = '';

  constructor(public _themeService : ThemeService) {}

  ngOnChanges(): void {
    // setting color and value to the gauge chart in auto ml

    if (this.chartValue*100 < 40) {
      this.chartColor = '#BA0202';
      this.chartValueName = 'Low';
    } else if (this.chartValue*100  < 80) {
      this.chartColor = '#F5B85D';
      this.chartValueName = 'Medium';
    } else {
      this.chartColor = '#5DB14E';
      this.chartValueName = 'High';
    }

  }

  get getElement() {
    return this.chartData;
  }
}
