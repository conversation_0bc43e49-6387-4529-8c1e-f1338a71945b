<div class="ifp-legend-props">
  <!-- <ifp-db-chart-props [propName]="'Legend X'" class="ifp-legend-props__props-item">
    <div class="ifp-chart-props__type">
      <input type="color">
    </div>
  </ifp-db-chart-props>
  <ifp-db-chart-props [propName]="'Legend X'" class="ifp-legend-props__props-item">
    <div class="ifp-chart-props__type">
      <input type="color">
    </div>
  </ifp-db-chart-props> -->
  <ifp-db-chart-props [propName]="'Align'" class="ifp-legend-props__props-item">
    <div class="ifp-chart-props__type">
      <app-ifp-db-dropdown [isMultiSelect]="false" [options]="alignOptions" (singleSelected)="getLegendPosition($event)"
        [selectedSingleItem]="selectedAlign" [defaultSelect]="false" [key]="'name'"></app-ifp-db-dropdown>
    </div>
  </ifp-db-chart-props>

  <ifp-db-chart-props [propName]="'Enable'" class="ifp-legend-props__props-item">
    <div class="ifp-chart-props__type">
      <app-ifp-toggle-button (toggleChange)="changeLegendValue($event)" [enable]="legendEnable"></app-ifp-toggle-button>
    </div>
  </ifp-db-chart-props>
  <!-- <ifp-db-chart-props [propName]="'x'" class="ifp-legend-props__props-item">
    <div class="ifp-chart-props__type">
      <ifp-input-counter [value]="0"></ifp-input-counter>
    </div>
  </ifp-db-chart-props>
  <ifp-db-chart-props [propName]="'x'" class="ifp-legend-props__props-item">
    <div class="ifp-chart-props__type">
      <ifp-input-counter [value]="0"></ifp-input-counter>
    </div>
  </ifp-db-chart-props> -->

</div>
