@use "../../../assets/ifp-styles/abstracts/index" as *;

:host {
  display: block;
  padding: $spacer-3;
  border: 1px solid $ifp-color-pale-grey;
  border-radius: 10px;
  background-color: $ifp-color-white;
}

.ifp-kpi-card {
  &__input {
    display: block;
    color: $ifp-color-black;
    width: 100%;
    padding: $spacer-2 ($spacer-2 + 2px);
    background-color: transparent;
    border: 1px solid $ifp-color-blue-med;
    border-radius: 5px;
    box-shadow: 0 0 0 2px $ifp-color-blue-1;
    &--textarea {
      min-height: 37px;
      resize: none;
    }
  }
  &__icon {
    margin-inline-end: $spacer-3;
  }
  &__head-sec {
    font-size: $ifp-fs-4;
    font-weight: $fw-semi-bold;
    display: flex;
    align-items: center;
    margin-bottom: $spacer-3;
    .ifp-kpi-card {
      &__icon,
      &__input {
        font-size: inherit;
      }
      &__input {
        font-weight: inherit;
        padding: $spacer-1 ($spacer-2 + 2px);
      }
    }
  }
  &__filter-wrapper {
    display: flex;
    flex-wrap: wrap;
    margin: $spacer-0 (-$spacer-2) $spacer-3;
  }
  &__filter {
    display: block;
    margin: $spacer-0 $spacer-2;
  }
  &__body {
    .ifp-kpi-card__input {
      font-size: $ifp-fs-3;
      margin-bottom: $spacer-4;
    }
  }
  &__legends {
    margin-bottom: $spacer-3;
    
    &-label {
      display: block;
      font-size: $ifp-fs-3;
      font-weight: $fw-semi-bold;
      color: $ifp-color-black;
      margin-bottom: $spacer-2;
    }
    
    &-dropdown {
      width: 100%;
    }
  }
  &--non-editable {
    .ifp-kpi-card__input {
      border: 0px;
      box-shadow: none;
    }
  }
}
