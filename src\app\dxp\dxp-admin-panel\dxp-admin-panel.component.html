<ifp-bg-page>
  <div class="ifp-page-bg__content ifp-dxp-admin" [ngClass]="{'ifp-dxp-admin--collapse': hideSideBar}">
    <div class="ifp-container">
      <app-ifp-breadcrumbs [pageData]="pageData"></app-ifp-breadcrumbs>
      <div class="ifp-node ifp-node--dxp-admin">
        <div class="ifp-node__card-left">
          <h1 class="ifp-node__tiltle">{{title}}</h1>
          <p class="ifp-node__subtitle">{{description}}</p>

          <div class="ifp-node__filters">
            @for (filter of filters; track i; let i = $index) {
              <app-ifp-dropdown class="ifp-node__filter-item" [dropDownItems]="filter.filterOptions" [filterLabel]="filter.filterLabel" [singleDefaultSelect]="true" [selectedValue]="filter.defaultValue" [key]="'value'"></app-ifp-dropdown>
            }
          </div>

          <ifp-highcharts class="ifp-dxp-admin__chart" [height]="400" [category]="chartData.category" [data]="chartData.series" ></ifp-highcharts>

          <div class="ifp-node__txt-icon">
            <ifp-icon-text class="ifp-node__source" [icon]="'ifp-icon-text'"[text]="'Environmental data'" [key]="'Product'"></ifp-icon-text>
            <ifp-icon-text class="ifp-node__source" [icon]="'ifp-icon-text'"[text]="'Economy and Investment'" [key]="'Asset'"></ifp-icon-text>
          </div>

          <div class="ifp-dxp-admin__access-wrapper">
            <div class="ifp-dxp-admin__user-detail">
              <p class="ifp-dxp-admin__user-detail-txt"><em class="ifp-icon ifp-icon-user-normal"></em></p>
              <ifp-user-tag [name]="'Ahmad Nadheem'" class="ifp-dxp-admin__user-detail-tag"></ifp-user-tag>
            </div>

            <div class="ifp-dxp-admin__user-detail">
              <p class="ifp-dxp-admin__user-detail-txt"><em class="ifp-icon ifp-icon-multiple-user"></em>{{'Shared with'}}: </p>
              <ifp-user-tag-group [tagList]="sharedUserList" [limit]="2" class="ifp-dxp-admin__user-group"></ifp-user-tag-group>
            </div>
          </div>

          <div class="ifp-dxp-admin__footer">
            <ifp-button [label]="'Revert'" [iconClass]="'ifp-icon-refresh'" [buttonClass]="buttonClass.secondary" class="ifp-dxp-admin__btn" (ifpClick)="hideSideBar = !hideSideBar"></ifp-button>
            <ifp-button [label]="'Reject'" [iconClass]="'ifp-icon-round-cross'" [buttonClass]="buttonClass.secondary" class="ifp-dxp-admin__btn"></ifp-button>
            <ifp-button [label]="'Approve'" [iconClass]="'ifp-icon-tick-round'" [buttonClass]="buttonClass.primary" class="ifp-dxp-admin__btn"></ifp-button>
          </div>

        </div>
        <div class="ifp-node__card-right">
          <div class="ifp-chart-toolbar__action-box">
            <span class="ifp-dxp-admin__revert-inline" (click)="hideSideBar = true"><em class="ifp-icon ifp-icon-left-arrow"></em>{{'Revert' | translate}}</span>
            <p class="ifp-chart-toolbar__right-desc ifp-dxp-admin__revert-desc">{{'Would you like to return this visualization to the user? You can include a comment or attach a file before sending it.' | translate}}</p>
          </div>
          @if (comments.length) {
          <div class="ifp-chart-toolbar__action-box ifp-dxp-admin__comment-wrapper">
            @for (comment of comments; track $index) {
              <div class="ifp-dxp-admin__comment-item" [ngClass]="{'ifp-dxp-admin__comment-item--self': comment.email === currentUserEmail}">
                <div class="ifp-dxp-admin__comment-name">
                  <ifp-abbreviation-tag [text]="comment.username" class="ifp-dxp-admin__name-tag"></ifp-abbreviation-tag>
                  <p class="ifp-dxp-admin__comment-desc">{{comment.username}} @if(comment.email === currentUserEmail) {
                    {{'Submitted for' | translate}} <strong>{{'Approval' | translate}}</strong>
                  } @else {
                    <strong>{{'Reverted' | translate}}</strong> {{'the KPI' | translate}}
                  }</p>
                </div>
                <p class="ifp-dxp-admin__comment">{{comment.comment}}</p>
                <p class="ifp-dxp-admin__comment-time">{{comment.date | date: dateFormat.mediumDate}}</p>
              </div>
            }
          </div>
          }
          <div class="ifp-chart-toolbar__action-box">
            <p class="ifp-dxp-admin__textarea-label">{{'Add comments' | translate}}</p>
            <textarea [placeholder]="'Leave a comment' | translate" appIfpInputAutoResize class="ifp-input-textarea ifp-dxp-admin__textarea" [(ngModel)]="newComment"></textarea>
            <div class="ifp-dxp-admin__btn-sec">
              <ifp-button [label]="'Cancel'" [buttonClass]="newComment.trim() === '' ? buttonClass.disabled : buttonClass.secondary" class="ifp-dxp-admin__btn" (ifpClick)="newComment = ''"></ifp-button>
              <ifp-button [label]="'Submit'" [buttonClass]="newComment.trim() === '' ? buttonClass.disabled : buttonClass.primary" class="ifp-dxp-admin__btn" (ifpClick)="addComment()"></ifp-button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</ifp-bg-page>
