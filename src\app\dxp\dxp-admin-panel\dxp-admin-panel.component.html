<ifp-bg-page>
  @if (loader()) {
  <div class=" ifp-page-bg__content  ifp-dxp-admin__loader">
    <app-ifp-spinner></app-ifp-spinner>
  </div>
  } @else {
  <div class="ifp-page-bg__content ifp-dxp-admin" [ngClass]="{'ifp-dxp-admin--collapse': hideSideBar()}">
    <div class="ifp-container">
      <app-ifp-breadcrumbs [pageData]="pageData"></app-ifp-breadcrumbs>
      <div class="ifp-node ifp-node--dxp-admin">
        <div class="ifp-node__card-left">
          <h1 class="ifp-node__tiltle">{{data()?.title}}</h1>
          <p class="ifp-node__subtitle">{{data()?.subTitle}}</p>

          <div class="ifp-node__filters">
            @for (filter of data()?.filterPanel; track i; let i = $index) {
            <app-ifp-dropdown [formDisable]="true" [searchEnable]="true" [isMulti]="true" [showTitle]="true"
              class="ifp-node__filter-item" [title]="filter.label" [dropDownItems]="filter.options"
              [selectedValues]="[filter.default]"
              (dropDownItemMultiClicked)="changeFilter($event,filter)"></app-ifp-dropdown>
            }
              @for (filter of data()?.legendPanel; track i; let i = $index) {

            }
               <app-ifp-dropdown (dropDownItemClicked)="legendChanges($event)" [formDisable]="true" [key]="'column'" [searchEnable]="true" [isMulti]="false" [showTitle]="true" class="ifp-node__filter-item" [title]="'Legends'" [dropDownItems]="data()?.legendPanel ?? []" [selectedValue]="defaultLegend()" ></app-ifp-dropdown>
              <app-ifp-dropdown [formDisable]="true" [searchEnable]="true" [isMulti]="true" [showTitle]="true"
              class="ifp-node__filter-item" [title]="defaultLegend()?.column ?? ''" [dropDownItems]="defaultLegend()?.options ?? []"
              [selectedValues]="defaultLegend()?.selected_values ?? []"
              (dropDownItemMultiClicked)="changeSubFilter($event)"></app-ifp-dropdown>
          </div>
          @if(xAxis() && data()?.series?.xAxis?.categories?.length && data()?.series?.series?.length ) {
            @if(chartLoader()) {
              <app-ifp-card-loader class="ifp-dxp-admin__loader-chart" [type]="'chart'"></app-ifp-card-loader>
            } @else {
          <ifp-highcharts [legends]="legends" [comparisonEnable]="false" [xAxis]="xAxis()" class="ifp-dxp-admin__chart" [chartType]="chartType[data()?.visualizationConfig?.chart_configuration?.default_chart_type ?? '' ]"
            [height]="400" [category]="data()?.series?.xAxis?.categories ?? []" [data]="data()?.series?.series ?? []"
           [chartName]="'lineChart'"></ifp-highcharts>
            }

          } @else {
          <app-ifp-no-data [isTransparent]="true"></app-ifp-no-data>
          }

          <div class="ifp-node__txt-icon">
            <ifp-icon-text class="ifp-node__source" [icon]="'ifp-icon-text'" [text]="data()?.asset?.displayName ?? ''"
              [key]="'Product'"></ifp-icon-text>
            <ifp-icon-text class="ifp-node__source" [icon]="'ifp-icon-text'" [text]="data()?.product?.displayName ?? ''"
              [key]="'Asset'"></ifp-icon-text>
          </div>

          <div class="ifp-dxp-admin__access-wrapper">
            <div class="ifp-dxp-admin__user-detail">
              <p class="ifp-dxp-admin__user-detail-txt"><em class="ifp-icon ifp-icon-user-normal"></em></p>
              <ifp-user-tag [name]="data()?.createdBy?.name ?? ''"
                class="ifp-dxp-admin__user-detail-tag"></ifp-user-tag>
            </div>

            <div class="ifp-dxp-admin__user-detail">
              <p class="ifp-dxp-admin__user-detail-txt"><em class="ifp-icon ifp-icon-multiple-user"></em>{{'Shared
                with'}}: </p>
              <ifp-user-tag-group [moreText]="'Others'" [tagList]="data()?.userAccess ?? []" [limit]="2"
                class="ifp-dxp-admin__user-group"></ifp-user-tag-group>
            </div>
          </div>

   @if(hideSideBar() && generalizedRoles.approver === data()?.user?.generalizedRole && data()?.approvalRequest
            && data()?.approvalRequest?.status === approveStatus.pending) {
          <div class="ifp-dxp-admin__footer">

            <div class="ifp-dxp-admin__footer-toggle">
              {{'Click to assign or unassign this KPI for review'}} <app-ifp-toggle-button
                (toggleChange)="enableReview.set($event);currentStatusUpdate(enableReview()? approveConst.claim: approveConst.unclaim);"
                [enable]="enableReview()" class="ifp-dxp-admin__footer-toggle-btn"></app-ifp-toggle-button>
            </div>
            <div class="ifp-dxp-admin__footer-btn">
              <ifp-button [label]="'Revert'" [iconClass]="'ifp-icon-refresh'"
                [buttonClass]="buttonClass.secondary +' '+(enableReview() ? '' : buttonClass.disabledSecondary)"
                class="ifp-dxp-admin__btn"
                (ifpClick)="hideSideBar.set(!hideSideBar());currentStatus.set(approveConst.revert)"></ifp-button>
              <ifp-button [label]="'Reject'" [iconClass]="'ifp-icon-round-cross'"
                [buttonClass]="buttonClass.secondary+' '+(enableReview() ? '' : buttonClass.disabledSecondary)"
                class="ifp-dxp-admin__btn"
                (ifpClick)="hideSideBar.set(!hideSideBar());currentStatus.set(approveConst.reject)"></ifp-button>
              <ifp-button [label]="'Approve'" [iconClass]="'ifp-icon-tick-round'"
                [buttonClass]="buttonClass.primary+' '+(enableReview() ? '' : buttonClass.disabled)"
                class="ifp-dxp-admin__btn" (ifpClick)="currentStatusUpdate(approveConst.approve);"></ifp-button>
            </div>

          </div>
       }


        </div>
        <div class="ifp-node__card-right">
          @if ( generalizedRoles.explorer  !== data()?.user?.generalizedRole) {
          <div class="ifp-chart-toolbar__action-box ifp-dxp-admin__tool-bar-header">
            @if(enableChat()) {
            <span class="ifp-dxp-admin__tool-bar-opener" (click)="hideSideBar.set(!hideSideBar())"> <em
                class="ifp-icon ifp-icon-new-chat ifp-dxp-admin__tool-bar-opener-icon"></em></span>
            }

            <span class="ifp-dxp-admin__revert-inline">{{'Leave a comment' | translate}}</span>
          </div>
          @if (comments().length) {
          <div class="ifp-chart-toolbar__action-box ifp-dxp-admin__comment-wrapper" [ngClass]="{'ifp-dxp-admin__comment-wrapper--approved': data()?.approvalRequest?.status !== approveStatus.pending}">
            @for (comment of comments(); track comment; let index = $index; let last = $last) {
            <div class="ifp-dxp-admin__comment-item"
              [ngClass]="{'ifp-dxp-admin__comment-item--self':comment?.userDetails?.email == currentUserEmail}">
              @if(comment.commentType === 'system') {
              <div class="ifp-dxp-admin__comment-name">
                <ifp-abbreviation-tag [text]="comment?.userDetails?.name?? ''"
                  class="ifp-dxp-admin__name-tag"></ifp-abbreviation-tag>
                <p class="ifp-dxp-admin__comment-desc">
                  <ifp-mark-down [data]="(comment?.userDetails?.name ?? '') +' '+comment.content"></ifp-mark-down>
                </p>
              </div>
              <p class="ifp-dxp-admin__comment-time">{{comment.createdAt | date: dateFormat.dateAndTime}}</p>
              } @else {
              <div class="ifp-dxp-admin__comment-name">
                @if(index !== 0 && comment?.userDetails?.email !== comments()[index-1].userDetails?.email) {

                } @else if(comment?.userDetails?.email !== currentUserEmail){
                <ifp-abbreviation-tag [text]="comment?.userDetails?.name?? ''"
                  class="ifp-dxp-admin__name-tag"></ifp-abbreviation-tag>
                <p class="ifp-dxp-admin__comment-desc">{{comment?.userDetails?.name }}</p>
                }

              </div>
              <p class="ifp-dxp-admin__comment">{{comment.content}}</p>
              <p class="ifp-dxp-admin__comment-time">{{comment.createdAt | date: dateFormat.mediumDate}}</p>
              }

            </div>
            }
          </div>
          }
       @if( data()?.approvalRequest?.status === approveStatus.pending) {
          <div class="ifp-chart-toolbar__action-box ifp-dxp-admin__comment-edit">

            <p class="ifp-dxp-admin__textarea-label">{{'Add comments' | translate}}</p>
            <textarea [placeholder]="'Leave a comment' | translate" appIfpInputAutoResize
              class="ifp-input-textarea ifp-dxp-admin__textarea" [(ngModel)]="newComment"></textarea>
            <div class="ifp-dxp-admin__btn-sec">
              <ifp-button [label]="'Cancel'"
                [buttonClass]="newComment.trim() === '' ? buttonClass.disabled : buttonClass.secondary"
                class="ifp-dxp-admin__btn" (ifpClick)="newComment = '';hideSideBar.set(true)"></ifp-button>
              <ifp-button [label]="'Submit'"
                [buttonClass]="newComment.trim() === '' ? buttonClass.disabled : buttonClass.primary"
                class="ifp-dxp-admin__btn" (ifpClick)="addComment()"></ifp-button>
            </div>

          </div>
            }
          }
        </div>
      </div>
    </div>
  </div>
  }

</ifp-bg-page>
<app-ifp-modal #modalUpdate >
  <ifp-dxp-validation-pop-up [successPopup]="true" ></ifp-dxp-validation-pop-up>
</app-ifp-modal>

