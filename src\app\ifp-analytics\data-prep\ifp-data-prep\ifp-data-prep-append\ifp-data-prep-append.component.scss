@use "../../../../../assets/ifp-styles/abstracts/index" as *;

%flex-vertical {
  display: flex;
  align-items: center;
}

.ifp-prep-append {
  padding-bottom: $spacer-5;
  .ifp-module-heading {
    margin: $spacer-3 $spacer-0 $spacer-5;
    text-align: center;
  }
  &__center {
    @extend %flex-vertical;
    justify-content: center;
    text-align: center;
    min-height: 300px;
    margin-top: $spacer-3;
    &:only-child {
      margin-top: $spacer-0;
    }
  }
  &__wrapper {
    background-color: $ifp-color-section-white;
    border-radius: 10px;
    padding: $spacer-4;
  }
  &__cta {
    width: 100%;
    max-width: 400px;
    border: 1px solid $ifp-color-grey-3;
    border-radius: 20px;
    text-align: center;
    padding: $spacer-4;
    margin: $spacer-4;
    transition: 0.3s;
    cursor: pointer;
    &:hover {
      box-shadow: 0 0 16px $ifp-color-black-16;
      transform: scale(1.05);
    }
  }
  &__box-icon {
    font-size: 4.5rem;
  }
  &__box-title,
  &__upload-title {
    font-weight: $fw-bold;
  }
  &__box-title {
    font-size: $ifp-fs-7;
    margin: $spacer-1 $spacer-0 $spacer-2;
  }
  &__upload-title {
    font-size: $ifp-fs-8;
    margin-bottom: $spacer-2;
  }
  &__box-desc,
  &__upload-desc {
    color: $ifp-color-tertiary-text;
    font-weight: $fw-medium;
  }
  &__box-desc {
    font-size: $ifp-fs-4;
    line-height: 1.4;
    min-height: 70px;
  }
  &__upload-desc {
    font-size: $ifp-fs-5;
    margin-bottom: $spacer-6;
  }
  &__upload {
    text-align: center;
  }
  &__upload-inner {
    width: 100%;
    max-width: 650px;
  }
  &__complete-top {
    display: flex;
    justify-content: space-between;
  }
  &__detail-top {
    display: flex;
    justify-content: space-between;
    margin-bottom: $spacer-3;
  }
  &__table {
    margin-bottom: $spacer-5;
  }
  &__table-row {
    @extend %flex-vertical;
    padding: $spacer-2 $spacer-2;
    border-bottom: 1px solid $ifp-color-grey-3;
    &--head {
      background-color: $ifp-color-grey-bg-2;
      border-radius: 10px;
      font-weight: $fw-semi-bold;
      border: none;
    }
  }
  &__table-col {
    @extend %flex-vertical;
    width: 100%;
  }
  &__table-item {
    width: 50%;
    padding: $spacer-2 $spacer-3;
  }
  &__table-divider {
    min-width: 90px;
    display: flex;
    justify-content: center;
  }
  &__table-line {
    width: 1px;
    height: 25px;
    display: inline-block;
    background-color: $ifp-color-tertiary-text;
  }
  &__btn-wrapper{
    margin-top: $spacer-2;
    display: flex;

  }
}
