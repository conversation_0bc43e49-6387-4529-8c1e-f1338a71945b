@use "../../../../assets/ifp-styles/abstracts/index" as *;

.ifp-prep-popup {
  width: 100%;
  max-width: 800px;
  text-align: center;
  margin-bottom: $spacer-4;

  &__content {
    background-color: $ifp-color-section-white;
    border-radius: 10px;
  }

  &__content-header {
    position: relative;
    height: 50px;
    min-height: 50px;
  }

  &__content-body {
    max-height: calc(100vh - 90px);
    @include ifp-scroll-y(transparent, $ifp-color-grey-1, 5px, 10px);
  }

  &__header-icon {
    position: absolute;
    color: $ifp-color-grey-6 ;
    right: 20px;
    top: 20px;
    cursor: pointer;
  }

  &__icon {
    height: 58px;
    background-color: $ifp-color-green-dark-1;
    border-radius: 50%;
    align-items: center;
    text-align: center;
    display: flex;
    margin: 0 auto;
    width: 58px;
    margin-bottom: $spacer-3;
    justify-content: center;

    .ifp-icon {
      font-size: $ifp-fs-13;
      color: $ifp-color-white;
    }
  }

  &__sub-body {
    padding: $spacer-4;
    min-height: 250px;
    background-color: $ifp-color-pale-grey;
    margin: $spacer-3;
    border-radius: 7px;
  }

  &__desc {
    font-size: $ifp-fs-5;
    margin-bottom: $spacer-5;
    color: $ifp-color-grey-14;
    padding: $spacer-3;
  }

  &__heading {
    font-size: $ifp-fs-10;
    font-weight: $fw-bold;
    margin-bottom: $spacer-2;
  }

  &__filename {
    font-size: $ifp-fs-5;
    font-weight: $fw-medium;
    color: $ifp-color-black;
  }

  &__sub-title {
    font-size: $ifp-fs-4;
    font-weight: $fw-bold;
    margin-bottom: $spacer-2;

    &--bottom {
      margin-bottom: $spacer-0;
      margin-top: $spacer-3;
    }
  }

  &__sub-desc {
    font-size: $ifp-fs-3;
    color: $ifp-color-grey-14;
  }

  &__footer {
    min-height: 1px;
  }

  &__btn-wrapper-export {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: $spacer-4;
  }

  &__btn-export {
    max-width: 250px;
  }

  &__btn-summery {
    margin: $spacer-3 $spacer-0 $spacer-2 $spacer-0 ;
    display: block;
  }

  &__img-warpper-summery {
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  &__img {
    margin-top: $spacer-4;
    max-width: 400px;
  }

  &__tool-card {
    width: calc(50% - $spacer-5);
    display: block;
    margin: $spacer-3;
  }

  &__btn-wrapper-common {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
  }

  &__btn-divi {
    margin-top: $spacer-2;
    padding-left: $spacer-6;
    padding-right: $spacer-6;
  }

  &__or {
    width: 100%;

    &::before {
      z-index: 0;
      position: absolute;
      content: "";
      width: 100%;
      top: 50%;
      left: 0;
      border-left: none;
      border-top: 1px solid var(--ifp-color-grey-w);
    }
  }

  &__or-line {
    background-color: var(--ifp-color-section-white);
    position: relative;
    z-index: 4;
    padding: 0 16px;
    color: var(--ifp-color-grey-14);
  }
}

.ifp-adv-tool {
  &__divider {
    margin: $spacer-3;
  }

  &__divider-text {
    background-color: $ifp-color-pale-grey;
  }

}

:host::ng-deep {
  .ifp-prep-popup__btn-wrapper-export {
    .ifp-btn {
      width: 100%;
    }
  }
}

:host-context(.ifp-dark-theme) {
  .ifp-prep-popup {
    &__sub-desc {
      color: $ifp-color-black
    }
  }
}
