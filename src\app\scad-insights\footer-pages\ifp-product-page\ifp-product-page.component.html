<div class="ifp-container">
  <app-ifp-breadcrumbs [pageData]="pageData"></app-ifp-breadcrumbs>
</div>
@if (productsData$ | async; as product) {
  <div class="ifp-products">
    <div class="ifp-container">
      <h1 class="ifp-products__heading ifp-products__heading--main">{{product.title | translate}}</h1>
      <p class="ifp-products__desc-short">{{product.description | translate}}</p>
      <div class="ifp-products__domain-outer">
        <h2 class="ifp-products__heading">{{product.domain.title| translate}}</h2>
        <div class="ifp-products__domain-wrapper" *ngIf="product.domain.items.length > 0">
          <div class="ifp-products__domain" *ngFor="let domain of product.domain.items">
            <ifp-img class="ifp-products__domain-icon" [darkIcon]="domain.page_menu_light_icon" [lightIcon]="domain.page_menu_icon" [width]="20"></ifp-img>
            <p class="ifp-products__domain-name">{{domain.title | translate}}</p>
          </div>
        </div>
      </div>

      <div class="ifp-products__carousel">

          <!-- <swiper-container init="false">
            <swiper-slide>
            </swiper-slide>
          </swiper-container> -->

          <div class="ifp-products__carousel-sec">
            <div class="ifp-products__sec-1">
              <!-- <ifp-img class="ifp-products__carousel-img" [darkIcon]="product.analytical_apps.items[active].image_dark_theme" [lightIcon]="product.analytical_apps.items[active].image_light_theme"></ifp-img> -->
              @if((_themeService.defaultTheme$ | async) === 'dark') {
                <object type="image/svg+xml" [data]="product.analytical_apps.items[active].image_light_theme | safe" class="ifp-journey__tab-img">
                  <img [src]="product.analytical_apps.items[active].image_light_theme" alt="icon">
                </object>
              } @else {
                <object type="image/svg+xml" [data]="product.analytical_apps.items[active].image_dark_theme | safe" class="ifp-journey__tab-img">
                  <img [src]="product.analytical_apps.items[active].image_dark_theme" alt="icon">
                </object>
              }

            </div>
            <div class="ifp-products__sec-2">
              <div>
                <h2 class="ifp-products__heading">{{product.analytical_apps.title | translate}}</h2>
                <ng-container *ngIf="product.analytical_apps.items.length > 0">
                  <ul ngbNav #nav="ngbNav" [(activeId)]="active" [destroyOnHide]="false" class="nav-tabs">
                    <li [ngbNavItem]="i" [destroyOnHide]="true" *ngFor="let app of product.analytical_apps.items; index as i">
                      <button ngbNavLink class="ifp-products__tab-item">{{app.title | translate}}</button>
                      <ng-template ngbNavContent>
                        <p class="ifp-products__desc">{{app.description_1 | translate}}</p>
                        <p class="ifp-products__desc-short">{{app.description_2 | translate}}</p>
                      </ng-template>
                    </li>
                  </ul>
                  <div class="ifp-products__dot-nav">
                    <ng-container *ngFor="let dot of product.analytical_apps.items; let i = index">
                      <span class="ifp-products__dot" [ngClass]="{'ifp-products__dot--active': i === active}"></span>
                    </ng-container>
                  </div>
                  <div [ngbNavOutlet]="nav" class="mt-2"></div>
                </ng-container>
              </div>
            </div>
          </div>
      </div>

        <div class="ifp-products__wrapper" #productsWrapper>
            @for(card of product.cards; let i = $index; track i) {
              <div class="ifp-products__item">
                <h2 class="ifp-products__heading">{{card.title | translate}}
                  @if(i === 0) {
                    <em class="ifp-icon ifp-icon-verifyed-tick"></em>
                  } @else if (i === 1) {
                    <em class="ifp-icon ifp-icon-conical-flask"></em>
                  }
                </h2>
                <div class="ifp-products__img-wrapper" [ngStyle]="{'background-image': 'url('+ cardDetail[i].background +')'}">
                  <ifp-img class="ifp-products__img" [darkIcon]="card.image_dark_theme" [lightIcon]="card.image_light_theme" [class]="isView ? cardDetail[i].class : ''"></ifp-img>
                </div>
                <p class="ifp-products__desc">{{card.description_1 | translate}}</p>
                <p class="ifp-products__desc-short">{{card.description_2 | translate}}</p>
              </div>
            }
        </div>
    </div>
  </div>
}

