@use "../../../../../assets/ifp-styles/abstracts/index" as *;

:host {
  display: block;
  border-radius: 10px;
  background-color: $ifp-color-white;
  overflow: hidden;
}

.ifp-kpi-edit {
  &__modal-header,
  &__modal-footer,
  &__modal-body {
    padding: $spacer-3 $spacer-5;
  }
  &__modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid $ifp-color-grey-13;
  }
  &__modal-footer {
    display: flex;
    justify-content: flex-end;
    border-top: 1px solid $ifp-color-grey-13;
  }
  &__footer-btn {
    margin-inline-start: $spacer-5;
  }
  &__modal-head {
    font-size: $ifp-fs-6;
    font-weight: $fw-bold;
  }
  &__modal-close {
    font-size: $ifp-fs-4;
    cursor: pointer;
    opacity: 0.7;
    transition: 0.3s;
    &:hover {
      opacity: 1;
    }
  }
  &__modal-body {
    height: calc(100vh - 300px);
    @include ifp-scroll-y(transparent, $ifp-color-grey-7, 10px, 10px);
    position: relative;
  }
  &__tab {
    display: block;
    background-color: $ifp-color-white;
    margin-top: (-$spacer-3);
    padding: $spacer-3 $spacer-0 $spacer-4;
    position: sticky;
    top: (-$spacer-3);
    left: 0;
    z-index: 1;
  }
  &__accordion {
    display: block;
    border: 1px solid $ifp-color-grey-13;
    border-radius: 5px;
    margin-bottom: $spacer-2 + 2px;
  }
  &__source-title,
  &__source-arrow {
    transition: 0.3s;
  }
  &__source-arrow {
    margin-inline-start: auto;
  }
  &__source-list,
  &__column-sec {
    margin-bottom: $spacer-4;
  }
  &__column-sec {
    &:last-child {
      margin-bottom: $spacer-0;
    }
  }
  &__label {
    color: $ifp-color-grey-19;
    margin-bottom: $spacer-2;
  }
  &__data-icon,
  &__source-icon {
    display: inline-block;
    margin-inline-end: $spacer-2;
    font-size: $ifp-fs-4;
  }
  &__source-icon {
    color: $ifp-color-blue-hover;
  }
  &__data-icon {
    color: $ifp-color-white-global;
    background-color: $ifp-color-green-dark-1;
    padding: 2px $spacer-1;
    border-radius: 5px;
  }
  &__source-item {
    display: flex;
    align-items: center;
    padding: $spacer-2 $spacer-3;
    border: 1px solid $ifp-color-grey-7;
    border-radius: 10px;
    margin-bottom: $spacer-2 + 2px;
    &--select {
      cursor: pointer;
      transition: 0.3s;
      &:hover {
        border-color: $ifp-color-blue-hover;
        box-shadow: 0 2px 4px $ifp-color-black-08;
        .ifp-kpi-edit {
          &__source-title,
          &__source-arrow {
            color: $ifp-color-blue-hover;
          }
        }
      }
    }
  }
  &__column-heading {
    font-size: $ifp-fs-5;
    font-weight: $fw-semi-bold;
    margin-bottom: $spacer-4;
  }
  &__dropdown-wrapper,
  &__style-wrapper {
    margin-bottom: $spacer-4;
    &:last-child {
      margin-bottom: $spacer-0;
    }
  }
  &__value-dropdown {
    display: block;
    margin-bottom: $spacer-3;
  }
  &__compare-wrapper {
    display: flex;
    align-items: center;
    .ifp-kpi-edit {
      &__label {
        margin: $spacer-0 $spacer-3;
      }
      &__value-dropdown {
        flex: 1;
        margin-bottom: $spacer-0;
      }
    }
  }
  &__alignment {
    display: block;
    margin-top: $spacer-3;
  }
}

:host::ng-deep {
  .ifp-kpi-edit {
    &__tab {
      .ifp-tab__item {
        padding-block: $spacer-2;
      }
      .ifp-category-label__txt {
        font-weight: $fw-bold;
      }
    }
    &__accordion {
      .ifp-accordion {
        &__head {
          border-top: 0;
          padding: $spacer-3 + 4px;
        }
        &__body-content {
          padding: $spacer-0 ($spacer-3 + 4px) ($spacer-3 + 4px);
        }
      }
    }
  }

}
