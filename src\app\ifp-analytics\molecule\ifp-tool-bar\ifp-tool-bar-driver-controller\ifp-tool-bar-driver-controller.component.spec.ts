import { ComponentFixture, TestBed } from '@angular/core/testing';

import { IfpToolBarDriverControllerComponent } from './ifp-tool-bar-driver-controller.component';

describe('IfpToolBarDriverControllerComponent', () => {
  let component: IfpToolBarDriverControllerComponent;
  let fixture: ComponentFixture<IfpToolBarDriverControllerComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [IfpToolBarDriverControllerComponent]
    })
    .compileComponents();
    
    fixture = TestBed.createComponent(IfpToolBarDriverControllerComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
