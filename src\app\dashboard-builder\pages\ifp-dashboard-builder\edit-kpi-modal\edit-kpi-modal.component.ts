import { ifpColors } from './../../../../scad-insights/core/constants/color.constants';
import { Component, input, InputSignal, linkedSignal, output, WritableSignal } from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';
import { IfpTabComponent } from "../../../../scad-insights/ifp-widgets/ifp-molecules/ifp-tab/ifp-tab.component";
import { IfpButtonComponent } from "../../../../scad-insights/ifp-widgets/ifp-atoms/ifp-button/ifp-button.component";
import { buttonClass } from 'src/app/scad-insights/core/constants/button.constants';
import { IfpAccordionComponent } from "../../../molecule/ifp-accordion/ifp-accordion.component";
import { Content } from 'src/app/dashboard-builder/organism/ifp-chart-toolbar/ifp-chart-toolbar.interface';
import { IfpDbValueCardComponent, KpiCardTemplate } from "../../../organism/ifp-db-value-card-templates/ifp-db-value-card-templates.component";
import { SourceList } from 'src/app/dashboard-builder/molecule/ifp-import-dropdown/ifp-import-dropdown.component';
import { IfpMultiTagDropdownComponent } from "../../../../scad-insights/ifp-widgets/ifp-atoms/ifp-multi-tag-dropdown/ifp-multi-tag-dropdown.component";
import { IfpDbDropdownComponent } from "../../../molecule/ifp-db-dropdown/ifp-db-dropdown.component";
import { kpiCardTemplates } from 'src/app/scad-insights/core/constants/dashboard.constants';
import { ColorPaletteGroupComponent } from "../../../molecule/color-palette-group/color-palette-group.component";
import { TextAlignmentSelectorComponent } from "../../../molecule/text-alignment-selector/text-alignment-selector.component";

@Component({
  selector: 'ifp-edit-kpi-modal',
  imports: [TranslateModule, IfpTabComponent, IfpButtonComponent, IfpAccordionComponent, IfpDbValueCardComponent, IfpMultiTagDropdownComponent, IfpDbDropdownComponent, ColorPaletteGroupComponent, TextAlignmentSelectorComponent],
  templateUrl: './edit-kpi-modal.component.html',
  styleUrl: './edit-kpi-modal.component.scss'
})
export class EditKpiModalComponent {

  closeModal = output();
  public kpiDetails: InputSignal<Record<string, any>> = input.required();
  public sourceList = input.required<SourceList[]>();
  public possibleAggregationColumns = input([]);
  public kpiCardTemplates: Record<string, KpiCardTemplate> = kpiCardTemplates;
  public selectedTemplate: WritableSignal<KpiCardTemplate> = linkedSignal(()=> this.kpiCardTemplates[this.kpiDetails()['name'] ?? 'kpi_1']);
  public ifpColors = ifpColors;
  public theme = ifpColors.white;

  tabData = [
    {
      name: 'Template',
      key: 'template'
    },
    {
      name: 'Styling',
      key: 'style'
    }
  ];

  tabAccordianContent = {
    template: [
      {
        title: 'Template',
        key: 'template',
        isExpand: true
      },
      {
        title: 'Data Source',
        key: 'source',
        isExpand: false
      },
      {
        title: 'Select Columns ',
        key: 'columns',
        isExpand: false
      }
    ]
  }

  selectedTab = 0;
  buttonClass = buttonClass;
  valueList = [
    {
      name: 'Sum',
      key: 'sum'
    },
    {
      name: 'Average',
      key: 'average'
    }
  ];

  primaryValue = this.valueList[0];
  selectedTextAlignment = 'left';
  defaultStyleSettings = {
    theme: this.ifpColors.white,
    alignment: 'left'
  }

  onCloseModal() {
    this.closeModal.emit();
  }

  tabClick(event: number) {
    this.selectedTab = event;
  }

  cancelModal() {
    this.onCloseModal();
  }

  onSave() {
    this.onCloseModal();
  }

  expandAccordian(_event: boolean, content: Content) {
    this.tabAccordianContent.template.forEach((x: Content) => x.isExpand = x.key === content.key ? !x.isExpand : false)
  }

  selectXaxis(event: any){

  }

  selectPrimaryValue(value: any) {

  }

  selectSecondaryValue(value: any) {

  }

  selectTertiaryValue(value: any) {

  }

  selectCompareValue(value: any) {

  }

  selectCompareWithValue(value: any) {

  }

  onSelectTemplate(template: KpiCardTemplate) {
    this.selectedTemplate.set(template);
  }
}
