import { name } from "@azure/msal-browser/dist/packageMetadata";

export const dashboardConstants = {
  customCard: 'custom_card',
  toolbarDataTabName: 'data',
  maxRowCount: 200,
  maxColumnCount: 25,
  cardPreview: 'preview',
  cardCreate: 'create'
};

export const columnData = [
  {
    name: 'Date',
    key: 'date',
    type: 'date',
    aggregations: ['Monthly', 'Quarterly', 'Yearly']
  },
  {
    name: 'Total GDP',
    key: 'total_gdp',
    type: 'number',
    aggregations: ['Sum', 'Average', 'Count']
  },
  {
    name: 'GDP Growth Rate',
    key: 'gdp_growth_rate',
    type: 'number',
    aggregations: ['Sum', 'Average', 'Count']
  },
  {
    name: 'GDP Per Capita',
    key: 'gdp_per_capita',
    type: 'number',
    aggregations: ['Sum', 'Average', 'Count']
  },
  {
    name: 'GDP Growth Rate',
    key: 'gdp_growth_rate',
    type: 'number',
    aggregations: ['Sum', 'Average', 'Count']
  },
  {
    name: 'GDP Per Capita',
    key: 'gdp_per_capita',
    type: 'number',
    aggregations: ['Sum', 'Average', 'Count']
  },
  {
    name: 'GDP Growth Rate',
    key: 'gdp_growth_rate',
    type: 'number',
    aggregations: ['Sum', 'Average', 'Count']
  },
  {
    name: 'GDP Per Capita',
    key: 'gdp_per_capita',
    type: 'number',
    aggregations: ['Sum', 'Average', 'Count']
  }
]

export const dateTypes = {
  date: 'ifp-icon-calender',
  number: 'ifp-icon-number-type'
}

export const valueTemplates = [
  {
    text_one: 'Label',
    text_two: undefined,
    text_three: 'Value',
    text_four: undefined,
    key: 'kpi_one',
    value_arrow: false,
    title: 'KPI 1'
  },
  {
    text_one: 'Label',
    text_two: undefined,
    text_three: 'Value',
    text_four: 'Label Value 2',
    key: 'kpi_two',
    value_arrow: false,
    title: 'KPI 2'
  },
  {
    text_one: 'Label',
    text_two: 'Label Value 2',
    text_three: 'Value',
    text_four: 'Label Value 3',
    key: 'kpi_three',
    value_arrow: false,
    title: 'KPI 3'
  },
  {
    text_one: 'Label',
    text_two: undefined,
    text_three: 'Value',
    text_four: undefined,
    key: 'kpi_four',
    value_arrow: true,
    title: 'KPI 4'
  },
  {
    text_one: 'Label',
    text_two: undefined,
    text_three: 'Value',
    text_four: 'Label Value 2',
    key: 'kpi_five',
    value_arrow: true,
    title: 'KPI 5'
  },
  {
    text_one: 'Label',
    text_two: 'Label Value 2',
    text_three: 'Value',
    text_four: 'Label Value 3',
    key: 'kpi_six',
    value_arrow: true,
    title: 'KPI 6'
  },
]

export const kpiCardTemplates = {
  kpi_1: {
    name: 'kpi_1',
    config: {
      label: 'Label',
      value: 'Value'
    }
  },
  kpi_2: {
    name: 'kpi_2',
    config: {
      label: 'Label',
      value: 'Value',
      valueSecondary: 'Label Value 2'
    }
  },
  kpi_3: {
    name: 'kpi_3',
    config: {
      label: 'Label',
      value: 'Value',
      valueSecondary: 'Label Value 2',
      valueTertiary: 'Label Value 3'
    }
  },
  kpi_4: {
  name: 'kpi_4',
    config: {
      label: 'Label',
      value: 'Value',
      compareValue: {}
    }
  },
  kpi_5: {
    name: 'kpi_5',
    config: {
      label: 'Label',
      value: 'Value',
      valueSecondary: 'Label Value 2',
      compareValue: {}
    }
  },
  kpi_6: {
    name: 'kpi_6',
    config: {
      label: 'Label',
      value: 'Value',
      valueSecondary: 'Label Value 2',
      valueTertiary: 'Label Value 3',
      compareValue: {}
    }
  }
}
