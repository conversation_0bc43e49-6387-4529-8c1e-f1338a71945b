import { FormsModule, ReactiveFormsModule, FormGroup, FormControl } from '@angular/forms';
import { Component, Input, input, InputSignal, model, SimpleChanges, Output, EventEmitter } from '@angular/core';
import { IfpInputAutoResizeDirective } from 'src/app/scad-insights/core/directives/ifp-input-auto-height.directive';
import { IFPHighChartsComponent } from "../../scad-insights/ifp-widgets/charts/ifp-highcharts.component";
import { IfpDropdownComponent } from "../../scad-insights/ifp-widgets/ifp-atoms/ifp-dropdown/ifp-dropdown.component";
import { IfpDbDropdownComponent } from "../../dashboard-builder/molecule/ifp-db-dropdown/ifp-db-dropdown.component";
import { TranslateModule } from '@ngx-translate/core';
import { DxpKpiCustomFilter } from '../dxp-visualization-wizard-toolbar/dxp-visualization-filter-form/dxp-visualization-filter-form.component';
import { NgClass } from '@angular/common';
import { ConvertedChartData } from '../dxp.interface';
import { log } from 'console';
import { DxpChartsComponent } from '../../scad-insights/ifp-widgets/dxp-charts/dxp-charts.component';

@Component({
  selector: 'ifp-dxp-kpi-card',
  imports: [IfpInputAutoResizeDirective, IFPHighChartsComponent, IfpDropdownComponent, IfpDbDropdownComponent, TranslateModule, FormsModule, ReactiveFormsModule, NgClass, DxpChartsComponent],
  templateUrl: './dxp-kpi-card.component.html',
  styleUrl: './dxp-kpi-card.component.scss',
  providers: []
})

export class DxpKpiCardComponent {
  public edit = input(true);
  public title = model('Title');
  public description = model('Description');
  // public filters: InputSignal<DxpKpiCustomFilter[] | undefined> = input();
  @Input() filters: any[] = [];
  @Input() legends: any[] = [];
  @Input() defaultLegend: any = null;
  @Input() isDropdownDisabled: boolean = false;
  @Input() xAxisLabel: string = '';
  @Input() yAxisLabel: string = '';
  @Output() filtersChanged = new EventEmitter<any[]>();
  @Output() defaultLegendChanged = new EventEmitter<any>();
  @Output() requestGraphData = new EventEmitter<void>();
  @Output() legendDropdownChanged = new EventEmitter<any>();
  public graphData = input<ConvertedChartData>({
    category: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
    series: [{
      name: 'Sample Data',
      data: [29.9, 71.5, 106.4, 129.2, 144.0, 176.0, 135.6, 148.5, 216.4, 194.1, 95.6, 54.4]
  }],
  // xAxisLabel:'',
  // yAxisLabel:''
  });
public chartName = input<string>('lineChart');

public filtersForm: FormGroup = new FormGroup({});
private previousFiltersLength: number = 0;


  ngOnInit() {
    this.initFiltersForm();
  }

    ngOnChanges(changes: SimpleChanges) {
    if (changes['legends']) {
      console.log('Legends changed in KPI card:', this.legends);
      console.log('Legends length:', this.legends?.length);
    }
    if (changes['defaultLegend']) {
      console.log('Default legend changed in KPI card:', this.defaultLegend);
    }
    if (changes['filters']) {
      const filtersArray = Array.isArray(this.filters) ? this.filters : [];
      if (filtersArray.length !== this.previousFiltersLength) {
        this.initFiltersForm();
        this.previousFiltersLength = filtersArray.length;
      }
    }
  }

private initFiltersForm() {
  const filtersArray = Array.isArray(this.filters) ? this.filters : [];
  const group: { [key: string]: FormControl } = {};
  filtersArray.forEach((filter: any, idx: number) => {
    const valueData = filter.defaultValue ? [filter.defaultValue.value] : [];
    group['filter_' + idx] = new FormControl(valueData);
  });
  this.filtersForm = new FormGroup(group);
  // Emit initial values
  this.emitFiltersChanged();
  // Subscribe to value changes
  this.filtersForm.valueChanges.subscribe(() => {
    this.emitFiltersChanged();
  });
}



getFormControl(key: string): FormControl<any> {
  const control = this.filtersForm.get(key);
  if (!(control instanceof FormControl)) {
    throw new Error(`Expected FormControl for key: ${key}`);
  }
  return control;
}

// Emit filter values to parent
emitFiltersChanged() {
  console.log(this.filters);
  const filtersArray = (this.filters || []).map((filter, idx) => {
    const value = this.filtersForm.get('filter_' + idx)?.value;
    console.log(value)
    return {
      column: filter.key || filter.column || filter.name || '',
      comparator: 'in',
      value: value,
      data_type: filter.data_type || ''
    };
  });
  this.filtersChanged.emit(filtersArray);
}

  onDropdownValueChange(newValue: any, idx: number) {
    console.log(newValue);
    // Update the value in the form control
    this.filtersForm.get('filter_' + idx)?.setValue(newValue);
    // Optionally, update the filter object as well
    if (Array.isArray(this.filters) && this.filters[idx]) {
      this.filters[idx].value = newValue;
    }
    // Emit the updated filters
    this.emitFiltersChanged();
  }

  onDefaultLegendChange(selectedLegend: any) {
    console.log('Legend changed in KPI card:', selectedLegend);
    console.log('Current defaultLegend:', this.defaultLegend);
    console.log('Available legends:', this.legends);
    console.log('Legends length:', this.legends?.length);
    
    // Emit to update the default legend in parent components
    this.defaultLegendChanged.emit(selectedLegend);
    
    // Emit to sync with the toolbar's "Choose Legend" section
    this.legendDropdownChanged.emit(selectedLegend);
    
    // Emit event to request graph data with new legend
    this.requestGraphData.emit();
  }

}
