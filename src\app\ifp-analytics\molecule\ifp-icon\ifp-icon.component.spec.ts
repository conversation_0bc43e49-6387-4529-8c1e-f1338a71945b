/* tslint:disable:no-unused-variable */
import { async, ComponentFixture, TestBed } from '@angular/core/testing';
import { By } from '@angular/platform-browser';
import { DebugElement } from '@angular/core';

import { IfpIconComponent } from './ifp-icon.component';

describe('IfpIconComponent', () => {
  let component: IfpIconComponent;
  let fixture: ComponentFixture<IfpIconComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ IfpIconComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(IfpIconComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
