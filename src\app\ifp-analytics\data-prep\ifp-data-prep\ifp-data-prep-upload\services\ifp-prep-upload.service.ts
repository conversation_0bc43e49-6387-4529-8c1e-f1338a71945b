import { Injectable } from '@angular/core';
import { HttpService } from 'src/app/scad-insights/core/services/http/http.service';
import { prepsApiEndpoints } from '../../constants/if-preps.constants';

@Injectable()
export class IfpPrepUploadService {

  constructor(private _http: HttpService) { }

  getUploadData(data: FormData) {
    return this._http.fileUpload(prepsApiEndpoints.sourceUpload, data, true);
  }

  getIndicatorUpload(data: any) {
    return this._http.post(prepsApiEndpoints.indicatorUpload, data, true);
  }

  getSheetList(data: FormData) {
    return this._http.fileUpload(prepsApiEndpoints.sheetList, data, true);
  }

}
