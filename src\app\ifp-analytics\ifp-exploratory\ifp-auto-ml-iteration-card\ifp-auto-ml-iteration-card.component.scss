@use "../../../../assets/ifp-styles/abstracts/index" as *;
.ifp-itr-card {
background-color: $ifp-color-section-white;
border-radius: 6px;
display: inline-flex;
align-items: center;
&__content{
  display: block;
}
&__sug-head {
  font-size:$ifp-fs-3 ;
  font-weight: $fw-bold;
  margin-bottom: $spacer-2;
}
&__sug-text {
  font-size:$ifp-fs-3 ;
  color: $ifp-color-tertiary-text;
  strong{
    @include desktop-sm {
      font-size:$ifp-fs-2 ;

    }
  }
  @include desktop-sm {
    font-size:$ifp-fs-2 ;

  }
}
&__sug-card {
  background-color: $ifp-color-pele-yellow;
  padding: $spacer-3;
  margin-top: $spacer-3;
}
&__badge-wrapper {
  display: flex;
  flex-wrap: wrap;
  margin: $spacer-0 (-$spacer-2) ;
  @include ifp-scroll-y(transparent, $ifp-color-grey-1, 4px, 8px);
  max-height: 300px;
}
&__selected-independent {
  font-size:$ifp-fs-3 ;
  font-weight:$fw-semi-bold;
  margin-top: $spacer-4;

}
&__badge {
  white-space: nowrap;
  margin:$spacer-3 $spacer-2 $spacer-0 ;
  padding: $spacer-2 $spacer-3 ;
  border-radius: 120px ;
  font-size: $ifp-fs-4;
  font-weight:$fw-medium;
  &--selected {
    background-color: $ifp-color-blue-menu;
    color: $ifp-color-white-global;
  }
  &--un-selected {
    border: $ifp-color-grey-1 1px solid;
    background-color: $ifp-color-grey-15;
    color: $ifp-color-grey-14 ;
  }
  &--ignore {
    border: $ifp-color-red 1px solid;
    background-color: $ifp-color-red-light;
    color: $ifp-color-red;
  }

  @include desktop-sm {
    margin: $spacer-2 $spacer-1  $spacer-0;
    padding: $spacer-1 $spacer-3;
    font-size: $ifp-fs-2;
    .ifp-icon {
      font-size: $ifp-fs-2;
    }
  }
}
}

:host ::ng-deep {
  .ifp-accord-box {
  &__head {
    padding: $spacer-3 !important;
  }
  &__content {
    border-top: unset !important;
  }
  &__head {
    .ifp-edit__input {
      width: 100%;
      min-width: auto;
    }
  }
  &__title {
    font-weight: $fw-bold;
  }
}
}
