import { Location } from '@angular/common';
import { Component, EventEmitter, Input, Output } from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';

@Component({
    selector: 'ifp-back-button',
    imports: [TranslateModule],
    templateUrl: './ifp-back-button.component.html',
    styleUrl: './ifp-back-button.component.scss'
})
export class IfpBackButtonComponent {

  constructor(private _location: Location) {}

  @Output() buttonClick = new EventEmitter();

  @Input() buttonText: string = 'Back';
  @Input() icon: string = 'ifp-icon-leftarrow';
  @Input() isGoBack: boolean = true;

  onButtonClick() {
    if (this.isGoBack) {
      this._location.back();
    } else {
      this.buttonClick.emit();
    }
  }
}
