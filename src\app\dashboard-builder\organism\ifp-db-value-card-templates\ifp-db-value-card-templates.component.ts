import { kpiCardTemplates } from '../../../scad-insights/core/constants/dashboard.constants';
import { ChangeDetectionStrategy, Component, model, OnInit, output } from '@angular/core';
import { KpiTemplateCardComponent } from "../../molecule/kpi-template-card/kpi-template-card.component";
import { NgClass } from '@angular/common';

@Component({
  selector: 'ifp-db-value-card-templates',
  imports: [KpiTemplateCardComponent, NgClass],
  templateUrl: './ifp-db-value-card-templates.component.html',
  styleUrl: './ifp-db-value-card-templates.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class IfpDbValueCardComponent implements OnInit {

  public selectTemplate = output<KpiCardTemplate>();

  public valueCardTemplates: any[] = [];
  public kpiCardTemplates: Record<string, KpiCardTemplate> = kpiCardTemplates;

  public selectedTemplate = model<KpiCardTemplate>();

  ngOnInit(): void {
    this.setCardTemplates();
  }

  setCardTemplates() {
    const keys = Object.keys(this.kpiCardTemplates)
    keys.forEach((key: string) => {
      this.valueCardTemplates.push(this.kpiCardTemplates[key]);
    });
  }

  onSelectTemplate(key: string) {
    this.selectedTemplate.set(this.kpiCardTemplates[key]);
    // this.selectTemplate.emit(this.kpiCardTemplates[key]);
  }
}

export interface KpiCardTemplate {
  name: string;
  config: Record<string, any>;
}

