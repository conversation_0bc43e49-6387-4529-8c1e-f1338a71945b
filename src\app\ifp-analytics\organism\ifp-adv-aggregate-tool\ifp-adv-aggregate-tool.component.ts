import { ChangeDetectionStrategy, Component, EventEmitter, inject, Input, On<PERSON><PERSON><PERSON>, OnDestroy, OnInit, Output, signal, WritableSignal } from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';
import { IfpButtonComponent } from '../../../scad-insights/ifp-widgets/ifp-atoms/ifp-button/ifp-button.component';
import { buttonClass } from 'src/app/scad-insights/core/constants/button.constants';
import { ColumnPrep, ToolOptions } from '../../data-prep/ifp-data-prep/ifp-prep-select-data/interface/prep-selection.interface';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { selectNodeStoreAdvance } from '../../data-prep/ifp-advance-prep/store/advance-node-store/advance-node.store';
import { IfpAdvancePrepService } from '../../data-prep/ifp-advance-prep/service/ifp-advance-prep.service';
import { ToasterService } from 'src/app/scad-insights/core/services/tooster/ToastrService.service';
import { IfpSpinnerComponent } from '../../../scad-insights/ifp-widgets/ifp-molecules/ifp-spinner/ifp-spinner.component';
import { IfpDropdownComponent } from 'src/app/scad-insights/ifp-widgets/ifp-atoms/ifp-dropdown/ifp-dropdown.component';
import { NgClass } from '@angular/common';
import { connectionType } from '../../data-prep/ifp-data-prep/constants/if-preps.constants';
import { IfpDataToolAccordianComponent } from '../../molecule/ifp-data-tool-accordian/ifp-data-tool-accordian.component';

@Component({
    selector: 'ifp-adv-aggregate-tool',
    templateUrl: './ifp-adv-aggregate-tool.component.html',
    styleUrl: './ifp-adv-aggregate-tool.component.scss',
    changeDetection: ChangeDetectionStrategy.OnPush,
    imports: [IfpDropdownComponent, TranslateModule, IfpButtonComponent, ReactiveFormsModule, IfpSpinnerComponent, NgClass, IfpDataToolAccordianComponent]
})
export class IfpAdvAggregateToolComponent implements OnChanges, OnInit, OnDestroy {
  @Output() cancel = new EventEmitter();
  @Output() save = new EventEmitter();

  @Input({ required: true }) columnList!: ColumnPrep[];
  @Input() workFlowRunning = signal(false);
  @Input() emptyConnection = signal(true);
  @Input() currentNodeId?: string='';
  @Input() currentConection: WritableSignal<boolean> = signal(false);
  @Input() sourceData: WritableSignal<boolean> = signal(false);
  @Input() isConfigUpdated: WritableSignal<boolean> = signal(false);
  @Input() errorMessageForTools?: string;
  public buttonClass = buttonClass;
  public readonly advanceStore = inject(selectNodeStoreAdvance);
  public aggregateForm!: FormGroup;

  public selectedColumn!: ColumnPrep;
  public aggregateOpts: ToolOptions[] = [];
  public selectedAggregate!: ToolOptions;
  public aggregationList: SelectedAggregate[] = [];
  public submitted: WritableSignal<boolean> = signal(false);
  public selectedDatatype!: string;
  public savedConfig: WritableSignal<boolean> = signal(true);
  public isSelectActive: boolean = false;

  constructor(private _formbuilder: FormBuilder, private _prepService: IfpAdvancePrepService, private _toaster: ToasterService) { }


  ngOnInit() {
    this.aggregateForm = this._formbuilder.group({
      aggregate_field: ['', Validators.required],
      type: [''],
      column_name: [''],
      method: ['', Validators.required],
      priority: ['']
    });
  }

  ngOnChanges() {
    if (this.columnList?.length > 0) {
      this.aggregationList = [];
      this.patchStoreData();
    }

  }

  onCancel() {
    this.cancel.emit();
  }

  // New functions

  patchStoreData() {
    this.resetForm();
    this.aggregationList = [];
    const nodeData = this.advanceStore.selectNodeValue().nodeValue(this.currentNodeId ?? '');
    const columns = nodeData?.configuration?.columns?.length ? nodeData.configuration?.columns : [];
    columns.forEach((element: any) => {
      const columnDataIndex: number = this.columnList?.findIndex(x => x.name === element.aggregate_field);
      if (columnDataIndex >= 0) {
        const aggregateOption = this.columnList[columnDataIndex].aggregate_options?.find((x: ToolOptions) => x.method === element.method);
        if (aggregateOption) {
          this.aggregationList.push({
            aggregate_field: element.aggregate_field,
            type: element.type,
            method: aggregateOption,
            column_name: element.column_name
          });
        }
      }
    });
  }

  selectColumn(event: ColumnPrep | any) {
    this.selectedColumn = event;
    this.aggregateForm.controls['aggregate_field'].setValue(this.selectedColumn.name);
    this.aggregateForm.controls['type'].setValue(this.selectedColumn.type);
    if (this.selectedColumn.aggregate_options) {
      this.aggregateOpts = this.selectedColumn.aggregate_options.filter(x => x.method != 'count_non_blank' && x.method != 'count_blank');
    }
  }

  selectMethod(event: ToolOptions | any) {
    this.selectedAggregate = event;
  }

  addAggregate() {
    if (this.aggregateForm.invalid) {
      this.submitted.set(true);
      return;
    }
    const columnName = this.aggregateForm?.value?.column_name ? this.aggregateForm?.value?.column_name : this.aggregateForm?.value?.aggregate_field;
    const existsInAggregationList = this.aggregationList?.some((agg: SelectedAggregate) => (agg?.column_name?.toLowerCase() || agg?.aggregate_field?.toLowerCase()) === columnName.toLowerCase());
    if (existsInAggregationList) {
      this.submitted.set(true);
      this._toaster.error('The column name already exists!');
      return;
    }
    this.submitted.set(false);
    if (this.isSelectActive) {
      const index = this.aggregateForm.value.priority;
      this.aggregationList[index] = this.aggregateForm.value;
    } else {
      this.aggregationList.push(this.aggregateForm.value);
    }
    this.resetForm();
  }

  removeAggregate(index: number) {
    this.aggregationList.splice(index, 1);
  }

  resetForm() {
    this.aggregateOpts = [];
    this.isSelectActive = false;
    this.aggregateForm?.patchValue({
      aggregate_field: '',
      type: '',
      column_name: '',
      method: '',
      priority: ''
    });
  }

  onSave() {
    const config = {
      columns: this.getAggregateOpts()
    };
    this.savedConfig.set(true);
    this.advanceStore.updateNode(this.currentNodeId ?? '', 'Aggregate Tool updated', connectionType.aggregateTool, config, 'Aggregate Tool updated');
    this._prepService.nodeChangeDetect.next('node updated');
    // this._toaster.success('Data Saved Successfully');
    this.save.emit();
  }

  getAggregateOpts() {
    const selectedFilters: any[] = [];
    if (this.aggregationList?.length) {
      this.aggregationList.forEach((element: SelectedAggregate, index: number) => {
        const data = {
          aggregate_field: element.aggregate_field,
          type: element.type,
          method: element.method.method,
          column_name: element.column_name !== '' ? element.column_name : element.aggregate_field,
          priority: index
        };
        selectedFilters.push(data);
      });
    }
    return selectedFilters;
  }

  selectAggregate(aggregate: SelectedAggregate, index: number) {
    this.isSelectActive = true;
    const selectedCol = this.columnList.find((col: ColumnPrep) => col.name === aggregate.aggregate_field);
    if (selectedCol) {
      this.selectedColumn = selectedCol;
      this.aggregateOpts = selectedCol.aggregate_options ?? [];
      this.selectedAggregate = aggregate.method;
    }
    this.aggregateForm.setValue({
      aggregate_field: aggregate.aggregate_field,
      type: aggregate.type,
      method: aggregate.method,
      column_name: aggregate.column_name,
      priority: index
    });
  }



  get getControls() {
    return this.aggregateForm.controls;
  }

  ngOnDestroy(): void {
    if (!this._prepService.isConfigSave() && this.columnList?.length > 0) {
      this.onSave();
    }
  }
}

interface SelectedAggregate {
  aggregate_field: string;
  type: string;
  column_name: string;
  method: ToolOptions;
  priority?: number;
}
