export interface WhatsNew {
  title: string;
  id: string;
  pageDescription: string;
  pageMenuIcon: string;
  pageMenuLightIcon: string;
  pageIcon: string;
  pageLightIcon: string;
  pageType: string;
  publication_date: string;
  updated: string;
  subsections: WhatsNewSubsection[] | [];
  route: string;
}

export interface WhatsNewSubsection {
  sectionOrder: string;
  sectionType: string;
  indicatorList: WhatsNewIndicatorList[];
  title: string;
  key: string;
}

export interface WhatsNewIndicatorList {
  indicatorId: string;
  contentType: string;
  appType: string;
  type: string;
  domain: string;
  domainDarkIcon:string;
  domainLightIcon:string;
  title:string;
}

