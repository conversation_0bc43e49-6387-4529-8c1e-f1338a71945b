import { CommonModule } from '@angular/common';
import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { Title } from '@angular/platform-browser';
import { Store } from '@ngrx/store';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { Observable, tap } from 'rxjs';
import { PageData } from 'src/app/scad-insights/core/interface/molecule/breadcrumb.interface';
import { LineBreakPipe } from 'src/app/scad-insights/core/pipes/lineBreak.pipe';
import { IfpBreadcrumbsComponent } from 'src/app/scad-insights/ifp-widgets/ifp-molecules/ifp-breadcrumbs/ifp-breadcrumbs.component';
import { getTerms } from 'src/app/scad-insights/store/footer/terms-n-conditions/terms-n-conditions.action';
import { selectTermsResponse } from 'src/app/scad-insights/store/footer/terms-n-conditions/terms-n-conditions.selector';
import { TermsState } from 'src/app/scad-insights/store/footer/terms-n-conditions/terms-n-conditions.state';
import { IfpButtonComponent } from '../../ifp-widgets/ifp-atoms/ifp-button/ifp-button.component';
import { CommonApiService } from 'src/app/scad-insights/core/services/common-api/commonApi.service';
import { headerKeys, title } from 'src/app/scad-insights/core/constants/header.constants';
import { ThemeService } from 'src/app/scad-insights/core/services/theme/theme.service';
import { buttonClass } from 'src/app/scad-insights/core/constants/button.constants';
import { SubSink } from 'subsink/dist/subsink';
import { Router } from '@angular/router';
import { IFPMsalService } from '../../core/services/IFP-msal.service';

@Component({
    selector: 'app-terms-n-conditions',
    templateUrl: './terms-n-conditions.component.html',
    styleUrls: ['./terms-n-conditions.component.scss'],
    imports: [CommonModule, TranslateModule, IfpBreadcrumbsComponent, LineBreakPipe, IfpButtonComponent]
})
export class TermsNConditionsComponent implements OnInit {


  @Input() isAccepted: boolean = false;
  @Input() isLanguage: boolean = false;
  @Input() isModal: boolean = false;
  public selectedLanguage: string = 'en';
  public defaultSettings: any;
  public termData: any = [];
  public buttonClass = buttonClass;
  public subs = new SubSink();

  @Output() termsResponse: EventEmitter<boolean> = new EventEmitter<boolean>();
  @Output() tncVersion: EventEmitter<number> = new EventEmitter<number>();


  constructor(private _titleService: Title, private store: Store, private _commonApiService: CommonApiService, private themeService: ThemeService, public _translate: TranslateService, private router: Router, private _msalService:IFPMsalService) {
    if (this.router.url == 'terms') {
      this._titleService.setTitle(`${title.bayaan} | Terms & Conditions`);

      (window as any)?.dataLayer?.push({
        'event': 'page_load',
        'page_title_var': 'Terms & Conditions',
        'userId': this._msalService.convertEmail(this._msalService.getLoginData.account.username)
      });
    }

  }

  public termsData$: Observable<TermsState> = this.store.select(selectTermsResponse).pipe(
    tap((data: any) => {
      if (data) {
        this.pageData = [
          {
            title: 'Home',
            route: '/home'
          },
          {
            title: data?.title,
            route: ''
          }
        ];
      }

    })
  );

  pageData: PageData[] = [
    {
      title: 'Home',
      route: '/home'
    },
    {
      title: 'Terms and Conditions',
      route: ''
    }
  ];

  ngOnInit() {
    this.store.dispatch(getTerms());
    this._commonApiService.getUserSettings().subscribe(res => {
      this.defaultSettings = {
        settings: [
          {
            name: 'fontSize',
            value: res.fontSize ? res.fontSize : 'md'
          },
          {
            name: 'cursor',
            value: res.cursor ? res.cursor : 'type1'
          },
          {
            name: 'theme',
            value: res.theme ? res.theme : 'light'
          },
          {
            name: 'lang',
            value: res.lang ? res.lang : 'en'
          }
        ]
      };
      this.selectedLanguage = res.lang;
      localStorage.setItem(headerKeys.lang, res.lang);
      this._translate.use(res.lang);
      this.selectedLanguage = res.lang;
      this.themeService.changeLanguage(res.lang);
      this.termsData$.subscribe((resp) => {
        this.termData = resp;
      });
    });
  }

  languageChange(lang: any) {
    document.cookie = `lang=${lang}; path=/;`;
    this.defaultSettings.settings[3].value = lang;

    this.subs.add(
      this._commonApiService.updateUserSettings(this.defaultSettings).subscribe((res: any) => {
        if (res) {
          this._translate.use(lang);
          this.themeService.changeLanguage(lang);
          this.selectedLanguage = lang;
          location.reload();
        }
      })
    );
  }

  onButtonClick(response: boolean, version: number = 1) {
    this.termsResponse.emit(response);
    this.tncVersion.emit(version);
  }
}
