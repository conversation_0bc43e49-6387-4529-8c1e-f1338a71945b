import { ChangeDetectorRef, Component, ElementRef, EventEmitter, Input, Output, Renderer2, signal, ViewChild } from '@angular/core';
import { IfpCheckBoxComponent } from '../../../../scad-insights/ifp-widgets/ifp-atoms/ifp-check-box/ifp-check-box.component';
import { TranslateModule } from '@ngx-translate/core';
import { IfpPdfTemplateComponent } from '../../../../scad-insights/ifp-widgets/ifp-molecules/ifp-pdf-template/ifp-pdf-template.component';
import html2canvas from 'html2canvas';
import { DownLoadService } from 'src/app/scad-insights/core/services/download-service/download.service';
import { DownloadService  } from 'src/app/scad-insights/core/services/download.service';
import jsPDF from 'jspdf';
import { ThemeService } from 'src/app/scad-insights/core/services/theme/theme.service';
import { NgClass } from '@angular/common';
import { SubSink } from 'subsink';
import { CommonApiService } from 'src/app/scad-insights/core/services/common-api/commonApi.service';
import { IfpModalComponent } from '../../../../scad-insights/ifp-widgets/ifp-organism/ifp-modal/ifp-modal.component';
import { IfpTncModalComponent } from '../../../../scad-insights/ifp-widgets/ifp-organism/ifp-tnc-modal/ifp-tnc-modal.component';
import { UsageDashboardLogService } from 'src/app/scad-insights/core/services/usage-dashboard-log/usage-dashboard-log.service';
import { logType } from 'src/app/scad-insights/core/services/usage-dashboard-log/usage-dashboard.constants';
@Component({
    selector: 'ifp-tool-download',
    imports: [IfpCheckBoxComponent, TranslateModule, IfpPdfTemplateComponent, NgClass, IfpModalComponent, IfpTncModalComponent],
    templateUrl: './ifp-tool-download.component.html',
    styleUrl: './ifp-tool-download.component.scss'
})
export class IfpToolDownloadComponent {
  @ViewChild('elementChart') elementChart!: ElementRef;
  @ViewChild('tncModal') tncModal!: IfpModalComponent;
  @Output() downloadEvent = new EventEmitter();
  @Output() checkTnC: EventEmitter<boolean> = new EventEmitter<boolean>();
  @Input() termsAndConditions = false;
  @Input() name = '';
  @Input() data: Record<string, string | number>[] = [];
  @Input() indicatorId!: string;
  @Input() tncState: boolean = false;
  public showValuePrint =  signal(false);
  public subs: SubSink = new SubSink();
  public isTncModalOpen: boolean = false;

  private sessionId!: string;

  constructor(private _cdr: ChangeDetectorRef, private _render: Renderer2, private _downloadService : DownLoadService, private _themeService:ThemeService,
    private _indicatorDownload: DownloadService, private _commonApiService: CommonApiService, private readonly log: UsageDashboardLogService) {}

  download(data: string) {
    this.downloadEvent.emit(data);
    if (data === 'pdf' ) {
      this.setImage();
      this.downloadPDfImage();
      this. removeImage();
    } else if (data === 'png') {
      this.setImage();
      this.downloadImage();
      this.removeImage();
    } else {
      this._indicatorDownload.exportToExcel(this.data, this.name);
    }
    if (this.sessionId) {
      this.log.logEnds(this.sessionId, this.log.currentTime );
    }
    this.sessionId = this.log.createUUid;
    this.log.logStart(this.sessionId, logType.download, this.log.currentTime, +this.indicatorId, this.name, data);
  }

  setImage() {
    if ( this._themeService.defaultTheme === 'dark' ) {
      this._render.removeClass(document.body, 'ifp-dark-theme');
      this._render.addClass(document.body, 'ifp-light-theme');
      this._render.addClass(document.body, 'ifp-light-theme--download');
    }

    this.showValuePrint.set(true);
    this._cdr.detectChanges();
    this._render.removeClass(this.elementChart.nativeElement, 'ifp-chart-toolbar__download-display');
    this._cdr.detectChanges();
  }

  removeImage() {
    this._render.addClass(this.elementChart.nativeElement, 'ifp-chart-toolbar__download-display');
    if ( this._themeService.defaultTheme === 'dark' ) {
      this._render.removeClass(document.body,  'ifp-light-theme');
      this._render.removeClass(document.body, 'ifp-light-theme--download');
      this._render.addClass(document.body, 'ifp-dark-theme');
    }

  }

  downloadImage() {
    this.canvasRender(this.elementChart.nativeElement).then(data=> {
      this._downloadService.downloadFiles(this.convertBase64ToFile(data, `${this.name}.jpg`), `${this.name}.jpg`);
      this.showValuePrint.set(false);
      this._cdr.detectChanges();
    });

  }

  downloadPDfImage() {
    const width = this.elementChart.nativeElement.offsetWidth;
    const height = this.elementChart.nativeElement.offsetHeight;
    this.canvasRender(this.elementChart.nativeElement).then(data=> {
      this.downloadPdf(width, height, data );
      this.showValuePrint.set(false);
      this._cdr.detectChanges();
    });
  }


  downloadPdf(width: number, height: number, image: string  )  {
    const orientation = width >= height ? 'landscape' : 'portrait';
    const pdf = new jsPDF({
      orientation: orientation, // or 'landscape'
      unit: 'pt',
      format: [height, width],
      compress: true
    });
    const imgWidth = width; // Adjust image width as needed
    const imgHeight = height; // Assuming image aspect ratio of 4:3, adjust as needed
    const xPos = 0;
    const yPos = 0;

    pdf.addImage(image, 'JPEG', xPos, yPos, imgWidth, imgHeight);
    pdf.save(`${this.name}.pdf`);
  }

  getFileNameWithoutExtension(filename: string) {
    // Split the filename at the last dot
    const lastDotIndex = filename.lastIndexOf('.');

    // If there's no dot, return the original filename
    if (lastDotIndex === -1) {
      return filename;
    }

    // Return the substring before the last dot
    return filename.substring(0, lastDotIndex);
  }

  convertBase64ToFile(base64Data: any, filename: string) {
    const base64String = base64Data.split(',')[1]; // Assuming the base64Data format is "data:image/png;base64,<actual_base64_data>"
    const byteCharacters = atob(base64String);
    const byteNumbers = new Array(byteCharacters.length);
    for (let i = 0; i < byteCharacters.length; i++) {
      byteNumbers[i] = byteCharacters.charCodeAt(i);
    }
    const byteArray = new Uint8Array(byteNumbers);
    const blob = new Blob([byteArray], { type: 'image/png' });
    return new File([blob], filename, { type: 'image/png' });
  }

  canvasRender(element: HTMLElement) {
    return new Promise((resolve: (value: string) => void) =>{
      html2canvas(element, {
        scale: 1.5,
        allowTaint: true, useCORS: true,
        removeContainer: true,
        backgroundColor: null,
        imageTimeout: 15000
      }).then(canvas => {
        resolve(canvas.toDataURL('image/png', 10));
      }
      );
    });
  }

  termsResponse(response: boolean) {
    if (response) {
      this.subs.add(this._commonApiService.setDownloadTermsStatus(this.indicatorId).subscribe((res: any) => {
        if (res) {
          this.tncState = true;
          this.checkTnC.emit(response);
        }
      }));
    }
    if (this.isTncModalOpen) {
      this.tncModal.removeModal();
      this.isTncModalOpen = false;
    }
  }

  showTnC() {
    this.tncModal.createElement();
    this.isTncModalOpen = true;
  }

}
