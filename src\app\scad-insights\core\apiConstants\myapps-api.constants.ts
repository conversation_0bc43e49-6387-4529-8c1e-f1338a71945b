export const myApps = {
  mappedList: 'content-type/myapps/apps-list',
  addAndList: 'content-type/myapps/submit',
  myAppsLandingPage: 'content-type/myapps',
  draftApi: 'content-type/myapps/draft',
  draftListApi: 'content-type/myapps/draft-list',
  draftListRemove: 'content-type/myapps/draft/delete/',
  draftNodeList: 'content-type/myapps/draft-nodes-list/',
  myAppsResize: 'common/interaction',
  saveMyApps: 'content-type/myapps/state',
  resetMyApps: 'content-type/myapps/state/reset',
  NewMyAppsApiDashboard: 'content-type/myapps/dashboards/',
  getDashboardData: 'content-type/myapps/dashboards/detail/',
  createDashboard: 'content-type/myapps/dashboards/create',
  editDashboard: 'content-type/myapps/dashboards/edit/',
  deleteDashboardNodes: 'content-type/myapps/dashboards/',
  subDeleteDashboardNodes: '/nodes/remove',
  deleteDashboard: 'content-type/myapps/dashboards/delete/',
  shareApps: 'content-type/myapps/share',
  viewShared: 'content-type/myapps/share/view',
  recievedApps: 'content-type/myapps/share/list/received',
  sentApps: 'content-type/myapps/share/list/sent',
  deleteSharedApp: 'content-type/myapps/share/delete',
  readApp: 'content-type/myapps/share/read'
};
