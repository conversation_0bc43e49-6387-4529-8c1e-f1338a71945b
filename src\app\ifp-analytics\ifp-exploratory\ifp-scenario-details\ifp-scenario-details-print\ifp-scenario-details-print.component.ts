import {  DecimalPipe } from '@angular/common';
import { Component, Input, WritableSignal } from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';
import { ShortNumberPipe } from 'src/app/scad-insights/core/pipes/shortNumber.pipe';
import { IfpAnalyticLineChartComponent } from '../../../../scad-insights/ifp-widgets/ifp-molecules/ifp-analytic-line-chart/ifp-analytic-line-chart.component';
import { ChartDataScenario } from '../ifp-scenario-details.component';

@Component({
    selector: 'ifp-scenario-details-print',
    imports: [
        ShortNumberPipe,
        DecimalPipe,
        TranslateModule,
        IfpAnalyticLineChartComponent
    ],
    templateUrl: './ifp-scenario-details-print.component.html',
    styleUrl: './ifp-scenario-details-print.component.scss'
})
export class IfpScenarioDetailsPrintComponent {

  @Input() currentIndicatorValue!: number;
  @Input() currentIndicatorForecastValue!: number;
  @Input() chartData:ChartDataScenario[] = [];
  @Input() yAxisLabel!: WritableSignal<string>;
  @Input() xAxisLabel!: WritableSignal<string>;
  @Input() frequency!: string;
  @Input() forecastHorizon!: number;
  @Input() percentageDifferenceAbs!: number;
  @Input() percentageDifference!: number;

}
