import { slaService } from './../../core/services/sla/sla.service';
import { ThemeService } from 'src/app/scad-insights/core/services/theme/theme.service';
import { CommonModule } from '@angular/common';
import { Component, OnDestroy, OnInit } from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';
import { Router } from '@angular/router';
import { IFPMsalService } from 'src/app/scad-insights/core/services/IFP-msal.service';
import { SubSink } from 'subsink';

@Component({
    selector: 'app-sla-certificate',
    templateUrl: './sla-certificate.component.html',
    styleUrls: ['./sla-certificate.component.scss'],
    imports: [CommonModule, TranslateModule]
})
export class SlaCertificateComponent implements OnInit, OnDestroy {

  public subs = new SubSink();
  public isGroup = false;
  constructor(public _themeService: ThemeService, private _slaService: slaService, private _route: Router, private _msal: IFPMsalService) { }
  public backgroundImage = 'assets/images/bg.png';
  public maintananceData!: Maintanance;


  ngOnInit() {
    this._slaService.slaLoader$.next(false);
    if (this._route.url === '/group_unassigned') {
      this.isGroup = true;
    }
    if (this._msal.getToken && this._msal.tokenExp && (new Date() < new Date(this._msal.tokenExp))) {
      this.subs.add(this._slaService.slaStatusCheck().subscribe((data: any) => {
        this.maintananceData = data;
        if (!data.maintenance_mode) {
          this._route.navigateByUrl('/');
        }
      }));
    } else {
      this._route.navigateByUrl('/auth');
    }

  }

  ngOnDestroy(): void {
    this.subs.unsubscribe();
  }

}

export interface Maintanance {
  body: string,
  end_date: string,
  from_date: string;
  maintenance_mode: boolean;
  message: string;
  status: boolean;
  title: string;
}
