@use "../../../assets/ifp-styles/abstracts/index" as *;
.ifp-gen-ai-dashboard-feedback {
  position: relative;

  background-color: $ifp-color-white;
  padding: $spacer-5;
  border-radius: 10px;
  &__icon-wrapper {
    display: flex;
    justify-content: center;
  }
  &__icon-inner {
    width: 44px;
    height: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: $ifp-color-blue-1;
    color: $ifp-color-blue-menu;
    border-radius: 50%;
    font-size:$ifp-fs-10;
    margin-bottom: $spacer-3;
  }
  &__header {
    font-weight: $fw-bold;
    font-size: $ifp-fs-8;
    text-align: center;
    margin-bottom: $spacer-3;
  }
  &__sub-heading {
    font-size: $ifp-fs-3;
    text-align: center;
    color: $ifp-color-grey-14;
    margin-bottom: $spacer-3;
    padding: $spacer-0 $spacer-5;
  }
  &__textarea {
    display: block;
    width: 100%;
    padding: ($spacer-2 + 2px) $spacer-3;
    resize: none;
    border-radius: 5px;
    color: $ifp-color-secondary-grey;
    margin-bottom: $spacer-3;
    font-size: $ifp-fs-3;
    border: 1px solid $ifp-color-grey-13;
    min-height: 100px;
  }
  &__icon-cross {
    position: absolute;
    right: 10px;
    top:10px;
    cursor: pointer;
    .ifp-icon-cross {
      font-size: $ifp-fs-1;
    }
  }
&__btn {
  margin: $spacer-0 $spacer-3;
}
&__btn-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
}

}
