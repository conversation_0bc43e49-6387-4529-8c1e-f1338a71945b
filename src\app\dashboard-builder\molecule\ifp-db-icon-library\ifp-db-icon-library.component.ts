import { Component, ElementRef, EventEmitter, Input, OnChanges, Output, SimpleChanges, ViewChild } from '@angular/core';
import { IfpSearchComponent } from 'src/app/scad-insights/ifp-widgets/ifp-atoms/ifp-search/ifp-search.component';
import { IfpIconSelectorComponent } from '../../atom/ifp-icon-selector/ifp-icon-selector.component';
import { DbToolbarIcon } from '../../organism/ifp-chart-toolbar/ifp-chart-toolbar.interface';
import { TranslateModule } from '@ngx-translate/core';
import { iconLibrary, tagList } from './ifp-db-icon-library.constants';
import { Ng<PERSON>lass, NgStyle, SlicePipe } from '@angular/common';
import { PaginationComponent } from 'src/app/scad-insights/ifp-widgets/ifp-molecules/pagination/pagination.component';
import { IfpNoDataComponent } from '../../../scad-insights/ifp-widgets/ifp-molecules/ifp-no-data/ifp-no-data.component';
import { DashboardService } from 'src/app/scad-insights/core/services/create-dashboard/dashboard.service';

@Component({
    selector: 'ifp-db-icon-library',
    templateUrl: './ifp-db-icon-library.component.html',
    styleUrl: './ifp-db-icon-library.component.scss',
    imports: [IfpSearchComponent, IfpIconSelectorComponent, TranslateModule, NgClass, PaginationComponent, SlicePipe, IfpNoDataComponent, NgStyle]
})
export class IfpDbIconLibraryComponent implements OnChanges {

  @ViewChild('scrollableDiv') scrollableDiv!: ElementRef;

  @Input() enableIconTitle: boolean = false;
  @Input() iconSet: DbToolbarIcon[] = iconLibrary;
  @Input() tagList: TagItem[] = tagList;
  @Input() selectedCard!: string;
  @Input() cntType!: string;
  @Output() selectIcon: EventEmitter<DbToolbarIcon> = new EventEmitter<DbToolbarIcon>();

  public iconsToDisplay: DbToolbarIcon[] = this.iconSet;
  public selectedtag: number = 0;

  public offset: number = 0;
  public limit: number = 30;
  public size: number = 0;
  public limitOptions: number[] = [10, 15, 20, 25, 30];
  public isDragging: boolean = false;
  private startX!: number;
  private scrollLeft!: number;

  public allowedExtensions: string[] = ['.png', '.svg'];

  constructor(private _dashboardService: DashboardService) { }


  ngOnChanges(changes: SimpleChanges): void {
    if (changes['selectedCard']) {
      this.iconsToDisplay.map(x => x.selected = false);
      const icon = this._dashboardService.chartSettings[this.cntType]?.find((x: { id: string; }) => x.id == this.selectedCard).icon?.title;

      const index = icon ? this.iconsToDisplay.findIndex(x => x.title == icon) : -1;
      if (index >= 0) {
        this.iconsToDisplay[index].selected = true;
      }
    }
  }

  selectChartType(item: DbToolbarIcon, index: number) {
    this.iconsToDisplay.map(x => x.selected = false);
    this.iconSet.map(x => x.selected = false);
    this.iconsToDisplay[index].selected = true;
    this.selectIcon.emit(item);
  }

  selectTag(tag: TagItem, index: number) {
    const icons = this.iconSet;
    this.selectedtag = index;
    if (tag.key !== 'all') {
      this.iconsToDisplay = icons.filter((icon: DbToolbarIcon) => icon.key === tag.key);
    } else {
      this.iconsToDisplay = this.iconSet;
    }
  }

  onPageChange(offset: number) {
    this.offset = offset;
  }

  limitChanged(event: number) {
    this.limit = event;
  }

  onIconUpload(event: File[]) {
    //
  }

  onIconRemoved(event: File[]) {
    //
  }

  getSearchIcon(event: any) {
    if (event) {
      this.iconsToDisplay = this.iconSet.filter((icon: DbToolbarIcon) => icon.title?.toLowerCase().includes(event.toLowerCase()));
    } else {
      this.iconsToDisplay = this.iconSet;
    }
  }

  scroll(key: string) {
    const div: HTMLElement = this.scrollableDiv.nativeElement;
    div.scrollTo({
      left: key == 'left' ? div.scrollLeft + 100 : div.scrollLeft - 100, // Adjust the value according to your requirement
      behavior: 'smooth' // Optional: Use smooth scrolling
    });
  }

  onMouseDown(event: MouseEvent) {
    this.isDragging = true;
    this.startX = event.pageX - this.scrollableDiv.nativeElement.offsetLeft;
    this.scrollLeft = this.scrollableDiv.nativeElement.scrollLeft;
  }

  onMouseUp(event: MouseEvent) {
    this.isDragging = false;
  }

  onMouseMove(event: MouseEvent) {
    if (this.isDragging) {
      event.preventDefault();
      const x = event.pageX - this.scrollableDiv.nativeElement.offsetLeft;
      const walk = (x - this.startX) * 2; // Adjust the multiplier as needed
      this.scrollableDiv.nativeElement.scrollLeft = this.scrollLeft - walk;
    }
  }
}

export interface TagItem {
  title: string;
  key: string;
}
