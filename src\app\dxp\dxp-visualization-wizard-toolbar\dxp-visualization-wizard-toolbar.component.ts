import { chartTypes } from './../../dashboard-builder/organism/ifp-chart-toolbar/ifp-chart-toolbar.constants';
import { buttonClass } from './../../scad-insights/core/constants/button.constants';
import { NgClass } from '@angular/common';
import { Component, input, InputSignal, linkedSignal, model, output, WritableSignal, inject, OnInit } from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';
import { IfpButtonComponent } from "../../scad-insights/ifp-widgets/ifp-atoms/ifp-button/ifp-button.component";
import { IfpNoDataComponent } from "../../scad-insights/ifp-widgets/ifp-molecules/ifp-no-data/ifp-no-data.component";
import { DxpKpiCustomFilter, DxpKpiCustomFilterColumnList, DxpVisualizationFilterFormComponent } from './dxp-visualization-filter-form/dxp-visualization-filter-form.component';
import { DxpVisualizationFilterCardComponent } from "./dxp-visualization-filter-card/dxp-visualization-filter-card.component";
import { IfpIconSelectorComponent } from "../../dashboard-builder/atom/ifp-icon-selector/ifp-icon-selector.component";
import { DbToolbarIcon } from 'src/app/dashboard-builder/organism/ifp-chart-toolbar/ifp-chart-toolbar.interface';
import { IfpAccordionComponent } from 'src/app/dashboard-builder/molecule/ifp-accordion/ifp-accordion.component';
import { dxpPreviewWizardTabMenu } from './dxp-visualization-wizard.constants';
import { FormsModule } from '@angular/forms';
import { SelectedProduct } from '../widgets/dxp-accordian/dxp-accordian.component';
import { IfpDbDropdownComponent } from 'src/app/dashboard-builder/molecule/ifp-db-dropdown/ifp-db-dropdown.component';
import { ApiService } from 'src/app/scad-insights/core/services/api.service';
import { dxpApi } from '../dxp.constants';

@Component({
  selector: 'ifp-dxp-visualization-wizard-toolbar',
  imports: [TranslateModule, NgClass, IfpButtonComponent, IfpNoDataComponent, DxpVisualizationFilterFormComponent, DxpVisualizationFilterCardComponent, IfpIconSelectorComponent, IfpAccordionComponent, FormsModule, IfpDbDropdownComponent],
  templateUrl: './dxp-visualization-wizard-toolbar.component.html',
  styleUrl: './dxp-visualization-wizard-toolbar.component.scss'
})
export class DxpVisualizationWizardToolbarComponent implements OnInit {

  private readonly _apiService = inject(ApiService);

  public filterSettingsChange = output<DxpKpiCustomFilter[]>();
  public chartSettingsChange = output<DxpKpiChartSettings>();
  public requestGraphData = output<void>();
  public legendsDataChange = output<any[]>();
  public legendDropdownChange = output<any>();

  public cardTitle = model('');
  public cardDescription = model('');
  public tabList = input<DxpKpiPreviewTabList[]>(dxpPreviewWizardTabMenu);

  // public columnList: InputSignal<DxpKpiCustomFilterColumnList[]> = input([{name: '', values: [{id: '', value: ''}]}]);
  public columnList = input<any>([]);
  public selectedProductDetails = input<SelectedProduct>();



  public selectedTabItem:WritableSignal<DxpKpiPreviewTabList> = linkedSignal(() => this.tabList()[0]);

  public isAddFilter: boolean = false;
  public addedFilters: DxpKpiCustomFilter[] = [];

  public buttonClass = buttonClass;
  public chartTypes = chartTypes;
  public chartSettings: {chartType: string} = {chartType: 'lineChart'};
//for dummy datause
  public legendOptions = [
    { id: 'legend1', value: 'Legend 1' },
    { id: 'legend2', value: 'Legend 2' }
    // need to add columns data
  ];
  public selectedLegend: string | null = null;
  public selectedLegends: any[] = [];
  public defaultLegend: any = null;

  ngOnInit(): void {
    console.log(this.selectedTabItem());

    // Set line chart as selected by default
    this.chartTypes.forEach(chartType => {
      chartType.selected = chartType.key === 'line';
    });
  }

  onLegendMultiSelected(selected: any[]) {
    this.selectedLegends = selected;
    console.log('=== LEGEND MULTI SELECT DEBUG ===');
    console.log('Selected legends:', selected);
    console.log('Selected legends length:', selected.length);
    console.log('Selected legends structure:', JSON.stringify(selected, null, 2));
    
    // If only one legend is selected, automatically set it as default
    if (this.selectedLegends.length === 1) {
      this.defaultLegend = this.selectedLegends[0];
    }
    // If defaultLegend is not in the new selection, reset it
    else if (!this.selectedLegends.includes(this.defaultLegend)) {
      this.defaultLegend = this.selectedLegends[0] || null;
    }
    
    // Sync the selection to ensure consistency
    this.syncDropdownSelection();
    
    // Emit all selected legends to parent component for dropdown display
    if (this.selectedLegends.length > 0) {
      this.legendsDataChange.emit(this.selectedLegends);
      console.log('All selected legends emitted:', this.selectedLegends);
    }
    console.log('=== END LEGEND MULTI SELECT DEBUG ===');
  }

  onLegendDropdownClose() {
    // Call the graph plotting API when dropdown is closed
    // Always call the API, with or without legends
    if (this.selectedLegends.length > 0) {
      this.callGraphPlottingAPI();
    } else {
      // Call API without legends when all items are unselected
      this.callGraphPlottingAPIWithoutLegends();
    }
  }

  private callGraphPlottingAPI() {
    // Always send the first item from the selected legends list to the graph payload
    if (this.selectedLegends.length > 0) {
      const firstSelectedLegend = this.selectedLegends[0];
      const legend = {
        column: firstSelectedLegend.name,
        data_type: firstSelectedLegend.data_type || 'string'
      };

      console.log('First selected legend for graph payload:', legend);

      // Emit all selected legends to parent component for dropdown display
      this.legendsDataChange.emit(this.selectedLegends);

      // Emit event to parent component to request full graph data with first legend
      this.requestGraphData.emit();
    }
  }

  private callGraphPlottingAPIWithoutLegends() {
    console.log('No legends selected - calling API without legends');

    // Emit empty legends data to parent component
    this.legendsDataChange.emit([]);

    // Emit event to parent component to request full graph data without legends
    this.requestGraphData.emit();
  }

  onDefaultLegendChange(selectedLegend: any) {
    
    this.defaultLegend = selectedLegend;
   
    
    // Ensure the selected legend is also in the selectedLegends array
    if (!this.selectedLegends.includes(selectedLegend)) {
      this.selectedLegends = [selectedLegend];
    }
    
    // Sync dropdown selection to reflect the radio button selection
    this.syncDropdownSelection();
    
    // Emit only the selected legend item from this.selectedLegends
    this.legendsDataChange.emit([selectedLegend]);
    
    // Emit event to request graph data with new legend
    this.requestGraphData.emit();
  }

  // Method to sync dropdown selection with radio button selection
  private syncDropdownSelection() {
    // This will trigger ngOnChanges in the dropdown component
    // and update the checked state of options
    this.selectedLegends = [...this.selectedLegends];
  }

  onLegendSelected(option: any) {
    this.selectedLegend = option.id;
    // need to update logic for legend
  }

  openNewFilter() {
    this.isAddFilter = true;
  }

  onSelectTabItem(index: number) {
    this.selectedTabItem.set(this.tabList()[index]);
  }

  addNewFilter(item: DxpKpiCustomFilter) {
    console.log(item)
    this.addedFilters = [...this.addedFilters, item]; // immutable update
    this.isAddFilter = false;
    this.filterSettingsChange.emit(this.addedFilters);
  }

  onRemoveCard(index: number) {
    this.addedFilters = this.addedFilters.filter((_, i) => i !== index); // immutable update
    this.filterSettingsChange.emit(this.addedFilters);
  }

  updateChartType(chartType: DbToolbarIcon) {
    console.log(chartType);
    this.chartSettings.chartType = chartType.key;
    this.chartSettingsChange.emit(this.chartSettings);
  }

  expandAccordian(_event: boolean, content: Content) {
    // Find the clicked content item
    const clickedContent = this.selectedTabItem().content.find(x => x.title === content.title);
    if (clickedContent) {
      // Toggle the clicked item's expand state
      clickedContent.isExpand = !clickedContent.isExpand;

      // Close all other accordions when opening one (accordion behavior)
      if (clickedContent.isExpand) {
        this.selectedTabItem().content.forEach(x => {
          if (x.title !== content.title) {
            x.isExpand = false;
          }
        });
      }
    }
  }
}
export interface DxpKpiChartSettings {
  chartType: string;
}

export interface DxpKpiPreviewTabList {
  key: string;
  title: string;
  description: string;
  icon: string;
  disabled: boolean;
  content: Content[];
}

interface Content {
  title: string;
  key: string;
  isExpand?: boolean;
}
