import { UploadedFile } from 'src/app/ifp-analytics/organism/ifp-prep-upload-data/ifp-prep-upload-data.component';

export interface Toolbar {
  key?: string;
  icon?: string;
  color?: string,
  isSelected?: boolean;
  menu: Tools[];
}

export interface Tools {
  key: string;
  name: string;
  description: string;
  icon: string;
  color?: string;
  url?: string;
  iconName?: string;
  leftNodeCount?: number;
  rightNodeCount?: number;
  type: string;
  anchorTexts?:string[]
  anchorInputTexts?:string[];
  enabled?: boolean;
}

export interface CustomEventEmit {
  value: any;
  event: string;
  index?: number
}

export interface PrepNode {
  name: string;
  replace_workflow?:string;
  description: string;
  nodes: Node[];
  connections: Connection2[];
}

interface Connection2 {
  source: string;
  destination: string;
  destination_anchor?: number;
  source_anchor?: number;
}

export interface Node {
  object_id: string;
  name: string;
  type: string;
  time: Date,
  configuration: PrepSelectConfiguration;
  settings?: any,
  status?: string
}

export interface PrepSelectConfiguration {
  connection?: Connection;
  update?: any;
  remove?: any;
  join_by?: Joinby | any;
  name?: string;
  type?: string;
  is_download?:boolean;
  columns?: Record<string, any> | [] | any;
  source?:string;
  target?: string;
  mapping?: {
    source:  string;
    target: string;
  }[]
}

interface Joinby {
  source: Source;
  target: Source;
}

interface Source {
  columns: string[];
  id?: string;
}



interface Connection {
  connection_type: string;
  path: string;
}

export interface FileResponePrep {
  name?: string;
  file?: string;
  owner?: string;
  storage_backend?: string;
  id?: string;
  objectId?: string;
  created_at?: string;
  type?: string;
  sheet?: boolean;
  sheetList?: string[];
  sheetName?:string;
  sheetDetails?: { file: UploadedFile, path: string, sheet: string, sheetList?: string[] },
  index?:number;
}



export interface ImputePrepData {
  name: string;
  type: string;
  metadata: Record<string, any>;
  nullable: boolean;
  null_count: number;
  sample_value: string;
  null_percentage: number;
}

export interface BoxOption {
  label: string,
  selected: boolean,
  id?:number,
  name?: string,
  toolTipDisable?: boolean,
  extraSpaceTop ?: number
  null_percentage?:number;
}

export interface NodeData {
  objectId: string;
  type: string;
  name: string;
  description?: string;
}

export interface ToolValues {
  toolName: string,
  type: string,
  config: any
}

export interface CanvasTool {
  type: string,
  value: string | number
}
