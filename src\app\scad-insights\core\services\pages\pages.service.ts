import { contentType } from './../../constants/contentType.constants';
import { Injectable } from '@angular/core';
import { commonApi } from '../../apiConstants/common-api.constants';
import { HttpService } from '../http/http.service';
import { Observable } from 'rxjs';
import { IndicatorListData } from '../../interface/indicator.interface';
import { Store } from '@ngrx/store';

@Injectable({
  providedIn: 'root'
})
export class PagesService {

  constructor(private _http: HttpService, private store: Store) {

  }

  getPages() {
    return this._http.get(commonApi.homePage);
  }

  getIndicator(id: string, contentTypeValue: string, driver: any = {}, overview: boolean = false, visa: any = {}, screener: boolean = false, classification:string = ''): Observable<IndicatorListData> {
    if (contentTypeValue === contentType['analytical-apps'] || contentTypeValue === contentType['analytical_apps']) {
      return this._http.post(`${commonApi.indicator + contentTypeValue.replace('_', '-')}/${id}`, { indicatorDrivers: driver });
    } else if (visa?.screener) {
      return this._http.get(`${commonApi.indicator + contentTypeValue}/${id}?viewName=${visa?.View}`);
    } else if (screener) {
      return this._http.get(`${commonApi.indicator + contentTypeValue}/${id}?screener=true`);
    }
    return this._http.get(`${commonApi.indicator + contentTypeValue}/${id}?overview=${overview}&classification=${classification}`);
  }

  getStatisticsInsights(id: string[], type: string) {
    return this._http.post(commonApi.statisticsInsights, { ids: id, type: type });
  }

  getFilterCall(data: any) {
    return this._http.post(commonApi.chartFilter, { meta: data });
  }

  compareIndicators(data: any) {
    return this._http.post(commonApi.compareIndicators, data);
  }

  compareAddToMyApps(payLoad: any) {
    return this._http.post(commonApi.compareAddToMyApps, payLoad);
  }

  getCompareAppsView(id: string) {
    return this._http.get(`${commonApi.getCompareAppsViewEndpoint}/${id}`);
  }

  getGlosseryFilter() {
    return this._http.get(commonApi.glosseryFilters);
  }

  getGlossoryList(page: number, per_page: number, searchString: string = '', filter: any) {
    let endpoint = `${commonApi.getGlossoryList}?page=${page}&limit=${per_page}`;
    if (searchString != '') {
      endpoint = `${endpoint}&term=${searchString}`;
    }
    return this._http.post(endpoint, filter);
  }

  getCensusData(){
    return this._http.get(commonApi.census);
  }
}
