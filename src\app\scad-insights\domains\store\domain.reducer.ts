import { createReducer, on } from '@ngrx/store';
import { CategoryState, NewsLetterState } from './domain.state';
import {loadCategorySuccess, loadNewsLetterSuccess } from './domain.action';


const category: CategoryState = {
  data: []
};

const newsLetter: NewsLetterState = {
  data: []
};


export const getCategory = createReducer(
  category,
  on(loadCategorySuccess, (state: any, action: any): CategoryState => {
    return ({ ...state,
      data: action.data,
      status: action.status,
      loader: action.loader,
      errorMessage: action.error
    });
  })
);


export const getNewsLetter = createReducer(
  newsLetter,
  on(loadNewsLetterSuccess, (state: any, action: any): NewsLetterState => {
    return ({ ...state,
      data: action.data,
      status: action.status,
      loader: action.loader,
      errorMessage: action.error
    });
  })
);




