import { Injectable } from '@angular/core';
import { HttpService } from '../http/http.service';
import { commonApi } from '../../apiConstants/common-api.constants';

@Injectable({
  providedIn: 'root'
})
export class CommonApiService {

  public censusData:any;

  constructor(private _http: HttpService) {}

  getDownloadTermsStatus(nodeId: string) {
    return this._http.get(`${commonApi.getIndicatorTnC}?nodeId=${nodeId}`);
  }

  setDownloadTermsStatus(nodeId: string) {
    return this._http.post(`${commonApi.getIndicatorTnC}/set?nodeId=${nodeId}`);
  }

  getUserSettings() {
    return this._http.get(commonApi.userSettings);
  }

  updateUserSettings(data: any) {
    return this._http.post(commonApi.userSettings, data);
  }
}
