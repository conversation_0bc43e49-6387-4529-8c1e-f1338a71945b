import { createAction, props } from '@ngrx/store';

import {  DomainDetailsNew } from 'src/app/scad-insights/core/interface/domain.interface';
import { domainDetailsApiStateName } from './domain-details-api.state';

export const domainDetailsNameNew = `[${domainDetailsApiStateName}] Domain details Api loaded`;
export const  domainDetailsSuccessNameNew = `[${domainDetailsApiStateName}] Domain detail Api loaded success`;
export const  domainDetailsSuccessNameNewNodes = `[${domainDetailsApiStateName}] Domain detail Api Nodes loaded success`;
export const  domainDetailsSuccessNameNewNodesData = `[${domainDetailsApiStateName}] Domain detail Api Nodes loaded data`;
export const  domainDetailsCancelNameNewNodesData = `[${domainDetailsApiStateName}] Domain detail Api Nodes cancel data`;
export const loadDomainDetailsNew = createAction(domainDetailsNameNew, props<{id: number}>());
export const loadDomainDetailsSuccessNew = createAction(domainDetailsSuccessNameNew, props<{data: DomainDetailsNew, status?: boolean, errorMessage?: string, loader?:boolean}>());

export const loadDomainDetailsNewNode = createAction(domainDetailsSuccessNameNewNodes, props<{id: string | null, filter: any}>());

export const loadDomainDetailsNewData = createAction(domainDetailsSuccessNameNewNodesData, props<{data: any}>());
export const loadDomainDetailsNodeCancelNewData = createAction(domainDetailsCancelNameNewNodesData);
