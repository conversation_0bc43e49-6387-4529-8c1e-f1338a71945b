import { Injectable } from '@angular/core';
import { HttpService } from '../http/http.service';
import { commonApi } from '../../apiConstants/common-api.constants';
import { chartConstants } from '../../constants/chart.constants';

@Injectable({
  providedIn: 'root'
})
export class ChartToolsService {


  fullscreen(element: any, chartComponent: any, chartType: string) {
    // this.chartComponent.fullscreen();
    const fullscreenElement = element?.nativeElement;

    if (document.fullscreenElement) {
      document.exitFullscreen().then(() => {
        if (chartType === chartConstants.TREECHART) {
          chartComponent.fullscreen(true);
        }
      }).catch((err) => {
      });
    } else {
      if (fullscreenElement.webkitRequestFullscreen()) {
        fullscreenElement.webkitRequestFullscreen().then(() => {
          if (chartType === chartConstants.TREECHART) {
            chartComponent.fullscreen(false);
          }
        }).catch((err: any) => {
        });
      } else {
        fullscreenElement.requestFullscreen().then(() => {
          if (chartType === chartConstants.TREECHART) {
            chartComponent.fullscreen(false);
          }
        }).catch((err: any) => {
        });
      }
    }
  }
}
