@use "../../../../assets/ifp-styles/abstracts/index" as *;
:host {
  display: block;
}

.ifp-adv-panel {
  background-color: $ifp-color-section-white;
  width: 100%;
  padding: $spacer-4 $spacer-4 $spacer-0;
  display: flex;
  flex-direction: column;
  position: relative;
  &__title {
    font-size: $ifp-fs-5;
    font-weight: $fw-bold;
    text-transform: capitalize;
    margin-bottom: $spacer-3;
  }
  &__desc {
    margin-bottom: $spacer-3;
    font-style: italic;
  }
  // &__footer {
  //   border-top: 1px solid $ifp-color-grey-3;
  //   margin: auto (-$spacer-4) $spacer-0;
  //   padding-bottom: $spacer-4;
  // }
  // &__footer-inner {
  //   display: flex;
  //   padding: $spacer-4 $spacer-3 $spacer-0;
  // }
  // &__button {
  //   display: flex;
  //   width: 50%;
  //   margin: $spacer-0 $spacer-2;
  // }
  &__note {
    padding: $spacer-3 ;
    background-color: $ifp-color-blue-bg-light;
    border-radius: 5px;
    margin-bottom: $spacer-3;
    display: flex;
    .ifp-icon {
      margin-inline-end: $spacer-2;
      font-size: $ifp-fs-4;
    }
  }
  &__cross {
    position: absolute;
    top: $spacer-3;
    right: $spacer-4;
    font-size: $ifp-fs-4;
    cursor: pointer;
  }
}

:host::ng-deep {
  .ifp-adv-panel {
    &__button .ifp-btn {
      width: 100%;
    }
    // &__content {
    //   height: 100%;
    //   @include ifp-scroll-y(transparent, $ifp-color-grey-1, 4px, 8px);
    // }
  }
}

:host-context([dir="rtl"]) {
  .ifp-adv-panel {
    &__cross {
      left: $spacer-4;
      right: auto;
    }
  }
}
