import { DecimalPipe, Ng<PERSON><PERSON>, Ng<PERSON>tyle } from '@angular/common';
import { AfterViewInit, ChangeDetectorRef, Component, ElementRef, input, model, OnChanges, Signal, SimpleChanges, viewChild } from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';

@Component({
  selector: 'ifp-tree-progress',
  imports: [TranslateModule, NgStyle, NgClass, DecimalPipe],
  templateUrl: './ifp-tree-progress.component.html',
  styleUrl: './ifp-tree-progress.component.scss'
})
export class IfpTreeProgressComponent implements OnChanges, AfterViewInit {

  public circle: Signal<ElementRef | undefined> = viewChild('circle');

  public title = input<string>('');
  public value = input<number>(0);
  public isVertical = input<boolean>(false);
  public size = input<number>(315);
  public progressData = model<ProgressData[]>([]);
  public circumference: number = 0;
  public isSemiCircle = input(true);

  constructor(private readonly _cdr: ChangeDetectorRef) {}

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['progressData'] && this.progressData().length) {
      this.progressData.update((item: ProgressData[]) => {
        item.forEach((value, i) => {
          item[i] = {...value, ...{percent: +((value.value / this.value())*100).toFixed(1)}}
        });
        return item;
      });
      if (this.circle()?.nativeElement && this.circumference > 0) {
        this._cdr.detectChanges();
        this.createProgressChart();
      }
    }
  }

  ngAfterViewInit(): void {
    this.circumference = this.size() * Math.PI;
    if (this.circle()?.nativeElement && this.progressData().length) {
      this.createProgressChart();
    }
  }

  createProgressChart() {
    const circle = this.circle()?.nativeElement.children;
    let angle = 0;
    this.progressData().forEach((data: ProgressData, index: number) => {
      const val = data?.percent ?? 0;
      let  percent = this.isSemiCircle() ? val/2 : val;
      let offset = this.circumference - percent / 100 * this.circumference;
      circle[index].style.strokeDashoffset = offset+'';
      if (index !== 0) {
        const prevVal = this.progressData()[index - 1]?.percent ?? 0;
        angle += (prevVal / 100 * (this.isSemiCircle() ? 180 : 360));
        circle[index].style.transform = `rotate(${angle}deg)`;
      }
    });
  }

}

export interface ProgressData {
  name: string;
  value: number;
  color: string;
  percent?: number;
}
