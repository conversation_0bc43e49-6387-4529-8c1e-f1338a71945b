import { Injectable } from '@angular/core';
import { Actions,  createEffect, ofType } from '@ngrx/effects';
import {  exhaustMap, map, of, switchMap } from 'rxjs';
import { loadCreateConnectionUploadCreateName, loadCreateNodeSuccess, loadNode } from './node-store.actions';
import { IfpPrepService } from '../../ifp-prep-service';
import { PrepNode } from '../../interface/ifp-data-prep.interface';

@Injectable()
export class PrepNodeEffects {

  constructor(
    private actions$: Actions,
    private ifpPrepService: IfpPrepService
  ) { }

  // currentNode$ = createEffect(() => {
  //   return this.actions$.pipe(
  //     ofType(loadNode[loadCreateConnectionUploadCreateName]),
  //     exhaustMap((data: { data: PrepNode; }) => {
  //       this.ifpPrepService.currentNodeData.next(data.data);
  //       return of(data).pipe(
  //         map(() => {
  //           return loadCreateNodeSuccess();
  //         })
  //       );
  //     })
  //   //  { dispatch: false }
  //   );
  // });


}


