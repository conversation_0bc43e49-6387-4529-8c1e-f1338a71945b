import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { IfpPagesRoutingModule } from './ifp-pages-routing.module';
import { HeaderComponent } from 'src/app/scad-insights/shared/header/header.component';
import { FooterComponent } from 'src/app/scad-insights/shared/footer/footer.component';
import { IfpModalComponent } from 'src/app/scad-insights/ifp-widgets/ifp-organism/ifp-modal/ifp-modal.component';
import { TranslateModule } from '@ngx-translate/core';
import { IfpStepperSmileComponent } from 'src/app/scad-insights/ifp-widgets/ifp-atoms/ifp-stepper-smally/ifp-stepper-smile.component';
import { IfpJourneyStartComponent } from 'src/app/scad-insights/ifp-widgets/ifp-organism/ifp-journey-start/ifp-journey-start.component';
import { SurveyModalComponent } from 'src/app/scad-insights/ifp-widgets/ifp-organism/survey-modal/survey-modal.component';
import { UserJourneyModule } from 'src/app/scad-insights/user-journey/user-journey.module';
import { ActivityMonitorService } from 'src/app/scad-insights/core/services/activity-monitor.service';
import { IfpPagesComponent } from './ifp-pages.component';
import { IfpChatBotComponent } from '../../scad-insights/ifp-chat-bot/ifp-chat-bot.component';


@NgModule({
  declarations: [
    IfpPagesComponent
  ],
  providers: [ActivityMonitorService],
  imports: [
    CommonModule,
    IfpPagesRoutingModule,
    HeaderComponent,
    FooterComponent,
    IfpModalComponent,
    TranslateModule,
    IfpStepperSmileComponent,
    IfpJourneyStartComponent,
    SurveyModalComponent,
    UserJourneyModule,
    IfpChatBotComponent
  ]
})
export class IfpPagesModule { }
