import { NgClass } from '@angular/common';
import { Component, input, model, signal, viewChild } from '@angular/core';
import { buttonClass } from '../../scad-insights/core/constants/button.constants';
import {
  CdkDragDrop,
  CdkDrag,
  CdkDropList,
  moveItemInArray,
  transferArrayItem,
  CdkDragPlaceholder,
  CdkDragEnd,
  CdkDragStart,
  CdkDragPreview
} from '@angular/cdk/drag-drop';
import { IfpButtonComponent } from "../../scad-insights/ifp-widgets/ifp-atoms/ifp-button/ifp-button.component";
@Component({
  selector: 'ifp-dxp-dataset-card',
  imports: [NgClass, CdkDropList, CdkDrag, CdkDragPlaceholder, CdkDragPreview, IfpButtonComponent],
  templateUrl: './dxp-dataset-card.component.html',
  styleUrl: './dxp-dataset-card.component.scss'
})
export class DxpDatasetCardComponent {
  public title = input('');
  public datasetList = model<{
    name: string;
    icon: string;
}[]>([]);
public buttonClass = buttonClass;
public dropdownRef =  viewChild<string | CdkDropList<any> | (string | CdkDropList<any>)>('dataset');
public  connectedto = input<string | CdkDropList<any> | (string | CdkDropList<any>)[]>([]);
public isDragging = signal(false);
drop (event: CdkDragDrop<{
  name: string;
  icon: string;
}[], any, any>) {

    if (event.previousContainer === event.container) {
      moveItemInArray(
        this.datasetList(),
        event.previousIndex,
        event.currentIndex
      );
    } else {
      transferArrayItem(
        event.previousContainer.data,
        event.container.data,
        event.previousIndex,
        event.currentIndex
      );
    }
    this.isDragging.set(false);
  }
  getRef() {
    return this.dropdownRef() ?? '';
  }

  dragStart(event: CdkDragStart) {
    this.isDragging.set(true);
  }

  dragStop(event: CdkDragEnd) {
    this.isDragging.set(false);
  }


deleteItem() {
  this.datasetList.set([]);
}


}

export interface DxpDataSet {
  name: string;
  icon: string;
}
