import { Component, EventEmitter, Input, Output } from '@angular/core';
import { FormControl, ReactiveFormsModule } from '@angular/forms';

@Component({
  selector: 'ifp-input',
  standalone: true,
  imports: [ReactiveFormsModule],
  templateUrl: './ifp-input.component.html',
  styleUrl: './ifp-input.component.scss'
})
export class IfpInputComponent {
@Input() formControls = new FormControl();
@Input() disableForm = new FormControl(false);
@Input() maxLength = 1500;
@Output() dataChangeEvent =  new EventEmitter();

dataChange() {
  this.dataChangeEvent.emit({event: 'input'});
}
}
