import { NgClass } from '@angular/common';
import { Component, Input } from '@angular/core';

@Component({
    selector: 'ifp-progress-bar',
    imports: [NgClass],
    templateUrl: './ifp-progress-bar.component.html',
    styleUrl: './ifp-progress-bar.component.scss',
})
export class IfpProgressBarComponent {
  @Input() color!: string;
  @Input() width: string = '0';
  @Input() height: string = '8px';
  @Input() bordered = false;
}
