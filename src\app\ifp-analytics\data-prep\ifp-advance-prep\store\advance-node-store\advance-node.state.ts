import { prepStatusStore } from '../../../ifp-data-prep/constants/ifp-state.contants';
import { PrepNode } from '../../../ifp-data-prep/interface/ifp-data-prep.interface';




export const  createNodeStateAdvanceName = 'createNodeAdvanceComponent';
export interface NodeStateAdvance {data: PrepNode; currentStatus: string}


export const createNodeInitalStateAdvance:  NodeStateAdvance  = {
  currentStatus: prepStatusStore.initial,
  data: {
    name: 'Workflow',
    description: 'Workflow',
    nodes: [],
    connections: []
  }
};



