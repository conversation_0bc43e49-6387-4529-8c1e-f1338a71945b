@if(graphData()&&graphData().category.length) {
<div class="ifp-kpi-card" [ngClass]="{ 'ifp-kpi-card--non-editable': !edit() }">
  <div class="ifp-kpi-card__head-sec">
    <img
      src="../../../assets/images/economy-icon.svg"
      alt=""
      class="ifp-kpi-card__icon"
    />
    @if (edit()) {
    <input
      type="text"
      class="ifp-kpi-card__input"
      [placeholder]="'Enter Title' | translate"
      [(ngModel)]="title"
    />
    }@else {
    <h4 class="ifp-kpi-card__input">
      {{ title() | translate }}
    </h4>
    }
  </div>
  <div class="ifp-kpi-card__body">
    <textarea
      appIfpInputAutoResize
      class="ifp-kpi-card__input ifp-kpi-card__input--textarea"
      rows="1"
      [placeholder]="'Enter description' | translate"
      [(ngModel)]="description"
    ></textarea>
    @if (filters.length) {

    <ng-container [formGroup]="filtersForm">
      @for (filter of filters; track i; let i = $index) {
      <app-ifp-dropdown
        [output]="'value'"
        [singleDefaultSelect]="false"
        [leftEnable]="$last"
        [isMulti]="true"
        [searchEnable]="true"
        [selectAllBox]="true"
        [key]="'value'"
        [outputKey]="'value'"
        class="ifp-pdt-lib__dropdown"
        [selectedValue]="filter.defaultValue.value"
        [formControlName]="'filter_' + i"
        [dropDownItems]="filter.filterOptions"
        [placeHolder]="''"
        [showTitle]="true"
        [title]="filter.filterLabel"
        [disableDropdown]="isDropdownDisabled"
        (dropDownItemMultiClicked)="onDropdownValueChange($event, 0)"
      ></app-ifp-dropdown>
      }
    </ng-container>
    }
    @if (legends.length) {
    <div class="ifp-kpi-card__legends">
      <label class="ifp-kpi-card__legends-label">Legend:</label>
      <app-ifp-dropdown
        [output]="'full'"
        [singleDefaultSelect]="false"
        [isMulti]="false"
        [key]="'name'"
        [dropDownItems]="legends"
        [placeHolder]="'Select Legend'"
        [showTitle]="false"
        [selectedValue]="defaultLegend"
        [disableDropdown]="false"
        [disableSingleValue]="false"
        [isInline]="false"
        [formDisable]="false"
        (dropDownItemClicked)="onDefaultLegendChange($event)"
        class="ifp-kpi-card__legend-dropdown"
      ></app-ifp-dropdown>
    </div>
    } @if(graphData()&&graphData().category) { @if(chartName()=='circular'){
    <ifp-dxp-charts
      class="ifp-kpi-card__chart"
      [isCircular]="true"
      [chartName]="'barChart'"
      [height]="250"
      [category]="graphData().category"
      [data]="graphData().series"
      [xAxisLabel]="xAxisLabel"
      [yaxisLabel]="yAxisLabel"
    ></ifp-dxp-charts>
    }@else if (chartName()=='doughnut') {
    <ifp-dxp-charts
      class="ifp-kpi-card__chart"
      [isCircular]="true"
      [chartName]="'donutChart'"
      [height]="250"
      [category]="graphData().category"
      [data]="graphData().series"
      [xAxisLabel]="xAxisLabel"
      [yaxisLabel]="yAxisLabel"
    ></ifp-dxp-charts>
    }@else if(chartName()=='pie'){
    <ifp-dxp-charts
      class="ifp-kpi-card__chart"
      [category]="graphData().category"
      [data]="graphData().series"
      [chartName]="'pie'"
      [xAxisLabel]="xAxisLabel"
      [yaxisLabel]="yAxisLabel"
    ></ifp-dxp-charts>
    }
     @else {
    <ifp-dxp-charts
      class="ifp-kpi-card__chart"
      [chartName]="chartName() == 'Pie' ? 'pieChart' : chartName()"
      [height]="250"
      [category]="graphData().category"
      [data]="graphData().series"
      [xAxisLabel]="xAxisLabel"
      [yaxisLabel]="yAxisLabel"
    ></ifp-dxp-charts>
    } }
  </div>
</div>
}

