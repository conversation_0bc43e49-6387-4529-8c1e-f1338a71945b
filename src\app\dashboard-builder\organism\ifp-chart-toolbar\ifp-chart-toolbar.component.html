<div class="ifp-db-toolbar handle">
  <div class="ifp-db-toolbar__head">
    <h2 class="ifp-db-toolbar__head-title">{{'Toolbar' | translate}}</h2>

    <!-- <div class="ifp-db-toolbar__right">
      <em class="ifp-icon ifp-icon-pin ifp-db-toolbar__pin"
        [ngClass]="{'ifp-db-toolbar__pin--active' : isPinned, 'ifp-db-toolbar__pin--disabled' : selectedDockItem?.key !== 'drag'}"
        [appIfpTooltip]="'Pin the toolbar' | translate" (click)="pinDashboard()" [zIndex]="2000"></em>
      <div class="ifp-db-toolbar__selected-wrapper" (click)="isDockDropdDown =!isDockDropdDown">
        <em class="ifp-icon {{selectedDockItem?.icon}}" [appIfpTooltip]="'Dock Positions' | translate"
          [zIndex]="2000"></em><em class="ifp-icon ifp-icon-triangle"></em>
        @if (isDockDropdDown) {
        <ul class="ifp-db-toolbar__ul">
          @for (option of dockItems; track option) {
          <li class="ifp-db-toolbar__item" (click)="selectDockItem(option)"> <em
              class="ifp-icon {{option?.icon}}"></em><span class="ifp-db-toolbar__key">{{defualtLang == 'en' ?
              option?.name_en : option.name_ar}}</span></li>
          }
        </ul>
        }
      </div>
    </div> -->

  </div>
  <ul class="ifp-db-toolbar__tab">
    @for (item of tabMenuTypes; track item; let index = $index) {
    <li class="ifp-db-toolbar__tab-item" (mouseup)="toolbarTabSelect(item, index)"
      (touchstart)="toolbarTabSelect(item, index)" [ngClass]="{'ifp-db-toolbar__tab-item--active': selectedTab === index, 'ifp-db-toolbar__tab-item--active-prev': index === (selectedTab - 1), 'ifp-db-toolbar__tab-item--disabled': item?.disabled
      }">
      <em class="ifp-icon" [class]="item.icon"></em>
      <p class="ifp-db-toolbar__tab-title">{{item.title_en}}</p>
    </li>
    }
  </ul>

  <div class="ifp-db-toolbar__tab-content">
    @for(content of selectedTabItem.content; let i = $index; track content.key) {
    <ifp-accordion [heading]="content.title | translate" class="ifp-db-toolbar__accordion"
      (selectAccordian)="expandAccordian($event,content)" [isExpanded]="content.isExpand" [ngClass]="{'ifp-db-toolbar__accordion--disabled' : content.key == 'coordinates' &&
      (selectedVisualization == 'pie' || selectedVisualization == 'doughnut')}">
      <div class="ifp-accordion__body-content">
        @switch(content.key) {
        @default {}
        @case('chartsType') {
        <div class="ifp-db-toolbar__chart-set">
          @for (chart of chartTypes; track chart.key; let i = $index) {
          <ifp-icon-selector [icon]="chart" (selectIcon)="updateValues($event, 'chartType')"
            class="ifp-db-toolbar__chart-icon"></ifp-icon-selector>
          }
        </div>
        }
        @case('chartStyle') {
        <ifp-db-chart-style (updateSpacingValueEmitter)="updateValues($event, 'chartStyle')"
          [selectedCard]="selectedCard" [cntType]="cntType" (updateXaxisPosition)="updateValues($event, 'xAxisPos')"
          [chartSeies]="chartSeries" [seriesTitles]="seriesTitles" [seriesColors]="seriesColors"
          (updateLineColor)="updateValues($event, 'lineColor')"></ifp-db-chart-style>
        }
        @case('legend') {
        <ifp-chart-legend-props (updateLegendPosition)="updateValues($event, 'legend')" [selectedCard]="selectedCard"
          [cntType]="cntType" (updateLegendEnable)="updateValues($event, 'legendEnable')"></ifp-chart-legend-props>
        }
        @case('library') {
        <ifp-db-icon-library [iconSet]="iconLibrary" [tagList]="tagList" (selectIcon)="updateValues($event, 'icon')"
          [selectedCard]="selectedCard" [cntType]="cntType"></ifp-db-icon-library>
        }
        @case ('cardTitle') {
        <ifp-ifp-db-text [textAreaTitle]="'Card Title' | translate" [titleValue]="cardTitle"
          (updateIndicatorTitle)="updateValues($event, 'cardTitle')"
          (updateTextColor)="updateValues($event, 'textColor')" [selectedTextColor]="selectedTextColor"
          (updateTextFontSize)="updateValues($event, 'textSize')"
          [selectedTextSize]="selectedTextSize"></ifp-ifp-db-text>
        }
        @case ('textArea') {
        <ifp-ifp-db-text [isDescription]="true" (updateDescription)="updateValues($event, 'cardDescription')"
          [textAreaData]="cardDescription" (updateDescriptionColor)="updateValues($event, 'descriptionColor')"
          (updateDescriptionFontSize)="updateValues($event, 'descriptionFontSize')"
          [selectedTextColor]="selectedDescriptionColor"
          [selectedTextSize]="selectedDescriptionTextSize"></ifp-ifp-db-text>
        }
        @case ('chartSettings') {
        <ifp-ifp-chart-settings (updateSetingsValue)="updateValues($event, 'chartSettings')"
          [options]="chartSettingsOption" [selectedCard]="selectedCard" [cntType]="cntType"></ifp-ifp-chart-settings>
        }
        @case ('coordinates') {
        @if (possibleAggregationColumns() && possibleAggregationColumns().length) {
        <ifp-data-aggregation-panel
          [possibleAggregationColumns]="possibleAggregationColumns()" (CreateCardActions)="createPreviewCard($event)"></ifp-data-aggregation-panel>
        }
        }
        @case ('widgets') {
        <ifp-db-value-card-templates></ifp-db-value-card-templates>
        }
        }
      </div>
    </ifp-accordion>


    }

    <!-- start multi dimentional filter -->
    @if (selectedTabItem?.key === 'filter') {
    <ifp-ifp-chart-card-filter class="ifp-db__toolbar-panel" [filters]="selectedCardData?.filterPanel?.properties"
      [selectedCard]="selectedCard" [cntType]="cntType" [visualizationData]="selectedCardData"
      [visualDataNames]="allDropDownData" [allChartData]="selectedAllCardData"
      (closeFilter)="closeModelToolbar($event)"></ifp-ifp-chart-card-filter>
    }
  </div>
</div>
