
export interface PageInterface {
  label: string;
  pageList: PageListInterface[];
}

export interface PageListInterface {
  key: string;
  title: string;
  sortOrder: number;
  hideDynamically: boolean;
  hidden: boolean;
  configurations: any[] | Configurations;
}
export interface Configurations {
  limit: string;
  per_page: string;
}

export interface ListingPages {
  listingType: string;
  listingLimit: string;
  listingLabel: string;

}

export interface IndicatorList {
  indicatorId: string;
  contentType: string;
  appType: string;
  type: string;
}

export interface PieChartSeries {
  xAxisPositions?: { xAxisPos: string; };
  type: string,
  data: PieChartData[],
  spacing?: any;
  legendPositions?: any;
  isDatalabel?:boolean;
  isPrecise?: boolean
}

export interface PieChartData {
  name: string,
  y: number
}

export interface DomainsList {
  name:string
}

export interface ClassificationList {
  categories?: [],
  datkIconPath: string,
  id: string,
  key: string,
  lightIconPath: string,
  name: string,
  nodeCount: number,
  items?: []
}

export interface SunburstChartData {
  color: string;
  id: string;
  name: string;
  parent: string;
  value: number;
  change?: number;
  contribution?: number;
  ogValue?: number;
  spacing?: any;
}

