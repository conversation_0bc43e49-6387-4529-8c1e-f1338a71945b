import { NgClass } from '@angular/common';
import { Component, EventEmitter, HostListener, Input, OnChanges, OnInit, Output, SimpleChanges, forwardRef, OnDestroy, ViewChild, ElementRef } from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';
import { IfpCheckBoxComponent } from 'src/app/scad-insights/ifp-widgets/ifp-atoms/ifp-check-box/ifp-check-box.component';
import { DbDropDown } from './ifp-db-dropdown.interface';
import { ControlValueAccessor, FormControl, NG_VALUE_ACCESSOR } from '@angular/forms';
import { SubSink } from 'subsink';
import { commonDataTypes } from 'src/app/ifp-analytics/data-prep/ifp-data-prep/ifp-data-prep.constant';


@Component({
    selector: 'app-ifp-db-dropdown',
    templateUrl: './ifp-db-dropdown.component.html',
    styleUrls: ['./ifp-db-dropdown.component.scss'],
    imports: [TranslateModule, NgClass, IfpCheckBoxComponent],
    providers: [
        {
            provide: NG_VALUE_ACCESSOR,
            multi: true,
            useExisting: forwardRef(() => IfpDbDropdownComponent)
        }
    ]
})
export class IfpDbDropdownComponent implements OnChanges, ControlValueAccessor, OnInit, OnDestroy {

  @ViewChild('drop') drop!: ElementRef;
  @HostListener('window:pointerup', ['$event.target'])
  onOutsideClick(target: HTMLElement) {
    if (this.drop) {
      if (!this.drop.nativeElement.contains(target)) {
        if (this.showList) {
          this.showList = false;
          this.close.emit();
        }
      }
    }
  }


  @Input() title!: string;
  @Input() isMultiSelect: boolean = true;
  @Input() options?: any[] = [];
  @Input() placeholder: string = 'Select';
  @Input() selectedSingleItem!: any;
  @Input() key!: string;
  @Input() defaultSelect: boolean = true;
  @Input() optionAlias: boolean = false;
  @Input() multipleSelectedItems: any = [];
  @Input() disableTranslation: boolean = false;
  @Input() iconEnable: boolean = false;
  @Input() formControls: FormControl = new FormControl();
  @Output() singleSelected: EventEmitter<any> = new EventEmitter<any>();
  @Output() multiSelected: EventEmitter<any[]> = new EventEmitter<any[]>();
  @Output() close: EventEmitter<void> = new EventEmitter<void>();


  public showList: boolean = false;
  public childElement!: HTMLElement | undefined;
  public subs = new SubSink();
  public commmonDataTypes: any = commonDataTypes;

  onChange = (_value: string) => {
    // chnage event
  };

  onTouched = () => {
    // touch event
  };

  writeValue(value: string): void {
    this.selectedSingleItem = value;
    if ( this.options && this.options?.length > 0 && this.defaultSelect && !this.selectedSingleItem) {
      this.selectedSingleItem = this.options[0];
      if (this.isMultiSelect && this.multipleSelectedItems?.length <= 0) {
        this.selectedSingleItem.checked = true;
        this.multipleSelectedItems.push(this.selectedSingleItem);
      }
    }
    if (this.options?.length == 1 && this.defaultSelect) {
      this.selectedSingleItem = this.options[0];
    }
    if (this.selectedSingleItem) {
      this.onChange(this.selectedSingleItem);
    }
  }

  registerOnChange(fn: any): void {
    this.onChange = fn;
  }

  registerOnTouched(fn: any): void {
    this.onTouched = fn;
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['options']) {
      if (this.options && this.options?.length > 0 && this.defaultSelect && !this.selectedSingleItem) {
        this.selectedSingleItem = this.options[0];
        this.formControls.setValue(this.selectedSingleItem);
        if (this.isMultiSelect && this.multipleSelectedItems?.length <= 0) {
          this.selectedSingleItem.checked = true;
          this.multipleSelectedItems.push(this.selectedSingleItem);
          this.formControls.setValue(this.multipleSelectedItems);
        }
      }
      if (this.options?.length == 1) {
        this.selectedSingleItem = this.options[0];
      }
    }
    
    // Handle external updates to multipleSelectedItems
    if (changes['multipleSelectedItems'] && this.isMultiSelect && this.options) {
      // Update the checked state of all options based on multipleSelectedItems
      this.options.forEach(option => {
        const isSelected = this.multipleSelectedItems?.some((selectedItem: any) => 
          selectedItem[this.key] === option[this.key]
        );
        option.checked = isSelected || false;
      });
    }
  }

  setChecked(event: boolean, item: DbDropDown | any) {
    if (this.isMultiSelect && event && this.multipleSelectedItems?.length >= 3) {
      // Prevent selecting more than 3
      return;
    }
    item.checked = event;
    if (event) {
      this.multipleSelectedItems.push(item);
    } else {
      const index = this.multipleSelectedItems.findIndex((x: { name: string | undefined; }) => x.name == item.name);
      if (index >= 0) {
        this.multipleSelectedItems.splice(index, 1);
      }
    }
    this.onChange(this.multipleSelectedItems);
    this.onTouched();
    this.formControls.setValue(this.multipleSelectedItems);
    this.multiSelected.emit(this.multipleSelectedItems);
  }

  selectSingleItem(item: DbDropDown | any, index: number) {
    this.selectedSingleItem = item;
    item.index = index;
    this.onChange(item);
    this.onTouched();
    this.formControls.setValue(item);
    this.singleSelected.emit(item);
    this.showList = false;
    this.close.emit();
  }

  toggleDropdown() {
    const wasOpen = this.showList;
    this.showList = !this.showList;
    if (wasOpen && !this.showList) {
      this.close.emit();
    }
  }

  getMutiselectedItem(item: Record<string, any>) {
    let isSelected: boolean = false;
    if (this.key && this.multipleSelectedItems?.find((x: Record<string, any>) => x?.[this.key] == item?.[this.key])) {
      isSelected = true;
    }
    return isSelected;
  }

  ngOnInit(): void {
    this.subs.add(this.formControls
      .valueChanges.subscribe(
        (value) => {
          this.selectedSingleItem = value;
        }
      ));

  }

  ngOnDestroy(): void {
    this.subs.unsubscribe();
  }
}
