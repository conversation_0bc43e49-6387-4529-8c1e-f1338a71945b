import { trigger, transition, style, animate, state } from '@angular/animations';

export const slideDownAnimations = trigger('slideDownAnimations', [
  transition(':enter', [
    style({ transform: 'translate(-50%, -22px)', opacity: 0 }),
    animate('300ms',
      style({
        opacity: 1,
        transform: 'translate(-50%, 0)'
      })
    )
  ]),
  transition(':leave', [
    style({ transform: 'translate(-50%, 0)', opacity: 1 }),
    animate('300ms',
      style({
        opacity: 0,
        transform: 'translate(-50%, -22px)'
      })
    )
  ])

]);

export const slideDownDropDownAnimations = trigger('slideDownDropDownAnimations', [
  transition(':enter', [
    style({ transform: 'scaleY(0)  translateX({{transform}}%)', opacity: 0 }),
    animate('200ms',
      style({
        opacity: 1,
        transform: 'scaleY(1)  translateX({{transform}}%)'
      })
    )
  ]),
  transition(':leave', [
    style({ transform: 'scaleY(1)  translateX({{transform}}%)', opacity: 1 }),
    animate('200ms',
      style({
        opacity: 0,
        transform: 'scaleY(0)  translateX({{transform}}%)'
      })
    )
  ])

]);

export const slideLeftToRightDropDownAnimations = trigger('slideLeftToRightDropDownAnimations', [
  transition(':enter', [
    style({ transform: 'scaleX(0) )', opacity: 0 }),
    animate('200ms',
      style({
        opacity: 1,
        transform: 'scaleX(1) )'
      })
    )
  ]),
  transition(':leave', [
    style({ transform: 'scaleX(1)  )', opacity: 1 }),
    animate('200ms',
      style({
        opacity: 0,
        transform: 'scaleX(0)  )'
      })
    )
  ])

]);
export const headerSlide = trigger('headerSlideAnimations', [
  state('true', style({ opacity: 1, transform: 'none' })),
  state('false', style({ opacity: 1, transform: 'none' })),
  transition('false => true', [
    style({ opacity: 0, transform: 'translateY(-100px)' }),
    animate('500ms cubic-bezier(0.35, 0, 0.25, 1)')
  ])
]);

export const fadInOut = trigger('fadeInOut', [
  transition(':enter', [
    style({ opacity: 0 }),
    animate('300ms', style({ opacity: 1 }))
  ]),
  transition(':leave', [
    animate('300ms', style({ opacity: 0 }))
  ])
]);

export const chatbotPanelAnimation = trigger('chatbotPanelAnimation', [
  transition(':enter', [
    style({ transform: 'scale(0)', opacity: 0 }),
    animate('300ms',
      style({
        opacity: 1,
        transform: 'scale(1)',
        transformOrigin: 'bottom'
      })
    )
  ]),
  transition(':leave', [
    style({ transform: 'scale(1)', opacity: 1 }),
    animate('300ms',
      style({
        opacity: 0,
        transform: 'scale(0)'
      })
    )
  ])

]);
