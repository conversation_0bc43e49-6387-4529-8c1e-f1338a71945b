@use '../../../../assets/ifp-styles/abstracts' as *;

:host {
  display: block;
  background-color: $ifp-color-white;
  padding: $spacer-4;
  border-radius: 20px;
  box-shadow: 0 64px 65px -50px $ifp-color-black-08;
}

.ifp-chart-table {
  &__header-sec {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  &__chart-wrapper {
    display: flex;
    align-items: center;
    justify-content: space-around;
    margin: $spacer-2 $spacer-0 $spacer-4;
  }

  &__title {
    font-size: $ifp-fs-6;
    font-weight: $fw-bold;
  }

  &__status-item {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    justify-content: space-between;
    background-color: $ifp-color-pale-grey-50;
    padding: $spacer-2 $spacer-3;
    z-index: 9999;
    border-radius: 8px;
    // &--selected {
    //   background-color: $ifp-color-active-blue;
    //   color: $ifp-color-white;

    //   .ifp-chart-table__status-text {
    //     color: $ifp-color-white;
    //   }
    // }
  }

  &__dot {
    height: 10px;
    width: 10px;
    border-radius: 50%;
    display: inline-block;
    margin-inline-end: $spacer-2;
  }

  &__status-text {
    font-size: $ifp-fs-3;
    color: $ifp-color-grey-9;
  }

  &__status-value {
    font-size: $spacer-4;
    font-weight: $fw-bold;
    padding-inline-start: $spacer-3;
  }

  &__status-list-wrapper {
    margin-inline-start: $spacer-5;
    border-radius: 10px;
  }

  &__table-wrapper {
    border: 1px solid $ifp-color-grey-3;
    padding: $spacer-2 $spacer-1;
    border-radius: 0 20px 20px 20px;
    min-height: 480px;
  }

  &__btn {
    border: 1px solid $ifp-color-blue-hover;
    padding: $spacer-2 ($spacer-2 + 4px) $spacer-2 $spacer-3;
    border-radius: 35px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    color: $ifp-color-blue-hover;
    margin-inline-start: $spacer-3;
    cursor: pointer;
    transition: 0.3s;
    .ifp-icon {
      margin-left: $spacer-2;
      color: inherit;
      position: relative;
      top: 1px;
      transition: 0.3s;
    }
    &:hover {
      background-color: $ifp-color-blue-hover;
      color: $ifp-color-white-global;
    }
  }

  &__download-btn {
    height: 35px;
    width: 35px;
    border-radius: 50%;
    border: 1px solid $ifp-color-grey-9;

    .ifp-icon {
      font-size: $ifp-fs-5;
      color: $ifp-color-grey-9;
      position: relative;
      top: 6px;
    }
  }

  &__table {
    display: block;
    max-height: 475px;
    padding: $spacer-0 $spacer-1;
    @include ifp-scroll(transparent, $ifp-color-grey-7, 8px, 8px);
  }

  &__tab {
    display: flex;
    align-items: flex-end;
    margin-bottom: -1px;
  }

  &__tab-text {
    position: relative;
    z-index: 1;
  }

  &__tab-item {
    color: $ifp-color-grey-14;
    text-align: center;
    background-color: $ifp-color-pale-grey;
    min-width: 125px;
    border: 1px solid $ifp-color-grey-13;
    padding: $spacer-1 $spacer-2;
    border-radius: 7px 7px 0 0;
    position: relative;
    cursor: pointer;
    overflow: hidden;
    transition: 0.3s;
    &::before {
      content: "";
      width: 100%;
      height: 100%;
      background-image: linear-gradient(90deg, $ifp-color-grey-16, $ifp-color-pale-grey);
      background-size: 6px;
      background-repeat: no-repeat;
      position: absolute;
      top: 0;
      left: 0;
      transition: 0.3s;
    }
    &:first-child {
      min-width: 65px;
    }
    &:hover {
      &::before {
        background-size: 200%;
      }
    }
    &--selected {
      font-weight: $fw-semi-bold;
      color: $ifp-color-black;
      background-color: transparent;
      background-image: none;
      padding: $spacer-2;
      pointer-events: none;
      border-radius: 10px 10px 0 0;
      border-bottom-color: $ifp-color-white;
      &::before {
        content: none;
      }
    }
  }

}

:host::ng-deep {
  .ifp-chart-table {
    &__table-wrapper {
      .ifp-data-table {
        &__row:last-child {
          border: none;
        }
        &__row--head .ifp-data-table__col{
          white-space: nowrap;
        }
      }
      .ifp-progress-bar {
        padding: $spacer-1 $spacer-0;
      }
    }
  }
}
