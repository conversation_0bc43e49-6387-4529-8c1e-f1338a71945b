@use "../../../../assets/ifp-styles/abstracts/index" as *;
:host {
  display: block;
}
:host::ng-deep {
  .ifp-accord-box {
    &__content {
      padding: $spacer-3;
      border-top: 1px solid $ifp-color-grey-3;
    }
    &--selection {
      .ifp-accord-box__content {
        border-top: none;
      }
    }
    &--error {
      .ifp-accord-box__content {
        border-color: $ifp-color-red;
      }
    }
    &--disabled {
      pointer-events: none;
    }
  }
}
.ifp-accord-box {
  border-radius: 6px;
  border: 1px solid $ifp-color-grey-3;
  &__head-left,
  &__head {
    display: flex;
    align-items: center;
  }
  &__head-left {
    overflow: hidden;
    app-ifp-check-box {
      overflow: hidden;
    }
  }
  &__head {
    font-weight: $fw-medium;
    justify-content: space-between;
    padding: ($spacer-2+2px);
    cursor: pointer;
    .ifp-icon {
      margin-right: $spacer-2;
    }
    &--drag {
      cursor: move;
    }
  }
  &__body {
    max-height: 0;
    overflow: hidden;
    transition: 0.3s;
  }
  &__arrow {
    transition: 0.3s;
  }
  &__title {
    display: flex;
    align-items: center;
    width: 100%;
    .ifp-icon {
      font-size: $ifp-fs-5;
      margin-inline-end: $spacer-2;
    }
  }
  &__edit-icon {
    margin-inline-start: $spacer-2;
    color: $ifp-color-black;
  }
  &__outer {
    &--edit {
      width: calc(100% - 18px);
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }

  &--expand {
    .ifp-accord-box {
      &__body {
        overflow: visible;
        max-height: 1000px;
      }
      &__arrow {
        transform: rotate(180deg);
      }
    }
  }
  &--selection {
    border: none;
    .ifp-accord-box {
      &__head {
        padding: $spacer-4 $spacer-5;
      }
      &__title  {
        font-size: $ifp-fs-4;
        font-weight: $fw-bold;
      }

  }
  }
  &--active {
    border-left:$ifp-color-blue-menu solid 4px;
  }
  &--error {
    border: 1px solid $ifp-color-red;
    .ifp-accord-box {
      &__head {
        color: $ifp-color-red;
      }
      &__content {
        border-top: 1px solid $ifp-color-red;
      }
    }
  }
  &--disabled {
    pointer-events: none;
  }
}

:host-context([dir="rtl"]) {
  .ifp-exp {
    &__summery{
    &--active {
      border-left:unset;
      border-right:$ifp-color-blue-menu solid 4px;
    }

  }
}
.ifp-accord-box {
  &--active {
    border-left:unset;
    border-right:$ifp-color-blue-menu solid 4px;
  }
}
}
