<div class="ifp-about">
  <div class="ifp-container">
    <app-ifp-breadcrumbs [pageData]="pageData"></app-ifp-breadcrumbs>
  </div>

  <ng-container *ngIf="(aboutUsData$ | async) as about">
    <div class="ifp-static">
      <div class="ifp-container">
        <h1 class="ifp-module-heading">{{ about.title ? about.title : '' | translate}}</h1>
        <div class="ifp-about__video-wrapper">
          <video class="ifp-about__video" [poster]="about.header_video_thumbnail ? about.header_video_thumbnail : ''" #aboutVideo (click)="toggleVideo()">
            <source *ngIf="about.header_video_link" [src]="about.header_video_link" type="video/mp4">
          </video>
        </div>
        <div class="ifp-about__container-sm">

          <ng-container *ngIf="about.page_content.length > 0">
            <div class="ifp-about__content" *ngFor="let content of about.page_content">
              <h3 class="ifp-about__title" *ngIf="content.paragraph_title">{{content.paragraph_title | translate}}</h3>
              <p class="ifp-about__desc" *ngIf="content.paragraph_content">{{content.paragraph_content | translate}}</p>
              <p class="ifp-about__short-desc" *ngIf="content.paragraph_short_content">{{content.paragraph_short_content | translate}}</p>
            </div>
          </ng-container>

          <ng-container *ngIf="about.page_images.length > 0">
            <div class="ifp-about__img-sec">
              <div class="ifp-about__tile" *ngFor="let tile of about.page_images">
                <h3 class="ifp-about__title">{{tile.alt ? tile.alt : '' | translate}}</h3>
                <div class="ifp-about__tile-img">
                  <img [src]="tile.url" alt="tile.alt">
                </div>
              </div>
            </div>
          </ng-container>
        </div>
      </div>
    </div>
  </ng-container>
</div>
