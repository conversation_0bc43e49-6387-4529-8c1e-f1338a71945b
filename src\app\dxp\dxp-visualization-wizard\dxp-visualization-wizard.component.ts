import { Component, inject, On<PERSON><PERSON>roy, OnInit, signal, viewChild, ViewChild } from '@angular/core';
import { Router } from '@angular/router';
import { IfpBreadcrumbsComponent } from "../../scad-insights/ifp-widgets/ifp-molecules/ifp-breadcrumbs/ifp-breadcrumbs.component";
import { TranslateModule } from '@ngx-translate/core';
import { IfpButtonComponent } from "../../scad-insights/ifp-widgets/ifp-atoms/ifp-button/ifp-button.component";
import { buttonClass, buttonIconPosition } from '../../scad-insights/core/constants/button.constants';
import { IfpModalComponent } from 'src/app/scad-insights/ifp-widgets/ifp-organism/ifp-modal/ifp-modal.component';
import { DxpPopupComponent } from '../dxp-popup/dxp-popup.component';
import { IfpModalService } from 'src/app/scad-insights/ifp-widgets/ifp-organism/ifp-modal/ifp-modal.service';
import { DxpDatasetCardComponent } from "../dxp-dataset-card/dxp-dataset-card.component";
import { IfpDropdownComponent } from "../../scad-insights/ifp-widgets/ifp-atoms/ifp-dropdown/ifp-dropdown.component";
import { DxpFilterBadgeComponent } from "../widgets/dxp-filter-badge/dxp-filter-badge.component";
import { IfpInfoComponent } from "../../scad-insights/ifp-widgets/ifp-molecules/ifp-heading-with-info/ifp-info.component";
import { DxpLabelDragComponent } from "../widgets/dxp-label-drag/dxp-label-drag.component";
import { CommonModule, NgClass } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { DxpVisualizationPreviewComponent } from "../dxp-visualization-preview/dxp-visualization-preview.component";
import { DxpValidationPopUpComponent } from "../dxp-validation-pop-up/dxp-validation-pop-up.component";
import { DxpUserConfigComponent } from "../dxp-user-config/dxp-user-config.component";
import { ApiService } from 'src/app/scad-insights/core/services/api.service';
import { SubSink } from 'subsink';
import { dxpApi } from '../dxp.constants';
import { ToasterService } from 'src/app/scad-insights/core/services/tooster/ToastrService.service';
import { SelectedProduct } from '../widgets/dxp-accordian/dxp-accordian.component';
import { ConvertedChartData, DxpPlotData, ProductDetail } from '../dxp.interface';
import { FormArray, FormBuilder, FormGroup, ReactiveFormsModule, Validators, AbstractControl } from '@angular/forms';
import { DragDropModule } from '@angular/cdk/drag-drop';
import { IfpCardLoaderComponent } from "src/app/scad-insights/ifp-widgets/ifp-atoms/ifp-card-loader/ifp-card-loader.component";

@Component({
  selector: 'ifp-dxp-visualization-wizard',
  imports: [IfpBreadcrumbsComponent, TranslateModule, ReactiveFormsModule, CommonModule, FormsModule,
    IfpButtonComponent, IfpModalComponent, DxpPopupComponent,
    DxpDatasetCardComponent, IfpDropdownComponent, DxpFilterBadgeComponent,
    IfpInfoComponent, DxpLabelDragComponent,
    NgClass, DxpVisualizationPreviewComponent, DxpValidationPopUpComponent, DxpUserConfigComponent, DragDropModule, IfpCardLoaderComponent],
  templateUrl: './dxp-visualization-wizard.component.html',
  styleUrl: './dxp-visualization-wizard.component.scss'
})

export class DxpVisualizationWizardComponent implements OnInit, OnDestroy{
  public modal = viewChild<IfpModalComponent>('modal');
  public modalPopUp = viewChild<IfpModalComponent>('modalPopUp');
  public userView = signal(false);
  @ViewChild(DxpVisualizationPreviewComponent) previewComponent!: DxpVisualizationPreviewComponent;
  @ViewChild('userConfigComponent') userConfigComponent!: DxpUserConfigComponent;

  public modalService =  inject(IfpModalService);
  private readonly _apiService =  inject(ApiService);
  private readonly _toasterService =  inject(ToasterService);
  private readonly _router = inject(Router);
  private readonly _subs: SubSink = new SubSink();
  public pageData = signal([
    {
      title: 'Home',
      route: '/home',
    },
    {
      title: 'Abu Dhabi Executive Office (ADEO)',
      route: '',
    },
  ]);
  public buttonClass= buttonClass;
  public  buttonIconPosition =  buttonIconPosition;
  public productdetailList: ProductDetail[] = [];
  public selectedProduct: SelectedProduct = {
    sourceAssetId: '',
    sourceProductId: '',
    title: ''
  };
  public dataset = signal([]);
  public fullColumnsData = signal([]);
  public selectedColumn = signal<any>(null);
  public comparators = signal([]);
  public productListLoader: boolean = true;
  public addedFilters = signal<any[]>([]);
  public kpiCardFilters: any[] = [];
  public selectedLegends: any[] = [];
  public datasetLoading = signal(false);
  public graphDatasetLoading = signal(false);


  // Add signals for X and Y variables with aggregation data
  public xVariable = signal<any[]>([]);
  public yVariable = signal<any[]>([]);

  // Add axis label variables for two-way binding
  public xAxisLabel: string = '';
  public yAxisLabel: string = '';

  public dataTypesList = {
    numeric: ['int', 'float', 'double', 'long'],
    character: ['str', 'string', 'char'],
    date: ['date']
  }
  public filterForm: FormGroup;
  public axisForm: FormGroup;
  graphData!: ConvertedChartData;
  public userConfigData = signal<any>(null);
  public selectedFilterIndex: number = -1;
  private filterColumnChangeSub: any = null;

  constructor(private fb: FormBuilder) {
    this.filterForm = this.fb.group({
      filters: this.fb.array([this.createFilterGroup()])
    });
    this.axisForm = this.fb.group({
      xAxisLabel: ['', Validators.required],
      xAxisVariable: this.fb.group({
        name: [''],
        data_type: [''],
        selectedAggregation: ['']
      }),
      yAxisLabel: ['', Validators.required],
      yAxisVariables: this.fb.array([]) // For multiple Y
    });

    // Subscribe to axis form changes to update the axis labels
    this.axisForm.valueChanges.subscribe(values => {
      this.xAxisLabel = values.xAxisLabel || '';
      this.yAxisLabel = values.yAxisLabel || '';
    });
  }

  ngOnInit(): void {
    this.getAssetList();
  }

  get filters(): FormArray {
    return this.filterForm.get('filters') as FormArray;
  }

  createFilterGroup(): FormGroup {
    const columnsData = this.fullColumnsData();
    const defaultColumn = columnsData.length > 0 ? columnsData[0] : '';
    const defaultComparator = defaultColumn && (defaultColumn as any).available_comparators && (defaultColumn as any).available_comparators.length > 0
      ? (defaultColumn as any).available_comparators[0] : '';

    return this.fb.group({
      column: [defaultColumn, Validators.required],
      comparator: [defaultComparator, Validators.required],
      value: ['', Validators.required]
    });
  }
  onDropdownSelect(index: number, controlName: string, value: any) {
    this.filters.at(index).get(controlName)?.setValue(value);
  }
  onEditFilter(index: number) {
    this.selectedFilterIndex = index;
    const filter = this.addedFilters()[index];
    // Patch the form with the selected filter's data
    this.filters.at(0).patchValue({
      column: filter.column,
      comparator: filter.comparator,
      value: filter.value
    });
    // Always set comparator to the first available comparator for the selected column
    const filterGroup = this.filters.at(0);
    const column = filter.column;
    if (column && column.available_comparators && column.available_comparators.length > 0) {
      filterGroup.get('comparator')!.setValue(column.available_comparators[0]);
    } else {
      filterGroup.get('comparator')!.setValue('');
    }
    // Subscribe to column changes to update comparator
    if (this.filterColumnChangeSub) {
      this.filterColumnChangeSub.unsubscribe();
    }
    this.filterColumnChangeSub = filterGroup.get('column')!.valueChanges.subscribe((column) => {
      if (column && column.available_comparators && column.available_comparators.length > 0) {
        filterGroup.get('comparator')!.setValue(column.available_comparators[0]);
      } else {
        filterGroup.get('comparator')!.setValue('');
      }
    });
  }

  addFilter(): void {
    const currentFilter = this.filters.at(0);
    const filterData = {
      column: currentFilter.get('column')?.value,
      comparator: currentFilter.get('comparator')?.value,
      value: currentFilter.get('value')?.value
    };

    if (this.selectedFilterIndex >= 0) {
      // Update existing filter
      this.addedFilters.update(filters => {
        const updated = [...filters];
        updated[this.selectedFilterIndex] = filterData;
        return updated;
      });
      this.selectedFilterIndex = -1; // Exit edit mode
    } else if (this.canAddMoreFilters()) {
      // Add new filter
      this.addedFilters.update(filters => [...filters, filterData]);
    }

    // Reset the form to default values
    this.resetFilterForm();
  }

  resetFilterForm(): void {
    const filter = this.filters.at(0);
    const columnsData = this.fullColumnsData();
    const defaultColumn = columnsData.length > 0 ? columnsData[0] : '';
    const defaultComparator = defaultColumn && (defaultColumn as any).available_comparators && (defaultColumn as any).available_comparators.length > 0
      ? (defaultColumn as any).available_comparators[0] : '';

    filter.patchValue({
      column: defaultColumn,
      comparator: defaultComparator,
      value: ''
    });
  }

  clearFilters(): void {
    // Clear all existing filters
    while (this.filters.length !== 0) {
      this.filters.removeAt(0);
    }
    // Add back one default empty filter group
    this.filters.push(this.createFilterGroup());
    // Clear the added filters signal
    this.addedFilters.set([]);
  }

  isFilterValid(filterIndex: number): boolean {
    const filter = this.filters.at(filterIndex);
    const column = filter.get('column')?.value;
    const comparator = filter.get('comparator')?.value;
    const value = filter.get('value')?.value;

    return column && comparator && value && value.toString().trim() !== '';
  }

  canAddMoreFilters(): boolean {
    return this.addedFilters().length < 3;
  }

  isAddButtonDisabled(): boolean {
    // Check if the current filter is complete and we haven't reached the limit
    const currentFilter = this.filters.at(0);
    return !this.isFilterValid(0) || !this.canAddMoreFilters();
  }

  removeFilter(index: number): void {
    // Remove the filter at the specified index from the addedFilters signal
    this.addedFilters.update(filters => filters.filter((_, i) => i !== index));
  }

  getAssetList(searchKey: string = '') {
    this.productListLoader = true;
    this._subs.add(
      this._apiService.getMethodRequest(dxpApi.product, {search: searchKey}).subscribe({
        next: (resp) => {
          this.productdetailList = resp;
          this.productListLoader = false;
        },
        error: (error) => {
          this._toasterService.error(error?.error?.message);
          this.productListLoader = false;
        }
      })
    )
  }

  onSearchAsset(key: string) {
    this.getAssetList(key);
  }

  ngAfterViewInit(): void {
    this.createKpi();
  }

  openPopup() {
    this.userView.set(true);
    this.userConfigData.set({
      payloadData: this.buildPayloadData(),
      onAppliedFilter: this.previewComponent?.cardFilters || [],
      legends: this.previewComponent?.cardLegends || [],
      defaultLegend: this.previewComponent?.defaultLegend || null,
      graphData: this.graphData,
      title: this.previewComponent?.cardTitle?.() ?? '',
      description: this.previewComponent?.cardDescription?.() ?? ''
    });
    // this.modalPopUp()?.createElement();
  }
  closePopup() {
    this.modalPopUp()?.removeModal();
  }

  createKpi() {
    this.modal()?.createElement();
  }
  closeModal() {
    this.modal()?.removeModal();
    this.modalService.removeAllModal();
  }

  getFullColumnsData() {
    this._subs.add(
      this._apiService.getMethodRequest(`${dxpApi.column(this.selectedProduct.sourceProductId)}${this.selectedProduct.sourceAssetId}/columns`).subscribe({
        next: (data) => {
          this.fullColumnsData.set(data);
          if (data && data.length > 0) {
            this.onColumnSelected(data[0]);
            // Set default values for existing filters
            this.setDefaultValuesForExistingFilters();
          }
        },
        error: error => {
          this._toasterService.error(error?.error?.message);
        }
      })
    )
  }

  setDefaultValuesForExistingFilters() {
    const columnsData = this.fullColumnsData();
    if (columnsData.length > 0) {
      const defaultColumn = columnsData[0];
      const defaultComparator = (defaultColumn as any).available_comparators && (defaultColumn as any).available_comparators.length > 0
        ? (defaultColumn as any).available_comparators[0] : '';

      // Update existing filters with default values if they don't have column selected
      for (let i = 0; i < this.filters.length; i++) {
        const filter = this.filters.at(i);
        if (!filter.get('column')?.value) {
          filter.get('column')?.setValue(defaultColumn);
          filter.get('comparator')?.setValue(defaultComparator);
        }
      }
    }
  }

  getDataset() {
    this.datasetLoading.set(true);
    this._subs.add(
      this._apiService.getMethodRequest(`${dxpApi.column(this.selectedProduct.sourceProductId)}${this.selectedProduct.sourceAssetId}/columns`).subscribe({
        next: (data) => {
          // Store the full column data with aggregations
          this.dataset.set(data.map((item: any) => ({
            ...item,
            name: item.name,
            icon: this.getDataTypeIcon(item.data_type),
            data_type: item.data_type,
            possible_aggregations: item.possible_aggregations
          })));
          this.datasetLoading.set(false);
        },
        error: error => {
          this._toasterService.error(error?.error?.message);
          this.datasetLoading.set(false);

        }
      })
    )
  }

  getDataTypeIcon(dataType: string) {
    const type = dataType.toLowerCase();
    if (this.dataTypesList.numeric.includes(type)) {
      return 'ifp-icon-number-type'
    } else if (this.dataTypesList.character.includes(type)) {
      return 'ifp-icon-char-type'
    } else {
      return 'ifp-icon-calender'
    }
  }

  addDataset() {
    this.createKpi();
  }

  onSelectedProduct(selectedProduct: SelectedProduct) {
    this.selectedProduct = selectedProduct;
    this.getDataset();
    this.getFullColumnsData();
    this.closeModal();
  }

  onColumnSelected(column: any) {
    this.selectedColumn.set(column);
    this.comparators.set(column?.available_comparators || []);
  }

  onColumnSelectedForFilter(column: any, filterIndex: number) {
    const filter = this.filters.at(filterIndex);
    filter.get('column')?.setValue(column);
    // Reset comparator when column changes
    const defaultComparator = column?.available_comparators && column.available_comparators.length > 0
      ? column.available_comparators[0] : '';
    filter.get('comparator')?.setValue(defaultComparator);
  }

  getComparatorsForColumn(column: any) {
    if (!column) return [];
    return column.available_comparators || [];
  }

  // Handle drag and drop for X variable
  onXVariableDrop(event: any) {
    if (event.previousContainer !== event.container) {
      const draggedItem = event.previousContainer.data[event.previousIndex];
      const defaultAggregation = (draggedItem.possible_aggregations?.x_aggregations && draggedItem.possible_aggregations.x_aggregations.length > 0)
        ? draggedItem.possible_aggregations.x_aggregations[0]
        : '';
      const itemWithAggregations = {
        ...draggedItem,
        aggregations: draggedItem.possible_aggregations?.x_aggregations || [],
        selectedAggregation: defaultAggregation
      };
      this.xVariable.set([itemWithAggregations]);
      // Update axisForm xAxisVariable
      this.axisForm.get('xAxisVariable')?.patchValue({
        name: itemWithAggregations.name,
        data_type: itemWithAggregations.data_type,
        selectedAggregation: defaultAggregation
      });
    }
  }

  // Handle drag and drop for Y variable
  onYVariableDrop(event: any) {
    if (event.previousContainer !== event.container) {
      const draggedItem = event.previousContainer.data[event.previousIndex];
      const defaultAggregation = (draggedItem.possible_aggregations?.y_aggregations && draggedItem.possible_aggregations.y_aggregations.length > 0)
        ? draggedItem.possible_aggregations.y_aggregations[0]
        : '';
      const itemWithAggregations = {
        ...draggedItem,
        aggregations: draggedItem.possible_aggregations?.y_aggregations || [],
        selectedAggregation: defaultAggregation
      };
      // Only allow one Y variable: replace any existing item
      this.yVariable.set([itemWithAggregations]);
      // Update axisForm yAxisVariables FormArray
      const yAxisVariables = this.axisForm.get('yAxisVariables') as FormArray;
      yAxisVariables.clear();
      yAxisVariables.push(this.fb.group({
        name: [itemWithAggregations.name],
        data_type: [itemWithAggregations.data_type],
        selectedAggregation: [itemWithAggregations.selectedAggregation || '']
      }));
    }
  }

  // Remove item from X variable
  removeXVariable(index: number) {
    this.xVariable.set([]);
  }

  // Remove item from Y variable
  removeYVariable(index: number) {
    this.yVariable.update(current => {
      const newArray = [...current];
      newArray.splice(index, 1);
      return newArray;
    });
  }

  // Handle aggregation selection for X variable
  onXVariableAggregationSelected(aggregation: string) {
    // Update the form control for X variable aggregation
    const xAxisVariable = this.axisForm.get('xAxisVariable');
    if (xAxisVariable) {
      xAxisVariable.get('selectedAggregation')?.setValue(aggregation);
    }
    // Also update the signal for UI sync if needed
    this.xVariable.update(current => {
      if (current.length > 0) {
        const updatedItem = { ...current[0], selectedAggregation: aggregation };
        return [updatedItem];
      }
      return current;
    });
  }

  // Handle aggregation selection for Y variable
  onYVariableAggregationSelected(aggregation: string, index: number) {
    // Update the form control for Y variable aggregation
    const yAxisVariables = this.axisForm.get('yAxisVariables') as FormArray;
    if (yAxisVariables && yAxisVariables.at(index)) {
      yAxisVariables.at(index).get('selectedAggregation')?.setValue(aggregation);
    }
    // Also update the signal for UI sync if needed
    this.yVariable.update(current => {
      const newArray = [...current];
      if (newArray[index]) {
        newArray[index] = { ...newArray[index], selectedAggregation: aggregation };
      }
      return newArray;
    });
  }

  // Method to receive filters from preview component
  onKpiCardFiltersChanged(filters: any[]) {
    console.log('Wizard received filters from Preview:', filters);
    this.kpiCardFilters = filters;
    // this.onSubmitClick()
  }

  onSubmitClick() {
    this.graphDatasetLoading.set(true);
    // Get form values
    const axisFormValue = this.axisForm.value;
    console.log(this.addedFilters());
    console.log('Wizard kpiCardFilters at submit:', this.kpiCardFilters);

    // Merge addedFilters and kpiCardFilters
    const filtersArray = [
      ...this.addedFilters().map(f => ({
        column: typeof f.column === 'object' && f.column !== null ? f.column.name : f.column,
        comparator: typeof f.comparator === 'object' && f.comparator !== null ? f.comparator.value || f.comparator.display_name : f.comparator,
        data_type: typeof f.column === 'object' && f.column !== null ? f.column.data_type : '',
        value: f.value
      })),
      ...((this.kpiCardFilters || []).map(f => ({
        column: f.column,
        comparator: 'in',
        value: (f.value !== undefined && f.value !== null && f.value !== '') ? f.value :  [f.defaultValue.value],
        data_type: f.data_type || ''
      })))
    ];

    // Build X axis from form
    const xAxisVariable = axisFormValue.xAxisVariable;
    const x_axis = {
      label: axisFormValue.xAxisLabel,
      axis: xAxisVariable && xAxisVariable.name ? {
        column: xAxisVariable.name,
        data_type: xAxisVariable.data_type,
        aggregator: xAxisVariable.selectedAggregation || ''
      } : null
    };

    // Build Y axis from form
    const yAxisVariables = axisFormValue.yAxisVariables || [];
    const yVar = yAxisVariables[0] || {};
    const y_axis = {
      label: axisFormValue.yAxisLabel,
      axis: yVar && yVar.name ? {
        column: yVar.name,
        data_type: yVar.data_type,
        aggregator: yVar.selectedAggregation || ''
      } : null
    };

    // Build legends payload - always send only the first chosen item
    // const legends = this.selectedLegends.map(legend => ({
    //   column: legend.name,
    //   data_type: legend.data_type || 'string'
    // }));
    const legends = this.selectedLegends.length > 0 ? [{
      column: this.selectedLegends[0].name,
      data_type: this.selectedLegends[0].data_type || 'string'
    }] : [];

    const payload = {
      filters: {
        groups: [
          {
            conditions: filtersArray,
            operator: 'and'
          }
        ],
        global_operator: 'and'
      },
      x_axis,
      y_axis,
      legends: legends
    };

    // Log the form values and the payload
    console.log('Axis Form Values:', axisFormValue);
    console.log('Payload:', payload);

    // --- API call logic ---
    const productId = this.selectedProduct.sourceProductId;
    const assetId = this.selectedProduct.sourceAssetId;
    if (!productId || !assetId) {
      console.error('Product ID or Asset ID is missing!');
      return;
    }
    const apiUrl = dxpApi.query(productId, assetId);
    this.graphData={category: [], series: [], xAxisLabel: '', yAxisLabel: ''};
    this._subs.add(
      this._apiService.postMethodRequest(apiUrl, payload).subscribe({
        next: (response:DxpPlotData) => {
          // this.graphData = response;
          this.graphData = this.transformChartData(response);
           this.graphDatasetLoading.set(false);
          console.log('API Response:', response);
        },
        error: (error) => {
          console.error('API Error:', error);
          this.graphDatasetLoading.set(false);
        }
      })
    );
  }

  transformChartData(apiData: DxpPlotData): ConvertedChartData {
  const category: string[] = apiData.series.xAxis.categories;
  const series = apiData.series.series.map((s) => ({
    name: s.name,
    data: s.data
  }));
  const xAxisLabel = apiData.xAxis;
  const yAxisLabel = apiData.yAxis;
  return { category, series, xAxisLabel, yAxisLabel };
}

  ngOnDestroy(): void {
    this._subs.unsubscribe();
    this.closeModal();
    if (this.filterColumnChangeSub) {
      this.filterColumnChangeSub.unsubscribe();
    }
  }

  // Getter for xAxisVariable FormGroup
  get xAxisVariableControl(): FormGroup {
    return this.axisForm.get('xAxisVariable') as FormGroup;
  }

  // Getter for yAxisVariables FormArray
  get yAxisVariablesControl(): FormArray {
    return this.axisForm.get('yAxisVariables') as FormArray;
  }

  // Handler to be called by the preview when filters are changed/removed
  onPreviewSubmitRequested() {
    this.onSubmitClick();
  }

  // Handler to receive legends data from toolbar component
  onLegendsDataReceived(legends: any[]) {
    this.selectedLegends = legends;
    console.log('Legends data received in wizard:', legends);
  }

  submitForApproval() {
    console.log('Send for Approval button clicked');
    const axisFormValue = this.axisForm.value;
    const xAxisVariable = axisFormValue.xAxisVariable;
    const yAxisVariables = axisFormValue.yAxisVariables || [];
    const yVar = yAxisVariables[0] || {};
    const payload = {
      title: this.userConfigComponent?.userConfig()?.title ?? '',
      subTitle: this.userConfigComponent?.userConfig()?.description ?? '',
      sourceAssetId: this.selectedProduct.sourceAssetId,
      sourceProductId: this.selectedProduct.sourceProductId,
      visualizationConfig: {
        source_filter: {
          groups: [
            {
              conditions: this.addedFilters().map(f => ({
                column: typeof f.column === 'object' && f.column !== null ? f.column.name : f.column,
                comparator: typeof f.comparator === 'object' && f.comparator !== null ? f.comparator.value || f.comparator.display_name : f.comparator,
                data_type: typeof f.column === 'object' && f.column !== null ? f.column.data_type : '',
                value: f.value
              })),
              operator: 'and'
            }
          ],
          global_operator: 'and'
        },
        chart_configuration: {

          x_axis : {
            label: axisFormValue.xAxisLabel,
            axis: xAxisVariable && xAxisVariable.name ? {
              column: xAxisVariable.name,
              data_type: xAxisVariable.data_type,
              aggregator: xAxisVariable.selectedAggregation || ''
            } : null
          },
          y_axis : {
            label: axisFormValue.yAxisLabel,
            axis: yVar && yVar.name ? {
              column: yVar.name,
              data_type: yVar.data_type,
              aggregator: yVar.selectedAggregation || ''
            } : null
          },
          filterPanel: [...((this.kpiCardFilters || []).map(f => ({
            column: f.column,
            comparator: 'in',
            value: (f.value !== undefined && f.value !== null && f.value !== '') ? f.value :  [f.defaultValue.value],
            data_type: f.data_type || ''
          })))]
        }
              },
        userIds: this.userConfigComponent?.sharedUserListSelected()?.map(user => user.id) || []
    };

    this._subs.add(
      this._apiService.postMethodRequest(dxpApi.listEntityKpi, payload).subscribe({
        next: (response) => {
          console.log('Entity KPI submitted successfully:', response);
          this._toasterService.success('Entity KPI submitted for approval successfully');
          this._router.navigate(['/dxp'], { queryParams: { subTab: 3 } });
        },
        error: (error) => {
          console.error('Error submitting Entity KPI:', error);
          this._toasterService.error(error?.error?.message || 'Failed to submit Entity KPI for approval');
        }
      })
    );
  }

  buildPayloadData() {
    const axisFormValue = this.axisForm.value;
    // Merge addedFilters and kpiCardFilters
    const filtersArray = [
      ...this.addedFilters().map(f => ({
        column: typeof f.column === 'object' && f.column !== null ? f.column.name : f.column,
        comparator: typeof f.comparator === 'object' && f.comparator !== null ? f.comparator.value || f.comparator.display_name : f.comparator,
        data_type: typeof f.column === 'object' && f.column !== null ? f.column.data_type : '',
        value: f.value
      })),
      ...((this.kpiCardFilters || []).map(f => ({
        column: f.column,
        comparator: 'in',
        value: (f.value !== undefined && f.value !== null && f.value !== '') ? f.value :  [f.defaultValue.value],
        data_type: f.data_type || ''
      })))
    ];
    const xAxisVariable = axisFormValue.xAxisVariable;
    const x_axis = {
      label: axisFormValue.xAxisLabel,
      axis: xAxisVariable && xAxisVariable.name ? {
        column: xAxisVariable.name,
        data_type: xAxisVariable.data_type,
        aggregator: xAxisVariable.selectedAggregation || ''
      } : null
    };
    const yAxisVariables = axisFormValue.yAxisVariables || [];
    const yVar = yAxisVariables[0] || {};
    const y_axis = {
      label: axisFormValue.yAxisLabel,
      axis: yVar && yVar.name ? {
        column: yVar.name,
        data_type: yVar.data_type,
        aggregator: yVar.selectedAggregation || ''
      } : null
    };
    const payload = {
      filters: {
        groups: [
          {
            conditions: filtersArray,
            operator: 'and'
          }
        ],
        global_operator: 'and'
      },
      x_axis,
      y_axis
    };
    return payload;
  }

  onCancelFilterEdit() {
    this.selectedFilterIndex = -1;
  }
}
