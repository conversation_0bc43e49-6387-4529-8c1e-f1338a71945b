import {
  animate,
  style,
  transition,
  trigger
} from '@angular/animations';

export const fadeInOut = trigger('fadeInOut', [
  transition(':enter', [
    style({   opacity: 0}),
    animate('300ms',
      style( {
        opacity: 1
      } )
    )
  ]),
  transition(':leave', [
    style({   opacity: 1}),
    animate('300ms',
      style( {
        opacity: 0
      } )
    )
  ])

]);

export const fadeIn = trigger('fadeIn', [
  transition(':enter', [
    style({ opacity: 0}),
    animate('400ms',
      style( {
        opacity: 1
      } )
    )
  ])]);

export const zoomIn = trigger('zoomIn', [
  transition(':enter', [
    style({  transform: 'scale(0.7)',  opacity: 0}),
    animate('300ms',
      style( {
        opacity: 1,
        transform: 'scale(1)'
      } )
    )
  ])]);
