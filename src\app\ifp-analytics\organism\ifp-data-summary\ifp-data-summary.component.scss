@use "../../../../assets/ifp-styles/abstracts/index" as *;
.ifp-summary {
  padding: $spacer-4 $spacer-4 $spacer-0;
  width: 100%;
  margin-bottom: $spacer-4;
  @include desktop-sm {
    padding: $spacer-2 $spacer-3 $spacer-0;
  }
  &__header {
    margin: $spacer-2 (-$spacer-4);
    display: flex;
    justify-content: space-between;
    align-items: center;
    &--border {
      border-bottom: 1px solid $ifp-color-grey-7;
      padding-bottom:$spacer-4 ;
      @include desktop-sm {
        padding-bottom:$spacer-3 ;
      }
    }
    @include desktop-sm {
      margin: $spacer-2 (-$spacer-3);
    }
  }
  &__heade-wrapper,&__button-sub {
    margin: $spacer-0 $spacer-4;
  }
  &__btn {
    font-size: $ifp-fs-4;
    display: inline-flex;
    align-items: center;
    padding: $spacer-1 $spacer-1 $spacer-1 $spacer-3;
    border: 1px solid $ifp-color-grey-7;
    color: $ifp-color-secondary-grey;
    font-weight: $fw-medium;
    border-radius: 4px;
    transition: 0.3s;
    cursor: pointer;
    &:hover {
      color: $ifp-color-hover-blue;
      border: 1px solid $ifp-color-hover-blue;
    }
  }
  &__main-head {
    display: flex;
    justify-content: flex-end;
    align-items: center;
  }
  &__icon {
    width: 40px;
    margin-left: $spacer-3;
  }
  &__body {
    max-height: 650px;
    overflow: auto;
    @include ifp-scroll(transparent, $ifp-color-grey-1, 10px, 8px);
    position: relative;
    padding-right: $spacer-3;
  }
  &__table,
  &__stats {
    width: 100%;
  }
  &__table {
    text-align: center;
  }
  &__footer {
    padding: $spacer-4;
    margin: $spacer-4 (-$spacer-4) $spacer-0;
    border-top: 1px solid $ifp-color-grey-7;
    display: flex;
    justify-content: flex-end;
  }
  &__col {
    padding: $spacer-4   $spacer-3;
    vertical-align: middle;
    // white-space: nowrap;
    // max-width: 100px;
    // overflow: hidden;
    // text-overflow: ellipsis;
    &--table {
      min-width: 300px;
    }
  }
  &__col-divider {
    height: 16px;
  }
  &__row {
    border: 1px solid $ifp-color-grey-7;
    border-radius: 4px;
    margin-bottom: $spacer-3;
    &:last-child {
      margin-bottom: $spacer-0;
    }
    &--head {
      border: none;
      margin-bottom: $spacer-0;
      .ifp-summary__col {
        position: sticky;
        top: 0;
        left: 0;
        background-color: $ifp-color-white;
        white-space: nowrap;
      }
    }
  }
  &__stats {
    border: 1px solid $ifp-color-grey-7;
    border-radius: 4px;
    table-layout: fixed;
  }
  &__stats-col {
    border-right: 1px solid $ifp-color-grey-7;
    font-weight: $fw-semi-bold;
    padding: $spacer-2 $spacer-3;
    width: 100%;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    &:last-child {
      border-right: none;
    }
  }
  &__stats-head {
    background-color: $ifp-color-dropdown-select;
    .ifp-summary__stats-col {
      color: $ifp-color-grey-9;
      font-weight: $fw-regular;
    }
  }
  &__value {
    font-size: $ifp-fs-5;
    font-weight: $fw-bold;
  }
  &__progress {
    width: 100%;
    border-radius: 5px;
    background-color: $ifp-color-grey-12;
    overflow: hidden;
    margin-top: $spacer-2;
  }
  &__progress-value {
    border-radius: 5px;
    height: 8px;
  }
  &__sub-title {
    margin-top: $spacer-2;
    color: $ifp-color-grey-6;
    &--bold {
      font-weight: $fw-bold;
    }
  }
  &__col-text {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  &__title {
    @include desktop-sm {
      font-size: $ifp-fs-4;
    }
  }
  &--popup {
padding: $spacer-0;
.ifp-summary {
  &__heade-wrapper {
    margin: $spacer-0;
  }
  &__header{
    margin: $spacer-0;
  }
  &__body {
    max-height: calc(100vh - 300px);
  }
}
  }
}

:host::ng-deep {
  @include desktop-sm {
  ifp-button {
    .ifp-btn {
      span {
        font-size: $ifp-fs-2;
      }
    }
  }
}
}

:host-context(.ifp-dark-theme) {
  .ifp-summary {
    &__stats-col {
      color: $ifp-color-grey-6;
    }
    &__stats-head{
      background-color: $ifp-color-white;
    }
  }
}
