export interface ThreadPreview {
  object_id: string;
  title: string;
}

export interface ThreadMessage {
  chart_nodes?: any;
  created_at: string;
  data: {
    content: string;
  };
  deleted_at: string | null;
  id: string;
  object_id: string;
  role: 'USER' | 'AI';
  type: 'CNV' | 'VIZ';
  updated_at: string;
  showTools?: boolean;
  responseType?: string,
  show_viz?: boolean,
  chart_desc?: string;
  hide?: boolean;
  custom_charts?: any;
  isCustom?: boolean;
  dashbordId?: string;
  top?: number,
  left?: number,
  parentWidth?: number;
  showPopUp?: boolean;
  liked?: string;
  feedback?: string;
  reason?: string;
  hyperlink_dict: { hyperlink: string, indicator_names: string }[];
  loadMore?:boolean;
}

export interface ThreadDetails {
  created_at: string;
  deleted_at: string | null;
  id: number;
  messages: ThreadMessage[];
  object_id: string;
  title: string;
  updated_at: string;
  user_uuid: string;
}

export interface StreamMessage {
  chat_id: string;
  message: {
    id: string;
    content: string;
  };
  status: 'in-progress' | 'complete' | 'reasoning',
  show_viz: boolean;
  show_gen_dashboard: boolean;
  viz_data: any[];
  chart_desc: string;
  custom_charts?: any;
  hyperlink_dict?: { hyperlink: string, indicator_names: string }[];
  Smart_Suggestion_list : {suggestion : string, Related_Query: string}[];
}

export interface UserMessageBody {
  action: 'next' | 'regenerate';
  reasoning: boolean;
  message: {
    id: string | null;
    content: string;
    type?: string;
    source?: string;
    responseType?: string;
  };
  conversation_id?: string
}
