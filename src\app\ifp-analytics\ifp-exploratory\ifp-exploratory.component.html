
  <div class="ifp-exprloratory">
    <div class="ifp-exprloratory__header">
      <div class="ifp-container">
        <!-- @if ((themeService.defaultLang$|async) === 'en') {
          <img src="../../../assets/images/beta-icon.svg" alt="BETA" class="ifp-beta-icon">
        } @else {
          <img src="../../../assets/images/beta-icon-arabic.svg" alt="BETA" class="ifp-beta-icon">
        } -->
      <app-ifp-stepper [count]="false" [advance]="true" [pending]="pending" [icon]="'ifp-icon-timer'" [completed]="completed"  class="ifp-exprloratory__stepper" [stepData]="_exploratory.autoMlStatus() ? fullSteps : steps"></app-ifp-stepper>
    </div>
    </div>
    <div class="ifp-exprloratory__header ifp-exprloratory__header--subheader">
      <div class="ifp-container">
        <div class="ifp-exprloratory__sub-header">
<ifp-back-button (buttonClick)="backClick()" [isGoBack]="!popup()" [buttonText]=" this.pending== 1 || popup() ?'Back' : 'Previous stage'"></ifp-back-button>
<div class="ifp-exprloratory__btn-wrapper">
  @if ( currentCount() >= 4 && (currentCount() !==4 || _exploratory.summeryLoaded())) {
    <ifp-exploratory-summery-preview class="ifp-exprloratory__process-btn-sec" [id]="dataId"></ifp-exploratory-summery-preview>
  }

  @if(enableScenario()) {
    <a (click)="routeToScenario()" class="ifp-exprloratory__btn--secondary ifp-exprloratory__btn "><span class="ifp-exprloratory__btn-text"><em class="ifp-icon  ifp-icon-library ifp-exprloratory__btn-icon"></em>{{"My Bayaan Scenarios" | translate}}</span></a>
  }
  @if(_exploratory.secondaryBtn()) {
    <ifp-button [iconClass]="_exploratory.secondaryIcon()" [loader]="_exploratory.loaderProcess()" (ifpClick)="secondaryProcess()" class="ifp-exprloratory__process-btn-sec" [label]="_exploratory.secondaryName() | translate" [buttonClass]="buttonClass.secondary+' '+buttonClass.large + (_exploratory.secondaryBtnDisable()? (' '+buttonClass.disabled): '')"></ifp-button>

  }
  <ifp-button [iconClass]="_exploratory.processIcon()" [loader]="_exploratory.loaderProcess()" (ifpClick)="process()" class="ifp-exprloratory__process-btn" [label]="_exploratory.processName() | translate" [buttonClass]="buttonClass.primary+' '+buttonClass.large + (_exploratory.processDisable()? (' '+buttonClass.disabled): '')"></ifp-button>

</div>

         </div>
      </div>
    </div>

<router-outlet></router-outlet>
    </div>


    <app-ifp-modal #backModal>
      <app-ifp-remove-card
        [text]="message" (firstButtonEvent)="resetModalClose()" (secondButtonEvent)="resetToolConfig()">
      </app-ifp-remove-card>
    </app-ifp-modal>
