import { Component, EventEmitter, Input, Output } from '@angular/core';
import { IfpAnalyticStepBarComponent } from '../../../../scad-insights/ifp-widgets/ifp-molecules/ifp-analytic-step-bar/ifp-analytic-step-bar.component';
import { DriverOption } from 'src/app/ifp-analytics/ifp-exploratory/ifp-scenario-details/ifp-scenario-details.component';

@Component({
    selector: 'ifp-tool-bar-driver-controller',
    imports: [IfpAnalyticStepBarComponent],
    templateUrl: './ifp-tool-bar-driver-controller.component.html',
    styleUrl: './ifp-tool-bar-driver-controller.component.scss'
})
export class IfpToolBarDriverControllerComponent {
  @Output() rangeOut = new EventEmitter<{ id: string; value:string; index: number; }>();
  @Input() name = '';
  @Input() driver: {options: DriverOption[]} ={ options: [
  ]};

  rangeSelected(value: { id: string; value:string; index: number; }) {
    this.rangeOut.emit(value);
  }
}
