<div class="ifp-db-icon-library">
  <ifp-search [boxType]="true" [placeholderText]="'Search icons' | translate" class="ifp-db-icon-library__lib-search"
    (searchEvent)="getSearchIcon($event)"></ifp-search>
  <div class="ifp-db-icon-library__taglist-nav">
    <em class="ifp-icon ifp-icon-left-arrow" (click)="scroll('right')"></em>
    <em class="ifp-icon ifp-icon-right-arrow" (click)="scroll('left')"></em>
  </div>
  <div class="ifp-db-icon-library__taglist-wrapper">
    <div class="ifp-db-icon-library__taglist-outer" [ngStyle]="{'cursor': isDragging ? 'grabbing' : 'grab'}" #scrollableDiv (mousedown)="onMouseDown($event)"
    (mouseup)="onMouseUp($event)" (mouseleave)="onMouseUp($event)" (mousemove)="onMouseMove($event)">
    @for (tag of tagList; track tag.key; let i = $index) {
    <div class="ifp-db-icon-library__tag-item" (click)="selectTag(tag, i)"
      [ngClass]="{'ifp-db-icon-library__tag-item--active': selectedtag === i}">{{ tag.title | translate }}</div>
    }
    </div>
  </div>
  <div class="ifp-db-icon-library__wrapper">
    @if (iconsToDisplay.length) {
    @for (icon of iconsToDisplay | slice:offset:(offset+limit); track i; let i = $index) {
    <ifp-icon-selector [icon]="icon" (selectIcon)="selectChartType($event, i)" [enableTitle]="enableIconTitle"
      class="ifp-db-icon-library__chart-icon" [isSmall]="true"></ifp-icon-selector>
    }
    } @else {
    <app-ifp-no-data class="ifp-db-icon-library__no-data"></app-ifp-no-data>
    }


  </div>
  <div class="ifp-db-icon-library__pagination">
    <app-pagination [offset]="offset" [limit]="limit" [size]="size" [limitOptions]="limitOptions"
      (pageChange)="onPageChange($event)" (limitChange)="limitChanged($event)"></app-pagination>
  </div>

  <!-- <p class="ifp-db-icon-library__divider"><span class="ifp-db-icon-library__divider-text">{{ 'or' | translate}}</span></p>

  <div class="ifp-db-icon-library__uploader">
    <app-ifp-db-file-uploader [allowedExtensions]="allowedExtensions" (fileUpload)="onIconUpload($event)" (removeFile)="onIconRemoved($event)"></app-ifp-db-file-uploader>
  </div> -->
</div>
