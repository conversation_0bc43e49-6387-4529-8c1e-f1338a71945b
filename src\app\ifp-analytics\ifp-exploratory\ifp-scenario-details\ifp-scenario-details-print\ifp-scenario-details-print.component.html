<div >
  <div class="ifp-scenario__rating">
    <div  class="ifp-scenario__rating-wrapper">
      <div class="ifp-scenario__rating-text">
        {{'Last Actual Value'    | translate}}
      </div>
      <div class="ifp-scenario__rating-number">
        {{currentIndicatorValue  | shortNumber}}
      </div>



    </div>
    <div  class="ifp-scenario__rating-wrapper">
      <div class="ifp-scenario__rating-text">
        {{'First Forecast Value' | translate}}
      </div>
      <div class="ifp-scenario__rating-number">
        {{currentIndicatorForecastValue | shortNumber}}
      </div>

    </div>
    <div  class="ifp-scenario__rating-wrapper" >
      <div class="ifp-scenario__rating-text">
        {{'% Change' | translate}}
        <!-- {{'% change'  | translate:{int: currentIndicatorDate | date:dateFormat , for:currentIndicatorForecastDate | date:dateFormat } }} -->
      </div>
      <div class="ifp-scenario__rating-number">
        <div class="ifp-scenario__icon">
          @if( 0 < percentageDifference) {
            <em  class="ifp-icon ifp-icon-triangle-up ifp-scenario__icon--up"></em>
          }@else {
            <em  class="ifp-icon ifp-icon-triangle ifp-scenario__icon--down"></em>
          }
</div>       {{percentageDifferenceAbs | number: '1.0-1'}} %
      </div>

    </div>
    <div  class="ifp-scenario__rating-wrapper">
      <div class="ifp-scenario__rating-text">
        {{'Frequency'  | translate}}
      </div>
      <div class="ifp-scenario__rating-number">
        {{frequency}}
      </div>
    </div>
    <div  class="ifp-scenario__rating-wrapper">
      <div class="ifp-scenario__rating-text">
        {{'Forecast Horizon'  | translate}}
      </div>
      <div class="ifp-scenario__rating-number">
        {{forecastHorizon }}
      </div>
    </div>
  </div>
  <app-ifp-analytic-line-chart [disableDarkModeSwitch]="true" [width]="1200"   [height]="600" [enableAnimation]="false" class="ifp-scenario__highchart" [isRangeSelect]="false" [chartData]="chartData" [yaxisLabel]="yAxisLabel()" [xAxisLabel]="xAxisLabel()"></app-ifp-analytic-line-chart>

    </div>
