@use "../../../../assets/ifp-styles/abstracts/index" as *;
@use "../../../../assets/ifp-styles/components/ai-insights" as *;
.ifp-irg-compare {
  &__table-outer {
    border-radius: 20px;
    box-shadow: 0 18.05px 96.14px 7.18px rgb(231 231 231);
  }
  &__table {
    position: relative;
    &::after {
      content: "";
      min-width: 20%;
      max-width: 20%;
      height: 100%;
      border-radius: 20px;
      position: absolute;
      top: -15px;
      left: 40%;
      background-image: linear-gradient(#0c4a8e, #2F81DA);
      box-shadow: 0 48px 50px -30px rgba(44, 124, 211, 0.78);
    }
  }
  &__table-col {
    padding: $spacer-4;
    text-align: center;
    width: 20%;
    font-weight: $fw-semi-bold;
    border-right: 1px solid $ifp-color-grey-7;
    &--official,
    &:first-child,
    &:last-child {
      border: none;
    }
    &:first-child {
      min-width: 40%;
      max-width: 40%;
      text-align: start;
      font-weight: $fw-regular;
    }
    &--official {
      color: $ifp-color-white-global;
      position: relative;
      z-index: 1;
    }
  }
  &__table-row {
    display: flex;
    width: 100%;
    background-color: $ifp-color-section-white;
    &--head {
      border-top: 3px solid $ifp-color-blue-hover;
      .ifp-irg-compare__table-col {
        font-weight: $fw-bold;
        display: flex;
        align-items: center;
        justify-content: center;
        &:first-child {
          justify-content: flex-start;
        }
        &--official {
          .ifp-irg-compare__table-icon {
            font-weight: $fw-regular;
            color: $ifp-color-white-global;
            position: absolute;
            top: 4px;
            right: 10px;
          }
        }
      }
    }
    &:first-child {
      border-radius: 20px 20px 0 0;
      .ifp-irg-compare__table-col--official {
        border-radius: 20px 20px 0 0;
      }
    }
    &:last-child {
      border-radius: 0 0 20px 20px;
      .ifp-irg-compare__table-col {
        min-height: 85px;
      }
    }
    &:nth-child(even) {
      background-color: rgba(205, 214, 214,0.14);
      .ifp-irg-compare__table-col--official {
        background-color: rgba(205, 214, 214,0.08);
        transform: translateY(6px);
      }
    }
  }
  &__table-icon {
    font-size: $ifp-fs-4;
    font-weight: $fw-bold;
    margin-inline-start: $spacer-1;
    position: relative;
    top: 2px;
    &--green {
      color: $ifp-color-green-dark-2;
    }
    &--red {
      color: $ifp-color-red-1;
    }
  }
  &--normal {
    .ifp-irg-compare {
      &__table {
      &::after {
        content: none;
      }
    }
    &__table-col {
      &:first-child {
        border-right: 1px solid $ifp-color-grey-7;
      }
    }
    &__table-row {
      &:nth-child(odd) {
        background-color: rgba(205, 214, 214,0.14);
      }
      &:nth-child(even) {
        background-color: transparent;
      }
    }
    }
  }
}

@include tablet-horizondal {
  .ifp-irg-compare {
    &__table-outer{
      overflow-x: auto;
    }
    &__table{
      width: fit-content
    }
    &__table-col {
      width: 248px;
    }
    &__table {
      &::after {
        max-width: 200px;
        min-width: 200px;
      }

    }
  }
}

:host-context(.ifp-dark-theme) {
.ifp-irg-compare {
  &__table-outer {
    box-shadow: 0 35px 50px -23px #707b9c14;
  }

}
}
