@use "../../../../assets/ifp-styles/abstracts/index" as *;

.ifp-input-counter {
  display: flex;
  border: 1px solid $ifp-color-grey-7;
  border-radius: 7px;
  overflow: hidden;
  &__text {
    text-align: center;
    color: $ifp-color-tertiary-text;
    background-color: $ifp-color-white;
    width: 60px;
    padding: $spacer-2 $spacer-3;
  }
  &__btn-sec {
    border-radius: 0 7px 7px 0;
    overflow: hidden;
  }
  &__btn {
    padding: $spacer-0 $spacer-2;
    background-color: $ifp-color-active-blue;
    transition: 0.3s;
    cursor: pointer;
    .ifp-icon {
      font-size: $ifp-fs-1;
      display: block;
      padding: ($spacer-1 + 2px) $spacer-0;
      color: $ifp-color-white-global;
    }
    &:first-child {
      .ifp-icon {
        border-bottom: 1px solid $ifp-color-white-50;
      }
    }
    &:hover {
      background-color: $ifp-color-hover-blue;
    }
  }
}
