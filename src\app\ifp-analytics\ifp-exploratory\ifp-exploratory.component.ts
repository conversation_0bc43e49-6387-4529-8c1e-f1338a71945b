import { TranslateModule } from '@ngx-translate/core';
import { Component, <PERSON><PERSON><PERSON><PERSON>, OnInit, signal, ViewChild } from '@angular/core';
import { IfpStepData, IfpStepperComponent } from '../../scad-insights/ifp-widgets/ifp-atoms/ifp-stepper/ifp-stepper.component';
import { IfpBackButtonComponent } from '../../ifp-widgets/atoms/ifp-back-button/ifp-back-button.component';
import { ActivatedRoute, NavigationEnd, Router, RouterOutlet, Scroll } from '@angular/router';
import { SubSink } from 'subsink';
import { IfpExploratoryService } from './services/ifp-exploratory.service';
import { IfpModalComponent } from '../../scad-insights/ifp-widgets/ifp-organism/ifp-modal/ifp-modal.component';
import { IfpRemoveCardComponent } from '../../scad-insights/ifp-widgets/ifp-atoms/ifp-remove-card/ifp-remove-card.component';
// import { FileResponePrep } from '../data-prep/ifp-data-prep/interface/ifp-data-prep.interface';
// import { prepsApiEndpoints } from '../data-prep/ifp-data-prep/constants/if-preps.constants';
import {  Location } from '@angular/common';
import { ThemeService } from 'src/app/scad-insights/core/services/theme/theme.service';
import { UsageDashboardLogService } from 'src/app/scad-insights/core/services/usage-dashboard-log/usage-dashboard-log.service';
import { logType } from 'src/app/scad-insights/core/services/usage-dashboard-log/usage-dashboard.constants';
import { filter } from 'rxjs';
import { IfpButtonComponent } from '../../scad-insights/ifp-widgets/ifp-atoms/ifp-button/ifp-button.component';
import { buttonClass } from 'src/app/scad-insights/core/constants/button.constants';
import { IfpExploratorySummeryPreviewComponent } from './ifp-exploratory-summery-preview/ifp-exploratory-summery-preview.component';
@Component({
    selector: 'ifp-ifp-exploratory',
    templateUrl: './ifp-exploratory.component.html',
    styleUrl: './ifp-exploratory.component.scss',
    imports: [IfpStepperComponent, TranslateModule, IfpBackButtonComponent, RouterOutlet, IfpModalComponent, IfpRemoveCardComponent, IfpButtonComponent, IfpExploratorySummeryPreviewComponent]
})
export class IfpExploratoryComponent implements OnDestroy, OnInit{
  @ViewChild('backModal') backModal!: IfpModalComponent;
  public steps: IfpStepData[] = [
    {
      counter: 1, subName: 'Stage', name: 'Upload Your Data', url: '/analytics/exploratory/upload-data', stepCount: 1
    },

    {
      counter: 3, subName: 'Stage', name: 'Exploratory Data Analysis',  url: '/analytics/exploratory/data-analysis', stepCount: 2
    },
    {
      counter: 4, subName: 'Stage', name: 'Run Your Model',  url: '/analytics/exploratory/run', stepCount: 3
    }
  ];

  public autoMlStages: IfpStepData[] = [
    {
      counter: 5, subName: 'Stage', name: 'Analyze Your Results', url: '/analytics/exploratory/data-result', stepCount: 4
    },
    {
      counter: 6, subName: 'Stage', name: 'Re-analyze Your Results (Optional)', url: '/analytics/exploratory/re-analysis', stepCount: 5
    },
    {
      counter: 7, subName: 'Stage', name: 'Create Scenarios', stepCount: 6
    }
  ];

  public fullSteps: IfpStepData[] = [...this.steps, ...this.autoMlStages];

  public completed = 0;
  public pending = 1;
  public subs = new SubSink();
  public enableScenario = signal(false);
  public popup=signal(false);
  public message = 'Your data will be lost, and you\'ll be redirected to the landing page. Do you want to continue?';
  private sessionId!: string;
  public  buttonClass = buttonClass;
  public currentCount = signal(0);
  public dataId!:string;

  constructor(private _router: Router,
    public _exploratory: IfpExploratoryService,
    private _location: Location,
    public themeService: ThemeService, private log: UsageDashboardLogService,
    private _activatedRoute : ActivatedRoute
  ) {}

  ngOnInit(): void {
    this.subs.add(this._activatedRoute.queryParams.subscribe(data => {
      this.dataId = data['dataId'] ?? data['id'];
    }));
    if (!this._router.url.includes('scenario') &&  this._router.url.includes('/analytics/auto-ml/upload-data')) {
      this._exploratory.uploadedFile.set({});
    }
    this.subs.add(this._router.events.pipe(filter(event =>{
      if (event instanceof Scroll) {
        return event['routerEvent'] instanceof NavigationEnd;
      }
      return false;

    })).subscribe(() => {
      if (this._router.url.includes('basic') ||  this._router.url.includes('adv')){
        this.popup.set(true);
      }
      if (this.sessionId) {
        this.log.logEnds(this.sessionId, this.log.currentTime );
      }
      if (this._router.url.includes('auto-ml') ||  this._router.url.includes('enableAutoMl')) {
        this.enableScenario.set(true);
        this.steps = [
          {
            counter: 1, subName: 'Stage', name: 'Upload Your Data', url: '/analytics/auto-ml/upload-data', stepCount: 1
          },
          {
            counter: 4, subName: 'Stage', name: 'Run Your Model',  url: '/analytics/auto-ml/run', stepCount: 2
          },
          {
            counter: 5, subName: 'Stage', name: 'Analyze Your Results', url: '/analytics/auto-ml/data-result', stepCount: 3
          },
          {
            counter: 6, subName: 'Stage', name: 'Re-analyze Your Results (Optional)', url: '/analytics/auto-ml/re-analysis', stepCount: 4
          },
          {
            counter: 7, subName: 'Stage', name: 'Create Scenarios', stepCount: 5
          }
        ];
        this.fullSteps = this.steps;
        this.sessionId = this.log.createUUid;
        this.log.logStart(this.sessionId, logType.autoMl, this.log.currentTime );
      } else {
        this.sessionId = this.log.createUUid;
        this.log.logStart(this.sessionId, logType.exploratory, this.log.currentTime );
        this.enableScenario.set(false);
      }
      this.fullSteps.forEach((data, index )=> {
        const url = this._router.url.split('?')[0];
        if (data.url === url) {
          this.completed = index;
          this.pending = index + 1;
          this.currentCount.set(data.counter);
        }
      });
    }));
    this.setCurrentUrl();

  }

  resetModalClose() {
    this._location.back();
    this.backModal.removeModal();
  }

  resetToolConfig() {
    this.backModal.removeModal();
  }

  backClick() {
    this.backModal.createElement();
  }

  process() {
    this._exploratory.processEvent.next('process');
  }

  secondaryProcess() {
    this._exploratory.processEventsecondary.next('process');
  }

  setCurrentUrl() {
    if (!this._router.url.includes('/analytics/auto-ml/upload-data')) {
      return;
    }
    const currentUrl = this._location.path();
    const urlTree = this._router.parseUrl(currentUrl);
    const queryParams:{
      enableAutoMl?: boolean;
  } = { };
    if (this.enableScenario()) {
      queryParams['enableAutoMl']= true;
    }
    urlTree.queryParams = queryParams;
    this._location.replaceState(urlTree.toString());
  }

  routeToScenario() {
    const currentUrl = this._location.path();
    const urlTree = this._router.parseUrl(currentUrl);
    const queryParams:{
      scenario: boolean;
      enableAutoMl?: boolean;
  } = { scenario: true };
    if (this.enableScenario()) {
      queryParams['enableAutoMl']= true;
    }
    urlTree.queryParams = queryParams;
    this._location.replaceState(urlTree.toString());
    this._router.navigateByUrl('/my-Scenarios');
  }


  ngOnDestroy(): void {
    this.subs.unsubscribe();
    if (this.sessionId) {
      this.log.logEnds(this.sessionId, this.log.currentTime );
    }
  }
}
