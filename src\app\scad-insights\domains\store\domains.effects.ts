import { Injectable } from '@angular/core';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { DomainsService } from 'src/app/scad-insights/core/services/domains/domains.service';
import { loadCategory, loadCategorySuccess, loadNewsLetter, loadNewsLetterSuccess } from './domain.action';
import { catchError, exhaustMap, map, of } from 'rxjs';
import { categoryStateName, newsLetterStateName } from './domain.state';
import { Store } from '@ngrx/store';

@Injectable()
export class DomainEffects {

  constructor(
    private actions$: Actions,
    // eslint-disable-next-line @typescript-eslint/no-shadow, no-shadow
    private _domainService: DomainsService,
    private store: Store
  ) { }


  category$ = createEffect(() => {
    // console.log("working effects")
    return this.actions$.pipe(
      ofType(loadCategory),
      exhaustMap((action) => this._domainService.getCategories(action.searchString).pipe(
        map((result) => {
          // console.log("result", result)
          return loadCategorySuccess({ data: result, errorMessage: '', status: true });
        }),
        catchError(error => of(loadCategorySuccess({ data: categoryStateName, errorMessage: error, status: false })) // Dispatch error action
        )
      )
      )
    );
  });


  newsLetter$ = createEffect(() => {
    return this.actions$.pipe(
      ofType(loadNewsLetter),
      exhaustMap(() => {
        return this._domainService.getNewsLetter().pipe(
          map((result) => {
            return loadNewsLetterSuccess({ data: result, errorMessage: '', status: true });
          }),
          catchError(error => of(loadNewsLetterSuccess({ data: newsLetterStateName, errorMessage: error, status: false })) // Dispatch error action
          )
        );
      })
    );
  });
}


