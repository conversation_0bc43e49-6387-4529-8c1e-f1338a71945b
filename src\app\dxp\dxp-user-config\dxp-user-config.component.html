<div class="ifp-dxp-user-config">
  <div class="ifp-dxp-user-config__sec-1">
    <div class="ifp-dxp-user-config__sec">
      <div class="ifp-dxp-user-config__user">
        <div class="ifp-dxp-user-config__user-wrap">
          <em  class="ifp-dxp-user-config__user-icon ifp-icon-manage-user "></em>
        </div>
        <h4 class="ifp-dxp-user-config__head"> {{'Configure User Access' | translate}}</h4>
        <p class="ifp-dxp-user-config__user-text">
          {{'Please configure user access for the dashboard before proceeding with publication. Once done, send it for approval to the Visualization Approver.'| translate}}
        </p>
      </div>

        <div class="ifp-dxp-user-config__sub-sec">
          <ifp-panel-dropdown  [userDropdown]="true"  [label]="'Add User'" [enableSearch]="true" (multiSelected)="dropDownItemMultiClicked($event)" [isBoxType]="true" [enableSelectAll]="true"
           [multiSelect]="true" [key]="'value'" [multipleSelectedItems]="currentSelection()" [options]="sharedUserList()"  class="ifp-dxp-user-config__user-list"  [title]="'Add users'" (searchChanged)="onUserSearch($event)"></ifp-panel-dropdown>
          @if (sharedUserListSelected().length !==0) {
          <ifp-user-tag-group [isEdit]="true" (removeData)="removeData($event)" [tagList]="sharedUserListSelected()" class="ifp-dxp-user-config__user-group"></ifp-user-tag-group>
        }
        </div>


    </div>
  </div>
  <div class="ifp-dxp-user-config__sec-2">
    <div class="ifp-dxp-user-config__title-wrap">
      <h3 class="ifp-dxp-user-config__title">{{'Preview' | translate}}</h3>
    </div>
    <div class="ifp-dxp-user-config__content">
        <ifp-dxp-kpi-card class="ifp-dxp-user-config__chart" [isDropdownDisabled]="true" [edit]="false" [title]="userConfig().title"  [description]="userConfig().description" [graphData]="userConfig().graphData" [filters]="userConfig().onAppliedFilter" [legends]="userConfig().legends" [defaultLegend]="userConfig().defaultLegend"></ifp-dxp-kpi-card>
    </div>
  </div>
</div>
