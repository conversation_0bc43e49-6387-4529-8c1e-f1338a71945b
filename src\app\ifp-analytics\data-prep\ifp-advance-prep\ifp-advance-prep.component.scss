@use "../../../../assets/ifp-styles/abstracts/index" as *;

:host {
  display: block;
}

.ifp-p-adv {
  display: flex;
  min-height: calc(100vh - 242px);
  background-color: $ifp-color-grey-bg;

  &__body-wrapper {
    position: relative;
  }

  &__toolbar {
    display: flex;
    border-inline-end: 1px solid $ifp-color-grey-3;
  }

  &__content {
    width: calc(100% - 321px);
    position: relative;
    transition: 0.4s;
  }

  &__header {
    display: block;
  }

  &__body {
    display: flex;
  }

  &__body-content {
    padding: $spacer-4 $spacer-4 $spacer-0;
    width: 100%;
  }

  &__preview {
    background-color: $ifp-color-white;
    padding: $spacer-4;
    position: relative;
    border-top: 1px solid $ifp-color-grey-7;
    display: flex;
  }

  &__preview-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: $spacer-4;
    position: relative;
    z-index: 2;
  }

  &__preview-tools {
    display: flex;
    align-items: center;
    margin: $spacer-0 (-$spacer-3);
  }

  &__preview-tool,
  &__preview-duration,
  &__preview-dropdown {
    margin: $spacer-0 $spacer-3;
  }

  &__preview-tool,
  &__preview-dropdown {
    font-size: $ifp-fs-6;
  }

  &__preview-tool {
    transition: 0.3s;
    cursor: pointer;

    &:hover,
    &--active {
      color: $ifp-color-blue-hover;
    }
  }

  &__probability {
    margin-inline-start: $spacer-4;
  }

  &__right-panel {
    position: absolute;
    top: 2px;
    right: 0;
    bottom: 0;
    z-index: 2;
    width: 415px;
    display: flex;
    background-color: $ifp-color-section-white;
    @include ifp-scroll-y($ifp-color-dropdown-select, $ifp-color-grey-1, 8px, 8px);
    // &--expand {
    //   height: calc(100vh - 40px);
    // }
  }
  &__records {
    display: flex;
    margin: $spacer-0 (-$spacer-2);
  }
  &__record-item {
    width: calc(100% - (2 * $spacer-2));
    margin: $spacer-0 $spacer-2;
  }
  &--collapse .ifp-p-adv__content {
    width: calc(100% - 70px);
  }
}

:host::ng-deep {
  .ifp-p-adv__toggle-sidebar {
    .ifp-toggle-sidebar__content {
      padding: $spacer-4;
    }

    .ifp-p-adv {
      &__heading {
        font-size: $ifp-fs-6;
        font-weight: $fw-bold;
        margin-bottom: $spacer-5;
      }

      &__process-item {
        position: relative;
        padding-bottom: $spacer-4;
        padding-inline-start: $spacer-4;

        &::before,
        &::after {
          content: "";
          position: absolute;
          top: 2px;
        }

        &::after {
          width: 11px;
          height: 11px;
          border-radius: 50%;
          background-color: $ifp-color-green-dark-1;
          left: 0;
        }

        &::before {
          width: 1px;
          height: 100%;
          background-color: $ifp-color-grey-8;
          left: 5px;
        }

        &:last-child {
          padding-bottom: $spacer-0;

          &::before {
            content: none;
          }
        }
      }

      &__process-name {
        font-weight: $fw-medium;
        margin-bottom: $spacer-2;
      }

      &__process-time {
        color: $ifp-color-grey-9;

        .ifp-icon {
          margin-inline-end: $spacer-2;
        }
      }
    }
  }
  .ifp-p-adv--fullscreen {
    overflow: auto;
    .ifp-p-adv__toolbar {
      height: calc(100vh + 213px);
    }
  }
}
.ifp-prep-data__preview-resize {
  background-color: $ifp-color-grey-2;
  color: $ifp-color-white;
  padding: 2px $spacer-6;
  border-radius: 30px;
  cursor: n-resize;
  position: absolute;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 3;
}

// * for drawing board //

:host-context([dir="rtl"]) {
  .ifp-p-adv {
    &__right-panel {
      left: 0;
      right: auto;
    }
  }
  .ifp-prep-data__preview-resize {
    right: auto;
    left: 50%;
    transform: translate(-50%, -50%);
  }
}

@include desktop-sm {
  .ifp-p-adv {
    position: relative;
    &__content {
      width: 100% !important;
      padding-left: 70px;
    }
  }
  .ifp-prep-data__preview-resize {
    z-index: 7;
  }
  :host-context([dir="rtl"]) {
    .ifp-p-adv {
      &__content {
        padding-right: 70px;
        padding-left: $spacer-0;
      }
    }
  }
}
