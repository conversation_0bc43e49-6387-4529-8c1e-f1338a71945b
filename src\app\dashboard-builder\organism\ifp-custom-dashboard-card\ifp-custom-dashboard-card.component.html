<div class="ifp-custom-db-card">
  @if (isVisible() && (chartType === 'line' || chartType === 'bar' || chartType === 'column') && seriesData?.length > 0 && !checkChartCategory())
  {
  <app-ifp-analytic-line-chart [chartData]="seriesData" [isRangeSelect]="false" #chartComponent [yaxisLabel]="''"
    [xAxisLabelType]="xAxisCatogory" [xAxisCatogory]="chartCatogory" [height]="300"
    [isDashboardCard]="true"></app-ifp-analytic-line-chart>
  } @else if (chartType === 'circular' && isVisible()) {
  <app-ifp-circular-bar-chart [chartData]="seriesData" [chartCategory]="chartCatogory" [height]="300"
    [isDashboardCard]="true"></app-ifp-circular-bar-chart>
  } @else if ((chartType === 'pie' || chartType === 'doughnut') && isVisible()) {
  <app-ifp-pie-chart
    [pieChartSeries]="seriesData"  [chartCategory]="chartCatogory" [height]="300" [isDonut]="chartType === 'doughnut'? true: false"
    [isDashboardCard]="true"></app-ifp-pie-chart>
  } @else if (chartType === 'table') {
    <app-ifp-table [tableData]="[chartData]"></app-ifp-table>
  }
</div>
