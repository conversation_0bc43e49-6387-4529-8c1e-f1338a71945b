import { Injectable, signal, WritableSignal } from '@angular/core';
import { HttpService } from '../http/http.service';
import { slaApis } from '../../apiConstants/sla-api.constants';
import { BehaviorSubject, catchError, of, tap } from 'rxjs';
import { Router } from '@angular/router';
import { ApiStatus } from '../../constants/api-status.constants';
import { IFPMsalService } from '../IFP-msal.service';
import { manitananceKeys } from '../../constants/chart.constants';
import { aiInsightApi } from 'src/app/gen-ai/ai-insight-report/constants/ai-insight-api.contants';
import { ApiGenAiService } from '../api-gen-ai.service';

@Injectable({
  providedIn: 'root'
})
export class slaService {

  public slaStatus$ = new BehaviorSubject(false);
  public slaLoader$ = new BehaviorSubject(true);
  public slaStatus = false;
  public permission: WritableSignal<Features> = signal({
    genAi: false,
    dataPrepBasic: false,
    dataPrepAdv: false,
    dataExploration: false,
    autoML: false,
    dashBoardBuilder: true
  });

  public maintainanceData!: SlaData;
  public downloadAiAuditAccess: WritableSignal<boolean> = signal(false);

  constructor(private _http: HttpService, private _router: Router, private _msal: IFPMsalService, private _ai: ApiGenAiService) {

  }


  slaStatusCheck(): any {
    return this._http.get(slaApis.statusCheck).pipe(tap((data: SlaData) => {
      this.slaStatus$.next(data.status ? true : false);
      this.slaStatus = false;
      if (data.maintenance_modules?.length > 0) {
        this.slaStatus = data.maintenance_modules.includes(manitananceKeys.all);
      }
      if (data?.features) {
        const features = data?.features ?? {
          genAi: false,
          dataPrepBasic: false,
          dataPrepAdv: false,
          dataExploration: false,
          autoML: false,
          dashBoardBuilder: true
        };
        this.permission.update(() => {
          return { ...features, dashBoardBuilder: true };
        });
      }
      localStorage.setItem('preventLogout', data.preventLogout);
      this.slaLoader$.next(false);
      if (data?.maintenance_modules?.includes(manitananceKeys.all)) {
        if (data?.error_code === 'sla_expired') {
          this._router.navigateByUrl('/sla');
        } else {
          this._router.navigateByUrl('/group_unassigned');
        }
      }
    }),
      catchError((error) => {
        if (error?.status == ApiStatus.unAuthorized) {
          sessionStorage.clear();
          localStorage.clear();
          this._msal.clearAll();
          this._router.navigateByUrl('/login');
        } else {
          this._router.navigateByUrl('/siteUnderMaintains');
        }
        this.slaStatus$.next(false);
        this.slaLoader$.next(false);
        return of({ status: false });
      })
    );
  }

  checkPermission(value: 'genAi' | 'dataPrepBasic' | 'dataPrepAdv' | 'dataExploration' | 'autoML' | 'dashBoardBuilder') {
    if (!this.slaStatus) {
      return this._http.get(slaApis.statusCheck).pipe(tap((data: SlaData) => {
        if (data?.features) {
          if (data?.features) {
            const features = data?.features ?? {
              genAi: false,
              dataPrepBasic: false,
              dataPrepAdv: false,
              dataExploration: false,
              autoML: false,
              dashBoardBuilder: true
            };
            this.permission.update(() => {
              return { ...features, dashBoardBuilder: true };
            });
          }
        }
        if (!this.permission()[value]) {
          this._router.navigateByUrl('/404');
          return false;
        }

        return true;
      }),
        catchError(() => {
          return of({ status: false });
        })
      );
    }
    if (!this.permission()[value]) {
      this._router.navigateByUrl('/404');
      return false;
    }
    return true;
  }

  checkInsightAccess() {
    return this._http.get(aiInsightApi.accesscheck);
  }


  feedback(): any {
    return this._http.get(slaApis.feedbackApi);
  }

  submitFeedback(data: any): any {
    return this._http.post(slaApis.submitFeedback, data);
  }

  checkMaintanance() {
    return this._http.get(slaApis.statusCheck);
  }

  checkTestingTools() {
    return this._ai.getMethodRequest(slaApis.checkTestingTools);
  }

  checkEvaluvateStatus() {
    return this._ai.getMethodRequest(slaApis.checkEvalutionVersion);
  }
  checkChatbotStatus() {
    return this._ai.getMethodRequest(slaApis.checkChatVersion);
  }


  checkTammAccess() {
    return this._ai.getMethodRequest(slaApis.tammUserAccess);
  }

}
export interface SlaData {
  message: string;
  status: boolean;
  preventLogout: string;
  features?: Features;
  error_code?: string;
  maintenance_modules: string[];
  title: string;
  end_date: string,
  from_date: string;
}

export interface Features {
  genAi?: boolean;
  dataPrepBasic?: boolean;
  dataPrepAdv?: boolean;
  dataExploration?: boolean;
  autoML?: boolean;
  dashBoardBuilder?: boolean;
}


export interface IfpMaintanance {
  body: string,
  end_date: string,
  from_date: string;
  maintenance_mode: boolean;
  message: string;
  status: boolean;
  title: string;
}
