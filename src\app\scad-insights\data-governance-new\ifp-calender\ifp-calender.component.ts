import { ChangeDetectionStrategy, Component, inject, input, InputSignal, OnChanges, OnInit, output, OutputEmitterRef, SimpleChanges } from '@angular/core';
import { statusList } from '../ifp-data-governance.constants';
import { monthNames, weekdays } from './ifp-calender.constants';
import { TranslateModule } from '@ngx-translate/core';
import { DatePipe, NgClass } from '@angular/common';
import { IfpTooltipDirective } from '../../core/directives/ifp-tooltip.directive';
import { IfpProgressValueComponent } from "../../../ifp-analytics/molecule/ifp-progress-value/ifp-progress-value.component";
import { DataGovernanceService } from '../../core/services/data-governance.service';

@Component({
  selector: 'ifp-calender',
  imports: [TranslateModule, NgClass, IfpTooltipDirective, IfpProgressValueComponent],
  templateUrl: './ifp-calender.component.html',
  styleUrl: './ifp-calender.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class IfpCalenderComponent implements OnInit, OnChanges {


  public eventDetails: InputSignal<EventDetails[]> = input([{
    date: '',
    name: '',
    status: [{ name: '', count: 0 }],
    totalCount: 0
  }]);

  public viewDetails: OutputEmitterRef<CalenderDays> = output<CalenderDays>();
  public changeMonth: OutputEmitterRef<Date> = output<Date>();

  public statusList = statusList;
  public weekdays = weekdays;
  public monthNames = monthNames;
  public today = new Date();
  public currentYear: number = this.today.getFullYear();
  public currentMonth: number = this.today.getMonth();
  public calendarDays: CalenderDays[] = [];
  public eventDates: string[] = [];
  public apiDateFormat: string = 'y-MM-dd';

  private readonly _datePipe = inject(DatePipe);
  public _dataGovernanceService = inject(DataGovernanceService)

  ngOnInit() {
    this.currentYear = this.today.getFullYear();
    this.currentMonth = this.today.getMonth();
  }


  ngOnChanges(changes: SimpleChanges): void {
    this.generateCalendar();
  }

  generateCalendar() {
    this.calendarDays = [];

    const firstDay = new Date(this.currentYear, this.currentMonth, 1).getDay();
    const totalDays = new Date(this.currentYear, this.currentMonth + 1, 0).getDate();

    const prevMonthDays = new Date(this.currentYear, this.currentMonth, 0).getDate();

    // Days from previous month
    for (let i = firstDay - 1; i >= 0; i--) {
      this.calendarDays.push({
        date: prevMonthDays - i,
        currentMonth: false
      });
    }

    // Days of current month

    const events = this.eventDetails?.() ?? [];
    const eventMap = Object.fromEntries(
      events.map(e => [+(this._datePipe.transform(e.date, 'dd') || 0), e])
    );

    const eventDates = Object.keys(eventMap);

    for (let i = 1; i <= totalDays; i++) {
      let detail = {
        date: i,
        currentMonth: true
      }
      if (eventDates.includes(i.toString())) {
        detail = {...detail, ...{isEvent: eventMap[i]}}
      }
      this.calendarDays.push(detail);
    }


    // (Optional) Fill the last row with next month's days to complete the grid
    const totalCells = this.calendarDays.length;
    const nextDays = 7 - (totalCells % 7);
    if (nextDays < 7) {
      for (let i = 1; i <= nextDays; i++) {
        this.calendarDays.push({
          date: i,
          currentMonth: false,
        });
      }
    }
  }

  prevMonth() {
    if (this.currentMonth === 0) {
      this.currentMonth = 11;
      this.currentYear--;
    } else {
      this.currentMonth--;
    }
    this.changeMonth.emit(new Date(this.currentYear, this.currentMonth));
    this.generateCalendar();
  }

  nextMonth() {
    if (this.currentMonth === 11) {
      this.currentMonth = 0;
      this.currentYear++;
    } else {
      this.currentMonth++;
    }
    this.changeMonth.emit(new Date(this.currentYear, this.currentMonth));
    this.generateCalendar();
  }

  openEventDetails(day: CalenderDays) {
    this.viewDetails.emit(day);
  }
}

export interface EventDetails {
  date: string;
  name: string;
  status: {name: string, count: number}[];
  totalCount:number;
  list?:any[]
}

export interface CalenderDays {
  date: number;
  currentMonth: boolean;
  isEvent?: EventDetails;
}
