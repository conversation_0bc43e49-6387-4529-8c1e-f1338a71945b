@use "../../../../assets/ifp-styles/abstracts/index" as *;
@use "../../../../assets/ifp-styles/components/ai-insights" as *;
$side-spacing: $spacer-5;
.ifp-ai-report {
  margin-bottom: $spacer-3;
  .ifp-container--sm {
    max-width: 85%;
  }
  position: relative;
  min-height: calc(100vh - 242px);

  &__module-curve {
    border-radius: 20px;
  }
  &__bg {
    position: absolute;
    width: 100%;
    height: 100%;
    object-position: top;
    top: 0;
    left: 0;
  }
  &__header {
    text-align: center;
    margin-top: $spacer-5;
    margin-bottom: $spacer-5;
  }
  &__key-driver {
    margin-bottom: 104px;
    display: block;
  }
  &__head-icon {
    font-size: 22px;
    margin-inline-end: $spacer-3;
  }
  &__heading {
    font-size: $ifp-fs-13;
    font-weight: $fw-bold;
    margin-bottom: $spacer-3;
  }
  &__breadcrumbs,
  &__header,
  &__main {
    position: relative;
    z-index: 1;
  }
  &__main {
    margin-top: $spacer-4;
    border-radius: 20px;
    overflow: hidden;
    background-color: $ifp-color-section-white;
  }

  &__domain-icon {
    margin-inline-end: $spacer-2;
  }
  &__domain-name {
    font-size: $ifp-fs-10;
    font-weight: $fw-bold;
  }

  &__module-header,
  &__drivers-desc {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  &__regenerate {
    display: flex;
    margin-top: $spacer-3;
    margin-bottom: $spacer-0;
    &--left{
      justify-content: flex-end;
    }
  }
  &__btn {
    margin-inline-start: $spacer-3;
  }

  &__drivers-desc {
    align-items: flex-end;
    margin-bottom: $spacer-3;
  }
&__stepper {
    display: block;
    margin: $spacer-5 $spacer-0;

  }
  &__module-body {
    // display: flex;
    // margin: $spacer-0 (-$spacer-4);
  }
  &__sec-1,
  &__sec-2 {
    // width: 50%;
    padding: $spacer-0 $spacer-4;
  }
  &__title {
    font-size: $ifp-fs-5;
    font-weight: $fw-bold;
    margin-bottom: $spacer-3;
  }
  &__insight-card {
    margin-bottom: $spacer-3;
    &:last-child {
      margin-bottom: $spacer-0;
    }
  }

  // comparison module start
  &__compare {
    padding: $spacer-4 $side-spacing;
  }
  &__compare-inner {
    padding: $side-spacing;
    background-color: $ifp-color-pale-grey-50;
  }
  &__source {
    margin-top: $spacer-3;
    color: $ifp-color-secondary-grey;
  }
  &__sources {
    padding-inline-end: $spacer-3;
    margin-inline-end: $spacer-3;
    border-inline-end: 1px solid $ifp-color-grey-7;
    &:last-child {
      padding-inline-end: $spacer-0;
      margin-inline-end: $spacer-0;
      border-inline-end: 0;
    }
  }
  &__loader {
    min-height: 400px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  &__main {
    position: relative;
    z-index: 1;
  }
  &__main-title {
    display: flex;
    justify-content: space-between;
    margin-top: $spacer-3;
    padding-inline-end: $spacer-5;
    align-items: center;
    margin-bottom: $spacer-3;
    flex-wrap: wrap;
  }
  &__main-title-text {
    font-size: $ifp-fs-11;
    font-weight: $fw-bold;
    background-color: $ifp-color-grey-18;
    padding: $spacer-3 $spacer-7 $spacer-3 $spacer-5;
    border-end-end-radius: 50px;
    border-start-end-radius: 50px;
    position: relative;

    &::before {
      content: "";
      background-color: $ifp-color-green;
      height: 40px;
      width: 6px;
      border-radius: 0 5px 5px 0;
      position: absolute;
      top: 50%;
      left: 0;
      transform: translateY(-50%);
    }
  }
  &__loader {
    min-height: 400px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  &__main {
    border-radius: 20px;
    overflow: hidden;
    background-color: $ifp-color-section-white;
  }
  &__main-download-wrap {
    display: flex;
    justify-content: end;
  }
  &__date-badge {
    border-radius: 40px;
    background-color: $ifp-color-grey-15;
    font-weight: $fw-medium;
    white-space: nowrap;
    padding: $spacer-2 $spacer-4;
    height: 100%;
    color: $ifp-color-grey-14;
  }
  &__main-download {
    margin-inline-start: $spacer-3;
  }
}

@include desktop-sm {
  .ifp-ai-report {
    &__main-title-text {
      margin-bottom: $spacer-3;
    }
    &__main-download-wrap,
    &__main-head-right {
      margin-bottom: $spacer-3;
      justify-content: start;
    }
    &__main-head-left {
      margin-bottom: $spacer-3;
    }
    &__main-bottom {
      margin: $spacer-0 $spacer-5;
    }
    &__regenerate {
      flex-wrap: wrap;
      margin: $spacer-0 (-$spacer-2);
      ifp-ai-button,
      ifp-button {
        margin: $spacer-0 $spacer-2 $spacer-3;
      }
    }
  }
}

@include tablet-horizondal {
  .ifp-ai-report {
  }
}

:host::ng-deep {
  .ifp-ai-report {
    // @media print {
    //   -webkit-print-color-adjust:exact;
    //   -webkit-filter: opacity(1);
    //   print-color-adjust: exact;
    // }
    &__reject {
      .ifp-btn__icon {
        font-size: 8px;
        position: relative;
        top: 1px;
      }
    }
  }
}
:host-context([dir="rtl"]) {
  .ifp-ai-report {
    direction: ltr;
  }

}
