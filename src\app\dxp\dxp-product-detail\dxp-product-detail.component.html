<ifp-bg-page class="ifp-dxp-detail">
  <div class="ifp-page-bg__content">
    <div class="ifp-container">
      <app-ifp-breadcrumbs [pageData]="pageData" class="ifp-dxp-detail__breadcrumb"></app-ifp-breadcrumbs>

      <div class="ifp-dxp-detail__wrapper">

        <div class="ifp-dxp-detail__banner-sec">
          <div [ngStyle]="{'background-image': 'url(../../../assets/images/dxp-sample-image.jpg)'}" class="ifp-dxp-detail__img"></div>
          <div class="ifp-dxp-detail__data">
            <h2 class="ifp-dxp-detail__heading">{{displayName()}}</h2>
            <p class="ifp-dxp-detail__desc">{{shortDescription()}}</p>
            <p class="ifp-dxp-detail__entity">{{organization()}}</p>
          </div>
        </div>

        <p class="ifp-dxp-detail__subtitle">{{'Assets' | translate}}</p>

        <ifp-data-table class="ifp-event__detail-table" [loader]="tableLoader" [tableHead]="detailTable().head" [tableData]="detailTable().data" [headerSettings]="detailTable().headerSettings" (sortEvent)="sortData($event)" (customEventEmitter)="onTableCustomEvent($event)"></ifp-data-table>
      </div>
    </div>
  </div>
</ifp-bg-page>

@if (showMetadataModal()) {
  <app-ifp-modal [overlayType]="'transparent'" #metadataModal>
    @if (selectedAssetMetadata()) {
      <div class="ifp-dxp-detail__modal">
        <div class="ifp-dxp-detail__modal-header">
          <h3 class="ifp-dxp-detail__modal-title">Metadata</h3>
          <em class="ifp-icon ifp-icon-cross ifp-dxp-detail__modal-close" (click)="onCloseModal()"></em>
        </div>
        <div class="ifp-dxp-detail__modal-body">
          <ul class="ifp-dxp-detail__metadata-list">
            <li  class="ifp-dxp-detail__metadata-item"><strong>Size:</strong> {{ selectedAssetMetadata().metadata?.size?.value ?? '-' }}</li>
            <li  class="ifp-dxp-detail__metadata-item"><strong>Format:</strong> {{ selectedAssetMetadata().metadata?.format?.value ?? '-' }}</li>
            <li  class="ifp-dxp-detail__metadata-item"><strong>Columns:</strong> {{ selectedAssetMetadata().metadata?.numColumns?.value ?? '-' }}</li>
            <li  class="ifp-dxp-detail__metadata-item"><strong>Rows:</strong> {{ selectedAssetMetadata().metadata?.numRows?.value ?? '-' }}</li>
            <li  class="ifp-dxp-detail__metadata-item"><strong>Last data refresh:</strong> {{ selectedAssetMetadata().lastRefreshDate ?? '-' }}</li>
            <li  class="ifp-dxp-detail__metadata-item"><strong>Created:</strong> {{ selectedAssetMetadata().createdAt ?? '-' }}</li>
          </ul>
        </div>
      </div>
    }
  </app-ifp-modal>
}

@if (showDictionaryModalFn()) {

  <app-ifp-modal [overlayType]="'transparent'" #dictionaryModal>
    <div class="ifp-dxp-detail__modal">
      <div class="ifp-dxp-detail__modal-header">
        <h3 class="ifp-dxp-detail__modal-title">Dictionary</h3>
        <em class="ifp-icon ifp-icon-cross ifp-dxp-detail__modal-close" (click)="onCloseDictionaryModal()"></em>
      </div>
      <div class="ifp-dxp-detail__modal-body">
        <ifp-data-table
          class="ifp-dxp-detail__modal-table ifp-dxp-detail__modal-table--dictionary"
          [tableHead]="dictionaryTable().head"
          [tableData]="dictionaryTable().data"
          [headerSettings]="dictionaryTable().headerSettings"
        ></ifp-data-table>
      </div>
    </div>
  </app-ifp-modal>
}

@if (showSampleDataModalFn()) {
  <app-ifp-modal [overlayType]="'transparent'" #sampleDataModal>
    <div class="ifp-dxp-detail__modal">
      <div class="ifp-dxp-detail__modal-header">
        <h3 class="ifp-dxp-detail__modal-title">Sample Data</h3>
        <em class="ifp-icon ifp-icon-cross ifp-dxp-detail__modal-close" (click)="closeSampleDataModal()"></em>
      </div>
      <div class="ifp-dxp-detail__modal-body">
        <ifp-data-table
          class="ifp-dxp-detail__modal-table ifp-dxp-detail__modal-table--sample"
          [tableHead]="sampleDataTableFn().head"
          [tableData]="sampleDataTableFn().data"
          [headerSettings]="sampleDataTableFn().headerSettings"
        ></ifp-data-table>
      </div>
    </div>
  </app-ifp-modal>
}

