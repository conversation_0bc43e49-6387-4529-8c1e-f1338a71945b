import { Injectable } from '@angular/core';
import { HttpService } from '../http/http.service';
import { insightsApi } from '../../apiConstants/chart-insights.api.constants';

@Injectable({
  providedIn: 'root'
})
export class InsightsService {

  constructor(private _http: HttpService) {}

  // Add comments API service start
  getInsightsData(nodeId: string | number){
    return this._http.get(`${insightsApi.getInsights}/${nodeId}`);
  }

  addInsights (data: any) {
    return this._http.post(insightsApi.addInsights, data);
  }

  editInsights (data: any, insightId: string | number) {
    return this._http.post(`${insightsApi.updateInsights}/${insightId}`, data);
  }

  deleteInsights (insightId: string | number) {
    return this._http.post(`${insightsApi.deleteInsights}/${insightId}`);
  }

}
