/* tslint:disable:no-unused-variable */
import { async, ComponentFixture, TestBed } from '@angular/core/testing';
import { By } from '@angular/platform-browser';
import { DebugElement } from '@angular/core';

import { IfpLiveabilityDashboardComponent } from './ifp-liveability-dashboard.component';

describe('IfpLiveabilityDashboardComponent', () => {
  let component: IfpLiveabilityDashboardComponent;
  let fixture: ComponentFixture<IfpLiveabilityDashboardComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ IfpLiveabilityDashboardComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(IfpLiveabilityDashboardComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
