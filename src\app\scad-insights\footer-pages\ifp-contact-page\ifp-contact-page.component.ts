import { CommonModule } from '@angular/common';
import { AfterViewChecked, ChangeDetectorRef, Component, ViewChild } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { Title } from '@angular/platform-browser';
import { TranslateModule } from '@ngx-translate/core';
import { buttonClass } from 'src/app/scad-insights/core/constants/button.constants';
import { PageData } from 'src/app/scad-insights/core/interface/molecule/breadcrumb.interface';
import { IFPMsalService } from 'src/app/scad-insights/core/services/IFP-msal.service';
import { FooterService } from 'src/app/scad-insights/core/services/footer/footer.service';
import { ToasterService } from 'src/app/scad-insights/core/services/tooster/ToastrService.service';
import { IfpButtonComponent } from 'src/app/scad-insights/ifp-widgets/ifp-atoms/ifp-button/ifp-button.component';
import { IfpCheckboxComponent } from 'src/app/scad-insights/ifp-widgets/ifp-atoms/ifp-checkbox/ifp-checkbox.component';
import { IfpBreadcrumbsComponent } from 'src/app/scad-insights/ifp-widgets/ifp-molecules/ifp-breadcrumbs/ifp-breadcrumbs.component';
import { SubSink } from 'subsink';
import { title } from '../../core/constants/header.constants';

@Component({
    selector: 'app-ifp-contact-page',
    templateUrl: './ifp-contact-page.component.html',
    styleUrls: ['./ifp-contact-page.component.scss'],
    imports: [CommonModule, TranslateModule, IfpBreadcrumbsComponent, IfpCheckboxComponent, IfpButtonComponent, FormsModule]
})
export class IfpContactPageComponent implements AfterViewChecked {
  @ViewChild('contactTextArea') contactTextArea!: HTMLTextAreaElement;
  buttonClass = buttonClass;
  messageLimit: number = 4000;
  subs: SubSink = new SubSink();
  pageData: PageData[] = [
    {
      title: 'Home',
      route: '/home'
    },
    {
      title: 'Contact',
      route: ''
    }
  ];

  myConcern: string = '';
  organization: string = '';
  subject: string = '';
  username: string = '';
  ifConcernInvalid: boolean = false;
  ifTextAreaInvalid: boolean = false;
  public radio = true;

  constructor(private _titleService: Title, private _footerService: FooterService, private _msal: IFPMsalService, private _toaster: ToasterService, private _cdr: ChangeDetectorRef) {
    this._titleService.setTitle(`${title.bayaan} | Contact Us`);

    (window as any)?.dataLayer?.push({
      'event': 'page_load',
      'page_title_var': 'Contact Us',
      'userId': this._msal.convertEmail(this._msal.getLoginData.account.username)
    });

    this.username = this._msal.getLoginData.account.username;
  }

  isChecked(event: any) {
    this.subject = event;
    this.ifConcernInvalid = false;
  }

  ngAfterViewChecked(): void {
    if (!this.radio) {
      this.radio = true;
      this._cdr.detectChanges();
    }

  }

  submitForm() {
    if (this.subject !== '' && this.myConcern !== '') {
      const data = {
        myConcern: this.myConcern,
        organization: this.organization,
        subject: this.subject,
        userName: this.username
      };
      this.subs.add(this._footerService.sendEmail(data).subscribe((res: any) => {
        if (res) {
          this._toaster.success('Message sent successfully');
          this.myConcern = '';
          this.radio = false;
          this._cdr.detectChanges();
        }
      }));
    } else {
      this.ifConcernInvalid = this.subject === '' ? true : false;
      this.ifTextAreaInvalid = this.myConcern === '' ? true : false;
      this._toaster.error('Mandatory fields missing');
    }
  }
}
