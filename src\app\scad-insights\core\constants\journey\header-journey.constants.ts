export const defaultStepOptions = {
  classes: 'ifp-journey',
  scrollTo: false,
  cancelIcon: {
    enabled: true
  }
};

export const stepsButtons = {
  back: {
    classes: 'ifp-btn--secondary',
    // secondary: true,
    text: 'Back',
    type: 'back'
  },
  cancel: {
    classes: 'ifp-btn--secondary',
    // secondary: true,
    text: 'Exit',
    type: 'cancel'
  },
  next: {
    classes: 'ifp-btn--primary',
    text: 'Next',
    type: 'next'
  }
};

export const steps:any = [
  {
    attachTo: {
      element: '.ifp-header__settings-item--profile',
      on: 'bottom'
    },
    buttons: [stepsButtons.cancel, stepsButtons.next],
    classes: 'ifp-header__settings-item ifp-header__settings-item--profi',
    id: 'header-profile',
    title: 'Profile',
    text: 'Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industrys standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book.'
  },
  {
    attachTo: {
      element: 'ifp-img',
      on: 'bottom'
    },
    buttons: [stepsButtons.cancel, stepsButtons.next],
    classes: 'custom-class-name-1 custom-class-name-2',
    id: 'header-notification',
    title: 'Notification',
    text: 'Content in paragraph at top'
  },
  {
    attachTo: {
      element: '.ifp-header__lang',
      on: 'bottom'
    },
    buttons: [stepsButtons.cancel, stepsButtons.next],
    classes: 'ifp-header__settings-item ifp-header__settings-item--select',
    id: 'header-lang',
    title: 'Language',
    text: 'Content in div at left only with  next button'
  },
  {
    attachTo: {
      element: '.ifp-header__theme',
      on: 'bottom'
    },
    buttons: [stepsButtons.cancel, stepsButtons.next],
    classes: 'ifp-header__settings-item ifp-header__settings-item--select',
    id: 'header-lang',
    title: 'Theme',
    text: 'Content in div at left only with  next button'
  },
  {
    attachTo: {
      element: '.ifp-header__settings-item--user',
      on: 'bottom'
    },
    buttons: [stepsButtons.cancel, stepsButtons.next],
    classes: 'ifp-header__settings-item ifp-header__settings-item--select',
    id: 'header-user',
    title: 'Accesibility',
    text: 'Content in div at left only with  next button'
  },
  {
    attachTo: {
      element: '.ifp_home',
      on: 'bottom'
    },
    buttons: [stepsButtons.cancel, stepsButtons.next],
    classes: 'ifp-header__settings-item ifp-header__settings-item--select',
    id: 'header-nav',
    title: 'Home',
    text: 'Content in div at left only with  next button'
  },
  {
    attachTo: {
      element: '.ifp_domain',
      on: 'bottom'
    },
    buttons: [stepsButtons.cancel, stepsButtons.next],
    classes: 'ifp-header__settings-item ifp-header__settings-item--select',
    id: 'header-nav',
    title: 'Domain',
    text: 'Content in div at left only with  next button'
  },
  {
    attachTo: {
      element: '.ifp_my_apps',
      on: 'bottom'
    },
    buttons: [stepsButtons.cancel, stepsButtons.next],
    classes: 'ifp-header__settings-item ifp-header__settings-item--select',
    id: 'header-nav',
    title: 'My Bookmarks',
    text: 'Content in div at left only with  next button'
  },
  {
    attachTo: {
      element: '.ifp-header__search-input',
      on: 'bottom'
    },
    buttons: [stepsButtons.cancel, stepsButtons.next],
    classes: 'ifp-header__settings-item ifp-header__settings-item--select',
    id: 'header-search',
    title: 'Search 1',
    text: 'Content in div at left only with  next button'
  },
  {
    attachTo: {
      element: '.ifp-header__search-btn',
      on: 'bottom'
    },
    buttons: [stepsButtons.cancel, stepsButtons.next],
    classes: 'ifp-header__settings-item ifp-header__settings-item--select',
    id: 'header-search 2',
    title: 'Search 1',
    text: 'Content in div at left only with  next button'
  },
  {
    attachTo: {
      element: '.ifp-myApps',
      on: 'bottom'
    },
    buttons: [stepsButtons.cancel, stepsButtons.next],
    classes: 'ifp-header__settings-item ifp-header__settings-item--select',
    id: 'add-myapps',
    title: 'Add To my bookmarks',
    text: 'Content in div at left only with  next button'
  },
  {
    attachTo: {
      element: '.ifp-notication-action',
      on: 'bottom'
    },
    buttons: [stepsButtons.cancel, stepsButtons.next],
    classes: 'ifp-header__settings-item ifp-header__settings-item--select',
    id: 'add-notification',
    title: 'Add Or Remove Notification',
    text: 'Content in div at left only with  next button'
  },
  {
    attachTo: {
      element: '.ifp-whats-new-card__rect--small',
      on: 'bottom'
    },
    buttons: [stepsButtons.cancel, stepsButtons.next],
    classes: 'ifp-header__settings-item ifp-header__settings-item--select',
    id: 'card-small',
    title: 'Small View',
    text: 'Content in div at left only with  next button'
  },
  {
    attachTo: {
      element: '.ifp-whats-new-card__rect--large',
      on: 'bottom'
    },
    buttons: [stepsButtons.cancel, stepsButtons.next],
    classes: 'ifp-header__settings-item ifp-header__settings-item--select',
    id: 'card-large',
    title: 'Expanded View',
    text: 'Content in div at left only with  next button'
  },
  {
    attachTo: {
      element: '.ifp-open-new',
      on: 'bottom'
    },
    buttons: [stepsButtons.cancel, stepsButtons.next],
    classes: 'ifp-header__settings-item ifp-header__settings-item--select',
    id: 'open-new',
    title: 'Open New Tab',
    text: 'Content in div at left only with  next button'
  },
  {
    attachTo: {
      element: '.ifp-whats-new-card__download',
      on: 'bottom'
    },
    buttons: [stepsButtons.cancel, stepsButtons.next],
    classes: 'ifp-dropdown__btn',
    id: 'ifp-csv-download',
    title: 'Download',
    text: 'Content in div at left only with  next button'
  }
];

export const headerNavJourney = [
  {
    heading: 'Domain',
    description: ['Choose between the 4 kinds of Data', 'Choose the domain', 'Mouse over to navigate the domain or click to go the page of the domain']
  },
  {
    heading: 'My Bookmarks',
    description: ['Choose your favorite indicators']
  },
  {
    heading: 'Glossary',
    description: ['Definition of the terms']
  }
];


