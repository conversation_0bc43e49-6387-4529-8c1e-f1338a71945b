import { Component, inject, input, InputSignal, linkedSignal, OnInit, output, OutputEmitterRef } from '@angular/core';
import { FormBuilder, FormControl, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { IfpButtonComponent } from "../../../scad-insights/ifp-widgets/ifp-atoms/ifp-button/ifp-button.component";
import { buttonClass } from 'src/app/scad-insights/core/constants/button.constants';
import { TranslateModule } from '@ngx-translate/core';
import { IfpDbDropdownComponent } from 'src/app/dashboard-builder/molecule/ifp-db-dropdown/ifp-db-dropdown.component';
import { ApiService } from 'src/app/scad-insights/core/services/api.service';
import { SelectedProduct } from '../../widgets/dxp-accordian/dxp-accordian.component';
import { dxpApi } from '../../dxp.constants';
import { signal } from '@angular/core';

@Component({
  selector: 'ifp-dxp-visualization-filter-form',
  imports: [ReactiveFormsModule, IfpButtonComponent, TranslateModule, IfpDbDropdownComponent],
  templateUrl: './dxp-visualization-filter-form.component.html',
  styleUrl: './dxp-visualization-filter-form.component.scss'
})
export class DxpVisualizationFilterFormComponent implements OnInit {
  private readonly _formBuilder = inject(FormBuilder);
  private readonly _apiService =  inject(ApiService);
  public columnList: InputSignal<DxpKpiCustomFilterColumnList[]> = input([{name: '', values: [{id: '', value: ''}]}]);
  public buttonClass = buttonClass;
  public selectedColumn = linkedSignal(() => {
    console.log(this.columnList());

    return this.columnList()[0];
  });
  public addFilter: OutputEmitterRef<DxpKpiCustomFilter> = output();
  public cancelFilter: OutputEmitterRef<void> = output();
  public selectedProductDetails = input<SelectedProduct>();
  public columnfiltervalues = signal<any[]>([]);

  public addFilterForm!: FormGroup<AddFilterForm>;

    ngOnInit(): void {
      this.initForm();
    }

  initForm() {
    this.addFilterForm = this._formBuilder.group({
      column: [null, Validators.required],
      label: [null],
      values: [null],
      default: [null, Validators.required],
    });
  }

  onSelectColumn(column: DxpKpiCustomFilterColumnList) {
    this.selectedColumn.set(column);
    this.addFilterForm.patchValue({
      values: null,
      default: null
    })
    this.getColumnFilterValue()
  }

  onSelectDefault(value: string) {
    this.addFilterForm.controls.default.setValue(value);
  }

  onAddFilter() {
    const values = this.addFilterForm.value;
    const data = {
      column: values.column.name,
      filterOptions: this.columnfiltervalues(),
      filterLabel: values.label && values.label !== '' ? values.label : values.column.name,
      defaultValue: values.default,
      data_type:values.column.data_type
    };
    this.addFilterForm.reset();
    this.addFilter.emit(data);
  }

  onCancelFilter() {
    this.addFilterForm.reset();
    this.cancelFilter.emit();
  }

  getColumnFilterValue() {
    const selectedProduct = this.selectedProductDetails();
    const selectedColumn = this.selectedColumn();
    if (!selectedProduct?.sourceProductId || !selectedProduct?.sourceAssetId || !selectedColumn?.name) {
      return;
    }
    const url = dxpApi.distinctValues(selectedProduct.sourceProductId, selectedProduct.sourceAssetId);
    const payload = { column: selectedColumn.name };
    this._apiService.postMethodRequest(url, payload).subscribe({
      next: (resp) => {
        const mapped = Array.isArray(resp) ? resp.map((v: any) => ({ id: v , value: v})) : [];
        this.columnfiltervalues.set(mapped);
      },
      error: (err) => {
        this.columnfiltervalues.set([]);
      }
    });
  }

}

interface AddFilterForm {
  column: FormControl;
  label: FormControl;
  values: FormControl;
  default: FormControl;
}

export interface DxpKpiCustomFilterColumnList {
  name: string;
  values: {id: string; value: string}[];
}

export interface DxpKpiCustomFilter {
  column: string;
  filterLabel: string;
  filterOptions: {id: string; value: string}[];
  defaultValue: string;
}
