@use "../../../../assets/ifp-styles/abstracts/index" as *;

:host {
  display: block;
}

.ifp-db-card {
  border-radius: 10px;
  border: 1px solid $ifp-color-grey-7;
  padding: $spacer-3;
  background-color: $ifp-color-white;
  overflow: hidden;
  position: relative;

  &__configure-wrapper {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    height: 100%;
    width: 100%;
  }

  &__preview-img {
    margin: $spacer-3 $spacer-0;
  }

  &__configure-txt {
    font-size: $ifp-fs-6;
    font-weight: $fw-bold;
    margin: $spacer-2 $spacer-0;
  }

  &__configure-desc {
    font-size: $ifp-fs-4;
    color: $ifp-color-secondary-grey;
    margin: $spacer-2 $spacer-0;
  }

  &__configure-btn {
    display: block;
    padding: $spacer-2;
  }

  &--active {
    border: 1px solid $ifp-color-secondary-blue-dark;
  }

  &__chart-header-wrapper {
    display: flex;
    align-items: center;
    justify-content: space-between;


    .ifp-icon-menu {
      font-size: $ifp-fs-6;
      color: $ifp-color-grey-9;
    }
  }

  &__card-title-grp {
    display: flex;
    align-items: center;

    .ifp-icon {
      font-size: $ifp-fs-6;
      margin-inline-end: $spacer-4;
      font-weight: $fw-semi-bold;
    }
  }

  &__chart-title {
    font-size: $ifp-fs-6;
    font-weight: $fw-semi-bold;
  }

  &__sub-title-wrapper {
    margin: $spacer-4 $spacer-0;
    font-weight: $ifp-fs-5;
    font-weight: $fw-regular;
    color: $ifp-color-grey-9;
  }
}