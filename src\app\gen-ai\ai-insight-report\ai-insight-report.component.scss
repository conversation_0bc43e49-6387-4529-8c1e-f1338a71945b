@use "../../../assets/ifp-styles/abstracts/index" as *;
@use "../../../assets/ifp-styles/components/ai-insights" as *;
$side-spacing: $spacer-5;
.ifp-ai-report {
  margin-bottom: $spacer-3;
  .ifp-container--sm {
    max-width: 85%;
  }
  position: relative;
  min-height: calc(100vh - 242px);

  &__module-curve {
    border-radius: 20px;
  }
  &__bg {
    position: absolute;
    width: 100%;
    height: 100%;
    object-position: top;
    top: 0;
    left: 0;
  }
  &__header {
    text-align: center;
    margin-top: $spacer-5;
    margin-bottom: $spacer-5;
  }

  &__heading {
    font-size: $ifp-fs-13;
    font-weight: $fw-bold;
    margin-bottom: $spacer-3;
  }
  &__breadcrumbs,
  &__header,
  &__main {
    position: relative;
    z-index: 1;
  }
  &__main-title {
    display: flex;
    justify-content: space-between;
    margin-top: $spacer-5;
    padding-inline-end: $spacer-5;
    align-items: center;
  }
  &__main-title-text {
    margin-inline-end: $spacer-3;
    font-size:$ifp-fs-11;
    font-weight: $fw-bold;
    background-color: $ifp-color-grey-18;
    padding: $spacer-3 $spacer-7 $spacer-3 $spacer-5;
    border-end-end-radius: 50px;
    border-start-end-radius: 50px;
    position: relative;

    &::before {
      content: '';
      background-color: $ifp-color-green;
      height: 40px;
      width: 6px;
      border-radius: 0 5px 5px 0;
      position: absolute;
      top: 50%;
      left: 0;
      transform: translateY(-50%);

    }
  }
  &__loader {
    min-height: 400px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  &__main {
    margin-top: $spacer-4;
    border-radius: 20px;
    overflow: hidden;
    background-color: $ifp-color-section-white;
  }
  &__main-head-left {
    display: flex;
    align-items: center;
  }
  &__domain-icon {
    margin-inline-end: $spacer-2;
  }
  &__date-badge {
    border-radius: 40px;
    background-color: $ifp-color-grey-15;
    font-weight: $fw-medium;
    white-space: nowrap;
    padding: $spacer-2 $spacer-4;
    height: 100%;
    color: $ifp-color-grey-14;
  }
  &__domain-name {
    font-size: $ifp-fs-9;
    font-weight: $fw-bold;
  }
  &__card,  &__impact-card {
    box-shadow: 0 18.05px 96.14px 7.18px rgb(231 231 231);
    min-height: 200px;
    border-radius: 20px;
  }
  &__impact-card {
    float: right;
    margin-inline-start:  $spacer-7;
    margin-bottom: $spacer-2;
    width: 45%;
    padding: $spacer-3;
  }
  &__sentimental-card {
    float: right;
    padding: $spacer-3;
    margin-inline-start: $spacer-4;
    width: 50%;
    background-color: $ifp-color-section-white;
  }
  &__clear{
    margin-bottom: $spacer-4;
  }
&__insight-card {
  width: calc(33.33% - (2 * $spacer-3)) ;
  margin: $spacer-3;

}
&__insight-card-wrapper {
  display: flex;
  flex-wrap: wrap;
  margin: $spacer-0 (-$spacer-3);
}

  &__module-header,
  &__main-head,
  &__drivers-desc {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  &__main-head {
    padding: $spacer-4 $side-spacing;
  }
  &__main-head-right {
    display: flex;
    align-items: center;
    margin: $spacer-0 (-$spacer-2);
    .ifp-ai-report__btn {
      margin: $spacer-0 $spacer-2;
    }
  }
  &__badge-text{
    font-size: $ifp-fs-4;
    color: $ifp-color-black;
    font-weight: $fw-bold;
    margin: $spacer-2 $spacer-0;
    display: inline-block;
  }
  &__drivers-desc {
    align-items: flex-end;
    margin-bottom: $spacer-3;
  }
  &__clear{
    clear: both;
  }

  &__module-body {
    display: flex;
    margin: $spacer-0 (-$spacer-3);
  }
  &__sec-1 {
    width: 40%;
    margin: $spacer-0 $spacer-3;
  }
  &__sec-2 {
    width: calc(60% - $spacer-3);
    padding: $spacer-4 $spacer-4;
    margin: $spacer-0 $spacer-3;
    background-color:$ifp-color-grey-18 ;
    border-radius: 20px;
  }
  &__title {
    font-size: $ifp-fs-5;
    font-weight: $fw-bold;
    margin-bottom: $spacer-3;
    &--mar-0 {
      margin-bottom: $spacer-0;
    }
  }

  // comparison module start
  &__compare {
    padding: $spacer-3 $spacer-5 $side-spacing;
  }
  &__compare-inner {
    // padding: $side-spacing;
    // background-color: $ifp-color-pale-grey-50;
  }
  &__source{
    margin-top: $spacer-3;
   color: $ifp-color-secondary-grey;
   margin-bottom: $spacer-4;
  }

  &__sources{
    padding-inline-end: $spacer-3;
    margin-inline-end: $spacer-3;
    border-inline-end: 1px solid $ifp-color-grey-7;
    &:last-child{
      padding-inline-end: $spacer-0;
      margin-inline-end: $spacer-0;
      border-inline-end: 0;
    }
  }
  &__main-download-wrap {
    display: flex;
  }
  &__main-download {
    margin-inline-start: $spacer-2;
  }
  &__stepper {
    display: block;
    margin: $spacer-5 $spacer-0;

  }
    &__matrics-markdown {

  }
  &__desc {
    text-align: left;
    font-size: $ifp-fs-4;
  }
}

ifp-ai-insight-consolidated-report-sentiment-card {
  height: 100%;
}
:host::ng-deep {
  // @media print {
  //   -webkit-print-color-adjust:exact;
  //   -webkit-filter: opacity(1);
  //   print-color-adjust: exact;
  // }
    .ifp-ai-report__matrics-markdown {
    .customMarkdown{
      h2,
      h4 {
        font-weight: $fw-semi-bold;
        margin: $spacer-0 $spacer-0 $spacer-2;
      }
      p {
        margin-top: $spacer-0;

      }
      li {
        text-align: start;
      }
      ul {
        margin-left: $spacer-0;
        padding-left:  $spacer-0;
        margin-right: $spacer-6;
        transform: translateX($spacer-6);
      }
      strong {
        color: $ifp-color-black;
      }
    }
  }
  .ifp-ai-insight-cr-sentiment-card {
    height: 100%;
  }
  .ifp-ai-report {
    &__reject {
      .ifp-btn__icon {
        font-size: 8px;
        position: relative;
        top: 1px;
      }
    }
  }
}
@include desktop-lg  {
  .ifp-ai-report {
    &__sec-1 ,  &__sec-2{
      width: 50%;
    }
  }
}
@include desktop-sm {
  .ifp-ai-report {
    &__insight-card {
      width: calc(50% - 32px);
    }
  }
}

@include tablet-horizondal {
  .ifp-ai-report {
    &__module-body {
      flex-wrap: wrap;
    }
    &__sec-1 {
      width: 100%;
    }
    &__sec-2 {
      width: 100%;
      margin-top: $spacer-4;
    }
    &__insight-card-wrapper {
      flex-wrap: wrap;
      margin: 0;
    }
    &__insight-card {
      width: 100%;
      margin: $spacer-0;
      margin-top: $spacer-3;
    }
    &__side-spacing {
      padding: 0;
      margin:$spacer-5;
    }
    &__impact-card {
      width: 100%;
      float: none;
    }
    &__indicator-card {
      display: block;
    }
     &__main-title {
      display: block;
     }
    &__main-head {
      display: block;
    }
  }
}




@include mobile {
  .ifp-ai-report {
    &__module-body {
      flex-wrap: wrap;
    }
    &__sec-1 {
      width: 100%;
    }
    &__sec-2 {
      width: 100%;
      margin: $spacer-4;
    }
    &__insight-card-wrapper {
      flex-wrap: wrap;
      margin: 0;
    }
    &__insight-card {
      width: 100%;
      margin: $spacer-0;
      margin-top: $spacer-3;
    }
    &__side-spacing {
      padding: 0;
      margin:$spacer-5;
    }
    &__impact-card {
      width: 100%;
      float: none;
    }
    &__indicator-card {
      display: block;
    }
     &__main-title {
      display: block;
     }
    &__main-head {
      display: block;
    }
  }
}

@include mobile {
  .ifp-ai-report {
    &__module-body {
      flex-wrap: wrap;
    }
    &__sec-1 {
      width: 100%;
    }
    &__sec-2 {
      width: 100%;
      margin: $spacer-4;
    }
    &__insight-card-wrapper {
      flex-wrap: wrap;
      margin: 0;
    }
    &__insight-card {
      width: 100%;
      margin: $spacer-0;
      margin-top: $spacer-3;
    }
    &__side-spacing {
      padding: 0;
      margin:$spacer-5;
    }
    &__impact-card {
      width: 100%;
      float: none;
    }
    &__indicator-card {
      display: block;
    }
     &__main-title {
      display: block;
     }
    &__main-head {
      display: block;
    }
  }
}



@include tablet-horizondal {
  .ifp-ai-report {
    &__main-title-text {
      margin-bottom: $spacer-3;
    }
    &__main-download-wrap,&__main-head-right {
      justify-content: center;
    }
    &__main-head-left {
      margin-bottom: $spacer-3;
    }
  }
}


:host-context(.ifp-dark-theme) {
  .ifp-ai-report {
    &__main-title-text,  &__sec-2  {
      background-color: $ifp-color-black-global;
    }

    &__impact-card {
      box-shadow: 0 18.05px 96.14px 7.18px #6f6f6f1a;
    }


  }
}
:host-context([dir="rtl"]) {
  .ifp-ai-report {
    direction: ltr;
  }

}
