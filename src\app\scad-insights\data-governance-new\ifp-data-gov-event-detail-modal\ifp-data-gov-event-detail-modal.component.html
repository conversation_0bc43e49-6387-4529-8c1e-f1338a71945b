<div class="ifp-event">
  <div class="ifp-event__header">
    <p class="ifp-event__main-head">{{'Event Details' | translate}}</p>
    <em class="ifp-icon ifp-icon-cross ifp-event__close" (click)="onCLoseModal()"></em>
  </div>
  <div class="ifp-event__body">
    <div class="ifp-event__body-header">
      <div class="ifp-event__title-sec">
        <!-- <p class="ifp-event__title">{{title()}}</p> -->
        <p class="ifp-event__date"><em class="ifp-icon ifp-icon-calender"></em>{{date() | date:dateFormat.longDate}}</p>
      </div>
      <div class="ifp-event__dropdown-sec">
        <p class="ifp-event__dropdown-label">{{'Status' | translate}}</p>
        <app-ifp-dropdown class="ifp-event__dropdown" [selectAll]="true" [dropDownItems]="statusList()" [leftEnable]="true"
          [isMulti]="false" [key]="'value'" (dropDownItemClicked)="changeStatus($event)"></app-ifp-dropdown>
      </div>
    </div>
    @if (dayDetail().data && dayDetail().data.length) {
    <ifp-data-table [tableHead]="dayDetail().head" [tableData]="dayDetail().data" class="ifp-event__detail-table"
      [headerSettings]="dayDetail().headerSettings" (sortEvent)="sortData($event)"></ifp-data-table>
    }

    <app-pagination class="ifp-event__pagination" [customPagination]="true" [offset]="offset" [limit]="perPage"
      [size]="totalDataCount()" (limitChange)="limitChanged($event)" (pageChange)="onPageChange($event)"></app-pagination>
  </div>
</div>
