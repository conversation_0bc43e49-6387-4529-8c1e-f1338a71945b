<div class="ifp-color-palette">
  <div class="ifp-color-palette__wrapper">
  @for (color of colorList(); track $index) {
    <div class="ifp-color-palette__item" [ngClass]="{'ifp-color-palette__item--selected': selectedColor() === color}" [style.backgroundColor]="color" (click)="onSelectColor(color)">
      <em class="ifp-icon ifp-icon-tick-round-fill ifp-color-palette__tick"></em>
    </div>
  }
  </div>

  <div class="ifp-color-palette__wrapper">
    <div class="ifp-color-palette__item ifp-color-palette__item--dotted" [style.backgroundColor]="customSelectedColor" [ngClass]="{'ifp-color-palette__item--selected': selectedColor() === customSelectedColor}" (click)="onSelectColor(customSelectedColor)">
      <em class="ifp-icon ifp-icon-tick-round-fill ifp-color-palette__tick"></em>
    </div>
    <div class="ifp-color-palette__item ifp-color-palette__item--input">
      <input type="color" class="ifp-color-palette__input" #colorPicker (change)="onSelectColorPicker($event.target)">
    </div>
  </div>
</div>
