@if (!workFlowRunning() && !emptyConnection() && currentConnectionCount >= 2 && sourceData() && columnList &&
columnList.length
!==0 && !errorMessageForTools) {
<!-- <div class="ifp-adv-tool__action">
  <div class="ifp-adv-tool__action-inner">
    <ifp-button [label]="'cancel'" (ifpClick)="onCancel()" class="ifp-adv-tool__button ifp-adv-tool__action-btn"
      [buttonClass]="buttonClass.secondary"></ifp-button>
    <ifp-button [label]="'Save'" (ifpClick)="onSave()" class="ifp-adv-tool__button ifp-adv-tool__action-btn"
   ></ifp-button>
  </div>
</div> -->
<form [formGroup]="joinForm">
  <app-ifp-check-box class="ifp-adv-tool__checkbox ifp-adv-tool__checkbox--radio"
    [label]="'Join by Specific Fields' | translate" [type]="'radio'" [name]="'joinBy'"
    (checked)="selectJoinBy(joinConstants.fieldType)"
    [checkedData]="byIdentifier === joinConstants.fieldType"></app-ifp-check-box>
  <app-ifp-check-box class="ifp-adv-tool__checkbox ifp-adv-tool__checkbox--radio"
    [label]="'Join by Position' | translate" [type]="'radio'" [name]="'joinBy'"
    (checked)="selectJoinBy(joinConstants.positionType)"
    [checkedData]="byIdentifier === joinConstants.positionType"></app-ifp-check-box>



    <div class="ifp-adv-tool__row ifp-adv-tool__row--between">
      <div class="ifp-adv-join__item">
        <p class="ifp-adv-join__item-name">L</p>
        <em class="ifp-icon ifp-icon-left-join"></em>
      </div>
      <div class="ifp-adv-join__item">
        <p class="ifp-adv-join__item-name">J</p>
        <em class="ifp-icon ifp-icon-common-join"></em>
      </div>
      <div class="ifp-adv-join__item">
        <p class="ifp-adv-join__item-name">R</p>
        <em class="ifp-icon ifp-icon-right-join"></em>
      </div>
    </div>
  @if (byIdentifier == joinConstants.fieldType) {
  <!-- <div class="ifp-adv-tool__edit-outer">
  <p class="ifp-adv-tool__label">{{'Left Table Identifier' | translate}}</p>
  @if (leftTableColumn() && leftTableColumn().length) {
    <app-ifp-db-dropdown formControlName="leftIdentifier" class="ifp-adv-tool__dropdown"  [isMultiSelect]="false" [options]="leftTableColumn()" (singleSelected)="onSelectIdentifier($event, 'left')" [selectedSingleItem]="leftTableColumn()[0].name" [defaultSelect]="false" [key]="'name'"></app-ifp-db-dropdown>
  }

</div>

<div class="ifp-adv-tool__edit-outer">
  <p class="ifp-adv-tool__label">{{'Right Table Identifier' | translate}}</p>
  @if (rightTableColumn() && rightTableColumn().length) {
  <app-ifp-db-dropdown formControlName="rightIdentifier" class="ifp-adv-tool__dropdown" [isMultiSelect]="false" [options]="rightTableColumn()" (singleSelected)="onSelectIdentifier($event, 'left')" [selectedSingleItem]="rightTableColumn()[0].name" [defaultSelect]="false" [key]="'name'"></app-ifp-db-dropdown>
  }
</div> -->

  <p class="ifp-adv-tool__desc">{{'Left Join Output (L): Includes the records from the left source that do not have corresponding identifiers in the right source' | translate}}.</p>
  <p class="ifp-adv-tool__desc">{{'Right Join Output (R): Contains the records from the right source that do not have corresponding identifiers in the left source' | translate}}.</p>
  <p class="ifp-adv-tool__desc">{{'Common Join Output (J): Contains the records that have matching identifiers in both the left and right sources' | translate}}.</p>

  <ifp-data-tool-accordian [title]="'New Join'" [enableCheckBox]="false" [isAccordianExpanded]="true" class="ifp-adv-tool__accordian ifp-adv-tool__accordian--tool-box">
    <div class="ifp-accord-box__content">
      <div class="ifp-adv-tool__edit-outer">
        @if (leftTableColumn() && leftTableColumn().length) {
        <p class="ifp-adv-tool__label">{{'Left Source Identifier' | translate}}</p>
        <app-ifp-db-dropdown formControlName="leftIdentifier" class="ifp-adv-tool__dropdown" [isMultiSelect]="false"
          [options]="leftTableColumn()"  [defaultSelect]="false"
          [key]="'name'" [disableTranslation]="true" ></app-ifp-db-dropdown>
        @if (formControls['leftIdentifier'].errors?.['required'] && submitted()) {
        <p class="text-danger">
          {{'*Please choose a specific column.' | translate}}
        </p>
        }
        }
      </div>

      <div class="ifp-adv-tool__edit-outer">
        @if (rightTableColumn() && rightTableColumn().length) {
        <p class="ifp-adv-tool__label">{{'Right Source Identifier' | translate}}</p>
        <app-ifp-db-dropdown formControlName="rightIdentifier" class="ifp-adv-tool__dropdown" [isMultiSelect]="false"
          [options]="rightTableColumn()"  [defaultSelect]="false"
          [key]="'name'" [disableTranslation]="true"></app-ifp-db-dropdown>
        @if (formControls['rightIdentifier'].errors?.['required'] && submitted()) {
        <p class="text-danger">
          {{'*Please choose a specific column.' | translate}}
        </p>
        }
        }
      </div>

      <ifp-button [label]="'Add Join'" (ifpClick)="addJoin()" class="ifp-adv-tool__button"
        [buttonClass]="buttonClass.secondary+ ' '+ buttonClass.large" [iconClass]="'ifp-icon-plus'"></ifp-button>
    </div>
  </ifp-data-tool-accordian>

  } @else {
  <p class="ifp-adv-tool__desc">{{'If the Left source has fewer records than the Right source, only the matching records up to the end of the Left source will be considered from the Right source.' | translate}}</p>
  <p class="ifp-adv-tool__desc">{{'If the Right source has more records than the Left source, empty records will be directly added for the right-hand table columns where there is no match with the Left source.' | translate}}</p>
  <!-- <div class="ifp-adv-tool__row ifp-adv-join__row">
    <div class="ifp-adv-join__item"><img src="../../../../assets/images/ifp-analytics/pos-rtl-join.svg" alt="rtl-join"
        class="ifp-adv-join__item-img"></div>
    <div class="ifp-adv-join__item"><img src="../../../../assets/images/ifp-analytics/pos-ltr-join.svg" alt="ltr-join"
        class="ifp-adv-join__item-img"></div>
  </div> -->
  }

  @if (joinList.length && byIdentifier == joinConstants.fieldType) {
  <div class="ifp-adv-tool__content ifp-adv-tool__content--grey">
    <p class="ifp-adv-tool__sub-title">{{'Added Joins' | translate}}</p>
    @for (join of joinList; let i = $index; track join) {
    <div class="ifp-adv-tool__card">
      <div class="ifp-adv-tool__card-head">
        <p class="ifp-adv-tool__card-title">{{'Join' | translate}} {{i+1}}</p>
        <em class="ifp-icon ifp-icon-minus-round ifp-adv-tool__remove" (click)="removeJoin(i)"></em>
      </div>
      @if (join.sourceColumn && join.sourceColumn.name && join.targetColumn && join.targetColumn.name) {
      <p class="ifp-adv-filter__filter-text">{{'Source' | translate}}: <strong>{{join.sourceColumn.name}}</strong>
        {{'joined with target' | translate}}: <strong>{{join.targetColumn.name}}</strong></p>
      }

    </div>
    }
  </div>
  }


  <div class="ifp-adv-tool__content ifp-adv-tool__content--border">
    <!-- <p class="ifp-adv-tool__sub-title">{{'Left Source Identifier to Join (12000 rows, 12 columns)' | translate}}</p> -->
     @if (leftColumnsArrays?.controls?.length) {
      <app-ifp-check-box class="ifp-adv-tool__checkbox" [label]="'Select All' | translate"
      [checkedData]="checkSelection('leftColumns')" (checked)="onSelectAll($event, 'leftColumns')"></app-ifp-check-box>
     }

    <div formArrayName="leftColumns">
      @for (item of leftColumnsArrays.controls; let i = $index; track item) {
      <ng-container [formGroup]="item">
        <ifp-data-tool-accordian [disableTranslation]="true" [title]="item.value.columnName" [isChecked]="item.value.checked"
          class="ifp-adv-tool__accordian" formControlName="checked" (selectItem)="checkDisabledStatus($event)">
          <div class="ifp-accord-box__content">
            <div class="ifp-adv-tool__edit-outer">
              <p class="ifp-adv-tool__label">{{'Rename Column' | translate}}</p>
              <input formControlName="rename" type="text" class="ifp-adv-tool__input"
                [placeholder]="'Enter here' | translate" (keyup)="updateRename($event)">
            </div>
          </div>
        </ifp-data-tool-accordian>
      </ng-container>
      }
    </div>
  </div>

  <div class="ifp-adv-tool__content">
    <!-- <p class="ifp-adv-tool__sub-title">{{'Right Source Identifier to Join (3800 rows, 5 columns)' | translate}}</p> -->
     @if (rightColumns?.controls?.length) {
      <app-ifp-check-box class="ifp-adv-tool__checkbox" [label]="'Select All' | translate"
      [checkedData]="checkSelection('rightColumns')" (checked)="onSelectAll($event, 'rightColumns')"
      [changeCheckedDataValue]="true"></app-ifp-check-box>
     }

    <div formArrayName="rightColumns">
      @for (item of rightColumns.controls; let i = $index; track item) {
      <ng-container [formGroup]="item">
        <ifp-data-tool-accordian [disableTranslation]="true" [title]="item.value.columnName" [isChecked]="item.value.checked" [isDraggable]="false"
          class="ifp-adv-tool__accordian" formControlName="checked">
          <div class="ifp-accord-box__content">
            <div class="ifp-adv-tool__edit-outer">
              <p class="ifp-adv-tool__label">{{'Rename Column' | translate}}</p>
              <input formControlName="rename" type="text" class="ifp-adv-tool__input"
                [placeholder]="'Enter here' | translate" (keyup)="updateRename($event)">
            </div>
          </div>
        </ifp-data-tool-accordian>
      </ng-container>
      }
    </div>
  </div>
</form>
} @else {
  @if(errorMessageForTools) {
    {{errorMessageForTools}}
  } @else if(workFlowRunning()) {
<div class="ifp-center-loader">
  <app-ifp-spinner></app-ifp-spinner>
  <p> {{'Workflow Running' | translate}} </p>
</div>

} @else if (emptyConnection()){
<p class="ifp-adv-tool__error"> <em class="ifp-icon ifp-icon-exclamation-round">

  </em>
  {{'Please choose a source and target' | translate}}
</p>
} @else if (!sourceData()) {
<p class="ifp-adv-tool__error"> <em class="ifp-icon ifp-icon-exclamation-round">

  </em>
  {{'Must upload atleast two source' | translate}}
</p>
} @else if(currentConnectionCount < 2){ <p class="ifp-adv-tool__error"> <em class="ifp-icon ifp-icon-exclamation-round">

  </em>
  {{'Node must have atleast two connection' | translate}}
  </p>
  }
  @else {
  <app-ifp-spinner></app-ifp-spinner>
  }

  }
