import { AfterViewInit, Component, <PERSON><PERSON><PERSON><PERSON>, OnInit, ViewChild } from '@angular/core';
import { IfpModalComponent } from '../../ifp-widgets/ifp-organism/ifp-modal/ifp-modal.component';
import { IfpNdaComponent } from '../../ifp-widgets/ifp-organism/ifp-nda/ifp-nda.component';
import { SubSink } from 'subsink';
import { NdaCheckService } from '../../core/services/nda-check.service';
import { Router } from '@angular/router';
import { ToasterService } from '../../core/services/tooster/ToastrService.service';
import { IFPMsalService } from '../../core/services/IFP-msal.service';
import { slaService } from '../../core/services/sla/sla.service';

@Component({
    selector: 'ifp-nda-initial',
    imports: [IfpModalComponent, IfpNdaComponent],
    templateUrl: './nda-initial.component.html',
    styleUrl: './nda-initial.component.scss'
})
export class NdaInitialComponent implements OnInit, AfterViewInit, OnDestroy {

  @ViewChild('ndaModal', {static: true}) ndaModal!: IfpModalComponent;

  public role!: string;
  public ndaStatus!: boolean;

  private subs: SubSink = new SubSink();

  constructor(private _ndaService: NdaCheckService, private _router: Router, private _toaster: ToasterService, private _msal: IFPMsalService, private _slaService: slaService) {
    this._slaService.slaLoader$.next(false);
  }

  ngOnInit() {
    this._ndaService.isNdaAccepted();
    this.role = this._ndaService.role();
    this.ndaStatus = this._ndaService.ndaStatus();
  }

  ngAfterViewInit(): void {
    this.ndaModal.createElement();
  }

  onAcceptNda(status: boolean) {
    const data = {isNdaAccepted: status};
    this.subs.add(
      this._ndaService.onNdaAccept(data).subscribe({
        next: ()=>{
          this.ndaModal.removeModal();
          this._router.navigateByUrl('/home');
        },
        error: (error) => {
          this._toaster.error(error.error.message);
        }
      })
    );
  }

  closeNda(status: boolean) {
    if (!status) {
      this.ndaModal.removeModal();
      this._msal.logout();
    }
  }

  ngOnDestroy() {
    this.subs.unsubscribe();
  }
}
