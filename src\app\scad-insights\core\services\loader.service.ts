import { Injectable } from '@angular/core';
import { BehaviorSubject } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class LoaderService {

  public isLoading = new BehaviorSubject(false);

  show(): void {
    this.isLoading.next(true);
    // Add logic to display the loader component
  }

  hide(): void {
    this.isLoading.next(false);
    // Add logic to hide the loader component
  }

}
