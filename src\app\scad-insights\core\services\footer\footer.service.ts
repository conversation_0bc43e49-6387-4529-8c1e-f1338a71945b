import { Injectable } from '@angular/core';
import { HttpService } from '../http/http.service';
import { footerApi } from '../../apiConstants/footer.api.constants';

@Injectable({
  providedIn: 'root'
})
export class FooterService {

  constructor(private _http: HttpService) { }

  getFooterData(){
    return this._http.get(footerApi.footer);
  }

  getAboutUsData(){
    return this._http.get(footerApi.aboutUs);
  }

  sendEmail (data: any) {
    return this._http.post(footerApi.sendMail, data);
  }

  getTermsData () {
    return this._http.get(footerApi.terms);
  }

  getProductsData () {
    return this._http.get(footerApi.products);
  }

  getPrivacy() {
    return this._http.get(footerApi.privacy);
  }
}
