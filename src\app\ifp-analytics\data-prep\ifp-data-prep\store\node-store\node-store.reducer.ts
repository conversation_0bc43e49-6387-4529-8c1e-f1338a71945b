
import {  createFeature, createReducer, on } from '@ngrx/store';
import { NodeState, createNodeInitalState, createNodeStateName } from './node-store.state';
import {  loadCreateConnectionName, loadCreateConnectionRemoveName, loadCreateConnectionUploadUpdateName, loadCreateNodeClear, loadCreateNodeDestination, loadCreateNodeSuccessCleaningCreate, loadCreateNodeSuccessCleaningRemove, loadCreateNodeSuccessCleaningUpdate, loadCreateNodeSuccessColumnCreate, loadCreateNodeSuccessColumnRemove, loadCreateNodeSuccessColumnUpdate, loadCreateNodeSuccessImputName, loadCreateNodeSuccessImputRemoveName, loadCreateNodeSuccessImputUpdateName, loadCreateNodeSuccessUnionCreate, loadCreateNodeSuccessUnionRemove, loadCreateNodeSuccessUnionUpdate, loadNode, loadRemoveConnectionUploadUpdateName } from './node-store.actions';
import { v4 as uuid } from 'uuid';
import { cloneDeep } from 'lodash';
import { connectionType } from '../../constants/if-preps.constants';
import { prepStatusStore } from '../../constants/ifp-state.contants';

export const nodesPrepFeature = createFeature({
  name: createNodeStateName,
  reducer: createReducer(
    createNodeInitalState,
    on(loadNode[loadCreateConnectionName], (state, _action): NodeState  => {
      const curretObject = uuid();
      const connection =   {
        object_id: curretObject,
        name: _action?.typeData === connectionType.ifp ? 'Indicator Added Succesfully' : 'Data uploaded successfully',
        type: connectionType.inputTool,
        time: new Date(),
        configuration: {
          connection: {
            connection_type: _action?.typeData === connectionType.ifp? connectionType.ifp : connectionType.dataset,
            path: _action.id
          }
        }
      };
      const dataValue =  cloneDeep(state.data);
      if (_action.earseData) {
        dataValue.nodes = [];
        dataValue.connections = [];
      }
      if (dataValue.connections?.length !== 0) {
        const lastNode = dataValue.nodes[dataValue.nodes.length-1];
        const connections ={
          source: lastNode?.object_id,
          destination: curretObject
        };
        dataValue.connections.push(connections);
      }

      dataValue.nodes.push(connection);
      return {data: dataValue,  currentStatus: prepStatusStore.datasetAdd};
    }),
    on(loadNode['[prepComponent]CreateNodeAddDupload'], (state, _action): NodeState  => {
      const curretObject = uuid();
      const connection =   {
        object_id: curretObject,
        name: 'Data type modification started!',
        type: connectionType.selectTool,
        time: new Date(),
        status: 'pending',
        configuration: _action.config
      };
      const dataValue =  cloneDeep(state.data);
      const lastNode = dataValue.nodes[dataValue.nodes.length-1];
      const connections ={
        source: lastNode?.object_id,
        destination: curretObject
      };
      dataValue.nodes.push(connection);
      dataValue.connections.push(connections);
      return {data: dataValue,  currentStatus: prepStatusStore.selectDataLoaded};
    }),
    on(loadNode[loadCreateConnectionUploadUpdateName], (state, _action): NodeState  => {
      const curretObject = _action.objectId;
      const connection =   {
        object_id: curretObject,
        time: new Date(),
        name: 'Data type modified successfully!',
        type: connectionType.selectTool,
        configuration: _action.config
      };
      const dataValue =  cloneDeep(state.data);
      const lastIndex = dataValue.nodes.findIndex(data => data.object_id === curretObject);
      if (lastIndex !== -1) {
        dataValue.nodes[lastIndex] = connection;
      } else {
        const lastNode = dataValue.nodes[dataValue.nodes.length-1];
        const connections = {
          source: lastNode?.object_id,
          destination: curretObject
        };
        dataValue.nodes.push(connection);
        dataValue.connections.push(connections);
      }

      return {data: dataValue,  currentStatus: prepStatusStore.selectDataLoaded};
    }),
    on(loadNode[loadRemoveConnectionUploadUpdateName], loadNode[loadCreateNodeSuccessImputRemoveName], loadNode[loadCreateConnectionRemoveName], loadNode[loadCreateNodeSuccessColumnRemove], loadNode[loadCreateNodeSuccessCleaningRemove], (state, _action): NodeState  => {
      const curretObject = _action.objectId;

      const dataValue =  cloneDeep(state.data);
      const lastIndex = dataValue.nodes.findIndex(data => data.object_id === curretObject);
      const connectionValue = dataValue.connections.findIndex(data => data.destination === curretObject);
      if (lastIndex !== -1) {
        dataValue.nodes.splice(lastIndex, 1);
      }
      if (connectionValue !== -1) {
        dataValue.connections.splice(connectionValue, 1);
      }

      return {data: dataValue,  currentStatus: prepStatusStore.nodeRemoved};
    }),
    on(loadNode[loadCreateNodeSuccessImputName], (state, _action): NodeState  => {
      const curretObject = uuid();
      const connection =   {
        object_id: curretObject,
        name: 'Missing values filling started!',
        type: connectionType.imputeTool,
        configuration: _action.config,
        status: 'pending',
        time: new Date()
      };
      const dataValue =  cloneDeep(state.data);
      const lastNode = dataValue.nodes[dataValue.nodes.length-1];
      const connections ={
        source: lastNode?.object_id,
        destination: curretObject
      };
      dataValue.nodes.push(connection);
      dataValue.connections.push(connections);
      return {data: dataValue,  currentStatus: prepStatusStore.imputeDataLoadeded};
    }),
    on(loadNode[loadCreateNodeSuccessImputUpdateName], (state, _action): NodeState  => {
      const curretObject = _action.objectId;
      const connection =   {
        object_id: curretObject,
        name: 'Missing values filled in successfully!',
        type: connectionType.imputeTool,
        time: new Date(),
        configuration: _action.config
      };
      const dataValue =  cloneDeep(state.data);
      const lastIndex = dataValue.nodes.findIndex(data => data.object_id === curretObject);
      if (lastIndex !== -1) {
        dataValue.nodes[lastIndex] = connection;
      } else {
        const lastNode = dataValue.nodes[dataValue.nodes.length-1];
        const connections = {
          source: lastNode?.object_id,
          destination: curretObject
        };
        dataValue.nodes.push(connection);
        dataValue.connections.push(connections);
      }

      return {data: dataValue,  currentStatus: prepStatusStore.imputeDataLoadeded};
    }),
    on(loadNode[loadCreateNodeSuccessUnionCreate], (state, _action): NodeState  => {
      const curretObject = uuid();

      const dataValue =  cloneDeep(state.data);
      const lastNode = dataValue.nodes[dataValue.nodes.length-1];
      const lastNode2 = dataValue.nodes[dataValue.nodes.length-2];
      const config =  cloneDeep(_action.config);
      config['source']= lastNode2?.object_id;
      config['target']=  lastNode?.object_id;
      const connection =   {
        object_id: curretObject,
        name: curretObject,
        type: connectionType.unionTool,
        time: new Date(),
        configuration: config
      };
      const connections ={
        source: lastNode?.object_id,
        destination: curretObject
      };

      const connections2 ={
        source: lastNode2?.object_id,
        destination: curretObject
      };
      dataValue.nodes.push(connection);
      dataValue.connections.push(connections2);
      dataValue.connections.push(connections);
      return {data: dataValue,  currentStatus: prepStatusStore.unionAdded};
    }),
    on(loadNode[loadCreateNodeSuccessUnionRemove], (state, _action): NodeState  => {
      const curretObject = _action.objectIdUnion;

      const dataValue =  cloneDeep(state.data);
      const lastIndex2 = dataValue.nodes.findIndex(data => data.object_id === _action.objectIdData);
      if (lastIndex2 !== -1) {
        dataValue.nodes.splice(lastIndex2, 1);
      }
      const lastIndex = dataValue.nodes.findIndex(data => data.object_id === curretObject);
      const connectionValue = dataValue.connections.findIndex(data => data.destination === curretObject);
      if (lastIndex !== -1) {
        dataValue.nodes.splice(lastIndex, 1);
      }
      if (connectionValue !== -1) {
        dataValue.connections.splice(connectionValue, 1);
      }
      const connectionValue2 = dataValue.connections.findIndex(data => data.destination === curretObject);
      if (connectionValue2 !== -1) {
        dataValue.connections.splice(connectionValue, 1);
      }

      return {data: dataValue,  currentStatus: prepStatusStore.nodeRemoved};
    }),
    on(loadNode[loadCreateNodeSuccessUnionUpdate], (state, _action): NodeState  => {
      const curretObject = _action.objectId;
      const dataValue =  cloneDeep(state.data);
      const lastNode = dataValue.nodes[dataValue.nodes.length-1];
      const lastNode2 = dataValue.nodes[dataValue.nodes.length-2];
      const config =  cloneDeep(_action.config);
      config['source']= lastNode2?.object_id;
      config['target']=  lastNode?.object_id;
      const connection =   {
        object_id: curretObject,
        name: curretObject,
        type: connectionType.unionTool,
        time: new Date(),
        configuration: config
      };
      const lastIndex = dataValue.nodes.findIndex(data => data.object_id === curretObject);
      if (lastIndex !== -1) {
        dataValue.nodes[lastIndex] = connection;
      } else {

        const connections2 ={
          source: lastNode2?.object_id,
          destination: curretObject
        };
        const connections = {
          source: lastNode?.object_id,
          destination: curretObject
        };
        dataValue.nodes.push(connection);
        dataValue.connections.push(connections);
        dataValue.connections.push(connections2);
      }

      return {data: dataValue,  currentStatus: prepStatusStore.unionAddedUpdated};
    }),
    on(loadNode[loadCreateNodeDestination], (state, _action): NodeState  => {
      const curretObject = uuid();
      const connection =   {
        object_id: curretObject,
        name: 'Data Export',
        type: connectionType.outputTool,
        time: new Date(),
        configuration: _action.config
      };
      const dataValue =  cloneDeep(state.data);
      const lastNode = dataValue.nodes[dataValue.nodes.length-1];
      const connections ={
        source: lastNode?.object_id,
        destination: curretObject
      };
      dataValue.nodes.push(connection);
      dataValue.connections.push(connections);
      return {data: dataValue,  currentStatus: prepStatusStore.destination};
    }),
    on(loadNode[loadCreateNodeSuccessColumnCreate], (state, _action): NodeState  => {
      const curretObject = uuid();

      const dataValue =  cloneDeep(state.data);
      const config =  cloneDeep(_action.config);
      const lastNode = dataValue.nodes[dataValue.nodes.length-1];
      const connection =   {
        object_id: curretObject,
        name: 'Calculated field addition started!',
        type: connectionType.formulaTool,
        time: new Date(),
        status: 'pending',
        configuration: config
      };
      const connections ={
        source: lastNode?.object_id,
        destination: curretObject
      };

      dataValue.nodes.push(connection);
      dataValue.connections.push(connections);
      return {data: dataValue,  currentStatus: prepStatusStore.formulaAdded};
    }),
    on(loadNode[loadCreateNodeSuccessColumnUpdate], (state, _action): NodeState  => {
      const curretObject = _action.objectId;
      const dataValue =  cloneDeep(state.data);
      const config =  cloneDeep(_action.config);
      const lastNode = dataValue.nodes[dataValue.nodes.length-1];
      const connection =   {
        object_id: curretObject,
        name: 'Calculated field added successfully!',
        type: connectionType.formulaTool,
        time: new Date(),
        configuration: config
      };
      const lastIndex = dataValue.nodes.findIndex(data => data.object_id === curretObject);
      if (lastIndex !== -1) {
        dataValue.nodes[lastIndex] = connection;
      } else {
        const connections = {
          source: lastNode?.object_id,
          destination: curretObject
        };
        dataValue.nodes.push(connection);
        dataValue.connections.push(connections);
      }

      return {data: dataValue,  currentStatus: prepStatusStore.unionAddedUpdated};
    }),
    on(loadNode[loadCreateNodeClear], (_state, _action): NodeState  => {
      return {
        currentStatus: prepStatusStore.initial,
        data: {
          name: 'Data Processing Workflow',
          description: 'A workflow for processing data',
          nodes: [],
          connections: []
        }
      };
    }),
    on(loadNode[loadCreateNodeSuccessCleaningCreate], (state, _action): NodeState  => {
      const curretObject = uuid();
      const connection =   {
        object_id: curretObject,
        name: 'Data cleaning started!',
        type: connectionType.cleansingTool,
        configuration: _action.config,
        time: new Date(),
        status: 'pending'
      };
      const dataValue =  cloneDeep(state.data);
      const lastNode = dataValue.nodes[dataValue.nodes.length-1];
      const connections ={
        source: lastNode?.object_id,
        destination: curretObject
      };
      dataValue.nodes.push(connection);
      dataValue.connections.push(connections);
      return {data: dataValue,  currentStatus: prepStatusStore.cleaningDataLoaded};
    }),
    on(loadNode[loadCreateNodeSuccessCleaningUpdate], (state, _action): NodeState  => {
      const curretObject = _action.objectId;
      const connection =   {
        object_id: curretObject,
        name: 'Data cleaned successfully',
        type: connectionType.cleansingTool,
        time: new Date(),
        configuration: _action.config
      };
      const dataValue =  cloneDeep(state.data);
      const lastIndex = dataValue.nodes.findIndex(data => data.object_id === curretObject);
      if (lastIndex !== -1) {
        dataValue.nodes[lastIndex] = connection;
      } else {
        const lastNode = dataValue.nodes[dataValue.nodes.length-1];
        const connections = {
          source: lastNode?.object_id,
          destination: curretObject
        };
        dataValue.nodes.push(connection);
        dataValue.connections.push(connections);
      }

      return {data: dataValue,  currentStatus: prepStatusStore.cleaningDataLoaded};
    })
  )
});

export const {
  name, // feature name
  reducer
} = nodesPrepFeature;
