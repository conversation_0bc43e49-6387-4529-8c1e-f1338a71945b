import { Component, inject, OnInit } from '@angular/core';
import { IfpDxpCardComponent } from "../widgets/ifp-dxp-card/ifp-dxp-card.component";
import { Router } from '@angular/router';
import { SubSink } from 'subsink';
import { ApiService } from 'src/app/scad-insights/core/services/api.service';
import { ToasterService } from 'src/app/scad-insights/core/services/tooster/ToastrService.service';
import { ProductDetail } from '../dxp.interface';
import { dxpApi } from '../dxp.constants';
import { IfpCardLoaderComponent } from "../../scad-insights/ifp-widgets/ifp-atoms/ifp-card-loader/ifp-card-loader.component";
import { loaderType } from 'src/app/scad-insights/core/constants/loader.constants';
import { IfpNoDataComponent } from "../../scad-insights/ifp-widgets/ifp-molecules/ifp-no-data/ifp-no-data.component";

@Component({
  selector: 'ifp-dxp-catalog',
  imports: [IfpDxpCardComponent, IfpCardLoaderComponent, IfpNoDataComponent],
  templateUrl: './dxp-catalog.component.html',
  styleUrl: './dxp-catalog.component.scss'
})
export class DxpCatalogComponent implements OnInit {

  private readonly _router: Router = inject(Router);
  private readonly _apiService =  inject(ApiService);
  private readonly _toasterService =  inject(ToasterService);

  public cardList: ProductDetail[] = [];
  public loaderType = loaderType;

  public productListLoader: boolean = true;
  private readonly _subs: SubSink = new SubSink();
  // public productdetailList: ProductDetail[] = [];

  ngOnInit(): void {
    this.getProductList();
  }

  getProductList(searchKey: string = '') {
    this.productListLoader = true;
    this._subs.add(
      this._apiService.getMethodRequest(dxpApi.product, {search: searchKey}).subscribe({
        next: (resp) => {
          this.cardList = resp;
          this.productListLoader = false;
        },
        error: (error) => {
          this._toasterService.error(error?.error?.message);
          this.productListLoader = false;
        }
      })
    )
  }

  onCLickCard(id: string) {
    this._router.navigate(['/dxp/product'], {queryParams: {id: id}});
  }
}

interface DxpCard {
  id: string;
  name: string;
  description: string;
  entity: string;
  image: string;
}
