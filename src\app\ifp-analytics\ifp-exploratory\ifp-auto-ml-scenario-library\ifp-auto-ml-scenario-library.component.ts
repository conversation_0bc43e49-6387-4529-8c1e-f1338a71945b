import { debounceTime } from 'rxjs';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, OnInit, signal } from '@angular/core';
import { IfpBackButtonComponent } from '../../../ifp-widgets/atoms/ifp-back-button/ifp-back-button.component';
import { TranslateModule } from '@ngx-translate/core';
import { IfpSearchComponent } from '../../../scad-insights/ifp-widgets/ifp-atoms/ifp-search/ifp-search.component';
import { IfpAutoMlCardComponent } from '../ifp-auto-ml-card/ifp-auto-ml-card.component';
import { SubSink } from 'subsink';
import { ApiService } from 'src/app/scad-insights/core/services/api.service';
import { autoMlApiEndpoints } from '../constants/auto-ml.constant';
import { DatePipe } from '@angular/common';
import { IfpNoDataComponent } from '../../../scad-insights/ifp-widgets/ifp-molecules/ifp-no-data/ifp-no-data.component';
import { PaginationComponent } from '../../../scad-insights/ifp-widgets/ifp-molecules/pagination/pagination.component';
import { IfpSpinnerComponent } from '../../../scad-insights/ifp-widgets/ifp-molecules/ifp-spinner/ifp-spinner.component';
import { ToasterService } from 'src/app/scad-insights/core/services/tooster/ToastrService.service';
import { UsageDashboardLogService } from 'src/app/scad-insights/core/services/usage-dashboard-log/usage-dashboard-log.service';
import { logType } from 'src/app/scad-insights/core/services/usage-dashboard-log/usage-dashboard.constants';

@Component({
    selector: 'ifp-ifp-auto-ml-scenario-library',
    imports: [IfpBackButtonComponent, TranslateModule, IfpSearchComponent, IfpAutoMlCardComponent, DatePipe, IfpNoDataComponent, PaginationComponent, IfpSpinnerComponent],
    templateUrl: './ifp-auto-ml-scenario-library.component.html',
    styleUrl: './ifp-auto-ml-scenario-library.component.scss'
})
export class IfpAutoMlScenarioLibraryComponent implements OnInit, OnDestroy {
  public scenarioList:ScenarioListAutoMl[] = [];
  public subs = new SubSink();
  public offset: number = 0;
  public limit: number = 10;
  public size: number = 0;
  public limitOptions: number[] = [10, 15, 20, 25, 30];
  public search = '';
  public loader =signal(true);
  private sessionId!: string;

  constructor( private _autoMlService :ApiService, private _toasterservice: ToasterService, private log: UsageDashboardLogService) {}

  ngOnInit(): void {
    if (this.sessionId) {
      this.log.logEnds(this.sessionId, this.log.currentTime );
    }
    this.sessionId = this.log.createUUid;
    this.log.logStart(this.sessionId, logType.autoMl, this.log.currentTime );
    this.callScenarioList();
  }

  // call scenario List
  callScenarioList() {
    this.loader.set(true);
    this.subs.unsubscribe();
    const value: {
      limit: number, offset: number, search: string, ordering?: string
    } = { limit: this.limit, offset: this.offset, search: this.search };
    this.subs.add(
      this._autoMlService.getMethodRequest(autoMlApiEndpoints.scenarioList, value, true ).pipe(debounceTime(800)).subscribe((data: {results: ScenarioListAutoMl[], count: number}) => {
        this.scenarioList = data.results;
        this.size = data.count;
        this.loader.set(false);
      })
    );
  }

  // event trigger when page change
  onPageChange(offset: number) {
    this.offset = offset;
    this.callScenarioList();
  }

  // event trigger when limit change
  limitChanged(event: number) {
    this.offset =0;
    this.limit = event;
    this.  callScenarioList();
  }

  // event trigger when search value change
  searchEvent(event: string) {
    this.offset = 0;
    this.search = event;
    this.callScenarioList();
  }

  // event trigger while clear search
  clearSearch() {
    this.offset = 0;
    this.search = '';
    this.callScenarioList();
  }

  // event  from delete card
  deleteEvent() {
    this.limitChanged(this.limit);
    this._toasterservice.success('Scenario Deleted Successfully!');
  }

  ngOnDestroy(): void {
    this.subs.unsubscribe();
    if (this.sessionId) {
      this.log.logEnds(this.sessionId, this.log.currentTime );
    }
  }

}
export interface ScenarioListAutoMl {
  object_id: string;
  name: string;
  iteration: string;
  status: string;
  description: null;
  updated_at: string;
}
