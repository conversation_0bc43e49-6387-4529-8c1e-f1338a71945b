import { Component, ViewChild, AfterViewInit } from '@angular/core';
import { Router } from '@angular/router';
import { IFPMsalService } from 'src/app/scad-insights/core/services/IFP-msal.service';
import { TermsCheckService } from 'src/app/scad-insights/core/services/terms-check.service';
import { IfpModalComponent } from 'src/app/scad-insights/ifp-widgets/ifp-organism/ifp-modal/ifp-modal.component';
import { IfpTncModalComponent } from 'src/app/scad-insights/ifp-widgets/ifp-organism/ifp-tnc-modal/ifp-tnc-modal.component';
import { SubSink } from 'subsink';
import { DatePipe } from '@angular/common';
import { HeaderComponent } from 'src/app/scad-insights/shared/header/header.component';


@Component({
  selector: 'app-terms-and-condition-initial',
  templateUrl: './terms-and-condition-initial.component.html',
  styleUrls: ['./terms-and-condition-initial.component.scss'],
  standalone: true,
  imports: [IfpModalComponent, IfpTncModalComponent, HeaderComponent]
})
export class TermsAndConditionInitialComponent implements AfterViewInit {
  @ViewChild('tncModal', {static: true}) tncModal!: IfpModalComponent;
  public termsData: any;
  public username!:string;
  public organization!: string;
  public tncVersion: number = 1;
  public subs: SubSink = new SubSink();

  constructor(private _msal: IFPMsalService, private _router: Router, private _termService: TermsCheckService, private datePipe: DatePipe) {
    this.username = this._termService.username;
    this.organization = this._termService.organization;
  }

  ngAfterViewInit(): void {
    this.tncModal.createElement();
  }

  getTermsVersion(version: number) {
    this.tncVersion = version;
  }

  termsResponse(response: boolean) {
    if (response) {
      const date = new Date();
      const data = {
        userId: this.username,
        organization: this.organization,
        tcVersion: this.tncVersion,
        tcAcceptDate: this.datePipe.transform(date, 'dd-MMM-yyyy')
      };
      this._termService.onTermsAccept(data).subscribe(_resp=>{
        this.tncModal.removeModal();
        this._router.navigateByUrl('/home');
      });

    } else {
      this._msal.logout();
    }
  }
}
