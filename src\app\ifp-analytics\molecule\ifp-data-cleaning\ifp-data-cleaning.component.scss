@use "../../../../assets/ifp-styles/abstracts/index" as *;

:host {
  display: block;
}

.ifp-adv-tool__disable {
  margin-top: $spacer-4;
}

.ifp-adv-clean {
  padding-bottom: $spacer-4;
  &__flex,
  &__replace-head,
  &__replace {
    display: flex;
    align-items: center;
  }

  &__replace-head {
    margin-bottom: $spacer-3;
  }

  &__flex {
    margin-bottom: $spacer-2;

    .ifp-adv-tool__label {
      margin-bottom: $spacer-0;
    }

    .ifp-adv-tool__info {
      margin-inline-start: $spacer-2;
    }
  }

  // &__item {
  //   padding-bottom: $spacer-3;
  // }

  &__replace {
    margin-bottom: $spacer-2;

    .ifp-adv-tool__input {
      margin-inline-start: $spacer-3;
      min-width: 50%;
      max-width: 50%;
      padding: $spacer-1 $spacer-2;
    }

    &:last-child {
      margin-bottom: $spacer-0;
    }
  }

  &__replace-text {
    padding-inline-start: $spacer-5 + 2px;
  }

  &__opts {
    margin-inline-end: $spacer-3;

    &:last-child {
      margin-inline-end: $spacer-0;
    }
  }

  &__box-dropdown {
    .ifp-adv-tool {
      &__dropdown-list {
        padding-inline-start: $spacer-5;

        .ifp-adv-tool__checkbox {
          margin-bottom: $spacer-2;
        }
      }

      &__edit-outer {
        margin-top: $spacer-3;
      }
      &__checkbox-dropdown {
        cursor: pointer;
      }
    }
  }

  &__replace-label {
    margin-right: $spacer-2;
  }
}

:host::ng-deep {
  .ifp-dropdown--inline {
    .ifp-dropdown__selected {
      min-width: 0 !important;
    }
  }
}
