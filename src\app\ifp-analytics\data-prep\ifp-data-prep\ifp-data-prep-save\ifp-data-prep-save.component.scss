@use "../../../../../assets/ifp-styles/abstracts/index" as *;

.ifp-prep-save {
  width: 100%;
  max-width: 1050px;
  text-align: center;
  margin-bottom: $spacer-4;
  &__heading,
  &__content-title {
    font-weight: $fw-bold;
  }
  &__heading {
    font-size: $ifp-fs-9;
    margin-bottom: $spacer-5;
  }
  &__content {
    background-color: $ifp-color-section-white;
    border-radius: 20px;
  }
  &__content-header,
  &__content-body {

    padding: $spacer-5;
  }
  &__content-body {
    display: flex;
  }
  &__library-content-wrapper{
    width:100%;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;

  }
  &__content-header {
    border-bottom: 1px solid $ifp-color-grey-7;
  }
  &__content-title {
    font-size: $ifp-fs-7;
    margin-bottom: $spacer-2;
  }
  &__content-desc {
    font-size: $ifp-fs-5;
    color: $ifp-color-grey-9;
  }
  &__library-text {
    font-size: $ifp-fs-4;
    padding: $spacer-3;
    border: 1px solid $ifp-color-grey-7;
    border-radius: 5px;
    margin-top:$spacer-5;
    display: flex;
    align-items: center;
    cursor: pointer;
    width: 100%;
    &:last-child {
      margin-bottom: $spacer-5;
    }
  }
  &__library-icon {
    display: inline-block;
    border-radius: 10px;
    background-color: $ifp-color-cyan;
    padding: $spacer-2 ($spacer-2 + 2px);
    margin: $spacer-0 $spacer-3;
    &--bg-yellow {
      background-color: $ifp-color-yellow-menu;
    }
    .ifp-icon {
      font-size: $ifp-fs-6;
      color: $ifp-color-white-global;
    }
  }
  &__btn-wrapper {
    display: flex;
    padding: $spacer-0 $spacer-5 $spacer-5 $spacer-5;
    justify-content: center;
}


&__library-vertical-divider {
	display: table;
	text-align: center;

	height: 100%; // CHANGE ME!
	width: 100%;


}
&__library-divider-wrapper{
 position: relative;
 padding: $spacer-0 $spacer-3;
}
&__browse {
  display: block;
  width: 100%;
}
&__library-line {
  position: relative;
  display: table-cell;
  vertical-align: middle;

  &:before, &:after {
    position: absolute;
    content: "";
    width: 1px;
    left: 50%;
    border-inline-start: 1px solid $ifp-color-grey-13 ;
  }
  &:before {
    bottom: 50%;
    top: 0;
    margin-bottom: 20px;
  }
  &:after {
    top: 50%;
    bottom: 0;
    margin-top: 20px;
  }
}
&__tools-wrapper {
  display: flex;
  flex-wrap: wrap;
  margin: $spacer-0 (-$spacer-3);
}
&__tool {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: ($spacer-3 - 3px);
  border: 1px solid $ifp-color-grey-7;
  border-radius: 7px;
  margin: $spacer-3;
  cursor: pointer;
  transition: 0.3s;
  &:hover {
    box-shadow: 0 0 16px $ifp-color-black-16;
  }
  width: 80%;
  &--disable {
    cursor:not-allowed;
    opacity: .5;
    &:hover {
      box-shadow:none;
    }

    app-ifp-info  {
      opacity: 1;
    }
  }
}
&__tool-name {
  font-size: $ifp-fs-4;
  display: flex;
  align-items: center;
  .ifp-icon {
    display: inline-block;
    width: 30px;
    height: 30px;
    line-height: 30px !important;
    text-align: center;
    margin-inline-end: $spacer-3;
    border-radius: 7px;
    font-size: $ifp-fs-4;
    color: $ifp-color-white-global;
  }
}
&__tool-desc {
  margin: $spacer-0 $spacer-1;
}
&__footer-btn {
  display: inline-block;
  width: 80%;
  margin: $spacer-3;
}

}

:host::ng-deep {
  .ifp-prep-save {
    &__footer-btn .ifp-btn{
      width: 100%;
    }
  }
}

@include desktop-sm {
  .ifp-prep-save__tool-name .ifp-icon {
    width: 40px;
  }
}
