import { Injectable } from '@angular/core';
import { HttpService } from '../http/http.service';
import { commonApi } from '../../apiConstants/common-api.constants';

@Injectable({
  providedIn: 'root'
})
export class CustomCardService {

  constructor(private _http: HttpService) { }

  formatCustomData(data: any) {
    // console.log("data", data)
  }

  uploadCustomData(data: any) {
    return this._http.post(commonApi.uploadCustomData, data);
  }

  updateCustomData(data: { data: any, objectId: string }) {
    return this._http.put(`${commonApi.uploadCustomData}/${data.objectId}`, data.data);
  }

  getCustomData(id:string){
    return this._http.get(`${commonApi.uploadCustomData}/${id}`);
  }
}
