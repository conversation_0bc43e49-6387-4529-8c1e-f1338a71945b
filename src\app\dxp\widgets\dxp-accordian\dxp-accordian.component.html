<div class="ifp-dxp-accordian">
  <div class="ifp-dxp-accordian__head"> {{dxpImageUrl}}
    <img alt="" [src]="dxpImageUrl ? 'data:image/jpeg;base64,' + dxpImageUrl : '../../../assets/images/agriculture-publication_1.png'" class="ifp-dxp-accordian__image">
    <div class="ifp-dxp-accordian__title-wrapper">
      <h3 class="ifp-dxp-accordian__title">
        {{title() | translate}}
      </h3>
      <p class="ifp-dxp-accordian__entity">
        {{entity() | translate}}
      </p>
    </div>
    <div class="ifp-dxp-accordian__toggle" (click)="minimize.set(!minimize())">
      @if (!minimize()) {
      <div class="ifp-dxp-accordian__minus">

      </div>
      }@else {
      <em class="ifp-icon ifp-icon-plus-light"></em>
      }
    </div>
  </div>
  @if (!minimize()) {
  <div class="ifp-dxp-accordian__assets">
    <h6 class="ifp-dxp-accordian__assets-title">
      {{'Select your asset' | translate}}
    </h6>
    @for (item of assets(); track item) {
      <ifp-dxp-accordian-badge class="ifp-dxp-accordian__assets-items" [title]="item.name" [date]="item.createdAt" [id]="item.id" (getSelectedAsset)="onSelectAsset($event)"></ifp-dxp-accordian-badge>
    }

  </div>
  }

</div>
