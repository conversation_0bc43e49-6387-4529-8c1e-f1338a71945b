@use "../../../../assets/ifp-styles/abstracts/index" as *;

.ifp-kpi-temp {
  &__head-sec {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: $spacer-2;
  }
  &__label {
    font-size: $ifp-fs-5;
    font-weight: $fw-semi-bold;
    padding: 2px $spacer-1;
    margin-inline-start: (-$spacer-1);
    border: 0;
    border-radius: 3px;
    &:focus {
      box-shadow: 0 0 0 2px $ifp-color-blue-border-light;
    }
  }
  &__value-wrapper {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    margin-top: $spacer-1;
  }
  &__value {
    color: $ifp-color-black;
    font-size: $ifp-fs-14;
    font-weight: $fw-semi-bold;
  }
  &__compare-arrow {
    display: none;
    font-weight: $fw-bold;
    margin-inline-end: $spacer-1 + 2px;
  }
  &__compare-value {
    display: flex;
    align-items: center;
    padding: ($spacer-1 + 2px) ($spacer-2 + 2px);
    margin-inline-start: $spacer-2;
    color: $ifp-color-grey-14;
    background-color: $ifp-color-grey-disabled;
    border-radius: 10px;
    &--green {
      color: $ifp-color-green-dark-3;
      background-color: $ifp-color-green-light;
      .ifp-kpi-temp__compare-arrow--up {
        display: inline-block;
      }
    }
    &--red {
      color: $ifp-color-red;
      background-color: $ifp-color-pale-red;
      .ifp-kpi-temp__compare-arrow--down {
        display: inline-block;
      }
    }
  }
  &__label-sub {
    color: $ifp-color-grey-14;
    margin-top: $spacer-2;
  }
  &__compare-text {
    font-weight: $fw-semi-bold;
    color: inherit;
  }
  &__options {
    display: block;
    margin-inline-start: $spacer-1;
    margin-inline-end: (-$spacer-2);
  }
}

:host {
  display: block;
  padding: $spacer-3;
  border-radius: 10px;
  background-color: $ifp-color-white;
  &.ifp-kpi-temp {
    &--center {
      text-align: center;
      .ifp-kpi-temp {
        &__value-wrapper,
        &__compare-value,
        &__head-sec {
          justify-content: center;
        }
        &__label {
          width: 100%;
          text-align: center;
        }
      }
    }
    &--right {
      text-align: end;
      .ifp-kpi-temp {
        &__value-wrapper,
        &__compare-value {
          justify-content: flex-end;
        }
        &__label {
          text-align: end;
        }
      }
    }
    &--icon {
      display: flex;
      align-items: center;
      justify-content: center;
      border: 1px solid $ifp-color-grey-13;
      border-radius: 4px;
      padding: $spacer-2;
      min-height: 90px;
      cursor: pointer;
      transition: 0.3s;
      .ifp-kpi-temp {
        &__label,
        &__value,
        &__label-sub {
          color: $ifp-color-secondary-grey;
          margin: $spacer-0;
        }
       &__label,
        &__label-sub {
          font-weight: $fw-regular;
        }
        &__label {
          font-size: $ifp-fs-2;
        }
        &__value {
          font-size: $ifp-fs-3;
        }
        &__label-sub {
          font-size: 0.8rem;
        }
        &__value-wrapper {
          margin: 2px $spacer-0;
        }
        &__compare-value {
          border-radius: 4px;
          padding: 2px $spacer-1;
          margin-inline-start: $spacer-1;
        }
        &__compare-arrow {
          margin: $spacer-0;
          font-size: 0.6rem;
        }
        &__compare-text {
          display: none;
        }
      }
      &:hover,
      &.ifp-kpi-temp--selected {
        border: 1px solid $ifp-color-blue-hover;
      }
      &.ifp-kpi-temp--selected {
        background-color: $ifp-color-blue-bg-light;
      }
    }
  }
}
