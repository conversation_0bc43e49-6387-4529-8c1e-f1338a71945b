import { Component, Input } from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';
import { IfpNegativeProgressBarComponent } from '../../organism/ifp-negative-progress-bar/ifp-negative-progress-bar.component';
import { IfpMlColorPipe } from '../ifp-ml-color.pipe';

@Component({
    selector: 'ifp-auto-ml-correlation-chart',
    imports: [TranslateModule, IfpNegativeProgressBarComponent, IfpMlColorPipe],
    templateUrl: './ifp-auto-ml-correlation-chart.component.html',
    styleUrl: './ifp-auto-ml-correlation-chart.component.scss'
})
export class IfpAutoMlCorrelationChartComponent {
  @Input()  barData: {name: string, value: number}[] = [
  ];
}
