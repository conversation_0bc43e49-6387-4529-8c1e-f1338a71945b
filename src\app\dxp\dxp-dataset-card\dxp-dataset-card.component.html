<div class="ifp-dxp-dataset-card">
  <div class="ifp-dxp-dataset-card__head">
    <h6 class="ifp-dxp-dataset-card__title">
      {{title()}}
    </h6>
    <em class="ifp-icon ifp-icon-trash ifp-dxp-dataset-card__delete" (click)="deleteItem()"></em>
  </div>
  <div class="ifp-dxp-dataset-card__body" cdkDropList  #dataset="cdkDropList" [cdkDropListData]="datasetList()" (cdkDropListDropped)="drop($event)" [cdkDropListConnectedTo]="connectedto()">
    @for (item of datasetList(); track item) {
    <div class="ifp-dxp-dataset-card__sub-card" >
      <div class="ifp-dxp-dataset-card__sub-card-clone">
        <div class="ifp-dxp-dataset-card__sub-card-ovelay" cdkDrag (cdkDragStarted)="dragStart($event)" (cdkDragEnded)="dragStop($event)">
          <div *cdkDragPlaceholder></div>
          <ng-template cdkDragPreview>
            <div class="ifp-dxp-dataset-card__sub-card">
              <div>
                <em class="ifp-icon ifp-icon-dragable ifp-dxp-dataset-card__dragable"></em>
                {{item.name}}
              </div>
                <em class="ifp-icon  ifp-dxp-dataset-card__icons" [ngClass]="item.icon"></em>
            </div>
          </ng-template>
        </div>

       </div>
      <div>
        <em class="ifp-icon ifp-icon-dragable ifp-dxp-dataset-card__dragable"></em>
        {{item.name}}
      </div>
      <em class="ifp-icon  ifp-dxp-dataset-card__icons" [ngClass]="item.icon"></em>
    </div>
    }
  </div>
</div>
