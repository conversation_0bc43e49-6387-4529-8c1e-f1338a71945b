import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from '@angular/common';
import { AfterViewInit, Component, <PERSON>ementRef, On<PERSON><PERSON>roy, OnInit, ViewChild } from '@angular/core';
import { Title } from '@angular/platform-browser';
import { TranslateModule } from '@ngx-translate/core';
import { IfpBreadcrumbsComponent } from '../../ifp-widgets/ifp-molecules/ifp-breadcrumbs/ifp-breadcrumbs.component';
import { PageData } from '../../core/interface/molecule/breadcrumb.interface';
import { NgbNavModule } from '@ng-bootstrap/ng-bootstrap';
import { selectProductsResponse } from 'src/app/scad-insights/store/footer/products/products.selector';
import { Store } from '@ngrx/store';
import { ProductsState } from 'src/app/scad-insights/store/footer/products/products.state';
import { Observable, tap } from 'rxjs';
import { getProducts } from 'src/app/scad-insights/store/footer/products/products.action';
import { IfpImgComponent } from 'src/app/scad-insights/ifp-widgets/ifp-atoms/ifp-img/ifp-img.component';
import { ThemeService } from 'src/app/scad-insights/core/services/theme/theme.service';
import { SafePipe } from 'src/app/scad-insights/core/pipes/safe.pipe';
import { IFPMsalService } from '../../core/services/IFP-msal.service';
import { title } from '../../core/constants/header.constants';

@Component({
    selector: 'app-ifp-product-page',
    templateUrl: './ifp-product-page.component.html',
    styleUrls: ['./ifp-product-page.component.scss'],
    imports: [AsyncPipe, NgClass, NgIf, NgFor, TranslateModule, IfpBreadcrumbsComponent, NgbNavModule, IfpImgComponent, NgStyle, SafePipe],
    animations: []
})
export class IfpProductPageComponent implements OnInit, AfterViewInit, OnDestroy {
  @ViewChild('productsWrapper') productsWrapper!: ElementRef;
  active = 0;
  viewPortObserver!: IntersectionObserver;
  cardDetail: any = [
    {
      title: '',
      background: '../../../assets/images/grid-horizontal-chart.png',
      class: 'ifp-products__img--expand-right'
    },
    {
      title: '',
      background: '../../../assets/images/grid-horizontal-chart.png',
      class: 'ifp-products__img--to-top'
    },
    {
      title: '',
      background: '../../../assets/images/grid-vertical-chart.png',
      class: 'ifp-products__img--to-right'
    }
  ];

  pageData: PageData[] = [
    {
      title: 'Home',
      route: '/home'
    },
    {
      title: 'Products',
      route: ''
    }
  ];

  public isView: boolean = false;
  public isInitial: boolean = true;

  public productsData$: Observable<ProductsState> = this.store.select(selectProductsResponse).pipe(
    tap(data => {
      if (data) {
        this._titleService.setTitle(`${title.bayaan} | ${data?.title}`);
        if (data?.title) {
          (window as any)?.dataLayer?.push({
            'event': 'page_load',
            'page_title_var': data?.title
          });
        }
        this.pageData = [
          {
            title: 'Home',
            route: '/home'
          },
          {
            title: data?.title,
            route: ''
          }
        ];
      }

    })
  );

  constructor(private _titleService: Title, private store: Store, public _themeService: ThemeService, private _msalService:IFPMsalService) {
    this._titleService.setTitle(`${title.bayaan} | Products`);

    (window as any)?.dataLayer?.push({
      'event': 'page_load',
      'page_title_var': 'Products',
      'userId': this._msalService.convertEmail(this._msalService.getLoginData.account.username)
    });

  }

  ngOnInit() {
    this.store.dispatch(getProducts());
  }

  ngAfterViewInit() {
    const threshold = 0.2; // how much % of the element is in view
    this.viewPortObserver = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            // this.viewPortObserver.disconnect(); // disconnect if you want to stop observing else it will rerun every time its back in view. Just make sure you disconnect in ngOnDestroy instead
            this.isView = this.isInitial ? this.isView = true : this.isView = false;
            this.isInitial = false;
          } else {
            this.isView = false;
          }
        });
      },
      { threshold }
    );
    setTimeout(() => {
      this.viewPortObserver.observe(this.productsWrapper.nativeElement);
    }, 400);
  }

  ngOnDestroy() {
    this.viewPortObserver.disconnect();
  }
}
