@use '../../../../assets/ifp-styles/abstracts' as *;

.ifp-domain-overlay {
  visibility: hidden;
  opacity: 0;
  transition: 0.3s;
}
.ifp-domain {
  &__wrapper {
    border-radius: 10px;
    background-color: $ifp-color-domain-nav-bg;
    overflow: hidden;
    margin: $spacer-0 auto;
    box-shadow: 0 0 10px $ifp-color-black-16;
  }
  &__note {
    color: $ifp-color-secondary-grey;
    position: absolute;
    bottom: 0;
    left: 0;
    padding: $spacer-4;
    font-size: 1rem;
  }
  &__sidebar-wrapper {
    display: flex;
    min-height: 300px;
    padding: $spacer-0 $spacer-4 $spacer-3;
  }
  &__search-input {
    display: block;
    width: 100%;
    max-width: 500px;
    margin: $spacer-0 auto;
  }
  &__sidebar-inner-item {
    .ifp-icon {
      position: absolute;
      right: -2px;
      top: 50%;
      transform: translateY(-50%);
    }
  }
  &__count{
    // color: #A2C2FE;
    // border: 1px solid #A2C2FE;
    // background-color: rgba(162, 194, 254, 0.2);
    color: $ifp-color-white;
    background-color: $ifp-color-grey-disabled;
    padding: $spacer-1 $spacer-2;
    margin-left: $spacer-2;
    border-radius: 5px;
    font-size: 1.2rem;
    font-weight: $fw-medium;
    transition: 0.3s;
  }
  &__sidebar-inner {
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: space-between;
    white-space: nowrap;
    position: relative;
  }

  &__sidebar-item {
    color: $ifp-color-secondary-grey;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: space-between;
    transition: 0.3s;
    position: relative;
    margin-top: -1px;
    & > .ifp-icon {
      position: absolute;
      right: -2px;
      top: 50%;
      transform: translateY(-50%);
      opacity: 0;
    }
    &:last-child {
      margin-bottom: $spacer-5;
    }
    &--back {
      display: none;
    }
    &--back,
    .ifp-domain__sidebar-inner {
      width: 100%;
      padding: 15px $spacer-4 15px $spacer-0;
      border-bottom: 1px solid $ifp-color-grey-3;
    }
    app-ifp-category-label {
      display: inline;
      max-width: calc(100% - 33px);
      opacity: 0.6;
    }
    &::ng-deep app-ifp-category-label {
      .ifp-category-label__link {
        margin: 2px $spacer-2 $spacer-0;
      }
    }
    &--active,
    &:hover {
      & > .ifp-icon {
        opacity: 1;
      }
      app-ifp-category-label {
        opacity: 1;
      }
      &::ng-deep app-ifp-category-label {
        .ifp-category-label__link {
          opacity: 1;
          visibility: visible;
        }
      }
      .ifp-domain {
        &__count {
          background-color: $ifp-color-black;
        }
        &__accordion-item {
          &::ng-deep app-ifp-category-label {
            .ifp-category-label__link {
              opacity: 0;
              visibility: hidden;
            }
            .ifp-category-label__txt {
              opacity: 0.6;
            }
          }
          &:hover {
            &::ng-deep app-ifp-category-label {
              .ifp-category-label__link {
                opacity: 1;
                visibility: visible;
              }
              .ifp-category-label__txt {
                opacity: 1;
              }
            }
          }
        }
      }
    }
    &:hover {
      color: $ifp-color-black;
    }
    &--active {
      color: $ifp-color-black;
      cursor: default;
      .ifp-domain__accordion-body {
        max-height: 2000px;
        @include ifp-scroll-y(transparent, $ifp-color-grey-1, 4px, 8px);
      }
    }
    &--last {
      app-ifp-category-label {
        max-width: 100%;
      }
      .ifp-domain__sidebar-inner {
        padding-right: $spacer-0;
      }
    }
  }
  &__accordion-item {
    padding: $spacer-2 $spacer-2 $spacer-2 $spacer-3;
    font-weight: $fw-regular;
    transition: 0.3s;
    cursor: pointer;
    border-bottom: 1px solid $ifp-color-grey-3;
  }
  &__sidebar-list {
    padding: $spacer-0 $spacer-3;
    min-height: 95%;
  }
  &__sidebar-item-inner {
    .ifp-icon {
      position: absolute;
      right: -2px;
      top: 50%;
      transition: 0.3s;
      transform: translateY(-50%), rotate(0deg);
    }
    &::ng-deep {
      .ifp-category-label__link {
        display: none;
      }
    }
  }
  &__accordion-body {
    max-height: 0;
    overflow: hidden;
    transition: 0.3s;
  }
  &__sidebar-container,
  &__sidebar {
    height: 420px;
    max-height: calc(100vh - 320px);
  }
  &__census-nav {
    padding-top: $spacer-4;
    padding-inline-start: $spacer-6;
  }
  &__sidebar-container {
    flex: 0.2;
    display: flex;
    overflow: hidden;
    position: relative;
    &::after {
      content: "";
      width: 100%;
      height: 50px;
      background: linear-gradient(0deg, $ifp-color-domain-nav-bg 0%, transparent 100%);
      position: absolute;
      bottom: 0;
      left: 0;
    }
    &--product {
      flex: 0.4;
    }
    &--extend {
      flex: 0.8;
    }
  }
  &__sidebar {
    @include ifp-scroll-y(transparent, $ifp-color-grey-1, 4px, 8px);
    transition: 0.3s;
    width: 100%;
    &--domain {
      .ifp-domain {
        &__sidebar-list {
          padding: $spacer-0;
        }
        &__sidebar-item {
          position: relative;
          padding: $spacer-0 $spacer-3;
          border-radius: 20px;
          app-ifp-category-label {
            max-width: 100%;
          }
          &:last-child {
            .ifp-domain__sidebar-inner::after {
              content: none;
            }
          }
          .ifp-icon {
            right: 16px;
          }
          &::ng-deep {
            .ifp-category-label__txt {
              font-size: $ifp-fs-5;
            }
          }
          &--active {
            background-color: $ifp-color-pale-blue;
            .ifp-domain__sidebar-inner {
              opacity: 0.5;
            }
          }
        }
        &__sidebar-inner {
          border-bottom: 1px solid transparent;
        }
      }
    }
    &--subtheme,
    &--indicator {
      .ifp-domain__sidebar-item {
        & > .ifp-icon {
          display: none;
        }
      }
    }
    &--product {
      .ifp-domain__sidebar-item:not(.ifp-domain__sidebar-item--back) {
        display: block;
        .ifp-icon {
          transform: translateY(-50%) rotate(90deg);
        }
      }
    }
  }
  &__search {
    padding: $spacer-5 $spacer-3;
    &::ng-deep .ifp-search-box {
      border-radius: 15px;
      flex-direction: row;
      padding: $spacer-0 $spacer-2;
    }
  }
  &__no-data {
    display: flex;
    justify-content: center;
    padding: $spacer-4;
    flex: 0.8;
    color: $ifp-color-black;
    app-ifp-no-data {
      width: 100%;
      display: flex;
    }
  }

  &__loading {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: $spacer-6;
    flex: 0.8;
    min-height: 300px;
  }

  &__loading-spinner {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: $spacer-3;

    .spinner-border {
      width: 3rem;
      height: 3rem;
      border-width: 0.3em;
    }
  }

  &__loading-text {
    color: $ifp-color-secondary-grey;
    font-size: $ifp-fs-3;
    margin: 0;
    text-align: center;
  }
}

:host::ng-deep {
  .ifp-domain__no-data {
    .ifp-empty {
      width: 100%;
    }
  }
}

.right-arrow-display {
  display: block;
}

:host-context([dir="rtl"]) {
  .ifp-domain {
    &__note {
      right: 0;
      left: auto;
    }
    &__sidebar-inner-item {
      .ifp-icon {
        left: -2px;
        right: auto;
      }
    }
    &__sidebar-item {
      & > .ifp-icon {
        left: -2px;
        right: auto;
      }
      &--back,
      .ifp-domain__sidebar-inner {
        padding: $spacer-3 $spacer-0 $spacer-3 $spacer-4;
      }
      &::ng-deep app-ifp-category-label {
        .ifp-category-label__link {
          margin: 2px $spacer-2 $spacer-0;
        }
      }
    }
    &__sidebar-item-inner {
      .ifp-icon {
        transform: translateY(-50%) rotate(-90deg);
        left: -2px;
        right: auto;
      }
    }
    &__sidebar {
      &--domain {
        .ifp-domain {
          &__sidebar-item {
            .ifp-icon {
              left: 16px;
              right: auto;
            }
          }
        }
      }
      &--product {
        .ifp-domain__sidebar-item:not(.ifp-domain__sidebar-item--back) {
          .ifp-icon {
            transform: translateY(-50%) rotate(-90deg);
          }
        }
      }
    }
    &__count{
      margin-right: $spacer-2;
      margin-left: $spacer-0;
    }
  }
}

@include mobile-tablet {
  .ifp-domain {
    &__wrapper {
      background-color: $ifp-color-grey-10;
      border-radius: 0;
      box-shadow: none;
      display: block;
    }
    &__sidebar-wrapper {
      position: relative;
      min-height: 0;
      padding: $spacer-0;
      .ifp-domain__search {
        display: none;
      }
    }
    &__sidebar-container,
    &__sidebar {
      flex: auto;
      height: auto;
    }
    &__sidebar-container {
      position: absolute;
      top: 0;
      right: 0;
      bottom: 0;
      left: 0;
      &::after {
        content: none;
      }
      &--domain {
        position: static;
      }
    }
    &__sidebar {
      background-color: $ifp-color-grey-10;
      &--domain {
        background-color: transparent;
        .ifp-domain {
          &__sidebar-item {
            & > .ifp-icon {
              color: $ifp-color-black;
            }
            &::ng-deep {
              .ifp-category-label__txt {
                font-size: $ifp-fs-3;
              }
            }
            &:hover,
            &--active {
              background-color: transparent;
              // color: $ifp-color-black;
              // &::ng-deep app-ifp-category-label {
              //   .ifp-category-label__txt {
              //     color: $ifp-color-black;
              //   }
              // }
            }
          }
          &__sidebar-inner {
            border-bottom: 1px solid $ifp-color-grey-5;
          }
        }
      }
    }
    &__item-inner {
      display: flex;
      align-items: center;
      & > .ifp-icon {
        margin-right: $spacer-2;
        display: inline-block;
      }
    }
    &__sidebar-item {
      &:last-child {
        margin-bottom: $spacer-0;
      }
      & > .ifp-icon {
        opacity: 1;
      }
      &--back {
        display: flex;
        justify-content: flex-start;
      }
      &--domain {
        .ifp-domain__sidebar-item--active {
          color: $ifp-color-black;
          .ifp-icon {
            color: $ifp-color-black;
          }
          // &::ng-deep app-ifp-category-label {
          //   .ifp-category-label__txt,
          //   .ifp-category-label__link {
          //     color: $ifp-color-black;
          //   }
          // }
        }
      }
    }
    &__search {
      padding: $spacer-4 $spacer-3 $spacer-3;
    }
    &__accordion-body {
      background-color: $ifp-color-grey-11;
    }
    &__accordion-item {
      border-bottom: 1px solid $ifp-color-grey-5;
    }
  }
  :host::ng-deep {
    .ifp-domain {
      .ifp-category-label__link {
        display: none;
      }
      &__sidebar--domain {
        .ifp-domain__sidebar-item {
          &:hover,
          &--active {
            .ifp-domain-icon__img--light {
              display: none;
            }
            .ifp-domain-icon__img--dark {
              display: block;
            }
          }
        }
      }
      .ifp-search-box--transparent {
        width: 100%;
        max-width: 100%;
        background-color: $ifp-color-white-global;
      }
    }
  }

  :host-context([dir="rtl"]) {
    .ifp-domain {
      &__sidebar {
        right: 0;
        left: auto;
      }
      &__item-inner {
        & > .ifp-icon {
          margin-left: $spacer-2;
          margin-right: $spacer-0;
        }
      }
    }
  }
}

@include mobile {
  .ifp-domain {
    &__sidebar-inner {
      white-space: normal;
    }
  }
}
