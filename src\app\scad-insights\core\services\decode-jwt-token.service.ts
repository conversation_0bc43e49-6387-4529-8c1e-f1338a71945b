import { Injectable } from '@angular/core';
import { <PERSON><PERSON><PERSON> } from 'buffer';

@Injectable({
  providedIn: 'root'
})

export class DecodeJwtTokenService {
  getTokenDetails(token: string) {
    const payload = token.split('.')[1];
    const decodedPayload = atob(payload.replace(/-/g, '+').replace(/_/g, '/'));
    return JSON.parse(decodedPayload);
  }

  getTokenData(token: string) {
    return JSON.parse(Buffer.from(token.split('.')[1], 'base64').toString());
  }
}
