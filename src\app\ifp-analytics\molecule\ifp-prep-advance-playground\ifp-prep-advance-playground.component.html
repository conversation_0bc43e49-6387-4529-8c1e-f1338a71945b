<div class="ifp-p-adv__body" >
  <div #content class="ifp-p-adv__body-content" [ngClass]="{'ifp-p-adv__body-content--disable-preview' : !previewSpace}">
    <!-- <div class="ifp-p-adv__actions" [ngClass]="{'ifp-p-adv__actions--collpase': selectedNode}">
      <em class="ifp-icon ifp-icon-undo"></em>
      <em class="ifp-icon ifp-icon-redo"></em>
      <em class="ifp-icon ifp-icon-save"></em>
      <em class="ifp-icon ifp-icon-tick-round"></em>
      <ifp-drop-down-button></ifp-drop-down-button>
    </div> -->


    <div #canvas id="drawflow" #drawflowPlayGround class="ifp-p-adv__drawflow" (drop)="drop($event)" (dragover)="allowDrop($event)"
      [ngClass]="{'ifp-p-adv__drawflow--grid': isGridEnable}">
      <!-- <div class="btn-clear" (click)="editor.clearModuleSelected()">Clear</div> -->

    </div>





    <!-- minimap -->
    <!-- <div #minimap class="ifp-p-adv__minimap">
      <div class="ifp-p-adv__drag-rect"  #minmapRect (mousedown)="onMiniMouseDown($event)" (document:mouseup)="onMiniMouseUp()"
      (document:mousemove)="onMiniMouseMove($event)">

      </div>
    </div> -->
  </div>
</div>
