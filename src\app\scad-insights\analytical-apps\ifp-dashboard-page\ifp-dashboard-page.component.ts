import { Store } from '@ngrx/store';
import { ChangeDetectorRef, Component, OnDestroy, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { getIndicator } from 'src/app/scad-insights/store/incidicator-list/incidicator-list.action';
import { SubSink } from 'subsink';
import { selectIndicatorGetById } from 'src/app/scad-insights/store/incidicator-list/incidicator-list.selector';
import { distinctUntilChanged } from 'rxjs';
import { CommonModule } from '@angular/common';
import { IfpDashboardTestComponent } from 'src/app/scad-insights/ifp-widgets/ifp-organism/ifp-dashboard-test/ifp-dashboard-test.component';
import { contentType } from 'src/app/scad-insights/core/constants/contentType.constants';
import { buttonClass } from 'src/app/scad-insights/core/constants/button.constants';
import { TranslateModule } from '@ngx-translate/core';
import { ThemeService } from 'src/app/scad-insights/core/services/theme/theme.service';
import { Title } from '@angular/platform-browser';
import { IFPMsalService } from '../../core/services/IFP-msal.service';
import { UsageDashboardLogService } from '../../core/services/usage-dashboard-log/usage-dashboard-log.service';
import { logType } from '../../core/services/usage-dashboard-log/usage-dashboard.constants';
import { title } from '../../core/constants/header.constants';
import { IfpBreadcrumbsComponent } from "../../ifp-widgets/ifp-molecules/ifp-breadcrumbs/ifp-breadcrumbs.component";
import { PageData } from '../../core/interface/molecule/breadcrumb.interface';

@Component({
    selector: 'app-ifp-dashboard-page',
    templateUrl: './ifp-dashboard-page.component.html',
    styleUrls: ['./ifp-dashboard-page.component.scss'],
    imports: [CommonModule, IfpDashboardTestComponent, TranslateModule, IfpBreadcrumbsComponent]
})
export class IfpDashboardPageComponent implements OnInit, OnDestroy {

  public id!: string;
  public contentType: string = contentType['analytical-apps'];
  public scriptSrc!: string;
  public scriptSrcLight!: string;
  public scriptSrcDark!: string;
  public height!: number;
  public subs: SubSink = new SubSink();
  public buttonClass = buttonClass;
  public toolbar = 'no';
  public hostUrl = 'https://bi-stage.scad.gov.ae/';
  public tabs = true;
  public dashboardSubs: any;
  public dashboardStatus!: boolean;
  public note!: string;
  public pageData!: PageData[];
  public breadcrumbData: BreadcrumbData = {
    domain: '',
    domainId: '',
    title: '',
    isStatic: false
  };
  private sessionId!: string;

  constructor(private _router: ActivatedRoute, public store: Store, private _cdr: ChangeDetectorRef, private _theme: ThemeService, private _titleService: Title, private _msalService: IFPMsalService, private log: UsageDashboardLogService) { }

  ngOnInit() {
    this.subs.add(this._theme.defaultTheme$.subscribe((data) => {
      this.dashboardStatus = false;
      this._cdr.detectChanges();
      this.scriptSrc = data === 'dark' ? this.scriptSrcDark : this.scriptSrcLight;
      this.dashboardStatus = true;
      this._cdr.detectChanges();
    }));
    this.subs.add(this._router.params.subscribe(val => {
      this.id = val['id'];
      if (this.sessionId) {
        this.log.logEnds(this.sessionId, this.log.currentTime );
      }
      this.sessionId = this.log.createUUid;
      this.log.logStart(this.sessionId, logType.indicator, this.log.currentTime, +this.id  );
      if (this.id && this.id !== 'static') {
        this.dashboardStatus = false;
        this.dashboardSubs?.unsubscribe();
        this.store.dispatch(getIndicator({
          id: this.id,
          contentType: this.contentType
        }));
        this.dashboardSubs = this.store.select(selectIndicatorGetById(this.id))
          .pipe(distinctUntilChanged((prev, curr) => prev.loader === curr.loader))
          .subscribe((data) => {
            if (data.status) {
              this._titleService.setTitle(`${title.bayaan} | ${data?.body?.component_title}`);
              this.breadcrumbData.domain = data?.body?.domain;
              this.breadcrumbData.domainId = data?.body?.domain_id;
              this.breadcrumbData.title = data?.body?.component_title;
              this.setBreadCrumbs(this.breadcrumbData);
              if (data?.body?.component_title) {
                (window as any)?.dataLayer?.push({
                  'event': 'page_load',
                  'page_title_var': data.body.component_title,
                  'userId': this._msalService.convertEmail(this._msalService.getLoginData.account.username)
                });
              }
              this.note = data.body?.note;
              this.dashboardStatus = true;
              this.scriptSrcLight = data?.body?.external_name;
              this.scriptSrcDark = data?.body?.external_name_dark ? data?.body?.external_name_dark : data?.body?.external_name;
              this.scriptSrc = this._theme.defaultTheme === 'dark' ? (data?.body?.external_name_dark ? data?.body?.external_name_dark : data?.body?.external_name) : data?.body?.external_name;
              this.height = parseInt(data?.body?.height);
              this.toolbar = (data?.body?.toolbar)?.toLowerCase();
              this.tabs = (data?.body?.tabs)?.toLowerCase();
              this.hostUrl = data?.body?.host_url;
            }
            this._cdr.detectChanges();
          });
      } else {
        this.note = '';
        this.dashboardStatus = true;
        this._titleService.setTitle(`${title.bayaan} | SV Dashboard`);
        this.breadcrumbData.isStatic = true;
        this.breadcrumbData.title = 'SV Dashboard';
        this.setBreadCrumbs(this.breadcrumbData);
        this.scriptSrcLight = 'https://bi.scad.gov.ae/views/SVComparisonReportDashboard/SVReportDashboard';
        this.scriptSrcDark = 'https://bi.scad.gov.ae/views/SVComparisonReportDashboard/SVReportDashboard';
        this.scriptSrc = 'https://bi.scad.gov.ae/views/SVComparisonReportDashboard/SVReportDashboard';
        this.height = 1440;
        this.tabs = false;
        this.hostUrl = 'https://bi.scad.gov.ae/';
      }
    }));
  }


  // goBack() {
  //   this._location.back();
  // }

  setBreadCrumbs(data: BreadcrumbData){
    if (data.isStatic) {
      this.pageData = [
        {
          title: 'Home',
          route: '/home'
        },
        {
          title: data.title,
          route: ''
        }
      ];
      return;
    }
    this.pageData = [
      {
        title: 'Home',
        route: '/home'
      },
      {
        title: data.domain,
        route: `/domain-exploration/${data.domain}/${data.domainId}`,
        queryParams: { key: 'analytical_apps', tabName: 'Analytical Apps' }
      },
      {
        title: data.title,
        route: ''
      }
    ];
  }

  ngOnDestroy(): void {
    this.subs.unsubscribe();
    this.dashboardSubs?.unsubscribe();
    if (this.sessionId) {
      this.log.logEnds(this.sessionId, this.log.currentTime );
    }
  }

}

interface BreadcrumbData {
  domain: string;
  domainId: string;
  title: string;
  isStatic: boolean;
}
