import {
  Component,
  input,
  model,
  output,
  signal,
  viewChild,
} from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';
import { buttonClass } from '../../../scad-insights/core/constants/button.constants';
import { IfpButtonComponent } from '../../../scad-insights/ifp-widgets/ifp-atoms/ifp-button/ifp-button.component';
import { AiInsightAnalysisCardComponent } from '../ai-insight-analysis-card/ai-insight-analysis-card.component';
import { AiInsightIndicatorCardComponent } from '../ai-insight-indicator-card/ai-insight-indicator-card.component';
import { AiInsightCompareModuleComponent } from '../ai-insight-compare-module/ai-insight-compare-module.component';
import { FormControl, ReactiveFormsModule } from '@angular/forms';
import {
  chartInsightsReport,
  InsightComparison,
} from '../interface/insights.interface';
import { AiInsightReportGenarateBadgeComponent } from '../ai-insight-report-genarate-badge/ai-insight-report-genarate-badge.component';
import { IfpAiChartCardComponent } from '../../../scad-insights/ifp-chat-bot/ifp-ai-chart-card/ifp-ai-chart-card.component';
import { AiInputComponent } from '../../../ifp-widgets/atoms/ai-input/ai-input.component';
import { AiButtonComponent } from '../../../ifp-widgets/atoms/ai-button/ai-button.component';
import { cloneDeep } from 'lodash';
import { genarateKeys } from '../constants/ai-insight.constant';
import { IfpMarkDownEditorComponent } from "../../../scad-insights/ifp-widgets/ifp-organism/ifp-mark-down-editor/ifp-mark-down-editor.component";

@Component({
  selector: 'ifp-ai-insight-report-popup',
  imports: [
    TranslateModule,
    IfpButtonComponent,
    AiInsightAnalysisCardComponent,
    AiInsightIndicatorCardComponent,
    ReactiveFormsModule,
    AiInsightCompareModuleComponent,
    AiInsightReportGenarateBadgeComponent,
    IfpAiChartCardComponent,
    AiInputComponent,
    AiButtonComponent,
    IfpMarkDownEditorComponent
],
  templateUrl: './ai-insight-report-popup.component.html',
  styleUrl: './ai-insight-report-popup.component.scss'
})
export class AiInsightReportPopupComponent {
  public close = output();
  public save = output<InsightSave>();
  public title = input('Edit Impact');
  public subTitle = input('Key Insights');
  public consolidated = input(false);
  public sectionOne = input('');
  public sectionTwo = input('');
  public modelAnimate = model(false);
  public buttonClass = buttonClass;
  public type = input<
    | 'header'
    | 'keyInsights'
    | 'table'
    | 'decription'
    | 'keyInsightsOnly'
    | 'gen'
  >('table');
  public genarateAllPop = input(false);
  public data = input(false);
  public markdown = input(false);
  public genarate = output<InsightSave>();
  public genarateAll = output<{
    type:
      | 'header'
      | 'keyInsights'
      | 'table'
      | 'decription'
      | 'keyInsightsOnly'
      | 'gen';
    value: string;
  }>();
  public keyInsights = signal<KeyInsight[]>([]);
  public drivers = signal<Drivers[]>([]);
  public chart = signal<chartInsightsReport | undefined>(undefined);
  public insightComparison = signal<InsightComparison>({
    headers: [],
    rows: [],
  });
  public badge = viewChild<AiInsightReportGenarateBadgeComponent>('badge1');
  public badge2 = viewChild<AiInsightReportGenarateBadgeComponent>('badge2');
  public badge3 = viewChild<AiInsightReportGenarateBadgeComponent>('badge3');
  public description = new FormControl('');
  public subHeading = new FormControl('');

  setValue(
    desc: string = '',
    subheading: string = '',
    keyInsight: KeyInsight[] = [],
    driver: Drivers[] = [],
    insightComparison: InsightComparison = { headers: [], rows: [] },
    chart?: chartInsightsReport
  ) {
    this.subHeading.setValue(subheading);
    this.description.setValue(desc);
    this.keyInsights.set(keyInsight);
    this.drivers.set(driver);
    this.insightComparison.set(insightComparison);
    this.chart.set(cloneDeep(chart));
  }

  setParticularValue(
    key = '',
    desc: string = '',
    subheading: string = '',
    keyInsight: KeyInsight[] = [],
    driver: Drivers[] = [],
    insightComparison: InsightComparison = { headers: [], rows: [] },
    chart?: chartInsightsReport
  ) {
    this.badge()?.resetValue();
    this.badge3()?.resetValue();
    this.badge2()?.resetValue();
    switch (key) {
      case genarateKeys.summery: {
        this.description.setValue(desc);
        break;
      }

      case genarateKeys.impact: {
        this.subHeading.setValue(subheading);
        this.description.setValue(desc);
        this.chart.set(cloneDeep(chart));
        break;
      }
      case genarateKeys.drivers: {
        this.drivers.update(() => driver);
        break;
      }
      case genarateKeys.comparison: {
        this.insightComparison.set(insightComparison);
        break;
      }
      case genarateKeys.keyInsight: {
        this.keyInsights.update(() => keyInsight);
        break;
      }
      case genarateKeys.sentimental: {
        this.description.setValue(desc);
        break;
      }
    }
  }

  genarateValue(genarateValue: string) {
    this.genarateAll.emit({ type: this.type(), value: genarateValue });
  }

  genarateParticulardata(value: string, section: string) {
    this.genarate.emit({
      description: this.description.value,
      heading: this.subHeading.value,
      keyInsights: this.keyInsights(),
      drivers: this.drivers(),
      type: this.type(),
      chart: this.chart(),
      insightComparison: this.insightComparison(),
      value: value,
      section: section,
    });
  }

  saveEvent() {
    this.save.emit({
      description: this.description.value,
      heading: this.subHeading.value,
      keyInsights: this.keyInsights(),
      drivers: this.drivers(),
      type: this.type(),
      chart: this.chart(),
      insightComparison: this.insightComparison(),
    });
  }
}

export interface InsightSave {
  description: string | null;
  heading: string | null;
  keyInsights: KeyInsight[];
  drivers: Drivers[];
  type: string;
  chart: chartInsightsReport | undefined;
  insightComparison: InsightComparison;
  value?: string;
  section?: string;
}

interface KeyInsight {
  heading: string;
  text: string;
  whether_positive_trend: boolean;
  chart?: chartInsightsReport;
}

interface Drivers {
  indicator_name: string;
  heading: string;
  value: number;
  whether_positive_trend: boolean;
  curr_period?: string;
  pre_period?: string;
  domain?: {
    name?: string;
    id?: string;
  };
}
