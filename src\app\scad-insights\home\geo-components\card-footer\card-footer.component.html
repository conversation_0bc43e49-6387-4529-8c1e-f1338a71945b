<div class="card-footer ifp-card-footer">
  <div class="row text-center">
    <div class="col">
      <ifp-button [tooltipValue]="'Notification updates'" class="ifp-whats-new-card__btn ifp-notication-action" id="add-notification" [buttonColor]="notificationSelector ? 'blue' :'black'" [buttonClass]="buttonClass.icon" [iconClass]="'ifp-icon-notification'" [event]="(notificationSelector) ? true : false" (ifpClick)="addNotification($event)" class="ifp-card-footer__btn"></ifp-button>
    </div>

    <div class="col" >
<!--      <i class="bi bi-plus-square fs-5" [autoClose]="false" [ngbPopover]="popContent" (click)="openPopup('ADD DATA')"></i>-->
      <ifp-button *ngIf="!addMyAppsLanding" [tooltipValue]="((myAppsStatus|async) ? 'Remove' : 'Add to my bookmarks' )" class="top-my-app" [event]="((myAppsStatus|async) ? true :false)" [buttonClass]="buttonClass.icon" [iconClass]="'ifp-icon-plus-square'" [buttonColor]="(myAppsStatus|async) ?'blue':'black'" (ifpClick)="addDataMyApps($event)" class="ifp-card-footer__btn"></ifp-button>
      <ifp-button *ngIf="addMyAppsLanding"  class="top-my-app" [buttonClass]="buttonClass.icon" [iconClass]="'ifp-icon-round-cross'" [buttonColor]="'black'"  (ifpClick)="removeEvent()" class="ifp-card-footer__btn"></ifp-button>
    </div>

   <div class="col" *ngIf="toolbarComment">
    <ifp-button [tooltipValue]="'My Comments' | translate" [buttonColor]="viewComment ? 'blue' :'black'" [buttonClass]="buttonClass.icon" [iconClass]="'ifp-icon-comment'" (ifpClick)="viewComment = !viewComment; viewDownload = false"  class="ifp-card-footer__btn"></ifp-button>
   </div>
    <!-- <div class="col">

    </div> -->
    <!-- <div class="col">
      <i class="bi bi-gear fs-5"  (click)="openPopup('SETTINGS')"></i>
      <em class="ifp-icon ifp-icon-settings" (click)="openPopup('SETTINGS')" [appIfpTooltip]=" 'Settings'| translate"></em>
    </div> -->
    <!-- <div class="col">
      <em class="ifp-icon ifp-icon-information" [ngbPopover]="popContent" (click)="openPopup('METADATA')" [appIfpTooltip]=" 'Information'| translate"></em>
      <em class="ifp-icon ifp-icon-print" (click)="openPopup('PRINT')" [appIfpTooltip]=" 'Print'| translate"></em>
    </div> -->
<!--    <div class="col">-->
<!--      &lt;!&ndash; <i class="bi bi-gear fs-5"  (click)="openPopup('SETTINGS')"></i> &ndash;&gt;-->
<!--      <em class="ifp-icon ifp-icon-settings" (click)="openPopup('SETTINGS')" [appIfpTooltip]=" 'Settings'| translate"></em>-->
<!--    </div>-->
<!--    <div class="col">-->
<!--      &lt;!&ndash; <i class="bi bi-printer fs-5"  (click)="openPopup('PRINT')"></i> &ndash;&gt;-->
<!--      <em class="ifp-icon ifp-icon-print" (click)="printChart()" [appIfpTooltip]=" 'Print'| translate"></em>-->
<!--    </div>-->
<!--    <div class="col">-->
<!--      <i class="bi bi-chat-square-text fs-5" #p="ngbPopover" [autoClose]="false" [ngbPopover]="popContent" (click)="openPopup('MESSAGE')"></i>-->
<!--    </div>-->

    <div class="col">
      <ifp-button [tooltipValue]="'Download'" class="ifp-card-footer__btn" [buttonColor]="viewDownload && previewUrl ? 'blue' :'black'" [buttonClass]="buttonClass.icon" [iconClass]="'ifp-icon-download'" (ifpClick)="viewDownload = !viewDownload; viewComment = false;"></ifp-button>
    </div>

    <div class="col">
      <ifp-button [tooltipValue]="'Print'" class="ifp-card-footer__btn" buttonColor="black" [buttonClass]="buttonClass.icon" [iconClass]="'ifp-icon-print'" (ifpClick)="printDownload()"></ifp-button>
    </div>

    <div class="col ifp-card-footer__collpase">
      <ifp-button [tooltipValue]="isExpanded ? ('Collapse' | translate) : ('Expand' | translate)" class="ifp-card-footer__btn" [buttonColor]="'grey'" [buttonClass]="buttonClass.icon" [iconClass]="'ifp-icon-down-arrow-round'" (ifpClick)="toggleExpand()" [ngStyle]="{'transform': isExpanded ? 'rotate(180deg)' : 'rotate(0)'}"></ifp-button>
    </div>
  </div>
</div>

<div class="ifp-card-footer__toolbar ifp-card-footer__toolbar--comment" *ngIf="viewComment && toolbarComment">
  <h3 class="ifp-card-footer__toolbar-title">{{'My Comments' | translate}}</h3>
  <p class="ifp-chart-toolbar__right-desc">{{'Comments are only visible to you' | translate}}</p>
  <app-ifp-comments [insightData]="insightData" (addComment)="addInsight($event)"
  (editComment)="updateInsight($event)" (deleteComment)="deleteInsight($event)"></app-ifp-comments>
</div>

<div class="ifp-card-footer__toolbar"   *ngIf="viewDownload && previewUrl">
  <app-ifp-document-download [fileTypes]="fileTypes" [custom]="true" [inputId]="geoMapId" [tncState]="tncState" [isExcel]="false" (downLoadClicked)="downloadDoc($event)"></app-ifp-document-download>
</div>

<!-- <app-summary></app-summary> -->

<ng-template #popContent data-bs-theme="dark-mode">
  <p>{{popTitle}}</p>
  <div class="check-box-tooltip mt-2">
    <ul>
      <li>
        <a href="https://datamart.scad.gov.ae:9443/data/view/id/1026#!tab-data-summary" target="_blank" >Job Seekers</a>
      </li>
      <li>
        <a href="https://datamart.scad.gov.ae:9443/data/view/id/1027" target="_blank" >Job Vacancies</a>
      </li>
    </ul>
  </div>
</ng-template>



<app-ifp-modal #subscribeNotification [modalClass]="'ifp-modal__box-sm'" [enableOverlay]="false"
  [modalModifier]="'ifp-modal--alert-box'">
  <app-ifp-alert-box *ngIf="isSubscNotifOpen" (alertResponse)="setEmailNotifStatus($event)" (closeAlert)="closeModal()"
    style="display: block;"></app-ifp-alert-box>
</app-ifp-modal>
