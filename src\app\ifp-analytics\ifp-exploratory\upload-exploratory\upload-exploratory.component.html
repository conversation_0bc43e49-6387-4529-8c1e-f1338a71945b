  <div class="ifp-exploratory-upload">
  <ifp-prep-upload-data [selectedWorksheet]="_exploratory.uploadedFile().sheetName ?? ''" [enableProcessBtn]="false" [sheetList]="_exploratory.uploadedFile().sheetList ?? []"  #uploadData [subHeading]="'Choose data from the source you want to'" [error]="error" [loaderFile]="loaderFile" [disableProcesed]="disableProceed" (uploadCompletedProced)="processed()" class="ifp-exploratory-upload__data"(openLibrary)="openLibrary()"   [currentFile]=" fileName"  [progress]="uploadDataProgess"  (fileUpload)="onFileUpload($event)" (removeFile)="deleteFile()" [indicatorEnable]="false" [rotate]="true" (selectSheet)="onSelectWorksheet($event)" (excelUploadedError)="excelUploadedError()"  (disableEvent)="disableEvent($event)" (excelUploaded)="excelUploaded($event)"
  [uploadedFile]="sheetDetails.file" [s3Path]="this._exploratory.uploadedFile().sheetDetails?.path ?? ''"
  ></ifp-prep-upload-data>
    <div class="ifp-exploratory-upload__image">
      @if((_themeService.defaultTheme$|async) === 'dark') {
        <object class="ifp-exploratory-upload__img" type="image/svg+xml" data="../../../../assets/images/Graphsmovingdark.svg" ></object>

      } @else {
        <object class="ifp-exploratory-upload__img" type="image/svg+xml"  data="../../../../assets/images/Graphsmovinglight.svg" ></object>

      }

    </div>
  </div>

  <app-ifp-modal [overlayClass]="'ifp-modal__bg-grey'" [modalClass]="'ifp-modal__import-indicators'" [isClose]="true" (closeModal)="closeModalEvent()" #libraryListModal>
@if (libraryEnable()) {
  <ifp-prep-library [viewTab]="false" [isModal]="true" (selectFileAll)="getFromLibrary($event)"
  (closeModal)="closeLibrary()" (removeFile)="removeFile($event)"></ifp-prep-library>
}


  </app-ifp-modal>
