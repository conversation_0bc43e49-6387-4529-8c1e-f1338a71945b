
@use "../../../../assets/ifp-styles/abstracts/index" as *;
.ifp-ml-analyzes {
  padding-bottom: $spacer-4;
  &__sec {
    display: flex;
    margin: $spacer-0 (-$spacer-3);
  }
  &__sec-left {
    margin: $spacer-0 $spacer-3;
    width:  calc(25% - $spacer-3);
  }
  &__sec-right {
    margin: $spacer-0 $spacer-3;
    width: calc(75% - $spacer-3);
  }
  &__heading {
    font-size: $ifp-fs-12;
    font-weight: $fw-bold;
    text-align: center;
    margin: $spacer-4;
  }
  &__heading-sub{
    font-size: $ifp-fs-12;
    font-weight: $fw-bold;
    text-align: center;
    color: $ifp-color-blue-menu;
  }
  &__legend-badge {
    border-radius: 6px;
    font-size: $ifp-fs-4;
    font-weight: $fw-medium;
    padding: $spacer-2 $spacer-4;
    background-color: $ifp-color-section-white;
    color: $ifp-color-secondary-grey;
    width: 100%;

    &--selected {
      border-inline-start: $ifp-color-blue-menu 5px solid;

    }
    &--un-selected {
      border-inline-start:  $ifp-color-grey-15 5px solid;
    }
    &--ignore {
      border-inline-start:   $ifp-color-red 5px solid;
    }
  }
  &__legends{
    margin-top: $spacer-4;
  }
  &__legend {
    display: flex;
    margin-bottom: $spacer-3;
    &:last-child {
      margin-bottom: $spacer-0;
    }
  }
  &__run-model{
    display: flex;
    height: 100%;
  }
  &__model-selection{
  display: block;
  width: calc(70% - $spacer-5);
  margin-inline-end: $spacer-5;
  }
  &__model-correlation {
    display: block;
    width: 30%;
  }
  &__loader {
    margin-top: $spacer-6;
    display: block;
  }
  &__accordion{
    margin-bottom: $spacer-3;
    display: block;
  }
  &__importance-score-card {
    height: 100%;
  }
  &__legends-head {
    font-size: $ifp-fs-4;
    margin-bottom: $spacer-4;
    font-weight: $fw-semi-bold;
  }
}
:host::ng-deep {
  .ifp-ml-analyzes__importance-score-card {
    .ifp-auto-imp-score {
      height: 100%;
    }
  }
.ifp-ml-analyzes__model-selection{
  .ifp-auto-selection {
    height: 100%;
  }
}

}

@include desktop-sm {
  .ifp-ml-analyzes {
    &__heading {
      font-size:$ifp-fs-9;
    }
    &__heading-sub{
      font-size:$ifp-fs-9;
    }
    &__run-model{
      display: block;
    }
    &__model-selection {
      width: 100%;
      margin-inline-end: $spacer-0;
    }
    &__model-correlation {
      width: 100%;
      margin-top: $spacer-4;
    }
    &__head-btn-wrapper {
      margin-top: $spacer-3;
    }
    &__sec {
      margin: $spacer-0 (-$spacer-2);
    }
    &__sec-left,
    &__sec-right {
      margin: $spacer-0 $spacer-2;
    }
    &__sec-left {
      width: calc(33.33% - (2 * $spacer-2));
    }
    &__sec-right {
      width: calc(66.66% - (2 * $spacer-2));
    }
    &__head-text {
      padding-bottom: $spacer-2;
    }
  }
}

@include mobile-tablet {
  .ifp-ml-analyzes {
    &__sec {
      flex-wrap: wrap;
    }
    &__sec-left {
      z-index: 10;
      width: 100%;
    }
    &__sec-right {
      width: 100%;
      margin-top: $spacer-3;
    }
  }
}
