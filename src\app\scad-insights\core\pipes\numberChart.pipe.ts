import { DecimalPipe } from '@angular/common';
import { Pipe, PipeTransform } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';


@Pipe({
  name: 'customDate',
  standalone: true
})
export class numberChartDatePipe implements PipeTransform {
  constructor(private _dec: DecimalPipe, private _translate: TranslateService) {
  }

  public keys: Record<string, string> = {
    Q: 'Quadrillion',
    T: 'Trillion',
    B: 'Billion',
    M: 'Million',
    k: 'Thousand'
  };

  transform(number: any, max: any, decimals: number = 3): any {
    const value = +number;
    const maxValue = +max;
    if (value === null || value === undefined ||isNaN(value) ) {
      return null;
    }
    if (value === 0) {
      return '0';
    }
    const fractionSize = 1;
    let abs =  Math.abs(maxValue);
    const rounder = Math.pow(10, fractionSize);
    let key = '';
    let index  = 0;
    let indexValue = false;
    const powers = [{ key: 'Q', value: Math.pow(10, 15) }, { key: 'T', value: Math.pow(10, 12) }, { key: 'B', value: Math.pow(10, 9) }, { key: 'M', value: Math.pow(10, 6) }, { key: 'k', value: 1000 }];
    for (const element of powers) {

      let reduced = abs / element.value;
      reduced = Math.round(reduced * rounder) / rounder;
      if (reduced >= 1) {
        abs = reduced;
        key = element.key;
        indexValue = true;
        break;
      }
      index ++;
    }
    const roundedValue = Math.round(value);
    const currentPower = powers[index];
    const currentValue = indexValue ? ( roundedValue/currentPower.value).toFixed(decimals)?.replace(/\.?0+$/, ''): value;

    return currentValue + key;
  }


}
