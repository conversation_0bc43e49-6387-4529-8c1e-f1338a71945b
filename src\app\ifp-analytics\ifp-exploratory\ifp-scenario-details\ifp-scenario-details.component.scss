@use "../../../../assets/ifp-styles/abstracts/index" as *;
.ifp-scenario  {
  margin-bottom: $spacer-6;
  &__heading-outer{
    display: flex;
    justify-content: space-between;
    margin-top: $spacer-6;
    margin-bottom: $spacer-6;
  }
  &__dropdown {
    padding: $spacer-2 $spacer-3;
    color: $ifp-color-blue-menu;
    background-color: $ifp-color-blue-1;
    border: 1px solid $ifp-color-blue-menu;
    border-radius: 7px;
    cursor: pointer;
    user-select: none;
    display: inline-flex;
    align-items: center;
  }
  &__dropdown-list {
    margin-top: 3px;
    position: absolute;
    background-color: $ifp-color-section-white;
    width: 100%;
    border-radius: 15px;
    padding: $spacer-0 $spacer-2;
    visibility: hidden;
    transform: scaleY(0) translateX(-50%);
    transform-origin: top;
    &--show {
      transform: scaleY(1);
      visibility: visible;
    }

  }
  &__dropdown-icon {
    margin-inline-end:  $spacer-1;
    position: relative;
    top: 1px;

  }
  &__dropdown-wrapper{
    position: relative;
  }
  &__dropdown-item {
    font-size: $ifp-fs-3;
    padding: $spacer-2 $spacer-0;
    border-bottom: 1px solid $ifp-color-pale-grey;
    cursor: pointer;
  }
  &__dropdown-view {
    color: $ifp-color-blue-menu;
  }
  &__dropdown-view-icon {
    margin-inline-start: $spacer-2;
  }
  &__details {
    background-color: $ifp-color-section-white;
    border-radius: 10px;
    border:  1px solid $ifp-color-grey-11;

  }
  &__header{
    display: flex;
    justify-content: space-between;
    padding: $spacer-3 $spacer-5;

  }
  &__header-desc{
    padding: $spacer-0 $spacer-5 $spacer-3;
    border-bottom:  1px solid $ifp-color-grey-13;
  }
  &__header-text {
    font-size: $ifp-fs-10;
    font-weight: $fw-bold;
    cursor:pointer;
  }
  &__header-desc-text {
    font-size: $ifp-fs-3;
    color: $ifp-color-secondary-grey
  }
  &__detail-inner {
    display: flex;
  }
  &__chart {
    padding: $spacer-0 $spacer-6 $spacer-6;
    width: 75%;
  }
  &__tool-bar {
    width: 25%;
  }
  &__highchart {
    width: 100%;
    display: block;
    }
    &__loader {
      display: flex;
      align-items: center;
      justify-content: center;
      min-height: 400px;
    }
    &__rating {
      display: flex;
      width: 100%;
      padding: $spacer-3 $spacer-5;
      @include ifp-scroll-x(transparent, $ifp-color-grey-1, 4px, 8px);
    }
    &__rating-text{
      color:$ifp-color-tertiary-text;
      font-size: $ifp-fs-3;
    }
    &__rating-number {
      font-size: $ifp-fs-8;
      font-weight: $fw-bold;
      display: flex;
    }
    &__header-icon {
      font-size:$ifp-fs-5;
    }
    &__rating {
      padding: 16px 32px;
    }
    &__rating-wrapper  {
      margin: $spacer-2 $spacer-2;
      border: $ifp-color-grey-13 solid 1px;
      padding: $spacer-3;
      border-radius: 10px;
      position: relative;
      min-width: 136px;
    }
    &__rating-tooltip {
      position: absolute;
      height: 100%;
      width: 100%;
      top: 0;
      left: 0;
    }
&__icon {
  margin-top: $spacer-1;
  margin-inline-end: $spacer-2;
  &--up {
    color: $ifp-color-green-dark;
  }
  &--down {
    color: $ifp-color-red;
  }
}
&__disclaimer {
  font-size: $ifp-fs-2;
  margin-top: $spacer-3;
&--bold {
  font-weight: $fw-semi-bold;
  font-size: $ifp-fs-3;
  margin-inline-end: $spacer-2;
  .ifp-icon-info-round {
    font-weight: $fw-semi-bold;
    font-size: $ifp-fs-3;
    margin-inline-end: $spacer-2;
  }
}
}


}

:host::ng-deep {
  .ifp-scenario {
    &__edit {
      .ifp-edit__input {
        font-size: $ifp-fs-10 !important;
      }
      }
    &__desc-edit {
      .ifp-edit__input {
        width: 100%;
        font-size: $ifp-fs-3 !important;
      }
    }
  }
}
:host-context(.ifp-dark-theme) {
  .ifp-scenario  {
    &__dropdown {
      background-color: $ifp-color-section-white;
      &:hover{
        background-color: $ifp-color-active-blue;
        color: $ifp-color-white;
      }
    }
  }
}
@include desktop-sm {
  .ifp-scenario  {
    &__rating {
      padding: $spacer-3 $spacer-0;
    }
    &__rating-wrapper {
      min-width: 0;
    }
    &__chart {
      padding: $spacer-0 $spacer-3 $spacer-3;
    }
    &__rating-number {
      white-space: nowrap;
      font-size: $ifp-fs-3;
    }
  }
}

@include mobile-tablet {
  .ifp-scenario  {
    &__detail-inner {
      flex-wrap:wrap ;
    }
    &__chart {
      width: 100%;
    }
    &__tool-bar {
      width: 100%;
    }
  }
}
