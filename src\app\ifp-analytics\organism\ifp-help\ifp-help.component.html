<div class="ifp-prep-help">
  <div class="ifp-prep-help__content">
    <div class="ifp-prep-help__content-header">
      <h2 class="ifp-prep-help__content-title">{{ mainHead | translate}}</h2>
      <em class="ifp-icon ifp-icon-cross ifp-prep-help__header-icon" (click)="close()"></em>
    </div>
    <div class="ifp-prep-help__content-body">
      <div class="ifp-prep-help__image-wrapeper">
        @if (( _themeService.defaultTheme$ | async) === 'dark' ) {

          <div  class="ifp-prep-help__image" [style.background-image]="'url('+currentContent.darkImg+')'"> </div>

        } @else {
          <div  class="ifp-prep-help__image" [style.background-image]="'url( '+currentContent.img+')'"> </div>
        }

      </div>
      <div class="ifp-prep-help__content-wrapper">
        <div class="ifp-prep-help__content-heading">
          {{currentContent.title | translate}}
        </div>

        <div class="ifp-prep-help__content-desc">
          @if (currentContent.desc.length > 1) {
              <ul class="ifp-prep-help__desc-list">
                @for (desc of currentContent.desc; track $index) {
                <li class="ifp-prep-help__desc-item">{{desc | translate}}</li>
                }
              </ul>
          } @else {
            {{currentContent.desc[0] | translate}}
          }
        </div>
        @if(currentContent.content?.length) {
          <div class="ifp-prep-help__content-inner">
          @for (item of currentContent.content; track $index) {
            <div class="ifp-prep-help__content-sub">
                <div class="ifp-prep-help__content-sub-title">
                  {{item.title | translate}}
                </div>
                <div class="ifp-prep-help__content-sub-desc">
                  {{item.desc | translate}}
                </div>

            </div>
          }
          </div>
        }

      </div>
    </div>
    @if (helpData.length !==0) {
      <div class="ifp-prep-help__footer" >
        @if (curretIndex !== 0) {
        <ifp-button [buttonClass]="button.secondary"  (ifpClick)="back()" [iconPosition]="position.left" [iconClass]="'ifp-icon-leftarrow'" [label]="'Back' | translate"></ifp-button>
        }@else {
          <span class="ifp-prep-help__dummy"> </span>
        }
        <div class="ifp-prep-help__carousel">
          @for (item of helpData; track item;let index = $index;) {
            <div class="ifp-prep-help__carousel-dot" (click)="helpSelection(index)" [ngClass]="{'ifp-prep-help__carousel-dot--active': index === curretIndex}"></div>
          }

        </div>
        @if (curretIndex === helpData.length-1) {
          <ifp-button [buttonClass]="button.primaryLight" (ifpClick)="close()" [iconClass]="'ifp-icon-rightarrow'"   [label]="'Explore Now'| translate"></ifp-button>
        } @else {
          <ifp-button [buttonClass]="button.primaryLight" (ifpClick)="next()" [iconClass]="'ifp-icon-rightarrow'"  [label]="'Next'| translate"></ifp-button>
        }
      </div>
    }

    <div>
    </div>

  </div>

</div>
