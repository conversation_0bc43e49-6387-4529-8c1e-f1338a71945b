<div class="ifp-summary" [ngClass]="{'ifp-summary--popup': summeryPopup}">
  @if(backButton && enableProcessBtn) {
  <div class="ifp-summary__main-head">
    <!-- <span (click)="goBack()" class="ifp-link ifp-summary__head-back" [title]="'Back' | translate"><em class="ifp-icon ifp-icon-left-arrow"></em> {{'Back' | translate}}</span> -->
    <ifp-button [loader]="loader" [label]="buttonName" (ifpClick)="onSubmit()" class="ifp-summary__button" [buttonClass]="loaderPage ? buttonClass.disabled : buttonClass.primaryLight " [iconClass]="'ifp-icon-rightarrow'"></ifp-button>
  </div>
}
  <div class="ifp-summary__header" [ngClass]="{'ifp-summary__header--border': border}">
    <div class="ifp-summary__heade-wrapper">
    <h2 class="ifp-summary__title ifp-sub-title">{{title | translate}}</h2>
    @if(subtitle) {
    <h5 class="ifp-summary__sub-title">{{subtitle | translate}}</h5>
    }
    @if(totalColumn &&totalRow ) {
      <div class="ifp-summary__sub-title"> {{'The data contains a total of' | translate}} <span  class="ifp-summary__sub-title--bold"> {{totalRow}} {{'rows and' | translate}} {{totalColumn}} {{'columns.' | translate}}</span> </div>
    }


    </div>
    <!-- @if (data.length !==0) {
      <div class="ifp-summary__btn" (click)="downloadPreview()">
        {{'Download' | translate}}
        <img src="../../../../../assets/images/xls-icon.svg" alt="XLS" class="ifp-summary__icon">
      </div>
    } -->
    @if(!backButton && enableProcessBtn) {
    <ifp-button [loader]="loader" [label]="buttonName" (ifpClick)="onSubmit()" class="ifp-summary__button-sub" [buttonClass]="disableProcessButton ?  buttonClass.disabled :buttonClass.primary" [iconClass]="'ifp-icon-rightarrow'"></ifp-button>
    }
  </div>
  @if (data.length !==0) {
  <div class="ifp-summary__body">
    <table class="ifp-summary__table" aria-label="stats">
      <tr class="ifp-summary__row ifp-summary__row--head">
        @if (settings.withId) {

          <th class="ifp-summary__col">{{'S. No' | translate}}</th>
            }
        @for (item of settings.setting; track $index) {

        <th class="ifp-summary__col" >{{item.name | translate}}</th>
        }
      </tr>
      @for (item of data; track indexValue; let indexValue = $index) {
        <tr>
          <td colspan="7" class="ifp-summary__col-divider"></td>
        </tr>
        <tr class="ifp-summary__row">
          @if (settings.withId) {

            <th class="ifp-summary__col">{{indexValue+ 1}} </th>
              }
          @for (setting of settings.setting; track $index) {
            @if(tableType.progress == setting.type) {
              <td class="ifp-summary__col"> <p class="ifp-summary__value">{{ setting.key ? item[setting.key] :  item}}</p>
                <p class="ifp-summary__label"> ({{setting.precentageKey ? (item[setting.precentageKey]| number: '1.1-2') :  item}}%)</p>
                <div class="ifp-summary__progress">
                  <div class="ifp-summary__progress-value" [style.width]="(setting.precentageKey ? item[setting.precentageKey] :  item )+ '%'" [style.backgroundColor]="setting.color ??( setting.reverse ? getColorReverse(setting.precentageKey ? item[setting.precentageKey] :  item) : getColor(setting.precentageKey ? item[setting.precentageKey] :  item))"></div>
                </div></td>
            }@else if (tableType.table == setting.type) {

              <td class="ifp-summary__col ifp-summary__col--table" >
                <table class="ifp-summary__stats" aria-label="stats">

                <tr class="ifp-summary__stats-head">
                  @for (tableValues of setting.tableSettings; track $index) {
                    @if(tableValues.funName) {
                      <th class="ifp-summary__stats-col" [appIfpTooltip]="tableValues.funName(tableValues, item)| translate">{{tableValues.funName(tableValues, item)| translate}}</th>
                    } @else {
                      <th class="ifp-summary__stats-col" [appIfpTooltip]="tableValues.name| translate" >{{tableValues.name| translate}}</th>
                    }

                  }
                </tr>
                <tr>
                  @for (tableValues of setting.tableSettings; track $index) {
                    @if(tableValues.fun ) {
                      <td class="ifp-summary__stats-col" ><p class="ifp-summary__col-text" [extraSpaceTop]="20" [disableTooltip]="tableValues.fun(tableValues,item) !== null&& tableValues.fun(tableValues,item) !== undefined? false : true " [appIfpTooltip]="tableValues.fun(tableValues, item)">{{tableValues.fun(tableValues,item) !== null&& tableValues.fun(tableValues,item) !== undefined ? tableValues.fun(tableValues,item) : '-'}}</p></td>
                    } @else {
                      <td class="ifp-summary__stats-col" ><p class="ifp-summary__col-text" [extraSpaceTop]="20" [disableTooltip]="tableValues.key ? item[tableValues.key]? false : true :  item ? false : true" [appIfpTooltip]="tableValues.key ? item[tableValues.key] :  item">{{tableValues.key ? item[tableValues.key] ?? '-':  item ??'-'}}</p></td>
                    }

                  }
                </tr>
              </table>
            </td>
            } @else if (tableType.withsubTittle == setting.type) {
              <td class="ifp-summary__col">
                <p class="ifp-summary__value">{{setting.key ?( item[setting.key]| number: '1.1-2') :  item}}</p>
                <p class="ifp-summary__label">{{setting.subTittle ?? '' | translate}}</p>
              </td>
            }
            @else if (tableType.bracket == setting.type) {
              <td class="ifp-summary__col">
                {{setting.key ?( item[setting.key] | translate):  item | translate}} ({{setting.secondKey ? ( item[setting.secondKey] | translate) :  (item | translate) }})
              </td>
            }
            @else {
              <td class="ifp-summary__col">
                <p class="ifp-summary__col-text" [title]="setting.key ? item[setting.key] :  item">
                {{ setting.key ? item[setting.key] :  item}}
                </p></td>
            }
        }
        </tr>
        <tr>
          <td colspan="7" class="ifp-summary__col-divider"></td>
        </tr>
      }



    </table>
  </div>

} @else {
  @if(loaderPage) {
    <div class="ifp-center-loader">
      <app-ifp-spinner></app-ifp-spinner>
    </div>

  } @else {
    <app-ifp-no-data [message]="'Summary Processing'"></app-ifp-no-data>
  }
}
<!-- <div class="ifp-summary__footer">
  <ifp-button [loader]="loader" [label]="buttonName" (ifpClick)="onSubmit()" class="ifp-summary__button" [buttonClass]="buttonClass.primary" [iconClass]="'ifp-icon-rightarrow'"></ifp-button>
</div> -->
</div>
