import { Injectable, signal, WritableSignal } from '@angular/core';
import { HttpService } from '../http/http.service';
import { HeaderApi } from '../../apiConstants/header.api.constants';
import { Observable, Subject } from 'rxjs';
import { powerBiGroupIds, uatContentId } from '../../constants/header.constants';


@Injectable({
  providedIn: 'root'
})
export class HeaderService {

  hideDomain$ = new Subject<boolean>();
  clearSearch$ = new Subject<boolean>();
  filterSearch$ = new Subject<boolean>();
  filterSearchDomain$ = new Subject<boolean>();
  searchApiLoading$ = new Subject<boolean>();
  isApiLoading: boolean = false;
  domainMenuRest$ = new Subject<boolean>();
  selectedCatogory: any;
  selectedDomain: any;
  approverGroupIds = ['815316be-989e-4f00-873e-8c04308ee971', '5bb79abd-7a69-405c-9519-370c1c3c9268']; // [IFP_Approvers, UAT_Content]
  powerBiApprovedGroupIds = powerBiGroupIds;
  tammDashboardId = uatContentId;
  unreadNotificationCount: WritableSignal<number> = signal(0);

  constructor(private _http: HttpService) { }

  getSearchList(data: any) {
    return this._http.post(HeaderApi.search, data);
  }

  getHeaderData() {
    return this._http.get(HeaderApi.header);
  }

  getNotification(status: string) {
    return this._http.get(`${HeaderApi.notification}?status=${status}`);
  }

  postDefaultSettings(settings: any) {
    return this._http.post(`${HeaderApi.updateSettings}`, settings);
  }

  getDefaultSettings(): Observable<any> {
    return this._http.get(`${HeaderApi.defaultSettings}`);
  }

  getPermissionList() {
    return this._http.get(HeaderApi.accessCheck);
  }
}
