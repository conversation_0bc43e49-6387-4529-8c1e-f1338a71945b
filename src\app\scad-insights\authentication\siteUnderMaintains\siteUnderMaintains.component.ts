import { slaService } from '../../core/services/sla/sla.service';
import { ThemeService } from 'src/app/scad-insights/core/services/theme/theme.service';
import { CommonModule } from '@angular/common';
import { Component, OnDestroy, OnInit } from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';
import { Router } from '@angular/router';
import { IFPMsalService } from 'src/app/scad-insights/core/services/IFP-msal.service';
import { SubSink } from 'subsink';

@Component({
    selector: 'app-site-under-maintains',
    templateUrl: './siteUnderMaintains.component.html',
    styleUrls: ['./siteUnderMaintains.component.scss'],
    imports: [CommonModule, TranslateModule]
})
export class SiteUnderMaintainsComponent implements OnInit, OnDestroy {

  public subs = new SubSink();

  constructor(public _themeService:ThemeService, private _slaService:slaService, private _route:Router, private _msal:IFPMsalService) { }
  public backgroundImage = 'assets/images/bg.png';
  ngOnInit() {
    this._slaService.slaLoader$.next(false);
    if (this._msal.getToken && this._msal.tokenExp && (new Date() < new Date(this._msal.tokenExp))) {
      this.subs.add(this._slaService.slaStatusCheck().subscribe((data: any) =>{
        if (data.status) {
          this._route.navigateByUrl('/');
        }
      }));
    } else {
      this._route.navigateByUrl('/auth');
    }

  }

  ngOnDestroy(): void {
    this.subs.unsubscribe();
  }

}
