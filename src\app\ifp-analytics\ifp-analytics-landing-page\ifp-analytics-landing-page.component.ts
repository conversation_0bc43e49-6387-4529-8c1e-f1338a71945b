import { Component } from '@angular/core';
import { Title } from '@angular/platform-browser';
import { title } from 'src/app/scad-insights/core/constants/header.constants';
import { CardsList, IfpToolsPageComponent } from 'src/app/shared/organism/ifp-tools-page/ifp-tools-page.component';

@Component({
  selector: 'ifp-ifp-analytics-landing-page',
  standalone: true,
  imports: [IfpToolsPageComponent],
  templateUrl: './ifp-analytics-landing-page.component.html',
  styleUrl: './ifp-analytics-landing-page.component.scss'
})
export class IfpAnalyticsLandingPageComponent {
  constructor(private _titleService: Title) {
    this._titleService.setTitle(`${title.bayaan} | Analytics`);
    // (window as any)?.dataLayer?.push({
    //   'event': 'page_load',
    //   'page_title_var': 'IFP | IFP Analytics'
    // });
  }

  public cards: CardsList[] = [
    {
      title: 'Data Preparation',
      description: 'Refine Your Data: Seamlessly extract, transform, and load your datasets for optimal analysis and insights.',
      img: '../../../assets/images/ifp-analytics/data-prep-logo.svg',
      url: 'analytics/data-preparation/upload-data',
      buttonLabel: 'Prepare Data',
      buttonSecondLabel: '',
      secondUrl: ''
    },
    {
      title: 'Data Exploration',
      description: 'Investigate relationships between variables through correlation analysis, gaining deeper insights into your data.',
      img: '../../../assets/images/ifp-analytics/data-explore-logo.svg',
      url: 'analytics/exploratory',
      buttonLabel: 'Explore Data',
      buttonSecondLabel: '',
      secondUrl: ''
    },
    {
      title: 'Advanced Analytics',
      description: 'Effortlessly generate powerful machine learning models with advanced automation and optimization.',
      img: '../../../assets/images/dashboard-builder/dashboard-builder-logo.svg',
      url: 'store/dashboards',
      buttonLabel: 'Start',
      buttonSecondLabel: '',
      secondUrl: ''
    }
  ];
}
