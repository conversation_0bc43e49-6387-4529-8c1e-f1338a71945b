trigger: none

variables:
  app_name: 'scad-insights'
  registry: '$(OpenShift.Registry)/azure-devops'
  ouser: '$(OpenShift.Registry.Username)'
  opassword: '$(OpenShift.SA.Token)'
  openshift_url: '$(OpenShift.API.Server)'

jobs:
- job: Init
  pool: 'oc-light-agent'
  steps:
  - script: |
        echo "##vso[task.setvariable variable=version;isOutput=true]$(Build.SourceVersion)"
        echo "##vso[build.updatebuildnumber]$(Build.SourceVersion)"
    name: setvarStep
    displayName: 'Generating version'
    failOnStderr: "true"


- job: Build
  pool: 'oc-light-agent'
  dependsOn: Init
  variables:
    version: $[ dependencies.Init.outputs['setvarStep.version'] ]
  steps:
  - task: NodeTool@0
    inputs:
      versionSpec: '18.x'
  - script: |
      sed -i 's/$CLIENTID/'"'$CLIENTID'"'/' $(Build.SourcesDirectory)/src/environment.js
      sed -i 's/$TENANTID/'"'$TENANTID'"'/' $(Build.SourcesDirectory)/src/environment.js
      sed -i 's/$DOMAIN/'"'$DOMAIN'"'/' $(Build.SourcesDirectory)/src/environment.js
      sed -i 's/$USERLIST/'"'$USERLIST'"'/' $(Build.SourcesDirectory)/src/environment.js
      cat  $(Build.SourcesDirectory)/src/environment.js
    displayName: 'prepare environment.js'
  - script: |
      export NODE_OPTIONS=--max_old_space_size=8192
      npm install
      npm run staging
      cat ./dist/scad-insights/environment.js
    failOnStderr: "false"
    displayName: 'npm install'


  - script: |
      buildah login --tls-verify=false $(registry) --username $(ouser) --password $(opassword)
    failOnStderr: "true"
    displayName: 'login to buildah registry'

  - script: |
      buildah build -t $(registry)/$(app_name):$(version) --build-arg download_key="RRp8u75iRpRMWnjfZ37DPg"  -f operations/dockerfiles/dev.dockerfile  .  
      buildah push --tls-verify=false $(registry)/$(app_name):$(version)
    displayName: 'Building and pushing image to buildah registry $(registry)/$(app_name):$(version)'


- job: Deploy
  pool: 'oc-light-agent'
  dependsOn: [Build,Init]
  variables:
    version: $[ dependencies.Init.outputs['setvarStep.version'] ]
  steps:
  - script: |
      B64_PAT=$(printf ":%s" "$PAT" | base64)
      git clone -c http.extraheader="AUTHORIZATION: Basic ${B64_PAT}" -b master https://<EMAIL>/tfs/COI-Projects/scad-gitops/_git/scad-gitops  $(Build.SourcesDirectory)/scad-gitops
      cd $(Build.SourcesDirectory)/scad-gitops/bayaangov/envs/staging/insights
      git config --global user.email "<EMAIL>"
      git config --global user.name "DevOps Server"
      full_image_name="$(OpenShift.Registry.Internal)/azure-devops/$(app_name)"
      yq eval --inplace \
      '(.images[] | select(.name == "'"$full_image_name"'") | .newTag) = "'"$(version)"'"' \
      "kustomization.yaml"
      git add kustomization.yaml
      git commit --allow-empty -m "Release pipeline staging -- $(version)"
      git push
    displayName: 'Change version in gitops repository'

 
