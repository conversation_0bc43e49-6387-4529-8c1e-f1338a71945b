import { Component, EventEmitter, Input, Output } from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';
import { buttonClass } from 'src/app/scad-insights/core/constants/button.constants';
import { IfpButtonComponent } from 'src/app/scad-insights/ifp-widgets/ifp-atoms/ifp-button/ifp-button.component';
import { SubSink } from 'subsink';
import { ifpColors } from 'src/app/scad-insights/core/constants/color.constants';
import { IfpNoDataComponent } from '../../../scad-insights/ifp-widgets/ifp-molecules/ifp-no-data/ifp-no-data.component';
import { IfpSpinnerComponent } from '../../../scad-insights/ifp-widgets/ifp-molecules/ifp-spinner/ifp-spinner.component';
import { DecimalPipe, NgClass } from '@angular/common';
import { IfpTooltipDirective } from 'src/app/scad-insights/core/directives/ifp-tooltip.directive';

@Component({
    selector: 'ifp-data-summary',
    templateUrl: './ifp-data-summary.component.html',
    styleUrl: './ifp-data-summary.component.scss',
    imports: [TranslateModule, IfpButtonComponent, IfpNoDataComponent, IfpSpinnerComponent, DecimalPipe, IfpTooltipDirective, NgClass]
})
export class IfpDataSummaryComponent  {

  @Input() data : any =[];
  @Input() settings:TableSettings = {};
  @Input() loader = false;
  @Input() loaderPage = false;
  @Input() diable = false;
  @Input() buttonName: string = 'Proceed';
  @Input() backButton = true;
  @Input() title = 'Data Exploratory Summary';
  @Input() subtitle!:string;
  @Input() border = false;
  @Input() disableProcessButton = false;
  @Input() totalRow?: number;
  @Input() totalColumn?: number;
  @Input() enableProcessBtn = true;
  @Input() summeryPopup = false;
  @Output() proceed = new EventEmitter();
  @Output() back = new EventEmitter();
  @Output() downloadPreviewValue  = new EventEmitter();

  public tableType = tableTypes;

  public buttonClass = buttonClass;
  public subs = new SubSink();

  onSubmit() {
    this.proceed.emit();
  }


  getColor(value: number) {
    if (66<= value &&   value<= 100) {
      return ifpColors.green;
    } else if (33<= value &&   value<= 66) {
      return ifpColors.yellow;
    }
    return ifpColors.red;

  }

  getColorReverse(value: number) {
    if (50<= value &&   value<= 100) {
      return ifpColors.red;
    }
    return ifpColors.yellow;

  }

  downloadPreview() {
    this.downloadPreviewValue.emit();
  }

  goBack() {
    this.back.emit();
  }
}
export const tableTypes = {
  progress: 'progress',
  table: 'table',
  withsubTittle: 'withsubTittle',
  bracket: 'bracket'
};

export interface TableSettings {setting?: {key?: string,
  precentageKey ?: string
  type?: string,
  name: string,
  subTittle?: string,
  color?: string,
  secondKey?: string,
  reverse?: boolean,
  tableSettings?:{key?: string, name: string, fun?: (column: any, row: any )=> string, funName?: (column: any, row: any )=> string}[],
}[], withId?:boolean }
