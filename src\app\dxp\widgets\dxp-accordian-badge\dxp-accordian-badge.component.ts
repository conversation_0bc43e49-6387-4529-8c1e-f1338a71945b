import { dateFormat } from './../../../scad-insights/core/constants/date.constants';
import { Component, input, output } from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';
import { IfpButtonComponent } from "../../../scad-insights/ifp-widgets/ifp-atoms/ifp-button/ifp-button.component";
import { buttonClass } from '../../../scad-insights/core/constants/button.constants';
import { DatePipe } from '@angular/common';

@Component({
  selector: 'ifp-dxp-accordian-badge',
  imports: [TranslateModule, IfpButtonComponent, DatePipe],
  templateUrl: './dxp-accordian-badge.component.html',
  styleUrl: './dxp-accordian-badge.component.scss'
})
export class DxpAccordianBadgeComponent {

public getSelectedAsset = output<string>();

public id = input('');
public title = input('');
public date = input('');
public buttonClass=buttonClass;
public dateFormat = dateFormat;

onSelectItem() {
  this.getSelectedAsset.emit(this.id());
}
}
