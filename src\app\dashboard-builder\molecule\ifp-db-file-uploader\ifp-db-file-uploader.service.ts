import { Injectable } from '@angular/core';
import { ToasterService } from 'src/app/scad-insights/core/services/tooster/ToastrService.service';
import { errorMessage, fileFormats, fileSizeLimit } from './ifp-db-file-uploader.constants';

@Injectable({
  providedIn: 'root'
})
export class IfpDbFileUploaderService {

  constructor(private _toast: ToasterService) { }

  public validateDocument(file: File | undefined, allowedExtensions: string | string[] = fileFormats.allowedExtensions, sizeLimit: number = fileSizeLimit.maxSize) {
    if (file) {
      const ext = file.name.substring(file.name.lastIndexOf('.') + 1).toLowerCase();
      if (!(allowedExtensions.includes(`.${ext}`))) {
        this._toast.error(errorMessage.formatError);
        return false;
      } else if ((file.size /1024) >= sizeLimit) {
        this._toast.error(`${errorMessage.fileSizeExceeds} ${(sizeLimit / 1024 )} MB`);
        return false;
      }
      return true;
    }
    return false;
  }
}
