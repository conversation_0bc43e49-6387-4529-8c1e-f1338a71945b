<div class="ifp-prep-upload" [ngClass]="{'ifp-prep-upload--rotate': rotate}">
  @if (headingEnable) {
  <h2 class="ifp-prep-upload__heading">{{'Upload the data you want to Clean and Prep' | translate}}</h2>
  }
  <div class="ifp-prep-upload__content" [ngClass]="{'ifp-disable': disableAll}">
    <div class="ifp-prep-upload__content-header">
      <h2 class="ifp-prep-upload__content-title">{{ heading| translate}}</h2>
      <p class="ifp-prep-upload__content-desc">{{subHeading | translate}}</p>
    </div>
    <div class="ifp-prep-upload__content-body">
      <div class="ifp-prep-upload__library-content-wrapper">
        <p class="ifp-prep-upload__library-text" (click)="onLibraryClick()"><span class="ifp-prep-upload__library-icon"><em class="ifp-icon ifp-icon-library"></em></span>{{'From My BAYAAN Library' | translate}}</p>
        @if (indicatorEnable) {
          <p class="ifp-prep-upload__library-text" (click)="onIndicatorClick()"><span class="ifp-prep-upload__library-icon ifp-prep-upload__library-icon--bg-yellow"><em class="ifp-icon ifp-icon-browse"></em></span>{{'From Indicators' | translate}}</p>

        }
            </div>
      <div  class="ifp-prep-upload__library-divider-wrapper">
        <div class="ifp-prep-upload__library-vertical-divider">
          <div class="ifp-prep-upload__library-line"> <span class="ifp-prep-upload__library-line-wrapper">{{'OR'| translate}}</span> </div>
        </div>
      </div>

      <div class="ifp-prep-upload__library-upload">
      <div class="ifp-prep-upload__library-content-wrapper">
      <app-ifp-db-file-uploader [hideDelete]="hideDelete" class="ifp-prep-upload__browse" [dragOnly]="true" [iconBackgroung]="'ifp-file-upload__blue'" [supportText]="'Supported file formats'" [dragAndDropText]="'Drag & Drop File Or'" [fileName]="currentFile()"  [isImage]="false" [allowedExtensions]="allowedExtensions" (fileUpload)="uploadFile($event)" ></app-ifp-db-file-uploader>
      </div>
      @if(currentFile() && currentFile() !== '') {
        <ifp-upload-bar [error]="error" [loader]="loaderFile" [size]="size" class="ifp-prep-upload__upload-bar" (closeEvent)="deleteFile($event)" [fileName]="currentFile" [width]="progress"></ifp-upload-bar>
      }

      @if (sheetList && sheetList.length) {
        <div class="ifp-prep-upload__dropdown-sec">
          <!-- <p class="ifp-prep-upload__label">{{'Select Worksheet' | translate}}</p> -->
            <app-ifp-dropdown class="ifp-prep-upload__dropdown" [singleDefaultSelect]="false" [selectedValue]="selectedWorksheet" [placeHolder]="'select'" (dropDownItemClicked)="selectWorkSheet($event)"
              [dropDownItems]="sheetList"></app-ifp-dropdown>
            @if (enableProcessBtn ) {
              <ifp-button  [label]="'Proceed'" (ifpClick)="onProceed()" class="ifp-prep-upload__button" [loader]="loader"
        [buttonClass]="(disableProcesed) ? buttonClass.disabled :  buttonClass.primary" [iconClass]="'ifp-icon-rightarrow'"></ifp-button>
            }
        </div>
        } @else if (enableProcessBtn && !disableProcesed ) {
          <div class="ifp-prep-upload__dropdown-sec ifp-prep-upload__dropdown-sec--button">
            <ifp-button  [label]="'Proceed'" (ifpClick)="onProceed()" class="ifp-prep-upload__button" [loader]="loader"
            [buttonClass]="disableProcesed ? buttonClass.disabled :  buttonClass.primary" [iconClass]="'ifp-icon-rightarrow'"></ifp-button>
          </div>
        }
      </div>

         </div>
    <div>
    </div>
    <!-- <div class="ifp-prep-upload__desc">
      <ul  class="ifp-prep-upload__inner">
        <li>{{'When uploading a CSV file, you can upload the file directly.' | translate}}</li>
        <li>{{'XLSX file with a single sheet can be uploaded directly. While uploading an XLSX file with multiple sheets, select the sheet name to be uploaded as source from the dropdown button after selecting the file.' | translate}}
        </li>
      </ul>
            </div> -->

    <!-- @if (enableProcessBtn) {
      <div class="ifp-prep-upload__btn-wrapper">
        <ifp-button  [label]="'Proceed'" (ifpClick)="onProceed()" class="ifp-prep-upload__button" [loader]="loader"
        [buttonClass]="disableProcesed ? buttonClass.disabled :  buttonClass.primary" [iconClass]="'ifp-icon-rightarrow'"></ifp-button>
      </div>
    } -->


  </div>

</div>
