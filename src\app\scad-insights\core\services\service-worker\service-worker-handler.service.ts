
import { ApplicationRef, Injectable } from '@angular/core';
import { SwUpdate, VersionReadyEvent } from '@angular/service-worker';
import { concat, filter, first, interval } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class ServiceWorkerHandlerService {

  constructor(
    private _updates: SwUpdate,
    private _appRef: ApplicationRef
  ) {

  }

  // used to check latest version of application and reload
  checkForUpdate() {
    return this._updates.versionUpdates
      .pipe(filter((evt): evt is VersionReadyEvent => evt.type === 'VERSION_READY'));
  }

  // used to check latest version of on check navigation every one hour
  // for more information https://angular.io/guide/service-worker-communications
  updateCheckOnNavigation() {
    const appIsStable$ = this._appRef.isStable.pipe(first((isStable: boolean) => isStable));
    const everyOneHours$ = interval(1 * 60 * 60 * 1000);
    const everyOneHoursOnceAppIsStable$ = concat(appIsStable$, everyOneHours$);

    everyOneHoursOnceAppIsStable$.subscribe(async () => {
      try {
        await this._updates.checkForUpdate();
      } catch (err) {
        // cache
      }
    });
  }
}

export const warningSwLatest = {
  headText: 'Latest build is available.',
  subText: 'Please click the reload button to load changes.',
  buttonOpt_1: 'Reload now',
  buttonOpt_2: 'I\'ll do it later'
};
