import { Component, inject, OnInit, signal, WritableSignal, effect, viewChild, computed, OnD<PERSON>roy } from '@angular/core';
import { IfpBgPageComponent } from "../../scad-insights/ifp-widgets/ifp-organism/ifp-bg-page/ifp-bg-page.component";
import { IfpBreadcrumbsComponent } from "../../scad-insights/ifp-widgets/ifp-molecules/ifp-breadcrumbs/ifp-breadcrumbs.component";
import { PageData } from 'src/app/scad-insights/core/interface/molecule/breadcrumb.interface';
import { NgStyle, DatePipe } from '@angular/common';
import { TranslateModule } from '@ngx-translate/core';
import { Router, ActivatedRoute } from '@angular/router';
import { IfpDataTableComponent } from "../../ifp-analytics/organism/ifp-data-table/ifp-data-table.component";
import { IfpTitleCasePipe } from 'src/app/scad-insights/core/pipes/title-case.pipe';
import { dxpProductDetailStore, sampleDataStore } from './dxp-product-detail.store';
import { IfpModalComponent } from 'src/app/scad-insights/ifp-widgets/ifp-organism/ifp-modal/ifp-modal.component';
import { SubSink } from 'subsink';
import { IfpModalService } from 'src/app/scad-insights/ifp-widgets/ifp-organism/ifp-modal/ifp-modal.service';

@Component({
  selector: 'ifp-dxp-product-detail',
  imports: [IfpBgPageComponent, IfpBreadcrumbsComponent, NgStyle, TranslateModule, IfpDataTableComponent, IfpModalComponent],
  templateUrl: './dxp-product-detail.component.html',
  styleUrl: './dxp-product-detail.component.scss',
  providers: [IfpTitleCasePipe, DatePipe]
})
export class DxpProductDetailComponent implements OnInit, OnDestroy {

  private readonly _router = inject(Router);
  private readonly _route = inject(ActivatedRoute);
  private readonly _ifpTitlePipe = inject(IfpTitleCasePipe);
  private readonly _detailStore = inject(dxpProductDetailStore);
  private readonly _sampleDataStore = inject(sampleDataStore);
  private readonly _datePipe = inject(DatePipe);
  private readonly _modalService = inject(IfpModalService);

  pageData: PageData[] = [
    { title: 'Home', route: '/home' },
    { title: 'Government Affairs', route: '/dxp' },
    { title: 'Abu Dhabi Executive Office (ADEO)', route: '/dxp/catalogue' }
  ];

  public title: string = 'Economy and Investment';

  detailTable: WritableSignal<{ data: any, head: string[], headerSettings: any }> = signal({ data: [], head: [], headerSettings: [] });

  // Computed properties for API-driven title, description, and organization
  public displayName = signal<string | null>(null);
  public shortDescription = signal<string | null>(null);
  public organization = signal<string | null>(null);

  public showMetadataModal = signal(false);
  public selectedAssetMetadata = signal<any>(null);
  public modalRef = viewChild<IfpModalComponent>('metadataModal');

  public showDictionaryModal = signal(false);
  public selectedAssetDictionary = signal<any[]>([]);
  public dictionaryModalRef = viewChild<IfpModalComponent>('dictionaryModal');

  public productId: string | null = null;

  public showSampleDataModal = signal(false);
  public sampleDataTable = signal<{ head: string[], data: any[][], headerSettings: any }>({ head: [], data: [], headerSettings: {} });
  public sampleDataModalRef = viewChild<IfpModalComponent>('sampleDataModal');

  // Computed signal for sampleData from store
  public sampleData = computed(() => this._sampleDataStore.data?.() ?? null);
  public tableLoader = true;
  private readonly _subs: SubSink = new SubSink();

  private readonly _logProductDetailEffect = effect(() => {
    this.tableLoader = this._detailStore.loading();
    const data = this._detailStore.data();
    if (data?.assets && Array.isArray(data.assets)) {
      const tableData = data.assets.map((asset: any) => ({
        showSampleData: asset.hasSample,
        name: asset.name,
        type: `Table ${asset.metadata?.numColumns?.value ?? '-'} Columns, ${asset.metadata?.numRows?.value ?? '-'} Rows`,
        size: asset.metadata?.size?.value ? `${asset.metadata.size.value} KB` : '-',
        last_date_refresh: asset.lastRefreshDate
          ? this._datePipe.transform(asset.lastRefreshDate, "dd MMM yy, HH:mm 'UTC'", 'UTC')
          : '-'
      }));
      this.detailTable.set(this.setTableData(tableData));
    }
  });

  private readonly _updateHeaderEffect = effect(() => {
    const data = this._detailStore.data();
    this.displayName.set(data?.displayName ?? null);
    this.shortDescription.set(data?.shortDescription ?? null);
    this.organization.set(data?.organization ?? null);
  });

  private readonly _sampleDataEffect = effect(() => {
    const sampleData = this.sampleData();
    const dataArray = Array.isArray(sampleData) ? sampleData : (sampleData?.data ?? []);
    if (!dataArray || !Array.isArray(dataArray) || dataArray.length === 0) {
      this.showSampleDataModal.set(false);
      return;
    }
    const head = ['ACTIVITIES_AR', 'ACTIVITIES_CODE', 'ACTIVITIES_EN', 'CATEGORY_NAME_AR', 'CATEGORY_NAME_EN'];
    const data = dataArray.map((row: any) =>
      head.map(key => ({
        key,
        title: key,
        value: row[key] ?? '-',
        type: 'default',
        renderComponent: '',
        inputValues: { value: row[key] ?? '-' }
      }))
    );
    const headerSettings = Object.fromEntries(head.map(key => [key, { sort: false }]));
    this.sampleDataTable.set({ head, data, headerSettings });
    this.showSampleDataModal.set(true);
    setTimeout(() => this.sampleDataModalRef()?.createElement(), 0);
  });

  ngOnInit() {
    this._subs.add(
      this._route.queryParams.subscribe(params => {
        const productId = params['id'];
        if (productId) {
          this.productId = productId;
          this._detailStore.fetchProductDetail(productId);
        }
      })
    );

    this.pageData.push({
      title: this.title,
      route: ''
    });
  }

  setTableData(data: any) {
    if (data?.length <= 0) { return { data: [], head: [], headerSettings: {} }; }
    let tableData: any[] = [];
    let pills = [
      { key: 'Metadata', value: 'Metadata', icon: 'ifp-icon-list-view' },
      { key: 'Dictionary', value: 'Dictionary', icon: 'ifp-icon-link' }
    ]
    data.forEach((element: { [s: string]: unknown; }) => {
      let rowData: any = [];
      if (element['showSampleData']) {
        pills.push({ key: 'Sample data', value: 'Sample data', icon: 'ifp-icon-plus-round' });
      }
      delete element['showSampleData'];
      Object.entries(element).forEach(([key, value]) => {
        rowData.push({
          key,
          title: key,
          value,
          type: 'default',
          renderComponent: '',
          inputValues: { value: value }
        });
        // Add custom pills as the last value in each row
        if (Object.keys(element).indexOf(key) === Object.keys(element).length - 1) {
          rowData.push({
            key: '',
            title: '',
            type: 'custom',
            renderComponent: () => import('src/app/dxp/widgets/dxp-pills/dxp-pills.component').then(m => m.DxpPillsComponent),
            inputValues: {
              items: signal(pills)
            },
            outputs: ['pillClick']
          });
        }
      });
      tableData.push(rowData);
    });
    const head = Object.keys(data[0]).map((key: string) => this._ifpTitlePipe.transform(key));
    head.push("");
    return {
      data: tableData,
      head: head,
      headerSettings: Object.fromEntries(Object.keys(data[0]).map(key => [key, { sort: true }]))
    };
  }

  sortData(event: any) {
    // Your sorting logic here (if needed)
  }

  onTableCustomEvent(event: any) {
    const rowIndex = event.index;
    const assets = this._detailStore.data()?.assets || [];
    const asset = assets[rowIndex];
    if (!asset) return;

    if (event?.value?.value === 'Metadata') {
      this.selectedAssetMetadata.set(asset);
      this.showMetadataModal.set(true);
      setTimeout(() => this.modalRef()?.createElement(), 0);
    }
    else if (event?.value?.value === 'Dictionary') {
      const columns = asset?.columns || [];
      this.selectedAssetDictionary.set(columns);
      this.showDictionaryModal.set(true);
      setTimeout(() => this.dictionaryModalRef()?.createElement(), 0);
    }
    else if (event?.value?.value === 'Sample data') {
      if (this.productId && asset?.id) {
        this.sampleDataModalRef()?.createElement();
        this._sampleDataStore.fetchSampleData(this.productId, asset.id);
      } else {
        console.warn('Missing productId or assetId for sample data fetch');
      }
    }
  }

  onCloseModal() {
    this.modalRef()?.removeModal();
    this.showMetadataModal.set(false);
  }

  onCloseDictionaryModal() {
    this.dictionaryModalRef()?.removeModal();
    this.showDictionaryModal.set(false);
  }

  openSampleDataModal() {
    this.showSampleDataModal.set(true);
  }
  closeSampleDataModal() {
    this.showSampleDataModal.set(false);
    // setTimeout(() => this._store.clear(), 300);
    this.sampleDataModalRef()?.removeModal();

    // this._store.clear();
  }

  sampleDataTableFn() {
    return this.sampleDataTable();
  }
  showSampleDataModalFn() {
    return this.showSampleDataModal();
  }

  selectedAssetDictionaryFn() {
    return this.selectedAssetDictionary();
  }
  showDictionaryModalFn() {
    return this.showDictionaryModal();
  }

  getDictionaryTableData() {
    return this.selectedAssetDictionary().map(col => ({
      'Column Name': col.name || col.columnName || '-',
      'Data Type': col.dataType || col.type || '-',
      'Description': col.description || col.desc || '-'
    }));
  }

  dictionaryTable() {
    const head = ['Column Name', 'Data Type', 'Description'];
    const data = this.getDictionaryTableData().map((row: Record<string, any>) => {
      return head.map(key => ({
        key,
        title: key,
        value: row[key],
        type: 'default',
        renderComponent: '',
        inputValues: { value: row[key] }
      }));
    });
    const headerSettings = {
      'Column Name': { sort: false },
      'Data Type': { sort: false },
      'Description': { sort: false }
    };
    return { head, data, headerSettings };
  }

  ngOnDestroy(): void {
    this._modalService.removeAllModal();
    this._subs.unsubscribe();
    if (this.sampleDataModalRef()) {
      this.sampleDataModalRef()?.removeModal();
    }
    if (this.dictionaryModalRef()) {
      this.dictionaryModalRef()?.removeModal();
    }
    if (this.modalRef()) {
      this.modalRef()?.removeModal();
    }
  }
}
