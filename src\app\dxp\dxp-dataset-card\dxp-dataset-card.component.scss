@use "../../../assets/ifp-styles/abstracts/index" as *;
:host {
  display: block;
}
.ifp-dxp-dataset-card {
  border-radius: 10px;
  padding: $spacer-3 $spacer-2;
  background-color: $ifp-color-blue-4;
  border: 1px solid $ifp-color-blue-3;
  &__head {
    display: flex;
    justify-content: space-between;
  }
  &__title {
    font-weight: $fw-medium;
    font-size: $ifp-fs-3;
  }
  &__delete{
    color: $ifp-color-blue-menu;
    cursor: pointer;
  }
  &__body {
    margin-top: $spacer-3;
    max-height: 75vh;
    @include ifp-scroll-y(transparent, $ifp-color-grey-8, 4px, 4px);
    padding: $spacer-0 $spacer-2;
  }
  &__sub-card {
    position: relative;
    display: flex;
    align-items: center;
    color: $ifp-color-grey-14;
    background-color: $ifp-color-white;
    padding: $spacer-2;
    margin-bottom: $spacer-3;
    border-radius: 10px;
    box-shadow: 0 17px 18pc -10px rgb(98, 110, 106, 0.07);
    min-width: 200px;
  }
  &__sub-card-clone {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;

  }
  &__sub-card-ovelay {
    width: 100%;
    height: 100%;
  }
  &__icons {
    font-size: $ifp-fs-5;
    margin-left: auto;
  }
  &__dragable {
    margin-inline-end:$spacer-3 ;
  }
}
