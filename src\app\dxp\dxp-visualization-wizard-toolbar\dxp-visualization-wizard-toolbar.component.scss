@use "../../../assets/ifp-styles/abstracts/index" as *;

.ifp-vis-toolbar {
  padding: $spacer-4;
  &__heading {
    font-size: $ifp-fs-5;
    font-weight: $fw-semi-bold;
    margin-bottom: $spacer-1;
  }
  &__desc {
    color: $ifp-color-grey-14;
    margin-bottom: $spacer-3;
  }
  &__tabs {
    display: flex;
    margin: $spacer-0 (-$spacer-2) $spacer-3;
  }
  &__tab-item {
    color: $ifp-color-secondary-grey;
    text-align: center;
    width: calc(25% - (2 * $spacer-2));
    padding: $spacer-2 $spacer-2 ($spacer-2 + 2px);
    border: 1px solid $ifp-color-grey-13;
    border-radius: 8px;
    margin: $spacer-0 $spacer-2;
    position: relative;
    overflow: hidden;
    transition: 0.3s;
    cursor: pointer;
    &::after {
      content: "";
      width: 100%;
      height: 5px;
      position: absolute;
      left: 0;
      bottom: -1px;
      transition: 0.3s;
    }
    &--active,
    &:hover {
      color: $ifp-color-blue-hover;
      border-color: $ifp-color-blue-hover;
    }
    &--active {
      &::after {
        background-color: $ifp-color-blue-hover;
      }
    }
  }
  &__tool-desc {
    margin-bottom: $spacer-3;
  }
  &__tab-icon {
    font-size: $ifp-fs-8;
    color: inherit;
    display: block;
    margin-bottom: $spacer-1;
  }
  &__new-filter {
    margin-bottom: $spacer-3;
  }
  &__filter-card {
    margin-top: $spacer-3;
  }
  &__chart-set {
    display: flex;
    flex-wrap: wrap;
    margin: $spacer-0 (-$spacer-3);
  }
  &__accordion {
    display: block;
    margin: $spacer-0 (-$spacer-4);
  }
  &__no-data {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: $spacer-3;
    border: 1px solid $ifp-color-grey-7;
    border-radius: 10px;
    min-height: 250px;
  }
  &__empty-message {
    font-size: $ifp-fs-7;
    font-weight: $fw-medium;
    text-align: center;
  }
}

.ifp-vis-toolbar__filter-btn {
  display: inline-block;
  margin-top: $spacer-4; // Moves the button upwards, adjust value as needed\
}

.ifp-vis-toolbar__chart-icon.disabled {
  pointer-events: none;
  opacity: 0.5;
  cursor: not-allowed;
}

:host::ng-deep {
  ifp-accordion {
    .ifp-vis-toolbar {
      &__chart-icon {
        display: block;
        margin: $spacer-2 $spacer-3;
        width: calc(25% - (2 * $spacer-3));
      }
    }
  }
}

// :host ::ng-deep .ifp-btn,
// :host ::ng-deep .ifp-btn--disabled {
//   margin-top: -$spacer-1;
//   margin-bottom: $spacer-3;
// }

.ifp-vis-toolbar__selected-list {
  margin-top: 1.5rem;
  padding: 1.5rem 1.5rem 1rem 1.5rem;
  background: linear-gradient(135deg, #f0f4ff 80%, #e3f2fd 100%);
  border-radius: 14px;
  box-shadow: 0 4px 16px rgba(30, 136, 229, 0.10), 0 1.5px 6px rgba(30, 136, 229, 0.04);
  border: 1.5px solid #b6ccfa;

  .ifp-vis-toolbar__default-label {
    font-weight: 700;
    font-size: 1.15rem;
    margin-bottom: 1.2rem;
    display: block;
    color: #142b6f;
    letter-spacing: 0.01em;
  }

  .ifp-vis-toolbar__selected-item {
    display: flex;
    align-items: center;
    padding: 0.7rem 1rem;
    border-radius: 8px;
    margin-bottom: 0.7rem;
    transition: background 0.2s, box-shadow 0.2s;
    cursor: pointer;
    background: transparent;

    &:hover {
      background: #e3f2fd;
      box-shadow: 0 2px 8px rgba(30, 136, 229, 0.10);
    }

    input[type="radio"] {
      accent-color: #1976d2;
      width: 1.25em;
      height: 1.25em;
      margin-right: 1em;
      cursor: pointer;
      background: #fff;
    }

    span {
      font-size: 1.08rem;
      color: #263238;
      font-weight: 500;
      letter-spacing: 0.01em;
      transition: color 0.2s, font-weight 0.2s;
    }

    // Highlight selected
    input[type="radio"]:checked + span {
      color: #1976d2;
      font-weight: 800;
      font-size: 1.13rem;
      text-shadow: 0 1px 2px #b6ccfa33;
    }
  }
}
