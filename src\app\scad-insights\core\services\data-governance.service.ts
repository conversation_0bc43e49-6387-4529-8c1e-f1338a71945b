
import { Injectable } from '@angular/core';
import { HttpService } from './http/http.service';
import { commonApi, dataGovernanceApiConstatnts, dataGovernanceDropDownConstants } from '../apiConstants/common-api.constants';
import { DatePipe } from '@angular/common';
import { environment } from 'src/environments/environment';

@Injectable({
  providedIn: 'root'
})
export class DataGovernanceService {

  constructor(private _http: HttpService, private _datePipe:DatePipe) { }

  getChartData() {
    return this._http.get(commonApi.getDataGovernanceChartData);
  }

  getDataGovernanceCategoryData(category: string, filter: { [key: string]: any } = {}, params: {page: number; per_page: number; exportType: string} = {page: 1, per_page: 10, exportType: ''}) {
    const endpoint = dataGovernanceApiConstatnts[category];
    return params.exportType === '' ?
    this._http.post(`${endpoint}?page=${params.page}&limit=${params.per_page}`, filter) :
    this._http.postFileDownloadWithHeader(`${environment.baseUrl}${environment.apiVersion}${endpoint}?export_type=${params.exportType}`, filter);
  }

  getDataGovernanceStatisticalTableData(filter: Filter, page: number = 1, per_page: number = 10) {
    const endpoint = dataGovernanceApiConstatnts.statistical_indicator;
    return this._http.post(`${endpoint}?page=${page}&limit=${per_page}`, filter)
  }

  getCatDataCount(category: string) {
    return this._http.get(`${commonApi.getCatDataCount}?category=${category}`);
  }

  getCatDropDownOpts(category: string) {
    const endpoint = dataGovernanceDropDownConstants[category]
    return this._http.get(endpoint);
  }

  getDataScienceInput(page: number = 1, per_page: number = 10, filter: string) {
    return this._http.get(`${commonApi.getDataScienceInput}?page=${page}&limit=${per_page}&filter=${filter}`);
  }

  getDataScienceOutput(page: number = 1, per_page: number = 10, filter: string) {
    return this._http.get(`${commonApi.getDataScienceOutput}?page=${page}&limit=${per_page}&filter=${filter}`);
  }

  getComparisonReport() {
    return this._http.get(commonApi.getComparisonReport);
  }


  checkColor = (status: any): string => {
    if(!status){ return '#ed5564'}
    status = status.toLowerCase().replace(/\s/g, "");
    const statusColors: { [key: string]: string } = {
      'outdated': '#F3593A',
      'uptodate': '#5DB14E',
      'ontime': '#a0d568',
      'notstarted': '#FAC656',
      'inprogress': '#f8b100',
      'statisticalindicators': '#2687fd',
      'bayaanindicators': '#3bd6ad',
      'upcoming': '#FAC656',
      'available': '#5DB14E',
    };

    return statusColors[status] || '#ed5564';
  };


  changeDateFormats(date: string, frequency: string): string {
    if (!date) return '';
    const freq = frequency.toLowerCase();
    const year = this._datePipe.transform(date, 'yyyy') ?? '';
    const month = parseInt(this._datePipe.transform(date, 'M') ?? '0', 10);

    switch (freq) {
      case 'monthly':
        return this._datePipe.transform(date, 'MMM yyyy') ?? '';
      case 'quarterly':
        return `Q${Math.ceil(month / 3)} ${year}`;
      case 'semi-annual':
        return `H${Math.ceil(month / 6)} ${year}`;
      default:
        return year;
    }
  }





}

export interface Filter {
  topic: string,
  theme: string,
  sub_theme: string,
  product: string,
  start_date: string,
  end_date: string,
  search: string
}
