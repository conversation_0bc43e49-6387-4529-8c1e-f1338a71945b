import { Injectable } from '@angular/core';
import {
  HttpRequest,
  HttpHandler,
  HttpEvent,
  HttpInterceptor,
} from '@angular/common/http';
import { Observable } from 'rxjs';
import { IFPMsalService } from 'src/app/scad-insights/core/services/IFP-msal.service';
import { CookieService } from '../services/cookie.service';
import { ThemeService } from '../services/theme/theme.service';
import { Router } from '@angular/router';
import { environment } from 'src/environments/environment';
import { xTokenList } from '../../user-onboarding/user-onboarding.constants';
import { authTypes } from '../constants/auth.constants';
import { UaePassService } from '../services/uae-pass-service/uae-pass-service.service';

@Injectable()
export class AuthInterceptor implements HttpInterceptor {
  constructor(
    private _msalService: IFPMsalService,
    private _cookie: CookieService,
    private _theme: ThemeService,
    private _router: Router,
    private _uaePassService: UaePassService
  ) { }

  intercept(
    request: HttpRequest<unknown>,
    next: HttpHandler
  ): Observable<HttpEvent<unknown>> {
    const isMsal = this._msalService.authMethod === authTypes.msal;
    const authtoken = this._msalService.getToken;
    const lang =
      this._cookie.getCookie('lang') !== ''
        ? this._cookie.getCookie('lang')
        : this._theme.defaultLang;
    if (authtoken) {
      if (isMsal) {
        if (
          this._msalService.tokenExp &&
          new Date() > new Date(this._msalService.tokenExp) &&
          localStorage.getItem(authTypes.msal)
        ) {
          this._msalService.callrefreshtoken();
        }
      } else if (
        this._uaePassService.tokenExp &&
        new Date() > new Date(this._uaePassService.tokenExp) &&
        localStorage.getItem(authTypes.uaePass)
      ) {
        this._uaePassService.callrefreshtoken();
      }
      if (request.url.includes(environment.prepbaseUrl)) {
        const authReq = request.clone({
          headers: request.headers
            .set('Authorization', `Bearer ${authtoken}`)
            .set('Accept-Language', lang)
            .set('WSession', this._msalService.wSessionId())
            .set('Auth-Type', isMsal ? 'msal' : 'upass')
        });
        return next.handle(authReq);
      }
      if (request.url.includes('https://graph.microsoft.com/')) {
        const authReq = request.clone({
          headers: request.headers
            .set('Authorization', `Bearer ${this._msalService.getAccessToken}`)
            .set('X-Content-Type-Options', 'nosniff')
            .set('X-XSS-Protection', '1; mode=block')
            .set('X-Frame-Options', 'DENY')
            .set(
              'Strict-Transport-Security',
              'max-age=31536000; includeSubDomains; preload'
            )
            .set('Accept', 'application/json')
            .set('Accept', 'application/json')
            .set('Content-Type', 'application/json')
            .set('Accept-Language', lang),
        });
        return next.handle(authReq);
      }
      if (request.url.includes('evaluation')) {
        const authReq = request.clone({
          headers: request.headers
            .set('Authorization', `Bearer ${authtoken}`)
            .set('Accept-Language', lang),
        });
        return next.handle(authReq);
      }
      if (
        request.url.includes('/ai-insight-report') ||
        request.url.includes('/domain-ticket') ||
        request.url.includes('/consolidated-ticket')
      ) {
        const authReq = request.clone({
          // url: request.url.replace(/https?:\/\/[^/]+/, 'http://localhost:3000'),
          headers: request.headers
            .set('Authorization', `Bearer ${authtoken}`)
            .set('Accept-Language', 'en'),
        });
        return next.handle(authReq);
      }
      if (
        request.url.includes('content-type/dashboard-builder/create') ||
        (request.url.includes('dashboard-builder/dashboard') &&
          request.method == 'PUT')
      ) {
        const authReq = request.clone({
          headers: request.headers
            .set('Authorization', `Bearer ${authtoken}`)
            .set('X-Content-Type-Options', 'nosniff')
            .set('X-XSS-Protection', '1; mode=block')
            .set('X-Frame-Options', 'DENY')
            .set(
              'Strict-Transport-Security',
              'max-age=31536000; includeSubDomains; preload'
            )
            .set('Accept', 'application/json')
            .set('Accept', 'application/json')
            .set('Accept-Language', lang),
        });
        return next.handle(authReq);
      }
      if (this._msalService.cacheToken) {
        const authReq = request.clone({
          headers: request.headers
            .set('Authorization', `Bearer ${authtoken}`)
            .set('X-Content-Type-Options', 'nosniff')
            .set('X-XSS-Protection', '1; mode=block')
            .set('X-Frame-Options', 'DENY')
            .set(
              'Strict-Transport-Security',
              'max-age=31536000; includeSubDomains; preload'
            )
            .set('Accept', 'application/json')
            .set('Accept', 'application/json')
            .set('Content-Type', 'application/json')
            .set('Accept-Language', lang)
            .set('Cache-Control', 'no-cache'),
        });
        return next.handle(authReq);
      }

      if (request.url.includes('domains/navigation')) {
        const authReq = request.clone({
          headers: request.headers
            .set('Authorization', `Bearer ${authtoken}`)
            .set('X-Content-Type-Options', 'nosniff')
            .set('X-XSS-Protection', '1; mode=block')
            .set('X-Frame-Options', 'DENY')
            .set(
              'Strict-Transport-Security',
              'max-age=31536000; includeSubDomains; preload'
            )
            .set('Accept', 'application/json')
            .set('Accept', 'application/json')
            .set('Content-Type', 'application/json')
            .set('Accept-Version', '2.0')
            .set('Accept-Language', lang),
        });
        return next.handle(authReq);
      }


      const authReq = request.clone({
        headers: request.headers
          .set('Authorization', `Bearer ${authtoken}`)
          .set('X-Content-Type-Options', 'nosniff')
          .set('X-XSS-Protection', '1; mode=block')
          .set('X-Frame-Options', 'DENY')
          .set(
            'Strict-Transport-Security',
            'max-age=31536000; includeSubDomains; preload'
          )
          .set('Accept', 'application/json')
          .set('Accept', 'application/json')
          .set('Content-Type', 'application/json')
          .set('Accept-Language', lang)
          .set('Auth-Type', this._msalService.authMethod == authTypes.uaePass ? 'upass' : 'msal')
      });
      return next.handle(authReq);
    }
    const isXtoken = xTokenList.find((x: string) => request.url.includes(x));
    if (isXtoken) {
      const xToken = this._router.url.split('=').pop();
      const registerReq = request.clone({
        headers: request.headers
          .set('X-TOKEN', xToken ?? '')
          .set('X-Content-Type-Options', 'nosniff')
          .set('X-XSS-Protection', '1; mode=block')
          .set('X-Frame-Options', 'DENY')
          .set(
            'Strict-Transport-Security',
            'max-age=31536000; includeSubDomains; preload'
          )
          .set('Accept', 'application/json')
          .set('Accept-Language', lang),
      });
      return next.handle(registerReq);
    }

    return next.handle(request);
  }
}
