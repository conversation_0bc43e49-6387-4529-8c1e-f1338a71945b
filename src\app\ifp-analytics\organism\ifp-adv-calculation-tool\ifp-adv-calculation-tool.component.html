@if(formulaLoader() && fieldLoader() && !workFlowRunning() && !emptyConnection() && currentConection() && sourceData() && columnList && columnList.length && !errorMessageForTools)
{
<!-- <div class="ifp-adv-tool__action">
    <div class="ifp-adv-tool__action-inner">
      <ifp-button [label]="'cancel'" (ifpClick)="onCancel()" class="ifp-adv-tool__button ifp-adv-tool__action-btn" [buttonClass]="buttonClass.secondary"></ifp-button>
      <ifp-button [label]="'Save'" (ifpClick)="onSave()" class="ifp-adv-tool__button ifp-adv-tool__action-btn" [buttonClass]="isConfigUpdated() ?  buttonClass.primary: buttonClass.disabled"></ifp-button>
    </div>
  </div> -->
<form [formGroup]="calcForm">


  <ifp-data-tool-accordian [enableCheckBox]="false" [isAccordianExpanded]="true" [title]="'Calculations' | translate"
    [icon]="'ifp-icon-calculation'" [iconColor]="color.green" class="ifp-adv-tool__accordian ifp-adv-calc__accordian">
    <div class="ifp-accord-box__content">
      <!-- <ifp-search class="ifp-adv-tool__search" [isKeypress]="true" (searchEvent)="searchResult($event)" [onSearch]="searchString" [boxType]="true" [placeholderText]="'Search'| translate"></ifp-search> -->

      <app-ifp-tab [tabData]="operationTypeList" [transparent]="true" [showIcon]="false" [tooltipDisabled]="true"
        (selectedTabEvent)="selectType($event)" [selectedTab]="selectedType" [selectionType]="'name'"
        class="ifp-adv-tool__tab"></app-ifp-tab>
      <div class="ifp-adv-calc__formula-wrapper">
        @for (item of fuctionList; track $index) {
        <ifp-button [disableTranslate]="true" class="ifp-adv-calc__formula-tag" [label]="item" (ifpClick)="selectFormula(item)" (mousemove)="onMouseMove(item)" (mouseleave)="onMouseLeave()"
          [buttonClass]="(selectedFunction && selectedFunction?.keyword == item ?  buttonClass.primary : buttonClass.tertiary)+' '+ buttonClass.round+ ' ' +  buttonClass.normalAplabetic"></ifp-button>
        }
      </div>
    </div>
  </ifp-data-tool-accordian>

  @if (selectedFunction && selectedFunction?.keyword) {
  <div class="ifp-adv-tool__syntax-wrapper">
    <p class="ifp-adv-tool__synatax-name">{{'Selected Function' | translate}} : <span
        class="ifp-adv-tool__synatax-value">{{selectedFunction.keyword+'()'}}</span></p>

    <div class="ifp-adv-tool__syntax-body">
      <div class="ifp-adv-tool__syntax-function">
        <h3 class="ifp-adv-tool__syntax-head">{{selectedFunction?.keyword+'()'}}</h3>
        <p class="ifp-adv-tool__syntax-desc">{{selectedFunction?.description}}</p>
      </div>
      <div class="ifp-adv-tool__syntax-function">
        <h3 class="ifp-adv-tool__syntax-head">{{'Syntax' | translate}}</h3>
        <p class="ifp-adv-tool__syntax-desc">{{selectedFunction?.syntax}}</p>
      </div>
      <div class="ifp-adv-tool__syntax-function">
        <h3 class="ifp-adv-tool__syntax-head">{{'Example' | translate}}</h3>
        <p class="ifp-adv-tool__syntax-desc">{{selectedFunction?.example}}</p>
      </div>
    </div>
  </div>
  }

  <!-- <ifp-data-tool-accordian [enableCheckBox]="false" [isAccordianExpanded]="true" [title]="'Drag Columns' | translate"
    [iconColor]="color.green" class="ifp-adv-calc__accordian-columns">
    <div class="ifp-accord-box__content">

      <div class="ifp-adv-tool__body-head">
        <ifp-search [isOnlyBorder]="true" [showPlaceHolder]="true" [isKeypress]="true"
          [placeholderText]="'Search here' | translate" class="ifp-adv-tool__search"
          (searchEvent)="searchField($event)"></ifp-search>
      </div>
      <ul class="ifp-adv-tool__field-list">
        @for (field of columnList; let i = $index; track i) {
        <li class="ifp-adv-tool__field" [draggable]="true" (drag)="onItemDrag(field.name, 'field')"><em
            class="ifp-icon ifp-icon-dragable ifp-adv-tool__field-icon"></em>{{field.name}}</li>
        } @empty {
        <div class="ifp-adv-tool__notFound">
          {{ 'Not Found' | translate}}!
        </div>
        }
      </ul>
    </div>
  </ifp-data-tool-accordian> -->




  <!-- <div class="ifp-adv-tool__edit-outer">
    <p class="ifp-adv-tool__label">{{'Select Column' | translate}}</p>
    <app-ifp-dropdown class="ifp-adv-tool__dropdown" formControlName="outputColumn"
      (dropDownItemClicked)="selectColumn($event)" [dropDownItems]="columnList" [key]="'name'"
      [disableSingleValue]="true"></app-ifp-dropdown>
  </div> -->

  <div class="ifp-adv-calc__preview-head">
    <p class="ifp-adv-tool__sub-title">{{'Enter the expressions below' | translate}}</p>
    <span class="ifp-link" (click)="resetExpression()"><em class="ifp-icon ifp-icon-refresh-round"></em>{{'Reset' | translate}}</span>
  </div>

  <p class="ifp-adv-tool__column-desc">{{'Select a function and start typing the field name in the expression pane. A dropdown will appear for easy selection.' | translate}}</p>
  <div class="ifp-adv-tool__box ifp-adv-calc__editor-outer" (dragover)="onDragOver($event)" (drop)="onDrop($event)">
    <ngx-codemirror [formControlName]="'expression'" #codeEditor [options]="configCodemirror"
      (codeMirrorLoaded)="codeMirrorLoaded($event)" (drop)="drop($event)"></ngx-codemirror>
  </div>

  <!-- <div class="ifp-adv-tool__content ifp-adv-tool__content--grey ifp-adv-calc__preview">
      <p class="ifp-adv-tool__sub-title">{{'Data Preview' | translate}}</p>
      <p class="ifp-adv-calc__sample"><strong>{{selectedDataType === 'string' ? 'Text sample' : '29,200'}}</strong></p>
    </div> -->

  <app-ifp-check-box class="ifp-adv-tool__checkbox ifp-adv-tool__checkbox--radio" [label]="'New Column' | translate"
    [type]="'radio'" [name]="'column'" (checked)="selectSaveType(calculationConfig.newColumn)"
    [checkedData]="isColumnSaveType === calculationConfig.newColumn"></app-ifp-check-box>
  <app-ifp-check-box class="ifp-adv-tool__checkbox ifp-adv-tool__checkbox--radio"
    [label]="'Existing Column' | translate" [type]="'radio'" [name]="'column'"
    (checked)="selectSaveType(calculationConfig.existingColumn)"
    [checkedData]="isColumnSaveType === calculationConfig.existingColumn"></app-ifp-check-box>

  @if (isColumnSaveType === calculationConfig.newColumn) {
  <div class="ifp-adv-tool__edit-outer">
    <p class="ifp-adv-tool__label">{{'New column Name' | translate}}</p>
    <input type="text" class="ifp-adv-tool__input" [formControlName]="'columnName'"
      [placeholder]="'Enter here' | translate">
  </div>
  }

  @if (isColumnSaveType === calculationConfig.existingColumn) {
  <div class="ifp-adv-tool__edit-outer">
    <p class="ifp-adv-tool__label">{{'Existing Columns' | translate}}</p>
    <app-ifp-dropdown class="ifp-adv-tool__dropdown" formControlName="columnName" [disableTranslation]="true"
       [dropDownItems]="columnList" [key]="'name'"
      [disableSingleValue]="true"></app-ifp-dropdown>
  </div>
  }


  <div class="ifp-adv-tool__edit-outer">
    <p class="ifp-adv-tool__label">{{'Data Type' | translate}}</p>
    <app-ifp-dropdown formControlName="dataType" class="ifp-adv-tool__dropdown"
      (dropDownItemClicked)="selectDataType($event)" [dropDownItems]="dataTypeList"
      [disableSingleValue]="true"></app-ifp-dropdown>
  </div>
@if (isEditCalculation) {
  <div class="ifp-adv-tool__update">
    <div class="ifp-adv-tool__action-inner">
      <ifp-button [label]="'cancel'" (ifpClick)="resetCalculation()" class="ifp-adv-tool__button ifp-adv-tool__action-btn" [buttonClass]="buttonClass.secondary"></ifp-button>
      <ifp-button [label]="'Update'" (ifpClick)="addCalculation()" class="ifp-adv-tool__button ifp-adv-tool__action-btn" [buttonClass]="calcForm.invalid && !formulaValidated() ? buttonClass.disabled : buttonClass.primary"></ifp-button>
    </div>
  </div>
} @else {
  <ifp-button class="ifp-adv-tool__button ifp-adv-calc__add-btn" [label]="'Add Calculation'"
  (ifpClick)="addCalculation()" [iconClass]="'ifp-icon-plus'"
  [buttonClass]="calcForm.invalid && !formulaValidated() ? buttonClass.disabled : buttonClass.secondary"></ifp-button>
}


</form>

@if (calculationList.length) {
<div class="ifp-adv-tool__content ifp-adv-tool__content--grey">
  <p class="ifp-adv-tool__sub-title">{{'Added Calculations' | translate}}</p>
  @for (calc of calculationList; let i=$index; track i) {
  <div class="ifp-adv-tool__card" (click)="updateCalculation(calc, i)">
    <div class="ifp-adv-tool__card-head">
      <p class="ifp-adv-tool__card-title">{{calc.name}}</p>
      <em class="ifp-icon ifp-icon-minus-round ifp-adv-tool__remove" (click)="removeCalc(i)"></em>
    </div>
    <div class="ifp-adv-calc__formula" [class]="'cm-s-'+configCodemirror.theme" [innerHTML]="calc.html"></div>
  </div>
  }
</div>
}
} @else {
  @if(errorMessageForTools) {
    {{errorMessageForTools}}
  } @else if(workFlowRunning()) {
<div class="ifp-center-loader">
  <app-ifp-spinner></app-ifp-spinner>
  <p>{{advanceMessage.workFlowRunning | translate}} </p>
</div>
} @else if (emptyConnection()){
<p class="ifp-adv-tool__error"><em class="ifp-icon ifp-icon-exclamation-round"></em>
  {{advanceMessage.emptyConnection | translate}}
</p>
} @else if (!sourceData()) {
<p class="ifp-adv-tool__error"><em class="ifp-icon ifp-icon-exclamation-round"></em>
  {{advanceMessage.sourceData | translate}}
</p>
} @else if(!currentConection()){
<p class="ifp-adv-tool__error"><em class="ifp-icon ifp-icon-exclamation-round"></em>
  {{advanceMessage.currentConnection | translate}}
</p>
}
@else {
<app-ifp-spinner></app-ifp-spinner>
}
}
