<ng-container *ngIf="status && !loader">
<div class="ifp-domain-detail">
  <div class="ifp-container">
    <app-ifp-breadcrumbs (itemClickEvent)="itemClick($event)" [pageData]="pageData"></app-ifp-breadcrumbs>
  </div>
  <div class="ifp-container ifp-module-spacing ">
    <div class="ifp-domain-detail__heading-wrapper">
      <ifp-img  class="ifp-domain-detail__heading-icon" [darkIcon]="darkIcon" [lightIcon]="lightIcon"></ifp-img>
      <h2 class="ifp-domain-detail__heading">{{heading}}</h2>

    </div>

  </div>



  <ifp-indicator-tab-api [tabIndex]="tabIndex" [domain]="this.heading" [selectedDomainSingles]="selectedDomainSingle" [isCompare]="true" [themeListSingles]="themeListSingle" [id]="id" [tab]="tab" [filter]="filter"  class="ifp-module-spacing ifp-d-block ifp-domain-detail__indicator-tab" (apply)="apply($event)"  [productSelect]="productData" [productSelectIndex]="productIndex" [domainDropdownOptions]="domainDropdownOptions" [selectedDropdownOption]="selectedDropdownOption" (dropdownChange)="onDropdownChange($event)" (dateRangeChange)="onDateRangeChange($event)"
  ></ifp-indicator-tab-api>
</div>

</ng-container>

<div class="ifp-container" *ngIf="!status || loader">
  <app-ifp-card-loader ></app-ifp-card-loader>
</div>


