@use "../../../../assets/ifp-styles/abstracts/index" as *;

.ifp-align-group {
  display: inline-flex;
  &__item {
    text-align: center;
    padding: $spacer-2 $spacer-5;
    border: 1px solid $ifp-color-grey-7;
    margin-inline-start: -1px;
    overflow: hidden;
    cursor: pointer;
    transition: 0.3s;
    &:first-child {
      border-radius: 10px 0 0 10px;
      margin-inline-start: $spacer-0;
    }
    &:last-child {
      border-radius: 0 10px 10px 0;
    }
    &:hover {
      background-color: $ifp-color-hover-blue;
      color: $ifp-color-white-global;
    }
    &--active {
      pointer-events: none;
      background-color: $ifp-color-blue-hover;
      color: $ifp-color-white-global;
    }
  }
}

:host-context([dir="rtl"]) {
  .ifp-align-group {
    &__item {
    &:first-child {
      border-radius: 0 10px 10px 0;
    }
    &:last-child {
      border-radius: 10px 0 0 10px;
    }
  }
  }
}
