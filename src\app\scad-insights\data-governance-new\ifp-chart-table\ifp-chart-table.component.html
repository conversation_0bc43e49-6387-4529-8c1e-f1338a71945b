<div class="ifp-chart-table">
  <div class="ifp-chart-table__header-sec">
    <h4 class="ifp-chart-table__title">{{ heading() | translate }}</h4>
    <div class="ifp-chart-table__btn" (click)="goToDetal()">
      <span class="ifp-chart-table__btn-text">{{ 'View in Detail' | translate }} </span> <em class="ifp-icon ifp-icon-right-arrow"></em>
    </div>
  </div>

  <div class="ifp-chart-table__chart-wrapper">
    <app-ifp-pie-chart [pieChartSeries]="chartData" [isDonut]="true" [height]="200" [innerSize]="70"
      [selectedPeriod]="'Use Case' | translate" [total]="true" [enableDataLabel]="false"
      [backGroundColor]="'transparent'" class="ifp-chart-table__chart" [width]="200"
      [isDynamicWidth]="false"></app-ifp-pie-chart>

    <div class="ifp-chart-table__status-list-wrapper">
      @for (item of chartData[0].data; track $index) {
      <div class="ifp-chart-table__status-item">
        <span class="ifp-chart-table__status-text"><span class="ifp-chart-table__dot" [style.background-color]="item.color"></span>{{ item.name | titlecase }}</span>
        <span class="ifp-chart-table__status-value">{{ item.y }}</span>
      </div>
      }
    </div>
  </div>
  <ul class="ifp-chart-table__tab">

    <li class="ifp-chart-table__tab-item" (click)="tabDetail('', -1)" [ngClass]="{'ifp-chart-table__tab-item--selected': selectedStatusIndex === -1 }"><span class="ifp-chart-table__tab-text">{{ 'All' | translate }}</span></li>

    @for (item of chartData[0].data; track $index) {
      <li class="ifp-chart-table__tab-item" (click)="tabDetail(item.name, $index)" [ngClass]="{'ifp-chart-table__tab-item--selected': selectedStatusIndex == $index}"><span class="ifp-chart-table__tab-text">{{ item.name | titlecase }}</span></li>
    }
  </ul>

  <div class="ifp-chart-table__table-wrapper">
    @if (tableHead && tableHead.length && tableData && tableData.length) {
    <ifp-data-table class="ifp-chart-table__table" #table [tableHead]="tableHead" [tableData]="tableData" [headerSettings]="headerSettings" (sortEvent)="onSortColumn($event)" [isTitleUpperCase]="true"></ifp-data-table>
    }
  </div>
</div>
