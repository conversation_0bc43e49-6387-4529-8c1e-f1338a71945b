import { buttonClass } from 'src/app/scad-insights/core/constants/button.constants';
import { After<PERSON>iewInit, Component, <PERSON><PERSON><PERSON>roy, OnInit, signal, ViewChild } from '@angular/core';
import { IfpBackButtonComponent } from '../../../ifp-widgets/atoms/ifp-back-button/ifp-back-button.component';
import { TranslateModule } from '@ngx-translate/core';
import { DatePipe, DecimalPipe, NgClass } from '@angular/common';
import { OutsideClickDirective } from 'src/app/scad-insights/core/directives/outsideClick.directive';
import { IfpInputEditComponent } from '../../../scad-insights/ifp-widgets/ifp-molecules/ifp-input-edit/ifp-input-edit.component';
import { IfpButtonComponent } from '../../../scad-insights/ifp-widgets/ifp-atoms/ifp-button/ifp-button.component';
import { IfpAnalyticLineChartComponent } from '../../../scad-insights/ifp-widgets/ifp-molecules/ifp-analytic-line-chart/ifp-analytic-line-chart.component';
import { IfpToolBarComponent } from '../../molecule/ifp-tool-bar/ifp-tool-bar.component';
import { ApiService } from 'src/app/scad-insights/core/services/api.service';
import { ActivatedRoute, Params, Router } from '@angular/router';
import { SubSink } from 'subsink';
import { autoMlApiEndpoints } from '../constants/auto-ml.constant';
import { IfpSpinnerComponent } from '../../../scad-insights/ifp-widgets/ifp-molecules/ifp-spinner/ifp-spinner.component';
import { ScenarioListAutoMl } from '../ifp-auto-ml-scenario-library/ifp-auto-ml-scenario-library.component';
import { ShortNumberPipe } from 'src/app/scad-insights/core/pipes/shortNumber.pipe';
import { ToasterService } from 'src/app/scad-insights/core/services/tooster/ToastrService.service';
import { IfpTooltipDirective } from 'src/app/scad-insights/core/directives/ifp-tooltip.directive';
import { IfpModalComponent } from '../../../scad-insights/ifp-widgets/ifp-organism/ifp-modal/ifp-modal.component';
import { IfpPrepPopupComponent } from '../../organism/ifp-prep-popup/ifp-prep-popup.component';
import { IfpScenarioDetailsPrintComponent } from './ifp-scenario-details-print/ifp-scenario-details-print.component';
import { UsageDashboardLogService } from 'src/app/scad-insights/core/services/usage-dashboard-log/usage-dashboard-log.service';
import { logType } from 'src/app/scad-insights/core/services/usage-dashboard-log/usage-dashboard.constants';

@Component({
    selector: 'ifp-scenario-details',
    imports: [
        IfpBackButtonComponent,
        TranslateModule,
        NgClass,
        OutsideClickDirective,
        IfpInputEditComponent,
        IfpButtonComponent,
        IfpAnalyticLineChartComponent,
        IfpToolBarComponent,
        IfpSpinnerComponent,
        ShortNumberPipe,
        DecimalPipe,
        IfpTooltipDirective,
        IfpModalComponent,
        IfpPrepPopupComponent,
        IfpScenarioDetailsPrintComponent
    ],
    templateUrl: './ifp-scenario-details.component.html',
    styleUrl: './ifp-scenario-details.component.scss'
})
export class IfpScenarioDetailsComponent implements OnInit, AfterViewInit, OnDestroy {
  @ViewChild('modalSuccess') popupModal!: IfpModalComponent;

  public scenarioList :ScenarioListAutoMl[] = [];
  public heading = '';
  public chartData: ChartDataScenario[] = [];

  public dropdownEnable = false;
  public edit = false;
  public buttonClass =buttonClass;
  public currentId = '';
  public yAxisLabel = signal('');
  public xAxisLabel = signal('');
  public loader =  signal(true);
  public subs = new SubSink();
  public driverValue: DriverScenario[] = [];
  public excelData: Record<string, string | number>[] = [];
  public isEditScenario = true;
  public isDescriptionEdit = false;
  public currentIndicatorValue = 0;
  public currentIndicatorForecastValue = 0;
  public currentIndicatorDate = '';
  public currentIndicatorForecastDate = '';
  public percentageDifference = 0;
  public percentageDifferenceAbs = 0;
  public dateFormat = 'd/M/y';
  public forecastHorizon = 0;
  public frequency = '';
  public desc: string | null = '';
  public modal = true;
  private sessionId!: string;

  constructor(private _autoMlService:ApiService, private _activatedRoute: ActivatedRoute,
    private _datePipe: DatePipe,
    private _router:Router,
    private _toasterService: ToasterService,
    private log: UsageDashboardLogService
  ) {
    // used to get scenario id
    this.subs.add(
      this._activatedRoute.queryParams.subscribe((event: Params) => {
        this.currentId = event['id'];
        this.modal =  event['modal'] ? false : true;
        if (event['editDisable']) {
          this.isEditScenario = false;
        }
      })
    );
  }

  ngOnInit(): void {
    if (this.sessionId) {
      this.log.logEnds(this.sessionId, this.log.currentTime );
    }
    this.sessionId = this.log.createUUid;
    this.log.logStart(this.sessionId, logType.autoMl, this.log.currentTime );
    this.loader.set(true);
    this.chartScenario();
    this.callScenarioList();
  }

  ngAfterViewInit(): void {
    if (this.isEditScenario && this.modal) {
      this._router.navigate(['/scenario-details'], { replaceUrl: true, queryParams: {modal: false, id: this.currentId} });
      this.popupModal.createElement();
    }
  }

  // runScenario while api call
  chartScenario(ColumnData:  {column: string, value: number }[]  = []) {
    this.subs.add(
      this._autoMlService.postMethodRequest(autoMlApiEndpoints.scenarioDetail + this.currentId +autoMlApiEndpoints.scenarioChart, {data: {driver: ColumnData}}, {}, true).subscribe((data: Scenario) => {

        const chartData: ChartDataScenario =  {
          'name': data.y_axis,
          'data': [


          ],
          'actualValue': [],
          'monthValues': [

          ],
          'yearValues': [

          ],
          'dashStyle': 'Solid',
          'type': 'line',
          'zIndex': 1,
          'identifier': data.y_axis,
          'lineWidth': 3,
          'color': '#3BD6AD',
          'marker': {
            'fillColor': '#3BD6AD',
            'lineWidth': 3,
            'lineColor': '#3BD6AD',
            'symbol': 'circle',
            'enabled': true
          }
        };
        const chartDataForecast: ChartDataScenario = {
          'name': `${data.y_axis}-forecast`,
          'data': [

          ],
          'monthValues': [

          ],
          'yearValues': [

          ],
          'actualValue': [],
          'type': 'line',
          'dashStyle': 'Dash',
          'identifier': data.y_axis,
          'zIndex': 1,
          'lineWidth': 3,
          'color': '#3BD6AD',
          'marker': {
            'enabled': true,
            'fillColor': '#3BD6AD',
            'lineWidth': 3,
            'lineColor': '#3BD6AD',
            'symbol': 'square'
          },
          'pointStart': 11
        };

        const chartDataForecaster: ChartDataScenario =   {
          'name': data.y_axis,
          'data': [

          ],
          'actualValue': [],
          'monthValues': [

          ],
          'yearValues': [

          ],
          'dashStyle': 'Dash',
          'type': 'arearange',
          'zIndex': 0,
          'identifier': data.y_axis,
          'lineWidth': 0,
          'color': '#3BD6AD',
          'marker': {
            'enabled': true,
            'symbol': 'square'
          },
          'zones': [
            {
              'value': 0,
              'color': '#3BD6AD'
            },
            {
              'color': '#3BD6AD'
            }
          ],
          'zoneAxis': 'x',
          'linkedTo': ':previous',
          'fillOpacity': 0.3,
          'pointStart': 11
        };
        this.excelData =[];
        data.data.forEach(dataValue => {

          this.excelData.push({
            [data.x_axis]: this._datePipe.transform(dataValue.date, this.dateFormat) ?? '',
            [data.y_axis]: dataValue.value
          });
          chartData.data.push([this.getConvertDate( dataValue.date), dataValue.value]);
        });
        const date = data.data[data.data.length-1].date;

        chartDataForecast.data.push([this.getConvertDate(date), data.data[data.data.length-1].value]);
        chartDataForecaster.data.push([this.getConvertDate(date), data.data[data.data.length-1].value, data.data[data.data.length-1].value]);
        data.forcast.forEach(dataValue => {
          const convertedDate = this.getConvertDate(dataValue.date);
          this.excelData.push({
            [data.x_axis]: this._datePipe.transform(dataValue.date, this.dateFormat) ?? '',
            [data.y_axis]: dataValue.forcast
          });
          // this.excelData.push({
          //   [data.x_axis]: this._datePipe.transform(dataValue.date, this.dateFormat) ?? '',
          //   [data.y_axis]: dataValue.forcast_ll
          // });
          // this.excelData.push({
          //   [data.x_axis]: this._datePipe.transform(dataValue.date, this.dateFormat) ?? '',
          //   [data.y_axis]: dataValue.forcast_ul
          // });
          chartDataForecast.data.push([convertedDate, dataValue.forcast]);
          chartDataForecaster.data.push([convertedDate, dataValue.forcast_ll, dataValue.forcast_ul]);
          const lastData =data.data[data.data.length-1];
          const  firstForecast =data.forcast?.[0];
          this. currentIndicatorValue = lastData.value;
          this. currentIndicatorForecastValue = firstForecast.forcast;
          this. currentIndicatorDate = lastData.date;
          this. currentIndicatorForecastDate = firstForecast.date;
          this. percentageDifference = ( this.currentIndicatorForecastValue - this.currentIndicatorValue)*100/this.currentIndicatorValue;
          this.percentageDifferenceAbs = Math.abs(this. percentageDifference);
        });
        this.chartData = [
          chartData,
          chartDataForecast,
          chartDataForecaster
        ];
        this.yAxisLabel.set( data.y_axis);
        this.xAxisLabel.set( data.x_axis);
        this.driverValue = data.drivers;
        this.heading = data.name;
        this.frequency = data.freequency;
        this.forecastHorizon = data.workflow_meta.frequency_range;
        this.loader.set(false);
        this.desc = data.description;
      })

    );
  }

  // get heading value while editing value
  validValueChange(event: string) {
    this.heading =event;
    this.saveScenarioApi({name: this.heading}, true);
    this.edit=false;
  }

  // get description while editing description
  validDescriptionValueChange(event: string) {
    this.desc =event;
    this.saveScenarioApi({description: this.desc}, false, true);
    this.isDescriptionEdit = false;
  }

  // reset driver value to 0
  resetToDefault() {
    this.chartScenario(this.setDriver(true));
  }

  // used to convert date to utc format
  getConvertDate(dateValue: string) {
    const date = new Date(dateValue);

    // Get the UTC timestamp
    return Date.UTC(date.getUTCFullYear(), date.getUTCMonth(), date.getUTCDate(),
      date.getUTCHours(), date.getUTCMinutes(), date.getUTCSeconds());
  }

  // scenario list dropdown
  dropdown() {
    this.dropdownEnable =  !this.dropdownEnable;
  }

  // toggle heading edit
  editHeading() {
    this.edit = true;
  }

  // cancel heading edit
  cancelEdit() {
    this.edit = false;
  }

  // scenario  dropdown outside click
  outsideClick() {
    this.dropdownEnable = false;
  }

  // event will fire when driver change
  driverChange(event: {event:{value: { id: string; value:string, index: number }, item: DriverScenario, _driveIndex: number}}) {
    event.event.item.options.forEach(data=> data.isSelected = false);
    event.event.item.options[event.event.value.index].isSelected = true;

    this.chartScenario(this.setDriver());
  }

  // call scenario List
  callScenarioList() {
    this.subs.add(
      this._autoMlService.getMethodRequest(autoMlApiEndpoints.scenarioList, {limit: 3, offset: 0}, true ).subscribe((data: {results: ScenarioListAutoMl[]}) => {
        this.scenarioList = data.results;
      })
    );
  }

  // edit description
  editHeadingDescription() {
    this.isDescriptionEdit = true;
  }

  // cancel description edit
  descriptionEditCancel() {
    this.isDescriptionEdit = false;
  }

  // used to got to particular page
  goToScenario(item: ScenarioListAutoMl) {
    this.currentId  = item.object_id;
    this._router.navigateByUrl(`/scenario-details?id=${item.object_id}&editDisable=true`);
    this.chartScenario();
    this.callScenarioList();
  }

  closeSuccessModal() {
    this.popupModal.removeModal();
  }

  // edit scenario
  editScenario() {
    this.isEditScenario =true;
  }

  // go to my-scenarioPage
  viewAll() {
    this._router.navigateByUrl('/my-Scenarios');
  }


  // save scenario value
  saveScenario() {
    const data = {
      name: this.heading,
      settings: this.driverValue,
      description: this.desc,
      value: this.currentIndicatorValue
    };
    this.saveScenarioApi(data);
  }

  saveScenarioApi(data:  {
    name?: string;
    settings?: DriverScenario[];
    description?: string | null;
    value?: number;
}, saveName : boolean = false, saveDescription : boolean = false) {
    this.subs.add(
      this._autoMlService.patchMethodRequest(autoMlApiEndpoints.scenarioSave + this.currentId +autoMlApiEndpoints.scenarioUpdate, data,  true).subscribe(() => {
        if (saveName) {
          this._toasterService.success('Scenario name saved successfully!');
        } else if (saveDescription) {
          this._toasterService.success('Scenario description saved successfully!');
        } else {
          this._toasterService.success('Scenario created successfully!');
          this._router.navigateByUrl('/my-Scenarios');
        }
        this.callScenarioList();
      })
    );
  }

  // event trigger when save driver
  driverUpdated(event: DriverScenario[] ) {
    this.driverValue = event;

    this.resetToDefault();
  }

  // used to get driver data in api format
  setDriver(defaultValue: boolean = false) {
    const columnData: {column: string, value: number }[] = [];
    this.driverValue.forEach(data => {
      data.options.forEach( options => {
        if (defaultValue) {
          if (options.isSelected && options.value !==0) {
            options.isSelected = false;
          } else if (options.value ===0) {
            options.isSelected = true;
          }
        }

        if (options.isSelected && data.selected) {
          columnData.push( {column: data.label, value: options.value });
        }
      }
      );
    });
    return columnData;
  }

  ngOnDestroy(): void {
    if (this.sessionId) {
      this.log.logEnds(this.sessionId, this.log.currentTime );
    }
  }

}
export interface Scenario {
  data: Datum[];
  freequency: string;
  forcast: Forcast[];
  drivers: DriverScenario[];
  x_axis: string;
  y_axis: string;
  name: string;
  workflow_meta: WorkflowMeta,
  description: string | null
}

interface WorkflowMeta {
  frequency: string;
  frequency_range: number;
  target_variable: string;
  time_stamp_variable: string;
}
export interface DriverScenario {
  label: string;
  options: DriverOption[];
  value: string;
  selected: boolean;
}

export interface DriverOption {
  label: string;
  value: number;
  isSelected: boolean;
}

interface Forcast {
  id: number;
  date: string;
  forcast: number;
  forcast_ll: number;
  forcast_ul: number;
}

interface Datum {
  value: number;
  date: string;
}

export interface  ChartDataScenario {
  name: string;
  data: any[];
  actualValue: any[];
  monthValues: any[];
  yearValues: any[];
  dashStyle: string;
  type: string;
  zIndex: number;
  identifier: string;
  lineWidth: number;
  color: string;
  marker: Marker;
  visible?: boolean;
  pointStart?: number;
  zones?: Zone[];
  zoneAxis?: string;
  linkedTo?: string;
  fillOpacity?: number;
  legendPositions? : {
    isLegend:  boolean;
  }
}

interface Zone {
  value?: number;
  color: string;
}

interface Marker {
  fillColor?: string;
  lineWidth?: number;
  lineColor?: string;
  symbol: string;
  enabled: boolean;
  radius?: number;
}
