import { Component, input, OnChanges, output } from '@angular/core';
import { AiButtonComponent } from "../../../ifp-widgets/atoms/ai-button/ai-button.component";
import { TranslateModule } from '@ngx-translate/core';
import { DecimalPipe, NgClass } from '@angular/common';
import { InsightComparison, InsightPercentagechange } from '../interface/insights.interface';

@Component({
  selector: 'ifp-ai-insight-compare-module',
  imports: [AiButtonComponent, TranslateModule, NgClass,DecimalPipe],
  templateUrl: './ai-insight-compare-module.component.html',
  styleUrl: './ai-insight-compare-module.component.scss'
})
export class AiInsightCompareModuleComponent implements OnChanges {
  public edit = output();
  public heading = input<string>('Experimental Statistics Analysis');
  public headerEnable = input(true)
  public currentTheme = input('');
  public compairData = input<InsightComparison>()
  public header = [
    "Indicator",
    "Latest Value",
    "Previous Value",
    "Percentage Change",
];
  public preview = input(false);
  public keys:  string[] = [];
  ngOnChanges(): void {
      this.keys = ['indicator', 'latest_value', 'previous_value', 'percentage_change'];
  }

}
