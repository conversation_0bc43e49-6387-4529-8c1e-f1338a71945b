

export interface DomainIconList {
  id: number;
  name: string;
  url: string;
  light_icon: string;
  icon: string;
}
export interface DomainDetails {
  domain: Domain;
  data:  DomainIndicatorData[];
  popularData: DomainPopularDatum[];
  nodeData: any;
}


export interface DomainDetailsNew {classification: any[]; filter: any; id :number; domain?:any}
export interface DomainPopularDatum {
  name: string;
  nodes: Node[];
}

export interface DomainIndicatorData {
  name: string;
  count: number;
  dark_icon:string;
  light_icon:string;
  nodes:  Nodes2[];
  nodesData: any;
  showTree:boolean;
  key: string;
}

interface Nodes2 {
  content_type: string;
  domains?: any;
  domain?: string;
  subtheme?: any;
  product?: any;
  category: any;
  screener?: any;
  content_classification: string;
  id: string;
  name: string;
  subthemes?: Subtheme[];
  theme?: any;
  title: string;
  subtitle: string;
  classification: string;
}

interface Subtheme {
  name: string;
  products: DomainDetailProduct[];
}

export interface DomainDetailProduct {
  name: string;
  nodes: Node[];
}

interface Node {
  id: string;
  content_type: string;
  category: any;
  name: string;
  products: DomainDetailProduct[];
  title: string;
  subtitle: string;
  domains?: any;
  domain?: string;
  theme?: any;
  subtheme?: any;
  product?: any;
  classification: string;
  content_classification: string;
}

interface Domain {
  id: string;
  name: string;
  icon: string;
  light_icon: string;
  dark_icon?: string;

  count?: number;
  nodes?: Node[];
  showTree?: boolean;
  nodesData?: any;
  key?: string;
}

export interface AnalyticalAppList {
  id: string;
  name: string;
  icon: string;
  route: string;
  node_count: number;
  isSelected: boolean;
  nodes: (AppNode | AppNodes2)[];
}

interface AppNodes2 {
  id: string;
  content_type: string;
  title: string;
  subtitle: string;
  domains: string[];
  domain: string;
  theme?: any;
  subtheme?: any;
  product?: any;
  classification: string;
}

interface AppNode {
  id: string;
  content_type: string;
  title: string;
  subtitle: string;
  domains: string[];
  domain: string;
  theme: string;
  subtheme?: any;
  product?: any;
  category: Category;
  classification: string;
}

type Category = Record<string, CategoryDetail>;

interface CategoryDetail {
  id: string;
  name: string;
  pageType: string;
  icon_path: string;
  light_icon_path: string;
  categorySectiontype: string;
}
export interface NewDomainClassification {
  classification: DomainClassification[];
  domain: DomainNameIcon;
}

interface DomainNameIcon {
  id: string;
  name: string;
  dark_icon: string;
  light_icon: string;
}


export interface DomainClassification {
  id: string;
  name: string;
  light_icon: string;
  dark_icon: string;
  count: string;
  key: string;

  nodeCount: number;
}

export interface DomainNodes {
  results: Result[];
  total_count: number;
  products: Product[];
}

interface Product {
  pid: string;
  count: string;
  title: string;
}

interface Result {
  id: string;
  title: string;
  content_type: string;
  app_type?: any;
  category: CategoryV2;
}

interface CategoryV2 {
  id: string;
  name: string;
  dark_icon: string;
  light_icon: string;
}


export interface NewsLetter {
  content_classification: string;
  id: string;
  publicationAttachment: string;
  publicationDate: string;
  subTitle: string;
  title: string;
  content_classification_key?:string;
  content_type?:string;
  isExternal?: boolean;
  image?: string;
}

export interface NewsLetterObj {
  iconClass: string,
  id: string,
  isSelected: false,
  name: string,
  nodeCount: number,
  route: string,
  showTree: boolean,
  nodes: NewsLetter[],
  isCustom: boolean
}
