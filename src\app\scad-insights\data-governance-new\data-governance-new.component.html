<div class="ifp-container ifp-d-govenance">
  <div class="ifp-d-govenance__breadcrumbs">
    <app-ifp-breadcrumbs [pageData]="pageData"></app-ifp-breadcrumbs>
  </div>

  <div class="ifp-d-govenance__header-wrapper">
    <div class="ifp-d-govenance__header ifp-container ifp-container--small">
      <h1 class="ifp-d-govenance__title">{{ 'Data Governance Console' | translate }}</h1>
      <p class="ifp-d-govenance__description">{{ 'Provides a centralized platform for managing data quality, security, and compliance. It ensures controlled access, policy enforcement, and seamless monitoring of data integrity across the organization.' | translate}}</p>
    </div>

   <app-ifp-tab [inverse]="false" [classification]="false" [tabData]="tab" (selectedTabEvent)="tabClick($event)"
    [selectedTab]="selectedTab.index" [tooltipDisabled]="true" [hideCount]="true" class="ifp-d-govenance__tab" ></app-ifp-tab>
  </div>

  @if (indicatorsOverview.indicators && indicatorsOverview.indicators.length && selectedTab.event?.value !=
  tabKeys.calender) {
  <div class="ifp-d-govenance__values-wrapper">
    <ifp-data-tree-diagram [showPercentage]="selectedTab.event.value != tabKeys.home " [title]="overviewTitle" [totalCount]="overviewTotal"
      [treeData]="indicatorsOverview.indicators"
      [ngClass]="{'ifp-d-govenance__values-wrapper--radius': selectedTab.event.value !== tabKeys.home}"
      [showPercentage]="false"></ifp-data-tree-diagram>
    @if (indicatorsOverview.products() && indicatorsOverview.products().length) {
    <div class="ifp-d-govenance__usage-wrapper">
      <h2 class="ifp-d-govenance__usage-title">{{ 'Product List' | translate }}</h2>
      <div class="ifp-d-govenance__usage">
        @for (card of indicatorsOverview.products(); track $index) {
        <ifp-data-governace-value-card [icon]="card.icon" [title]="card.name" [value]="card.value"
          class="ifp-d-govenance__usage-card"></ifp-data-governace-value-card>
        }
      </div>
    </div>
    }
  </div>
  }

@if (selectedTab.event.value == tabKeys.home) {
  <div class="ifp-d-govenance__chart-table-wrapper">
    @if (detailTableData() && detailTableData().chartData?.length) {
    <ifp-chart-table class="ifp-d-govenance__chart-table" [heading]="'Statistical Product Status'" [chartData]="detailTableData().chartData"
    [tableData]="detailTableData().tableData" [tableHead]="detailTableData().tableHead" (detailClicked)="openDetail($event, tabKeys.use_case)" (sortColumn)="sortDetailTableData($event, true)" (tabClicked)="filterData($event, tabKeys.use_case)" [selectedStatus]="selectedUseCaseStatus"></ifp-chart-table>
    }

    @if (statisticalValuesTableData() && statisticalValuesTableData().chartData?.length) {
      <ifp-chart-table class="ifp-d-govenance__chart-table" [heading]="'Bayaan Statistical Value Status'" [chartData]="statisticalValuesTableData().chartData"
      [tableData]="statisticalValuesTableData().tableData" [tableHead]="statisticalValuesTableData().tableHead" (detailClicked)="openDetail($event, tabKeys.bayaan_svs)" (sortColumn)="sortStatisticalValuesTableData($event, true)" (tabClicked)="filterData($event, tabKeys.bayaan_svs)" [selectedStatus]="selectedUseCaseStatus"></ifp-chart-table>
      }
  </div>
}

  @if (comparisonChartData()  && selectedTab.event.value == tabKeys.statistical_indicator) {
    <div class="ifp-d-govenance__stat-diagram">
    <ifp-statistical-indicators class="ifp-d-govenance__sv-chart" [data]="comparisonChartData()"></ifp-statistical-indicators>
  </div>
  }




  <!-- tab details  -->

  @if (selectedTab.event?.value != tabKeys.home && selectedTab.event?.value != tabKeys.calender) {
  <div class="ifp-d-govenance__tab-wrapper">
    <div class="ifp-d-govenance__table-header">
      <h2 class="ifp-d-govenance__tab-view-header">{{selectedTab.event?.tabTitle}}</h2>
      <ifp-icon-button class="ifp-d-govenance__download-btn" [iconClass]="'ifp-icon-download'" [tooltip]="'Download'" (btnClick)="onExportData()"></ifp-icon-button>
    </div>

    <div class="ifp-d-govenance__filter-wrapper">
      <div class="ifp-d-govenance__filter-grp">
        @if(selectedTab.event?.value === tabKeys.use_case){
        <div class="ifp-d-govenance__search-wrapper">
          <p class="ifp-d-govenance__search-title">{{'Statistical Product Name'}}</p>
          <ifp-search class="ifp-d-govenance__search" [isKeypress]="true" (searchEvent)="onSearch($event)" [boxType]="true"
            [bgGrey]="true"></ifp-search>
        </div>
        }
        @if(selectedTab.event?.value != tabKeys.statistical_indicator){
        @for (filter of filters(); track $index) {
        @if (filter.path == 'search') {
        <div class="ifp-d-govenance__search-wrapper">
          <p class="ifp-d-govenance__search-title">{{filter.name}}</p>
          <ifp-search class="ifp-d-govenance__search" [isKeypress]="true" (searchEvent)="onSearch($event)" [boxType]="true"
            [bgGrey]="true"></ifp-search>
        </div>
        } @else {
        <app-ifp-dropdown [key]="'name'" class="ifp-d-govenance__filter-dropdown" [dropDownItems]="filter.options"
          (dropDownItemClicked)="selectFilter($event, filter.path)" [title]="filter.name"
          [showTitle]="true" [searchEnable]="true"></app-ifp-dropdown>
        }
        }
        } @else {
          <div class="ifp-d-govenance__search-wrapper">
            <p class="ifp-d-govenance__search-title">{{'Stat Value Name or SV ID' | translate}}</p>
            <ifp-search class="ifp-d-govenance__search" [isKeypress]="true" (searchEvent)="onSearch($event)" [boxType]="true"
              [bgGrey]="true"></ifp-search>
          </div>

          <app-ifp-dropdown [key]="'name'" class="ifp-d-govenance__filter-dropdown" [dropDownItems]="domainOpts"
          (dropDownItemClicked)="selectHirarchyFilter($event, 'domain')" [title]="'Domain'"
          [showTitle]="true" [searchEnable]="true"></app-ifp-dropdown>

          <app-ifp-dropdown [key]="'name'" class="ifp-d-govenance__filter-dropdown" [dropDownItems]="themeOpts"
          (dropDownItemClicked)="selectHirarchyFilter($event, 'theme')" [title]="'Theme'"
          [showTitle]="true" [searchEnable]="true"></app-ifp-dropdown>

          <app-ifp-dropdown [key]="'name'" class="ifp-d-govenance__filter-dropdown" [dropDownItems]="subThemeOpts"
          (dropDownItemClicked)="selectHirarchyFilter($event, 'subTheme')" [title]="'Sub Theme'"
          [showTitle]="true" [searchEnable]="true"></app-ifp-dropdown>

          <app-ifp-dropdown [key]="'name'" class="ifp-d-govenance__filter-dropdown" [dropDownItems]="productOpts"
          (dropDownItemClicked)="selectHirarchyFilter($event, 'product')" [title]="'Products'"
          [showTitle]="true" [searchEnable]="true"></app-ifp-dropdown>

          <app-ifp-dropdown [key]="'name'" class="ifp-d-govenance__filter-dropdown" [dropDownItems]="svStatusOpts"
          (dropDownItemClicked)="selectHirarchyFilter($event, 'status')" [title]="'Status'"
          [showTitle]="true" [searchEnable]="true"
           ></app-ifp-dropdown>
        }

        <ifp-panel-dropdown class="ifp-d-govenance__filter-dropdown ifp-d-govenance__filter-dropdown--cols" [multiSelect]="true"
        [enableSearch]="true"
        [isBoxType]="true"
        [options]="detailTableColumns()"
        [changeCheckedDataValue]="true"
        (multiSelected)="selectDetailColumns($event)"
        [key]="'value'"
        [label]="'Columns'"></ifp-panel-dropdown>
      </div>
    </div>

    <div class="ifp-d-govenance__detail-table-wrapper">
      <ifp-data-table [tableHead]="detailTableData().tableHead" [loader]="tableLoader" [tableData]="detailTableData().tableData" [headerSettings]="detailTableData().headerSettings" [isTitleUpperCase]="true" (sortEvent)="sortDetailTableData($event, false)"
        class="ifp-d-govenance__detail-table"></ifp-data-table>
    </div>
    @if (detailTableData().tableData && detailTableData().tableData.length) {
    <div class="ifp-d-govenance__pagination">
      <app-pagination [offset]="offset" [limit]="perPage" [size]="tabDeatilData().totalCount"
        (pageChange)="onPageChange($event)" (limitChange)="limitChanged($event)"></app-pagination>
    </div>
    }
  </div>
  } @else if (selectedTab.event?.value == tabKeys.calender ) {
  <ifp-calender (viewDetails)="viewDateDetails($event)" (changeMonth)="getCalendarDetails($event)"
    [eventDetails]="eventDetails()"></ifp-calender>
  }
</div>

<app-ifp-modal #viewEventDetailsModal [overlayType]="'transparent'">
  <ifp-data-gov-event-detail-modal (closeModalClick)="onCloseEventDetails()" class="ifp-d-govenance__event-detail-modal"
  [dayDetail]="dayDetail()" (sortClicked)="sortData($event)" [date]="selectedDateDetail().isEvent?.date ?? ''"
  (statusChanged)="statusChanged($event)" [statusList]="statusOptions" [totalDataCount]="totalDetailEntry"
  (pageSettingChanged)="pageSettingChanged($event)" [offset]="detailPageSettings.offset"
  [perPage]="detailPageSettings.limit" ></ifp-data-gov-event-detail-modal>
</app-ifp-modal>
