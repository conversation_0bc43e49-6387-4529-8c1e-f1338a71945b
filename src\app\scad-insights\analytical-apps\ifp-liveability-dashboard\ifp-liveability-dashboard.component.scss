@use '../../../../assets/ifp-styles/abstracts' as *;


.ifp-lvda {
  margin: $spacer-5 $spacer-0;

  .ifp-node {
    &__title {
      margin-bottom: $spacer-3;
    }
  }

  &__description {
    margin-bottom: $spacer-5;
  }

  &__title {
    font-size: $ifp-fs-6;
    font-weight: $fw-bold;
  }

  &__dropdown-filters {
    margin-top: $spacer-5;
    justify-content: space-between;
    display: flex;
    align-items: flex-end;
  }

  &__rating-outer {
    border: 1px solid $ifp-color-grey-7;
    border-radius: 5px;
    background-color: $ifp-color-white;
  }

  &__rating {
    display: flex;
    padding: $spacer-2 $spacer-3;
  }

  &__dropdown {
    margin-right: $spacer-4;

    &:last-child {
      margin-right: $spacer-0;
    }
  }

  &__drop-set {
    display: flex;
  }

  &__htab,
  &__cnt-wrap {
    margin: $spacer-0 $spacer-2;
  }

  &__htab {
    width: calc(25% - 16px);
  }

  &__dashboard-card {
    display: flex;
    width: calc(33.33% - 16px);
    margin: $spacer-2;

    app-ifp-dashboard-card {
      display: flex;
      width: 100%;
    }
  }

  &__dashboard-card-exp {
    width: calc(66.66% - 16px);
  }

  &__cnt-wrap {
    width: calc(75% - 16px);
  }

  &__horizontal-tabs {
    margin-top: $spacer-3;
    display: flex;
    margin: $spacer-3 (
      -$spacer-2) $spacer-0;

    &--full {
      .ifp-lvda {
        &__htab {
          width: 25%;
        }

        &__dashboard-card {
          width: calc(25% - 16px
          );
      }

      &__dashboard-card-exp {
        width: calc(50% - 16px);
      }

      &__cnt-wrap {
        width: calc(100% + 16px);
      }
    }
  }
}

&__dashboard-card-wrap {
  display: flex;
  flex-wrap: wrap;
  margin: $spacer-0 (
    -$spacer-2
  );
}

&__rating-point {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  display: inline-block;
}

&__rating-wrapper {
  text-align: center;
  margin: $spacer-0 $spacer-3;
}

&__rating-value {
  font-size: 1.3rem;
}

&__composition-card {
  width: 100%;
  background-color: $ifp-color-white;
  padding: $spacer-4;
  border-radius: 13px;
  border: solid $ifp-color-grey-7 1px;
  margin: $spacer-2;
}

&__composition-head {
  font-size: $ifp-fs-2;
  font-weight: $fw-bold;
  margin-bottom: $spacer-3;
  font-size: 1.4rem;
}

&__composition-desc {
  margin-bottom: $spacer-3;
}

&__composition-wrapper {
  display: flex;
  flex-wrap: wrap;
  margin: $spacer-0 (
    -$spacer-2
  );
}

&__composition-item {
  width: calc(50% - 16px);
  margin: $spacer-2;
}

&__composition-bottom {
  margin-top: $spacer-3;
  display: flex;
  justify-content: space-between;
}

&__composition-source {
  color: $ifp-color-red;
}

&__composition-note {
  text-align: right;
  font-weight: 500;
  font-style: italic;
}

&__flex {
  display: flex;
  align-items: center;
  height: calc(100vh - 500px)
}

&__no-data {
  margin-top: $spacer-3;
}
}

:host ::ng-deep {
  .ifp-lvda {
    &__dropdown {
      .ifp-dropdown {
        min-width: 200px;
        max-width: 200px;
      }
    }

    &__composition-item {
      .highcharts-xaxis-labels text {
        min-width: 130px;
        max-width: 130px;
      }
    }

    .ifp-tab__item {
      padding: $spacer-2 $spacer-3;
    }
  }
}

@include mobile-tablet {
  .ifp-lvda {
    &__title {
      font-size: $ifp-fs-4;
    }
  }
}
