import { inject, Injectable } from '@angular/core';
import { HttpService } from './http/http.service';

@Injectable({
  providedIn: 'root'
})
export class IfpVisulizationBuilderService {

  private _http = inject(HttpService)

  constructor() { }

  getParams(params: any) {
    return params ? Object.entries(params).map(([key, value]) => `${key}=${value}`).join('&') : '';
  }

  getMethodRequest(url: string, params?: any) {
    const query = this.getParams(params);
    return this._http.get(`${url}${query === '' ? query : `?${query}`}`, true);
  }

  getMethodStreamRequest(url: string, params?: any) {
    const query = this.getParams(params);
    return this._http.getSream(`${url}${query === '' ? query : `?${query}`}`, true);
  }

  getDeleteRequest(url: string, params?: any) {
    const query = this.getParams(params);
    return this._http.delete(`${url}${query === '' ? query : `?${query}`}`, true);
  }

  postMethodRequest(url: string, data?: any) {
    return this._http.post(`${url}`, data, true);
  }

  postFormDataRequest(url: string, data: any) {
    const formData = new FormData();
    for (const key in data) {
      if (data.hasOwnProperty(key)) {
        const value = data[key];
        // If value is an object, stringify it
        if (typeof value === 'object' || Array.isArray(value)) {
          formData.append(key, JSON.stringify(value));
        } else {
          formData.append(key, value);
        }
      }
    }
    return this._http.postFormData(`${url}`, formData, true);
  }

  putMethodRequest(url: string, data?: any) {
    return this._http.put(`${url}`, data, true);
  }


  getDownloadRequest(url: string, params?: any) {
    const query = this.getParams(params);
    return this._http.fileDownloadWithHeader(`${url}${query === '' ? query : `?${query}`}`);
  }


}
