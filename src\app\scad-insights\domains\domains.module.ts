import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { DomainsRoutingModule } from './domains-routing.module';
import { DomainsComponent } from 'src/app/scad-insights/domains/domains/domains.component';
import { IfpImgComponent } from '../ifp-widgets/ifp-atoms/ifp-img/ifp-img.component';
import { TranslateModule } from '@ngx-translate/core';

@NgModule({
  declarations: [
  ],
  imports: [
    CommonModule,
    DomainsComponent,
    DomainsRoutingModule,
    TranslateModule,
    IfpImgComponent
  ]
})
export class DomainsModule { }
