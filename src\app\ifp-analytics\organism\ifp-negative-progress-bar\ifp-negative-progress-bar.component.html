
<div class="ifp-negative-progress">
  <div class="ifp-negative-progress__wrapper">

    <div class="ifp-negative-progress__bar-outer-left" >
      @if(0>value) {
        {{value | number:format}}
        <div [style.width]="(value*-1)*100+ '%'" class="ifp-negative-progress__bar" [style.background]="color">

        </div>
      }


    </div>
    <div class="ifp-negative-progress__bar-outer-right" >
      @if(0<=value) {
        <div class="ifp-negative-progress__bar" [style.width]="(value)*100 + '%'" [style.background]="color">

        </div>
       {{value}}
      }

    </div>
  </div>

</div>
