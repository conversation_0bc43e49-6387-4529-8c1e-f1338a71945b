import { Component } from '@angular/core';
import { Router, RouterModule } from '@angular/router';
import { TranslateModule } from '@ngx-translate/core';
import { buttonClass } from 'src/app/scad-insights/core/constants/button.constants';
import { IfpButtonComponent } from 'src/app/scad-insights/ifp-widgets/ifp-atoms/ifp-button/ifp-button.component';

@Component({
    selector: 'app-not-found',
    templateUrl: './not-found.component.html',
    styleUrls: ['./not-found.component.scss'],
    imports: [TranslateModule, IfpButtonComponent, RouterModule]
})
export class NotFoundComponent {
  public buttonClass = buttonClass;

  constructor (public router: Router) {}
}
