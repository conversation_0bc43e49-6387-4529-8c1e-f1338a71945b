import { AfterViewInit, ChangeDetectorRef, Component, EventEmitter, Input, Output, Type, ViewChild, ViewContainerRef } from '@angular/core';
import { CustomEventEmit } from '../../data-prep/ifp-data-prep/interface/ifp-data-prep.interface';

@Component({
  selector: 'app-ifp-dynamic',
  templateUrl: './ifp-dynamic.component.html',
  styleUrls: ['./ifp-dynamic.component.scss'],
  standalone: true
})
export class IfpDynamicComponent implements AfterViewInit {
  @Input() inputValues!: any;
  @Input() outputs: string[] = [];
  @Input() row!: Record<string, () => string | string>;
  @Input() value!: string;
  @Input() headValue!: string;
  @Input() key!: string;
  @Input() columnSetting: any = {};
  @ViewChild('container', { read: ViewContainerRef }) private container!: ViewContainerRef;
  @Output() customEvent: EventEmitter<CustomEventEmit> = new EventEmitter<CustomEventEmit>();

  constructor(private _changeDetect: ChangeDetectorRef) { }


  ngAfterViewInit(): void {
    if (this.columnSetting.type !== 'custom') {
      return;
    }
    if (this.columnSetting.renderComponent) {
      this.container.clear();
      this.columnSetting.renderComponent().then(
        (module: Type<any>) => {
          const componentRef = this.container.createComponent(module);
          const inputKeys = this.objectKeys(this.inputValues);
          inputKeys.forEach(element => {
            componentRef.instance['value'] = this.value;
            componentRef.instance[element] = this.inputValues[element];
          });
          if (this.outputs?.length > 0) {
            this.outputs.forEach(output => {
              if (componentRef.instance[output]) {
                componentRef.instance[output].subscribe((val: any) => {
                  this.customEvent.emit({ value: val, event: output });
                }
                );
              }
            });

          }
          this._changeDetect.detectChanges();
        }
      );

    }
  }

  objectKeys(obj: any): any[] {
    return obj ? Object.keys(obj) : [];
  }


}
