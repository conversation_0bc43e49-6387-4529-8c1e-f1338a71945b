
export interface IndicatorList {
data: IndicatorListData;
status: {
  status?:  boolean,
  errorMessage?: '',
  loader?:  boolean,
}
default: any[];
defaultValue:any[];
periodFilter: any[];
initialData:any[];
overview: boolean;
isRender?:any;
coiData:any
}



export interface IndicatorListData {
  id: string;
  component_title: string;
  component_subtitle: string;
  domains: string[];
  type: string;
  note: string;
  narrative: string;
  search_tags: any[];
  attachment: string;
  Indicator: string;
  policy_guide: string;
  enableCompare: boolean;
  compare_data: string;
  filterPanel?:  any;
  insights: string;
  enablePointToggle: boolean;
  maxPointLimit: string;
  enableVisualizationTypes: boolean;
  enableUploadAndCompare: boolean;
  showInsights: string;
  language: string;
  indicatorTools: IndicatorTool[] | any;
  indicatorFilters: IndicatorFilter[] | any;
  indicatorDrivers?: any;
  indicatorValues: IndicatorValues| any;
  indicatorVisualizations: IndicatorVisualizations | any;
  isFavorite: string;
  isUploadCompareData: boolean;
  benchmarks: any[];
  visualizations:any;
}
interface IndicatorVisualizations {
  visualizationsMeta: VisualizationsMeta[];
  visualizationDefault: string;
}

interface VisualizationsMeta {
  id: string;
  type: string;
  dimension: boolean;
  dimensionColumn: string;
  viewName: string;
  PARAMETER_COMBO_ID: string;
  filterBy: FilterBy;
  seriesMeta: SeriesMeta[];
  markersMeta: MarkersMeta[];
  showInterval: boolean;
  showQuarterlyIntervals: boolean;
  showPointLabels: boolean;
  xAxisLabel?: any;
  yAxisLabel?: any;
  yAxisExtraStepMin: number;
  yAxisExtraStepMax: number;
  xAxisFormat: string;
  yAxisFormat: string;
  tooltipTitleFormat: string;
  tooltipValueFormat: string;
  enableVisualizationCharts: EnableVisualizationChart[];
  minLimitYAxis: string;
}

interface EnableVisualizationChart {
  label: string;
  id: string;
  default: boolean;
}

interface MarkersMeta {
  id: string;
  color: string;
  type: string;
  labelText: string;
  axis: string;
  accessor: XAccessor;
  data: Data;
}

interface Data {
  DATE: string;
}

interface SeriesMeta {
  id: string;
  label: string;
  color: string;
  tableDefinition?: TableDefinition[];
  type: string;
  dimensionValue: string;
  xAccessor: XAccessor;
  yAccessor: YAccessor;
  data: Datum[];
  yMax: number;
  yMin: number;
  xMin: string;
  xMax: string;
}

interface Datum {
  INDICATOR_ID: string;
  PARAMETER_COMBO_ID: string;
  RUN_SEQ_ID: number;
  RUN_DT: string;
  VALUE: number;
  UNIT: string;
  OBS_DT: string;
  OPT: number;
  TYPE: string;
  OIL_NONOIL: string;
  SECTOR: string;
  INDUSTRY: string;
  YEAR: string;
  VALUE_LL?: number;
  VALUE_UL?: number;
}

interface YAccessor {
  type: string;
  path: string;
}

interface XAccessor {
  type: string;
  path: string;
  specifier: string;
}

interface TableDefinition {
  label: string;
  path: string;
}

interface FilterBy {
  SECTOR: string;
  INDUSTRY: string;
  OIL_NONOIL: string;
}

interface IndicatorValues {
  valuesMeta: ValuesMeta[];
}

interface ValuesMeta {
  id: string;
  title: string;
  type: string;
  valueFormat: string;
  color: string;
  templateFormat: string;
  viewName: string;
  dimension: Dimension;
  dateStart: string;
  value: string;
  unit?: string;
  period?: number;
  dateEnd?: string;
}

interface Dimension {
  SECTOR: string;
  INDUSTRY: string;
  OIL_NONOIL: string;
  TYPE?: string;
  PARAMETER_COMBO_ID?: string;
}

interface IndicatorFilter {
  id: string;
  options: Option[];
}

interface Option {
  id: string;
  label: string;
  value?: number;
  unit?: string;
  isSelected?: boolean;
}



interface IndicatorTool {
  id: string;
  disabled?: boolean;
  label: string;
}

export interface CompareApp {
  id:string;
  status: boolean;
  loader: boolean;
  data: any;
  errorMessage: any;
}

export interface Security {
  id: string;
  name: string;
  description?: string;
  color?: string;
  label: string;
}
