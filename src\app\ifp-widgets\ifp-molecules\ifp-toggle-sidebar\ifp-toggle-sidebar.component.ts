import { NgClass } from '@angular/common';
import { Component, ElementRef, EventEmitter, Input, Output, ViewChild } from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';
import { IfpTooltipDirective } from 'src/app/scad-insights/core/directives/ifp-tooltip.directive';

@Component({
    selector: 'ifp-toggle-sidebar',
    imports: [IfpTooltipDirective, TranslateModule, NgClass],
    templateUrl: './ifp-toggle-sidebar.component.html',
    styleUrl: './ifp-toggle-sidebar.component.scss'
})
export class IfpToggleSidebarComponent {
  @ViewChild('siderWidth') siderWidth!:ElementRef;
  @Output() siderWidthEvent = new EventEmitter<ElementRef>();

  @Output() toggleToolbar: EventEmitter<boolean> = new EventEmitter<boolean>();
  @Input() isToolbarExpanded: boolean = window.innerWidth > 1280;
  @Input() submenu: boolean = false;


  expandOrCollapseToolbar() {
    this.isToolbarExpanded = !this.isToolbarExpanded;
    this.toggleToolbar.emit(this.isToolbarExpanded);
    this.siderWidthEvent.emit(this.siderWidth);
  }

  get getElementRef ():ElementRef {
    return this.siderWidth;
  }
}
