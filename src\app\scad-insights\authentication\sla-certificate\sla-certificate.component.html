<div class="ifp-sla">



  <div class="ifp-sla__card">
    <img src="../../../assets/images/unauthorized-vector.svg" alt="sla" class="ifp-sla__icon" *ngIf="isGroup; else img">

    <ng-template #img>

      <svg width="284" height="142" viewBox="0 0 284 142" fill="none" xmlns="http://www.w3.org/2000/svg"
        class="ifp-sla__icon">
        <rect x="284" y="97.5" width="2.00001" height="283" rx="1.00001" transform="rotate(90 284 97.5)"
          fill="#8793FF" />
        <path d="M1 68H277.5" stroke="#CACBCA" stroke-linecap="round" stroke-linejoin="round" stroke-dasharray="2 7" />
        <path d="M1 39.5H277.5" stroke="#CACBCA" stroke-linecap="round" stroke-linejoin="round"
          stroke-dasharray="2 7" />
        <path d="M1 11H277.5" stroke="#CACBCA" stroke-linecap="round" stroke-linejoin="round" stroke-dasharray="2 7" />
        <g clip-path="url(#clip0_3679_82463)">
          <path d="M151 0H216L236 17.5V110H151V0Z" fill="#EBEEED" />
          <rect x="162" y="16" width="43" height="3" rx="1.5" fill="#C8D2CF" />
          <rect x="162" y="28" width="26" height="3" rx="1.5" fill="#C8D2CF" />
          <rect x="162" y="49" width="60" height="3" rx="1.5" fill="#C8D2CF" />
          <rect x="162" y="73" width="60" height="3" rx="1.5" fill="#C8D2CF" />
          <rect x="162" y="61" width="60" height="3" rx="1.5" fill="#C8D2CF" />
          <rect x="162" y="85" width="60" height="3" rx="1.5" fill="#C8D2CF" />
          <path opacity="0.6" d="M236 17.5L216 0V17.5H236Z" fill="white" />
        </g>
        <circle cx="136.5" cy="85.5" r="45.5" fill="white" />
        <path
          d="M192 84.5C192 115.152 167.152 140 136.5 140C105.848 140 81 115.152 81 84.5C81 53.8482 105.848 29 136.5 29C167.152 29 192 53.8482 192 84.5ZM97.65 84.5C97.65 105.956 115.044 123.35 136.5 123.35C157.956 123.35 175.35 105.956 175.35 84.5C175.35 63.0437 157.956 45.65 136.5 45.65C115.044 45.65 97.65 63.0437 97.65 84.5Z"
          fill="#AFB7FF" />
        <path
          d="M192 84.5C192 74.5896 189.346 64.86 184.315 56.322C179.283 47.784 172.057 40.7489 163.387 35.9478L155.321 50.5135C161.39 53.8743 166.448 58.7988 169.97 64.7754C173.492 70.752 175.35 77.5627 175.35 84.5H192Z"
          fill="#FC6895" />
        <path
          d="M81 84.5C81 94.4104 83.6536 104.14 88.6852 112.678C93.7168 121.216 100.943 128.251 109.613 133.052L117.679 118.487C111.61 115.126 106.552 110.201 103.03 104.225C99.5075 98.248 97.65 91.4372 97.65 84.5H81Z"
          fill="#FC6895" />
        <path
          d="M191.814 89.0366C192.047 86.1969 192.061 83.3436 191.856 80.5018L175.249 81.7012C175.393 83.6905 175.383 85.6879 175.22 87.6756L191.814 89.0366Z"
          fill="#606FFF" />
        <path
          d="M81.0488 84.1734C81.1689 81.3095 81.5108 78.4593 82.0713 75.6482L98.3999 78.9037C98.0076 80.8715 97.7683 82.8667 97.6842 84.8714L81.0488 84.1734Z"
          fill="#606FFF" />
        <path
          d="M103.685 129.26C105.824 130.828 108.073 132.241 110.414 133.487L118.24 118.791C116.601 117.919 115.027 116.93 113.53 115.832L103.685 129.26Z"
          fill="#EB0048" />
        <path
          d="M165.263 37.0351C163.491 35.9609 161.659 34.9868 159.778 34.1175L152.794 49.2322C154.111 49.8408 155.393 50.5226 156.634 51.2745L165.263 37.0351Z"
          fill="#EB0048" />
        <rect x="134" y="60" width="5" height="29" rx="2.5" fill="#8793FF" />
        <rect x="161" y="84" width="5" height="27" rx="2.5" transform="rotate(90 161 84)" fill="#8793FF" />
        <defs>
          <clipPath id="clip0_3679_82463">
            <rect x="151" width="85" height="110" rx="10" fill="white" />
          </clipPath>
        </defs>
      </svg>
    </ng-template>

    <ng-container *ngIf="isGroup; else elseTemplate">

      @if (maintananceData && (maintananceData.title || maintananceData.message)) {
      <h3 class="ifp-sla__heading">{{maintananceData.title| translate}}</h3>
      <p class="ifp-sla__description">{{maintananceData.body| translate}} <a href="mailto:<EMAIL>"
          title="<EMAIL>" class="ifp-link">{{'<EMAIL>'}}</a></p>
      <p class="ifp-sla__duration"><span class="ifp-sla__time">{{maintananceData.from_date}}</span> <span
          class="ifp-sla__diff">{{'to'}}</span> <span class="ifp-sla__time">{{maintananceData.end_date}}</span></p>
      }
    </ng-container>
    <ng-template #elseTemplate>

      @if (maintananceData && (maintananceData.title || maintananceData.message)) {
      <h3 class="ifp-sla__heading">{{maintananceData.title | translate}}</h3>
      <p class="ifp-sla__description">{{maintananceData.message| translate}} <a href="mailto:<EMAIL>"
          title="<EMAIL>" class="ifp-link">{{'<EMAIL>'}}</a></p>
      <p class="ifp-sla__duration"><span class="ifp-sla__time">{{maintananceData.from_date}}</span> <span
          class="ifp-sla__diff">{{'to'}}</span> <span class="ifp-sla__time">{{maintananceData.end_date}}</span></p>
      }
    </ng-template>
  </div>
</div>
