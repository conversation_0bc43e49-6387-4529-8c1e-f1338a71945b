import { Component, input, output, signal } from '@angular/core';
import { FormControl, ReactiveFormsModule } from '@angular/forms';
import { provideNativeDateAdapter } from '@angular/material/core';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { TranslateModule } from '@ngx-translate/core';

@Component({
  selector: 'ifp-gen-ai-dashboard-date-picker',
  imports: [MatDatepickerModule, ReactiveFormsModule, TranslateModule],
  templateUrl: './gen-ai-dashboard-date-picker.component.html',
  styleUrl: './gen-ai-dashboard-date-picker.component.scss',
  providers: [provideNativeDateAdapter()]
})
export class GenAiDashboardDatePickerComponent {
  public date = output<{ startDate: Date | null; endDate: Date | null; }>();
  public startDate = input(new FormControl(new Date(new Date().setFullYear(new Date().getFullYear() - 1))));
  public endDate = input(new FormControl(new Date()));
  public maxDate = signal(new Date());

  ngOnChanges(): void {

  }

  dateChangeValue() {
    const today = new Date( this.startDate().value ?? '');
    // Add 365 days to the current date
    today.setDate(today.getDate() + 365);

    // Display the new date in YYYY-MM-DD format
    const futureDate = today.toISOString().split('T')[0];
    this.maxDate.set( new Date(futureDate ?? '') <  new Date() ?  new Date(futureDate ?? '')  : new Date());

  }

  dateChange(){
    if (this.endDate().value === null) {
      this.endDate().setValue(this.startDate().value);
    }
    this.date.emit({
      startDate: this.startDate().value,
      endDate: this.endDate().value
    });
  }



}
