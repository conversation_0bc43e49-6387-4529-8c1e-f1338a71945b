<div class="ifp-itr-card">
  <ifp-data-tool-accordian  (deleteValue)="deleteEvent()" (valueChange)="valueChange($event)" [isEdit]="true" [deleteIcon]="deleteIcon" class="ifp-itr-card__acc" [enableCheckBox]="false" [title]="chartTitle" [isAccordianExpanded]="isAccordianExpanded" (expandedEvent)="expandedEvent($event)">
    <div class="ifp-accord-box__content" >
      <ifp-auto-ml-chart-data [chartListValue]="true" class=" ifp-itr-card__content" [chartValueName]="chartValueName" [chartHeading]="chartHeading" [chartValue]="chartValue" [chartList]="chartList"></ifp-auto-ml-chart-data>

      <div  class="ifp-itr-card__sug-card">
        <div class="ifp-itr-card__sug-head">
          <em class="ifp-icon ifp-icon-bulb-off"></em>
          {{'Smart suggestions' | translate}}
        </div>

        <div class="ifp-itr-card__sug-text" [innerHTML]="aiText">
        </div>

      </div>
      <div  class="ifp-itr-card__selected-independent">
        {{'Selected Independent Variables' | translate}}
      </div>
      <div class="ifp-itr-card__badge-wrapper" >
        <div></div>
        @for (item of badgeData; track item) {
          <div class="ifp-itr-card__badge" [ngClass]="{'ifp-itr-card__badge--selected': item.variable_type ===selectedValues.selected , 'ifp-itr-card__badge--ignore':  item.variable_type ===selectedValues.ignored , 'ifp-itr-card__badge--un-selected' :  item.variable_type ===selectedValues.unselected}">
            {{item.column}}
            @if (item.variable_type ===selectedValues.ignored) {
              <em class="ifp-icon ifp-icon-thunder"></em>
            }
          </div>

        }

      </div>
    </div>

  </ifp-data-tool-accordian>

</div>
<app-ifp-modal #alertModal>
  <app-ifp-remove-card [text]="alertText" (firstButtonEvent)="openOrCloseModel($event)" (secondButtonEvent)="openOrCloseModel($event)">
  </app-ifp-remove-card>
</app-ifp-modal>
