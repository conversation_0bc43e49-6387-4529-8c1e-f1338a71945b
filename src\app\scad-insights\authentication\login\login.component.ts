import { Component } from '@angular/core';
import { Router } from '@angular/router';
import { TranslateModule } from '@ngx-translate/core';
import { buttonClass, buttonIconPosition } from 'src/app/scad-insights/core/constants/button.constants';
import { IFPMsalService } from 'src/app/scad-insights/core/services/IFP-msal.service';
import { slaService } from 'src/app/scad-insights/core/services/sla/sla.service';
import { IfpButtonComponent } from 'src/app/scad-insights/ifp-widgets/ifp-atoms/ifp-button/ifp-button.component';
import { IfpImgComponent } from 'src/app/scad-insights/ifp-widgets/ifp-atoms/ifp-img/ifp-img.component';
import { UaePassService } from '../../core/services/uae-pass-service/uae-pass-service.service';
import { authTypes, localStorageKeys } from '../../core/constants/auth.constants';
import { NgStyle } from '@angular/common';

@Component({
  selector: 'app-login',
  templateUrl: './login.component.html',
  styleUrls: ['./login.component.scss'],
  imports: [IfpButtonComponent, TranslateModule, IfpImgComponent, NgStyle],
  providers: [UaePassService]
})
export class LoginComponent{
  public backgroundImage = 'assets/images/bg.png';
  public buttonIconPosition = buttonIconPosition;
  public buttonClass = buttonClass;
  public currentYear: number = new Date().getFullYear();
  constructor( private _msalService: IFPMsalService, private _sla:slaService, private _router: Router, private _uaePassService: UaePassService) {
    this._sla.slaLoader$.next(false);
  }


  login() {
    // sessionStorage.clear();
    // localStorage.clear();
    // this._msalService.clearAll();
    this._msalService.initialize().then(() => {
      localStorage.setItem(localStorageKeys.loginType, authTypes.msal);
      this._msalService.login();
      this._router.navigateByUrl('/auth');
      // this._msalService.handleRedirectPromise().then(() => {
      //   this._router.navigateByUrl('/auth');
      // });
    });
  }

  uaePassLogin() {
    localStorage.removeItem('accessToken');
    localStorage.removeItem('userEmailToken');
    this._uaePassService.navigateToUAEPass();
  }
}
