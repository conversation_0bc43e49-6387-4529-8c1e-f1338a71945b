@use "../../../../assets/ifp-styles/abstracts/index" as *;
.ifp-toggle-sidebar {
  position: relative;
  padding-left: $spacer-5;
  background-color: $ifp-color-section-white;
  &__body {
    max-width: 0;
    min-width: 0;
    transition: 0.3s;
    overflow: hidden;
  }
  &__btn {
    width: 25px;
    height: 25px;
    text-align: center;
    color: $ifp-color-white;
    border-radius: 50%;
    background-color: $ifp-color-secondary-blue-dark;
    position: absolute;
    top: 60px;
    left: 0;
    transform: translateX(-50%);
    transition: 0.3s;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    z-index: 2;
    &:before {
      height: 14px;
    }
  }
  &--submenu {

    padding-left: $spacer-0;
.ifp-toggle-sidebar{
    &__body {
    max-width: 40px;
    min-width: 40px;
    }}
  }
  &--expand {
    padding-left: $spacer-0;
    .ifp-toggle-sidebar {
      &__body {
        max-width: 480px;
        min-width: 280px;
      }
      &__btn {
        transform: rotate(180deg) translateX(50%);
        &::before {
          position: relative;
          top: 1px;
          height: 18px;
        }
      }
    }
  }
  &__collapse {
    border: 1px solid #808A9D;
    height: 16px;
    width: 16px;
    border-radius: 4px;
    border-right: 1px solid #808A9D;
    border-left: 11px solid #808A9D;
    transition: all 0.2s;
    margin: 12px;
    cursor: pointer;
    &--collapse {
      border-right: 11px solid #808A9D;
      border-left: 1px solid #808A9D;
    }
  }
}

:host-context([dir="rtl"]) {
  .ifp-toggle-sidebar__collapse {
    transform: rotate(180deg);
  }
}
