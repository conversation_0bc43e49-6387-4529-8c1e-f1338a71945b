import { TitleCasePipe } from '@angular/common';
import { Component, Input, OnChanges, signal, SimpleChanges, WritableSignal } from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';
import { IfpStatusTagComponent } from "../ifp-status-tag/ifp-status-tag.component";
import { IfpTreeProgressComponent, ProgressData } from "../ifp-tree-progress/ifp-tree-progress.component";

@Component({
  selector: 'ifp-statistical-indicators',
  standalone: true,
  imports: [TranslateModule, TitleCasePipe, IfpStatusTagComponent, IfpTreeProgressComponent],
  templateUrl: './ifp-statistical-indicators.component.html',
  styleUrl: './ifp-statistical-indicators.component.scss'
})
export class IfpStatisticalIndicatorsComponent implements OnChanges {

  @Input() data: any;
  public progressData: WritableSignal<ProgressData[]> = signal([]);

  ngOnChanges(changes: SimpleChanges) {
    if (changes['data']) {
      this.progressData.set([]);
      this.progressData.update((value: ProgressData[]) => {
        value.push({name: 'approved', value: this.data.bayaan_production, color: '#3BD6AD'});
        value.push({name: 'notApproved', value: this.data.not_approved, color: '#F3593A'});
        return value;
      })
    }
  }

}
