@use "../../../../assets/ifp-styles/abstracts/index" as *;

.ifp-link {
  display: inline-flex;
  align-items: center;
  .ifp-icon {
    margin-inline-end: $spacer-2;
  }
}
.ifp-adv-source {
  &__button {
    display: block;
    margin-top: $spacer-4;
    &::ng-deep .ifp-btn{
      width: 100%;
    }
  }
  &__file-input {
    display: none;
  }
  &__icon-sec {
    .ifp-adv-source__icon {
      margin: $spacer-0;
    }
  }
  &__uploaded-time,
  &__type {
    color: $ifp-color-grey-9;
  }
  &__type {
    font-size: $ifp-fs-2;
    display: block;
    text-align: center;
    margin-top: $spacer-1 - 2px;
  }
  &__detail-sec {
    width: calc(100% - 48px);
    margin-inline-start: $spacer-3;
    .ifp-adv-tool__item-box-name {
      margin: $spacer-0;
      padding-inline-end: $spacer-3;
    }
  }
  &__progress {
    display: block;
    margin-top: $spacer-3 + 3px;

  }
  &__detail-box {
    align-items: flex-start;
    margin: $spacer-4 $spacer-0;
    position: relative;
  }
  &__upload-detail {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: $spacer-3 - 3px;
    .ifp-icon {
      font-size: $ifp-fs-4;
    }
  }
  &__meta-sec {
    margin-bottom: $spacer-6;
  }
  &__close {
    font-size: $ifp-fs-2;
    position: absolute;
    top: $spacer-2;
    right: $spacer-2;
    z-index: 1;
    cursor: pointer;
  }
  .ifp-adv-tool {
    &__item-box {
      margin: $spacer-3 $spacer-0;
      cursor: pointer;
      &:last-of-type {
        margin-bottom: $spacer-0;
      }
    }
    &__divider {
      padding: $spacer-5 $spacer-0;
    }
  }
  &__error-light {
    background-color: $ifp-color-pale-red;
  }
}

:host::ng-deep {
  .ifp-file-upload--lg {
    .ifp-file-upload {
      &__inner {
        padding: $spacer-3 !important;
      }
      &__logo,
      &__text-wrapper {
        margin: $spacer-0 !important;
      }
      &__logo {
        margin-inline-end: $spacer-3 !important;
        width: 62px !important;
      }
      &__text {
        font-size: $ifp-fs-4 !important;
      }
    }
  }
}

:host-context([dir="rtl"]) {
  .ifp-adv-source {
    &__close {
      right: auto;
      left: $spacer-2;
    }
  }
}

:host-context(.ifp-dark-theme) {
  .ifp-adv-source {
    &__error-light {
      .ifp-adv-tool__item-box-name,
      .ifp-adv-source__close {
        color: $ifp-color-white;
      }
    }
  }
}
