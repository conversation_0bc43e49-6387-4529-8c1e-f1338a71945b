import { ifpColors } from 'src/app/scad-insights/core/constants/color.constants';
import { connectionType } from './constants/if-preps.constants';

export const basicTools = [
  {
    key: 'Upload Data',
    icon: 'ifp-icon-read-write',
    color: ifpColors.lightBlue,
    isSelected: true,
    menu: [
      {
        key: 'source',
        name: 'Select Data',
        icon: 'ifp-icon-upload-thick',
        color: ifpColors.blueHover,
        description: 'Start your data preparation by uploading the data. To upload data, select your preferred data source from the options given.',
        url: '/analytics/data-preparation/upload-data',
        type: 'SourceTool',
        enabled: true
      }
    ]
  },
  {
    key: 'Summary',
    icon: 'ifp-icon-read-write',
    color: ifpColors.lightBlue,
    isSelected: false,
    menu: [
      {
        key: 'summary',
        name: 'Summary',
        icon: 'ifp-icon-summary',
        color: ifpColors.greenDark1,
        description: 'View the entire metadata (summary) of the uploaded dataset, including data type, variable, null values, statistics, non-null values, etc.',
        url: '/analytics/data-preparation/upload-data',
        type: 'SummaryTool'
      }
    ]
  },
  {
    key: 'Processing tools',
    icon: 'ifp-icon-read-write',
    color: ifpColors.lightBlue,
    isSelected: false,
    menu: [

      {
        key: 'select',
        name: 'Modify Data Type',
        icon: 'ifp-icon-data-file',
        color: ifpColors.yellow,
        description: 'Change the data type for any column or delete any column if required. You cannot change the String data type.',
        url: 'analytics/data-preparation/select-data',
        type: 'SelectTool'
      },
      {
        key: 'clean',
        name: 'Clean Data',
        icon: 'ifp-icon-broom-stick',
        color: ifpColors.brickRed,
        description: 'Remove columns or rows with null or empty values from your dataset by selecting the columns you want to clean.',
        url: 'analytics/data-preparation/cleaning-data',
        type: 'CleansingTool'
      },
      {
        key: 'add',
        name: 'Add Calculated Columns',
        icon: 'ifp-icon-column',
        color: ifpColors.green,
        description: 'You can perform calculations using values from the existing dataset in Bayaan and create new columns or fields.',
        url: 'analytics/data-preparation/add-column',
        type: 'AddTool'
      }
      // {
      //   key: 'append',
      //   name: 'Append Data',
      //   icon: 'ifp-icon-text',
      //   color: ifpColors.chartVilot,
      //   description: 'Combine data from various sources to enrich your analysis.',
      //   url: 'analytics/data-preparation/append-data',
      //   type: 'AppendTool'
      // }
    ]
  },
  {
    key: 'Export Data',
    icon: 'ifp-icon-read-write',
    color: ifpColors.lightBlue,
    isSelected: false,
    menu: [
      {
        key: 'destination',
        name: 'Export Data',
        icon: 'ifp-icon-download-thick',
        color: ifpColors.purple,
        description: 'Export the prepared dataset to your device as a downloaded file, save it to My Bayaan Library, or export it to the tools listed.',
        url: '/analytics/data-preparation/save',
        type: 'DestinationTool'
      }
    ]
  }
];

export const popupButtonList = [
  {
    key: 'select',
    name: 'Modify Data Type',
    icon: 'ifp-icon-data-file',
    color: ifpColors.yellow,
    url: 'analytics/data-preparation/select-data'
  },
  {
    key: 'clean',
    name: 'Clean Data',
    icon: 'ifp-icon-broom-stick',
    color: ifpColors.yellow,
    url: 'analytics/data-preparation/cleaning-data'
  },
  {
    key: 'add',
    name: 'Add Calculated Columns',
    icon: 'ifp-icon-column',
    color: ifpColors.green,
    url: 'analytics/data-preparation/add-column'
  },
  {
    key: 'destination',
    name: 'Export Data',
    icon: 'ifp-icon-download-thick',
    color: ifpColors.purple,
    url: 'analytics/data-preparation/save'
  }
];


export const advancedTools = [
  {
    key: 'Upload and Export Tools',
    icon: 'ifp-icon-read-write',
    color: ifpColors.lightBlue,
    isSelected: true,
    menu: [
      {
        key: 'source',
        name: 'Source',
        icon: 'ifp-icon-upload-thick',
        color: ifpColors.blueHover,
        description: '',
        url: 'analytics/data-preparation/upload-data',
        leftNodeCount: 0,
        rightNodeCount: 1,
        type: connectionType.inputTool
      },
      {
        key: 'destination',
        name: 'Destination',
        icon: 'ifp-icon-download-thick',
        color: ifpColors.purple,
        description: 'You can unload your data into a connector, export it directly into any of the tools below, download the output, or save it to your Bayaan library.',
        leftNodeCount: 1,
        rightNodeCount: 0,
        type: connectionType.outputTool
      }
    ]
  },
  {
    key: 'Processing tools',
    icon: 'ifp-icon-transform',
    color: ifpColors.lightBlue,
    isSelected: true,
    menu: [
      {
        key: 'select',
        name: 'Select Data Type',
        icon: 'ifp-icon-select-col',
        color: ifpColors.cyan,
        description: 'Select, rename, and reorder columns, modify data types, or remove irrelevant fields to manage your dataset. The modifications you make will be applied in the next step of the workflow.',
        leftNodeCount: 1,
        rightNodeCount: 1,
        type: connectionType.selectTool
      },
      {
        key: 'filter',
        name: 'Filter',
        icon: 'ifp-icon-filter-fill',
        color: ifpColors.brickRed,
        description: 'Apply filters to refine your data into two streams, one where the entry in the column is true based on the condition selected and one where the entry does not true based on the condition selected.',
        leftNodeCount: 1,
        rightNodeCount: 2,
        type: 'FilterTool',
        anchorTexts: ['T', 'F']
      },
      {
        key: 'clean',
        name: 'Data Cleaning',
        icon: 'ifp-icon-broom-stick',
        color: ifpColors.yellow,
        description: 'Perform basic data cleaning tasks like selecting columns, replacing values, and removing null values, characters, and duplicates to ensure data quality and accuracy.',
        leftNodeCount: 1,
        rightNodeCount: 1,
        type: connectionType.cleansingTool
      },
      {
        key: 'aggregate',
        name: 'Aggregate',
        icon: 'ifp-icon-plus-round-fill',
        color: ifpColors.purple,
        description: 'Combine/group data from different columns to make analysis easier and more efficient.',
        leftNodeCount: 1,
        rightNodeCount: 1,
        type: connectionType.aggregateTool
      },
      {
        key: 'sort',
        name: 'Sort',
        icon: 'ifp-icon-sort',
        color: ifpColors.blueHover,
        description: 'Sort column data to organize table content. Start with a primary sort, then add more parameters for subsequent sorting.',
        leftNodeCount: 1,
        rightNodeCount: 1,
        type: connectionType.sortTool
      },
      {
        key: 'impute',
        name: 'Data Impute',
        icon: 'ifp-icon-impute',
        color: ifpColors.red,
        description: 'Use the Impute function to automatically fill in missing, or user-specified numerical values in your dataset with another selected value and ensure your data is complete and accurate. ',
        leftNodeCount: 1,
        rightNodeCount: 1,
        type: connectionType.imputeTool
      },
      {
        key: 'calculations',
        name: 'Calculations',
        icon: 'ifp-icon-calculation',
        color: ifpColors.green,
        description: 'Use calculations to create and update new columns and deploy expressions to perform functions and operations',
        leftNodeCount: 1,
        rightNodeCount: 1,
        type: connectionType.formulaTool
      }
    ]
  },
  {
    key: 'Transformation Tools',
    icon: 'ifp-icon-join',
    color: ifpColors.lightBlue,
    description: '',
    isSelected: true,
    menu: [
      {
        key: 'join',
        name: 'Join',
        icon: 'ifp-icon-join',
        color: ifpColors.green,
        description: 'Discover similarities and link columns from two tables. Connect the tables using either a common identifier or their row positions.',
        leftNodeCount: 2,
        rightNodeCount: 3,
        type: 'JoinTool',
        anchorTexts: ['L', 'J', 'R']
      },
      {
        key: 'append',
        name: 'Append',
        icon: 'ifp-icon-append-new',
        color: ifpColors.purple,
        description: 'Attach rows from a Source dataset to a Target dataset.',
        leftNodeCount: 2,
        rightNodeCount: 1,
        type: 'UnionTool',
        anchorInputTexts: ['S', 'T']
      }
    ]
  }
];

export const dataTypes = ['INT', 'FLOAT', 'DOUBLE', 'STRING'];

export const advanceMessage = {
  workFlowRunning: 'Workflow Running',
  emptyConnection: 'Source must have a connection!',
  sourceData: 'You must upload at least one source!',
  currentConnection: 'Node must have atleast one connection'
};

export const nodeAnchors = {
  input: 'input',
  output: 'output'
};

export const commonDataTypes = {
  bigint: 'Number',
  int: 'Number',
  long: 'Number',
  string: 'Text',
  float: 'Decimal',
  double: 'Decimal',
  integer: 'Number'
};

export const canvasTools = {
  fullScreen: 'fullscreen',
  grid: 'grid',
  zoomIn: 'zoom-in',
  zoomOut: 'zoo'
};

export const columnTypes = {
  string: 'string'
};
