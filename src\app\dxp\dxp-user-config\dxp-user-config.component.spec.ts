import { ComponentFixture, TestBed } from '@angular/core/testing';

import { DxpUserConfigComponent } from './dxp-user-config.component';

describe('DxpUserConfigComponent', () => {
  let component: DxpUserConfigComponent;
  let fixture: ComponentFixture<DxpUserConfigComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [DxpUserConfigComponent]
    })
    .compileComponents();

    fixture = TestBed.createComponent(DxpUserConfigComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
