<div class="ifp-prep-append">
  @if (step !== 'detail') {
    <h2 class="ifp-module-heading">{{'Select the data you want to append' | translate}}</h2>
  }
  @switch (step) {
    @default {
      <div class="ifp-prep-append__wrapper">
        <div class="ifp-prep-append__center">
          <div class="ifp-prep-append__cta" (click)="step = 'upload'">
            <em class="ifp-icon ifp-icon-upload-thick ifp-prep-append__box-icon" [style.color]="color.blueHover"></em>
            <h3 class="ifp-prep-append__box-title">{{'Upload data' | translate}}</h3>
            <p class="ifp-prep-append__box-desc">{{'Upload the data that you would like to append to the source' | translate}}</p>
          </div>

          <div class="ifp-prep-append__cta" (click)="step = 'browse'">
            <em class="ifp-icon ifp-icon-browse ifp-prep-append__box-icon" [style.color]="color.purple"></em>
            <h3 class="ifp-prep-append__box-title">{{'Import from Indicators' | translate}}</h3>
            <p class="ifp-prep-append__box-desc">{{'Import from indicators: Choose the indicator data that you would like to append to the source' | translate}}</p>
          </div>
        </div>
      </div>
    }
    @case ('upload') {
      <div class="ifp-prep-append__wrapper">
        <ifp-back-button [isGoBack]="false" (buttonClick)="step = ''"></ifp-back-button>
        <div class="ifp-prep-append__upload ifp-prep-append__center">
          <div class="ifp-prep-append__upload-inner">
            <h3 class="ifp-prep-append__upload-title">{{'Upload data' | translate}}</h3>
            <p class="ifp-prep-append__upload-desc">{{'Upload the data that you would like to append to the source' | translate}}</p>
            <app-ifp-db-file-uploader [progress]="uploadDataProgess" class="ifp-prep-append__file-uploader" [isImage]="false" [allowedExtensions]="allowedExtensions" (fileUpload)="uploadFile($event)" (removeFile)="deleteFile()"></app-ifp-db-file-uploader>

          </div>
        </div>
       <div class="ifp-prep-append__btn-wrapper">
          <ifp-button  [label]="'Proceed'" (ifpClick)="onProceedInput()" class="ifp-prep-append__button" [loader]="loader"
          [buttonClass]="disableProcesed ? buttonClass.disabled :  buttonClass.primary" [iconClass]="'ifp-icon-rightarrow'"></ifp-button>
        </div>
      </div>
    }
    @case ('browse') {
      <div class="ifp-prep-append__wrapper">
        <ifp-back-button [isGoBack]="false" (buttonClick)="step = ''"></ifp-back-button>
        <app-ifp-import-indicators [noDataText]="'No Data Available'" (cancel)="cancelImport()" [loader]="loader" [isSingleSelect]="true" [primaryButtonText]="'Add data' | translate" (addToDashboard)="addAllIndicators($event)" [importType]="step"></app-ifp-import-indicators>
      </div>
    }
    @case ('detail') {
      <div class="ifp-prep-append__detail">
        <div class="ifp-prep-append__detail-top">
          <ifp-back-button [isGoBack]="false" (buttonClick)="step = ''"></ifp-back-button>
          <ifp-back-button [isGoBack]="false" [buttonText]="'Reset'" [icon]="'ifp-icon-refresh-round'"></ifp-back-button>
        </div>
        <div class="ifp-prep-append__table">
          <div class="ifp-prep-append__table-row ifp-prep-append__table-row--head">
            <div class="ifp-prep-append__table-col ifp-prep-append__table-col--head">
              <div class="ifp-prep-append__table-item">{{'Column' | translate}}</div>
              <div class="ifp-prep-append__table-item">{{'Data type' | translate}}</div>
            </div>
            <div class="ifp-prep-append__table-divider"><span class="ifp-prep-append__table-line"></span></div>
            <div class="ifp-prep-append__table-col ifp-prep-append__table-col--head">
              <div class="ifp-prep-append__table-item">{{'Append data from uploaded table' | translate}}</div>
              <div class="ifp-prep-append__table-item">{{'Data type' | translate}}</div>
            </div>
          </div>
          @for (col of formControl?.value.array; let i = $index; track col.name) {
            <div class="ifp-prep-append__table-row" >
              <div class="ifp-prep-append__table-col">
                <div class="ifp-prep-append__table-item">
                  <div>{{col.name}}</div>
                </div>
                <div class="ifp-prep-append__table-item">
                  <ifp-tag [tagName]="col.type"></ifp-tag>
                </div>
              </div>
              <div class="ifp-prep-append__table-divider"><em class="ifp-icon ifp-icon-rightarrow"></em></div>
              <div class="ifp-prep-append__table-col">
                <div class="ifp-prep-append__table-item">
                  <app-ifp-db-dropdown [key]="'name'" class="ifp-import__filter-item" [formControl]="formControl.controls['array'].controls[i].controls['selectedValue']" [options]="col.options" [isMultiSelect]="false"   ></app-ifp-db-dropdown>
                </div>
                <div class="ifp-prep-append__table-item"><ifp-tag [tagName]="col.type"></ifp-tag></div>
              </div>
            </div>
          }
        </div>
        <ifp-button [loader]="loader" [label]="'Proceed'" (ifpClick)="onProceed()" class="ifp-prep-append__button"
        [buttonClass]="buttonClass.primary" [iconClass]="'ifp-icon-rightarrow'"></ifp-button>
      </div>
    }
  }
</div>

<ifp-data-preview class="ifp-prep-data__preview"></ifp-data-preview>
