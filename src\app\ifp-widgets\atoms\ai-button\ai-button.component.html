<div class="ifp-ai-button" [class]="theme()" (click)="ifpClickEvent()" [ngClass]="{'ifp-ai-button--disable-translate': disableTranslate(), 'ifp-ai-button--disabled': disabled()}">
  <div class="ifp-ai-button__outer-wrap" >
    <div class="ifp-ai-button__outer">
      <button class="ifp-ai-button__btn">
        <em class="ifp-ai-button__icon ifp-icon" [class]="iconClass()"></em>
        {{disableTranslate() ? label() :( label() | translate)}}
        @if(loader()) {
          <app-ifp-spinner-round  class="ifp-btn--spinner"></app-ifp-spinner-round>
          }
      </button>
    </div>
  </div>


</div>
