@use "../../../../assets/ifp-styles/abstracts/index" as *;

.ifp-select {
  padding-bottom: $spacer-5;
  .ifp-module-heading {
    margin: $spacer-5 $spacer-0;
    text-align: center;
  }
  &__heading-section {
    text-align: center;
    font-size: $ifp-fs-8;
    font-weight: $fw-bold;
    margin-bottom: $spacer-2;
  }
  &__heading-sub{
    text-align: center;
    font-size: $ifp-fs-6;
    color: $ifp-color-tertiary-text;
  }

  &__center {
    display: flex;
    justify-content: center;
    text-align: center;
    min-height: 300px;
    margin: $spacer-3 (-$spacer-3) $spacer-0;
    &:only-child {
      margin-top: $spacer-0;
    }
  }
  &__wrapper {
    background-color: $ifp-color-section-white;
    border-radius: 20px;
    padding: $spacer-5 $spacer-5 $spacer-0;
    margin: 0 auto;
    margin-top: $spacer-5;
    max-width: 1200px;
  }
  &__cta {
    flex: 1;
    border: 1px solid $ifp-color-grey-3;
    border-radius: 20px;
    text-align: center;
    padding: $spacer-4;
    margin: $spacer-3;
    transition: 0.3s;
    cursor: pointer;
    position: relative;
    &::before,
    &::after {
      content: "";
      position: absolute;
      border-radius: 50%;
      top: 16px;
      left: 16px;
    }
    &::before {
      width: 25px;
      height: 25px;
      border: 1px solid $ifp-color-blue-menu;
    }
    &::after {
      width: 15px;
      height: 15px;
      background-color: $ifp-color-blue-menu;
      margin: 5px;
      opacity: 0;
      transition: 0.3s;
    }
    &:hover {
      box-shadow: 0 0 16px $ifp-color-black-16;
    }
    &--active {
      border: 1px solid $ifp-color-blue-menu;
      background-color: rgba(231, 242, 255, 0.3);
      &::after {
        opacity: 1;
      }
    }
  }
  &__box-icon {
    display: inline-block;
    font-size: 4.5rem;
    margin-bottom: $spacer-2;
  }
  &__box-title {
    font-size: $ifp-fs-5;
    font-weight: $fw-bold;
    margin-bottom: $spacer-3;
  }
  &__box-desc {
    font-size: $ifp-fs-4;
    color: $ifp-color-tertiary-text;
    font-weight: $fw-medium;
    line-height: 1.4;
    min-height: 70px;
  }
  &__footer {
    display: flex;
    justify-content: flex-end;
    padding: $spacer-4;
    border-top: 1px solid $ifp-color-grey-13;
    margin: $spacer-3 (-$spacer-5) $spacer-0;
  }
  &__close-modal,
  &__button--cancel {
    display: none;
  }
  &__button {
    margin: $spacer-0 $spacer-2;
  }
  &__close-modal {
    font-size: $ifp-fs-4;
    position: absolute;
    top: $spacer-5;
    right: $spacer-5;
    cursor: pointer;
  }
  &--modal {
    position: relative;
    .ifp-select {
      &__close-modal,
      &__button--cancel {
        display: block;
      }
    }
  }
}
