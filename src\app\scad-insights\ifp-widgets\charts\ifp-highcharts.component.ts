import { ShortNumberPipe } from './../../core/pipes/shortNumber.pipe';
import { ChangeDetectorRef, Component, ElementRef, Input, OnChanges, OnDestroy, OnInit, Renderer2, SimpleChanges, ViewChild, inject, input } from '@angular/core';
import { DecimalPipe, DatePipe, NgClass } from '@angular/common';
import { HighchartsChartModule } from 'highcharts-angular';
import * as Highcharts from 'highcharts';
import { HighchartService } from 'src/app/scad-insights/core/services/highchart.service';
import { Subscription, fromEvent, SubscriptionLike } from 'rxjs';
import { ifpColors } from 'src/app/scad-insights/core/constants/color.constants';
import { TranslateService } from '@ngx-translate/core';
import { numberChartDatePipe } from 'src/app/scad-insights/core/pipes/numberChart.pipe';
import HighchartsMore from 'highcharts/highcharts-more';
import SolidGauge from 'highcharts/modules/solid-gauge';
HighchartsMore(Highcharts);
SolidGauge(Highcharts);
import { ThemeService } from '../../core/services/theme/theme.service';
import { Chart } from 'highcharts/highcharts.src';
let that: any = this;
@Component({
  selector: 'ifp-highcharts',
  imports: [NgClass,
    HighchartsChartModule],
  templateUrl: './ifp-highcharts.component.html',
  styleUrls: ['./ifp-highcharts.component.scss'],
  providers: [DecimalPipe, numberChartDatePipe, DatePipe]
})
export class IFPHighChartsComponent implements OnChanges, OnInit, OnDestroy {

  private readonly datePipe = inject(DatePipe);

  @ViewChild('chart') chart!: any;
  @Input() chartClass: string = 'ifp-line-doted-axis';
  @Input() chartName: string = 'areaChart';
  @Input() comparison: string = 'Y/Y';
  @Input() tickIntervell!: number;
  @Input() height: number | null = 100;
  @Input() width!: number | null;
  @Input() axisXFormat = (value: any) => {
    return value;
  };

  @Input() data: any[] = [];

  @Input() yaxisLabel!: string;
  @Input() xAxisLabel!: string;
  @Input() format!: string;
  @Input() marker = true;
  @Input() enableAnimation = true;
  @Input() category!: string[];
  @Input() borderRadius!: number;
  @Input() tooltipLabel = '';
  @Input() comparisonEnable = true;
  @Input() markerColor: string = '#3bd6ad'
  public yAxis = input<Highcharts.YAxisOptions>();

  public xAxis = input<Highcharts.XAxisOptions>();
  public showXAxisLabel = input<boolean>(true);


  public plotOptions = input<Highcharts.PlotOptions>();

  public legends = input<Highcharts.LegendOptions>();
  public tooltip = input<Highcharts.TooltipOptions>();
  public isCircular = input<boolean>(false);
  public donutCenter = input(0);
  public donutCenterFunc = input<(chart: Chart) => void>();
  public custom = input<Record<string, any>>();

  public ifpColors = ifpColors;
  public plotBand: Record<string, any> = {
    line1: null,
    line2: null
  };
  public chartType = input();
  public chartInstance!: Highcharts.Chart;
  public axisTitleColor: string = ifpColors.primaryGrey;
  public updateFlag = false;
  public clickSubscription: Subscription[] = [];
  public axisGroup!: Element;
  public subscription!: SubscriptionLike;
  public axisStyle = {
    fontSize: '1.2rem',
    color: '#6A7180',
    lineHeight: '1.4rem',
    fontWeight: '500',
    fontFamily: 'Noto Sans'
  }


  public charts: Record<string, any> = {
    areaChart: {
      exporting: {
        enabled: false
      },
      tooltip: {
        animation: false,
        hideDelay: 0,
        backgroundColor: this.ifpColors.blackLight,
        borderRadius: 10,
        shadow: true,
        distance: 0,
        style: {
          color: this.ifpColors.white
        },
        useHTML: true,
        outside: true,
        className: 'ifp-chart-tooltip',
        formatter: function (): any {
          const chartData: any = this;
          const date = that.category?.length > 0 ? chartData.x : that.datePipe.transform(new Date(chartData.x), 'MMM yyyy');
          return `<div class="ifp-chart-tooltip__wrapper"><span class="ifp-chart-tooltip__header">${date}</span><span class="ifp-chart-tooltip__value" >${that._number.transform(chartData.y, that.format)}</span></div>`;
        }
      },
      chart: {
        title: {
          enabled: false
        },
        exporting: {
          enabled: false
        },
        height: 270,
        events: {
          load: (event: Event | any) => {
            const axis = event.target?.['xAxis']?.[0];
            this.axisGroup = axis.axisGroup.element;
            fromEvent(this._el.nativeElement, 'click').subscribe(() => {
              this.chartData.xAxis.plotBands = [];
              this.updateFlag = true;
              this._cdr.detectChanges();
            });
          },
          render: (eventRender: Event | any) => {
            const xAxis = eventRender.target.xAxis[0];
            const ticks = xAxis.ticks;
            this.clickSubscription?.forEach((element: Subscription) => {
              element?.unsubscribe();
            });

            if (xAxis.axisGroup.element) {
              for (const key in ticks) {
                if (Object.prototype.hasOwnProperty.call(ticks, key) && ticks[key].mark) {
                  xAxis.axisGroup.element.appendChild(ticks[key].mark.element);
                  const clicks = fromEvent(ticks[key].mark.element, 'click').subscribe(() => {
                    const date: number = +key;
                    if (!this.plotBand['line1']) {
                      this.plotBand['line1'] = date;
                    } else if (!this.plotBand['line2']) {
                      this.plotBand['line2'] = date;
                    } else {
                      this.plotBand['line1'] = date;
                      this.plotBand['line2'] = null;
                    }
                    this.chartData['xAxis']['plotBands'] = [{
                      zIndex: 1,
                      className: 'ifp-high-chart_dot-band',
                      value: this.plotBand['line1']
                    },
                    {
                      zIndex: 1,
                      className: 'ifp-high-chart_dot-band',
                      value: this.plotBand['line2']
                    }
                    ];
                    this.updateFlag = true;
                    this._cdr.detectChanges();
                  });
                  this.clickSubscription.push(clicks);

                }
              }
            }

          }
        }
      },
      title: {
        text: ''
      },
      credits: {
        enabled: false
      },
      xAxis: {
        type: 'datetime',
        lineColor: '#D9DCDD',
        tickColor: '#D9DCDD',
        tickLength: 1,
        lineWidth: 1,
        gridLineColor: '#F8F8F8',
        tickPosition: 'outside',
        tickmarkPlacement: 'outside',
        // startOnTick: true,
        // endOnTick: true,
        title: {
          enabled: true,
          text: this.xAxisLabel,
          style: {
            fontSize: '14px',
            fontWeight: '600',
          },
        },
        labels: {
          style: this.axisStyle
        }
      },
      yAxis: {
        title: {
          enabled: this.yaxisLabel ? true : false,
          text: this.yaxisLabel,
          style: {
            fontSize: '14px',
            fontWeight: '600',
          },
        },
        labels: {
          style: this.axisStyle
        }
      },
      legend: {
        rtl: this._translate.currentLang === 'ar',
        enabled: false,
        itemStyle: {
          fontSize: '14px', // set the desired font size here,
          fontFamily: 'Noto Sans',
          fontWeight: '500'
        }
      },
      plotOptions: {
        area: {
          fillColor: {
            linearGradient: {
              x1: 0,
              y1: 0,
              x2: 0,
              y2: 1
            },
            stops: [
              [0, this._highChart.color('#2687FD').setOpacity(80).get('rgba')],
              [1, this._highChart.color('#2687FD').setOpacity(0).get('rgba')]
            ]
          },
          marker: {
            radius: 2,
          },
          lineWidth: 1,
          states: {
            hover: {
              lineWidth: 1
            }
          },
          threshold: null
        }
      },

      series: [{
        type: 'area',
        name: 'USD to EUR',
        data: [],
        color: '#2687FD',
        marker: {
          enabled: false
        }
      }]
    },
    lineChart: {
      exporting: {
        enabled: false
      },
      chart: {
        title: {
          enabled: false
        },
        height: 180
      },
      title: {
        text: ''
      },

      credits: {
        enabled: false
      },
      xAxis: {
        type: 'datetime',
        lineColor: '#D9DCDD',
        tickColor: '#D9DCDD',
        tickWidth: 10,
        tickLength: 1,
        gridLineWidth: 30,
        lineWidth: 1,
        gridLineColor: '#F8F8F8',
        tickPosition: 'outside',
        tickmarkPlacement: 'outside',
        startOnTick: true,
        endOnTick: true,
        title: {
          enabled: true,
          text: this.xAxisLabel,
          style: {
            fontSize: '14px',
            fontWeight: '600',
          },
        },
        labels: {
          style: this.axisStyle
        }
      },

      yAxis: {
        gridLineDashStyle: 'dot',
        gridLineColor: '#e6e6e6',
        tickWidth: 0,
        tickLength: 0,
        title: {
          enabled: false
        },
        labels: {
          formatter: (value: any) => {
            const number = (this._number.transform(+value.value, '1.0-2'))?.replaceAll(',', '');
            return this._shortNumber.transform(number, value.axis.dataMax);
          }
        }
      },
      legend: {
        rtl: this._translate.currentLang === 'ar',
        enabled: false
      },
      tooltip: {
        animation: false,
        hideDelay: 0,
        backgroundColor: '#182234',
        borderRadius: 10,
        shadow: true,
        distance: 0,
        style: {
          color: '#FFFFFF'
        },
        useHTML: true,
        outside: true,
        className: 'ifp-chart-tooltip',
        formatter: function (): any {
          const chartData: any = this;
          const date = that.datePipe.transform(new Date(chartData.x), 'MMM yyyy');
          return `<div class="ifp-chart-tooltip__wrapper"><span class="ifp-chart-tooltip__header">${date}</span><span class="ifp-chart-tooltip__value" >${that._number.transform(chartData.y, that.format)}</span></div>`;
        }
      },

      series: [{
        data: [],
        color: '#3bd6ad',
        marker: {
          enabled: true
        }
      }]
    },
    barChart: {
      exporting: {
        enabled: false
      },
      chart: {
        type: 'column',
        backgroundColor: 'transparent',
        width: this.width
      },
      title: {
        text: ''
      },
      xAxis: {
        lineWidth: 0,
        categories: undefined,
        labels: {
          style: this.axisStyle
        },
      },
      yAxis: { visible: false },
      credits: {
        enabled: false
      },
      plotOptions: {
        column: {
          borderRadius: 4,
          color: '#8cbdf7'
        },
        series: {
          animation: this.enableAnimation
        }
      },
      legend: {
        enabled: true,
        rtl: this._translate.currentLang === 'ar',
        itemStyle: {
          fontSize: '1.4rem', // set the desired font size here,
          fontFamily: 'Noto Sans',
          fontWeight: 400,
          color: '#6A7180'
        },
      },
      tooltip: {
        style: {
          zIndex: 9999 // Set the z-index of the tooltip
        },
        positioner: (boxWidth: number, boxHeight: number, point: { plotX: number, plotY: number }) => {
          return {
            x: point.plotX - (boxWidth / 3),
            y: point.plotY - boxHeight - 10 // Adjust the offset as needed
          };
        },
        animation: false,
        hideDelay: 0,
        backgroundColor: '#182234',
        borderRadius: 10,
        shadow: false,
        distance: 0,
        useHTML: true,
        outside: true,
        formatter: (a: { chart: { hoverPoint: { x: string | number, y: string | number, category: string | number, count?: number } } }) => {
          const point = a.chart.hoverPoint;
          if (this.isCircular()) {
            let html = `<div class="ifp-chart-tooltip__usage ifp-chart-tooltip__usage--circular"> <p class="ifp-chart-tooltip__usage-title">${point.category}</p>`;
            if (point.count) {
              html += `<span class="ifp-chart-tooltip__usage-value">${point.count}</span>`;
            }
            html += `<span class="ifp-chart-tooltip__usage-value">${point.y} ${this.tooltipLabel}</span></div>`;
            return html;
          } else {
            return `<div class="ifp-chart-tooltip__usage"> <div>${point.category}</div>  ${point.y}  ${this.tooltipLabel}</div>`;
          }

        }
      },
      series: []
    },
    bubbleChart: {

      chart: {
        type: 'bubble',
        zooming: {
          type: 'xy'
        },
        height: this.height
      },
      title: {
        text: ''
      },
      colorAxis: [{}, {
        minColor: '#434348',
        maxColor: '#e6ebf5'
      }],
      tooltip: {
        animation: false,
        hideDelay: 0,
        backgroundColor: '#182234',
        borderRadius: 10,
        shadow: true,
        distance: 0,
        style: {
          color: '#FFFFFF'
        },
        useHTML: true,
        outside: true,
        className: 'ifp-chart-tooltip',
        formatter: function (): any {
          const chartData: any = this;
          return `<div class="ifp-chart-tooltip__wrapper"><span class="ifp-chart-tooltip__header">${chartData.key}</span><span class="ifp-chart-tooltip__value" >${chartData.y}</span></div>`;
        }
      },

      xAxis: {
        lineColor: '#D9DCDD',
        tickColor: '#D9DCDD',
        gridLineWidth: 50,
        lineWidth: 1,
        gridLineColor: '#F8F8F8',
        tickPosition: 'outside',
        type: 'category',
        labels: {
          style: {
            fontSize: '14px',
            color: this.axisTitleColor,
            lineHeight: '14px',
            fontWeight: '500',
            fontFamily: 'Noto Sans'
          }

        }
      },
      legend: {
        rtl: this._translate.currentLang === 'ar',
        itemStyle: {
          fontSize: '14px', // set the desired font size here,
          fontFamily: 'Noto Sans',
          fontWeight: '500'
        }
      },
      yAxis: {
        startOnTick: false,
        endOnTick: false,
        offset: 30,
        lineWidth: 0,
        gridLineDashStyle: 'Dash',
        visible: true, // Hide the y-axis
        title: {
          text: this.yaxisLabel,
          style: {
            fontSize: '15px',
            fontWeight: '500',
            color: this.axisTitleColor
          }
        },
        labels: {
          formatter: (value: any) => {
            const datamax = value.axis.dataMax;
            const number = (this._number.transform(+value.value, '1.0-2'))?.replaceAll(',', '');
            return this._shortNumber.transform(number, datamax);
          },
          style: {
            fontSize: '14px',
            color: this.axisTitleColor,
            fontFamily: 'Noto Sans',
            fontWeight: '500'
          }
        }
      },
      credits: {
        enabled: false
      },
      plotOptions: {
        series: {
          animation: this.enableAnimation
        }
      },
      series: [],
      exporting: {
        enabled: false
      }
    },
    boxChart: {

      chart: {
        type: 'boxplot',

        height: this.height
      },

      title: {
        text: ''
      },
      colorAxis: [{}, {
        minColor: '#434348',
        maxColor: '#e6ebf5'
      }],
      legend: {
        enabled: false
      },
      tooltip: {
        animation: false,
        hideDelay: 0,
        backgroundColor: '#182234',
        borderRadius: 10,
        shadow: true,
        distance: 0,
        style: {
          color: '#FFFFFF'
        },
        useHTML: true,
        outside: true,
        className: 'ifp-chart-tooltip',
        formatter: function (): any {
          const chartData: any = this;
          if (chartData.series.userOptions.type) {

            return `<div class="ifp-chart-tooltip__wrapper"><span class="ifp-chart-tooltip__header">${chartData.key}</span><span class="ifp-chart-tooltip__value" >${that._translate.instant('outlier')}: ${chartData.y}</span></div>`;
          }
          return `<div class="ifp-chart-tooltip__wrapper"><span class="ifp-chart-tooltip__header">${chartData.key}</span>
          <span class="ifp-chart-tooltip__value" >${that._translate.instant('Maximum')}: ${chartData.point.high}</span>
            <span class="ifp-chart-tooltip__value" >Q3: ${chartData.point.q3}</span>
              <span class="ifp-chart-tooltip__value" >${that._translate.instant('Median')}: ${chartData.point.median}</span>

                <span class="ifp-chart-tooltip__value" >Q1: ${chartData.point.q1}</span>
                 <span class="ifp-chart-tooltip__value" > ${that._translate.instant('Minimum')}: ${chartData.point.low}</span>
          </div>`;
        }
      },
      xAxis: {
        lineColor: '#D9DCDD',
        tickColor: '#D9DCDD',
        gridLineWidth: 50,
        lineWidth: 1,
        gridLineColor: '#F8F8F8',
        tickPosition: 'outside',
        type: 'category',
        categories: [],
        labels: {
          style: {
            fontSize: '14px',
            color: this.axisTitleColor,
            lineHeight: '14px',
            fontWeight: '500',
            fontFamily: 'Noto Sans'
          }

        }
      },

      yAxis: {
        startOnTick: false,
        endOnTick: false,
        offset: 30,
        lineWidth: 0,
        gridLineDashStyle: 'Dash',
        visible: true, // Hide the y-axis
        title: {
          text: this.yaxisLabel,
          style: {
            fontSize: '15px',
            fontWeight: '500',
            color: this.axisTitleColor
          }
        },
        labels: {
          formatter: (value: any) => {
            const datamax = value.axis.dataMax;
            const number = (this._number.transform(+value.value, '1.0-2'))?.replaceAll(',', '');
            return this._shortNumber.transform(number, datamax);
          },
          style: {
            fontSize: '14px',
            color: this.axisTitleColor,
            fontFamily: 'Noto Sans',
            fontWeight: '500'
          }
        }
      },

      credits: {
        enabled: false
      },
      plotOptions: {
        series: {
          animation: this.enableAnimation
        }
      },
      series: [],
      exporting: {
        enabled: false
      }


    },
    scatterPlot: {

      chart: {
        type: 'scatter',
        zooming: {
          type: 'xy'
        },
        height: this.height
      },

      title: {
        text: ''
      },
      colorAxis: [{}, {
        minColor: '#434348',
        maxColor: '#e6ebf5'
      }],
      legend: {
        enabled: false
      },
      tooltip: {
        animation: false,
        hideDelay: 0,
        backgroundColor: '#182234',
        borderRadius: 10,
        shadow: true,
        distance: 0,
        style: {
          color: '#FFFFFF'
        },
        useHTML: true,
        outside: true,
        className: 'ifp-chart-tooltip',
        formatter: function (): any {
          const chartData: any = this;
          return `<div class="ifp-chart-tooltip__wrapper"><span class="ifp-chart-tooltip__header">${that.xAxisLabel} - ${that._number.transform(chartData.x, that.format)} </span><span class="ifp-chart-tooltip__value" >${that.yaxisLabel} - ${that._number.transform(chartData.y, that.format)}</span></div>`;
        }
      },
      xAxis: {
        lineColor: '#D9DCDD',
        tickColor: '#D9DCDD',

        tickLength: 1,
        gridLineWidth: 30,
        lineWidth: 1,
        gridLineColor: '#F8F8F8',
        tickPosition: 'outside',
        tickmarkPlacement: 'outside',
        startOnTick: false,
        endOnTick: false,
        offset: 30,
        title: {
          text: this.xAxisLabel,
          style: {
            fontSize: '15px',
            fontWeight: '500',
            color: this.axisTitleColor
          }
        },
        labels: {
          formatter: (value: any) => {
            const datamax = value.axis.dataMax;
            const number = (this._number.transform(+value.value, '1.0-2'))?.replaceAll(',', '');
            return this._shortNumber.transform(number, datamax);
          },
          style: {
            fontSize: '14px',
            color: this.axisTitleColor,
            fontFamily: 'Noto Sans',
            fontWeight: '500'
          }
        }
      },
      yAxis: {
        startOnTick: false,
        endOnTick: false,
        offset: 30,
        lineWidth: 0,
        gridLineDashStyle: 'Dash',
        visible: true, // Hide the y-axis
        title: {
          text: this.yaxisLabel,
          style: {
            fontSize: '15px',
            fontWeight: '500',
            color: this.axisTitleColor
          }
        },
        labels: {
          formatter: (value: any) => {
            const datamax = value.axis.dataMax;
            const number = (this._number.transform(+value.value, '1.0-2'))?.replaceAll(',', '');
            return this._shortNumber.transform(number, datamax);
          },
          style: {
            fontSize: '14px',
            color: this.axisTitleColor,
            fontFamily: 'Noto Sans',
            fontWeight: '500'
          }
        }
      },

      credits: {
        enabled: false
      },
      plotOptions: {
        series: {
          animation: this.enableAnimation
        }
      },
      series: [],
      exporting: {
        enabled: false
      }


    },
    histogram: {

      chart: {
        type: 'column',
        height: this.height
      },
      title: {
        text: ''
      },
      colorAxis: [{}, {
        minColor: '#434348',
        maxColor: '#e6ebf5'
      }],
      tooltip: {
        animation: false,
        hideDelay: 0,
        backgroundColor: '#182234',
        borderRadius: 10,
        shadow: true,
        distance: 0,
        style: {
          color: '#FFFFFF'
        },
        useHTML: true,
        outside: true,
        className: 'ifp-chart-tooltip',
        formatter: function (): any {
          const chartData: any = this;
          return `<div class="ifp-chart-tooltip__wrapper"><span class="ifp-chart-tooltip__header">${chartData.key}</span><span class="ifp-chart-tooltip__value" >${chartData.y}</span></div>`;
        }
      },

      xAxis: {
        lineColor: '#D9DCDD',
        tickColor: '#D9DCDD',
        gridLineWidth: 50,
        lineWidth: 1,
        gridLineColor: '#F8F8F8',
        tickPosition: 'outside',
        type: 'category',
        min: -5, // Adjust min to create a gap at the start
        max: 15,
        labels: {
          formatter: (value: any) => {
            const index = this.category.indexOf(value.value);
            let currentValue = '';
            if (index !== -1) {
              currentValue = value.value;
              // if (index === 0){
              //   currentValue = value.value.split(' - ')[0];
              // } else {
              //   currentValue = value.value.split(' - ')[1];
              // }
            }
            return currentValue;
          },
          style: {
            fontSize: '14px',
            color: this.axisTitleColor,
            lineHeight: '14px',
            fontWeight: '500',
            fontFamily: 'Noto Sans'
          }

        }
      },
      legend: {
        rtl: this._translate.currentLang === 'ar',
        itemStyle: {
          fontSize: '14px', // set the desired font size here,
          fontFamily: 'Noto Sans',
          fontWeight: '500'
        }
      },
      yAxis: {
        startOnTick: false,
        endOnTick: false,
        offset: 30,
        lineWidth: 0,
        gridLineDashStyle: 'Dash',
        visible: true, // Hide the y-axis
        title: {
          text: this.yaxisLabel,
          style: {
            fontSize: '15px',
            fontWeight: '500',
            color: this.axisTitleColor
          }
        },
        labels: {
          formatter: (value: any) => {
            const datamax = value.axis.dataMax;
            const number = (this._number.transform(+value.value, '1.0-2'))?.replaceAll(',', '');
            return this._shortNumber.transform(number, datamax);
          },
          style: {
            fontSize: '14px',
            color: this.axisTitleColor,
            fontFamily: 'Noto Sans',
            fontWeight: '500'
          }
        }
      },
      credits: {
        enabled: false
      },
      plotOptions: {
        column: {
          pointPadding: 0,
          borderWidth: 0.3,
          groupPadding: 0,
          shadow: false
        },
        series: {
          animation: this.enableAnimation
        }
      },
      series: [],
      exporting: {
        enabled: false
      }
    },
    donutChart: {
      chart: {
        type: 'pie',
        backgroundColor: 'transparent',
        events: {
          load: (chart: { target: Chart }) => {
            if (this.donutCenterFunc()) {
              this.donutCenterFunc()?.(chart.target);
            } else {
              this.setCenterValueDonut(chart.target);
            }

          },
          redraw: (chart: { target: Chart }) => {
            if (this.donutCenterFunc()) {
              this.donutCenterFunc()?.(chart.target);
            } else {
              this.setCenterValueDonut(chart.target);
            }
          }

        }
      },
      title: {
        text: ''
      },
      legend: {
        align: 'center',
        verticalAlign: 'bottom',

        layout: 'horizontal'
      },
      tooltip: {
        animation: false,
        hideDelay: 0,
        backgroundColor: '#182234',
        borderRadius: 10,
        shadow: true,
        distance: 0,
        style: {
          color: '#FFFFFF'
        },
        useHTML: true,
        outside: true,
        className: 'ifp-chart-tooltip',
        formatter: function (): any {
          const chartData: any = this;
          return `<div class="ifp-chart-tooltip__wrapper"><span class="ifp-chart-tooltip__header">${chartData.key}</span><span class="ifp-chart-tooltip__value" >${chartData.y}</span></div>`;
        }
      },
      plotOptions: {
        pie: {
          innerSize: '85%', // Thickness of the donut
          borderWidth: 1,
          borderRadius: 0,
          dataLabels: {
            enabled: false // Disable data labels
          },
          series: {
            animation: this.enableAnimation
          }

        }
      },
      credits: {
        enabled: false
      },
      exporting: {
        enabled: false
      },
      series: []
    },
    gaugeChart: {
      chart: {
        type: 'solidgauge'
      },
      title: {
        text: ''
      },
      exporting: {
        enabled: false
      },
      credits: {
        enabled: false
      },

      pane: {
        center: ['50%', '85%'],
        size: '140%',
        startAngle: -90,
        endAngle: 90,
        background: {
          backgroundColor: '#fafafa',
          borderRadius: 5,
          innerRadius: '60%',
          outerRadius: '100%',
          shape: 'arc',
          borderWidth: 0
        },
      },

      yAxis: {
        lineWidth: 0,
        tickWidth: 0,
        minorTickInterval: null,
        tickAmount: 2,

        labels: {
          y: 16,
          style: this.axisStyle
        },
        min: 0,
        max: 100,
        linearGradient: { x: 0.5, y: 0.5, r: 0.5 },
        stops: [
          [0.1, '#DF5353'], // green
          [0.5, '#DDDF0D'], // yellow
          [0.9, '#55BF3B'] // red
        ]
      },
      plotOptions: {
        solidgauge: {
          borderRadius: 3,
          dataLabels: {
            y: 5,
            borderWidth: 0,
            useHTML: true,
            format: `<div style="text-align:center"><span style="font-size:20px;color:${this.themeService.defaultTheme === 'dark' ? 'white' : 'black'}" >{y}%</span><br/></div>`
          }
        }
      },
      series: []
    },
    pieChart: {
      exporting: {
        enabled: false
      },
      chart: {
        type: 'pie',
        backgroundColor: 'transparent',
        height: this.height
      },
      title: {
        text: ''
      },
      credits: {
        enabled: false
      },
      legend: {
        rtl: this._translate.currentLang === 'ar',
        enabled: true,
        align: 'center',
        verticalAlign: 'bottom',
        layout: 'horizontal',
        itemStyle: {
          fontSize: '14px',
          fontFamily: 'Noto Sans',
          fontWeight: '500',
          color: this.axisTitleColor
        }
      },
      tooltip: {
        animation: false,
        hideDelay: 0,
        backgroundColor: '#182234',
        borderRadius: 10,
        shadow: true,
        distance: 0,
        style: {
          color: '#FFFFFF'
        },
        useHTML: true,
        outside: true,
        className: 'ifp-chart-tooltip',
        formatter: function (): any {
          const chartData: any = this;
          return `<div class="ifp-chart-tooltip__wrapper"><span class="ifp-chart-tooltip__header">${chartData.key}</span><span class="ifp-chart-tooltip__value">${that._number.transform(chartData.y, that.format)}</span><span class="ifp-chart-tooltip__percentage">${chartData.percentage.toFixed(1)}%</span></div>`;
        }
      },
      plotOptions: {
        pie: {
          allowPointSelect: true,
          cursor: 'pointer',
          innerSize: '0%',
          dataLabels: {
            enabled: true,
            format: '{point.name}: {point.percentage:.1f}%',
            style: {
              fontSize: '12px',
              fontFamily: 'Noto Sans',
              fontWeight: '500',
              color: this.axisTitleColor,
              textOutline: 'none'
            }
          },
          showInLegend: true
        },
        series: {
          animation: this.enableAnimation
        }
      },
      series: [{
        name: 'Data',
        colorByPoint: true,
        data: []
      }]
    },
    custom: {

    }
  };

  public chartData: Highcharts.Options | any = this.charts[this.chartName];

  constructor(public _highChart: HighchartService, private readonly _el: ElementRef, private readonly _cdr: ChangeDetectorRef, public _number: DecimalPipe, public _render: Renderer2,
    private readonly _shortNumber: numberChartDatePipe, private readonly _translate: TranslateService, private readonly themeService: ThemeService) {
    that = this;

  }

  ngOnInit() {
    if (this.themeService.defaultTheme === 'dark') {
      this.axisTitleColor = ifpColors.white;
    } else {
      this.axisTitleColor = ifpColors.primaryGrey;
    }
    // Only set yAxis for charts that have it (not pieChart)
    if (this.charts[this.chartName].yAxis) {
      this.charts[this.chartName].yAxis.title = {
        text: this.yaxisLabel,
        style: {
          fontSize: '15px',
          fontWeight: '500',
          color: this.axisTitleColor
        }
      };
    }

    this.subscription = this.themeService.defaultTheme$.subscribe(() => {
      if (this.yaxisLabel && this.charts[this.chartName].yAxis) {
        this.charts[this.chartName].yAxis.title = {
          text: this.yaxisLabel,
          style: {
            fontSize: '15px',
            fontWeight: '500',
            color: this.axisTitleColor
          }
        };
        this.updateFlag = false;
        this._cdr.detectChanges();
        this.updateFlag = true;
        this._cdr.detectChanges();
      }
    });
    if (this.yAxis() && this.charts[this.chartName].yAxis) {
      this.charts[this.chartName].yAxis = this.yAxis();
    }
    if (this.xAxis() && this.charts[this.chartName].xAxis) {
      this.charts[this.chartName].xAxis = this.xAxis();
    }
    if (this.plotOptions()) {
      this.charts[this.chartName].plotOptions = this.plotOptions();
    }
    if (this.legends()) {
      this.charts[this.chartName].legend = this.legends();
    }
    if (this.tooltip()) {
      this.charts[this.chartName].tooltip = this.tooltip();
    }
    if (this.isCircular() && this.chartName == 'barChart') {
      this.charts[this.chartName].xAxis.labels['enabled'] = this.showXAxisLabel();
      this.charts[this.chartName].chart.polar = true;
      this.charts[this.chartName].chart.inverted = true;
    }
    if (this.custom()) {
      this.charts[this.chartName] = this.custom();
    }
         if(this.chartType()) {
        this.charts[this.chartName].chart['type'] = this.chartType();
      }
    this.charts[this.chartName].chart.height = this.height;
    this.chartData = this.charts[this.chartName];
    this.chartData.series = this.data;
    this.updateFlag = true;
  }

  ngOnChanges(changes: SimpleChanges): void {
   if (changes['chartName']) {
      this.buildChartOptions();
    }
    if (this.chart) {
      this.charts[this.chartName].chart.height = this.height;
      if (this.width) {
        this.charts[this.chartName].chart.width = this.width;
      }
      if (this.charts[this.chartName]?.plotOptions?.series?.animation) {
        this.charts[this.chartName].plotOptions.series.animation = this.enableAnimation;
      }
      if (this.category && this.charts[this.chartName].xAxis) {
        this.charts[this.chartName].xAxis.categories = this.category;
      }
      if (this.borderRadius && this.chartName == 'barChart') {
        this.charts[this.chartName].plotOptions.column.borderRadius = this.borderRadius;
      }
      if (this.yaxisLabel && this.charts[this.chartName].yAxis) {
        this.charts[this.chartName].yAxis.title.text = this.yaxisLabel;
      }
      if (this.xAxisLabel && this.charts[this.chartName].xAxis) {
        this.charts[this.chartName].xAxis.title.text = this.xAxisLabel;
      }
      if (this.yAxis() && this.charts[this.chartName].yAxis) {
        this.charts[this.chartName].yAxis = this.yAxis();
      }
      if (this.xAxis() && this.charts[this.chartName].xAxis) {
        this.charts[this.chartName].xAxis = this.xAxis();
      }
      if (this.plotOptions()) {
        this.charts[this.chartName].plotOptions = this.plotOptions();
      }
      if (this.legends()) {
        this.charts[this.chartName].legend = this.legends();
      }
      if (this.tooltip()) {
        this.charts[this.chartName].tooltip = this.tooltip();
      }
      if (this.isCircular() && this.chartName == 'barChart') {
        this.charts[this.chartName].xAxis.labels['enabled'] = this.showXAxisLabel();
        this.charts[this.chartName].chart.polar = true;
        this.charts[this.chartName].chart.inverted = true;
      }
      if (this.custom()) {
        this.charts[this.chartName] = this.custom();
      }
      if (this.tickIntervell && this.charts[this.chartName].yAxis) {
        this.charts[this.chartName].yAxis.tickInterval = this.tickIntervell;
      }
      if(this.chartType()) {
        this.charts[this.chartName].chart['type'] = this.chartType();
      }
      this.chartData = this.charts[this.chartName];
      this.chartData.series = this.data;
      this.updateFlag = true;
      this._cdr.detectChanges();
      if (this.comparisonEnable) {
        if (this.chartData?.series?.[0]?.data?.length > 1) {
          if (this.marker) {
            this.changesComparison();
          }
        }
      }
    }
  }
  setCenterValueDonut(chart: Chart) {

    const centerText = `<div text-anchor="middle"  class="ifp-high-chart__donut-center">
    <span   class="ifp-high-chart__donut-heading" >${this.donutCenter()}</span>
        <br/>
    <span dy="10" class="ifp-high-chart__donut-value">Total Users</span>
</div>`;
    if ((chart as any).centerLabel) {
      (chart as any).centerLabel.destroy();

    }

    // Create the center label if it doesn't exist
    (chart as any).centerLabel = chart.renderer.label(centerText, 0, 0)
      .attr({
        zIndex: 5
      })
      .add();

    // Center the label based on the chart's dimensions
    const bbox = (chart as any).centerLabel.getBBox();

    (chart as any).centerLabel.attr({
      x: chart.plotLeft + (chart.plotWidth / 2),
      y: (chart.plotHeight / 2)  // Adjust for vertical centering
    });

  }
  update(data: any[]) {
    this.chartData.series = data;
    this.updateFlag = true;
  }

  charData(event: Highcharts.Chart) {
    this.chartInstance = event;
  }


  removeTooltip() {
    if (this.chart.chart.tooltip) {
      this.chart.chart.tooltip.hide();
      this._cdr.detectChanges();
    }

  }

  changesComparison() {
    const chart = this.chart.chart;
    const series = chart.series[0];
    const points = series.points;
    for (let i = 0; i < points.length; i++) {
      const point = points[i];
      point?.update({
        marker: {
          fillColor: this.markerColor,
          lineColor: this.markerColor,
          radius: 4,
          symbol: 'circle'
        }
      }, false);
    }
    const pointLastIndex = points[points.length - 1];
    pointLastIndex?.update({
      marker: {
        fillColor: '#3366fb',
        lineColor: '#3366fb',
        radius: 6,
        symbol: 'url(../../../../../assets/images/trg.png)'
      }
    }, false);

    const pointMidIndex = points[this.comparison == 'Y/Y' ? 0 : (this.comparison == 'Q/Q' ? 8 : 10)];
    pointMidIndex?.update({
      marker: {
        fillColor: '#3366fb',
        lineColor: '#3366fb',
        radius: 6,
        symbol: 'url(../../../../../assets/images/trg.png)'
      }
    }, false);
    chart.redraw();
  }

  buildChartOptions() {
    if (this.chartName === 'pie') {
      this.chartData = this.getPieChartConfig();
    } else if (this.chartName === 'donut') {
      this.chartData = this.getDonutChartConfig();
    } else {
      // keep existing chart config logic here
      return;
    }
    this.updateFlag = true;
  }

  getPieChartConfig(): any {
    return {
      chart: {
        type: 'pie',
      },
      title: { text: '' },
      tooltip: {
        pointFormat: '{series.name}: <b>{point.y}</b>',
      },
      accessibility: {
        point: {
          valueSuffix: '%',
        },
      },
      plotOptions: {
        pie: {
          allowPointSelect: true,
          cursor: 'pointer',
          dataLabels: {
            enabled: true,
            format: '<b>{point.name}</b>: {point.y}',
          },
        },
      },
      series: [
        {
          name: 'Data',
          colorByPoint: true,
          data: this.data.map((d, i) => ({
            name: this.category[i] || `Item ${i + 1}`,
            y: d.data[0],
          })),
        },
      ],
    };
  }

  getDonutChartConfig(): any {
    return {
      chart: {
        type: 'pie',
      },
      title: { text: '' },
      tooltip: {
        pointFormat: '{series.name}: <b>{point.y}</b>',
      },
      plotOptions: {
        pie: {
          innerSize: '60%',
          dataLabels: {
            enabled: true,
            format: '<b>{point.name}</b>: {point.y}',
          },
        },
      },
      series: [
        {
          name: 'Data',
          colorByPoint: true,
          data: this.data.map((d, i) => ({
            name: this.category[i] || `Item ${i + 1}`,
            y: d.data[0],
          })),
        },
      ],
    };
  }


  ngOnDestroy(): void {
    this.subscription?.unsubscribe();
  }
}




