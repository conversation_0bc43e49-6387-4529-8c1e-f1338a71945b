
import { ChangeDetectionStrategy, Component, OnDestroy, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { Store } from '@ngrx/store';
import { headerKeys } from 'src/app/scad-insights/core/constants/header.constants';
import { IFPMsalService } from 'src/app/scad-insights/core/services/IFP-msal.service';
import { IfpSpinnerComponent } from 'src/app/scad-insights/ifp-widgets/ifp-molecules/ifp-spinner/ifp-spinner.component';
import { SubSink } from 'subsink';
@Component({
  selector: 'app-auth',
  templateUrl: './auth.component.html',
  styleUrls: ['./auth.component.scss'],
  imports: [IfpSpinnerComponent],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class AuthComponent implements OnInit, OnDestroy {

  private subs = new SubSink();
  private timout:any;

  constructor(private _msalService: IFPMsalService, private store: Store, private _route: Router) { }

  ngOnInit() {
    this.initMsal();
    localStorage.removeItem(headerKeys.cursor);
    localStorage.removeItem(headerKeys.fontSize);
    localStorage.removeItem(headerKeys.appearence);
    localStorage.removeItem('defaultSettings');
    this.clearLocalAndReload();
  }

  clearLocalAndReload() {
   this.timout = setTimeout(() => {
      localStorage.clear();
      window.sessionStorage.clear();
      window.location.reload();
    }, 30000);
  }

  async initMsal() {
    if (this._msalService.getCurrentUser) {
      if (!this._msalService.msalInit()) {
        await this._msalService.initialize();
      }
      this._msalService.acquireTokenSilent();
    } else if (this._msalService.getCurrentUser == null) {
      await this._msalService.initialize();
      await this._msalService.handleRedirectPromise();
      this._route.navigate(['/']);
    }
  }

  ngOnDestroy(): void {
    this.subs.unsubscribe();
    if (this.timout) {
      clearTimeout(this.timout);
    }
  }

}
