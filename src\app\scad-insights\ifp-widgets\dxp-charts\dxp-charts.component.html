
@if(data && data.length > 0) {
  <div class="ifp-chart-container">
    @if(showSeriesDropdown) {
      <div class="ifp-chart-dropdown">
        <app-ifp-dropdown 
          [dropDownItems]="seriesOptions"
          [key]="'name'"
          [selectedValue]="seriesOptions[selectedSeriesIndex]"
          [placeHolder]="'Select Series'"
          (dropDownItemClicked)="onSeriesSelectionChange($event)">
        </app-ifp-dropdown>
      </div>
    }
    @if(showCategoryDropdown) {
      <div class="ifp-chart-dropdown">
        <app-ifp-dropdown 
          [dropDownItems]="categoryOptions"
          [key]="'name'"
          [selectedValue]="categoryOptions[selectedCategoryIndex]"
          [placeHolder]="'Select Category'"
          (dropDownItemClicked)="onCategorySelectionChange($event)">
        </app-ifp-dropdown>
      </div>
    }
    <div class="ifp-high-chart" (click)="removeTooltip()">
      <highcharts-chart class="ifp-high-chart__wrapper" [ngClass]="chartClass" #chart
      [Highcharts]="this._highChart.highcharts"
      [options]="chartData"  (chartInstance)="charData($event)"  [(update)]="updateFlag"
      ></highcharts-chart>
    </div>
  </div>
}
  
  