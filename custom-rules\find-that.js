module.exports = {
  create: function(context) {
      return {
        ExpressionStatement(node){
          if(node.expression.type === "AssignmentExpression"){
            if(node.expression.left.name === "that" && node.expression.right.type === "ThisExpression"){
              context.report({
                node: node,
                hasSuggestions: true,
                message: "remove the that and use this"
             });
            }

          }
        },
        VariableDeclaration(node){
          if(node.declarations[0].id.name === "that" && node.declarations[0].init?.type === "ThisExpression"){
            context.report({
                   node: node,
                   hasSuggestions: true,
                   message: "remove the that and use this"
            });
          }
        }
      };
  }
};
