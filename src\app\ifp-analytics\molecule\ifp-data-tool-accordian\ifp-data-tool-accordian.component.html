<div class="ifp-accord-box" [ngClass]="{'ifp-accord-box--expand': isAccordianExpanded, 'ifp-accord-box--selection': selection, 'ifp-accord-box--active': active, 'ifp-accord-box--error': isError,
'ifp-accord-box--disabled': isdisableCheckbox}">
  <div class="ifp-accord-box__head"  (click)="isAccordianExpanded = !isAccordianExpanded;expandedEventEmit()" [ngClass]="{'ifp-accord-box__head--drag': isDraggable}">
    <div class="ifp-accord-box__head-left">
      @if (isDraggable) {
        <em class="ifp-icon ifp-icon-dragable"></em>
      }
      @if (enableCheckBox) {
        <app-ifp-check-box [translation]="!disableTranslation" class="ifp-accord-box__title" (click)="onClick($event)" [label]="disableTranslation ?title: (title | translate)" [checkedData]="isChecked" (checked)="onSelectItem($event)"
        [disabled]="isDisabled" [isDrag]="isDraggable"></app-ifp-check-box>
      } @else {
        <div class="ifp-accord-box__title">
          @if (icon) {
            <em class="ifp-icon" [class]="icon" [style.color]="iconColor"></em>
          }
          @if (edit) {
            <div (click)="editInput($event)">
              <app-ifp-input-edit (validValueChange)="cancelValueChange($event)" [vSmall]="true" [customEvent]="true"  (cancelEdit)="cancelEdit($event)" [data]="title"></app-ifp-input-edit>
            </div>
          } @else {
            <span class="ifp-accord-box__outer" [ngClass]="{'ifp-accord-box__outer--edit': isEdit}"> {{title | translate}}</span>

            @if (isEdit) {
              <em class="ifp-icon ifp-icon-edit ifp-accord-box__edit-icon"   (click)="editEvent($event)"></em>
            }

          }

        </div>
      }
    </div>
    <div>
      @if(deleteIcon) {
        <em class="ifp-icon {{deleteIcon}}"  (click)="deleteEvent($event)"></em>
      }
      @if (isAccordianExpanded) {
        <em class="ifp-icon ifp-icon-up-arrow ifp-accord-box__arrow"></em>
      }@else {
        <em class="ifp-icon ifp-icon-down-arrow ifp-accord-box__arrow"></em>
      }
    </div>

  </div>
  <div class="ifp-accord-box__body">
    <ng-content select=".ifp-accord-box__content"></ng-content>
  </div>
</div>
