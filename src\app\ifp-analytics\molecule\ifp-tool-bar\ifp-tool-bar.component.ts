import { buttonClass } from 'src/app/scad-insights/core/constants/button.constants';
import { Component, EventEmitter, Input, OnChanges, Output } from '@angular/core';
import { IfpToolDownloadComponent } from './ifp-tool-download/ifp-tool-download.component';
import { IfpToolBarChangeDriverComponent } from './ifp-tool-bar-change-driver/ifp-tool-bar-change-driver.component';
import { IfpButtonComponent } from '../../../scad-insights/ifp-widgets/ifp-atoms/ifp-button/ifp-button.component';
import { IfpToolBarDriverControllerComponent } from './ifp-tool-bar-driver-controller/ifp-tool-bar-driver-controller.component';
import { NgClass } from '@angular/common';
import { TranslateModule } from '@ngx-translate/core';
import { IfpOptionBoxComponent } from '../ifp-option-box/ifp-option-box.component';
import { DriverScenario } from '../../ifp-exploratory/ifp-scenario-details/ifp-scenario-details.component';
import { cloneDeep } from 'lodash';

@Component({
    selector: 'ifp-tool-bar',
    imports: [IfpToolDownloadComponent, IfpToolBarChangeDriverComponent, IfpButtonComponent,
        IfpToolBarDriverControllerComponent, NgClass, TranslateModule, IfpOptionBoxComponent],
    templateUrl: './ifp-tool-bar.component.html',
    styleUrl: './ifp-tool-bar.component.scss'
})
export class IfpToolBarComponent implements OnChanges{
  public buttonClass =buttonClass;
  @Input() driverList?: DriverScenario[] = [];
  @Input() data: Record<string, string | number>[] = [];
  @Input() enableEditDriver = true;
  @Input() name ='';
  public currentDriver: DriverScenario[] = [];

  @Output() dataRangeSelected =  new EventEmitter();
  @Output() resetToDefault =  new EventEmitter();
  @Output() driverUpdated =  new EventEmitter();
  public editDriver = false;

  ngOnChanges(): void {
    this.currentDriver = cloneDeep(this.driverList ?? []);
  }

  // user to change data range
  rangeSelected(value: { id: string; value:string, index: number }, item: DriverScenario, _driveIndex: number) {
    this.dataRangeSelected.emit({event: {value: value, item: item, _driveIndex: _driveIndex}} );
  }

  // used to switch between edit driver and change driver
  editDriverEvent() {
    if (!this.editDriver) {
      this.currentDriver = cloneDeep(this.driverList ?? []);
    }
    this.editDriver = !this.editDriver;
  }

  // save button event
  save() {
    this.driverList =  this.currentDriver;
    this.editDriver = false;
    this.driverUpdated.emit( this.currentDriver);
  }

  // reset Event emit
  ResetToDefault() {
    this.resetToDefault.emit();
  }
}
