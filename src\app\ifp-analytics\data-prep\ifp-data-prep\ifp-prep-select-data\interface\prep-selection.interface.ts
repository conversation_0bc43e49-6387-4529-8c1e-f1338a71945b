import { Comparator } from 'src/app/ifp-analytics/organism/ifp-adv-filter-tool/ifp-adv-filter-tool.component';

export interface PrepColumnInterface {
  name: string;
  type: string;
  allowed_types_availability: boolean;
  allowed_types: string[];
  allowed_type_probability_availability: boolean;
  type_probability: Typeprobability;
  isSelected?: boolean;
  order?: number;
}

interface Typeprobability {
  ID: number;
  Boolean: number;
  Numeric: number;
  Categorical: number;
}


export interface ColumnUpstream {
  columns_1: ColumnPrep[];
  preview_1:Record<string, string> [];
  null_columns: Nullcolumn[];
}

export interface Nullcolumn {
  name: string;
  type: string;
  metadata: Record<string, string>[];
  nullable: boolean;
  null_count: number;
  sample_value: string;
  null_percentage: number;
  impute_options: {
    label: string,
    method: string
  }[]
}


export interface ColumnPrep {
  name: string;
  type: string;
  metadata?: any;
  nullable?: boolean;
  null_count?: number;
  sample_value?: number | string;
  null_percentage?: number;
  aggregate_options?: ToolOptions[];
  isSelected?: boolean;
  allowed_types_availability?: boolean;
  order?: number;
  allowed_types?: string[];
  currentType?: {name: string; value:string;};
  currentName?: string;
  aggregate_field?: string;
  method?: string;
  priority?: string;
  column_name?: string;
  prefix?: string;
  suffix?: string;
  checked?:boolean;
  rename?:string;
  filter_options?:Comparator[];
  impute_options?: ToolOptions[];
  customImputeValue?: string;
  user_specified?: string;
  mapedColumn?: any;
  hide?:boolean;
  isHide?:boolean;
}

export interface DuplicatedColumn {
  name: string;
  count: number;
  increment: number;
}

export interface ToolOptions {
  label: string;
  method: string
}

