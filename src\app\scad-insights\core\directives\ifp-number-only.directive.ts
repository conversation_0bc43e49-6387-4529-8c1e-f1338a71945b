import { Directive, ElementRef, Input, HostListener, OnChanges } from '@angular/core';

@Directive({
  selector: '[appNumberOnly]',
  standalone: true
})
export class IfpNumberOnlyDirective implements OnChanges {
  @Input() min!: number;
  @Input() max!: number;
  @Input() decimal!: boolean;
  @Input() number = false;
  // flag for shift focus to next field on tabkey press
  @Input() shiftFocusOnTabKey: boolean = false;
  private regex: RegExp = new RegExp('^[0-9]*$');
  private specialKeys: string[] = ['Backspace', 'ArrowLeft', 'ArrowRight'];
  constructor(private elementRef: ElementRef) {

  }

  /**
   * Key board action
   * @param event
   */
  @HostListener('keydown', ['$event']) onKeyDown(event: KeyboardEvent) {
    // shift focus to next field if user presses tab key (for accessibility)
    if (event?.key === 'Tab') {
      return this.shiftFocusOnTabKey;
    }
    if (this.specialKeys.includes(event.key)) {
      return;
    }
    const inputValue: string = this.elementRef.nativeElement.value.concat(event.key);
    if (inputValue && !String(inputValue).match(this.regex) && !event.ctrlKey) {
      event.preventDefault();
    }
    if (this.number) {
      if (this.min && inputValue) {
        if (+inputValue < this.min) {
          event.preventDefault();
        }
      }
      if (this.max && inputValue) {
        if (+inputValue > this.max) {
          event.preventDefault();
        }
      }
    } else {
      if (this.min && inputValue) {
        if (+inputValue.length < this.min) {
          event.preventDefault();
        }
      }
      if (this.max && inputValue) {
        if (+inputValue.length > this.max) {
          event.preventDefault();
        }
      }
    }

    return;
  }

  /**
   * Copy Paste action
   * @param event
   */
  @HostListener('paste', ['$event']) onPaste(event: { originalEvent: any; preventDefault: () => void; }) {
    const clipboardData = (event.originalEvent || event).clipboardData.getData('text/plain');
    if (clipboardData) {
      const regEx = this.regex;
      if (!regEx.test(clipboardData)) {
        event.preventDefault();
      }
    }
    return;
  }

  ngOnChanges(): void {
    if (this.decimal) {
      this.regex = new RegExp('^[0-9]*\\.?[0-9]*$');
    }
  }
}
