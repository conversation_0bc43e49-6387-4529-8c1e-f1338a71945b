@use "../../../../assets/ifp-styles/abstracts/index" as *;

.ifp-negative-progress {
  display: flex;
  &__bar-outer-left , &__bar-outer-right {
    display: flex;
    align-items: center;
    width: 50%;
    padding: $spacer-1 $spacer-0;
    color: $ifp-color-secondary-grey;
    font-size: $ifp-fs-2;
    font-weight: $fw-medium;
    min-height: 24px;
  }
  &__bar-outer-left {
    justify-content: right;
    padding-inline-start: $spacer-2;
    border-right: 1px solid $ifp-color-grey-13;
    .ifp-negative-progress  {
      &__bar{
        border-radius: 6px 3px 3px 6px;
        margin-left: $spacer-8;
      }
    }
  }
  &__bar-outer-right {
    justify-content: left;

    padding-inline-end: $spacer-2;
    border-left: 1px solid $ifp-color-grey-13;
    .ifp-negative-progress  {
      &__bar{
        border-radius: 3px 6px 6px 3px;
        margin-right: $spacer-8;
      }
    }
  }
  &__wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  }
  &__bar{
    height: 16px;
    width: 0;
    transition: width .3s;
  }
}

:host-context([dir="rtl"]) {
  .ifp-negative-progress {
    direction: ltr;
  }
}
