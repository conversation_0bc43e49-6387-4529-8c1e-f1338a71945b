@use "../../../../assets/ifp-styles/abstracts/index" as *;
:host{
  display: block;
}
.ifp-ai-input {
  &__inp {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: $spacer-1 $spacer-3;
    background-color: transparent;border: 1px solid $ifp-color-grey-2;
    cursor: pointer;
    border-radius: 5px;
    width: 100%;
  }
  &__icon {
    margin-inline-end: $spacer-2;
  }
  &__textarea{
    display: block;
    width: 100%;
    resize: none;
    min-height: 150px;
    background-color: $ifp-color-white;
  }
  &--ai {

    background-image: linear-gradient(158deg, #126DEF,#3EBEF2, #8A7DFA,#0154E9);
    border-radius: 5px;
    padding: 3px;
    overflow: hidden;

    .ifp-ai-input {
      &__outer{
        background-color: $ifp-color-section-white;
        border-radius: 4px;
      }

    &__inp {
      width: 100%;
      background-size: cover;
      color: black;
      border: 1px solid transparent;
      border-radius: 10px;
      object-fit: cover;
      background-size: cover;
      background-repeat: no-repeat;     }}
      &--30 {
        background-image: radial-gradient(ellipse at top left, #126DEF4d -50%, #3EBEF24d 30%, #8A7DFA4d 60%, #0154E94d 100%);
      }
      &--bordered {
        .ifp-ai-input {
        &__outer-wrap  {
          padding:2px;
          border-radius: 5px;
          background-image: radial-gradient(ellipse at top left, #126DEF -50%, #3EBEF2 30%, #8A7DFA 60%, #0154E9 100%);
        }
      }
      }

  }
  &--animation {
    background-size: 200% 200%;
    background-position: 0% 0%, 100% 100%;
    animation: gradientShift 300ms ease-in-out infinite;
  }
  &--disable {
    pointer-events: none;
    opacity: .9;
  }
  &--ai-secondary{
    background-image: radial-gradient(ellipse at top left, #126DEF -50%,#3EBEF2 31%, #8A7DFA 60%,#0154E9 100%);
    border-radius: 5px;
    padding: 3px;
    overflow: hidden;

.ifp-ai-input {
    &__inp {
      border: 0;
      color: $ifp-color-white-global;
    }
  }

  }
  &--ai-round{
    padding: 3px;
    border-radius: 5px;
    .ifp-ai-input {
      &--ai {
        border-radius:  5px;
        padding: $spacer-1;
      }
      &__outer {
        border-radius:  5px;
      }
    }

  }
  &--round {
    .ifp-ai-input {
      &__inp {
    border-radius: 50px;
      }}
  }

  &--white {
    .ifp-ai-input {
      &__inp {
        color: $ifp-color-white;
        border: 1px solid $ifp-color-white;
      }
    }
  }
  &--border-only {
    .ifp-ai-input {
      &__outer-wrap {
        border: 1px solid $ifp-color-blue-menu;
        padding: 0;
        background-color: transparent;
        background-image: none;
      }
      &__textarea {
        border-radius: 5px;
            border: none;
      }
    }
  }
}

:host-context(.ifp-dark-theme) {
  .ifp-ai-input {
    &__textarea {
      color: $ifp-color-black;
    }
  }
}
@keyframes gradientShift {
  0% {
    background-position: 0% 0%, 100% 100%;
    background-size: 200% 200%;
  }
  50% {
    background-position: 50% 50%, 50% 50%;
    background-size: 250% 250%;
  }
  100% {
    background-position: 0% 0%, 100% 100%;
    background-size: 200% 200%;
  }
}

:host-context(.ifp-dark-theme) {
  .ifp-ai-input {
   &__inp , &__textarea{
    color: $ifp-color-black;
   }

  }
}
