@use "../../../assets/ifp-styles/abstracts/index" as *;
.ifp-gen-ai-dashboard-run-preview{
  border: 1px solid $ifp-color-grey-7;
  overflow: hidden;
  border-radius: 10px;
  &__question-label {
    display: block;
    font-size: $ifp-fs-3;
    color: $ifp-color-grey-14;
    margin-bottom: $spacer-2;
  }
  &__question-wrapper {
  padding: $spacer-4;
  }
  &__file-input {
    margin-bottom: $spacer-4;
    color: $ifp-color-black;
    background-color: $ifp-color-white;
    width: 100%;
    font-size: $ifp-fs-5;
    border-radius: 5px;
    border: 1px solid $ifp-color-grey-7;
    padding: $spacer-2;
    resize: none;

  }
  &__question-wrapper {
    display: flex;
  }
  &__icon-blue {
    color: $ifp-color-blue-hover;
  }
  &__footer-left {
        color: $ifp-color-grey-14;
        margin-inline-end: $spacer-7;
        min-width: 175px;
  }
   &__footer-right {
    margin-top: -14px;
    }
    &__question-outer {
      width: 100%;
    }
    &__question-expand {
      margin-inline-start: $spacer-6;
    }
      &__footer {
    padding: $spacer-4;
    background-color: $ifp-color-grey-18;
    display: flex;
  }
    &__feed-resp-inner {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid $ifp-color-grey-7;
  }

  &__feed-resp {
    margin-inline-start: auto;
    display: flex;
    align-items: center;
    &--active{
     background-color: $ifp-color-blue-1;
          color: $ifp-color-primary-blue;
          border: 1px solid $ifp-color-blue-menu;


    }
  }
&__feed-resp-icon{
    position: relative;
    top: 2px;

  }
  &--close {
    .ifp-gen-ai-dashboard-run-preview {
      &__file-input {
        margin-bottom: $spacer-0;
      }
       &__question-wrapper {
        padding-bottom: $spacer-3;
       }
    }
  }
  }
