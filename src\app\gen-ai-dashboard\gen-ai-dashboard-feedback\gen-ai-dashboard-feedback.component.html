<div class="ifp-gen-ai-dashboard-feedback">
  <div  class="ifp-gen-ai-dashboard-feedback__icon-cross" (click)="close.emit()" >
    <em class="ifp-icon ifp-icon-cross"></em>
  </div>
  <div  class="ifp-gen-ai-dashboard-feedback__icon-wrapper" >
    <span class="ifp-gen-ai-dashboard-feedback__icon-inner">
      <em class="ifp-icon-feather-edit ifp-icon ifp-gen-ai-dashboard-feedback__icon"></em>
    </span>

  </div>
  <h3 class="ifp-gen-ai-dashboard-feedback__header">{{'Provide Additional Feedback' | translate}}</h3>
  <p class="ifp-gen-ai-dashboard-feedback__sub-heading">
    {{'Share your thoughts to help us improve! Your feedback is valuable in enhancing our products and services for a better experience.' | translate}}
  </p>
  <textarea class="ifp-gen-ai-dashboard-feedback__textarea" [formControl]="feedback" [placeholder]="'Feel free to add specific details'"></textarea>
  <div class="ifp-gen-ai-dashboard-feedback__btn-wrapper">
    <ifp-button class="ifp-gen-ai-dashboard-feedback__btn"  [label]="'Cancel'" [buttonClass]="buttonClass.transpreantBlue" (ifpClick)="close.emit()"></ifp-button>
    <ifp-button class="ifp-gen-ai-dashboard-feedback__btn" [label]="'Submit'" (ifpClick)="submit.emit(feedback.value ?? '')"  [buttonClass]="buttonClass.hoverBlue"  [iconClass]=" 'ifp-icon-rightarrow'"></ifp-button>
  </div>
</div>
