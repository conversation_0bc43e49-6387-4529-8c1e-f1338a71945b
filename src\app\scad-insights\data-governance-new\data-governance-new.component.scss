@use '../../../assets/ifp-styles/abstracts' as *;

:host {
  display: block;
}

.ifp-d-govenance {
  margin-bottom: $spacer-5;
  &__header {
    text-align: center;
    align-items: center;
  }

  &__title {
    font-size: $ifp-fs-13;
    font-weight: $fw-bold;
    margin-bottom: $spacer-2;
  }

  &__description {
    color: $ifp-color-grey-9;
    font-size: $ifp-fs-4;
    line-height: 1.4;
  }

  &__breadcrumbs {
    margin-bottom: $spacer-5;
  }

  &__tab {
    display: flex;
    justify-content: center;
    margin: ($spacer-4 + 6) auto;
  }

  &__usage-wrapper {
    background-color: $ifp-color-white;
    padding-top: $spacer-3;
    border-top: 2px solid $ifp-color-pale-grey;
    border-bottom-left-radius: 20px;
    border-bottom-right-radius: 20px;
    box-shadow: 0 -57px 50px -60px rgba(212, 220, 229, 0.46);
    position: relative;
    z-index: 1;
  }

  &__usage {
    display: flex;
  }

  &__usage-title {
    font-size: $ifp-fs-6;
    font-weight: $fw-bold;
    text-align: center;
  }

  &__usage-card {
    margin: $spacer-4;
    width: 14.28%;
  }

  &__chart-table-wrapper {
    display: flex;
    margin: $spacer-4 $spacer-0;
  }

  &__chart-table {
    width: 50%;

    &:first-child {
      margin-right: $spacer-4;
    }
  }

  &__tab-wrapper {
    background-color: $ifp-color-white;
    padding: $spacer-5 $spacer-5 $spacer-3;
    border-radius: 20px;
  }

  &__filter-wrapper {
    display: flex;
    align-items: flex-end;
    .ifp-icon {
      font-size: $ifp-fs-6;
      border: 1px solid $ifp-color-grey-9;
      height: 35px;
      width: 35px;
      border-radius: 50%;
      position: relative;
      display: inline-flex;
      align-items: center;
      justify-content: center;
      top: 1px;
    }
  }

  &__filter-grp {
    display: flex;
    flex-wrap: wrap;
  }

  &__filter-dropdown,
  &__search-wrapper {
    margin: $spacer-1 $spacer-3 $spacer-1 $spacer-0;
  }

  &__tab-view-header {
    font-size: $ifp-fs-6;
    font-weight: $fw-bold;
    margin-bottom: $spacer-3;
  }

  &__table-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  &__download-btn {
    margin-inline-start: $spacer-4;
  }

  &__detail-table {
    display: block;
    width: 100%;
    @include ifp-scroll-x(transparent, $ifp-color-grey-7, 10px, 10px);
    &::ng-deep {
      .ifp-data-table {
        &__col {
          max-width: 300px;
        }
        &__row:last-child {
          border: none;
        }
        &__row--head .ifp-data-table__col{
          white-space: nowrap;
        }
      }
    }
  }

  &__detail-table-wrapper {
    border: 1px solid $ifp-color-grey-3;
    padding: 8px;
    border-radius: 20px;
    margin-top: $spacer-5;
    margin-bottom: $spacer-4;
  }

  &__pagination {
    margin-bottom: $spacer-2;
  }

  &__values-wrapper {
    margin-bottom: $spacer-5;

    &--radius {
      display: block;
      border-radius: 20px;
    }
  }

  &__stat-diagram {
    margin-bottom: $spacer-5;
  }

  &__search-title {
    margin: 8px 0;
    color: $ifp-color-grey-2;
    font-size: 1.2rem;
    font-weight: 600;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  &__search-wrapper {
    margin-right: $spacer-3;
  }

  &__event-detail-modal {
    border-radius: 10px;
    background-color: $ifp-color-white;
    width: 100%;
    max-width: 900px;
  }
  &__no-data {
    margin-top: $spacer-4;
  }
}



:host::ng-deep {
  .ifp-d-govenance {
    .ifp-tab {
      padding: $spacer-1;
      border-radius: 5px;
      border: 1px solid $ifp-color-grey-7;

      &__item {
        border: none;
        margin: $spacer-0 (
          $spacer-1 - 2
        );

      &--active,
      &:hover {
        border-radius: 5px;
      }
    }
  }

  .ifp-dropdown,
  .ifp-panel-dropdown {
    min-width: 250px;
  }

  &__search {
    display: block;
    .ifp-search-box {
      height: auto;
      &__input {
        padding: $spacer-2;
      }
    }
  }

  &__filter-dropdown--cols {
    .ifp-panel-dropdown {
      &__label {
        margin: $spacer-2 $spacer-0;
        color: $ifp-color-grey-2;
        font-size: $ifp-fs-2;
        font-weight: $fw-semi-bold;
      }
      &__selected {
        font-size: $ifp-fs-3;
      }
      &__selected-count {
        font-size: $ifp-fs-1;
        width: 19px;
        min-width: 19px;
        height: 19px;
        line-height: 19px;
      }
    }

  }

  .ifp-data-table__row:last-child {
    border: none;
  }
}
}
