export interface InsightReport {
  keyDriversData: KeyDriversData;
}

export interface KeyDriversData {
  domain: Domain;
  quarter: string;
  key_drivers: Keydrivers;
  impact: Impact;
  key_insights: KeyinsightInsights[];
  comparison_one: Comparisonone;
  comparison:InsightComparison;
  sentiment: InsightSentimentanalysis;
  key_insights_desc: string;
  report_id: number;
  report_type: string;
  published_date: string;
  status?: string;
  workFlow: workflow;
}

export interface InsightSentimentanalysis {
  title: string;
  overall_impact: string;
  data_source: string;
  meta_data: string;
  key?:string;
  overall_impact_status: Record<string, Overallimpactstatus>;
  chart: chartInsightsReport
  key_insights_desc: string;
}

export interface Overallimpactstatus {
  status_name: string;
  value: string;
  color: string;
  key: string;
}





interface Header {
  name: string;
  key: string;
}

interface Comparisonone {
  chartConfig: ChartConfig;
  textComparison: string;
}

interface ChartConfig {
  Custom_Charts: CustomChart[];
  isTimePeriod: boolean;
  dashboardTitle: string;
}

interface CustomChart {
  name: string;
  data: number[];
  dashStyle: string;
  type: string;
  zIndex: number;
  lineWidth: number;
  color: string;
  marker: Marker;
  category: string[];
  yAxisLabel: string;
  xAxisLabel: string;
}

interface Marker {
  fillColor: string;
  lineWidth: number;
  lineColor: string;
  symbol: string;
  enabled: boolean;
}

export interface KeyinsightInsights {
  heading: string;
  text: string;
  whether_positive_trend: boolean;
  chart?: chartInsightsReport
}

interface Impact {
  heading: string;
  text: string;
  chart: chartInsightsReport
}

export interface Keydrivers {
  ai_summary: string;
  drivers: Driver[];
}

export interface Driver {
  indicator_name: string;
  heading: string;
  value: number;
  whether_positive_trend: boolean;
}

interface Domain {
  name: string;
  id: number;
}


export interface ListInsights{
  headers: Header[];
  data: Datum[];
}

interface Datum {
  id: string;
  report_name: string;
  quarter: string;
  status:  {
    color: string;
    value: string;
  };
  created_date: string;
  }

interface Status {
  color: string;
  value: string;
}

interface Header {
  name: string;
  key: string;
}

export interface chartInsightsReport {
  // Custom_Charts?: CustomChart[];
  charts?: CustomChart[];
  isTimePeriod: boolean;
  dashboardTitle: string;
}

interface CustomChart {
  name: string;
  data: number[];
  dashStyle: string;
  type: string;
  zIndex: number;
  lineWidth: number;
  color: string;
  marker: Marker;
  category: string[];
  yAxisLabel: string;
  xAxisLabel: string;
}

interface Marker {
  fillColor: string;
  lineWidth: number;
  lineColor: string;
  symbol: string;
  enabled: boolean;
}


export interface GenrateKeyInsights {
  sub_heading: string;
  description: string;
  key_insights: Keyinsight[];
}

export interface Keyinsight {
  heading: string;
  text: string;
  whether_positive_trend: boolean;
}

export interface ResponseInsight {
  message: string
}

export interface GenarateDriver {
  description: string;
  card_view: Cardview[];
}

export interface Cardview {
  indicator_name: string;
  heading: string;
  value: number;
  whether_positive_trend: boolean;
}


export interface InsightComparison {
  headers: string[];
  rows:Record<string, any>[];
}


export interface InsightPercentagechange {
  value: number;
  progress: boolean;
}

export interface workflow {
  stepNames: string[];
  currentStep: string;
}