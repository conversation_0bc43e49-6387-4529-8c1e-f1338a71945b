<div class="ifp-container">
  <app-ifp-breadcrumbs [pageData]="pageData()"></app-ifp-breadcrumbs>
</div>
<div class="ifp-container">
  <div class="ifp-dxp-visualization-wizard">
    <div class="ifp-dxp-visualization-wizard__header-wrap">
      <h2 class="ifp-dxp-visualization-wizard__header">
        {{'Visualization wizard' | translate}}
      </h2>
      <div class="ifp-dxp-visualization-wizard__header-btns">
        <ifp-button class="ifp-dxp-visualization-wizard__header-btn" [label]="'Cancel'" [url]="'/dxp'" [link]="true"
          [buttonClass]="buttonClass.secondary +' '+ buttonClass.normalAplabetic"></ifp-button>
        @if(!userView()) {
          <ifp-button class="ifp-dxp-visualization-wizard__header-btn" [label]="'Procced'" (ifpClick)="openPopup()"
          [buttonClass]="buttonClass.hoverBlue+' '+ buttonClass.normalAplabetic" [iconClass]="'ifp-icon-rightarrow'">
        </ifp-button>
      }@else{
        <ifp-button class="ifp-dxp-visualization-wizard__header-btn" [label]="'Send for Approval'" (ifpClick)="submitForApproval()"
          [buttonClass]="buttonClass.hoverBlue+' '+ buttonClass.normalAplabetic" [iconClass]="'ifp-icon-rightarrow'">
        </ifp-button>}
      </div>

    </div>
    @if (userView()) {
      <ifp-dxp-user-config class="ifp-dxp-visualization-wizard__dxp-user" [userConfig]="userConfigData()" #userConfigComponent></ifp-dxp-user-config>
    } @else {
      <div class="ifp-dxp-visualization-wizard__section">
        <div class="ifp-dxp-visualization-wizard__section-left">


          <h6 class="ifp-dxp-visualization-wizard__title">
            {{'Select Dataset' | translate}}
          </h6>

            <ifp-dxp-dataset-card [ngClass]="{'ifp-dxp-visualization-wizard--diplay-none': dataset().length == 0}" [(datasetList)]="dataset" [title]="selectedProduct.title" #ref [connectedto]="[ drag1.getRef(), drag2.getRef() ]">

            </ifp-dxp-dataset-card>
            @if(datasetLoading()) {
              <app-ifp-card-loader  [type]="'large'"></app-ifp-card-loader>
            }
            @if (dataset().length == 0 && !datasetLoading()) {

                <ifp-button class="ifp-dxp-visualization-wizard__add-btn" [buttonClass]="buttonClass.round +' '+buttonClass.secondary+' '+ buttonIconPosition.left" [label]="'Add Dataset'" [iconClass]="'ifp-icon-plus-round'" (click)="addDataset()"></ifp-button>

              <div  class="ifp-dxp-visualization-wizard__no-data">Added assets will appear here</div>
            }


        </div>
        <div class="ifp-dxp-visualization-wizard__section-right">
          <div class="ifp-dxp-visualization-wizard__heading-source">
            <h6 class="ifp-dxp-visualization-wizard__heading-right">
              {{'Source Filter' | translate}}
            </h6>
            <app-ifp-info class="ifp-dxp-visualization-wizard__head-info" [infoContent]="'info'"></app-ifp-info>
          </div>
          @if (fullColumnsData().length > 0 && comparators().length > 0) {
          <div class="ifp-dxp-visualization-wizard__sub-sec" >
            <div class="ifp-dxp-visualization-wizard__dropdown-wrapper">
              <div class="ifp-dxp-visualization-wizard__dropdown-list">
                <!-- <app-ifp-dropdown class="ifp-dxp-visualization-wizard__dropdown" [title]="'Select Column'"
                  [showTitle]="true" [key]="'name'" [dropDownItems]="fullColumnsData()"
                  (dropDownItemClicked)="onColumnSelected($event)"></app-ifp-dropdown>
                <app-ifp-dropdown class="ifp-dxp-visualization-wizard__dropdown" [title]="'Select Comparator'"
                  [showTitle]="true" [key]="'display_name'" [dropDownItems]="comparators()"></app-ifp-dropdown>
               <div class="ifp-dxp-visualization-wizard__input ifp-dxp-visualization-wizard__dropdown">
                <label class="ifp-dxp-visualization-wizard__input-label">{{'Value' | translate}}</label>
                  <input class="ifp-input ifp-dxp-visualization-wizard__input-field" placeholder="Enter Value">
                 </div> -->
                 <form [formGroup]="filterForm">
                 <div formArrayName="filters">
                    <div [formGroupName]="0" class="ifp-dxp-visualization-wizard__dropdown-list">

                      <app-ifp-dropdown
                        class="ifp-dxp-visualization-wizard__dropdown"
                        [title]="'Select Column'"
                        [showTitle]="true"
                        [key]="'name'"
                        [dropDownItems]="fullColumnsData()"
                        [selectedValue]="filters.at(0).get('column')?.value"
                        [singleDefaultSelect]="true"
                        (dropDownItemClicked)="onColumnSelectedForFilter($event, 0)"
                      ></app-ifp-dropdown>

                      <app-ifp-dropdown
                        class="ifp-dxp-visualization-wizard__dropdown"
                        [title]="'Select Comparator'"
                        [showTitle]="true"
                        [key]="'display_name'"
                        [dropDownItems]="getComparatorsForColumn(filters.at(0).get('column')?.value)"
                        [selectedValue]="filters.at(0).get('comparator')?.value"
                        [singleDefaultSelect]="true"
                        (dropDownItemClicked)="filters.at(0).get('comparator')?.setValue($event)"
                      ></app-ifp-dropdown>

                      <div class="ifp-dxp-visualization-wizard__input ifp-dxp-visualization-wizard__dropdown">
                        <label class="ifp-dxp-visualization-wizard__input-label">{{'Value' | translate}}</label>
                        <input class="ifp-input ifp-dxp-visualization-wizard__input-field" placeholder="Enter Value" formControlName="value">
                      </div>

                    </div>
                  </div>
                </form>
                </div>
                @if(selectedFilterIndex >= 0) {
                  <div class="ifp-dxp-visualization-wizard__btn-sec">
                    <ifp-button [buttonClass]="buttonClass.secondary" class="ifp-dxp-visualization-wizard__filter-btn"
                    [label]="'Cancel'" (ifpClick)="onCancelFilterEdit()"></ifp-button>

                    <ifp-button [buttonClass]="buttonClass.hoverBlue" class="ifp-dxp-visualization-wizard__filter-btn"
                    [label]="'Update Filter'" (ifpClick)="addFilter()"></ifp-button>
                  </div>
                } @else {
                  <div class="ifp-dxp-visualization-wizard__btn-sec">
                  <ifp-button [buttonClass]="isAddButtonDisabled() ? buttonClass.disabled : buttonClass.hoverBlue" class="ifp-dxp-visualization-wizard__filter-btn"
                  [label]="'Add Filter'" (ifpClick)="addFilter()" [iconClass]="'ifp-icon-plus-light'"></ifp-button>
                </div>
                }
            </div>
            <span class="ifp-link ifp-dxp-visualization-wizard__filter" (click)="clearFilters()">{{'Remove Filters' | translate}}</span>
            <div class="ifp-dxp-visualization-wizard__filter-list">
              @for (filter of addedFilters(); track $index; let i = $index) {
                <ifp-dxp-filter-badge class="ifp-dxp-visualization-wizard__filter-item"
                  [title]="'Filter ' + (i + 1)"
                  [id]="i"
                  [showRemove]="true"
                  (removeFilter)="removeFilter($event)"
                  (editFilter)="onEditFilter(i)"
                  [column]="filter.column?.name || filter.column"
                  [comparator]="filter.comparator?.display_name || filter.comparator"
                  [value]="filter.value"
                  [isSelected]="selectedFilterIndex === i">
                </ifp-dxp-filter-badge>
              }
            </div>

          </div>
        }
          <div class="ifp-dxp-visualization-wizard__box ifp-dxp-visualization-wizard__box--sub-sec">
            <div [formGroup]="axisForm">
              <div class="ifp-dxp-visualization-wizard__heading-source">
                <h6 class="ifp-dxp-visualization-wizard__heading-right">
                  {{'Select X-Variable and Y-Variable' | translate}}
                </h6>
                <app-ifp-info class="ifp-dxp-visualization-wizard__head-info" [infoContent]="'info'"></app-ifp-info>
              </div>
              <div class="ifp-dxp-visualization-wizard__axis-lebal-wrap">
                <div class="ifp-dxp-visualization-wizard__axis-lebal">{{'X-Variable'}}</div>
                <input class="ifp-input ifp-dxp-visualization-wizard__axis-input" formControlName="xAxisLabel" placeholder="Enter X Axis Label" name="xAxisLabel">
                <ifp-dxp-label-drag
                  #drag1
                  [limit]="1"
                  [connectedto]="[ref.getRef()]"
                  class="ifp-dxp-visualization-wizard__axis-labe-drag"
                  [isDragging]="ref.isDragging()"
                  [dropdownList]="xVariable()"
                  [axisControl]="xAxisVariableControl"
                  (dropEvent)="onXVariableDrop($event)"
                  (aggregationSelected)="onXVariableAggregationSelected($event.aggregation)">
                </ifp-dxp-label-drag>
              </div>
              <div class="ifp-dxp-visualization-wizard__axis-lebal-wrap">
                <div class="ifp-dxp-visualization-wizard__axis-lebal">{{'Y-Variable'}}</div>
                <input class="ifp-input ifp-dxp-visualization-wizard__axis-input" formControlName="yAxisLabel" placeholder="Enter Y Axis Label" name="yAxisLabel">
                <ifp-dxp-label-drag
                  #drag2
                  [limit]="1"
                  [connectedto]="[ref.getRef()]"
                  class="ifp-dxp-visualization-wizard__axis-labe-drag"
                  [isDragging]="ref.isDragging()"
                  [dropdownList]="yVariable()"
                  [axisControl]="yAxisVariablesControl"
                  (dropEvent)="onYVariableDrop($event)"
                  (aggregationSelected)="onYVariableAggregationSelected($event.aggregation, $event.index)">
                </ifp-dxp-label-drag>
              </div>
              <div style="display: flex; justify-content: flex-end; margin-top: 16px;">
                <ifp-button [label]="'Submit'" [buttonClass]="buttonClass.primary" (ifpClick)="onSubmitClick()"></ifp-button>
              </div>
            </div>
          </div>
          <div class="ifp-dxp-visualization-wizard__box ifp-dxp-visualization-wizard__box--preview">
              <ifp-dxp-visualization-preview [selectedProductDetails]="selectedProduct" [columnData]="fullColumnsData()" [graphData]="graphData" [noData]="!graphData" [xAxisLabel]="xAxisLabel" [yAxisLabel]="yAxisLabel" [isLoading]="graphDatasetLoading()" (kpiCardFiltersChanged)="onKpiCardFiltersChanged($event)" (submitRequested)="onPreviewSubmitRequested()" (legendsDataChanged)="onLegendsDataReceived($event)"></ifp-dxp-visualization-preview>
          </div>
        </div>
      </div>
    }

  </div>
</div>

<app-ifp-modal #modal [overlayClass]="'ifp-modal__bg-grey'">
  <ifp-dxp-popup (close)="closeModal()" (selectedProductDetail)="onSelectedProduct($event)" (searchAsset)="onSearchAsset($event)" [loader]="productListLoader" [assetList]="productdetailList"></ifp-dxp-popup>
</app-ifp-modal>
<app-ifp-modal #modalPopUp [overlayClass]="'ifp-modal__bg-grey'">
  <ifp-dxp-validation-pop-up [successPopup]="false" (close)="closePopup()"></ifp-dxp-validation-pop-up>
</app-ifp-modal>

