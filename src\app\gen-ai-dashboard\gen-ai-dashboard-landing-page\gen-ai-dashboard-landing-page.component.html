<div class="ifp-gen-ai-dashboard-landing-page">
  <div class=" ifp-container " >
    <div class="ifp-gen-ai-dashboard-landing-page__top">
        <app-ifp-dropdown (dropDownItemClicked)="dropdownVersion($event)"  class="ifp-gen-ai-dashboard-landing-page__dropdown-version" [dropDownItems]="modalDropdown() ?? []" [key]="'value'" ></app-ifp-dropdown>
      <ifp-gen-ai-dashboard-date-picker [startDate]="startForm" [endDate]="endForm" (date)="date($event)" class="ifp-gen-ai-dashboard-landing-page__date-picker"></ifp-gen-ai-dashboard-date-picker>
      <!-- <ifp-panel-dropdown  class="ifp-gen-ai-dashboard-landing-page__filter-picker" [icon]="'ifp-icon ifp-icon-filter-2'" [textValue]="'Filter by'" ></ifp-panel-dropdown> -->
    </div>
    <section  class="ifp-gen-ai-dashboard-landing-page__sec-1">
  <div class="ifp-gen-ai-dashboard-landing-page__card ifp-gen-ai-dashboard-landing-page__card--number ifp-gen-ai-dashboard-landing-page__card--25">
        <div  class="ifp-gen-ai-dashboard-landing-page__header">
          {{'Number Of Evaluated Questions' | translate }}
        </div>
       <ifp-gen-ai-dashboard-qus-arc [value]="averageQuestion() | shortNumber"></ifp-gen-ai-dashboard-qus-arc>
      </div>
      <div class="ifp-gen-ai-dashboard-landing-page__card ifp-gen-ai-dashboard-landing-page__card--25">
        <div  class="ifp-gen-ai-dashboard-landing-page__header">
          {{'Accuracy' | translate }}
        </div>
           <div class="ifp-gen-ai-dashboard-landing-page__guage">
             <ifp-highcharts class="ifp-gen-ai-dashboard-landing-page__high-chart" [data]="analyticMetrixGauge[analyticMatrixOptions.evalAnsAccuracy]" [height]="165" [chartName]="'gaugeChart'"  [comparisonEnable]="false"></ifp-highcharts>
           </div>

      </div>

      <div class="ifp-gen-ai-dashboard-landing-page__card ifp-gen-ai-dashboard-landing-page__card--25">
        <div  class="ifp-gen-ai-dashboard-landing-page__header">
          {{'Relevancy' | translate }}
        </div>
        <div class="ifp-gen-ai-dashboard-landing-page__guage">
        <ifp-highcharts class="ifp-gen-ai-dashboard-landing-page__high-chart" [data]="analyticMetrixGauge[analyticMatrixOptions.evalAnsRelevance]" [height]="165" [chartName]="'gaugeChart'" [comparisonEnable]="false"></ifp-highcharts>
        </div>

      </div>
  <div class="ifp-gen-ai-dashboard-landing-page__card ifp-gen-ai-dashboard-landing-page__card--25">
        <div  class="ifp-gen-ai-dashboard-landing-page__header">
          {{'Response rating' | translate }}
        </div>
          <ifp-gen-ai-dashboard-rating [star]="gaugeChartData.star" [value]="gaugeChartData.data"></ifp-gen-ai-dashboard-rating>
      </div>

   <div class="ifp-gen-ai-dashboard-landing-page__card ifp-gen-ai-dashboard-landing-page__card--33">
        <div  class="ifp-gen-ai-dashboard-landing-page__header">
          {{'Response Style' | translate }}
        </div>
        <ifp-highcharts [data]="analyticMetrixGauge[analyticMatrixOptions.evalAnsStyle]" [height]="300" [donutCenterFunc]="chart" [chartName]="'donutChart'" [comparisonEnable]="false" [data]="seriesDataStyle()"></ifp-highcharts>
      </div>
      <div class="ifp-gen-ai-dashboard-landing-page__card ifp-gen-ai-dashboard-landing-page__card--66">
        <div  class="ifp-gen-ai-dashboard-landing-page__header">
          {{'Average Time Of Response' | translate }}
        </div>
        @if (lineChartData[0].data?.length !==0) {
          <ifp-highcharts [data]="lineChartData"[height]="300" [comparisonEnable]="false" ></ifp-highcharts>
        } @else {
          <app-ifp-no-data  [isTransparent]="true"></app-ifp-no-data>
        }
      </div>
          <div class="ifp-gen-ai-dashboard-landing-page__card ifp-gen-ai-dashboard-landing-page__card--100">
        <div  class="ifp-gen-ai-dashboard-landing-page__header">
          {{'Accuracy over the week' | translate }}
        </div>
        @if (weeklyAccurary[0].data?.length !==0) {
          <ifp-highcharts [data]="weeklyAccurary"[height]="300" [comparisonEnable]="false" ></ifp-highcharts>
        } @else {
          <app-ifp-no-data  [isTransparent]="true"></app-ifp-no-data>
        }
      </div>



      <!-- <div class="ifp-gen-ai-dashboard-landing-page__card ifp-gen-ai-dashboard-landing-page__card--33">
        <div  class="ifp-gen-ai-dashboard-landing-page__header">
          {{'Complexity v/s Accuracy' | translate }}
        </div>
        @if (barChartDataComplexity()[0].data?.length !==0) {
        <ifp-highcharts [data]="barChartDataComplexity()" [tooltip]="tooltip" [legends]="legends" [xAxis]="xAxis" [plotOptions]="plotOptions" [yAxis]="yAxis"  [height]="300" [chartName]="'barChart'"  [comparisonEnable]="false"></ifp-highcharts>
        } @else {
          <app-ifp-no-data  [isTransparent]="true"></app-ifp-no-data>
        }

      </div>
      <div class="ifp-gen-ai-dashboard-landing-page__card ifp-gen-ai-dashboard-landing-page__card--33">
        <div  class="ifp-gen-ai-dashboard-landing-page__header">
          {{'Type v/s Accuracy' | translate }}
        </div>
        @if (barChartDataCategory()[0].data?.length !==0) {
        <ifp-highcharts [data]="barChartDataCategory()" [tooltip]="tooltip" [legends]="legends" [xAxis]="xAxis"  [plotOptions]="plotOptions" [yAxis]="yAxis" [height]="300" [chartName]="'barChart'"  [comparisonEnable]="false"></ifp-highcharts>
        }@else {
          <app-ifp-no-data [isTransparent]="true"></app-ifp-no-data>
        }
      </div> -->


    </section>


  </div>

</div>
