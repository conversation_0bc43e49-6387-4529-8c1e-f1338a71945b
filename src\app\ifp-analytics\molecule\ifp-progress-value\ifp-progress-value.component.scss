@use "../../../../assets/ifp-styles/abstracts/index" as *;
.ifp-progress-value {
  display: flex;
  align-items: center;
  &__value {
    font-weight: $fw-semi-bold;
    padding-left: $spacer-2;
    border-inline-start: 1px solid $ifp-color-grey-7;
  }
  &__bar-outer {
    width: 100%;
  }
  &__percent {
    white-space: nowrap;
    display: flex;
    margin: $spacer-0 (-$spacer-1);
  }
  &__percentage,&__value {
    white-space: nowrap;
    padding: $spacer-0 ($spacer-2 + 2px);
  }
  &__wrapper {

    padding: $spacer-0 $spacer-2;
    &--size-sm {

      .ifp-progress-value {
        &__value {
        margin-inline-start: $spacer-0;
        }
       }
    }
  }
  &__bordered {
    display: flex;
    .ifp-progress-value {

      &__percentage {
        font-weight: $fw-semi-bold;
        border-inline-start: 1px solid $ifp-color-grey-7;
        &--bd-rem {
          border-inline-start: 0
        }
      }


      &__value {
        border-inline-start: 0;
      }
    }
    padding: $spacer-2 $spacer-0;
    border-inline-start: 2px solid $ifp-color-grey-7;
  }
  &__progress-bar {
    border-radius: 30px;
    background-color: $ifp-color-grey-12;
    overflow: hidden;
    &--bordered {
      border-start-start-radius: 0;
      border-end-start-radius: 0;
      background-color:transparent;
      padding: $spacer-2 $spacer-0;
      border-inline-start: 2px solid $ifp-color-grey-7;
    }
  }
  &--rect {
    border-inline-start: unset;
    padding: $spacer-0;
    .ifp-progress-value {
      &__progress-bar {
        border-inline-start: unset;
        border-radius: 3px 6px 6px 3px;
      }
    }
  }


}
:host-context([dir="rtl"]) {
  .ifp-progress-value {
    &--rect {
      .ifp-progress-value {
        &__progress-bar {
          border-radius: 6px 3px 3px 6px;
        }
      }
    }
    &__percentage {
      direction: ltr;
    }
  }
}
