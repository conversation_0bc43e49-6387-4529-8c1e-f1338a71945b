@if(!loader()) {

<div class="ifp-select-data">
  <div class="ifp-select-data__header-wrapper">
    <div class="ifp-select-data__header">
      <h2 class="ifp-select-data__title"> {{'Modify Data Type' | translate}}</h2>
      <h5 class="ifp-select-data__sub-title"> {{'Modify the data types of columns in the table to ensure the data is in a valid format for analysis.' | translate}}</h5>
    </div>
    <!-- <ifp-button [loader]="loaderProcess()" [label]="'Process' | translate" class="ifp-select-data__button" [buttonClass]="isChanges ? buttonClass.primaryLight : buttonClass.disabled"
    [iconClass]="'ifp-icon-rightarrow'" (ifpClick)="proceed()"></ifp-button> -->
  </div>
  <div class="ifp-select-data__table">
    <ifp-data-table [translation]="false" (customEventEmitter)="customEventEmitter($event)" [tableData]="tableData" [tableHead]="tableHead"></ifp-data-table>
  </div>

</div>

<!-- <div class="ifp-select-data__table-row fp-select-data__table-row--head">
      <div class="ifp-select-data__table-col">{{'Fields' | translate}}</div>
      <div class="ifp-select-data__table-col">{{'Data Type' | translate}}</div>
    </div>
    <div class="ifp-select-data__table-row fp-select-data__table-row--head">
      <div class="ifp-select-data__table-col">{{'Id' | translate}}</div>
      <div class="ifp-select-data__table-col">
        <div class="ifp-select-data__table-modify">
          <app-ifp-db-dropdown class="ifp-import__filter-item" [options]="dataTypeList" [key]="'value'"></app-ifp-db-dropdown>
        </div>
        <div class="ifp-select-data__table-info">

        </div>
      </div>
    </div> -->

  } @else {
    <div class="ifp-center-loader">
      <app-ifp-spinner></app-ifp-spinner>
    </div>
  }

  <!-- <ifp-data-preview  class="ifp-prep-data__preview"></ifp-data-preview> -->
  <app-ifp-modal [overlayClass]="'ifp-modal__bg-grey'" #modalSucess >

    <ifp-prep-popup (closePopUp)="closeSucessModal()"   (buttonEventValue)="buttonClick($event)" [heading]="'Data Type Modified Successfully!'" [desc]="''"  [fileName]="prepService.uploadedFileResponse()?.name ?? ''"  [subTitle]="'Explore More'" [subDesc]="'What would you like to do next?'" [buttonText]="'View Data Exploratory Summary'"></ifp-prep-popup>

      </app-ifp-modal>
