import { Component, input, InputSignal, model, ModelSignal, output } from '@angular/core';
import { IfpUserTagComponent } from "../../ifp-atoms/ifp-user-tag/ifp-user-tag.component";
import { NgClass, SlicePipe } from '@angular/common';
import { TranslateModule } from '@ngx-translate/core';

@Component({
  selector: 'ifp-user-tag-group',
  imports: [IfpUserTagComponent, SlicePipe, NgClass, TranslateModule],
  templateUrl: './ifp-user-tag-group.component.html',
  styleUrl: './ifp-user-tag-group.component.scss'
})
export class IfpUserTagGroupComponent {
  public tagList: InputSignal<UserDetail[]> = input([{name: ''}]);
  public isEdit: InputSignal<boolean> = input(false);
  public moreText: InputSignal<string> = input('');
  public limit: ModelSignal<number> = model(2);
  public removeData = output<{ tag: UserDetail; index: number; }>()


  remove(tag:UserDetail, index: number) {
    this.removeData.emit({
      tag: tag,
      index: index
    });
  }
}

export interface UserDetail {
  name: string;
  id?: string;
  email?: string;
}
