@use "../../../../assets/ifp-styles/abstracts/index" as *;

:host {
  display: block;
}

.ifp-event {

  &__header,
  &__body-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  &__header,
  &__body {
    padding: $spacer-4 $spacer-5;

  }

  &__header {
    border-bottom: 1px solid $ifp-color-grey-13;
  }

  &__body-header {
    margin-bottom: $spacer-4;
  }

  &__main-head,
  &__title {
    font-size: $ifp-fs-7;
    font-weight: $fw-bold;
  }

  &__close {
    font-size: $ifp-fs-4;
    cursor: pointer;
    transition: 0.3s;

    &:hover {
      opacity: 0.7;
    }
  }

  &__title {
    margin-bottom: $spacer-1;
  }

  &__date {
    color: $ifp-color-grey-2;

    .ifp-icon {
      margin-inline-end: $spacer-1;
    }
  }

  &__dropdown-label {
    color: $ifp-color-grey-14;
    margin-bottom: $spacer-1;
  }

  &__pagination {
    display: block;
    margin-top: $spacer-4;
  }

  &__detail-table {
    display: block;
    height: 350px;
    @include ifp-scroll-y(transparent, $ifp-color-grey-7, 8px, 8px);
    margin-bottom: $spacer-4;
  }
}
