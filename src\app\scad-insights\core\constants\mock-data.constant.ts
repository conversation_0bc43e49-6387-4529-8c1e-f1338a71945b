export const lineChart= [
  [
    1262304000000,
    0.7537
  ],
  [
    1262563200000,
    0.6951
  ],
  [
    1262649600000,
    0.6925
  ],
  [
    1262736000000,
    0.697
  ],
  [
    1262822400000,
    0.6992
  ],
  [
    1262908800000,
    0.7007
  ],
  [
    1263168000000,
    0.6884
  ],
  [
    1263254400000,
    0.6907
  ],
  [
    1263340800000,
    0.6868
  ],
  [
    1263427200000,
    0.6904
  ],
  [
    1263513600000,
    0.6958
  ],
  [
    1263772800000,
    0.696
  ],
  [
    1263859200000,
    0.7004
  ],
  [
    1263945600000,
    0.7077
  ],
  [
    1264032000000,
    0.7111
  ],
  [
    1264118400000,
    0.7076
  ],
  [
    1264377600000,
    0.7068
  ],
  [
    1264464000000,
    0.7101
  ],
  [
    1264550400000,
    0.7107
  ],
  [
    1264636800000,
    0.7144
  ],
  [
    1264723200000,
    0.7161
  ],
  [
    1264982400000,
    0.7189
  ],
  [
    1265068800000,
    0.7176
  ],
  [
    1265155200000,
    0.7152
  ],
  [
    1265241600000,
    0.7223
  ],
  [
    1265328000000,
    0.7305
  ],
  [
    1265587200000,
    0.7314
  ],
  [
    1265673600000,
    0.7268
  ],
  [
    1265760000000,
    0.7279
  ],
  [
    1265846400000,
    0.7291
  ],
  [
    1265932800000,
    0.7369
  ],
  [
    1266192000000,
    0.735
  ],
  [
    1266278400000,
    0.7328
  ],
  [
    1266364800000,
    0.7286
  ],
  [
    1266451200000,
    0.7372
  ],
  [
    1266537600000,
    0.7398
  ],
  [
    1266796800000,
    0.734
  ],
  [
    1266883200000,
    0.7366
  ],
  [
    1266969600000,
    0.7383
  ],
  [
    1267056000000,
    0.7414
  ],
  [
    1267142400000,
    0.737
  ],
  [
    1267401600000,
    0.7395
  ],
  [
    1267488000000,
    0.7382
  ],
  [
    1267574400000,
    0.7332
  ],
  [
    1267660800000,
    0.7317
  ],
  [
    1267747200000,
    0.7364
  ],
  [
    1268006400000,
    0.7321
  ],
  [
    1268092800000,
    0.7377
  ],
  [
    1268179200000,
    0.7349
  ],
  [
    1268265600000,
    0.7323
  ],
  [
    1268352000000,
    0.7266
  ],
  [
    1268611200000,
    0.7298
  ],
  [
    1268697600000,
    0.7288
  ],
  [
    1268784000000,
    0.7271
  ],
  [
    1268870400000,
    0.7322
  ]

];
export const barChart = {
  type: 'column',
  name: 'Berlin',
  color: '#807AED',
  data: [42.4, 33.2, 34.5, 39.7, 52.6, 75.5, 57.4, 60.4, 47.6]

}
;
export const analyticsMock =  [
  {
    'id': '2143',
    'type': 'Internal',
    'component_title': 'WHAT IF ANALYSIS: QUARTERLY NON OIL ECONOMIC ACTIVITY INDICATOR(CONSTANT)',
    'component_subtitle': 'WHAT IF ANALYSIS: QUARTERLY NON OIL ECONOMIC ACTIVITY INDICATOR(CONSTANT)',
    'page_icon': '',
    'page_light_icon': '',
    'domains': [
      'Agriculture & Environment'
    ],
    'theme': '',
    'subtheme': '',
    'product': '',
    'application_url': '',
    'imgSrc': '',
    'attachment': '',
    'policy_guide': '',
    'note': '',
    'search_tags': [],
    'narrative': '',
    'Indicator': ' GDP_QUARTERLY_SHORT_FORECAST_CONSTANT_AUG2021 ',
    'indicator_list': '',
    'indicatorValues_subtitle': '',
    'indicatorValues_title': '',
    'visualization_subtitle': '',
    'visualization_title': '',
    'enableDynamicPanel': false,
    'listofDyanmicPanelContent': [],
    'highlightsMeta': '',
    'infogramUrl': '',
    'confidenceIntervalMeta': '',
    'enableConfidenceInterval': false,
    'endpoint_label': '',
    'endpoint_title': '',
    'endpoint_url': '',
    'default_layer': 'False',
    'show_on_legend': 'False',
    'defaultDistrictId': '',
    'endpointType': '',
    'nodeId': '',
    'summaryCardId': '',
    'endpoint_icon_id': '',
    'cardDate': '',
    'dashboardUrl': '',
    'enablePointToggle': true,
    'maxPointLimit': '',
    'minLimitYAxis': '',
    'publication_date': '2023-07-10',
    'tagName': 'Scenario Drivers',
    'tagColorCode': '#1da3a1',
    'showInsights': 'False',
    'height': '883',
    'host_url': '',
    'embedded_code_version': '3',
    'site_root': '',
    'external_name': '',
    'tabs': 'No',
    'toolbar': 'No',
    'showAppBanner': 'False',
    'compare_data': '',
    'language': 'EN',
    'indicatorTools': [
      {
        'id': 'export-png',
        'disabled': true,
        'label': 'Export PNG'
      },
      {
        'id': 'export-csv',
        'label': 'Export CSV'
      }
    ],
    'indicatorDrivers': [
      {
        'title': 'OPEC Oil Price',
        'type': 'radio',
        'id': 'parameter_4_range',
        'subtitle': 'Range',
        'options': [
          {
            'label': '-50%',
            'value': 'very low',
            'isSelected': false
          },
          {
            'label': '-25%',
            'value': 'low',
            'isSelected': false
          },
          {
            'label': 'No change',
            'value': 'medium',
            'isSelected': true
          },
          {
            'label': '25%',
            'value': 'high',
            'isSelected': false
          },
          {
            'label': '50%',
            'value': 'very high',
            'isSelected': false
          }
        ]
      },
      {
        'title': 'Import of Non-oil goods',
        'type': 'radio',
        'id': 'parameter_2_range',
        'subtitle': 'Range',
        'options': [
          {
            'label': '-40%',
            'value': 'very low',
            'isSelected': false
          },
          {
            'label': '-20%',
            'value': 'low',
            'isSelected': false
          },
          {
            'label': 'No change',
            'value': 'medium',
            'isSelected': true
          },
          {
            'label': '20%',
            'value': 'high',
            'isSelected': false
          },
          {
            'label': '40%',
            'value': 'very high',
            'isSelected': false
          }
        ]
      },
      {
        'title': 'PMI',
        'id': 'parameter_1_range',
        'type': 'radio',
        'subtitle': 'Range',
        'note': '',
        'options': [
          {
            'label': '-10%',
            'value': 'very low',
            'isSelected': false
          },
          {
            'label': '-5%',
            'value': 'low',
            'isSelected': false
          },
          {
            'label': 'No change',
            'value': 'medium',
            'isSelected': true
          },
          {
            'label': '5%',
            'value': 'high',
            'isSelected': false
          },
          {
            'label': '10%',
            'value': 'very high',
            'isSelected': false
          }
        ]
      },
      {
        'title': 'M1 Monetary Supply',
        'type': 'radio',
        'id': 'parameter_3_range',
        'subtitle': 'Range',
        'options': [
          {
            'label': '-5%',
            'value': 'very low',
            'isSelected': false
          },
          {
            'label': '-2.5%',
            'value': 'low',
            'isSelected': false
          },
          {
            'label': 'No change',
            'value': 'medium',
            'isSelected': true
          },
          {
            'label': '2.5%',
            'value': 'high',
            'isSelected': false
          },
          {
            'label': '5%',
            'value': 'very high',
            'isSelected': false
          }
        ]
      }
    ],
    'indicatorFilters': [
      {
        'id': 'period-filter',
        'options': [
          {
            'id': '1-year',
            'label': '1 YEAR',
            'value': 1,
            'unit': 'years',
            'isSelected': true
          },
          {
            'id': '2-years',
            'label': '2 YEARS',
            'value': 2,
            'unit': 'years',
            'isSelected': false
          },
          {
            'id': '5-years',
            'label': '5 YEARS',
            'value': 5,
            'unit': 'years',
            'isSelected': false
          },
          {
            'id': 'All',
            'label': 'ALL',
            'value': null,
            'unit': null,
            'isSelected': false
          }
        ]
      }
    ],
    'indicatorValues': {
      'overviewValuesMeta': [
        {
          'id': 'latest-date-value',
          'type': 'static-with-title-template',
          'valueFormat': 'number_1.0-0',
          'templateFormat': 'dd-MM-yy',
          'viewName': 'VW_RI_GDP_AUG2021_IND_CNST',
          'comboIdTable': 'DS_RI_SCE_COMBO_CONSTANT',
          'dimension': {
            'TYPE': 'FORECAST',
            'SECTOR': 'TOTAL',
            'OIL_NONOIL': 'NON_OIL',
            'INDUSTRY': 'TOTAL'
          },
          'dateFormat': 'YYYY-MM-DD',
          'dateStart': '2021-09-30',
          'value': '97.72'
        },
        {
          'id': 'percentage-change',
          'title': 'Q/Q',
          'type': 'dynamic-with-title-template',
          'valueFormat': 'percentage_1.1-1',
          'color': '#3667ff',
          'templateFormat': 'dateQuarterYYYY',
          'viewName': 'VW_RI_GDP_AUG2021_IND_CNST',
          'dimension': {
            'SECTOR': 'TOTAL',
            'OIL_NONOIL': 'NON_OIL',
            'INDUSTRY': 'TOTAL'
          },
          'unit': 'latest-to-previous-quarter',
          'period': 1,
          'dateStart': '2021-06-30',
          'dateEnd': '2021-09-30',
          'value': 1.1959863638354884
        }
      ],
      'valuesMeta': [
        {
          'id': 'current-index',
          'title': 'Indicator as of {dateStart}',
          'type': 'static-with-title-template',
          'valueFormat': 'number_1.1-1',
          'templateFormat': 'monthQuotesYear'
        },
        {
          'id': 'estimate',
          'title': '% change {dateStart} to {dateEnd}',
          'type': 'dynamic-with-title-template',
          'valueFormat': 'percentage_1.1-1',
          'templateFormat': 'monthQuotesYear'
        },
        {
          'id': 'quarter-index',
          'title': '{dateStart}',
          'type': 'static-with-title-template',
          'valueFormat': 'number_1.1-1',
          'templateFormat': 'monthQuotesYear'
        },
        {
          'id': 'quarter-percentage',
          'title': '% change {dateStart} to {dateEnd} ',
          'type': 'dynamic-with-title-template',
          'valueFormat': 'percentage_1.1-1',
          'templateFormat': 'monthQuotesYear'
        }
      ]
    },
    'indicatorVisualizations': {
      'visualizationsMeta': [
        {
          'id': 'line-chart-economy-indicator-constant',
          'type': 'line-chart',
          'comboIdTable': 'DS_RI_SCE_COMBO_CONSTANT',
          'viewName': 'VW_RI_GDP_AUG2021_IND_CNST',
          'seriesMeta': [
            {
              'id': 'economic-index',
              'label': 'Economic activity index',
              'color': '#3667ff',
              'type': 'solid',
              'dimension': {
                'TYPE': 'NOWCAST',
                'SECTOR': 'TOTAL',
                'OIL_NONOIL': 'NON_OIL',
                'INDUSTRY': 'TOTAL'
              },
              'xAccessor': {
                'type': 'date',
                'path': 'OBS_DT',
                'specifier': '%Y-%m-%d'
              },
              'yAccessor': {
                'type': 'value',
                'path': 'VALUE'
              }
            },
            {
              'id': 'economic-index-forecast',
              'label': 'Economic activity index',
              'color': '#3667ff',
              'type': 'forecast-with-arrow',
              'dimension': {
                'TYPE': 'FORECAST',
                'SECTOR': 'TOTAL',
                'OIL_NONOIL': 'NON_OIL',
                'INDUSTRY': 'TOTAL'
              },
              'xAccessor': {
                'type': 'date',
                'path': 'OBS_DT',
                'specifier': '%Y-%m-%d'
              },
              'yAccessor': {
                'type': 'value',
                'path': 'VALUE'
              }
            }
          ],
          'markersMeta': [
            {
              'id': 'economic-index_real-vs-forecast',
              'color': '#ffffff',
              'type': 'line-with-label',
              'labelText': 'Forecast',
              'axis': 'x',
              'accessor': {
                'type': 'date',
                'path': 'DATE',
                'specifier': '%Y-%m-%d'
              }
            }
          ],
          'showInterval': true,
          'showQuarterlyIntervals': true,
          'showPointLabels': true,
          'xAxisLabel': null,
          'yAxisLabel': null,
          'yAxisExtraStepMin': 0.005,
          'yAxisExtraStepMax': 0.005,
          'xAxisFormat': 'date_y',
          'yAxisFormat': 'd3-number',
          'tooltipTitleFormat': 'date_y',
          'tooltipValueFormat': 'number_1.1-1',
          'accuracyMetrics': {
            'title': '*Accuracy rate: {ACCURACY_VALUE} as of {OBS_DT}',
            'viewName': 'DS_ACCURACY_METRICS',
            'dbColumn': 'NODE_ID',
            'dateFormat': 'MMMM YYYY'
          }
        }
      ],
      'visualizationDefault': 'line-chart-economy-indicator-constant'
    },
    'indicatorType': 'analytical-apps',
    'showSecondLevel': true
  },
  {
    'id': '627',
    'type': 'Internal',
    'component_title': 'Economic Activity Index Forecasting Model by Sector (Current GDP)',
    'component_subtitle': 'Index based on 2014 as a reference year (2014=100)',
    'page_icon': '',
    'page_light_icon': '',
    'domains': [
      'Economy'
    ],
    'theme': 'National Accounts',
    'subtheme': '',
    'product': '',
    'application_url': '',
    'imgSrc': '',
    'attachment': '',
    'policy_guide': '',
    'note': 'Data science methodology',
    'search_tags': [],
    'narrative': '',
    'Indicator': ' GDP SECTORS CURRENT CONF ',
    'indicator_list': '',
    'indicatorValues_subtitle': '',
    'indicatorValues_title': '',
    'visualization_subtitle': '',
    'visualization_title': '',
    'enableDynamicPanel': false,
    'listofDyanmicPanelContent': [],
    'highlightsMeta': '',
    'infogramUrl': '',
    'confidenceIntervalMeta': '',
    'enableConfidenceInterval': false,
    'endpoint_label': '',
    'endpoint_title': '',
    'endpoint_url': '',
    'default_layer': 'False',
    'show_on_legend': 'False',
    'defaultDistrictId': '',
    'endpointType': '',
    'nodeId': '',
    'summaryCardId': '',
    'endpoint_icon_id': '',
    'cardDate': '',
    'dashboardUrl': '',
    'enablePointToggle': true,
    'maxPointLimit': '',
    'minLimitYAxis': '',
    'publication_date': '2023-06-21',
    'tagName': 'Scenario Drivers',
    'tagColorCode': '#1da3a1',
    'showInsights': 'False',
    'height': '',
    'host_url': '',
    'embedded_code_version': '',
    'site_root': '',
    'external_name': '',
    'tabs': 'No',
    'toolbar': 'No',
    'showAppBanner': 'False',
    'compare_data': '',
    'language': 'EN',
    'sortVisualizations': true,
    'indicatorTools': [
      {
        'id': 'export-png',
        'disabled': true,
        'label': 'Export PNG'
      },
      {
        'id': 'export-csv',
        'label': 'Export CSV'
      }
    ],
    'driverTitle': 'Time Selector',
    'driverSubtitle': 'Modify below to view changes in the projection',
    'indicatorDrivers': [
      {
        'title': 'Forecast',
        'id': 'parameter_1_range',
        'type': 'radio',
        'subtitle': 'Range',
        'note': '1 quarter forecast',
        'options': [
          {
            'label': '1 Quarter',
            'value': '1q',
            'isSelected': true
          },
          {
            'label': '2 Quarter',
            'value': '2q',
            'isSelected': false
          }
        ]
      }
    ],
    'indicatorValues': {
      'overviewValuesMeta': [
        {
          'id': 'latest-date-value',
          'type': 'static-with-title-template',
          'valueFormat': 'number_1.0-0',
          'templateFormat': 'date_MMM y',
          'viewName': 'VW_RI_GDP_SECTOR_CRNT_IND_PCT',
          'comboIdTable': 'DS_GDP_SECTORS_COMBO_CRNT',
          'hasDefault': true,
          'dimension': {
            'SECTOR': 'CONSTRUCTION'
          },
          'dateFormat': 'YYYY-MM-DD'
        },
        {
          'id': 'percentage-change',
          'title': 'Y/Y',
          'type': 'dynamic-with-title-template',
          'valueFormat': 'percentage_1.1-1',
          'color': '#3667ff',
          'templateFormat': 'dateQuarterYYYY',
          'viewName': 'VW_RI_GDP_SECTOR_CRNT_IND_PCT',
          'dimension': {
            'SECTOR': 'CONSTRUCTION'
          },
          'unit': 'latest-to-previous-quarter',
          'period': 1,
          'dateStart': null,
          'dateEnd': null,
          'value': null
        }
      ],
      'valuesMeta': [
        {
          'id': 'latest-date',
          'title': 'Forecast Period',
          'type': 'static-with-title-template',
          'valueFormat': 'date_MMM y',
          'viewName': 'VW_RI_GDP_SECTOR_CRNT_IND_PCT',
          'comboIdTable': 'DS_GDP_SECTORS_COMBO_CRNT',
          'hasDefault': true,
          'dimension': {
            'SECTOR': 'CONSTRUCTION'
          },
          'dateFormat': 'YYYY-MM-DD'
        }
      ]
    },
    'indicatorVisualizations': {
      'visualizationsMeta': [
        {
          'id': 'tree-chart-economy-sector-indicator',
          'type': 'tree-map-with-change-chart',
          'sortOrder': 1,
          'viewName': 'VW_RI_GDP_SECTOR_CRNT_IND_PCT',
          'proportionValueFormat': 'percentage_1.1-1',
          'comboIdTable': 'DS_GDP_SECTORS_COMBO_CRNT',
          'xAxisLabel': 'Other non-oil sectors (Government, Utilities, and Household Production) are not included in this forecast.',
          'hasDefault': true,
          'colorCodes': {
            'positive': {
              '1_5': '#19AA2B',
              '5_10': '#0e931f',
              '10_20': '#007506',
              '20_60': '#005700',
              '60+': '#003b00'
            },
            'negetive': {
              '1_5': '#e54d35',
              '5_10': '#c83220',
              '10_20': '#ac0e0a',
              '20_60': '#870101',
              '60+': '#610000'
            },
            'neutral': '#666878'
          },
          'selectRangeBy': 'CHANGE',
          'colorsRange': [
            {
              'range': '1_5',
              'valueLL': 1,
              'valueUL': 5
            },
            {
              'range': '5_10',
              'valueLL': 5,
              'valueUL': 10
            },
            {
              'range': '10_20',
              'valueLL': 10,
              'valueUL': 20
            },
            {
              'range': '20_60',
              'valueLL': 20,
              'valueUL': 60
            },
            {
              'range': '60+',
              'valueLL': 60,
              'valueUL': 'Infinity'
            },
            {
              'range': 'neutral',
              'valueLL': 0,
              'valueUL': 1
            }
          ],
          'tooltips': [
            {
              'type': 'text',
              'path': 'SECTOR'
            },
            {
              'type': 'value',
              'label': 'Current Index Value',
              'path': 'VALUE_CURRENT'
            },
            {
              'type': 'value',
              'label': 'Forecasted Index Value',
              'path': 'VALUE_FORECAST'
            },
            {
              'type': 'percentage',
              'label': 'Q/Q Change',
              'path': 'CHANGE'
            },
            {
              'type': 'percentage',
              'label': 'Y/Y Change',
              'path': 'CHANGE_PY'
            },
            {
              'type': 'value',
              'label': 'Proportion of Total Economy',
              'path': 'VALUE_PERC_ECO'
            }
          ],
          'seriesMeta': [
            {
              'id': 'construction',
              'label': 'Construction',
              'color': 'rgba(56, 101, 255, 0.3)',
              'type': 'forecast',
              'dimension': {
                'SECTOR': 'CONSTRUCTION'
              },
              'valueAccessor': {
                'type': 'value',
                'path': 'VALUE_PERC_ECO'
              },
              'changeAccessor': {
                'type': 'value',
                'label': 'Q/Q Change',
                'path': 'CHANGE'
              },
              'sectorNameAccessor': {
                'type': 'value',
                'path': 'SECTOR'
              },
              'proportionAccessor': {
                'type': 'value',
                'label': 'Proportion of Total Economy',
                'path': 'VALUE_PERC_ECO'
              },
              'currentValueAccessor': {
                'type': 'value',
                'label': 'Current Index Value',
                'path': 'VALUE_CURRENT'
              },
              'forecastedValueAccessor': {
                'type': 'value',
                'label': 'Forecasted Index Value',
                'path': 'VALUE_FORECAST'
              }
            },
            {
              'id': 'financial-and-insurance',
              'label': 'Financial and insurance',
              'color': 'rgba(56, 101, 255, 0.3)',
              'type': 'forecast',
              'dimension': {
                'SECTOR': 'Financial and insurance'
              },
              'valueAccessor': {
                'type': 'value',
                'path': 'VALUE_PERC_ECO'
              },
              'changeAccessor': {
                'type': 'value',
                'label': 'Q/Q Change',
                'path': 'CHANGE'
              },
              'sectorNameAccessor': {
                'type': 'value',
                'path': 'SECTOR'
              },
              'proportionAccessor': {
                'type': 'value',
                'label': 'Proportion of Total Economy',
                'path': 'VALUE_PERC_ECO'
              },
              'currentValueAccessor': {
                'type': 'value',
                'label': 'Current Index Value',
                'path': 'VALUE_CURRENT'
              },
              'forecastedValueAccessor': {
                'type': 'value',
                'label': 'Forecasted Index Value',
                'path': 'VALUE_FORECAST'
              }
            },
            {
              'id': 'manufacturing',
              'label': 'Manufacturing',
              'color': 'rgba(56, 101, 255, 0.3)',
              'type': 'forecast',
              'dimension': {
                'SECTOR': 'Manufacturing'
              },
              'valueAccessor': {
                'type': 'value',
                'path': 'VALUE_PERC_ECO'
              },
              'changeAccessor': {
                'type': 'value',
                'label': 'Q/Q Change',
                'path': 'CHANGE'
              },
              'sectorNameAccessor': {
                'type': 'value',
                'path': 'SECTOR'
              },
              'proportionAccessor': {
                'type': 'value',
                'label': 'Proportion of Total Economy',
                'path': 'VALUE_PERC_ECO'
              },
              'currentValueAccessor': {
                'type': 'value',
                'label': 'Current Index Value',
                'path': 'VALUE_CURRENT'
              },
              'forecastedValueAccessor': {
                'type': 'value',
                'label': 'Forecasted Index Value',
                'path': 'VALUE_FORECAST'
              }
            },
            {
              'id': 'publi-administration-and-defence-compulsory-social-security',
              'label': 'Public admin & Defence; Compulsory Social Sec.',
              'color': 'rgba(56, 101, 255, 0.3)',
              'type': 'forecast',
              'dimension': {
                'SECTOR': 'Public admin & Defence; Compulsory Social Sec.'
              },
              'valueAccessor': {
                'type': 'value',
                'path': 'VALUE_PERC_ECO'
              },
              'changeAccessor': {
                'type': 'value',
                'label': 'Q/Q Change',
                'path': 'CHANGE'
              },
              'sectorNameAccessor': {
                'type': 'value',
                'path': 'SECTOR'
              },
              'proportionAccessor': {
                'type': 'value',
                'label': 'Proportion of Total Economy',
                'path': 'VALUE_PERC_ECO'
              },
              'currentValueAccessor': {
                'type': 'value',
                'label': 'Current Index Value',
                'path': 'VALUE_CURRENT'
              },
              'forecastedValueAccessor': {
                'type': 'value',
                'label': 'Forecasted Index Value',
                'path': 'VALUE_FORECAST'
              }
            },
            {
              'id': 'wholesale-and-retail-trade-repair-of-motor-vehicles-and-motorcycles',
              'label': 'Wholesale & Retail Trade; Repair of vehicles & Motorcycles',
              'color': 'rgba(56, 101, 255, 0.3)',
              'type': 'forecast',
              'dimension': {
                'SECTOR': 'Wholesale & Retail Trade; Repair of vehicles & Motorcycles'
              },
              'valueAccessor': {
                'type': 'value',
                'path': 'VALUE_PERC_ECO'
              },
              'changeAccessor': {
                'type': 'value',
                'label': 'Q/Q Change',
                'path': 'CHANGE'
              },
              'sectorNameAccessor': {
                'type': 'value',
                'path': 'SECTOR'
              },
              'proportionAccessor': {
                'type': 'value',
                'label': 'Proportion of Total Economy',
                'path': 'VALUE_PERC_ECO'
              },
              'currentValueAccessor': {
                'type': 'value',
                'label': 'Current Index Value',
                'path': 'VALUE_CURRENT'
              },
              'forecastedValueAccessor': {
                'type': 'value',
                'label': 'Forecasted Index Value',
                'path': 'VALUE_FORECAST'
              }
            },
            {
              'id': 'information-and-communication',
              'label': 'Information and communication',
              'color': 'rgba(56, 101, 255, 0.3)',
              'type': 'forecast',
              'dimension': {
                'SECTOR': 'INFORMATION AND COMMUNICATION'
              },
              'valueAccessor': {
                'type': 'value',
                'path': 'VALUE_PERC_ECO'
              },
              'changeAccessor': {
                'type': 'value',
                'label': 'Q/Q Change',
                'path': 'CHANGE'
              },
              'sectorNameAccessor': {
                'type': 'value',
                'path': 'SECTOR'
              },
              'proportionAccessor': {
                'type': 'value',
                'label': 'Proportion of Total Economy',
                'path': 'VALUE_PERC_ECO'
              },
              'currentValueAccessor': {
                'type': 'value',
                'label': 'Current Index Value',
                'path': 'VALUE_CURRENT'
              },
              'forecastedValueAccessor': {
                'type': 'value',
                'label': 'Forecasted Index Value',
                'path': 'VALUE_FORECAST'
              }
            },
            {
              'id': 'electricity-gas-and-water-supply-waste-management',
              'label': 'Electricity, Gas, & Water supply; Waste Mgmt',
              'color': 'rgba(56, 101, 255, 0.3)',
              'type': 'forecast',
              'dimension': {
                'SECTOR': 'Electricity, Gas, & Water supply; Waste Mgmt'
              },
              'valueAccessor': {
                'type': 'value',
                'path': 'VALUE_PERC_ECO'
              },
              'changeAccessor': {
                'type': 'value',
                'label': 'Q/Q Change',
                'path': 'CHANGE'
              },
              'sectorNameAccessor': {
                'type': 'value',
                'path': 'SECTOR'
              },
              'proportionAccessor': {
                'type': 'value',
                'label': 'Proportion of Total Economy',
                'path': 'VALUE_PERC_ECO'
              },
              'currentValueAccessor': {
                'type': 'value',
                'label': 'Current Index Value',
                'path': 'VALUE_CURRENT'
              },
              'forecastedValueAccessor': {
                'type': 'value',
                'label': 'Forecasted Index Value',
                'path': 'VALUE_FORECAST'
              }
            },
            {
              'id': 'professional-scientific-and-technical-administrative-and-support-services',
              'label': 'Professional, scientific and technical& Administrative and support services',
              'color': 'rgba(56, 101, 255, 0.3)',
              'type': 'forecast',
              'dimension': {
                'SECTOR': 'Professional, scientific and technical& Administrative and support services'
              },
              'valueAccessor': {
                'type': 'value',
                'path': 'VALUE_PERC_ECO'
              },
              'changeAccessor': {
                'type': 'value',
                'label': 'Q/Q Change',
                'path': 'CHANGE'
              },
              'sectorNameAccessor': {
                'type': 'value',
                'path': 'SECTOR'
              },
              'proportionAccessor': {
                'type': 'value',
                'label': 'Proportion of Total Economy',
                'path': 'VALUE_PERC_ECO'
              },
              'currentValueAccessor': {
                'type': 'value',
                'label': 'Current Index Value',
                'path': 'VALUE_CURRENT'
              },
              'forecastedValueAccessor': {
                'type': 'value',
                'label': 'Forecasted Index Value',
                'path': 'VALUE_FORECAST'
              }
            },
            {
              'id': 'real-estate',
              'label': 'Real estate',
              'color': 'rgba(56, 101, 255, 0.3)',
              'type': 'forecast',
              'dimension': {
                'SECTOR': 'Real estate'
              },
              'valueAccessor': {
                'type': 'value',
                'path': 'VALUE_PERC_ECO'
              },
              'changeAccessor': {
                'type': 'value',
                'label': 'Q/Q Change',
                'path': 'CHANGE'
              },
              'sectorNameAccessor': {
                'type': 'value',
                'path': 'SECTOR'
              },
              'proportionAccessor': {
                'type': 'value',
                'label': 'Proportion of Total Economy',
                'path': 'VALUE_PERC_ECO'
              },
              'currentValueAccessor': {
                'type': 'value',
                'label': 'Current Index Value',
                'path': 'VALUE_CURRENT'
              },
              'forecastedValueAccessor': {
                'type': 'value',
                'label': 'Forecasted Index Value',
                'path': 'VALUE_FORECAST'
              }
            }
          ]
        }
      ],
      'visualizationDefault': 'tree-chart-economy-sector-indicator'
    },
    'country_flag': '',
    'page_menu_icon': '',
    'isFavorite': 'false',
    'showSecondLevel': false
  },
  {
    'id': '624',
    'type': 'insights-discovery',
    'component_title': 'VISA CONSUMER SPENDING',
    'component_subtitle': 'The analytical app is based on Visa data. It is intended to work as an insights discovery tool to analyse consumer spending behavior and trends across various aspects.',
    'page_icon': 'https://ifp-cms-dev.scad.gov.ae/sites/default/files/2023-07/Analytical%20apps_1.svg',
    'page_light_icon': 'https://ifp-cms-dev.scad.gov.ae/sites/default/files/2023-07/Analytical%20apps_2.svg',
    'domains': [
      'Social Statistics'
    ],
    'theme': '',
    'subtheme': '',
    'product': '',
    'application_url': '',
    'imgSrc': '',
    'attachment': '',
    'policy_guide': '',
    'note': '',
    'search_tags': [],
    'narrative': '',
    'Indicator': ' Spending Trend Analysis - Insights Discovery ',
    'indicator_list': '358, 359, 453',
    'indicatorValues_subtitle': 'This section highlights  key figures in visa consumer spending',
    'indicatorValues_title': 'Visa Consumer Spending At Glance',
    'visualization_subtitle': '',
    'visualization_title': 'Visa Consumer Spending Insight Discovery',
    'enableDynamicPanel': true,
    'infogramUrl': '',
    'confidenceIntervalMeta': '',
    'enableConfidenceInterval': false,
    'endpoint_label': '',
    'endpoint_title': '',
    'endpoint_url': '',
    'default_layer': 'False',
    'show_on_legend': 'False',
    'defaultDistrictId': '',
    'endpointType': '',
    'nodeId': '',
    'summaryCardId': '',
    'endpoint_icon_id': '',
    'cardDate': '',
    'dashboardUrl': '',
    'enablePointToggle': false,
    'maxPointLimit': '',
    'minLimitYAxis': '',
    'publication_date': '2023-07-10',
    'tagName': 'Insights Discovery',
    'tagColorCode': '#dbaa21',
    'showInsights': 'False',
    'height': '',
    'host_url': '',
    'embedded_code_version': '',
    'site_root': '',
    'external_name': '',
    'tabs': 'No',
    'toolbar': 'No',
    'showAppBanner': 'False',
    'compare_data': '',
    'dynamicPanel': [
      {
        'id': 'highlights',
        'title': 'Highlights',
        'meta': {
          'indicatorValues': {
            'selectAggregation': {
              'label': 'Time Period',
              'options': [
                'Year to Date',
                'Year on Year',
                'Month on Month'
              ],
              'default': 'Year to Date'
            },
            'valuesMeta': [
              {
                'id': 'year-to-date',
                'title': '',
                'viewName': 'CONSUMER_DOMESTIC_SPEND_TRENDS',
                'additionalText': 'Jan to',
                'dimension': {
                  'INDICATOR_ID': [
                    'COI_CONSUMER_SPEND_YTD',
                    'COI_CONSUMER_SPEND_YOY',
                    'COI_CONSUMER_SPEND_MOM'
                  ]
                },
                'dateFormat': 'MMM YYYY'
              },
              {
                'id': 'year-to-date-value',
                'title': 'Overall Spending',
                'viewName': 'CONSUMER_DOMESTIC_SPEND_TRENDS',
                'iconId': 'Total-Spend',
                'iconLightId': 'Total-Spend_LIGHT',
                'note': 'Overall spending shows the domestic spending through visa cards',
                'dimension': {
                  'INDICATOR_ID': [
                    'COI_CONSUMER_SPEND_YTD',
                    'COI_CONSUMER_SPEND_YTD_CH',
                    'COI_CONSUMER_SPEND_YOY',
                    'COI_CONSUMER_SPEND_YOY_CH',
                    'COI_CONSUMER_SPEND_MOM',
                    'COI_CONSUMER_SPEND_MOM_CH'
                  ]
                }
              },
              {
                'id': 'year-to-date-value',
                'title': 'Essential Spending',
                'viewName': 'CONSUMER_DOMESTIC_SPEND_TRENDS',
                'iconId': 'Essential',
                'iconLightId': 'Essential_LIGHT',
                'note': 'Essential Spending includes Food and Groceries, Fuel, healthcare, department stores, education and government, and transportation',
                'filterBy': {
                  'SPEND_CATEGORIZATION': 'ESSENTIALS'
                },
                'dimension': {
                  'INDICATOR_ID': [
                    'COI_CNSMR_CTGRZTN_YTD',
                    'COI_CNSMR_CTGRZTN_YTD_CH',
                    'COI_CNSMR_CTGRZTN_YOY',
                    'COI_CNSMR_CTGRZTN_YOY_CH',
                    'COI_CNSMR_CTGRZTN_MOM',
                    'COI_CNSMR_CTGRZTN_MOM_CH'
                  ]
                }
              },
              {
                'id': 'year-to-date-value',
                'title': 'Online Spending',
                'viewName': 'CONSUMER_DOMESTIC_SPEND_TRENDS',
                'iconId': 'Online',
                'iconLightId': 'Online_LIGHT',
                'note': 'Represents amount spent by UAE card holders via e-commerce platforms that are inside or outside UAE',
                'filterBy': {
                  'CHANNEL': 'ONLINE'
                },
                'dimension': {
                  'INDICATOR_ID': [
                    'COI_CNSMR_CHNNL_YTD',
                    'COI_CNSMR_CHNNL_YTD_CH',
                    'COI_CNSMR_CHNNL_YOY',
                    'COI_CNSMR_CHNNL_YOY_CH',
                    'COI_CNSMR_CHNNL_MOM',
                    'COI_CNSMR_CHNNL_MOM_CH'
                  ]
                }
              },
              {
                'id': 'year-to-date-value',
                'title': 'Average Spending per Transaction',
                'viewName': 'CONSUMER_DOMESTIC_SPEND_TRENDS',
                'iconId': 'Average-Ticket-Size',
                'iconLightId': 'Average-Ticket-Size_LIGHT',
                'note': 'Average Spending per Transaction is a representation of the average transaction value a customer is spending domestically',
                'dimension': {
                  'INDICATOR_ID': [
                    'COI_CNSMR_TICKET_SIZE_YTD',
                    'COI_CNSMR_TICKET_SIZE_YTD_CH',
                    'COI_CNSMR_TICKET_SIZE_YOY',
                    'COI_CNSMR_TICKET_SIZE_YOY_CH',
                    'COI_CNSMR_TICKET_SIZE_MOM',
                    'COI_CNSMR_TICKET_SIZE_MOM_CH'
                  ]
                }
              },
              {
                'id': 'year-to-date-value',
                'title': 'Non-Essential Spending',
                'viewName': 'CONSUMER_DOMESTIC_SPEND_TRENDS',
                'iconId': 'Non-Essential',
                'iconLightId': 'Non-Essential_LIGHT',
                'note': 'Non Essential Spending includes Discount Stores, General Retail Services, Insurance, automotive, Lodging, Entertainment, Business services, Professional Services, General Retail Goods, Apparels and Accessories, Auto Rental, Electronics, Travel Services, Airlines, Direct Marketing, Luxury goods, Duty Free',
                'filterBy': {
                  'SPEND_CATEGORIZATION': 'NON-ESSENTIALS'
                },
                'dimension': {
                  'INDICATOR_ID': [
                    'COI_CNSMR_CTGRZTN_YTD',
                    'COI_CNSMR_CTGRZTN_YTD_CH',
                    'COI_CNSMR_CTGRZTN_YOY',
                    'COI_CNSMR_CTGRZTN_YOY_CH',
                    'COI_CNSMR_CTGRZTN_MOM',
                    'COI_CNSMR_CTGRZTN_MOM_CH'
                  ]
                }
              },
              {
                'id': 'year-to-date-value',
                'title': 'In-store Spending',
                'viewName': 'CONSUMER_DOMESTIC_SPEND_TRENDS',
                'iconId': 'In-Store',
                'iconLightId': 'In-Store_LIGHT',
                'note': 'In-store spending represents amount spent in physical stores by Abu Dhubai card holders in UAE and the rest of UAE card holders spend in Abu Dhabi',
                'filterBy': {
                  'CHANNEL': 'IN-STORE'
                },
                'dimension': {
                  'INDICATOR_ID': [
                    'COI_CNSMR_CHNNL_YTD',
                    'COI_CNSMR_CHNNL_YTD_CH',
                    'COI_CNSMR_CHNNL_YOY',
                    'COI_CNSMR_CHNNL_YOY_CH',
                    'COI_CNSMR_CHNNL_MOM',
                    'COI_CNSMR_CHNNL_MOM_CH'
                  ]
                }
              }
            ]
          }
        }
      }
    ],
    'language': 'EN',
    'sortVisualizations': true,
    'indicatorValues': {
      'overviewValuesMeta': [
        {
          'id': 'year-to-date-value',
          'title': 'Overall Spending',
          'aggregation': 'Year to Date',
          'hideYearDate': true,
          'viewName': 'CONSUMER_DOMESTIC_SPEND_TRENDS',
          'dimension': {
            'INDICATOR_ID': [
              'COI_CONSUMER_SPEND_YTD',
              'COI_CONSUMER_SPEND_YTD_CH',
              'COI_CONSUMER_SPEND_YOY',
              'COI_CONSUMER_SPEND_YOY_CH',
              'COI_CONSUMER_SPEND_MOM',
              'COI_CONSUMER_SPEND_MOM_CH'
            ]
          },
          'values': [
            {
              'value': 55214660745.6207,
              'type': 'ABSOLUTE',
              'aggregation': 'Year to Date',
              'date': '2022-12-01'
            },
            {
              'value': 16.752130052974596,
              'type': 'PERCENTAGE_CHANGE',
              'aggregation': 'Year to Date',
              'date': '2022-12-01'
            },
            {
              'value': 4916791038.60883,
              'type': 'ABSOLUTE',
              'aggregation': 'Year on Year',
              'date': '2022-12-01'
            },
            {
              'value': 12.1709458381261,
              'type': 'PERCENTAGE_CHANGE',
              'aggregation': 'Year on Year',
              'date': '2022-12-01'
            },
            {
              'value': 4916791038.60883,
              'type': 'ABSOLUTE',
              'aggregation': 'Month on Month',
              'date': '2022-12-01'
            },
            {
              'value': 2.7662616822165402,
              'type': 'PERCENTAGE_CHANGE',
              'aggregation': 'Month on Month',
              'date': '2022-12-01'
            }
          ]
        },
        {
          'id': 'percentage-change',
          'title': 'Y/Y',
          'type': 'dynamic-with-title-template',
          'valueFormat': 'percentage_1.1-1',
          'color': '#3667ff',
          'templateFormat': 'dateQuarterYYYY',
          'viewName': 'VW_VISA_DOMESTIC_SPENDING',
          'dimension': {
            'INDICATOR_ID': 'VISA_DOMESTIC_SPEND'
          },
          'filterBy': {
            'CHANNEL': 'ALL',
            'CUSTOMER_PRODUCT_CLASS': 'ALL',
            'SPEND_CATEGORIZATION': 'ALL',
            'SPEND_CATEGORY_SEGMENT': 'ALL'
          },
          'unit': 'years',
          'period': 1,
          'dateStart': '2021-12-01',
          'dateEnd': '2022-12-01',
          'value': '12.17'
        }
      ]
    },
    'indicatorTools': [
      {
        'id': 'export-png',
        'disabled': true,
        'label': 'Export PNG'
      },
      {
        'id': 'export-csv-all',
        'label': 'Extract All'
      },
      {
        'id': 'export-csv',
        'label': 'Extract Current Selection'
      }
    ],
    'indicatorActions': [
      {
        'id': 'toggle-fullscreen',
        'type': 'simple',
        'label': 'Toggle Fullscreen'
      }
    ],
    'country_flag': '',
    'field_page_light_icon': '',
    'page_menu_icon': '',
    'page_menu_light_icon': '',
    'field_page_menu_light_icon': '',
    'showSecondLevel': true
  },
  {
    'id': '617',
    'type': 'Internal',
    'component_title': 'Economic Activity Index Forecasting Model by Sector (Constant GDP)',
    'component_subtitle': 'Index based on 2014 as a reference year (2014=100)',
    'page_icon': '',
    'page_light_icon': '',
    'domains': [
      'Economy'
    ],
    'theme': 'National Accounts',
    'subtheme': '',
    'product': '',
    'application_url': '',
    'imgSrc': '',
    'attachment': '',
    'policy_guide': '',
    'note': 'Data science methodology',
    'search_tags': [],
    'narrative': '',
    'Indicator': ' FORECAST CONSTANT CONF ',
    'indicator_list': '',
    'indicatorValues_subtitle': '',
    'indicatorValues_title': '',
    'visualization_subtitle': '',
    'visualization_title': '',
    'enableDynamicPanel': false,
    'listofDyanmicPanelContent': [],
    'highlightsMeta': '',
    'infogramUrl': '',
    'confidenceIntervalMeta': '',
    'enableConfidenceInterval': false,
    'endpoint_label': '',
    'endpoint_title': '',
    'endpoint_url': '',
    'default_layer': 'False',
    'show_on_legend': 'False',
    'defaultDistrictId': '',
    'endpointType': '',
    'nodeId': '',
    'summaryCardId': '',
    'endpoint_icon_id': '',
    'cardDate': '',
    'dashboardUrl': '',
    'enablePointToggle': true,
    'maxPointLimit': '',
    'minLimitYAxis': '',
    'publication_date': '2023-06-21',
    'tagName': 'Scenario Drivers',
    'tagColorCode': '#1da3a1',
    'showInsights': 'False',
    'height': '',
    'host_url': '',
    'embedded_code_version': '',
    'site_root': '',
    'external_name': '',
    'tabs': 'No',
    'toolbar': 'No',
    'showAppBanner': 'False',
    'compare_data': '',
    'language': 'EN',
    'sortVisualizations': true,
    'indicatorTools': [
      {
        'id': 'export-png',
        'disabled': true,
        'label': 'Export PNG'
      },
      {
        'id': 'export-csv',
        'label': 'Export CSV'
      }
    ],
    'indicatorActions': [],
    'driverTitle': 'Time Selector',
    'driverSubtitle': 'Modify below to view changes in the projection',
    'indicatorDrivers': [
      {
        'title': 'Forecast',
        'id': 'parameter_1_range',
        'type': 'radio',
        'subtitle': 'Range',
        'note': '1 quarter forecast',
        'options': [
          {
            'label': '1 Quarter',
            'value': '1q',
            'isSelected': true
          },
          {
            'label': '2 Quarter',
            'value': '2q',
            'isSelected': false
          }
        ]
      }
    ],
    'indicatorValues': {
      'overviewValuesMeta': [
        {
          'id': 'latest-date-value',
          'type': 'static-with-title-template',
          'valueFormat': 'number_1.0-0',
          'templateFormat': 'date_MMM y',
          'viewName': 'VW_RI_GDP_SECTOR_CNST_IND_PCT',
          'comboIdTable': 'DS_GDP_SECTORS_COMBO_CNST',
          'hasDefault': true,
          'dimension': {
            'SECTOR': 'MANUFACTURING'
          },
          'dateFormat': 'YYYY-MM-DD',
          'dateStart': '2023-06-30',
          'value': '177.34'
        },
        {
          'id': 'percentage-change',
          'title': 'Y/Y',
          'type': 'dynamic-with-title-template',
          'valueFormat': 'percentage_1.1-1',
          'color': '#3667ff',
          'templateFormat': 'dateQuarterYYYY',
          'viewName': 'VW_RI_GDP_SECTOR_CNST_IND_PCT',
          'dimension': {
            'SECTOR': 'MANUFACTURING'
          },
          'unit': 'latest-to-previous-quarter',
          'period': 1,
          'dateStart': '2023-06-30',
          'dateEnd': '2023-09-30',
          'value': 1.0887120716125187
        }
      ],
      'valuesMeta': [
        {
          'id': 'latest-date',
          'title': 'Forecast Period',
          'type': 'static-with-title-template',
          'valueFormat': 'dateQuarterYYYY',
          'viewName': 'VW_RI_GDP_SECTOR_CNST_IND_PCT',
          'comboIdTable': 'DS_GDP_SECTORS_COMBO_CNST',
          'hasDefault': true,
          'dimension': {
            'SECTOR': 'MANUFACTURING'
          },
          'dateFormat': 'YYYY-MM-DD'
        }
      ]
    },
    'indicatorVisualizations': {
      'visualizationsMeta': [
        {
          'id': 'tree-chart-economy-sector-indicator',
          'type': 'tree-map-with-change-chart',
          'sortOrder': 1,
          'viewName': 'VW_RI_GDP_SECTOR_CNST_IND_PCT',
          'comboIdTable': 'DS_GDP_SECTORS_COMBO_CNST',
          'xAxisLabel': 'Other non-oil sectors (Government, Utilities, and Household Production) are not included in this forecast.',
          'hasDefault': true,
          'tooltips': [
            {
              'type': 'text',
              'path': 'SECTOR'
            },
            {
              'type': 'value',
              'label': 'Current Value',
              'path': 'VALUE_CURRENT'
            },
            {
              'type': 'value',
              'label': 'Forecasted Value',
              'path': 'VALUE_FORECAST'
            },
            {
              'type': 'percentage',
              'label': 'Q/Q Change',
              'path': 'CHANGE'
            },
            {
              'type': 'percentage',
              'label': 'Y/Y Change',
              'path': 'CHANGE_PY'
            },
            {
              'type': 'value',
              'label': 'Proportion of Total Economy',
              'path': 'VALUE_PERC_ECO'
            }
          ],
          'colorCodes': {
            'positive': {
              '1_5': '#19AA2B',
              '5_10': '#0e931f',
              '10_20': '#007506',
              '20_60': '#005700',
              '60+': '#003b00'
            },
            'negetive': {
              '1_5': '#e54d35',
              '5_10': '#c83220',
              '10_20': '#ac0e0a',
              '20_60': '#870101',
              '60+': '#610000'
            },
            'neutral': '#666878'
          },
          'selectRangeBy': 'CHANGE',
          'colorsRange': [
            {
              'range': '1_5',
              'valueLL': 1,
              'valueUL': 5
            },
            {
              'range': '5_10',
              'valueLL': 5,
              'valueUL': 10
            },
            {
              'range': '10_20',
              'valueLL': 10,
              'valueUL': 20
            },
            {
              'range': '20_60',
              'valueLL': 20,
              'valueUL': 60
            },
            {
              'range': '60+',
              'valueLL': 60,
              'valueUL': 'Infinity'
            },
            {
              'range': 'neutral',
              'valueLL': 0,
              'valueUL': 1
            }
          ],
          'seriesMeta': [
            {
              'id': 'construction',
              'label': 'Construction',
              'color': 'rgba(56, 101, 255, 0.3)',
              'type': 'forecast',
              'dimension': {
                'SECTOR': 'CONSTRUCTION'
              },
              'valueAccessor': {
                'type': 'value',
                'path': 'VALUE_PERC_ECO'
              },
              'changeAccessor': {
                'type': 'value',
                'label': 'Q/Q Change',
                'path': 'CHANGE'
              },
              'sectorNameAccessor': {
                'type': 'value',
                'path': 'SECTOR'
              },
              'proportionAccessor': {
                'type': 'value',
                'label': 'Proportion of Total Economy',
                'path': 'VALUE_PERC_ECO'
              },
              'currentValueAccessor': {
                'type': 'value',
                'label': 'Current Value',
                'path': 'VALUE_CURRENT'
              },
              'forecastedValueAccessor': {
                'type': 'value',
                'label': 'Forecasted Value',
                'path': 'VALUE_FORECAST'
              }
            },
            {
              'id': 'financial-and-insurance',
              'label': 'Financial and insurance',
              'color': 'rgba(56, 101, 255, 0.3)',
              'type': 'forecast',
              'dimension': {
                'SECTOR': 'Financial and insurance'
              },
              'valueAccessor': {
                'type': 'value',
                'path': 'VALUE_PERC_ECO'
              },
              'changeAccessor': {
                'type': 'value',
                'label': 'Q/Q Change',
                'path': 'CHANGE'
              },
              'sectorNameAccessor': {
                'type': 'value',
                'path': 'SECTOR'
              },
              'proportionAccessor': {
                'type': 'value',
                'label': 'Proportion of Total Economy',
                'path': 'VALUE_PERC_ECO'
              },
              'currentValueAccessor': {
                'type': 'value',
                'label': 'Current Value',
                'path': 'VALUE_CURRENT'
              },
              'forecastedValueAccessor': {
                'type': 'value',
                'label': 'Forecasted Value',
                'path': 'VALUE_FORECAST'
              }
            },
            {
              'id': 'manufacturing',
              'label': 'Manufacturing',
              'color': 'rgba(56, 101, 255, 0.3)',
              'type': 'forecast',
              'dimension': {
                'SECTOR': 'Manufacturing'
              },
              'valueAccessor': {
                'type': 'value',
                'path': 'VALUE_PERC_ECO'
              },
              'changeAccessor': {
                'type': 'value',
                'label': 'Q/Q Change',
                'path': 'CHANGE'
              },
              'sectorNameAccessor': {
                'type': 'value',
                'path': 'SECTOR'
              },
              'proportionAccessor': {
                'type': 'value',
                'label': 'Proportion of Total Economy',
                'path': 'VALUE_PERC_ECO'
              },
              'currentValueAccessor': {
                'type': 'value',
                'label': 'Current Value',
                'path': 'VALUE_CURRENT'
              },
              'forecastedValueAccessor': {
                'type': 'value',
                'label': 'Forecasted Value',
                'path': 'VALUE_FORECAST'
              }
            },
            {
              'id': 'publi-administration-and-defence-compulsory-social-security',
              'label': 'Public admin & Defence; Compulsory Social Sec.',
              'color': 'rgba(56, 101, 255, 0.3)',
              'type': 'forecast',
              'dimension': {
                'SECTOR': 'Public admin & Defence; Compulsory Social Sec.'
              },
              'valueAccessor': {
                'type': 'value',
                'path': 'VALUE_PERC_ECO'
              },
              'changeAccessor': {
                'type': 'value',
                'label': 'Q/Q Change',
                'path': 'CHANGE'
              },
              'sectorNameAccessor': {
                'type': 'value',
                'path': 'SECTOR'
              },
              'proportionAccessor': {
                'type': 'value',
                'label': 'Proportion of Total Economy',
                'path': 'VALUE_PERC_ECO'
              },
              'currentValueAccessor': {
                'type': 'value',
                'label': 'Current Value',
                'path': 'VALUE_CURRENT'
              },
              'forecastedValueAccessor': {
                'type': 'value',
                'label': 'Forecasted Value',
                'path': 'VALUE_FORECAST'
              }
            },
            {
              'id': 'wholesale-and-retail-trade-repair-of-motor-vehicles-and-motorcycles',
              'label': 'Wholesale & Retail Trade; Repair of vehicles & Motorcycles',
              'color': 'rgba(56, 101, 255, 0.3)',
              'type': 'forecast',
              'dimension': {
                'SECTOR': 'Wholesale & Retail Trade; Repair of vehicles & Motorcycles'
              },
              'valueAccessor': {
                'type': 'value',
                'path': 'VALUE_PERC_ECO'
              },
              'changeAccessor': {
                'type': 'value',
                'label': 'Q/Q Change',
                'path': 'CHANGE'
              },
              'sectorNameAccessor': {
                'type': 'value',
                'path': 'SECTOR'
              },
              'proportionAccessor': {
                'type': 'value',
                'label': 'Proportion of Total Economy',
                'path': 'VALUE_PERC_ECO'
              },
              'currentValueAccessor': {
                'type': 'value',
                'label': 'Current Value',
                'path': 'VALUE_CURRENT'
              },
              'forecastedValueAccessor': {
                'type': 'value',
                'label': 'Forecasted Value',
                'path': 'VALUE_FORECAST'
              }
            },
            {
              'id': 'information-and-communication',
              'label': 'Information and communication',
              'color': 'rgba(56, 101, 255, 0.3)',
              'type': 'forecast',
              'dimension': {
                'SECTOR': 'INFORMATION AND COMMUNICATION'
              },
              'valueAccessor': {
                'type': 'value',
                'path': 'VALUE_PERC_ECO'
              },
              'changeAccessor': {
                'type': 'value',
                'label': 'Q/Q Change',
                'path': 'CHANGE'
              },
              'sectorNameAccessor': {
                'type': 'value',
                'path': 'SECTOR'
              },
              'proportionAccessor': {
                'type': 'value',
                'label': 'Proportion of Total Economy',
                'path': 'VALUE_PERC_ECO'
              },
              'currentValueAccessor': {
                'type': 'value',
                'label': 'Current Value',
                'path': 'VALUE_CURRENT'
              },
              'forecastedValueAccessor': {
                'type': 'value',
                'label': 'Forecasted Value',
                'path': 'VALUE_FORECAST'
              }
            },
            {
              'id': 'electricity-gas-and-water-supply-waste-management',
              'label': 'Electricity, Gas, & Water supply; Waste Mgmt',
              'color': 'rgba(56, 101, 255, 0.3)',
              'type': 'forecast',
              'dimension': {
                'SECTOR': 'Electricity, Gas, & Water supply; Waste Mgmt'
              },
              'valueAccessor': {
                'type': 'value',
                'path': 'VALUE_PERC_ECO'
              },
              'changeAccessor': {
                'type': 'value',
                'label': 'Q/Q Change',
                'path': 'CHANGE'
              },
              'sectorNameAccessor': {
                'type': 'value',
                'path': 'SECTOR'
              },
              'proportionAccessor': {
                'type': 'value',
                'label': 'Proportion of Total Economy',
                'path': 'VALUE_PERC_ECO'
              },
              'currentValueAccessor': {
                'type': 'value',
                'label': 'Current Value',
                'path': 'VALUE_CURRENT'
              },
              'forecastedValueAccessor': {
                'type': 'value',
                'label': 'Forecasted Value',
                'path': 'VALUE_FORECAST'
              }
            },
            {
              'id': 'professional-scientific-and-technical-administrative-and-support-services',
              'label': 'Professional, scientific and technical& Administrative and support services',
              'color': 'rgba(56, 101, 255, 0.3)',
              'type': 'forecast',
              'dimension': {
                'SECTOR': 'Professional, scientific and technical& Administrative and support services'
              },
              'valueAccessor': {
                'type': 'value',
                'path': 'VALUE_PERC_ECO'
              },
              'changeAccessor': {
                'type': 'value',
                'label': 'Q/Q Change',
                'path': 'CHANGE'
              },
              'sectorNameAccessor': {
                'type': 'value',
                'path': 'SECTOR'
              },
              'proportionAccessor': {
                'type': 'value',
                'label': 'Proportion of Total Economy',
                'path': 'VALUE_PERC_ECO'
              },
              'currentValueAccessor': {
                'type': 'value',
                'label': 'Current Value',
                'path': 'VALUE_CURRENT'
              },
              'forecastedValueAccessor': {
                'type': 'value',
                'label': 'Forecasted Value',
                'path': 'VALUE_FORECAST'
              }
            },
            {
              'id': 'real-estate',
              'label': 'Real estate',
              'color': 'rgba(56, 101, 255, 0.3)',
              'type': 'forecast',
              'dimension': {
                'SECTOR': 'Real estate'
              },
              'valueAccessor': {
                'type': 'value',
                'path': 'VALUE_PERC_ECO'
              },
              'changeAccessor': {
                'type': 'value',
                'label': 'Q/Q Change',
                'path': 'CHANGE'
              },
              'sectorNameAccessor': {
                'type': 'value',
                'path': 'SECTOR'
              },
              'proportionAccessor': {
                'type': 'value',
                'label': 'Proportion of Total Economy',
                'path': 'VALUE_PERC_ECO'
              },
              'currentValueAccessor': {
                'type': 'value',
                'label': 'Current Value',
                'path': 'VALUE_CURRENT'
              },
              'forecastedValueAccessor': {
                'type': 'value',
                'label': 'Forecasted Value',
                'path': 'VALUE_FORECAST'
              }
            }
          ]
        },
        {
          'id': 'tree-chart-economy-sector-indicator-2',
          'type': 'tree-map-with-change-chart',
          'sortOrder': 2,
          'viewName': 'VW_RI_GDP_SECTOR_CNST_IND_PCT',
          'comboIdTable': 'DS_GDP_SECTORS_COMBO_CNST',
          'xAxisLabel': 'Other non-oil sectors (Government, Utilities, and Household Production) are not included in this forecast.',
          'hasDefault': true,
          'colorCodes': {
            'positive': {
              '1_5': '#19AA2B',
              '5_10': '#0e931f',
              '10_20': '#007506',
              '20_60': '#005700',
              '60+': '#003b00'
            },
            'negetive': {
              '1_5': '#e54d35',
              '5_10': '#c83220',
              '10_20': '#ac0e0a',
              '20_60': '#870101',
              '60+': '#610000'
            },
            'neutral': '#666878'
          },
          'selectRangeBy': 'CHANGE',
          'colorsRange': [
            {
              'range': '1_5',
              'valueLL': 1,
              'valueUL': 5
            },
            {
              'range': '5_10',
              'valueLL': 5,
              'valueUL': 10
            },
            {
              'range': '10_20',
              'valueLL': 10,
              'valueUL': 20
            },
            {
              'range': '20_60',
              'valueLL': 20,
              'valueUL': 60
            },
            {
              'range': '60+',
              'valueLL': 60,
              'valueUL': 'Infinity'
            },
            {
              'range': 'neutral',
              'valueLL': 0,
              'valueUL': 1
            }
          ],
          'seriesMeta': [
            {
              'id': 'construction',
              'label': 'Construction',
              'color': 'rgba(56, 101, 255, 0.3)',
              'type': 'forecast',
              'dimension': {
                'SECTOR': 'CONSTRUCTION'
              },
              'valueAccessor': {
                'type': 'value',
                'path': 'VALUE_PERC_ECO'
              },
              'changeAccessor': {
                'type': 'value',
                'label': 'Q/Q Change',
                'path': 'CHANGE'
              },
              'sectorNameAccessor': {
                'type': 'value',
                'path': 'SECTOR'
              },
              'proportionAccessor': {
                'type': 'value',
                'label': 'Proportion of Total Economy',
                'path': 'VALUE_PERC_ECO'
              },
              'currentValueAccessor': {
                'type': 'value',
                'label': 'Current Value',
                'path': 'VALUE_CURRENT'
              },
              'forecastedValueAccessor': {
                'type': 'value',
                'label': 'Forecasted Value',
                'path': 'VALUE_FORECAST'
              }
            },
            {
              'id': 'financial-and-insurance',
              'label': 'Financial and insurance',
              'color': 'rgba(56, 101, 255, 0.3)',
              'type': 'forecast',
              'dimension': {
                'SECTOR': 'Financial and insurance'
              },
              'valueAccessor': {
                'type': 'value',
                'path': 'VALUE_PERC_ECO'
              },
              'changeAccessor': {
                'type': 'value',
                'label': 'Q/Q Change',
                'path': 'CHANGE'
              },
              'sectorNameAccessor': {
                'type': 'value',
                'path': 'SECTOR'
              },
              'proportionAccessor': {
                'type': 'value',
                'label': 'Proportion of Total Economy',
                'path': 'VALUE_PERC_ECO'
              },
              'currentValueAccessor': {
                'type': 'value',
                'label': 'Current Value',
                'path': 'VALUE_CURRENT'
              },
              'forecastedValueAccessor': {
                'type': 'value',
                'label': 'Forecasted Value',
                'path': 'VALUE_FORECAST'
              }
            },
            {
              'id': 'manufacturing',
              'label': 'Manufacturing',
              'color': 'rgba(56, 101, 255, 0.3)',
              'type': 'forecast',
              'dimension': {
                'SECTOR': 'Manufacturing'
              },
              'valueAccessor': {
                'type': 'value',
                'path': 'VALUE_PERC_ECO'
              },
              'changeAccessor': {
                'type': 'value',
                'label': 'Q/Q Change',
                'path': 'CHANGE'
              },
              'sectorNameAccessor': {
                'type': 'value',
                'path': 'SECTOR'
              },
              'proportionAccessor': {
                'type': 'value',
                'label': 'Proportion of Total Economy',
                'path': 'VALUE_PERC_ECO'
              },
              'currentValueAccessor': {
                'type': 'value',
                'label': 'Current Value',
                'path': 'VALUE_CURRENT'
              },
              'forecastedValueAccessor': {
                'type': 'value',
                'label': 'Forecasted Value',
                'path': 'VALUE_FORECAST'
              }
            },
            {
              'id': 'publi-administration-and-defence-compulsory-social-security',
              'label': 'Public admin & Defence; Compulsory Social Sec.',
              'color': 'rgba(56, 101, 255, 0.3)',
              'type': 'forecast',
              'dimension': {
                'SECTOR': 'Public admin & Defence; Compulsory Social Sec.'
              },
              'valueAccessor': {
                'type': 'value',
                'path': 'VALUE_PERC_ECO'
              },
              'changeAccessor': {
                'type': 'value',
                'label': 'Q/Q Change',
                'path': 'CHANGE'
              },
              'sectorNameAccessor': {
                'type': 'value',
                'path': 'SECTOR'
              },
              'proportionAccessor': {
                'type': 'value',
                'label': 'Proportion of Total Economy',
                'path': 'VALUE_PERC_ECO'
              },
              'currentValueAccessor': {
                'type': 'value',
                'label': 'Current Value',
                'path': 'VALUE_CURRENT'
              },
              'forecastedValueAccessor': {
                'type': 'value',
                'label': 'Forecasted Value',
                'path': 'VALUE_FORECAST'
              }
            },
            {
              'id': 'wholesale-and-retail-trade-repair-of-motor-vehicles-and-motorcycles',
              'label': 'Wholesale & Retail Trade; Repair of vehicles & Motorcycles',
              'color': 'rgba(56, 101, 255, 0.3)',
              'type': 'forecast',
              'dimension': {
                'SECTOR': 'Wholesale & Retail Trade; Repair of vehicles & Motorcycles'
              },
              'valueAccessor': {
                'type': 'value',
                'path': 'VALUE_PERC_ECO'
              },
              'changeAccessor': {
                'type': 'value',
                'label': 'Q/Q Change',
                'path': 'CHANGE'
              },
              'sectorNameAccessor': {
                'type': 'value',
                'path': 'SECTOR'
              },
              'proportionAccessor': {
                'type': 'value',
                'label': 'Proportion of Total Economy',
                'path': 'VALUE_PERC_ECO'
              },
              'currentValueAccessor': {
                'type': 'value',
                'label': 'Current Value',
                'path': 'VALUE_CURRENT'
              },
              'forecastedValueAccessor': {
                'type': 'value',
                'label': 'Forecasted Value',
                'path': 'VALUE_FORECAST'
              }
            },
            {
              'id': 'information-and-communication',
              'label': 'Information and communication',
              'color': 'rgba(56, 101, 255, 0.3)',
              'type': 'forecast',
              'dimension': {
                'SECTOR': 'INFORMATION AND COMMUNICATION'
              },
              'valueAccessor': {
                'type': 'value',
                'path': 'VALUE_PERC_ECO'
              },
              'changeAccessor': {
                'type': 'value',
                'label': 'Q/Q Change',
                'path': 'CHANGE'
              },
              'sectorNameAccessor': {
                'type': 'value',
                'path': 'SECTOR'
              },
              'proportionAccessor': {
                'type': 'value',
                'label': 'Proportion of Total Economy',
                'path': 'VALUE_PERC_ECO'
              },
              'currentValueAccessor': {
                'type': 'value',
                'label': 'Current Value',
                'path': 'VALUE_CURRENT'
              },
              'forecastedValueAccessor': {
                'type': 'value',
                'label': 'Forecasted Value',
                'path': 'VALUE_FORECAST'
              }
            },
            {
              'id': 'electricity-gas-and-water-supply-waste-management',
              'label': 'Electricity, Gas, & Water supply; Waste Mgmt',
              'color': 'rgba(56, 101, 255, 0.3)',
              'type': 'forecast',
              'dimension': {
                'SECTOR': 'Electricity, Gas, & Water supply; Waste Mgmt'
              },
              'valueAccessor': {
                'type': 'value',
                'path': 'VALUE_PERC_ECO'
              },
              'changeAccessor': {
                'type': 'value',
                'label': 'Q/Q Change',
                'path': 'CHANGE'
              },
              'sectorNameAccessor': {
                'type': 'value',
                'path': 'SECTOR'
              },
              'proportionAccessor': {
                'type': 'value',
                'label': 'Proportion of Total Economy',
                'path': 'VALUE_PERC_ECO'
              },
              'currentValueAccessor': {
                'type': 'value',
                'label': 'Current Value',
                'path': 'VALUE_CURRENT'
              },
              'forecastedValueAccessor': {
                'type': 'value',
                'label': 'Forecasted Value',
                'path': 'VALUE_FORECAST'
              }
            },
            {
              'id': 'professional-scientific-and-technical-administrative-and-support-services',
              'label': 'Professional, scientific and technical& Administrative and support services',
              'color': 'rgba(56, 101, 255, 0.3)',
              'type': 'forecast',
              'dimension': {
                'SECTOR': 'Professional, scientific and technical& Administrative and support services'
              },
              'valueAccessor': {
                'type': 'value',
                'path': 'VALUE_PERC_ECO'
              },
              'changeAccessor': {
                'type': 'value',
                'label': 'Q/Q Change',
                'path': 'CHANGE'
              },
              'sectorNameAccessor': {
                'type': 'value',
                'path': 'SECTOR'
              },
              'proportionAccessor': {
                'type': 'value',
                'label': 'Proportion of Total Economy',
                'path': 'VALUE_PERC_ECO'
              },
              'currentValueAccessor': {
                'type': 'value',
                'label': 'Current Value',
                'path': 'VALUE_CURRENT'
              },
              'forecastedValueAccessor': {
                'type': 'value',
                'label': 'Forecasted Value',
                'path': 'VALUE_FORECAST'
              }
            },
            {
              'id': 'real-estate',
              'label': 'Real estate',
              'color': 'rgba(56, 101, 255, 0.3)',
              'type': 'forecast',
              'dimension': {
                'SECTOR': 'Real estate'
              },
              'valueAccessor': {
                'type': 'value',
                'path': 'VALUE_PERC_ECO'
              },
              'changeAccessor': {
                'type': 'value',
                'label': 'Q/Q Change',
                'path': 'CHANGE'
              },
              'sectorNameAccessor': {
                'type': 'value',
                'path': 'SECTOR'
              },
              'proportionAccessor': {
                'type': 'value',
                'label': 'Proportion of Total Economy',
                'path': 'VALUE_PERC_ECO'
              },
              'currentValueAccessor': {
                'type': 'value',
                'label': 'Current Value',
                'path': 'VALUE_CURRENT'
              },
              'forecastedValueAccessor': {
                'type': 'value',
                'label': 'Forecasted Value',
                'path': 'VALUE_FORECAST'
              }
            }
          ]
        }
      ],
      'visualizationDefault': 'tree-chart-economy-sector-indicator'
    },
    'country_flag': '',
    'page_menu_icon': '',
    'isFavorite': 'false',
    'showSecondLevel': false
  },
  {
    'id': '610',
    'type': 'Internal',
    'component_title': 'Quarterly Forecast for Non-Oil Current GDP',
    'component_subtitle': 'The line chart displays the current GDP historical figure sand one subsequent quarter forecast as per the inputs provided by change drivers and selected time period. Base year 2014=100',
    'page_icon': '',
    'page_light_icon': '',
    'domains': [
      'Economy'
    ],
    'theme': 'National Accounts',
    'subtheme': '',
    'product': '',
    'application_url': '',
    'imgSrc': 'https://ifp-cms-dev.scad.gov.ae/sites/default/files/uploads/images/dashboard_colored.svg',
    'attachment': '',
    'policy_guide': '',
    'note': 'Data science methodology',
    'search_tags': [],
    'narrative': '',
    'Indicator': ' GDP_QUARTERLY_SHORT_FORECAST_CURRENT_AUG2021_V2 ',
    'indicator_list': '',
    'indicatorValues_subtitle': '',
    'indicatorValues_title': '',
    'visualization_subtitle': '',
    'visualization_title': '',
    'enableDynamicPanel': false,
    'listofDyanmicPanelContent': [],
    'highlightsMeta': '',
    'infogramUrl': '',
    'confidenceIntervalMeta': '',
    'enableConfidenceInterval': false,
    'endpoint_label': '',
    'endpoint_title': '',
    'endpoint_url': '',
    'default_layer': 'False',
    'show_on_legend': 'False',
    'defaultDistrictId': '',
    'endpointType': '',
    'nodeId': '',
    'summaryCardId': '',
    'endpoint_icon_id': '',
    'cardDate': '',
    'dashboardUrl': '',
    'enablePointToggle': true,
    'maxPointLimit': '',
    'minLimitYAxis': '',
    'publication_date': '2023-05-10',
    'tagName': 'Scenario Drivers',
    'tagColorCode': '#1da3a1',
    'showInsights': 'False',
    'height': '',
    'host_url': '',
    'embedded_code_version': '',
    'site_root': '',
    'external_name': '',
    'tabs': 'No',
    'toolbar': 'No',
    'showAppBanner': 'False',
    'compare_data': '',
    'language': 'EN',
    'indicatorTools': [
      {
        'id': 'export-png',
        'disabled': true,
        'label': 'Export PNG'
      },
      {
        'id': 'export-csv',
        'label': 'Export CSV'
      }
    ],
    'indicatorDrivers': [
      {
        'title': 'Business Licenses',
        'id': 'parameter_1_range',
        'type': 'radio',
        'subtitle': 'Range',
        'note': '',
        'options': [
          {
            'label': '-20%',
            'value': 'very low',
            'isSelected': false
          },
          {
            'label': '-10%',
            'value': 'low',
            'isSelected': false
          },
          {
            'label': 'No change',
            'value': 'medium',
            'isSelected': true
          },
          {
            'label': '10%',
            'value': 'high',
            'isSelected': false
          },
          {
            'label': '20%',
            'value': 'very high',
            'isSelected': false
          }
        ]
      },
      {
        'title': 'Import of Non-oil goods',
        'type': 'radio',
        'id': 'parameter_2_range',
        'subtitle': 'Range',
        'options': [
          {
            'label': '-40%',
            'value': 'very low',
            'isSelected': false
          },
          {
            'label': '-20%',
            'value': 'low',
            'isSelected': false
          },
          {
            'label': 'No change',
            'value': 'medium',
            'isSelected': true
          },
          {
            'label': '20%',
            'value': 'high',
            'isSelected': false
          },
          {
            'label': '40%',
            'value': 'very high',
            'isSelected': false
          }
        ]
      },
      {
        'title': 'Fixed Capital Formation',
        'type': 'radio',
        'id': 'parameter_3_range',
        'subtitle': 'Range',
        'options': [
          {
            'label': '-6%',
            'value': 'very low',
            'isSelected': false
          },
          {
            'label': '-3%',
            'value': 'low',
            'isSelected': false
          },
          {
            'label': 'No change',
            'value': 'medium',
            'isSelected': true
          },
          {
            'label': '3%',
            'value': 'high',
            'isSelected': false
          },
          {
            'label': '6%',
            'value': 'very high',
            'isSelected': false
          }
        ]
      },
      {
        'title': 'OPEC Oil Price',
        'type': 'radio',
        'id': 'parameter_4_range',
        'subtitle': 'Range',
        'options': [
          {
            'label': '-50%',
            'value': 'very low',
            'isSelected': false
          },
          {
            'label': '-25%',
            'value': 'low',
            'isSelected': false
          },
          {
            'label': 'No change',
            'value': 'medium',
            'isSelected': true
          },
          {
            'label': '25%',
            'value': 'high',
            'isSelected': false
          },
          {
            'label': '50%',
            'value': 'very high',
            'isSelected': false
          }
        ]
      }
    ],
    'indicatorFilters': [
      {
        'id': 'period-filter',
        'options': [
          {
            'id': '1-year',
            'label': '1 YEAR',
            'value': 1,
            'unit': 'years',
            'isSelected': true
          },
          {
            'id': '2-years',
            'label': '2 YEARS',
            'value': 2,
            'unit': 'years'
          },
          {
            'id': '5-years',
            'label': '5 YEARS',
            'value': 5,
            'unit': 'years'
          },
          {
            'id': 'All',
            'label': 'ALL',
            'value': null,
            'unit': null
          }
        ]
      }
    ],
    'indicatorValues': {
      'valuesMeta': [
        {
          'id': 'current-index',
          'title': 'Indicator as of {dateStart}',
          'type': 'static-with-title-template',
          'valueFormat': 'number_1.1-1',
          'templateFormat': 'date_MMM \'\'yy'
        },
        {
          'id': 'estimate',
          'title': '% change {dateStart} to {dateEnd}',
          'type': 'dynamic-with-title-template',
          'valueFormat': 'percentage_1.1-1',
          'templateFormat': 'date_MMM \'\'yy'
        },
        {
          'id': 'quarter-index',
          'title': '{dateStart}',
          'type': 'static-with-title-template',
          'valueFormat': 'number_1.1-1',
          'templateFormat': 'date_MMM \'\'yy'
        },
        {
          'id': 'quarter-percentage',
          'title': '% change {dateStart} to {dateEnd} ',
          'type': 'dynamic-with-title-template',
          'valueFormat': 'percentage_1.1-1',
          'templateFormat': 'date_MMM \'\'yy'
        }
      ]
    },
    'indicatorVisualizations': {
      'visualizationsMeta': [
        {
          'id': 'line-chart-economy-indicator-constant',
          'type': 'line-chart',
          'comboIdTable': 'DS_RI_SCE_COMBO_CURRENT',
          'viewName': 'VW_RI_GDP_AUG21_INDCNST_1Q',
          'seriesMeta': [
            {
              'id': 'economic-index',
              'label': 'Economic activity index',
              'color': '#3667ff',
              'type': 'solid',
              'dimension': {
                'TYPE': 'NOWCAST',
                'SECTOR': 'TOTAL',
                'OIL_NONOIL': 'NON_OIL',
                'INDUSTRY': 'TOTAL'
              },
              'xAccessor': {
                'type': 'date',
                'path': 'OBS_DT',
                'specifier': '%Y-%m-%d'
              },
              'yAccessor': {
                'type': 'value',
                'path': 'VALUE'
              }
            },
            {
              'id': 'economic-index-forecast',
              'label': 'Economic activity index',
              'color': '#3667ff',
              'type': 'forecast-with-arrow',
              'dimension': {
                'TYPE': 'FORECAST',
                'SECTOR': 'TOTAL',
                'OIL_NONOIL': 'NON_OIL',
                'INDUSTRY': 'TOTAL'
              },
              'xAccessor': {
                'type': 'date',
                'path': 'OBS_DT',
                'specifier': '%Y-%m-%d'
              },
              'yAccessor': {
                'type': 'value',
                'path': 'VALUE'
              }
            }
          ],
          'markersMeta': [
            {
              'id': 'economic-index_real-vs-forecast',
              'color': '#ffffff',
              'type': 'line-with-label',
              'labelText': 'Forecast',
              'axis': 'x',
              'accessor': {
                'type': 'date',
                'path': 'DATE',
                'specifier': '%Y-%m-%d'
              }
            }
          ],
          'showInterval': true,
          'showQuarterlyIntervals': true,
          'showPointLabels': true,
          'xAxisLabel': null,
          'yAxisLabel': null,
          'yAxisExtraStepMin': 0.005,
          'yAxisExtraStepMax': 0.005,
          'xAxisFormat': 'date_y',
          'yAxisFormat': 'd3-number',
          'tooltipTitleFormat': 'date_y',
          'tooltipValueFormat': 'number_1.1-1',
          'accuracyMetrics': {
            'title': '*Accuracy rate: {ACCURACY_VALUE} as of {OBS_DT}',
            'viewName': 'DS_ACCURACY_METRICS',
            'dbColumn': 'NODE_ID',
            'dateFormat': 'MMMM YYYY'
          }
        }
      ],
      'visualizationDefault': 'line-chart-economy-indicator-constant'
    },
    'indicatorType': 'analytical-apps',
    'country_flag': '',
    'field_page_light_icon': '',
    'page_menu_icon': '',
    'page_menu_light_icon': '',
    'field_page_menu_light_icon': '',
    'showSecondLevel': true
  },
  {
    'id': '598',
    'type': 'insights-discovery',
    'component_title': 'STR - Insights Discovery',
    'component_subtitle': 'The analytical app is based on Hotel Industry data. It provides an important performance indicators and metrics on hotels across selected cities.STR stands for Smith Travel Research, a hospitality analytics firm founded in 1985. They collect the data from hotels who subscribed to STR service to observe the performance metrics of their competitors. The genius part of STR\'s model is that these hotel customers also contribute their own performance metrics with STR which then packages and resells them to other competitors and investors. So readers of this dashboard should bear in mind that the following data doesn\'t cover the whole scope of the hotels in the listed cities.  Please visit for more information: https://hoteltechreport.com/news/str-report',
    'page_icon': '',
    'page_light_icon': '',
    'domains': [
      'Economy'
    ],
    'theme': 'National Accounts',
    'subtheme': '',
    'product': '',
    'application_url': '',
    'imgSrc': 'https://ifp-cms-dev.scad.gov.ae/sites/default/files/uploads/images/Tnhumb-img-1.png',
    'attachment': '',
    'policy_guide': '',
    'note': '',
    'search_tags': [],
    'narrative': '',
    'Indicator': ' Real Estate - Highlights Section Sale ',
    'indicator_list': '599',
    'indicatorValues_subtitle': '',
    'indicatorValues_title': '',
    'visualization_subtitle': '',
    'visualization_title': '',
    'enableDynamicPanel': true,
    'infogramUrl': 'https://coi.infogram.com/1pkr1dzrkdvx7dh9x7yn7vvxgyu3dp27qep',
    'confidenceIntervalMeta': '',
    'enableConfidenceInterval': false,
    'endpoint_label': '',
    'endpoint_title': '',
    'endpoint_url': '',
    'default_layer': 'False',
    'show_on_legend': 'False',
    'defaultDistrictId': '',
    'endpointType': '',
    'nodeId': '',
    'summaryCardId': '',
    'endpoint_icon_id': '',
    'cardDate': '',
    'dashboardUrl': '',
    'enablePointToggle': true,
    'maxPointLimit': '',
    'minLimitYAxis': '',
    'publication_date': '2023-06-22',
    'tagName': 'Insights Discovery',
    'tagColorCode': '#dbaa21',
    'showInsights': 'False',
    'height': '',
    'host_url': '',
    'embedded_code_version': '',
    'site_root': '',
    'external_name': '',
    'tabs': 'No',
    'toolbar': 'No',
    'showAppBanner': 'False',
    'compare_data': '',
    'dynamicPanel': [
      {
        'id': 'highlights',
        'title': 'Highlights',
        'meta': {
          'indicatorValues': {
            'selectAggregation': {
              'label': 'Time Period',
              'options': [
                'Year to Date',
                'Year on Year',
                'Month on Month'
              ],
              'default': 'Year to Date'
            },
            'valuesMeta': [
              {
                'id': 'year-to-date',
                'title': '',
                'viewName': 'VW_STR_INDICATORS',
                'additionalText': 'Jan to',
                'dimension': {
                  'INDICATOR_ID': [
                    'OCCUPANCY_YTD'
                  ]
                },
                'dateFormat': 'MMM YYYY'
              },
              {
                'id': 'year-to-date-value',
                'title': 'Occupancy',
                'subtitle': '',
                'hideYearDate': true,
                'viewName': 'VW_STR_INDICATORS',
                'iconId': 'RENT',
                'iconLightId': 'RENT_LIGHT',
                'note': 'Represent the percentage of available rooms sold during a specified time period. Occupancy rate is calculated by dividing the number of rooms sold by rooms available. @br@Occupancy rate = Rooms Sold / Rooms available',
                'dimension': {
                  'INDICATOR_ID': [
                    'OCCUPANCY_YTD',
                    'OCCUPANCY_MOM',
                    'OCCUPANCY_YOY',
                    'CH_OCCUPANCY_YTD',
                    'CH_OCCUPANCY_MOM',
                    'CH_OCCUPANCY_YOY'
                  ]
                }
              },
              {
                'id': 'year-to-date-value',
                'title': 'Available rooms',
                'subtitle': '(Room\'s Supply)',
                'viewName': 'VW_STR_INDICATORS',
                'iconId': 'VILLA',
                'note': 'The number of rooms in a hotel or set of hotels multiplied by the number of days in a specified time period. @br@Example: 100 rooms in subject hotel x 31 days in the month = Room Supply of 3,100 for the month.',
                'dimension': {
                  'INDICATOR_ID': [
                    'SUPPLY_YTD',
                    'SUPPLY_MOM',
                    'SUPPLY_YOY',
                    'CH_SUPPLY_YTD',
                    'CH_SUPPLY_MOM',
                    'CH_SUPPLY_YOY'
                  ]
                }
              },
              {
                'id': 'year-to-date-value',
                'title': 'Occupied/ Sold rooms',
                'subtitle': '(Room\'s Demand)',
                'viewName': 'VW_STR_INDICATORS',
                'iconId': 'APARTMENT',
                'note': 'The number of rooms sold in a specified time period (excludes complimentary rooms)',
                'dimension': {
                  'INDICATOR_ID': [
                    'DEMAND_YTD',
                    'DEMAND_MOM',
                    'DEMAND_YOY',
                    'CH_DEMAND_YTD',
                    'CH_DEMAND_MOM',
                    'CH_DEMAND_YOY'
                  ]
                }
              },
              {
                'id': 'year-to-date-value',
                'title': 'Revenue',
                'subtitle': '(Room\'s Revenue)',
                'viewName': 'VW_STR_INDICATORS',
                'iconId': 'NUM_LISTING',
                'note': 'Total room revenue generated from the guestroom rentals or sales',
                'dimension': {
                  'INDICATOR_ID': [
                    'REVENUE_YTD',
                    'REVENUE_MOM',
                    'REVENUE_YOY',
                    'CH_REVENUE_YTD',
                    'CH_REVENUE_MOM',
                    'CH_REVENUE_YOY'
                  ]
                }
              },
              {
                'id': 'year-to-date-value',
                'title': 'Revenue Per Available Room',
                'subtitle': '(RevPAR)',
                'viewName': 'VW_STR_INDICATORS',
                'iconId': 'COMMERCIAL',
                'iconLightId': 'COMMERCIAL_LIGHT',
                'note': 'Total room revenue divided by the total number of available rooms. @br@RevPAR= Room Revenue/Rooms Available',
                'dimension': {
                  'INDICATOR_ID': [
                    'REVPAR_YTD',
                    'REVPAR_MOM',
                    'REVPAR_YOY',
                    'CH_REVPAR_YTD',
                    'CH_REVPAR_MOM',
                    'CH_REVPAR_YOY'
                  ]
                }
              },
              {
                'id': 'year-to-date-value',
                'title': 'Average Daily Rate',
                'subtitle': '(ADR)',
                'format': 'USD',
                'viewName': 'VW_STR_INDICATORS',
                'iconId': 'LAND',
                'iconLightId': 'LAND_LIGHT',
                'note': 'A measure of the average rate paid for rooms sold, calculated by dividing room revenue by rooms sold. @br@ADR = Room Revenue/Rooms Sold',
                'dimension': {
                  'INDICATOR_ID': [
                    'ADR_YTD',
                    'ADR_MOM',
                    'ADR_YOY',
                    'CH_ADR_YTD',
                    'CH_ADR_MOM',
                    'CH_ADR_YOY'
                  ]
                }
              }
            ]
          }
        }
      }
    ],
    'language': 'EN',
    'sortVisualizations': true,
    'indicatorValues': {
      'overviewValuesMeta': [
        {
          'id': 'year-to-date-value',
          'title': 'Occupancy',
          'aggregation': 'Year to Date',
          'hideYearDate': true,
          'viewName': 'VW_STR_INDICATORS',
          'dimension': {
            'INDICATOR_ID': [
              'OCCUPANCY_YTD',
              'OCCUPANCY_MOM',
              'OCCUPANCY_YOY',
              'CH_OCCUPANCY_YTD',
              'CH_OCCUPANCY_MOM',
              'CH_OCCUPANCY_YOY'
            ]
          },
          'values': [
            {
              'value': '73.7',
              'type': 'PERCENTAGE',
              'aggregation': 'Year to Date',
              'date': '2023-03-01',
              'dimension': null
            },
            {
              'value': '73.7',
              'type': 'PERCENTAGE',
              'aggregation': 'Month on Month',
              'date': '2023-03-01',
              'dimension': null
            },
            {
              'value': '73.7',
              'type': 'PERCENTAGE',
              'aggregation': 'Year on Year',
              'date': '2023-03-01',
              'dimension': null
            },
            {
              'value': '1.23626373626374',
              'type': 'PERCENTAGE_CHANGE',
              'aggregation': 'Year to Date',
              'date': '2023-03-01',
              'dimension': null
            },
            {
              'value': '-5.14800514800515',
              'type': 'PERCENTAGE_CHANGE',
              'aggregation': 'Month on Month',
              'date': '2023-03-01',
              'dimension': null
            },
            {
              'value': '-2.64200792602378',
              'type': 'PERCENTAGE_CHANGE',
              'aggregation': 'Year on Year',
              'date': '2023-03-01',
              'dimension': null
            }
          ]
        },
        {
          'id': 'percentage-change',
          'title': 'Y/Y',
          'type': 'dynamic-with-title-template',
          'valueFormat': 'percentage_1.1-1',
          'color': '#3667ff',
          'templateFormat': 'dateQuarterYYYY',
          'viewName': 'VW_STR_MONTHLY_KPIS_CITY',
          'dimension': {
            'INDICATOR_ID': 'KPIS_BY_CITY_MONTHLY'
          },
          'filterBy': {
            'CATEGORY_NAME': 'OCCUPANCY',
            'CITY_NAME': 'ABU DHABI'
          },
          'unit': 'years',
          'period': 1,
          'dateStart': '2022-03-01',
          'dateEnd': '2023-03-01',
          'value': '-2.64'
        }
      ]
    },
    'indicatorTools': [
      {
        'id': 'export-png',
        'disabled': true,
        'label': 'Export PNG'
      },
      {
        'id': 'export-csv-all',
        'label': 'Extract All'
      },
      {
        'id': 'export-csv',
        'label': 'Extract Current Selection'
      }
    ],
    'indicatorActions': [
      {
        'id': 'toggle-fullscreen',
        'type': 'simple',
        'label': 'Toggle Fullscreen'
      }
    ],
    'country_flag': '',
    'field_page_light_icon': '',
    'page_menu_icon': '',
    'page_menu_light_icon': '',
    'field_page_menu_light_icon': '',
    'showSecondLevel': true
  },
  {
    'id': '545',
    'type': 'Internal',
    'component_title': 'Trade by Transaction Type',
    'component_subtitle': 'Trade by Transaction Type',
    'page_icon': '',
    'page_light_icon': '',
    'domains': [
      'Economy'
    ],
    'theme': '',
    'subtheme': '',
    'product': '',
    'application_url': '',
    'imgSrc': '',
    'attachment': '',
    'policy_guide': '',
    'note': '',
    'search_tags': [],
    'narrative': '',
    'Indicator': ' non-oil-Trade-by-Transaction-Type ',
    'indicator_list': '',
    'indicatorValues_subtitle': '',
    'indicatorValues_title': '',
    'visualization_subtitle': '',
    'visualization_title': '',
    'enableDynamicPanel': false,
    'listofDyanmicPanelContent': [],
    'highlightsMeta': '',
    'infogramUrl': '',
    'confidenceIntervalMeta': '',
    'enableConfidenceInterval': false,
    'endpoint_label': '',
    'endpoint_title': '',
    'endpoint_url': '',
    'default_layer': 'False',
    'show_on_legend': 'False',
    'defaultDistrictId': '',
    'endpointType': '',
    'nodeId': '',
    'summaryCardId': '',
    'endpoint_icon_id': '',
    'cardDate': '',
    'dashboardUrl': '',
    'enablePointToggle': false,
    'maxPointLimit': '',
    'minLimitYAxis': '',
    'publication_date': '2022-06-08',
    'tagName': '',
    'tagColorCode': '',
    'showInsights': '',
    'height': '',
    'host_url': '',
    'embedded_code_version': '',
    'site_root': '',
    'external_name': '',
    'tabs': '',
    'toolbar': '',
    'showAppBanner': '',
    'compare_data': '',
    'language': 'EN',
    'sortVisualizations': true,
    'indicatorTools': [
      {
        'id': 'export-png',
        'disabled': true,
        'label': 'Export PNG'
      },
      {
        'id': 'export-csv',
        'label': 'Export CSV'
      }
    ],
    'indicatorActions': [
      {
        'id': 'switch-view',
        'type': 'simple',
        'label': 'Switch view'
      }
    ],
    'driverTitle': 'Time Selector',
    'driverSubtitle': 'Modify below to view changes in the projection',
    'indicatorDrivers': [
      {
        'title': 'Forecast',
        'id': 'parameter_1_range',
        'type': 'radio',
        'subtitle': 'Range',
        'note': '1 quarter forecast',
        'options': [
          {
            'label': '1 Quarter',
            'value': '1q',
            'isSelected': true
          },
          {
            'label': '2 Quarter',
            'value': '2q',
            'isSelected': false
          }
        ]
      }
    ],
    'indicatorValues': {
      'valuesMeta': [
        {
          'id': 'latest-date',
          'title': 'Forecast Year',
          'type': 'static-with-title-template',
          'valueFormat': 'date_MMM y',
          'viewName': 'VW_RI_GDP_SECTOR_CRNT_IND_PCT',
          'comboIdTable': 'DS_GDP_SECTORS_COMBO_CRNT',
          'hasDefault': true,
          'dimension': {
            'SECTOR': 'OIL'
          },
          'dateFormat': 'YYYY-MM-DD'
        }
      ]
    },
    'indicatorVisualizations': {
      'visualizationsMeta': [
        {
          'id': 'tree-chart-economy-sector-indicator',
          'type': 'tree-map-with-change-chart',
          'sortOrder': 1,
          'viewName': 'VW_RI_GDP_SECTOR_CRNT_IND_PCT',
          'comboIdTable': 'DS_GDP_SECTORS_COMBO_CRNT',
          'xAxisLabel': 'Other non-oil sectors (Government, Utilities, and Household Production) are not included in this forecast.',
          'hasDefault': true,
          'colorCodes': {
            'positive': {
              '1_5': '#19AA2B',
              '5_10': '#0e931f',
              '10_20': '#007506',
              '20_60': '#005700',
              '60+': '#003b00'
            },
            'negetive': {
              '1_5': '#e54d35',
              '5_10': '#c83220',
              '10_20': '#ac0e0a',
              '20_60': '#870101',
              '60+': '#610000'
            },
            'neutral': '#666878'
          },
          'selectRangeBy': 'CHANGE',
          'colorsRange': [
            {
              'range': '1_5',
              'valueLL': 1,
              'valueUL': 5
            },
            {
              'range': '5_10',
              'valueLL': 5,
              'valueUL': 10
            },
            {
              'range': '10_20',
              'valueLL': 10,
              'valueUL': 20
            },
            {
              'range': '20_60',
              'valueLL': 20,
              'valueUL': 60
            },
            {
              'range': '60+',
              'valueLL': 60,
              'valueUL': 'Infinity'
            },
            {
              'range': 'neutral',
              'valueLL': 0,
              'valueUL': 1
            }
          ],
          'seriesMeta': [
            {
              'id': 'oil-gdp',
              'label': 'Oil',
              'color': 'rgba(56, 101, 255, 0.2)',
              'type': 'forecast',
              'dimension': {
                'SECTOR': 'Oil'
              },
              'valueAccessor': {
                'type': 'value',
                'path': 'VALUE_PERC_ECO'
              },
              'changeAccessor': {
                'type': 'value',
                'label': 'Change',
                'path': 'CHANGE'
              },
              'sectorNameAccessor': {
                'type': 'value',
                'path': 'SECTOR'
              },
              'proportionAccessor': {
                'type': 'value',
                'label': 'Proportion of Total Economy',
                'path': 'VALUE_PERC_ECO'
              },
              'currentValueAccessor': {
                'type': 'value',
                'label': 'Current Value',
                'path': 'VALUE_CURRENT'
              },
              'forecastedValueAccessor': {
                'type': 'value',
                'label': 'Forecasted Value',
                'path': 'VALUE_FORECAST'
              }
            },
            {
              'id': 'construction',
              'label': 'Construction',
              'color': 'rgba(56, 101, 255, 0.3)',
              'type': 'forecast',
              'dimension': {
                'SECTOR': 'CONSTRUCTION'
              },
              'valueAccessor': {
                'type': 'value',
                'path': 'VALUE_PERC_ECO'
              },
              'changeAccessor': {
                'type': 'value',
                'label': 'Change',
                'path': 'CHANGE'
              },
              'sectorNameAccessor': {
                'type': 'value',
                'path': 'SECTOR'
              },
              'proportionAccessor': {
                'type': 'value',
                'label': 'Proportion of Total Economy',
                'path': 'VALUE_PERC_ECO'
              },
              'currentValueAccessor': {
                'type': 'value',
                'label': 'Current Value',
                'path': 'VALUE_CURRENT'
              },
              'forecastedValueAccessor': {
                'type': 'value',
                'label': 'Forecasted Value',
                'path': 'VALUE_FORECAST'
              }
            },
            {
              'id': 'financial-and-insurance',
              'label': 'Financial and insurance',
              'color': 'rgba(56, 101, 255, 0.3)',
              'type': 'forecast',
              'dimension': {
                'SECTOR': 'Financial and insurance'
              },
              'valueAccessor': {
                'type': 'value',
                'path': 'VALUE_PERC_ECO'
              },
              'changeAccessor': {
                'type': 'value',
                'label': 'Change',
                'path': 'CHANGE'
              },
              'sectorNameAccessor': {
                'type': 'value',
                'path': 'SECTOR'
              },
              'proportionAccessor': {
                'type': 'value',
                'label': 'Proportion of Total Economy',
                'path': 'VALUE_PERC_ECO'
              },
              'currentValueAccessor': {
                'type': 'value',
                'label': 'Current Value',
                'path': 'VALUE_CURRENT'
              },
              'forecastedValueAccessor': {
                'type': 'value',
                'label': 'Forecasted Value',
                'path': 'VALUE_FORECAST'
              }
            },
            {
              'id': 'manufacturing',
              'label': 'Manufacturing',
              'color': 'rgba(56, 101, 255, 0.3)',
              'type': 'forecast',
              'dimension': {
                'SECTOR': 'Manufacturing'
              },
              'valueAccessor': {
                'type': 'value',
                'path': 'VALUE_PERC_ECO'
              },
              'changeAccessor': {
                'type': 'value',
                'label': 'Change',
                'path': 'CHANGE'
              },
              'sectorNameAccessor': {
                'type': 'value',
                'path': 'SECTOR'
              },
              'proportionAccessor': {
                'type': 'value',
                'label': 'Proportion of Total Economy',
                'path': 'VALUE_PERC_ECO'
              },
              'currentValueAccessor': {
                'type': 'value',
                'label': 'Current Value',
                'path': 'VALUE_CURRENT'
              },
              'forecastedValueAccessor': {
                'type': 'value',
                'label': 'Forecasted Value',
                'path': 'VALUE_FORECAST'
              }
            },
            {
              'id': 'publi-administration-and-defence-compulsory-social-security',
              'label': 'Public admin & Defence; Compulsory Social Sec.',
              'color': 'rgba(56, 101, 255, 0.3)',
              'type': 'forecast',
              'dimension': {
                'SECTOR': 'Public admin & Defence; Compulsory Social Sec.'
              },
              'valueAccessor': {
                'type': 'value',
                'path': 'VALUE_PERC_ECO'
              },
              'changeAccessor': {
                'type': 'value',
                'label': 'Change',
                'path': 'CHANGE'
              },
              'sectorNameAccessor': {
                'type': 'value',
                'path': 'SECTOR'
              },
              'proportionAccessor': {
                'type': 'value',
                'label': 'Proportion of Total Economy',
                'path': 'VALUE_PERC_ECO'
              },
              'currentValueAccessor': {
                'type': 'value',
                'label': 'Current Value',
                'path': 'VALUE_CURRENT'
              },
              'forecastedValueAccessor': {
                'type': 'value',
                'label': 'Forecasted Value',
                'path': 'VALUE_FORECAST'
              }
            },
            {
              'id': 'wholesale-and-retail-trade-repair-of-motor-vehicles-and-motorcycles',
              'label': 'Wholesale & Retail Trade; Repair of vehicles & Motorcycles',
              'color': 'rgba(56, 101, 255, 0.3)',
              'type': 'forecast',
              'dimension': {
                'SECTOR': 'Wholesale & Retail Trade; Repair of vehicles & Motorcycles'
              },
              'valueAccessor': {
                'type': 'value',
                'path': 'VALUE_PERC_ECO'
              },
              'changeAccessor': {
                'type': 'value',
                'label': 'Change',
                'path': 'CHANGE'
              },
              'sectorNameAccessor': {
                'type': 'value',
                'path': 'SECTOR'
              },
              'proportionAccessor': {
                'type': 'value',
                'label': 'Proportion of Total Economy',
                'path': 'VALUE_PERC_ECO'
              },
              'currentValueAccessor': {
                'type': 'value',
                'label': 'Current Value',
                'path': 'VALUE_CURRENT'
              },
              'forecastedValueAccessor': {
                'type': 'value',
                'label': 'Forecasted Value',
                'path': 'VALUE_FORECAST'
              }
            },
            {
              'id': 'information-and-communication',
              'label': 'Information and communication',
              'color': 'rgba(56, 101, 255, 0.3)',
              'type': 'forecast',
              'dimension': {
                'SECTOR': 'INFORMATION AND COMMUNICATION'
              },
              'valueAccessor': {
                'type': 'value',
                'path': 'VALUE_PERC_ECO'
              },
              'changeAccessor': {
                'type': 'value',
                'label': 'Change',
                'path': 'CHANGE'
              },
              'sectorNameAccessor': {
                'type': 'value',
                'path': 'SECTOR'
              },
              'proportionAccessor': {
                'type': 'value',
                'label': 'Proportion of Total Economy',
                'path': 'VALUE_PERC_ECO'
              },
              'currentValueAccessor': {
                'type': 'value',
                'label': 'Current Value',
                'path': 'VALUE_CURRENT'
              },
              'forecastedValueAccessor': {
                'type': 'value',
                'label': 'Forecasted Value',
                'path': 'VALUE_FORECAST'
              }
            },
            {
              'id': 'electricity-gas-and-water-supply-waste-management',
              'label': 'Electricity, Gas, & Water supply; Waste Mgmt',
              'color': 'rgba(56, 101, 255, 0.3)',
              'type': 'forecast',
              'dimension': {
                'SECTOR': 'Electricity, Gas, & Water supply; Waste Mgmt'
              },
              'valueAccessor': {
                'type': 'value',
                'path': 'VALUE_PERC_ECO'
              },
              'changeAccessor': {
                'type': 'value',
                'label': 'Change',
                'path': 'CHANGE'
              },
              'sectorNameAccessor': {
                'type': 'value',
                'path': 'SECTOR'
              },
              'proportionAccessor': {
                'type': 'value',
                'label': 'Proportion of Total Economy',
                'path': 'VALUE_PERC_ECO'
              },
              'currentValueAccessor': {
                'type': 'value',
                'label': 'Current Value',
                'path': 'VALUE_CURRENT'
              },
              'forecastedValueAccessor': {
                'type': 'value',
                'label': 'Forecasted Value',
                'path': 'VALUE_FORECAST'
              }
            },
            {
              'id': 'professional-scientific-and-technical-administrative-and-support-services',
              'label': 'Professional, scientific and technical& Administrative and support services',
              'color': 'rgba(56, 101, 255, 0.3)',
              'type': 'forecast',
              'dimension': {
                'SECTOR': 'Professional, scientific and technical& Administrative and support services'
              },
              'valueAccessor': {
                'type': 'value',
                'path': 'VALUE_PERC_ECO'
              },
              'changeAccessor': {
                'type': 'value',
                'label': 'Change',
                'path': 'CHANGE'
              },
              'sectorNameAccessor': {
                'type': 'value',
                'path': 'SECTOR'
              },
              'proportionAccessor': {
                'type': 'value',
                'label': 'Proportion of Total Economy',
                'path': 'VALUE_PERC_ECO'
              },
              'currentValueAccessor': {
                'type': 'value',
                'label': 'Current Value',
                'path': 'VALUE_CURRENT'
              },
              'forecastedValueAccessor': {
                'type': 'value',
                'label': 'Forecasted Value',
                'path': 'VALUE_FORECAST'
              }
            },
            {
              'id': 'real-estate',
              'label': 'Real estate',
              'color': 'rgba(56, 101, 255, 0.3)',
              'type': 'forecast',
              'dimension': {
                'SECTOR': 'Real estate'
              },
              'valueAccessor': {
                'type': 'value',
                'path': 'VALUE_PERC_ECO'
              },
              'changeAccessor': {
                'type': 'value',
                'label': 'Change',
                'path': 'CHANGE'
              },
              'sectorNameAccessor': {
                'type': 'value',
                'path': 'SECTOR'
              },
              'proportionAccessor': {
                'type': 'value',
                'label': 'Proportion of Total Economy',
                'path': 'VALUE_PERC_ECO'
              },
              'currentValueAccessor': {
                'type': 'value',
                'label': 'Current Value',
                'path': 'VALUE_CURRENT'
              },
              'forecastedValueAccessor': {
                'type': 'value',
                'label': 'Forecasted Value',
                'path': 'VALUE_FORECAST'
              }
            }
          ]
        },
        {
          'id': 'tree-chart-economy-sector-indicator-2',
          'type': 'tree-map-with-change-chart',
          'sortOrder': 2,
          'viewName': 'VW_RI_GDP_SECTOR_CRNT_IND_PCT',
          'comboIdTable': 'DS_GDP_SECTORS_COMBO_CRNT',
          'xAxisLabel': 'Other non-oil sectors (Government, Utilities, and Household Production) are not included in this forecast.',
          'hasDefault': true,
          'colorCodes': {
            'positive': {
              '1_5': '#19AA2B',
              '5_10': '#0e931f',
              '10_20': '#007506',
              '20_60': '#005700',
              '60+': '#003b00'
            },
            'negetive': {
              '1_5': '#e54d35',
              '5_10': '#c83220',
              '10_20': '#ac0e0a',
              '20_60': '#870101',
              '60+': '#610000'
            },
            'neutral': '#666878'
          },
          'selectRangeBy': 'CHANGE',
          'colorsRange': [
            {
              'range': '1_5',
              'valueLL': 1,
              'valueUL': 5
            },
            {
              'range': '5_10',
              'valueLL': 5,
              'valueUL': 10
            },
            {
              'range': '10_20',
              'valueLL': 10,
              'valueUL': 20
            },
            {
              'range': '20_60',
              'valueLL': 20,
              'valueUL': 60
            },
            {
              'range': '60+',
              'valueLL': 60,
              'valueUL': 'Infinity'
            },
            {
              'range': 'neutral',
              'valueLL': 0,
              'valueUL': 1
            }
          ],
          'seriesMeta': [
            {
              'id': 'construction',
              'label': 'Construction',
              'color': 'rgba(56, 101, 255, 0.3)',
              'type': 'forecast',
              'dimension': {
                'SECTOR': 'CONSTRUCTION'
              },
              'valueAccessor': {
                'type': 'value',
                'path': 'VALUE_PERC_ECO'
              },
              'changeAccessor': {
                'type': 'value',
                'label': 'Change',
                'path': 'CHANGE'
              },
              'sectorNameAccessor': {
                'type': 'value',
                'path': 'SECTOR'
              },
              'proportionAccessor': {
                'type': 'value',
                'label': 'Proportion of Total Economy',
                'path': 'VALUE_PERC_ECO'
              },
              'currentValueAccessor': {
                'type': 'value',
                'label': 'Current Value',
                'path': 'VALUE_CURRENT'
              },
              'forecastedValueAccessor': {
                'type': 'value',
                'label': 'Forecasted Value',
                'path': 'VALUE_FORECAST'
              }
            },
            {
              'id': 'financial-and-insurance',
              'label': 'Financial and insurance',
              'color': 'rgba(56, 101, 255, 0.3)',
              'type': 'forecast',
              'dimension': {
                'SECTOR': 'Financial and insurance'
              },
              'valueAccessor': {
                'type': 'value',
                'path': 'VALUE_PERC_ECO'
              },
              'changeAccessor': {
                'type': 'value',
                'label': 'Change',
                'path': 'CHANGE'
              },
              'sectorNameAccessor': {
                'type': 'value',
                'path': 'SECTOR'
              },
              'proportionAccessor': {
                'type': 'value',
                'label': 'Proportion of Total Economy',
                'path': 'VALUE_PERC_ECO'
              },
              'currentValueAccessor': {
                'type': 'value',
                'label': 'Current Value',
                'path': 'VALUE_CURRENT'
              },
              'forecastedValueAccessor': {
                'type': 'value',
                'label': 'Forecasted Value',
                'path': 'VALUE_FORECAST'
              }
            },
            {
              'id': 'manufacturing',
              'label': 'Manufacturing',
              'color': 'rgba(56, 101, 255, 0.3)',
              'type': 'forecast',
              'dimension': {
                'SECTOR': 'Manufacturing'
              },
              'valueAccessor': {
                'type': 'value',
                'path': 'VALUE_PERC_ECO'
              },
              'changeAccessor': {
                'type': 'value',
                'label': 'Change',
                'path': 'CHANGE'
              },
              'sectorNameAccessor': {
                'type': 'value',
                'path': 'SECTOR'
              },
              'proportionAccessor': {
                'type': 'value',
                'label': 'Proportion of Total Economy',
                'path': 'VALUE_PERC_ECO'
              },
              'currentValueAccessor': {
                'type': 'value',
                'label': 'Current Value',
                'path': 'VALUE_CURRENT'
              },
              'forecastedValueAccessor': {
                'type': 'value',
                'label': 'Forecasted Value',
                'path': 'VALUE_FORECAST'
              }
            },
            {
              'id': 'publi-administration-and-defence-compulsory-social-security',
              'label': 'Public admin & Defence; Compulsory Social Sec.',
              'color': 'rgba(56, 101, 255, 0.3)',
              'type': 'forecast',
              'dimension': {
                'SECTOR': 'Public admin & Defence; Compulsory Social Sec.'
              },
              'valueAccessor': {
                'type': 'value',
                'path': 'VALUE_PERC_ECO'
              },
              'changeAccessor': {
                'type': 'value',
                'label': 'Change',
                'path': 'CHANGE'
              },
              'sectorNameAccessor': {
                'type': 'value',
                'path': 'SECTOR'
              },
              'proportionAccessor': {
                'type': 'value',
                'label': 'Proportion of Total Economy',
                'path': 'VALUE_PERC_ECO'
              },
              'currentValueAccessor': {
                'type': 'value',
                'label': 'Current Value',
                'path': 'VALUE_CURRENT'
              },
              'forecastedValueAccessor': {
                'type': 'value',
                'label': 'Forecasted Value',
                'path': 'VALUE_FORECAST'
              }
            },
            {
              'id': 'wholesale-and-retail-trade-repair-of-motor-vehicles-and-motorcycles',
              'label': 'Wholesale & Retail Trade; Repair of vehicles & Motorcycles',
              'color': 'rgba(56, 101, 255, 0.3)',
              'type': 'forecast',
              'dimension': {
                'SECTOR': 'Wholesale & Retail Trade; Repair of vehicles & Motorcycles'
              },
              'valueAccessor': {
                'type': 'value',
                'path': 'VALUE_PERC_ECO'
              },
              'changeAccessor': {
                'type': 'value',
                'label': 'Change',
                'path': 'CHANGE'
              },
              'sectorNameAccessor': {
                'type': 'value',
                'path': 'SECTOR'
              },
              'proportionAccessor': {
                'type': 'value',
                'label': 'Proportion of Total Economy',
                'path': 'VALUE_PERC_ECO'
              },
              'currentValueAccessor': {
                'type': 'value',
                'label': 'Current Value',
                'path': 'VALUE_CURRENT'
              },
              'forecastedValueAccessor': {
                'type': 'value',
                'label': 'Forecasted Value',
                'path': 'VALUE_FORECAST'
              }
            },
            {
              'id': 'information-and-communication',
              'label': 'Information and communication',
              'color': 'rgba(56, 101, 255, 0.3)',
              'type': 'forecast',
              'dimension': {
                'SECTOR': 'INFORMATION AND COMMUNICATION'
              },
              'valueAccessor': {
                'type': 'value',
                'path': 'VALUE_PERC_ECO'
              },
              'changeAccessor': {
                'type': 'value',
                'label': 'Change',
                'path': 'CHANGE'
              },
              'sectorNameAccessor': {
                'type': 'value',
                'path': 'SECTOR'
              },
              'proportionAccessor': {
                'type': 'value',
                'label': 'Proportion of Total Economy',
                'path': 'VALUE_PERC_ECO'
              },
              'currentValueAccessor': {
                'type': 'value',
                'label': 'Current Value',
                'path': 'VALUE_CURRENT'
              },
              'forecastedValueAccessor': {
                'type': 'value',
                'label': 'Forecasted Value',
                'path': 'VALUE_FORECAST'
              }
            },
            {
              'id': 'electricity-gas-and-water-supply-waste-management',
              'label': 'Electricity, Gas, & Water supply; Waste Mgmt',
              'color': 'rgba(56, 101, 255, 0.3)',
              'type': 'forecast',
              'dimension': {
                'SECTOR': 'Electricity, Gas, & Water supply; Waste Mgmt'
              },
              'valueAccessor': {
                'type': 'value',
                'path': 'VALUE_PERC_ECO'
              },
              'changeAccessor': {
                'type': 'value',
                'label': 'Change',
                'path': 'CHANGE'
              },
              'sectorNameAccessor': {
                'type': 'value',
                'path': 'SECTOR'
              },
              'proportionAccessor': {
                'type': 'value',
                'label': 'Proportion of Total Economy',
                'path': 'VALUE_PERC_ECO'
              },
              'currentValueAccessor': {
                'type': 'value',
                'label': 'Current Value',
                'path': 'VALUE_CURRENT'
              },
              'forecastedValueAccessor': {
                'type': 'value',
                'label': 'Forecasted Value',
                'path': 'VALUE_FORECAST'
              }
            },
            {
              'id': 'professional-scientific-and-technical-administrative-and-support-services',
              'label': 'Professional, scientific and technical& Administrative and support services',
              'color': 'rgba(56, 101, 255, 0.3)',
              'type': 'forecast',
              'dimension': {
                'SECTOR': 'Professional, scientific and technical& Administrative and support services'
              },
              'valueAccessor': {
                'type': 'value',
                'path': 'VALUE_PERC_ECO'
              },
              'changeAccessor': {
                'type': 'value',
                'label': 'Change',
                'path': 'CHANGE'
              },
              'sectorNameAccessor': {
                'type': 'value',
                'path': 'SECTOR'
              },
              'proportionAccessor': {
                'type': 'value',
                'label': 'Proportion of Total Economy',
                'path': 'VALUE_PERC_ECO'
              },
              'currentValueAccessor': {
                'type': 'value',
                'label': 'Current Value',
                'path': 'VALUE_CURRENT'
              },
              'forecastedValueAccessor': {
                'type': 'value',
                'label': 'Forecasted Value',
                'path': 'VALUE_FORECAST'
              }
            },
            {
              'id': 'real-estate',
              'label': 'Real estate',
              'color': 'rgba(56, 101, 255, 0.3)',
              'type': 'forecast',
              'dimension': {
                'SECTOR': 'Real estate'
              },
              'valueAccessor': {
                'type': 'value',
                'path': 'VALUE_PERC_ECO'
              },
              'changeAccessor': {
                'type': 'value',
                'label': 'Change',
                'path': 'CHANGE'
              },
              'sectorNameAccessor': {
                'type': 'value',
                'path': 'SECTOR'
              },
              'proportionAccessor': {
                'type': 'value',
                'label': 'Proportion of Total Economy',
                'path': 'VALUE_PERC_ECO'
              },
              'currentValueAccessor': {
                'type': 'value',
                'label': 'Current Value',
                'path': 'VALUE_CURRENT'
              },
              'forecastedValueAccessor': {
                'type': 'value',
                'label': 'Forecasted Value',
                'path': 'VALUE_FORECAST'
              }
            }
          ]
        }
      ],
      'visualizationDefault': 'tree-chart-economy-sector-indicator'
    },
    'country_flag': '',
    'page_menu_icon': '',
    'isFavorite': 'false',
    'showSecondLevel': false
  },
  {
    'id': '544',
    'type': 'Internal',
    'component_title': 'Top Ten Non-oil Trade Countries For Abu Dhabi (2021 & 2022) : Trade Volume: Current and Forecasted',
    'component_subtitle': 'Top Ten Non-oil Trade Countries For Abu Dhabi (2021 & 2022) : Trade Volume: Current and Forecasted',
    'page_icon': '',
    'page_light_icon': '',
    'domains': [
      'Economy'
    ],
    'theme': '',
    'subtheme': '',
    'product': '',
    'application_url': '',
    'imgSrc': '',
    'attachment': '',
    'policy_guide': '',
    'note': '',
    'search_tags': [],
    'narrative': '',
    'Indicator': ' non-oil-Trade-Volume ',
    'indicator_list': '',
    'indicatorValues_subtitle': '',
    'indicatorValues_title': '',
    'visualization_subtitle': '',
    'visualization_title': '',
    'enableDynamicPanel': false,
    'listofDyanmicPanelContent': [],
    'highlightsMeta': '',
    'infogramUrl': '',
    'confidenceIntervalMeta': '',
    'enableConfidenceInterval': false,
    'endpoint_label': '',
    'endpoint_title': '',
    'endpoint_url': '',
    'default_layer': 'False',
    'show_on_legend': 'False',
    'defaultDistrictId': '',
    'endpointType': '',
    'nodeId': '',
    'summaryCardId': '',
    'endpoint_icon_id': '',
    'cardDate': '',
    'dashboardUrl': '',
    'enablePointToggle': false,
    'maxPointLimit': '',
    'minLimitYAxis': '',
    'publication_date': '2022-06-08',
    'tagName': '',
    'tagColorCode': '',
    'showInsights': '',
    'height': '',
    'host_url': '',
    'embedded_code_version': '',
    'site_root': '',
    'external_name': '',
    'tabs': '',
    'toolbar': '',
    'showAppBanner': '',
    'compare_data': '',
    'language': 'EN',
    'sortVisualizations': true,
    'indicatorTools': [
      {
        'id': 'export-png',
        'disabled': true,
        'label': 'Export PNG'
      },
      {
        'id': 'export-csv',
        'label': 'Export CSV'
      }
    ],
    'indicatorActions': [
      {
        'id': 'switch-view',
        'type': 'simple',
        'label': 'Switch view'
      }
    ],
    'driverTitle': 'Time Selector',
    'driverSubtitle': 'Modify below to view changes in the projection',
    'indicatorDrivers': [
      {
        'title': 'Forecast',
        'id': 'parameter_1_range',
        'type': 'radio',
        'subtitle': 'Range',
        'note': '1 quarter forecast',
        'options': [
          {
            'label': '1 Quarter',
            'value': '1q',
            'isSelected': true
          },
          {
            'label': '2 Quarter',
            'value': '2q',
            'isSelected': false
          }
        ]
      }
    ],
    'indicatorValues': {
      'valuesMeta': [
        {
          'id': 'latest-date',
          'title': 'Forecast Year',
          'type': 'static-with-title-template',
          'valueFormat': 'date_MMM y',
          'viewName': 'VW_RI_GDP_SECTOR_CRNT_IND_PCT',
          'comboIdTable': 'DS_GDP_SECTORS_COMBO_CRNT',
          'hasDefault': true,
          'dimension': {
            'SECTOR': 'OIL'
          },
          'dateFormat': 'YYYY-MM-DD'
        }
      ]
    },
    'indicatorVisualizations': {
      'visualizationsMeta': [
        {
          'id': 'tree-chart-economy-sector-indicator',
          'type': 'tree-map-with-change-chart',
          'sortOrder': 1,
          'viewName': 'VW_RI_GDP_SECTOR_CRNT_IND_PCT',
          'comboIdTable': 'DS_GDP_SECTORS_COMBO_CRNT',
          'xAxisLabel': 'Other non-oil sectors (Government, Utilities, and Household Production) are not included in this forecast.',
          'hasDefault': true,
          'colorCodes': {
            'positive': {
              '1_5': '#19AA2B',
              '5_10': '#0e931f',
              '10_20': '#007506',
              '20_60': '#005700',
              '60+': '#003b00'
            },
            'negetive': {
              '1_5': '#e54d35',
              '5_10': '#c83220',
              '10_20': '#ac0e0a',
              '20_60': '#870101',
              '60+': '#610000'
            },
            'neutral': '#666878'
          },
          'selectRangeBy': 'CHANGE',
          'colorsRange': [
            {
              'range': '1_5',
              'valueLL': 1,
              'valueUL': 5
            },
            {
              'range': '5_10',
              'valueLL': 5,
              'valueUL': 10
            },
            {
              'range': '10_20',
              'valueLL': 10,
              'valueUL': 20
            },
            {
              'range': '20_60',
              'valueLL': 20,
              'valueUL': 60
            },
            {
              'range': '60+',
              'valueLL': 60,
              'valueUL': 'Infinity'
            },
            {
              'range': 'neutral',
              'valueLL': 0,
              'valueUL': 1
            }
          ],
          'seriesMeta': [
            {
              'id': 'oil-gdp',
              'label': 'Oil',
              'color': 'rgba(56, 101, 255, 0.2)',
              'type': 'forecast',
              'dimension': {
                'SECTOR': 'Oil'
              },
              'valueAccessor': {
                'type': 'value',
                'path': 'VALUE_PERC_ECO'
              },
              'changeAccessor': {
                'type': 'value',
                'label': 'Change',
                'path': 'CHANGE'
              },
              'sectorNameAccessor': {
                'type': 'value',
                'path': 'SECTOR'
              },
              'proportionAccessor': {
                'type': 'value',
                'label': 'Proportion of Total Economy',
                'path': 'VALUE_PERC_ECO'
              },
              'currentValueAccessor': {
                'type': 'value',
                'label': 'Current Value',
                'path': 'VALUE_CURRENT'
              },
              'forecastedValueAccessor': {
                'type': 'value',
                'label': 'Forecasted Value',
                'path': 'VALUE_FORECAST'
              }
            },
            {
              'id': 'construction',
              'label': 'Construction',
              'color': 'rgba(56, 101, 255, 0.3)',
              'type': 'forecast',
              'dimension': {
                'SECTOR': 'CONSTRUCTION'
              },
              'valueAccessor': {
                'type': 'value',
                'path': 'VALUE_PERC_ECO'
              },
              'changeAccessor': {
                'type': 'value',
                'label': 'Change',
                'path': 'CHANGE'
              },
              'sectorNameAccessor': {
                'type': 'value',
                'path': 'SECTOR'
              },
              'proportionAccessor': {
                'type': 'value',
                'label': 'Proportion of Total Economy',
                'path': 'VALUE_PERC_ECO'
              },
              'currentValueAccessor': {
                'type': 'value',
                'label': 'Current Value',
                'path': 'VALUE_CURRENT'
              },
              'forecastedValueAccessor': {
                'type': 'value',
                'label': 'Forecasted Value',
                'path': 'VALUE_FORECAST'
              }
            },
            {
              'id': 'financial-and-insurance',
              'label': 'Financial and insurance',
              'color': 'rgba(56, 101, 255, 0.3)',
              'type': 'forecast',
              'dimension': {
                'SECTOR': 'Financial and insurance'
              },
              'valueAccessor': {
                'type': 'value',
                'path': 'VALUE_PERC_ECO'
              },
              'changeAccessor': {
                'type': 'value',
                'label': 'Change',
                'path': 'CHANGE'
              },
              'sectorNameAccessor': {
                'type': 'value',
                'path': 'SECTOR'
              },
              'proportionAccessor': {
                'type': 'value',
                'label': 'Proportion of Total Economy',
                'path': 'VALUE_PERC_ECO'
              },
              'currentValueAccessor': {
                'type': 'value',
                'label': 'Current Value',
                'path': 'VALUE_CURRENT'
              },
              'forecastedValueAccessor': {
                'type': 'value',
                'label': 'Forecasted Value',
                'path': 'VALUE_FORECAST'
              }
            },
            {
              'id': 'manufacturing',
              'label': 'Manufacturing',
              'color': 'rgba(56, 101, 255, 0.3)',
              'type': 'forecast',
              'dimension': {
                'SECTOR': 'Manufacturing'
              },
              'valueAccessor': {
                'type': 'value',
                'path': 'VALUE_PERC_ECO'
              },
              'changeAccessor': {
                'type': 'value',
                'label': 'Change',
                'path': 'CHANGE'
              },
              'sectorNameAccessor': {
                'type': 'value',
                'path': 'SECTOR'
              },
              'proportionAccessor': {
                'type': 'value',
                'label': 'Proportion of Total Economy',
                'path': 'VALUE_PERC_ECO'
              },
              'currentValueAccessor': {
                'type': 'value',
                'label': 'Current Value',
                'path': 'VALUE_CURRENT'
              },
              'forecastedValueAccessor': {
                'type': 'value',
                'label': 'Forecasted Value',
                'path': 'VALUE_FORECAST'
              }
            },
            {
              'id': 'publi-administration-and-defence-compulsory-social-security',
              'label': 'Public admin & Defence; Compulsory Social Sec.',
              'color': 'rgba(56, 101, 255, 0.3)',
              'type': 'forecast',
              'dimension': {
                'SECTOR': 'Public admin & Defence; Compulsory Social Sec.'
              },
              'valueAccessor': {
                'type': 'value',
                'path': 'VALUE_PERC_ECO'
              },
              'changeAccessor': {
                'type': 'value',
                'label': 'Change',
                'path': 'CHANGE'
              },
              'sectorNameAccessor': {
                'type': 'value',
                'path': 'SECTOR'
              },
              'proportionAccessor': {
                'type': 'value',
                'label': 'Proportion of Total Economy',
                'path': 'VALUE_PERC_ECO'
              },
              'currentValueAccessor': {
                'type': 'value',
                'label': 'Current Value',
                'path': 'VALUE_CURRENT'
              },
              'forecastedValueAccessor': {
                'type': 'value',
                'label': 'Forecasted Value',
                'path': 'VALUE_FORECAST'
              }
            },
            {
              'id': 'wholesale-and-retail-trade-repair-of-motor-vehicles-and-motorcycles',
              'label': 'Wholesale & Retail Trade; Repair of vehicles & Motorcycles',
              'color': 'rgba(56, 101, 255, 0.3)',
              'type': 'forecast',
              'dimension': {
                'SECTOR': 'Wholesale & Retail Trade; Repair of vehicles & Motorcycles'
              },
              'valueAccessor': {
                'type': 'value',
                'path': 'VALUE_PERC_ECO'
              },
              'changeAccessor': {
                'type': 'value',
                'label': 'Change',
                'path': 'CHANGE'
              },
              'sectorNameAccessor': {
                'type': 'value',
                'path': 'SECTOR'
              },
              'proportionAccessor': {
                'type': 'value',
                'label': 'Proportion of Total Economy',
                'path': 'VALUE_PERC_ECO'
              },
              'currentValueAccessor': {
                'type': 'value',
                'label': 'Current Value',
                'path': 'VALUE_CURRENT'
              },
              'forecastedValueAccessor': {
                'type': 'value',
                'label': 'Forecasted Value',
                'path': 'VALUE_FORECAST'
              }
            },
            {
              'id': 'information-and-communication',
              'label': 'Information and communication',
              'color': 'rgba(56, 101, 255, 0.3)',
              'type': 'forecast',
              'dimension': {
                'SECTOR': 'INFORMATION AND COMMUNICATION'
              },
              'valueAccessor': {
                'type': 'value',
                'path': 'VALUE_PERC_ECO'
              },
              'changeAccessor': {
                'type': 'value',
                'label': 'Change',
                'path': 'CHANGE'
              },
              'sectorNameAccessor': {
                'type': 'value',
                'path': 'SECTOR'
              },
              'proportionAccessor': {
                'type': 'value',
                'label': 'Proportion of Total Economy',
                'path': 'VALUE_PERC_ECO'
              },
              'currentValueAccessor': {
                'type': 'value',
                'label': 'Current Value',
                'path': 'VALUE_CURRENT'
              },
              'forecastedValueAccessor': {
                'type': 'value',
                'label': 'Forecasted Value',
                'path': 'VALUE_FORECAST'
              }
            },
            {
              'id': 'electricity-gas-and-water-supply-waste-management',
              'label': 'Electricity, Gas, & Water supply; Waste Mgmt',
              'color': 'rgba(56, 101, 255, 0.3)',
              'type': 'forecast',
              'dimension': {
                'SECTOR': 'Electricity, Gas, & Water supply; Waste Mgmt'
              },
              'valueAccessor': {
                'type': 'value',
                'path': 'VALUE_PERC_ECO'
              },
              'changeAccessor': {
                'type': 'value',
                'label': 'Change',
                'path': 'CHANGE'
              },
              'sectorNameAccessor': {
                'type': 'value',
                'path': 'SECTOR'
              },
              'proportionAccessor': {
                'type': 'value',
                'label': 'Proportion of Total Economy',
                'path': 'VALUE_PERC_ECO'
              },
              'currentValueAccessor': {
                'type': 'value',
                'label': 'Current Value',
                'path': 'VALUE_CURRENT'
              },
              'forecastedValueAccessor': {
                'type': 'value',
                'label': 'Forecasted Value',
                'path': 'VALUE_FORECAST'
              }
            },
            {
              'id': 'professional-scientific-and-technical-administrative-and-support-services',
              'label': 'Professional, scientific and technical& Administrative and support services',
              'color': 'rgba(56, 101, 255, 0.3)',
              'type': 'forecast',
              'dimension': {
                'SECTOR': 'Professional, scientific and technical& Administrative and support services'
              },
              'valueAccessor': {
                'type': 'value',
                'path': 'VALUE_PERC_ECO'
              },
              'changeAccessor': {
                'type': 'value',
                'label': 'Change',
                'path': 'CHANGE'
              },
              'sectorNameAccessor': {
                'type': 'value',
                'path': 'SECTOR'
              },
              'proportionAccessor': {
                'type': 'value',
                'label': 'Proportion of Total Economy',
                'path': 'VALUE_PERC_ECO'
              },
              'currentValueAccessor': {
                'type': 'value',
                'label': 'Current Value',
                'path': 'VALUE_CURRENT'
              },
              'forecastedValueAccessor': {
                'type': 'value',
                'label': 'Forecasted Value',
                'path': 'VALUE_FORECAST'
              }
            },
            {
              'id': 'real-estate',
              'label': 'Real estate',
              'color': 'rgba(56, 101, 255, 0.3)',
              'type': 'forecast',
              'dimension': {
                'SECTOR': 'Real estate'
              },
              'valueAccessor': {
                'type': 'value',
                'path': 'VALUE_PERC_ECO'
              },
              'changeAccessor': {
                'type': 'value',
                'label': 'Change',
                'path': 'CHANGE'
              },
              'sectorNameAccessor': {
                'type': 'value',
                'path': 'SECTOR'
              },
              'proportionAccessor': {
                'type': 'value',
                'label': 'Proportion of Total Economy',
                'path': 'VALUE_PERC_ECO'
              },
              'currentValueAccessor': {
                'type': 'value',
                'label': 'Current Value',
                'path': 'VALUE_CURRENT'
              },
              'forecastedValueAccessor': {
                'type': 'value',
                'label': 'Forecasted Value',
                'path': 'VALUE_FORECAST'
              }
            }
          ]
        },
        {
          'id': 'tree-chart-economy-sector-indicator-2',
          'type': 'tree-map-with-change-chart',
          'sortOrder': 2,
          'viewName': 'VW_RI_GDP_SECTOR_CRNT_IND_PCT',
          'comboIdTable': 'DS_GDP_SECTORS_COMBO_CRNT',
          'xAxisLabel': 'Other non-oil sectors (Government, Utilities, and Household Production) are not included in this forecast.',
          'hasDefault': true,
          'colorCodes': {
            'positive': {
              '1_5': '#19AA2B',
              '5_10': '#0e931f',
              '10_20': '#007506',
              '20_60': '#005700',
              '60+': '#003b00'
            },
            'negetive': {
              '1_5': '#e54d35',
              '5_10': '#c83220',
              '10_20': '#ac0e0a',
              '20_60': '#870101',
              '60+': '#610000'
            },
            'neutral': '#666878'
          },
          'selectRangeBy': 'CHANGE',
          'colorsRange': [
            {
              'range': '1_5',
              'valueLL': 1,
              'valueUL': 5
            },
            {
              'range': '5_10',
              'valueLL': 5,
              'valueUL': 10
            },
            {
              'range': '10_20',
              'valueLL': 10,
              'valueUL': 20
            },
            {
              'range': '20_60',
              'valueLL': 20,
              'valueUL': 60
            },
            {
              'range': '60+',
              'valueLL': 60,
              'valueUL': 'Infinity'
            },
            {
              'range': 'neutral',
              'valueLL': 0,
              'valueUL': 1
            }
          ],
          'seriesMeta': [
            {
              'id': 'construction',
              'label': 'Construction',
              'color': 'rgba(56, 101, 255, 0.3)',
              'type': 'forecast',
              'dimension': {
                'SECTOR': 'CONSTRUCTION'
              },
              'valueAccessor': {
                'type': 'value',
                'path': 'VALUE_PERC_ECO'
              },
              'changeAccessor': {
                'type': 'value',
                'label': 'Change',
                'path': 'CHANGE'
              },
              'sectorNameAccessor': {
                'type': 'value',
                'path': 'SECTOR'
              },
              'proportionAccessor': {
                'type': 'value',
                'label': 'Proportion of Total Economy',
                'path': 'VALUE_PERC_ECO'
              },
              'currentValueAccessor': {
                'type': 'value',
                'label': 'Current Value',
                'path': 'VALUE_CURRENT'
              },
              'forecastedValueAccessor': {
                'type': 'value',
                'label': 'Forecasted Value',
                'path': 'VALUE_FORECAST'
              }
            },
            {
              'id': 'financial-and-insurance',
              'label': 'Financial and insurance',
              'color': 'rgba(56, 101, 255, 0.3)',
              'type': 'forecast',
              'dimension': {
                'SECTOR': 'Financial and insurance'
              },
              'valueAccessor': {
                'type': 'value',
                'path': 'VALUE_PERC_ECO'
              },
              'changeAccessor': {
                'type': 'value',
                'label': 'Change',
                'path': 'CHANGE'
              },
              'sectorNameAccessor': {
                'type': 'value',
                'path': 'SECTOR'
              },
              'proportionAccessor': {
                'type': 'value',
                'label': 'Proportion of Total Economy',
                'path': 'VALUE_PERC_ECO'
              },
              'currentValueAccessor': {
                'type': 'value',
                'label': 'Current Value',
                'path': 'VALUE_CURRENT'
              },
              'forecastedValueAccessor': {
                'type': 'value',
                'label': 'Forecasted Value',
                'path': 'VALUE_FORECAST'
              }
            },
            {
              'id': 'manufacturing',
              'label': 'Manufacturing',
              'color': 'rgba(56, 101, 255, 0.3)',
              'type': 'forecast',
              'dimension': {
                'SECTOR': 'Manufacturing'
              },
              'valueAccessor': {
                'type': 'value',
                'path': 'VALUE_PERC_ECO'
              },
              'changeAccessor': {
                'type': 'value',
                'label': 'Change',
                'path': 'CHANGE'
              },
              'sectorNameAccessor': {
                'type': 'value',
                'path': 'SECTOR'
              },
              'proportionAccessor': {
                'type': 'value',
                'label': 'Proportion of Total Economy',
                'path': 'VALUE_PERC_ECO'
              },
              'currentValueAccessor': {
                'type': 'value',
                'label': 'Current Value',
                'path': 'VALUE_CURRENT'
              },
              'forecastedValueAccessor': {
                'type': 'value',
                'label': 'Forecasted Value',
                'path': 'VALUE_FORECAST'
              }
            },
            {
              'id': 'publi-administration-and-defence-compulsory-social-security',
              'label': 'Public admin & Defence; Compulsory Social Sec.',
              'color': 'rgba(56, 101, 255, 0.3)',
              'type': 'forecast',
              'dimension': {
                'SECTOR': 'Public admin & Defence; Compulsory Social Sec.'
              },
              'valueAccessor': {
                'type': 'value',
                'path': 'VALUE_PERC_ECO'
              },
              'changeAccessor': {
                'type': 'value',
                'label': 'Change',
                'path': 'CHANGE'
              },
              'sectorNameAccessor': {
                'type': 'value',
                'path': 'SECTOR'
              },
              'proportionAccessor': {
                'type': 'value',
                'label': 'Proportion of Total Economy',
                'path': 'VALUE_PERC_ECO'
              },
              'currentValueAccessor': {
                'type': 'value',
                'label': 'Current Value',
                'path': 'VALUE_CURRENT'
              },
              'forecastedValueAccessor': {
                'type': 'value',
                'label': 'Forecasted Value',
                'path': 'VALUE_FORECAST'
              }
            },
            {
              'id': 'wholesale-and-retail-trade-repair-of-motor-vehicles-and-motorcycles',
              'label': 'Wholesale & Retail Trade; Repair of vehicles & Motorcycles',
              'color': 'rgba(56, 101, 255, 0.3)',
              'type': 'forecast',
              'dimension': {
                'SECTOR': 'Wholesale & Retail Trade; Repair of vehicles & Motorcycles'
              },
              'valueAccessor': {
                'type': 'value',
                'path': 'VALUE_PERC_ECO'
              },
              'changeAccessor': {
                'type': 'value',
                'label': 'Change',
                'path': 'CHANGE'
              },
              'sectorNameAccessor': {
                'type': 'value',
                'path': 'SECTOR'
              },
              'proportionAccessor': {
                'type': 'value',
                'label': 'Proportion of Total Economy',
                'path': 'VALUE_PERC_ECO'
              },
              'currentValueAccessor': {
                'type': 'value',
                'label': 'Current Value',
                'path': 'VALUE_CURRENT'
              },
              'forecastedValueAccessor': {
                'type': 'value',
                'label': 'Forecasted Value',
                'path': 'VALUE_FORECAST'
              }
            },
            {
              'id': 'information-and-communication',
              'label': 'Information and communication',
              'color': 'rgba(56, 101, 255, 0.3)',
              'type': 'forecast',
              'dimension': {
                'SECTOR': 'INFORMATION AND COMMUNICATION'
              },
              'valueAccessor': {
                'type': 'value',
                'path': 'VALUE_PERC_ECO'
              },
              'changeAccessor': {
                'type': 'value',
                'label': 'Change',
                'path': 'CHANGE'
              },
              'sectorNameAccessor': {
                'type': 'value',
                'path': 'SECTOR'
              },
              'proportionAccessor': {
                'type': 'value',
                'label': 'Proportion of Total Economy',
                'path': 'VALUE_PERC_ECO'
              },
              'currentValueAccessor': {
                'type': 'value',
                'label': 'Current Value',
                'path': 'VALUE_CURRENT'
              },
              'forecastedValueAccessor': {
                'type': 'value',
                'label': 'Forecasted Value',
                'path': 'VALUE_FORECAST'
              }
            },
            {
              'id': 'electricity-gas-and-water-supply-waste-management',
              'label': 'Electricity, Gas, & Water supply; Waste Mgmt',
              'color': 'rgba(56, 101, 255, 0.3)',
              'type': 'forecast',
              'dimension': {
                'SECTOR': 'Electricity, Gas, & Water supply; Waste Mgmt'
              },
              'valueAccessor': {
                'type': 'value',
                'path': 'VALUE_PERC_ECO'
              },
              'changeAccessor': {
                'type': 'value',
                'label': 'Change',
                'path': 'CHANGE'
              },
              'sectorNameAccessor': {
                'type': 'value',
                'path': 'SECTOR'
              },
              'proportionAccessor': {
                'type': 'value',
                'label': 'Proportion of Total Economy',
                'path': 'VALUE_PERC_ECO'
              },
              'currentValueAccessor': {
                'type': 'value',
                'label': 'Current Value',
                'path': 'VALUE_CURRENT'
              },
              'forecastedValueAccessor': {
                'type': 'value',
                'label': 'Forecasted Value',
                'path': 'VALUE_FORECAST'
              }
            },
            {
              'id': 'professional-scientific-and-technical-administrative-and-support-services',
              'label': 'Professional, scientific and technical& Administrative and support services',
              'color': 'rgba(56, 101, 255, 0.3)',
              'type': 'forecast',
              'dimension': {
                'SECTOR': 'Professional, scientific and technical& Administrative and support services'
              },
              'valueAccessor': {
                'type': 'value',
                'path': 'VALUE_PERC_ECO'
              },
              'changeAccessor': {
                'type': 'value',
                'label': 'Change',
                'path': 'CHANGE'
              },
              'sectorNameAccessor': {
                'type': 'value',
                'path': 'SECTOR'
              },
              'proportionAccessor': {
                'type': 'value',
                'label': 'Proportion of Total Economy',
                'path': 'VALUE_PERC_ECO'
              },
              'currentValueAccessor': {
                'type': 'value',
                'label': 'Current Value',
                'path': 'VALUE_CURRENT'
              },
              'forecastedValueAccessor': {
                'type': 'value',
                'label': 'Forecasted Value',
                'path': 'VALUE_FORECAST'
              }
            },
            {
              'id': 'real-estate',
              'label': 'Real estate',
              'color': 'rgba(56, 101, 255, 0.3)',
              'type': 'forecast',
              'dimension': {
                'SECTOR': 'Real estate'
              },
              'valueAccessor': {
                'type': 'value',
                'path': 'VALUE_PERC_ECO'
              },
              'changeAccessor': {
                'type': 'value',
                'label': 'Change',
                'path': 'CHANGE'
              },
              'sectorNameAccessor': {
                'type': 'value',
                'path': 'SECTOR'
              },
              'proportionAccessor': {
                'type': 'value',
                'label': 'Proportion of Total Economy',
                'path': 'VALUE_PERC_ECO'
              },
              'currentValueAccessor': {
                'type': 'value',
                'label': 'Current Value',
                'path': 'VALUE_CURRENT'
              },
              'forecastedValueAccessor': {
                'type': 'value',
                'label': 'Forecasted Value',
                'path': 'VALUE_FORECAST'
              }
            }
          ]
        }
      ],
      'visualizationDefault': 'tree-chart-economy-sector-indicator'
    },
    'country_flag': '',
    'page_menu_icon': '',
    'isFavorite': 'false',
    'showSecondLevel': false
  },
  {
    'id': '474',
    'type': 'Internal',
    'component_title': 'GDP SECTORS CURRENT',
    'component_subtitle': 'GDP SECTORS CURRENT SUBTITLE',
    'page_icon': '',
    'page_light_icon': '',
    'domains': [
      'Economy'
    ],
    'theme': '',
    'subtheme': '',
    'product': '',
    'application_url': '',
    'imgSrc': 'https://ifp-cms-dev.scad.gov.ae/sites/default/files/uploads/images/dashboard_colored.svg',
    'attachment': '',
    'policy_guide': '',
    'note': '',
    'search_tags': [],
    'narrative': '',
    'Indicator': ' GDP SECTORS CURRENT CONF ',
    'indicator_list': '',
    'indicatorValues_subtitle': '',
    'indicatorValues_title': '',
    'visualization_subtitle': '',
    'visualization_title': '',
    'enableDynamicPanel': false,
    'listofDyanmicPanelContent': [],
    'highlightsMeta': '',
    'infogramUrl': '',
    'confidenceIntervalMeta': '',
    'enableConfidenceInterval': false,
    'endpoint_label': '',
    'endpoint_title': '',
    'endpoint_url': '',
    'default_layer': 'False',
    'show_on_legend': 'False',
    'defaultDistrictId': '',
    'endpointType': '',
    'nodeId': '',
    'summaryCardId': '',
    'endpoint_icon_id': '',
    'cardDate': '',
    'dashboardUrl': '',
    'enablePointToggle': false,
    'maxPointLimit': '',
    'minLimitYAxis': '',
    'publication_date': '2023-05-10',
    'tagName': '',
    'tagColorCode': '',
    'showInsights': 'False',
    'height': '',
    'host_url': '',
    'embedded_code_version': '',
    'site_root': '',
    'external_name': '',
    'tabs': 'No',
    'toolbar': 'No',
    'showAppBanner': 'False',
    'compare_data': '',
    'language': 'EN',
    'sortVisualizations': true,
    'indicatorTools': [
      {
        'id': 'export-png',
        'disabled': true,
        'label': 'Export PNG'
      },
      {
        'id': 'export-csv',
        'label': 'Export CSV'
      }
    ],
    'indicatorActions': [
      {
        'id': 'switch-view',
        'type': 'simple',
        'label': 'Switch view'
      }
    ],
    'driverTitle': 'Time Selector',
    'driverSubtitle': 'Modify below to view changes in the projection',
    'indicatorDrivers': [
      {
        'title': 'Forecast',
        'id': 'parameter_1_range',
        'type': 'radio',
        'subtitle': 'Range',
        'note': '1 quarter forecast',
        'options': [
          {
            'label': '1 Quarter',
            'value': '1q',
            'isSelected': true
          },
          {
            'label': '2 Quarter',
            'value': '2q',
            'isSelected': false
          }
        ]
      }
    ],
    'indicatorValues': {
      'valuesMeta': [
        {
          'id': 'latest-date',
          'title': 'Forecast Year',
          'type': 'static-with-title-template',
          'valueFormat': 'date_MMM y',
          'viewName': 'VW_RI_GDP_SECTOR_CRNT_IND_PCT',
          'comboIdTable': 'DS_GDP_SECTORS_COMBO_CRNT',
          'hasDefault': true,
          'dimension': {
            'SECTOR': 'OIL'
          },
          'dateFormat': 'YYYY-MM-DD'
        }
      ]
    },
    'indicatorVisualizations': {
      'visualizationsMeta': [
        {
          'id': 'tree-chart-economy-sector-indicator',
          'type': 'tree-map-with-change-chart',
          'sortOrder': 1,
          'viewName': 'VW_RI_GDP_SECTOR_CRNT_IND_PCT',
          'comboIdTable': 'DS_GDP_SECTORS_COMBO_CRNT',
          'xAxisLabel': 'Other non-oil sectors (Government, Utilities, and Household Production) are not included in this forecast.',
          'hasDefault': true,
          'colorCodes': {
            'positive': {
              '1_5': '#19AA2B',
              '5_10': '#0e931f',
              '10_20': '#007506',
              '20_60': '#005700',
              '60+': '#003b00'
            },
            'negetive': {
              '1_5': '#e54d35',
              '5_10': '#c83220',
              '10_20': '#ac0e0a',
              '20_60': '#870101',
              '60+': '#610000'
            },
            'neutral': '#666878'
          },
          'selectRangeBy': 'CHANGE',
          'colorsRange': [
            {
              'range': '1_5',
              'valueLL': 1,
              'valueUL': 5
            },
            {
              'range': '5_10',
              'valueLL': 5,
              'valueUL': 10
            },
            {
              'range': '10_20',
              'valueLL': 10,
              'valueUL': 20
            },
            {
              'range': '20_60',
              'valueLL': 20,
              'valueUL': 60
            },
            {
              'range': '60+',
              'valueLL': 60,
              'valueUL': 'Infinity'
            },
            {
              'range': 'neutral',
              'valueLL': 0,
              'valueUL': 1
            }
          ],
          'tooltips': [
            {
              'type': 'text',
              'path': 'SECTOR'
            },
            {
              'type': 'value',
              'label': 'Current Value',
              'path': 'VALUE_CURRENT'
            },
            {
              'type': 'value',
              'label': 'Forecasted Value',
              'path': 'VALUE_FORECAST'
            },
            {
              'type': 'percentage',
              'label': 'Change',
              'path': 'CHANGE'
            },
            {
              'type': 'value',
              'label': 'Proportion of Total Economy',
              'path': 'VALUE_PERC_ECO'
            }
          ],
          'seriesMeta': [
            {
              'id': 'oil-gdp',
              'label': 'Oil',
              'color': 'rgba(56, 101, 255, 0.2)',
              'type': 'forecast',
              'dimension': {
                'SECTOR': 'Oil'
              },
              'valueAccessor': {
                'type': 'value',
                'path': 'VALUE_PERC_ECO'
              },
              'changeAccessor': {
                'type': 'value',
                'label': 'Change',
                'path': 'CHANGE'
              },
              'sectorNameAccessor': {
                'type': 'value',
                'path': 'SECTOR'
              },
              'proportionAccessor': {
                'type': 'value',
                'label': 'Proportion of Total Economy',
                'path': 'VALUE_PERC_ECO'
              },
              'currentValueAccessor': {
                'type': 'value',
                'label': 'Current Value',
                'path': 'VALUE_CURRENT'
              },
              'forecastedValueAccessor': {
                'type': 'value',
                'label': 'Forecasted Value',
                'path': 'VALUE_FORECAST'
              }
            },
            {
              'id': 'construction',
              'label': 'Construction',
              'color': 'rgba(56, 101, 255, 0.3)',
              'type': 'forecast',
              'dimension': {
                'SECTOR': 'CONSTRUCTION'
              },
              'valueAccessor': {
                'type': 'value',
                'path': 'VALUE_PERC_ECO'
              },
              'changeAccessor': {
                'type': 'value',
                'label': 'Change',
                'path': 'CHANGE'
              },
              'sectorNameAccessor': {
                'type': 'value',
                'path': 'SECTOR'
              },
              'proportionAccessor': {
                'type': 'value',
                'label': 'Proportion of Total Economy',
                'path': 'VALUE_PERC_ECO'
              },
              'currentValueAccessor': {
                'type': 'value',
                'label': 'Current Value',
                'path': 'VALUE_CURRENT'
              },
              'forecastedValueAccessor': {
                'type': 'value',
                'label': 'Forecasted Value',
                'path': 'VALUE_FORECAST'
              }
            },
            {
              'id': 'financial-and-insurance',
              'label': 'Financial and insurance',
              'color': 'rgba(56, 101, 255, 0.3)',
              'type': 'forecast',
              'dimension': {
                'SECTOR': 'Financial and insurance'
              },
              'valueAccessor': {
                'type': 'value',
                'path': 'VALUE_PERC_ECO'
              },
              'changeAccessor': {
                'type': 'value',
                'label': 'Change',
                'path': 'CHANGE'
              },
              'sectorNameAccessor': {
                'type': 'value',
                'path': 'SECTOR'
              },
              'proportionAccessor': {
                'type': 'value',
                'label': 'Proportion of Total Economy',
                'path': 'VALUE_PERC_ECO'
              },
              'currentValueAccessor': {
                'type': 'value',
                'label': 'Current Value',
                'path': 'VALUE_CURRENT'
              },
              'forecastedValueAccessor': {
                'type': 'value',
                'label': 'Forecasted Value',
                'path': 'VALUE_FORECAST'
              }
            },
            {
              'id': 'manufacturing',
              'label': 'Manufacturing',
              'color': 'rgba(56, 101, 255, 0.3)',
              'type': 'forecast',
              'dimension': {
                'SECTOR': 'Manufacturing'
              },
              'valueAccessor': {
                'type': 'value',
                'path': 'VALUE_PERC_ECO'
              },
              'changeAccessor': {
                'type': 'value',
                'label': 'Change',
                'path': 'CHANGE'
              },
              'sectorNameAccessor': {
                'type': 'value',
                'path': 'SECTOR'
              },
              'proportionAccessor': {
                'type': 'value',
                'label': 'Proportion of Total Economy',
                'path': 'VALUE_PERC_ECO'
              },
              'currentValueAccessor': {
                'type': 'value',
                'label': 'Current Value',
                'path': 'VALUE_CURRENT'
              },
              'forecastedValueAccessor': {
                'type': 'value',
                'label': 'Forecasted Value',
                'path': 'VALUE_FORECAST'
              }
            },
            {
              'id': 'publi-administration-and-defence-compulsory-social-security',
              'label': 'Public admin & Defence; Compulsory Social Sec.',
              'color': 'rgba(56, 101, 255, 0.3)',
              'type': 'forecast',
              'dimension': {
                'SECTOR': 'Public admin & Defence; Compulsory Social Sec.'
              },
              'valueAccessor': {
                'type': 'value',
                'path': 'VALUE_PERC_ECO'
              },
              'changeAccessor': {
                'type': 'value',
                'label': 'Change',
                'path': 'CHANGE'
              },
              'sectorNameAccessor': {
                'type': 'value',
                'path': 'SECTOR'
              },
              'proportionAccessor': {
                'type': 'value',
                'label': 'Proportion of Total Economy',
                'path': 'VALUE_PERC_ECO'
              },
              'currentValueAccessor': {
                'type': 'value',
                'label': 'Current Value',
                'path': 'VALUE_CURRENT'
              },
              'forecastedValueAccessor': {
                'type': 'value',
                'label': 'Forecasted Value',
                'path': 'VALUE_FORECAST'
              }
            },
            {
              'id': 'wholesale-and-retail-trade-repair-of-motor-vehicles-and-motorcycles',
              'label': 'Wholesale & Retail Trade; Repair of vehicles & Motorcycles',
              'color': 'rgba(56, 101, 255, 0.3)',
              'type': 'forecast',
              'dimension': {
                'SECTOR': 'Wholesale & Retail Trade; Repair of vehicles & Motorcycles'
              },
              'valueAccessor': {
                'type': 'value',
                'path': 'VALUE_PERC_ECO'
              },
              'changeAccessor': {
                'type': 'value',
                'label': 'Change',
                'path': 'CHANGE'
              },
              'sectorNameAccessor': {
                'type': 'value',
                'path': 'SECTOR'
              },
              'proportionAccessor': {
                'type': 'value',
                'label': 'Proportion of Total Economy',
                'path': 'VALUE_PERC_ECO'
              },
              'currentValueAccessor': {
                'type': 'value',
                'label': 'Current Value',
                'path': 'VALUE_CURRENT'
              },
              'forecastedValueAccessor': {
                'type': 'value',
                'label': 'Forecasted Value',
                'path': 'VALUE_FORECAST'
              }
            },
            {
              'id': 'information-and-communication',
              'label': 'Information and communication',
              'color': 'rgba(56, 101, 255, 0.3)',
              'type': 'forecast',
              'dimension': {
                'SECTOR': 'INFORMATION AND COMMUNICATION'
              },
              'valueAccessor': {
                'type': 'value',
                'path': 'VALUE_PERC_ECO'
              },
              'changeAccessor': {
                'type': 'value',
                'label': 'Change',
                'path': 'CHANGE'
              },
              'sectorNameAccessor': {
                'type': 'value',
                'path': 'SECTOR'
              },
              'proportionAccessor': {
                'type': 'value',
                'label': 'Proportion of Total Economy',
                'path': 'VALUE_PERC_ECO'
              },
              'currentValueAccessor': {
                'type': 'value',
                'label': 'Current Value',
                'path': 'VALUE_CURRENT'
              },
              'forecastedValueAccessor': {
                'type': 'value',
                'label': 'Forecasted Value',
                'path': 'VALUE_FORECAST'
              }
            },
            {
              'id': 'electricity-gas-and-water-supply-waste-management',
              'label': 'Electricity, Gas, & Water supply; Waste Mgmt',
              'color': 'rgba(56, 101, 255, 0.3)',
              'type': 'forecast',
              'dimension': {
                'SECTOR': 'Electricity, Gas, & Water supply; Waste Mgmt'
              },
              'valueAccessor': {
                'type': 'value',
                'path': 'VALUE_PERC_ECO'
              },
              'changeAccessor': {
                'type': 'value',
                'label': 'Change',
                'path': 'CHANGE'
              },
              'sectorNameAccessor': {
                'type': 'value',
                'path': 'SECTOR'
              },
              'proportionAccessor': {
                'type': 'value',
                'label': 'Proportion of Total Economy',
                'path': 'VALUE_PERC_ECO'
              },
              'currentValueAccessor': {
                'type': 'value',
                'label': 'Current Value',
                'path': 'VALUE_CURRENT'
              },
              'forecastedValueAccessor': {
                'type': 'value',
                'label': 'Forecasted Value',
                'path': 'VALUE_FORECAST'
              }
            },
            {
              'id': 'professional-scientific-and-technical-administrative-and-support-services',
              'label': 'Professional, scientific and technical& Administrative and support services',
              'color': 'rgba(56, 101, 255, 0.3)',
              'type': 'forecast',
              'dimension': {
                'SECTOR': 'Professional, scientific and technical& Administrative and support services'
              },
              'valueAccessor': {
                'type': 'value',
                'path': 'VALUE_PERC_ECO'
              },
              'changeAccessor': {
                'type': 'value',
                'label': 'Change',
                'path': 'CHANGE'
              },
              'sectorNameAccessor': {
                'type': 'value',
                'path': 'SECTOR'
              },
              'proportionAccessor': {
                'type': 'value',
                'label': 'Proportion of Total Economy',
                'path': 'VALUE_PERC_ECO'
              },
              'currentValueAccessor': {
                'type': 'value',
                'label': 'Current Value',
                'path': 'VALUE_CURRENT'
              },
              'forecastedValueAccessor': {
                'type': 'value',
                'label': 'Forecasted Value',
                'path': 'VALUE_FORECAST'
              }
            },
            {
              'id': 'real-estate',
              'label': 'Real estate',
              'color': 'rgba(56, 101, 255, 0.3)',
              'type': 'forecast',
              'dimension': {
                'SECTOR': 'Real estate'
              },
              'valueAccessor': {
                'type': 'value',
                'path': 'VALUE_PERC_ECO'
              },
              'changeAccessor': {
                'type': 'value',
                'label': 'Change',
                'path': 'CHANGE'
              },
              'sectorNameAccessor': {
                'type': 'value',
                'path': 'SECTOR'
              },
              'proportionAccessor': {
                'type': 'value',
                'label': 'Proportion of Total Economy',
                'path': 'VALUE_PERC_ECO'
              },
              'currentValueAccessor': {
                'type': 'value',
                'label': 'Current Value',
                'path': 'VALUE_CURRENT'
              },
              'forecastedValueAccessor': {
                'type': 'value',
                'label': 'Forecasted Value',
                'path': 'VALUE_FORECAST'
              }
            }
          ]
        },
        {
          'id': 'tree-chart-economy-sector-indicator-2',
          'type': 'tree-map-with-change-chart',
          'sortOrder': 2,
          'viewName': 'VW_RI_GDP_SECTOR_CRNT_IND_PCT',
          'comboIdTable': 'DS_GDP_SECTORS_COMBO_CRNT',
          'xAxisLabel': 'Other non-oil sectors (Government, Utilities, and Household Production) are not included in this forecast.',
          'hasDefault': true,
          'colorCodes': {
            'positive': {
              '1_5': '#19AA2B',
              '5_10': '#0e931f',
              '10_20': '#007506',
              '20_60': '#005700',
              '60+': '#003b00'
            },
            'negetive': {
              '1_5': '#e54d35',
              '5_10': '#c83220',
              '10_20': '#ac0e0a',
              '20_60': '#870101',
              '60+': '#610000'
            },
            'neutral': '#666878'
          },
          'selectRangeBy': 'CHANGE',
          'colorsRange': [
            {
              'range': '1_5',
              'valueLL': 1,
              'valueUL': 5
            },
            {
              'range': '5_10',
              'valueLL': 5,
              'valueUL': 10
            },
            {
              'range': '10_20',
              'valueLL': 10,
              'valueUL': 20
            },
            {
              'range': '20_60',
              'valueLL': 20,
              'valueUL': 60
            },
            {
              'range': '60+',
              'valueLL': 60,
              'valueUL': 'Infinity'
            },
            {
              'range': 'neutral',
              'valueLL': 0,
              'valueUL': 1
            }
          ],
          'seriesMeta': [
            {
              'id': 'construction',
              'label': 'Construction',
              'color': 'rgba(56, 101, 255, 0.3)',
              'type': 'forecast',
              'dimension': {
                'SECTOR': 'CONSTRUCTION'
              },
              'valueAccessor': {
                'type': 'value',
                'path': 'VALUE_PERC_ECO'
              },
              'changeAccessor': {
                'type': 'value',
                'label': 'Change',
                'path': 'CHANGE'
              },
              'sectorNameAccessor': {
                'type': 'value',
                'path': 'SECTOR'
              },
              'proportionAccessor': {
                'type': 'value',
                'label': 'Proportion of Total Economy',
                'path': 'VALUE_PERC_ECO'
              },
              'currentValueAccessor': {
                'type': 'value',
                'label': 'Current Value',
                'path': 'VALUE_CURRENT'
              },
              'forecastedValueAccessor': {
                'type': 'value',
                'label': 'Forecasted Value',
                'path': 'VALUE_FORECAST'
              }
            },
            {
              'id': 'financial-and-insurance',
              'label': 'Financial and insurance',
              'color': 'rgba(56, 101, 255, 0.3)',
              'type': 'forecast',
              'dimension': {
                'SECTOR': 'Financial and insurance'
              },
              'valueAccessor': {
                'type': 'value',
                'path': 'VALUE_PERC_ECO'
              },
              'changeAccessor': {
                'type': 'value',
                'label': 'Change',
                'path': 'CHANGE'
              },
              'sectorNameAccessor': {
                'type': 'value',
                'path': 'SECTOR'
              },
              'proportionAccessor': {
                'type': 'value',
                'label': 'Proportion of Total Economy',
                'path': 'VALUE_PERC_ECO'
              },
              'currentValueAccessor': {
                'type': 'value',
                'label': 'Current Value',
                'path': 'VALUE_CURRENT'
              },
              'forecastedValueAccessor': {
                'type': 'value',
                'label': 'Forecasted Value',
                'path': 'VALUE_FORECAST'
              }
            },
            {
              'id': 'manufacturing',
              'label': 'Manufacturing',
              'color': 'rgba(56, 101, 255, 0.3)',
              'type': 'forecast',
              'dimension': {
                'SECTOR': 'Manufacturing'
              },
              'valueAccessor': {
                'type': 'value',
                'path': 'VALUE_PERC_ECO'
              },
              'changeAccessor': {
                'type': 'value',
                'label': 'Change',
                'path': 'CHANGE'
              },
              'sectorNameAccessor': {
                'type': 'value',
                'path': 'SECTOR'
              },
              'proportionAccessor': {
                'type': 'value',
                'label': 'Proportion of Total Economy',
                'path': 'VALUE_PERC_ECO'
              },
              'currentValueAccessor': {
                'type': 'value',
                'label': 'Current Value',
                'path': 'VALUE_CURRENT'
              },
              'forecastedValueAccessor': {
                'type': 'value',
                'label': 'Forecasted Value',
                'path': 'VALUE_FORECAST'
              }
            },
            {
              'id': 'publi-administration-and-defence-compulsory-social-security',
              'label': 'Public admin & Defence; Compulsory Social Sec.',
              'color': 'rgba(56, 101, 255, 0.3)',
              'type': 'forecast',
              'dimension': {
                'SECTOR': 'Public admin & Defence; Compulsory Social Sec.'
              },
              'valueAccessor': {
                'type': 'value',
                'path': 'VALUE_PERC_ECO'
              },
              'changeAccessor': {
                'type': 'value',
                'label': 'Change',
                'path': 'CHANGE'
              },
              'sectorNameAccessor': {
                'type': 'value',
                'path': 'SECTOR'
              },
              'proportionAccessor': {
                'type': 'value',
                'label': 'Proportion of Total Economy',
                'path': 'VALUE_PERC_ECO'
              },
              'currentValueAccessor': {
                'type': 'value',
                'label': 'Current Value',
                'path': 'VALUE_CURRENT'
              },
              'forecastedValueAccessor': {
                'type': 'value',
                'label': 'Forecasted Value',
                'path': 'VALUE_FORECAST'
              }
            },
            {
              'id': 'wholesale-and-retail-trade-repair-of-motor-vehicles-and-motorcycles',
              'label': 'Wholesale & Retail Trade; Repair of vehicles & Motorcycles',
              'color': 'rgba(56, 101, 255, 0.3)',
              'type': 'forecast',
              'dimension': {
                'SECTOR': 'Wholesale & Retail Trade; Repair of vehicles & Motorcycles'
              },
              'valueAccessor': {
                'type': 'value',
                'path': 'VALUE_PERC_ECO'
              },
              'changeAccessor': {
                'type': 'value',
                'label': 'Change',
                'path': 'CHANGE'
              },
              'sectorNameAccessor': {
                'type': 'value',
                'path': 'SECTOR'
              },
              'proportionAccessor': {
                'type': 'value',
                'label': 'Proportion of Total Economy',
                'path': 'VALUE_PERC_ECO'
              },
              'currentValueAccessor': {
                'type': 'value',
                'label': 'Current Value',
                'path': 'VALUE_CURRENT'
              },
              'forecastedValueAccessor': {
                'type': 'value',
                'label': 'Forecasted Value',
                'path': 'VALUE_FORECAST'
              }
            },
            {
              'id': 'information-and-communication',
              'label': 'Information and communication',
              'color': 'rgba(56, 101, 255, 0.3)',
              'type': 'forecast',
              'dimension': {
                'SECTOR': 'INFORMATION AND COMMUNICATION'
              },
              'valueAccessor': {
                'type': 'value',
                'path': 'VALUE_PERC_ECO'
              },
              'changeAccessor': {
                'type': 'value',
                'label': 'Change',
                'path': 'CHANGE'
              },
              'sectorNameAccessor': {
                'type': 'value',
                'path': 'SECTOR'
              },
              'proportionAccessor': {
                'type': 'value',
                'label': 'Proportion of Total Economy',
                'path': 'VALUE_PERC_ECO'
              },
              'currentValueAccessor': {
                'type': 'value',
                'label': 'Current Value',
                'path': 'VALUE_CURRENT'
              },
              'forecastedValueAccessor': {
                'type': 'value',
                'label': 'Forecasted Value',
                'path': 'VALUE_FORECAST'
              }
            },
            {
              'id': 'electricity-gas-and-water-supply-waste-management',
              'label': 'Electricity, Gas, & Water supply; Waste Mgmt',
              'color': 'rgba(56, 101, 255, 0.3)',
              'type': 'forecast',
              'dimension': {
                'SECTOR': 'Electricity, Gas, & Water supply; Waste Mgmt'
              },
              'valueAccessor': {
                'type': 'value',
                'path': 'VALUE_PERC_ECO'
              },
              'changeAccessor': {
                'type': 'value',
                'label': 'Change',
                'path': 'CHANGE'
              },
              'sectorNameAccessor': {
                'type': 'value',
                'path': 'SECTOR'
              },
              'proportionAccessor': {
                'type': 'value',
                'label': 'Proportion of Total Economy',
                'path': 'VALUE_PERC_ECO'
              },
              'currentValueAccessor': {
                'type': 'value',
                'label': 'Current Value',
                'path': 'VALUE_CURRENT'
              },
              'forecastedValueAccessor': {
                'type': 'value',
                'label': 'Forecasted Value',
                'path': 'VALUE_FORECAST'
              }
            },
            {
              'id': 'professional-scientific-and-technical-administrative-and-support-services',
              'label': 'Professional, scientific and technical& Administrative and support services',
              'color': 'rgba(56, 101, 255, 0.3)',
              'type': 'forecast',
              'dimension': {
                'SECTOR': 'Professional, scientific and technical& Administrative and support services'
              },
              'valueAccessor': {
                'type': 'value',
                'path': 'VALUE_PERC_ECO'
              },
              'changeAccessor': {
                'type': 'value',
                'label': 'Change',
                'path': 'CHANGE'
              },
              'sectorNameAccessor': {
                'type': 'value',
                'path': 'SECTOR'
              },
              'proportionAccessor': {
                'type': 'value',
                'label': 'Proportion of Total Economy',
                'path': 'VALUE_PERC_ECO'
              },
              'currentValueAccessor': {
                'type': 'value',
                'label': 'Current Value',
                'path': 'VALUE_CURRENT'
              },
              'forecastedValueAccessor': {
                'type': 'value',
                'label': 'Forecasted Value',
                'path': 'VALUE_FORECAST'
              }
            },
            {
              'id': 'real-estate',
              'label': 'Real estate',
              'color': 'rgba(56, 101, 255, 0.3)',
              'type': 'forecast',
              'dimension': {
                'SECTOR': 'Real estate'
              },
              'valueAccessor': {
                'type': 'value',
                'path': 'VALUE_PERC_ECO'
              },
              'changeAccessor': {
                'type': 'value',
                'label': 'Change',
                'path': 'CHANGE'
              },
              'sectorNameAccessor': {
                'type': 'value',
                'path': 'SECTOR'
              },
              'proportionAccessor': {
                'type': 'value',
                'label': 'Proportion of Total Economy',
                'path': 'VALUE_PERC_ECO'
              },
              'currentValueAccessor': {
                'type': 'value',
                'label': 'Current Value',
                'path': 'VALUE_CURRENT'
              },
              'forecastedValueAccessor': {
                'type': 'value',
                'label': 'Forecasted Value',
                'path': 'VALUE_FORECAST'
              }
            }
          ]
        }
      ],
      'visualizationDefault': 'tree-chart-economy-sector-indicator'
    },
    'country_flag': '',
    'page_menu_icon': '',
    'isFavorite': 'false',
    'field_page_light_icon': '',
    'page_menu_light_icon': '',
    'field_page_menu_light_icon': '',
    'showSecondLevel': false
  },
  {
    'id': '462',
    'type': 'Internal',
    'component_title': 'GDP SECTORS CONSTANT',
    'component_subtitle': 'GDP SECTORS CONSTANT SUBTITLE',
    'page_icon': '',
    'page_light_icon': '',
    'domains': [
      'Economy'
    ],
    'theme': '',
    'subtheme': '',
    'product': '',
    'application_url': '',
    'imgSrc': 'https://ifp-cms-dev.scad.gov.ae/sites/default/files/uploads/images/dashboard_colored.svg',
    'attachment': 'https://ifp-cms-dev.scad.gov.ae/sites/default/files/uploads/Quarterly Review of the Central Bank of the UAE Q1 2020 E.pdf',
    'policy_guide': '',
    'note': 'note',
    'search_tags': [],
    'narrative': 'narrative content',
    'Indicator': ' GDP SECTORS CONF ',
    'indicator_list': '',
    'indicatorValues_subtitle': '',
    'indicatorValues_title': '',
    'visualization_subtitle': '',
    'visualization_title': '',
    'enableDynamicPanel': false,
    'listofDyanmicPanelContent': [],
    'highlightsMeta': '',
    'infogramUrl': '',
    'confidenceIntervalMeta': '',
    'enableConfidenceInterval': false,
    'endpoint_label': '',
    'endpoint_title': '',
    'endpoint_url': '',
    'default_layer': 'False',
    'show_on_legend': 'False',
    'defaultDistrictId': '',
    'endpointType': '',
    'nodeId': '',
    'summaryCardId': '',
    'endpoint_icon_id': '',
    'cardDate': '',
    'dashboardUrl': '',
    'enablePointToggle': true,
    'maxPointLimit': '',
    'minLimitYAxis': '',
    'publication_date': '2023-05-10',
    'tagName': '',
    'tagColorCode': '',
    'showInsights': 'False',
    'height': '',
    'host_url': '',
    'embedded_code_version': '',
    'site_root': '',
    'external_name': '',
    'tabs': 'No',
    'toolbar': 'No',
    'showAppBanner': 'False',
    'compare_data': '',
    'language': 'EN',
    'sortVisualizations': true,
    'indicatorTools': [
      {
        'id': 'export-png',
        'disabled': true,
        'label': 'Export PNG'
      },
      {
        'id': 'export-csv',
        'label': 'Export CSV'
      }
    ],
    'indicatorActions': [],
    'driverTitle': 'Time Selector',
    'driverSubtitle': 'Modify below to view changes in the projection',
    'indicatorDrivers': [
      {
        'title': 'Forecast',
        'id': 'parameter_1_range',
        'type': 'radio',
        'subtitle': 'Range',
        'note': '1 quarter forecast',
        'options': [
          {
            'label': '1 Quarter',
            'value': '1q',
            'isSelected': true
          },
          {
            'label': '2 Quarter',
            'value': '2q',
            'isSelected': false
          }
        ]
      }
    ],
    'indicatorValues': {
      'valuesMeta': [
        {
          'id': 'latest-date',
          'title': 'Forecast Year',
          'type': 'static-with-title-template',
          'valueFormat': 'date_MMM y',
          'viewName': 'VW_RI_GDP_SECTOR_CNST_IND_PCT',
          'comboIdTable': 'DS_GDP_SECTORS_COMBO_CNST',
          'hasDefault': true,
          'dimension': {
            'SECTOR': 'OIL'
          },
          'dateFormat': 'YYYY-MM-DD'
        }
      ]
    },
    'indicatorVisualizations': {
      'visualizationsMeta': [
        {
          'id': 'tree-chart-economy-sector-indicator',
          'type': 'tree-map-with-change-chart',
          'sortOrder': 1,
          'viewName': 'VW_RI_GDP_SECTOR_CNST_IND_PCT',
          'comboIdTable': 'DS_GDP_SECTORS_COMBO_CNST',
          'xAxisLabel': 'Other non-oil sectors (Government, Utilities, and Household Production) are not included in this forecast.',
          'hasDefault': true,
          'tooltips': [
            {
              'type': 'text',
              'path': 'SECTOR'
            },
            {
              'type': 'value',
              'label': 'Current Value',
              'path': 'VALUE_CURRENT'
            },
            {
              'type': 'value',
              'label': 'Forecasted Value',
              'path': 'VALUE_FORECAST'
            },
            {
              'type': 'percentage',
              'label': 'Change',
              'path': 'CHANGE'
            },
            {
              'type': 'value',
              'label': 'Proportion of Total Economy',
              'path': 'VALUE_PERC_ECO'
            }
          ],
          'colorCodes': {
            'positive': {
              '1_5': '#19AA2B',
              '5_10': '#0e931f',
              '10_20': '#007506',
              '20_60': '#005700',
              '60+': '#003b00'
            },
            'negetive': {
              '1_5': '#e54d35',
              '5_10': '#c83220',
              '10_20': '#ac0e0a',
              '20_60': '#870101',
              '60+': '#610000'
            },
            'neutral': '#666878'
          },
          'selectRangeBy': 'CHANGE',
          'colorsRange': [
            {
              'range': '1_5',
              'valueLL': 1,
              'valueUL': 5
            },
            {
              'range': '5_10',
              'valueLL': 5,
              'valueUL': 10
            },
            {
              'range': '10_20',
              'valueLL': 10,
              'valueUL': 20
            },
            {
              'range': '20_60',
              'valueLL': 20,
              'valueUL': 60
            },
            {
              'range': '60+',
              'valueLL': 60,
              'valueUL': 'Infinity'
            },
            {
              'range': 'neutral',
              'valueLL': 0,
              'valueUL': 1
            }
          ],
          'seriesMeta': [
            {
              'id': 'oil-gdp',
              'label': 'Oil',
              'color': 'rgba(56, 101, 255, 0.2)',
              'tableDefinition': [
                {
                  'label': 'Time Period',
                  'path': 'OBS_DT'
                },
                {
                  'label': 'Sector',
                  'path': 'SECTOR'
                },
                {
                  'label': 'Value Forecast',
                  'path': 'VALUE_FORECAST'
                },
                {
                  'label': 'Change',
                  'path': 'CHANGE'
                }
              ],
              'type': 'forecast',
              'dimension': {
                'SECTOR': 'Oil'
              },
              'valueAccessor': {
                'type': 'value',
                'path': 'VALUE_PERC_ECO'
              },
              'changeAccessor': {
                'type': 'value',
                'label': 'Change',
                'path': 'CHANGE'
              },
              'sectorNameAccessor': {
                'type': 'value',
                'path': 'SECTOR'
              },
              'proportionAccessor': {
                'type': 'value',
                'label': 'Proportion of Total Economy',
                'path': 'VALUE_PERC_ECO'
              },
              'currentValueAccessor': {
                'type': 'value',
                'label': 'Current Value',
                'path': 'VALUE_CURRENT'
              },
              'forecastedValueAccessor': {
                'type': 'value',
                'label': 'Forecasted Value',
                'path': 'VALUE_FORECAST'
              }
            },
            {
              'id': 'construction',
              'label': 'Construction',
              'color': 'rgba(56, 101, 255, 0.3)',
              'type': 'forecast',
              'dimension': {
                'SECTOR': 'CONSTRUCTION'
              },
              'valueAccessor': {
                'type': 'value',
                'path': 'VALUE_PERC_ECO'
              },
              'changeAccessor': {
                'type': 'value',
                'label': 'Change',
                'path': 'CHANGE'
              },
              'sectorNameAccessor': {
                'type': 'value',
                'path': 'SECTOR'
              },
              'proportionAccessor': {
                'type': 'value',
                'label': 'Proportion of Total Economy',
                'path': 'VALUE_PERC_ECO'
              },
              'currentValueAccessor': {
                'type': 'value',
                'label': 'Current Value',
                'path': 'VALUE_CURRENT'
              },
              'forecastedValueAccessor': {
                'type': 'value',
                'label': 'Forecasted Value',
                'path': 'VALUE_FORECAST'
              }
            },
            {
              'id': 'financial-and-insurance',
              'label': 'Financial and insurance',
              'color': 'rgba(56, 101, 255, 0.3)',
              'type': 'forecast',
              'dimension': {
                'SECTOR': 'Financial and insurance'
              },
              'valueAccessor': {
                'type': 'value',
                'path': 'VALUE_PERC_ECO'
              },
              'changeAccessor': {
                'type': 'value',
                'label': 'Change',
                'path': 'CHANGE'
              },
              'sectorNameAccessor': {
                'type': 'value',
                'path': 'SECTOR'
              },
              'proportionAccessor': {
                'type': 'value',
                'label': 'Proportion of Total Economy',
                'path': 'VALUE_PERC_ECO'
              },
              'currentValueAccessor': {
                'type': 'value',
                'label': 'Current Value',
                'path': 'VALUE_CURRENT'
              },
              'forecastedValueAccessor': {
                'type': 'value',
                'label': 'Forecasted Value',
                'path': 'VALUE_FORECAST'
              }
            },
            {
              'id': 'manufacturing',
              'label': 'Manufacturing',
              'color': 'rgba(56, 101, 255, 0.3)',
              'type': 'forecast',
              'dimension': {
                'SECTOR': 'Manufacturing'
              },
              'valueAccessor': {
                'type': 'value',
                'path': 'VALUE_PERC_ECO'
              },
              'changeAccessor': {
                'type': 'value',
                'label': 'Change',
                'path': 'CHANGE'
              },
              'sectorNameAccessor': {
                'type': 'value',
                'path': 'SECTOR'
              },
              'proportionAccessor': {
                'type': 'value',
                'label': 'Proportion of Total Economy',
                'path': 'VALUE_PERC_ECO'
              },
              'currentValueAccessor': {
                'type': 'value',
                'label': 'Current Value',
                'path': 'VALUE_CURRENT'
              },
              'forecastedValueAccessor': {
                'type': 'value',
                'label': 'Forecasted Value',
                'path': 'VALUE_FORECAST'
              }
            },
            {
              'id': 'publi-administration-and-defence-compulsory-social-security',
              'label': 'Public admin & Defence; Compulsory Social Sec.',
              'color': 'rgba(56, 101, 255, 0.3)',
              'type': 'forecast',
              'dimension': {
                'SECTOR': 'Public admin & Defence; Compulsory Social Sec.'
              },
              'valueAccessor': {
                'type': 'value',
                'path': 'VALUE_PERC_ECO'
              },
              'changeAccessor': {
                'type': 'value',
                'label': 'Change',
                'path': 'CHANGE'
              },
              'sectorNameAccessor': {
                'type': 'value',
                'path': 'SECTOR'
              },
              'proportionAccessor': {
                'type': 'value',
                'label': 'Proportion of Total Economy',
                'path': 'VALUE_PERC_ECO'
              },
              'currentValueAccessor': {
                'type': 'value',
                'label': 'Current Value',
                'path': 'VALUE_CURRENT'
              },
              'forecastedValueAccessor': {
                'type': 'value',
                'label': 'Forecasted Value',
                'path': 'VALUE_FORECAST'
              }
            },
            {
              'id': 'wholesale-and-retail-trade-repair-of-motor-vehicles-and-motorcycles',
              'label': 'Wholesale & Retail Trade; Repair of vehicles & Motorcycles',
              'color': 'rgba(56, 101, 255, 0.3)',
              'type': 'forecast',
              'dimension': {
                'SECTOR': 'Wholesale & Retail Trade; Repair of vehicles & Motorcycles'
              },
              'valueAccessor': {
                'type': 'value',
                'path': 'VALUE_PERC_ECO'
              },
              'changeAccessor': {
                'type': 'value',
                'label': 'Change',
                'path': 'CHANGE'
              },
              'sectorNameAccessor': {
                'type': 'value',
                'path': 'SECTOR'
              },
              'proportionAccessor': {
                'type': 'value',
                'label': 'Proportion of Total Economy',
                'path': 'VALUE_PERC_ECO'
              },
              'currentValueAccessor': {
                'type': 'value',
                'label': 'Current Value',
                'path': 'VALUE_CURRENT'
              },
              'forecastedValueAccessor': {
                'type': 'value',
                'label': 'Forecasted Value',
                'path': 'VALUE_FORECAST'
              }
            },
            {
              'id': 'information-and-communication',
              'label': 'Information and communication',
              'color': 'rgba(56, 101, 255, 0.3)',
              'type': 'forecast',
              'dimension': {
                'SECTOR': 'INFORMATION AND COMMUNICATION'
              },
              'valueAccessor': {
                'type': 'value',
                'path': 'VALUE_PERC_ECO'
              },
              'changeAccessor': {
                'type': 'value',
                'label': 'Change',
                'path': 'CHANGE'
              },
              'sectorNameAccessor': {
                'type': 'value',
                'path': 'SECTOR'
              },
              'proportionAccessor': {
                'type': 'value',
                'label': 'Proportion of Total Economy',
                'path': 'VALUE_PERC_ECO'
              },
              'currentValueAccessor': {
                'type': 'value',
                'label': 'Current Value',
                'path': 'VALUE_CURRENT'
              },
              'forecastedValueAccessor': {
                'type': 'value',
                'label': 'Forecasted Value',
                'path': 'VALUE_FORECAST'
              }
            },
            {
              'id': 'electricity-gas-and-water-supply-waste-management',
              'label': 'Electricity, Gas, & Water supply; Waste Mgmt',
              'color': 'rgba(56, 101, 255, 0.3)',
              'type': 'forecast',
              'dimension': {
                'SECTOR': 'Electricity, Gas, & Water supply; Waste Mgmt'
              },
              'valueAccessor': {
                'type': 'value',
                'path': 'VALUE_PERC_ECO'
              },
              'changeAccessor': {
                'type': 'value',
                'label': 'Change',
                'path': 'CHANGE'
              },
              'sectorNameAccessor': {
                'type': 'value',
                'path': 'SECTOR'
              },
              'proportionAccessor': {
                'type': 'value',
                'label': 'Proportion of Total Economy',
                'path': 'VALUE_PERC_ECO'
              },
              'currentValueAccessor': {
                'type': 'value',
                'label': 'Current Value',
                'path': 'VALUE_CURRENT'
              },
              'forecastedValueAccessor': {
                'type': 'value',
                'label': 'Forecasted Value',
                'path': 'VALUE_FORECAST'
              }
            },
            {
              'id': 'professional-scientific-and-technical-administrative-and-support-services',
              'label': 'Professional, scientific and technical& Administrative and support services',
              'color': 'rgba(56, 101, 255, 0.3)',
              'type': 'forecast',
              'dimension': {
                'SECTOR': 'Professional, scientific and technical& Administrative and support services'
              },
              'valueAccessor': {
                'type': 'value',
                'path': 'VALUE_PERC_ECO'
              },
              'changeAccessor': {
                'type': 'value',
                'label': 'Change',
                'path': 'CHANGE'
              },
              'sectorNameAccessor': {
                'type': 'value',
                'path': 'SECTOR'
              },
              'proportionAccessor': {
                'type': 'value',
                'label': 'Proportion of Total Economy',
                'path': 'VALUE_PERC_ECO'
              },
              'currentValueAccessor': {
                'type': 'value',
                'label': 'Current Value',
                'path': 'VALUE_CURRENT'
              },
              'forecastedValueAccessor': {
                'type': 'value',
                'label': 'Forecasted Value',
                'path': 'VALUE_FORECAST'
              }
            },
            {
              'id': 'real-estate',
              'label': 'Real estate',
              'color': 'rgba(56, 101, 255, 0.3)',
              'type': 'forecast',
              'dimension': {
                'SECTOR': 'Real estate'
              },
              'valueAccessor': {
                'type': 'value',
                'path': 'VALUE_PERC_ECO'
              },
              'changeAccessor': {
                'type': 'value',
                'label': 'Change',
                'path': 'CHANGE'
              },
              'sectorNameAccessor': {
                'type': 'value',
                'path': 'SECTOR'
              },
              'proportionAccessor': {
                'type': 'value',
                'label': 'Proportion of Total Economy',
                'path': 'VALUE_PERC_ECO'
              },
              'currentValueAccessor': {
                'type': 'value',
                'label': 'Current Value',
                'path': 'VALUE_CURRENT'
              },
              'forecastedValueAccessor': {
                'type': 'value',
                'label': 'Forecasted Value',
                'path': 'VALUE_FORECAST'
              }
            }
          ]
        },
        {
          'id': 'tree-chart-economy-sector-indicator-2',
          'type': 'tree-map-with-change-chart',
          'sortOrder': 2,
          'viewName': 'VW_RI_GDP_SECTOR_CNST_IND_PCT',
          'comboIdTable': 'DS_GDP_SECTORS_COMBO_CNST',
          'xAxisLabel': 'Other non-oil sectors (Government, Utilities, and Household Production) are not included in this forecast.',
          'hasDefault': true,
          'colorCodes': {
            'positive': {
              '1_5': '#19AA2B',
              '5_10': '#0e931f',
              '10_20': '#007506',
              '20_60': '#005700',
              '60+': '#003b00'
            },
            'negetive': {
              '1_5': '#e54d35',
              '5_10': '#c83220',
              '10_20': '#ac0e0a',
              '20_60': '#870101',
              '60+': '#610000'
            },
            'neutral': '#666878'
          },
          'selectRangeBy': 'CHANGE',
          'colorsRange': [
            {
              'range': '1_5',
              'valueLL': 1,
              'valueUL': 5
            },
            {
              'range': '5_10',
              'valueLL': 5,
              'valueUL': 10
            },
            {
              'range': '10_20',
              'valueLL': 10,
              'valueUL': 20
            },
            {
              'range': '20_60',
              'valueLL': 20,
              'valueUL': 60
            },
            {
              'range': '60+',
              'valueLL': 60,
              'valueUL': 'Infinity'
            },
            {
              'range': 'neutral',
              'valueLL': 0,
              'valueUL': 1
            }
          ],
          'seriesMeta': [
            {
              'id': 'construction',
              'label': 'Construction',
              'color': 'rgba(56, 101, 255, 0.3)',
              'type': 'forecast',
              'dimension': {
                'SECTOR': 'CONSTRUCTION'
              },
              'valueAccessor': {
                'type': 'value',
                'path': 'VALUE_PERC_ECO'
              },
              'changeAccessor': {
                'type': 'value',
                'label': 'Change',
                'path': 'CHANGE'
              },
              'sectorNameAccessor': {
                'type': 'value',
                'path': 'SECTOR'
              },
              'proportionAccessor': {
                'type': 'value',
                'label': 'Proportion of Total Economy',
                'path': 'VALUE_PERC_ECO'
              },
              'currentValueAccessor': {
                'type': 'value',
                'label': 'Current Value',
                'path': 'VALUE_CURRENT'
              },
              'forecastedValueAccessor': {
                'type': 'value',
                'label': 'Forecasted Value',
                'path': 'VALUE_FORECAST'
              }
            },
            {
              'id': 'financial-and-insurance',
              'label': 'Financial and insurance',
              'color': 'rgba(56, 101, 255, 0.3)',
              'type': 'forecast',
              'dimension': {
                'SECTOR': 'Financial and insurance'
              },
              'valueAccessor': {
                'type': 'value',
                'path': 'VALUE_PERC_ECO'
              },
              'changeAccessor': {
                'type': 'value',
                'label': 'Change',
                'path': 'CHANGE'
              },
              'sectorNameAccessor': {
                'type': 'value',
                'path': 'SECTOR'
              },
              'proportionAccessor': {
                'type': 'value',
                'label': 'Proportion of Total Economy',
                'path': 'VALUE_PERC_ECO'
              },
              'currentValueAccessor': {
                'type': 'value',
                'label': 'Current Value',
                'path': 'VALUE_CURRENT'
              },
              'forecastedValueAccessor': {
                'type': 'value',
                'label': 'Forecasted Value',
                'path': 'VALUE_FORECAST'
              }
            },
            {
              'id': 'manufacturing',
              'label': 'Manufacturing',
              'color': 'rgba(56, 101, 255, 0.3)',
              'type': 'forecast',
              'dimension': {
                'SECTOR': 'Manufacturing'
              },
              'valueAccessor': {
                'type': 'value',
                'path': 'VALUE_PERC_ECO'
              },
              'changeAccessor': {
                'type': 'value',
                'label': 'Change',
                'path': 'CHANGE'
              },
              'sectorNameAccessor': {
                'type': 'value',
                'path': 'SECTOR'
              },
              'proportionAccessor': {
                'type': 'value',
                'label': 'Proportion of Total Economy',
                'path': 'VALUE_PERC_ECO'
              },
              'currentValueAccessor': {
                'type': 'value',
                'label': 'Current Value',
                'path': 'VALUE_CURRENT'
              },
              'forecastedValueAccessor': {
                'type': 'value',
                'label': 'Forecasted Value',
                'path': 'VALUE_FORECAST'
              }
            },
            {
              'id': 'publi-administration-and-defence-compulsory-social-security',
              'label': 'Public admin & Defence; Compulsory Social Sec.',
              'color': 'rgba(56, 101, 255, 0.3)',
              'type': 'forecast',
              'dimension': {
                'SECTOR': 'Public admin & Defence; Compulsory Social Sec.'
              },
              'valueAccessor': {
                'type': 'value',
                'path': 'VALUE_PERC_ECO'
              },
              'changeAccessor': {
                'type': 'value',
                'label': 'Change',
                'path': 'CHANGE'
              },
              'sectorNameAccessor': {
                'type': 'value',
                'path': 'SECTOR'
              },
              'proportionAccessor': {
                'type': 'value',
                'label': 'Proportion of Total Economy',
                'path': 'VALUE_PERC_ECO'
              },
              'currentValueAccessor': {
                'type': 'value',
                'label': 'Current Value',
                'path': 'VALUE_CURRENT'
              },
              'forecastedValueAccessor': {
                'type': 'value',
                'label': 'Forecasted Value',
                'path': 'VALUE_FORECAST'
              }
            },
            {
              'id': 'wholesale-and-retail-trade-repair-of-motor-vehicles-and-motorcycles',
              'label': 'Wholesale & Retail Trade; Repair of vehicles & Motorcycles',
              'color': 'rgba(56, 101, 255, 0.3)',
              'type': 'forecast',
              'dimension': {
                'SECTOR': 'Wholesale & Retail Trade; Repair of vehicles & Motorcycles'
              },
              'valueAccessor': {
                'type': 'value',
                'path': 'VALUE_PERC_ECO'
              },
              'changeAccessor': {
                'type': 'value',
                'label': 'Change',
                'path': 'CHANGE'
              },
              'sectorNameAccessor': {
                'type': 'value',
                'path': 'SECTOR'
              },
              'proportionAccessor': {
                'type': 'value',
                'label': 'Proportion of Total Economy',
                'path': 'VALUE_PERC_ECO'
              },
              'currentValueAccessor': {
                'type': 'value',
                'label': 'Current Value',
                'path': 'VALUE_CURRENT'
              },
              'forecastedValueAccessor': {
                'type': 'value',
                'label': 'Forecasted Value',
                'path': 'VALUE_FORECAST'
              }
            },
            {
              'id': 'information-and-communication',
              'label': 'Information and communication',
              'color': 'rgba(56, 101, 255, 0.3)',
              'type': 'forecast',
              'dimension': {
                'SECTOR': 'INFORMATION AND COMMUNICATION'
              },
              'valueAccessor': {
                'type': 'value',
                'path': 'VALUE_PERC_ECO'
              },
              'changeAccessor': {
                'type': 'value',
                'label': 'Change',
                'path': 'CHANGE'
              },
              'sectorNameAccessor': {
                'type': 'value',
                'path': 'SECTOR'
              },
              'proportionAccessor': {
                'type': 'value',
                'label': 'Proportion of Total Economy',
                'path': 'VALUE_PERC_ECO'
              },
              'currentValueAccessor': {
                'type': 'value',
                'label': 'Current Value',
                'path': 'VALUE_CURRENT'
              },
              'forecastedValueAccessor': {
                'type': 'value',
                'label': 'Forecasted Value',
                'path': 'VALUE_FORECAST'
              }
            },
            {
              'id': 'electricity-gas-and-water-supply-waste-management',
              'label': 'Electricity, Gas, & Water supply; Waste Mgmt',
              'color': 'rgba(56, 101, 255, 0.3)',
              'type': 'forecast',
              'dimension': {
                'SECTOR': 'Electricity, Gas, & Water supply; Waste Mgmt'
              },
              'valueAccessor': {
                'type': 'value',
                'path': 'VALUE_PERC_ECO'
              },
              'changeAccessor': {
                'type': 'value',
                'label': 'Change',
                'path': 'CHANGE'
              },
              'sectorNameAccessor': {
                'type': 'value',
                'path': 'SECTOR'
              },
              'proportionAccessor': {
                'type': 'value',
                'label': 'Proportion of Total Economy',
                'path': 'VALUE_PERC_ECO'
              },
              'currentValueAccessor': {
                'type': 'value',
                'label': 'Current Value',
                'path': 'VALUE_CURRENT'
              },
              'forecastedValueAccessor': {
                'type': 'value',
                'label': 'Forecasted Value',
                'path': 'VALUE_FORECAST'
              }
            },
            {
              'id': 'professional-scientific-and-technical-administrative-and-support-services',
              'label': 'Professional, scientific and technical& Administrative and support services',
              'color': 'rgba(56, 101, 255, 0.3)',
              'type': 'forecast',
              'dimension': {
                'SECTOR': 'Professional, scientific and technical& Administrative and support services'
              },
              'valueAccessor': {
                'type': 'value',
                'path': 'VALUE_PERC_ECO'
              },
              'changeAccessor': {
                'type': 'value',
                'label': 'Change',
                'path': 'CHANGE'
              },
              'sectorNameAccessor': {
                'type': 'value',
                'path': 'SECTOR'
              },
              'proportionAccessor': {
                'type': 'value',
                'label': 'Proportion of Total Economy',
                'path': 'VALUE_PERC_ECO'
              },
              'currentValueAccessor': {
                'type': 'value',
                'label': 'Current Value',
                'path': 'VALUE_CURRENT'
              },
              'forecastedValueAccessor': {
                'type': 'value',
                'label': 'Forecasted Value',
                'path': 'VALUE_FORECAST'
              }
            },
            {
              'id': 'real-estate',
              'label': 'Real estate',
              'color': 'rgba(56, 101, 255, 0.3)',
              'type': 'forecast',
              'dimension': {
                'SECTOR': 'Real estate'
              },
              'valueAccessor': {
                'type': 'value',
                'path': 'VALUE_PERC_ECO'
              },
              'changeAccessor': {
                'type': 'value',
                'label': 'Change',
                'path': 'CHANGE'
              },
              'sectorNameAccessor': {
                'type': 'value',
                'path': 'SECTOR'
              },
              'proportionAccessor': {
                'type': 'value',
                'label': 'Proportion of Total Economy',
                'path': 'VALUE_PERC_ECO'
              },
              'currentValueAccessor': {
                'type': 'value',
                'label': 'Current Value',
                'path': 'VALUE_CURRENT'
              },
              'forecastedValueAccessor': {
                'type': 'value',
                'label': 'Forecasted Value',
                'path': 'VALUE_FORECAST'
              }
            }
          ]
        }
      ],
      'visualizationDefault': 'tree-chart-economy-sector-indicator'
    },
    'country_flag': '',
    'page_menu_icon': '',
    'isFavorite': 'false',
    'field_page_light_icon': '',
    'page_menu_light_icon': '',
    'field_page_menu_light_icon': '',
    'showSecondLevel': false
  }
];
export const indicatorMock = [
  {
    'indicatorId': '700',
    'contentType': 'scad_official_indicator',
    'appType': '',
    'type': 'Official Statistics'
  },
  {
    'indicatorId': '710',
    'contentType': 'scad_official_indicator',
    'appType': '',
    'type': 'Official Statistics'
  },
  {
    'indicatorId': '685',
    'contentType': 'scad_official_indicator',
    'appType': '',
    'type': 'Official Statistics'
  },
  {
    'indicatorId': '686',
    'contentType': 'scad_official_indicator',
    'appType': '',
    'type': 'Official Statistics'
  },
  {
    'indicatorId': '783',
    'contentType': 'scad_official_indicator',
    'appType': '',
    'type': 'Official Statistics'
  },
  {
    'indicatorId': '730',
    'contentType': 'scad_official_indicator',
    'appType': '',
    'type': 'Official Statistics'
  },
  {
    'indicatorId': '732',
    'contentType': 'scad_official_indicator',
    'appType': '',
    'type': 'Official Statistics'
  },
  {
    'indicatorId': '735',
    'contentType': 'scad_official_indicator',
    'appType': '',
    'type': 'Official Statistics'
  }
];
