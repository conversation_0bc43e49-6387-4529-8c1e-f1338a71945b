import { Injectable } from '@angular/core';
import { BehaviorSubject } from 'rxjs';
import { HttpService } from '../http/http.service';
import { commonApi } from '../../apiConstants/common-api.constants';
import { cloneDeep } from 'lodash';
import { loadIndicatorSuccess } from 'src/app/scad-insights/store/incidicator-list/incidicator-list.action';
import { DomainsService } from '../domains/domains.service';
import { Store } from '@ngrx/store';
import { chartConstants } from '../../constants/chart.constants';
import { PagesService } from '../pages/pages.service';
import { jsPDF } from 'jspdf';
import html2canvas from 'html2canvas';
import { ShareAppsData } from 'src/app/scad-insights/ifp-widgets/ifp-organism/ifp-share-modal/ifp-share-modal.interface';
import { dashboardConstants } from '../../constants/dashboard.constants';
import { ifpColors } from '../../constants/color.constants';



@Injectable({
  providedIn: 'root'
})
export class DashboardService {

  public chartSettings: any = {
    experimental_statistics: [],
    analytical_apps: [],
    official_statistics: [],
    compare_statistics: [],
    custom_card: []
  };

  public chartSettingsSeries: any = {
    experimental_statistics: [],
    analytical_apps: [],
    official_statistics: [],
    compare_statistics: [],
    custom_card: []
  };

  public settingsChanged = new BehaviorSubject<{ type: string, id: string, tools: string }>({ type: 'line', id: '', tools: '' });

  public titleUpdated = new BehaviorSubject<string>('');
  public deleteSelectedCard = new BehaviorSubject<{ id: string, cntType: string }>({ id: '', cntType: '' });
  public dashboardTools: string[] = ['icon', 'cardTitle', 'textColor', 'textSize', 'cardDescription', 'descriptionColor', 'descriptionFontSize'];
  public customDashboardCardTools: string[] = ['Xaxis', 'Yaxis', 'dataUpdate', 'axisUpdate', 'chartStyle', 'xAxisPos', 'lineColor', 'chartSettings', 'chartType',
    'legend', 'legendEnable'
  ];

  public cardFilter: string = 'FILTER';
  public selectedCards: string = 'SELECTED_CARDS';
  public canvas: any;
  public dashboardProperties: { title?: string, logo?: string, file: any } = { title: '', logo: '', file: '' };


  constructor(private _http: HttpService, private _domainService: DomainsService,
    private store: Store, private _pageService: PagesService) { }


  gettingSpaceValues(cntType: string, id: string) {
    let spacing: number[] = [];
    const settingArray = this.chartSettings[cntType];
    const index = settingArray?.findIndex((x: { id: string; }) => x.id == id);
    if (index >= 0) {
      spacing.push(settingArray[index].spacingTop ? settingArray[index].spacingTop : 15);
      spacing.push(settingArray[index].spacingRight ? settingArray[index].spacingRight : 10);
      spacing.push(settingArray[index].spacingBottom ? settingArray[index].spacingBottom : 10);
      spacing.push(settingArray[index].spacingLeft ? settingArray[index].spacingLeft : 10);
    } else {
      spacing = [15, 10, 10, 10];
    }
    return spacing;
  }

  getXaxisPositions(cntType: string, id: string) {
    const xAxisPositions: { xAxisPos: string } = { xAxisPos: 'right' };
    const settingArray = this.chartSettings[cntType];
    const index = settingArray.findIndex((x: { id: string; }) => x.id == id);
    if (index >= 0) {
      xAxisPositions.xAxisPos = settingArray[index].xAxisPos ? settingArray[index].xAxisPos : 'right';
    }
    return xAxisPositions;
  }

  getLegendPositions(cntType: string, id: string) {
    const legendPositions: { Pos: string, isLegend: boolean } = { Pos: 'center', isLegend: true };
    const settingArray = this.chartSettings[cntType];
    const index = settingArray.findIndex((x: { id: string; }) => x.id == id);
    if (index >= 0) {
      legendPositions.Pos = settingArray[index].legendPos ? settingArray[index].legendPos : 'center';
      legendPositions.isLegend = [false, true].includes(settingArray[index].isLegend) ? settingArray[index].isLegend : true;
    }
    return legendPositions;
  }

  getChartType(cntType: string, id: string) {
    let chartType = 'line';
    const chartDatas = this.chartSettings[cntType];
    if (chartDatas?.find((x: { id: string; }) => x.id == id)) {
      chartType = this.chartSettings[cntType].find((x: { id: string; }) => x.id == id).chartType;
      chartType = chartType ? chartType : 'line';
    }
    return chartType;
  }

  getRecentValue(cntType: string, id: string) {
    const selectNode = (this.chartSettings[cntType] || []).find((x: { id: string; }) => x.id === id);
    return this.prepareRecent(selectNode?.recent ?? true);
  }

  prepareRecent(recent: boolean) {
    return {
      id: recent ? 'Latest-Readings' : 'All',
      isSelected: false,
      label: recent ? 'Recent' : 'ALL',
      unit: recent ? 'Recent' : null,
      value: ''
    };
  }


  setSeriesLength(cntType: string, id: string, chartData: any) {
    let filterData = chartData?.type && chartData?.type == 'pie' ? chartData[0].data : chartData;
    if (filterData?.length && filterData[0]?.identifier) {
      filterData = filterData.filter((item: { identifier: any; }, index: any, self: any[]) => index === self.findIndex((t) => t.identifier === item.identifier)
      );
    }
    const chartDatas = this.chartSettingsSeries[cntType];
    if (chartDatas?.find((x: { id: string; }) => x.id == id)) {
      const index = chartDatas?.findIndex((x: { id: string; }) => x.id == id);
      this.chartSettingsSeries[cntType][index].seriesLength = filterData?.length;
      this.chartSettingsSeries[cntType][index].titles = this.getValues(filterData, 'name');
      this.chartSettingsSeries[cntType][index].colors = this.getValues(filterData, 'color');
    } else {
      const data = {
        id: id,
        seriesLength: filterData.length,
        titles: this.getValues(filterData, 'name'),
        colors: this.getValues(filterData, 'color')
      };
      this.chartSettingsSeries[cntType].push(data);
    }
  }

  getValues(data: any, key: string) {
    const values: any = [];
    if (data?.length > 0) {
      data.forEach((element: any) => {
        values.push(element[key]);
      });
    }
    return values;
  }

  getColors(cntType: string, id: string, index: number) {
    let color = this.getColor(index);
    const selectedObject = this.chartSettings[cntType];
    if (selectedObject?.length) {
      const objectIndex = selectedObject.findIndex((x: { id: string; }) => x.id == id);
      if (objectIndex >= 0 && selectedObject[objectIndex].lineColors?.find((x: { index: number; }) => x.index == index)) {
        color = selectedObject[objectIndex].lineColors?.find((x: { index: number; }) => x.index == index).color;
      }
    }
    return color;
  }

  getTreeMapColors(cntType: string, id: string, index: number, delaultColor: string) {
    let color = delaultColor;
    const selectedObject = this.chartSettings[cntType];
    const objectIndex = selectedObject.findIndex((x: { id: string; }) => x.id == id);
    if (objectIndex >= 0 && selectedObject[objectIndex].lineColors?.find((x: { index: number; }) => x.index == index)) {
      color = selectedObject[objectIndex].lineColors?.find((x: { index: number; }) => x.index == index).color;
    }
    return color;
  }

  setIndicatorTitles(title: string, cntType: string, id: string) {
    const chartDatas = this.chartSettings[cntType];
    if (chartDatas?.find((x: { id: string; }) => x.id == id)) {
      const index = chartDatas?.findIndex((x: { id: string; }) => x.id == id);
      if (!this.chartSettings[cntType][index].isTitleEdit) {
        this.chartSettings[cntType][index].title = title;
        this.chartSettings[cntType][index].isTitleEdit = false;
      }
    } else {
      const data = {
        id: id,
        title: title,
        isTitleEdit: false
      };
      if (this.chartSettings[cntType]) {
        this.chartSettings[cntType].push(data);
      }
    }

  }

  updateIndicatorTitle(title: string, cntType: string, id: string, key: string) {
    const chartDatas = this.chartSettings[cntType];
    if (chartDatas?.find((x: { id: string; }) => x.id == id)) {
      const index = chartDatas?.findIndex((x: { id: string; }) => x.id == id);
      this.chartSettings[cntType][index][key] = title;
      this.chartSettings[cntType][index].isTitleEdit = true;
    }
  }


  getChartSettings(cntType: string, id: string, key: string) {
    let value = true;
    const chartDatas = this.chartSettings[cntType];
    if (chartDatas?.find((x: { id: string; }) => x.id == id)) {
      value = this.chartSettings[cntType].find((x: { id: string; }) => x.id == id)[key];
      value = [true, false].includes(value) ? value : (key != 'preciseValue' ? true : false);
    }
    return value;
  }

  getLiveabilityData(id: string) {
    const payLoad: any = {
      id: id
    };
    this._domainService.getLiveabilityDashboard(payLoad).subscribe(resp => {
      const response = cloneDeep(resp);
      response.id = id;
      response.component_title = resp.title;
      const postData = { data: { ...response, id }, status: { status: true, errorMessage: '', loader: false }, defaultValue: [], periodFilter: [], initialData: [], overview: [], coiData: [] };
      this.store.dispatch(loadIndicatorSuccess({ data: postData }));
    });
  }

  getCompareIndicatorData(id: string, title: string) {
    this._pageService.getCompareAppsView(id).subscribe(resp => {
      const response = cloneDeep(resp);
      response.component_title = title;
      response.content_classification_key = chartConstants.COMPARE_STATISTICS;
      const postData = { data: { ...response, id }, status: { status: true, errorMessage: '', loader: false }, defaultValue: [], periodFilter: [], initialData: [], overview: [], coiData: [] };
      this.store.dispatch(loadIndicatorSuccess({ data: postData }));
    });

  }



  createDashboard(data: any, logo: any, image_light: any, image_dark: any, isUpdate: boolean = false, id: string = '') {
    const formData = new FormData();
    if (logo) {
      formData.append('logo', logo, logo.name);
    }
    formData.append('data', JSON.stringify(data));
    formData.append('thumbnailLight', image_light);
    formData.append('thumbnailDark', image_dark);
    // formData.append('csvfiles[]', filelist);
    const url = isUpdate ? this._http.put(`${commonApi.updateDashboard}/${id}`, formData) : this._http.post(commonApi.createDashboard, formData);
    return url;
  }

  getDashboards(page: number, per_page: number, searchString: string = '', tab: string = 'dashboard', sort: string = 'desc') {
    const endUrl = tab == 'Dashboards' ? commonApi.dashboardList : (tab == 'Sent' ? commonApi.sendDashboardsList : commonApi.recieveDashboardList);
    let endpoint = `${endUrl}?page=${page}&limit=${per_page}`;
    // if (searchString != '') {
    endpoint = `${endpoint}&search=${searchString}&sort=${sort}`;
    // }
    return this._http.get(endpoint);
  }

  getDashboardData(id: string, tab: string) {
    return this._http.get(`${tab == 'Dashboards' ? commonApi.dashboardDetail : commonApi.sendOrRecieveDashboardDetail}/${id}`);
  }

  deleteDashboard(id: string, tab: string) {
    const endpoint = tab == 'Dashboards' ? commonApi.dashboardDetail : (tab == 'Sent' ? commonApi.deleteSendDashboard : commonApi.deleteRecievedDashboard);
    return this._http.delete(`${endpoint}/${id}`);
  }

  setUploadCardTitle(name: string | undefined, selectId: string) {
    const index = this.chartSettings[dashboardConstants.customCard].findIndex((x: { id: string; }) => x.id == selectId);
    if (index >= 0 && name) {
      this.chartSettings[dashboardConstants.customCard][index].title = name.split('.').slice(0, -1).join('.');
    }
  }


  public sendRequest(url: string, params: any, data?: any) {
    const query = params ? this.getParams(params) : '';
    return this._http[data ? 'post' : 'get'](`${url}`, (data ? data : query));
  }

  private getParams(params: any) {
    return Object.entries(params).map(([key, value]) => `${key}=${value}`).join('&');
  }

  downloadPdf(element: any, title: string) {
    html2canvas(element, {
      scale: 2 // Increase the scale for higher resolution
    }).then((canvas) => {
      // Convert canvas to base64 image
      const image = canvas.toDataURL('image/png');
      const pdf = new jsPDF({
        orientation: 'portrait', // or 'landscape'
        unit: 'px',
        format: [(element.offsetWidth - 500), element.offsetHeight]
      });
      const imgWidth = (element.offsetWidth - 500); // Adjust image width as needed
      const imgHeight = element.offsetHeight; // Assuming image aspect ratio of 4:3, adjust as needed
      const xPos = 0;
      const yPos = 0;

      pdf.addImage(image, 'JPEG', xPos, yPos, imgWidth, imgHeight);
      pdf.save(`${title}.pdf`);
    });
  }


  async cutImageIntoParts(element: HTMLElement) {
    this.canvas = await html2canvas(element);
    const partCanvas = document.createElement('canvas');
    const ctx = partCanvas.getContext('2d');
    if (ctx) {
      ctx.drawImage(
        this.canvas,
        element.offsetWidth, element.offsetHeight
      );
    }
    return partCanvas;
  }

  convertPixelToMM(pixel: number) {
    const dpi: number = window.devicePixelRatio * 96;
    return pixel / dpi * 25.4;
  }


  // * dashboard share apis //

  shareApps(data: ShareAppsData) {
    return this._http.post(commonApi.shareDashboard, data);
  }

  getSendDashboards(page: number, per_page: number, searchString: string = '') {
    let endpoint = `${commonApi.sendDashboardsList}?page=${page}&limit=${per_page}`;
    // if (searchString != '') {
    endpoint = `${endpoint}&search=${searchString}`;
    // }
    return this._http.get(endpoint);
  }

  // getSentApp() {
  //   return this._http.get(myApps.sentApps);
  // }

  // viewSharedApp(token: string) {
  //   return this._http.get(`${myApps.viewShared}?token=${token}`);
  // }

  getColor(index: number) {
    let color: any;
    switch (index) {
      case 0:
        color = ifpColors.green;
        break;
      case 1:
        color = ifpColors.blue;
        break;
      case 2:
        color = ifpColors.lightBlue;
        break;
      case 3:
        color = ifpColors.red;
        break;
      case 4:
        color = ifpColors.darkPurple;
        break;
      case 5:
        color = ifpColors.skyBlue;
        break;
      case 6:
        color = ifpColors.orange;
        break;
      case 7:
        color = ifpColors.yellow;
        break;
      case 8:
        color = ifpColors.chartBlue;
        break;
      case 9:
        color = ifpColors.chartGreen;
        break;
      case 10:
        color = ifpColors.chartVilot;
        break;
      case 11:
        color = ifpColors.chartBlack;
        break;
      case 12:
        color = ifpColors.chartOrange;
        break;
      default:
        color = ifpColors.grey;
        break;
    }
    return color;
  }
}

