
@if (!isLoder()) {
  <div class="ifp-prep-save">
    <div class="ifp-prep-save__content">
      <div class="ifp-prep-save__content-header">
        <h2 class="ifp-prep-save__content-title">{{'Export Data' | translate}}</h2>
        <p class="ifp-prep-save__content-desc">{{'Export the prepared data to your preferred tool, download the output, or save it in your Bayaan library' | translate}}.</p>
      </div>
      <div class="ifp-prep-save__content-body">
        @if (this.toolsList.length !== 0) {
        <div class="ifp-prep-save__library-content-wrapper">
          @for (tool of toolsList; track $index) {
            <div class="ifp-prep-save__tool" [ngClass]="{'ifp-prep-save__tool--disable': tool.disable || !_prepService.workflowStarted()}" (click)="goTOTool(tool)">
              <p class="ifp-prep-save__tool-name"><em class="ifp-icon" [class]="tool.icon" [style.backgroundColor]="tool.tagColor"></em>{{tool.title | translate}}</p>
              @if (!tool.disable) {
              <app-ifp-info class="ifp-prep-save__tool-desc" [infoContent]="tool.description"></app-ifp-info>
              }
            </div>
          } </div>
         }
          @if (this.toolsList.length !== 0) {
            <div  class="ifp-prep-save__library-divider-wrapper">
              <div class="ifp-prep-save__library-vertical-divider">
                <div class="ifp-prep-save__library-line">  {{'OR'| translate}}</div>
              </div>
            </div>
          }


        <div class="ifp-prep-save__library-content-wrapper">
          <ifp-button [loader]="loaderProcess()&&download" [label]="'Download Output'" (ifpClick)="openSaveModal(true)" class="ifp-prep-save__footer-btn" [buttonClass]="buttonClass.primaryLight+' '+ buttonClass.large+' '+ buttonClass.capitalize" [iconClass]="'ifp-icon-download'" ></ifp-button>

          <ifp-button [spinnerClass]="'ifp-round-spinner--secondary'" [loader]="loaderProcess()&&!download" [label]="'Save to My Bayaan Library'" (ifpClick)="openSaveModal()" class="ifp-prep-save__footer-btn" [buttonClass]="buttonClass.secondary+' '+ buttonClass.large+' '+ buttonClass.capitalize" [iconClass]="'ifp-icon-library'"></ifp-button>
        </div>
        <!-- <p><em class="ifp-icon ifp-icon-info-round"></em>{{'The dashboard builder tool supports a maximum of 200 rows and 25 columns.' | translate}}</p> -->
      </div>
      <div>
      </div>
    </div>

  </div>
} @else {
  <div class="ifp-center-loader">
    <app-ifp-spinner></app-ifp-spinner>
  </div>

}



<app-ifp-modal [overlayType]="'transparent'" #saveDataModal>
  @if (nameModal) {
    <ifp-prep-save-modal (closeModal)="closeModal($event)" (saveFile)="saveToLibrary($event)" [message]="download ? 'Enter the file name and click Download button to download the file.': 'Save the output file to your Bayaan library.' |translate"
    [title]="download ? 'Download' : 'Save'" [buttonSecondary]="toolForward ?  'Don\'t Save' : 'Cancel' " [buttonTitle]="download ? 'Download' : 'Save'"></ifp-prep-save-modal>
  }
</app-ifp-modal>

<app-ifp-modal [overlayClass]="'ifp-modal__bg-grey'" #modalSucess >

  <ifp-prep-popup [type]="'btn'" (closePopUp)="closeSucessModal()"   (buttonEventValue)="buttonClick($event)" [heading]="'Data Prep Completed Successfully!'" [desc]="''"  [fileName]="_prepService.uploadedFileResponse()?.name ?? ''"  [subTitle]="'Explore More'" [subDesc]="'What would you like to do next?'" ></ifp-prep-popup>

    </app-ifp-modal>

    <app-ifp-modal [overlayType]="'transparent'" #dashboardModel>
      <ifp-sc-upload-model class="ifp-db__upload-model"
       [tabData]="tabData" [isCustomInstruction]="true" [buttonLabel]="'Ok'" (closeUploadModel)="closeDashBordModel($event)"></ifp-sc-upload-model>
</app-ifp-modal>

