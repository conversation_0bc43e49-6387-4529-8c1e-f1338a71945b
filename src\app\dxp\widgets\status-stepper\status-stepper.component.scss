@use "../../../../assets/ifp-styles/abstracts/index" as *;
:host {
  display: block;
}
.ifp-status-stepper {
  width: 300px;
  border-radius:10px;
  padding: $spacer-5 $spacer-4;
  border: 1px solid $ifp-color-grey-13;
  &__process-item {
    position: relative;
    padding-bottom: $spacer-4;
    display: flex;
    &::before{
      content: "";
      position: absolute;
      top: 2px;
      transform: translateX(-50%);
      width: 1px;
      height: 100%;
      background-color: $ifp-color-grey-8;
      left: 15px;
    }
    &:last-child {
      padding-bottom: $spacer-0;
      &::before {
        content: none;
      }
    }

  }
  &__process-icon  {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    color: $ifp-color-white-global;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    z-index: 1;
  }
  &__data {
      margin-inline-start: $spacer-3;
  }
  &__process {
    height: calc(100vh - 400px);
    @include ifp-scroll-y(transparent, $ifp-color-grey-7, 8px, 8px);
  }
  &__process-name {
    font-weight: $fw-semi-bold;
    font-size: $ifp-fs-4;
    margin-bottom: $spacer-2;
  }
  &__process-status {
    border-radius: 30px;
    margin-top: $spacer-3;
    color: $ifp-color-white;
    padding: $spacer-1 $spacer-3;
    font-size: $ifp-fs-2;
    display: inline-block;
  }
  &__process-stage {
    color: $ifp-color-grey-9;
    font-size: $ifp-fs-3;
    .ifp-icon {
      margin-inline-end: $spacer-2;
    }
  }
  &__process-no-data {
    font-weight: $fw-bold;
    text-align: center;
  }
  &__box {
    width: 10px;
    height: 10px;
    border-radius: 4px;
    background-color: $ifp-color-grey-14;
  }
  &--completed {
    .ifp-status-stepper {
      &__process-status ,&__process-icon{
        background-color: $ifp-color-green-dark-3;
      }
    }

    &::before{
      background-color: $ifp-color-green-dark-3;
      width: 3px;
    }  }
  &--pending {
    .ifp-status-stepper {
      &__process-status  {
        background-color: $ifp-color-grey-19;
      }
      &__process-icon{
        background-color: $ifp-color-grey-8;
      }
      &__process-name {
        color: $ifp-color-grey-14;
        font-weight: $fw-regular;
      }
    }
  }
  &--inprogress {
    .ifp-status-stepper {
      &__process-status  ,&__process-icon{
        background-color:  $ifp-color-orange;
      }
    }
  }
  &--failed {
    .ifp-status-stepper {
      &__process-status  ,&__process-icon{
        background-color: $ifp-color-red;
      }
  }
}
}
