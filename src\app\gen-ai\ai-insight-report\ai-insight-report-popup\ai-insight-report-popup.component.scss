@use "../../../../assets/ifp-styles/abstracts/index" as *;
:host {
  display: block;
  background-color: $ifp-color-white;
  border-radius: 30px;
  box-shadow: 0 18px 96px 7px $ifp-color-black-08;
}
.ifp-ai-insight-report-popup{
  &__header {

    border-bottom:1px solid $ifp-color-grey-7;
    display: flex;
    justify-content: space-between;
    padding: $spacer-3 $spacer-4 ;
    align-items: center;
}
  &__head{
    font-weight: $fw-bold;
      font-size:$ifp-fs-8 ;
      color: $ifp-color-black;
  }

  &__text-area {
    background: transparent;
    width: 100%;
    border: 0;
    resize: none;
    min-height: 40px;
    font-size: $fw-medium;
    background-color: $ifp-color-white;
    padding: $spacer-3;
    @include ifp-scroll-y($ifp-color-dropdown-select, $ifp-color-grey-1, 8px, 8px);
    &--bottom {
      margin-bottom: $spacer-3;
    }
  }
  &__fields-name {
    color: $ifp-color-primary-grey;
    font-size: $ifp-fs-4;
    margin-top: $spacer-3;
    margin-bottom: $spacer-2;
    &:first-child{
      margin-top: $spacer-0;
    }
  }
  &__sub-heading {
    font-size: $ifp-fs-4;
    color: $ifp-color-primary-grey;
    font-weight: $fw-bold;
    font-size: $ifp-fs-6;
    display: flex;
    justify-content: space-between;
  }
  &__body {
    @include ifp-scroll-y($ifp-color-dropdown-select, $ifp-color-grey-1, 8px, 8px);
    margin:$spacer-4;
    max-height: calc(100vh - 200px);
    padding-inline-end: 8px;
  }
  &__footer-btn {
    margin-inline-start: $spacer-4;
  }
  &__footer-btns{
    display: flex;
    margin-left: auto;
  }
  &__footer {
    display: flex;
    border-top:1px solid $ifp-color-grey-7;
    padding: $spacer-3 $spacer-4 ;
  }
  &__badge-text{
    font-size: $ifp-fs-4;
    color: $ifp-color-black;
    font-weight: $fw-bold;
    margin: $spacer-0 $spacer-0 $spacer-2;
    display: inline-block;
  }
  &__body-sub {
    background-color: $ifp-color-pale-grey;
    border-radius: 20px;
    margin-top: $spacer-3;
    min-height: 20px;
    padding: $spacer-4;
    margin-bottom: $spacer-4;
  }
  &__sub-card {
    border-radius: 20px;
    margin-bottom: $spacer-4;
    &:last-child {
      margin-bottom: $spacer-0;
    }
  }
  &__sub-table {
    margin-top: $spacer-3;
    display: block;
    margin-bottom: $spacer-4;
  }
  &__text-wrapper {
    position: relative;
  }
  &__text-border {
    border-radius: 5px;
    border:1px solid $ifp-color-grey-7;
  }
  &__card {
    background-color: $ifp-color-section-white;
    box-shadow: 0 35px 50px -23px rgba(112, 123, 156, 0.08);
    min-height: 200px;
    border-radius: 20px;
    margin-top: $spacer-6;
    padding: $spacer-4;
  }
  &__close {
    cursor: pointer;
  }
  &__genarater-badge {
    background-image: radial-gradient(ellipse farthest-side  at top left, rgba(18, 109, 239, 0.15) 0% ,rgba(62, 191, 242, 0.15) 31%, rgba(138, 125, 250, .15) 60%,rgba(1, 84, 233, .15) 100%);
    padding: $spacer-3;
    border-radius: 5px;
  }
}
:host::ng-deep {
  .ifp-ai-insight-report-popup__body-sub {
      .ifp-ai-insight-report-popup__sub-card {
        box-shadow: 0 18px 30px 7px rgb(112, 123, 156, .14);
      }
  }
}
:host-context([dir="rtl"]) {
  direction: ltr;
}
:host-context(.ifp-dark-theme) {
  .ifp-ai-insight-report-popup{
    &__text-area {
    color: $ifp-color-black;
   }

  }
}
