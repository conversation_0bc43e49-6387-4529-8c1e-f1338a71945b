import { Async<PERSON>ipe, SlicePipe } from '@angular/common';
import {
  Component,
  inject,
  input,
  OnDestroy,
  signal,
  viewChild,
  WritableSignal,
} from '@angular/core';
import { ThemeService } from 'src/app/scad-insights/core/services/theme/theme.service';
import { IfpBreadcrumbsComponent } from '../../scad-insights/ifp-widgets/ifp-molecules/ifp-breadcrumbs/ifp-breadcrumbs.component';
import { PageData } from 'src/app/scad-insights/core/interface/molecule/breadcrumb.interface';
import { TranslateModule } from '@ngx-translate/core';
import { IfpButtonComponent } from '../../scad-insights/ifp-widgets/ifp-atoms/ifp-button/ifp-button.component';
import {
  buttonClass,
  buttonIconPosition,
} from 'src/app/scad-insights/core/constants/button.constants';
import { AiButtonComponent } from '../../ifp-widgets/atoms/ai-button/ai-button.component';
import { AiInsightAnalysisCardComponent } from './ai-insight-analysis-card/ai-insight-analysis-card.component';
import { AiInsightCompareModuleComponent } from './ai-insight-compare-module/ai-insight-compare-module.component';
import { AiInsightReportFooterComponent } from './ai-insight-report-footer/ai-insight-report-footer.component';
import {
  GenAiKeyHeaderComponent,
} from './gen-ai-key-header/gen-ai-key-header.component';
import {
  AiInsightReportPopupComponent,
  InsightSave,
} from './ai-insight-report-popup/ai-insight-report-popup.component';
import { IfpModalComponent } from 'src/app/scad-insights/ifp-widgets/ifp-organism/ifp-modal/ifp-modal.component';
import { IfpModalService } from 'src/app/scad-insights/ifp-widgets/ifp-organism/ifp-modal/ifp-modal.service';
import { ApiService } from 'src/app/scad-insights/core/services/api.service';
import { SubSink } from 'subsink';
import {
  InsightReport,
  KeyDriversData,
  ResponseInsight,
} from './interface/insights.interface';
import { IfpSpinnerComponent } from '../../scad-insights/ifp-widgets/ifp-molecules/ifp-spinner/ifp-spinner.component';
import { aiInsightApi } from './constants/ai-insight-api.contants';
import { ToastrService } from 'ngx-toastr';
import { Store } from '@ngrx/store';
import { selectDomainIconGetByIds } from 'src/app/scad-insights/store/domain-icon/domain-icon.selector';
import { IfpAiChartCardComponent } from 'src/app/scad-insights/ifp-chat-bot/ifp-ai-chart-card/ifp-ai-chart-card.component';
import { AiInsightConsolidatedReportSentimentCardComponent } from './ai-insight-consolidated-report-sentiment-card/ai-insight-consolidated-report-sentiment-card.component';
import { ActivatedRoute, Router } from '@angular/router';
import { slaService } from '../../scad-insights/core/services/sla/sla.service';
import { getDomainIconList } from 'src/app/scad-insights/store/domain-icon/domain-icon.action';
import { DownLoadService } from 'src/app/scad-insights/core/services/download-service/download.service';
import { environment } from 'src/environments/environment';
import {
  genarateKeys,
  genarateKeyValue,
} from './constants/ai-insight.constant';
import { cloneDeep } from 'lodash';
import { IfpMarkDownComponent } from '../../scad-insights/ifp-chat-bot/ifp-mark-down/ifp-mark-down.component';
import {
  IfpStepData,
  IfpStepperComponent,
} from '../../scad-insights/ifp-widgets/ifp-atoms/ifp-stepper/ifp-stepper.component';

@Component({
  selector: 'ifp-ai-insight-report',
  imports: [AsyncPipe, IfpBreadcrumbsComponent, TranslateModule,
    IfpButtonComponent, AiButtonComponent,
    AiInsightAnalysisCardComponent, AiInsightCompareModuleComponent, AiInsightReportFooterComponent,
    GenAiKeyHeaderComponent, IfpModalComponent, AiInsightReportPopupComponent,
    IfpAiChartCardComponent, SlicePipe,
    IfpSpinnerComponent, AiInsightConsolidatedReportSentimentCardComponent, IfpMarkDownComponent, IfpStepperComponent],
  templateUrl: './ai-insight-report.component.html',
  styleUrl: './ai-insight-report.component.scss',
})
export class AiInsightReportComponent implements OnDestroy {
  public buttonClass = buttonClass;
  public buttonIconPosition = buttonIconPosition;
  public popupData = signal(false);
  public disableTranslate = signal(true);

  public modal = viewChild<IfpModalComponent>('modal');
  public popup = viewChild<AiInsightReportPopupComponent>('popup');
  public modalType: WritableSignal<
    'header' | 'keyInsights' | 'table' | 'decription' | 'gen'
  > = signal('table');
  public pageData: PageData[] = [
    {
      title: 'Home',
      route: '/home',
    },
    {
      title: 'AI Insight Report Generator',
      route: '',
    },
  ];
  public title = signal('');
  public subTitle = signal('');
  public loader = signal(true);
  public genaratedPreview = signal(false);
  public subs = new SubSink();
  public domainSection = input<{
    name: string;
    id:number;
}>();

public markdown = signal(false);

  public modelAnimate = signal(false);
  public previewExternal = signal(false);
  public previewNot = signal(true);
  public previewGenaratePopup = signal(false);
  public report: WritableSignal<KeyDriversData | undefined> = signal(undefined);
  public temp: WritableSignal<KeyDriversData | undefined> = signal(undefined);
  private _modalService = inject(IfpModalService);
  public _themeService = inject(ThemeService);
  private _api = inject(ApiService);
  private _toaster = inject(ToastrService);
  private _router = inject(Router);
  private _store = inject(Store);
  private _activeRoute = inject(ActivatedRoute);
  private _slaService = inject(slaService);
  public _downloadService = inject(DownLoadService);
  private id = signal('');
  public sectionOne = '';
  public sectionTwo = '';
  public downloadProgress = signal(false);
  public loaderSubmitProgress = signal(false);
  public domainIconSelector$ = this._store.select(selectDomainIconGetByIds(0));
  public stepsData = signal<IfpStepData[]>([]);
  public completedSteps = signal(0);
  ngOnChanges(): void {}
  ngOnInit(): void {
    this.subs.add(
      this._activeRoute.params.subscribe((params) => {
        this.id.set(params['id']);
      })
    );
    const token = this._activeRoute.snapshot.queryParams['token'];
    if (token) {
      this._slaService.slaLoader$.next(false);
      this.genaratedPrewviewData(token);
      this.previewExternal.set(true);
      this.previewNot.set(false);
      this._store.dispatch(getDomainIconList({ token: token, lang: 'en' }));
    } else {
      this.genaratedData();
    }
  }

  callEditHeader() {
    this.markdown.set(false);
    this.markdown.set(false);
    this.popupData.set(true);
    this.modal()?.createElement();
    this.title.set('Edit Key Drivers');
    this.subTitle.set('Cards view');
    this.modalType.set('header');
    this.sectionOne = genarateKeys.summery;
    this.sectionTwo = genarateKeys.drivers;
    this.popup()?.setValue(
      this.report()?.key_drivers.ai_summary ?? '',
      '',
      this.report()?.key_insights,
      this.report()?.key_drivers.drivers
    );
  }

  callKeyInsightsEdit() {
    this.markdown.set(true);
    this.popupData.set(true);
    this.modal()?.createElement();
    this.title.set('Edit Impact');
    this.subTitle.set('Key Insights:');
    this.modalType.set('keyInsights');
    this.sectionOne = genarateKeys.impact;
    this.sectionTwo = genarateKeys.keyInsight;
    this.popup()?.setValue(
      this.report()?.impact.text ?? '',
      this.report()?.impact.heading ?? '',
      this.report()?.key_insights,
      this.report()?.key_drivers.drivers,
      this.report()?.comparison,
      this.report()?.impact.chart
    );
  }

  callComparisonEdit() {
    this.markdown.set(false);
    this.popupData.set(true);
    this.modal()?.createElement();
    this.title.set('Edit Comparison');
    this.subTitle.set('Comparison Table');
    this.modalType.set('table');
    this.sectionOne = genarateKeys.comparison;
    this.sectionTwo = genarateKeys.comparison;
    this.popup()?.setValue(
      this.report()?.impact.text ?? '',
      this.report()?.impact.heading ?? '',
      this.report()?.key_insights,
      this.report()?.key_drivers.drivers,
      this.report()?.comparison
    );
  }
  callSentimentEdit() {
    this.markdown.set(false);
    this.markdown.set(false);
    this.popupData.set(true);
    this.modal()?.createElement();
    this.title.set('Edit Sentiment Analysis');
    this.subTitle.set('Sentiment Analysis');
    this.modalType.set('decription');
    this.sectionOne = genarateKeys.sentimental;
    this.sectionTwo = genarateKeys.sentimental;
    this.popup()?.setValue(
      this.report()?.sentiment?.key_insights_desc,
      '',
      [],
      [],
      this.report()?.comparison,
      this.report()?.sentiment?.chart
    );
  }

  downloadReport() {
    const query = {
      report_id: this.report()?.report_id,
      report_type: this.report()?.report_type,
    };
    this.downloadProgress.set(true);
    this.subs.add(
      this._api
        .getDownloadRequest(
          `${environment.baseUrl}${environment.apiVersion}${aiInsightApi.reportDownload}`,
          query
        )
        .subscribe({
          next: (data) => {
            // Assume contentDisposition is the variable that might contain the header string
            const contentDisposition = data.headers.get('Content-Disposition'); // example value

            let fileName = null;

            if (
              typeof contentDisposition !== 'undefined' &&
              contentDisposition
            ) {
              const matches = contentDisposition.match(
                /filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/
              );
              if (matches && matches[1]) {
                // Clean up quotes if present
                fileName = matches[1].replace(/['"]/g, '');
              }
            }
            this._downloadService.downloadFiles(data.body, fileName ?? '.pdf');
            this.downloadProgress.set(false);
          },
          error: () => {
            this.downloadProgress.set(false);
          },
        })
    );
  }

  save(event: InsightSave) {
    switch (event.type) {
      case 'header': {
        this.saveKeyInsights(event);
        break;
      }
      case 'table': {
        this.saveComparison(event);
        break;
      }
      case 'decription': {
        this.editSentimetal(event);
        break;
      }
      case 'keyInsights': {
        this.saveImpact(event);
        break;
      }
    }
  }

  saveImpact(event: InsightSave) {
    const impact = {
      section: 'impact',
      data: [
        {
          impact: {
            heading: event.heading,
            text: event.description,
            chart: event.chart,
          },
        },
        {
          key_insights: event.keyInsights,
        },
      ],
    };
    this.subs.add(
      this._api
        .patchMethodRequest(aiInsightApi.impactEdit + this.id(), impact)
        .subscribe((data: ResponseInsight) => {
          this._toaster.success(data.message);
          this.genaratedData();
          this.closeModal();
        })
    );
  }

  saveKeyInsights(event: InsightSave) {
    const keyInsights = {
      section: 'key_drivers',
      data: [
        {
          key_drivers: {
            ai_summary: event.description,
            drivers: event.drivers,
          },
        },
      ],
    };
    this.subs.add(
      this._api
        .patchMethodRequest(
          aiInsightApi.editKeyInsights + this.id(),
          keyInsights
        )
        .subscribe((data: ResponseInsight) => {
          this._toaster.success(data.message);
          this.genaratedData();
          this.closeModal();
        })
    );
  }

  saveComparison(event: InsightSave) {
    const comparison = {
      section: 'comparison',
      data: [{ comparison: event.insightComparison }],
    };
    this.subs.add(
      this._api
        .patchMethodRequest(
          aiInsightApi.editComparision + this.id(),
          comparison
        )
        .subscribe((data: ResponseInsight) => {
          this._toaster.success(data.message);
          this.genaratedData();
          this.closeModal();
        })
    );
  }

  editSentimetal(event: InsightSave) {
    const editSentimetal = {
      section: 'sentiment',
      data: [{ key_insights_desc: event.description }],
    };

    this.subs.add(
      this._api
        .patchMethodRequest(
          aiInsightApi.editSentimetal + this.id(),
          editSentimetal
        )
        .subscribe((data: ResponseInsight) => {
          this._toaster.success(data.message);
          this.genaratedData();
          this.closeModal();
        })
    );
  }

  regenarateAll() {
    this.popupData.set(true);
    this.modal()?.createElement();
    this.title.set('Regenerate All');
    this.modalType.set('gen');
    this.previewGenaratePopup.set(true);
    this.popup()?.setValue('', '', [], [], { headers: [], rows: [] });
  }
  cancel() {
    this.modelAnimate.set(false);
    this.subs.unsubscribe();
    this.closeModal();
  }

  genarate(event: InsightSave) {
    const value = {
      report_id: this.id(),
      section: event.section,
      user_prompt: event.value,
    };
    this.modelAnimate.set(true);
    this.subs.add(
      this._api.postMethodRequest(aiInsightApi.genarateCards, value).subscribe({
        next: (data) => {
          switch (event.type) {
            case 'header': {
              this.popup()?.setParticularValue(
                event.section,
                data?.[genarateKeyValue.summery] ?? '',
                '',
                data?.[genarateKeyValue.keyInsight],
                data?.[genarateKeyValue.drivers],
                data?.[genarateKeyValue.comparison]
              );
              break;
            }
            case 'table': {
              this.popup()?.setParticularValue(
                event.section,
                data?.[genarateKeyValue.summery] ?? '',
                '',
                data?.[genarateKeyValue.keyInsight],
                data?.[genarateKeyValue.drivers],
                data?.[genarateKeyValue.comparison]
              );
              break;
            }
            case 'decription': {
              this.popup()?.setParticularValue(
                event.section,
                data?.[genarateKeyValue.sentimental] ?? '',
                '',
                data?.[genarateKeyValue.keyInsight],
                data?.[genarateKeyValue.drivers],
                data?.[genarateKeyValue.comparison]
              );
              break;
            }
            case 'keyInsights': {
              this.popup()?.setParticularValue(
                event.section,
                data?.text ?? '',
                data?.heading ?? '',
                data?.[genarateKeyValue.keyInsight] ?? [],
                data?.[genarateKeyValue.drivers],
                data?.[genarateKeyValue.comparison],
                data?.chart
              );
              break;
            }
            case 'gen': {
              this.temp.set(cloneDeep(this.report()));
              this.report.update(() => {
                return data;
              });
              this.previewNot.set(false);
              this.genaratedPreview.set(true);
              this.closeModal();
              break;
            }
          }

          this.modelAnimate.set(false);
        },
        error: () => {
          this.modelAnimate.set(false);
        },
      })
    );
  }

  cancelGenerate() {
    this.previewNot.set(true);
    this.genaratedPreview.set(false);
    this.report.update(() => {
      return this.temp();
    });
  }
  submitGenerate() {
    this.previewNot.set(true);
    this.genaratedPreview.set(false);
    const keys = Object.keys(this.report() ?? {});
    const dataValue: Record<string, any>[] = [];
    keys.forEach((keys) => {
      dataValue.push({ [keys]: this.report()?.[keys as keyof KeyDriversData] });
    });
    const editSentimetal = {
      section: 'all',
      data: dataValue,
    };

    this.subs.add(
      this._api
        .patchMethodRequest(
          aiInsightApi.saveAllData + this.id(),
          editSentimetal
        )
        .subscribe((data: ResponseInsight) => {
          this._toaster.success(data.message);
          this.genaratedData();
        })
    );
  }

  genaratedData() {
    this.loader.set(true);
    this.subs.add(
      this._api
        .getMethodRequest(aiInsightApi.getGenaratedData + this.id())
        .subscribe((data: InsightReport) => {
          this.loader.set(false);
          this.report.set(data.keyDriversData);
          const step: IfpStepData[] = [];
          data.keyDriversData.workFlow.stepNames.forEach((dataValue, index) => {
            step.push({
              counter: index+1,
              name: dataValue,
              stepCount: index+1,
            });
            if(dataValue === data.keyDriversData.workFlow.currentStep) {
              if((data.keyDriversData.workFlow.stepNames.length-1 === index)) {
                  this.completedSteps.set(index + 1);
              } else {
                  this.completedSteps.set(index);
              }
            }
          });
          this.stepsData.set(step);

          this.domainIconSelector$ = this._store.select(
            selectDomainIconGetByIds(data.keyDriversData.domain?.id ?? 0)
          );
        })
    );
  }

  genaratedPrewviewData(token: string) {
    this.loader.set(true);
    this.subs.add(
      this._api
        .getMethodPreview(aiInsightApi.getGenaratedData + this.id(), {}, token)
        .subscribe((data: InsightReport) => {
          this.loader.set(false);
          this.report.set(data.keyDriversData);
          this.domainIconSelector$ = this._store.select(
            selectDomainIconGetByIds(data.keyDriversData.domain?.id ?? 0)
          );
        })
    );
  }
  submit() {
    this._toaster.info(
      'The PDF for the Domain page is being generated and will be attached to the external system soon. Please expect some delay.'
    );
    //this._router.navigateByUrl('/insight-report-list');
    const data = {
      domain_name: this.report()?.domain?.name,
      report_id: this.report()?.report_id,
    };
    this.loaderSubmitProgress.set(true);
    this.subs.add(
      this._api
        .postMethodRequest(aiInsightApi.reviewSubmitDomain, data)
        .subscribe(() => {
          this._router.navigateByUrl('/insight-report-list');
          this._toaster.success(
            'Sanadkom ticket has been created for the domain and sent for publication approval.'
          );
          // this.popup()?.setParticularValue(event.type,data.key_insights, []);
          this.loaderSubmitProgress.set(false);
        })
    );
  }

  closeModal() {
    this.popupData.set(false);
    this.modal()?.removeModal();
    this._modalService.removeAllModal();
    this.previewGenaratePopup.set(false);
  }

  ngOnDestroy(): void {
    this.subs.unsubscribe();
  }
}
