import { Component, input, output, signal } from '@angular/core';
import { AiInputComponent } from "../../../ifp-widgets/atoms/ai-input/ai-input.component";
import { AiButtonComponent } from "../../../ifp-widgets/atoms/ai-button/ai-button.component";
import { TranslateModule } from '@ngx-translate/core';
import { NgClass } from '@angular/common';
import { FormControl, ReactiveFormsModule } from '@angular/forms';
import { IfpTooltipDirective } from 'src/app/scad-insights/core/directives/ifp-tooltip.directive';

@Component({
  selector: 'ifp-ai-insight-report-genarate-badge',
  imports: [AiInputComponent, AiButtonComponent, TranslateModule, NgClass, ReactiveFormsModule,IfpTooltipDirective],
  templateUrl: './ai-insight-report-genarate-badge.component.html',
  styleUrl: './ai-insight-report-genarate-badge.component.scss'
})
export class AiInsightReportGenarateBadgeComponent {
  public animation =input(false);
  public enableCardView = input(false);
  public disable = input(false);
  public disableTranslate =  input(false);
  public enableBadge =signal(false)
  public genarate = output<string>();
  public formControl = new FormControl('')

  genarateValue() {
    this.genarate.emit(this.formControl.value ?? '');
  }

  resetValue() {
    this.formControl.reset();
  }
}
