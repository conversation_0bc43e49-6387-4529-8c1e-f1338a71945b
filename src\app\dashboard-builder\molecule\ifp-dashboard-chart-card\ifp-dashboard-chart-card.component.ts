import { ChangeDetectionStrategy, Component, input, InputSignal, output, signal, WritableSignal } from '@angular/core';
import { IfpButtonComponent } from "../../../scad-insights/ifp-widgets/ifp-atoms/ifp-button/ifp-button.component";
import { buttonClass } from 'src/app/scad-insights/core/constants/button.constants';
import { TranslateModule } from '@ngx-translate/core';
import { NgClass } from '@angular/common';
import { cardObject } from '../../pages/dashboard.interface';
import { IfpAnalyticLineChartComponent } from "../../../scad-insights/ifp-widgets/ifp-molecules/ifp-analytic-line-chart/ifp-analytic-line-chart.component";
import { chartConstants } from 'src/app/scad-insights/core/constants/chart.constants';

@Component({
  selector: 'ifp-dashboard-chart-card',
  imports: [IfpButtonComponent, TranslateModule, NgClass, IfpAnalyticLineChartComponent],
  templateUrl: './ifp-dashboard-chart-card.component.html',
  styleUrl: './ifp-dashboard-chart-card.component.scss',
})
export class IfpDashboardChartCardComponent {

  // inputs //
  public slectedCardId: InputSignal<number> = input(0);
  public isActiveCard: InputSignal<boolean> = input(false);
  public cardConfig: InputSignal<cardObject> = input({} as cardObject)

  // output //
  public emitConfigureData = output<boolean>();

  public buttonClass = buttonClass;
  public xAxisLabelType: string = chartConstants.xAxisCatogory;


  openConfigurePanel() {
    this.emitConfigureData.emit(true);
  }

  createChartData() {

  }
}
