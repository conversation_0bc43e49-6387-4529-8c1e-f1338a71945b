import { Injectable } from '@angular/core';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { concatLatestFrom } from '@ngrx/operators';

import { DomainsService } from 'src/app/scad-insights/core/services/domains/domains.service';
import { catchError, map, of, filter, forkJoin, switchMap, concatMap, retry, timer, tap } from 'rxjs';
import { Store } from '@ngrx/store';

import { dummyAction } from 'src/app/scad-insights/store/app.action';
import { ROUTER_NAVIGATION, RouterNavigatedAction } from '@ngrx/router-store';
import { RouterStateUrl } from './../../../store/router/custom-serializer';
import { loadDomainDetailsNew, loadDomainDetailsNewData, loadDomainDetailsNewNode, loadDomainDetailsSuccessNew } from './domain-details-api.action';
import { selectDomainDetailsNewGetById, selectDomainDetailsNewGetId } from './domain-details-api.selector';
import { DomainClassification } from 'src/app/scad-insights/core/interface/domain.interface';
import { classifications } from 'src/app/scad-insights/core/constants/domain.constants';
@Injectable()
export class DomainDetailsNewEffects {

  constructor(
    private actions$: Actions,
    private _domainService: DomainsService,
    private store: Store
  ) { }

  category$ = createEffect(() => {
    let classification = '';
    return this.actions$.pipe(
      ofType(ROUTER_NAVIGATION),
      filter((r: RouterNavigatedAction<RouterStateUrl>) => {
        return r.payload.routerState.url.startsWith('/domain-exploration/') &&  r.payload.routerState['params']['domain'];
      }),
      map((r: RouterNavigatedAction<RouterStateUrl>) => {
        classification = r.payload.routerState['queryParams']['key'];

        return {id: r.payload.routerState['params']['id'], domain: r.payload.routerState['params']['domain'] };
      }),
      concatLatestFrom(() => this.store.select(selectDomainDetailsNewGetById)),
      switchMap(([data, action]: any) =>{
        if (!action?.status && data?.domain) {
          return this._domainService.getDomainDetailClassification(data.id, classification).pipe(map((result)=> {
            return {result: result, data: data, action: action};
          }));
        }
        return of(dummyAction());
      }

      ),
      switchMap(({result, data, action}: any) =>{
        console.log("result", result, data, action)
        if (!action?.status && data?.domain) {
          const filterApi: any = {};
          const tab: DomainClassification[] =[];
          result?.classification?.forEach((element: DomainClassification) => {
            element['nodeCount']= +element.count;
            tab.push(element);
            if (+element.count > 0) {
              filterApi[element.id] = this._domainService.getDomainDetailFilter(data.id, +element.id).pipe(map((filterData: any) => {
                if (element.key === classifications.analyticalApps && filterData?.length !== 0) {
                  filterData?.unshift({
                    'id': 'All',
                    'name': 'All',
                    'subthemes': []
                  });
                }
                return {
                  data: filterData,
                  key: element.key
                };
              }
              ), catchError(() => of([])));
            }
          });
          return forkJoin(filterApi).pipe(map((resultFilter: any) => {
            return loadDomainDetailsSuccessNew({ data: {
              id: data.id,
              classification: tab,
              filter: resultFilter,
              domain: result?.domain
            }, errorMessage: '', status: true, loader: false});
          }));
        }
        return of(dummyAction());
      }
      )
    );
  });

  selectById$ = createEffect(() => {
    return this.actions$.pipe(
      ofType(loadDomainDetailsNew),
      concatLatestFrom((action) => this.store.select(selectDomainDetailsNewGetId(action.id))),
      switchMap(([data, action]: any) =>{
        if (!action?.status) {
          return this._domainService.getDomainDetailClassification(data.id).pipe(map((result)=> {
            return {result: result, data: data, action: action};
          }));
        }
        return of(dummyAction());
      }

      ),
      switchMap(({result, data, action}: any) =>{
        if (!action?.status) {
          const filterApi: any = {};
          const tab: DomainClassification[] =[];
          result?.classification?.forEach((element: DomainClassification) => {
            element['nodeCount']= +element.count;
            tab.push(element);
            if (+element.count > 0) {
              filterApi[element.id] = this._domainService.getDomainDetailFilter(data.id, +element.id).pipe(map((filterData: any) => {
                if (element.key === classifications.analyticalApps && filterData?.length !== 0) {
                  filterData?.unshift({
                    'id': 'All',
                    'name': 'All',
                    'subthemes': []
                  });
                }
                return {
                  data: filterData,
                  key: element.key
                };
              }
              ), catchError(() => of([])));
            }

          }
          );
          return forkJoin(filterApi).pipe(map((resultFilter: any) => {
            return loadDomainDetailsSuccessNew({ data: {
              id: data.id,
              classification: tab,
              filter: resultFilter,
              domain: result?.domain
            }, errorMessage: '', status: true, loader: false});
          }));
        }
        return of(dummyAction());
      }
      )
    );
  });

  selectByNodes$ = createEffect(() => {
    return this.actions$.pipe(
      ofType(loadDomainDetailsNewNode),
      tap((data: any) => {
        console.log("API call initiated with data:", data);
      }),
      // Use concatMap instead of switchMap to prevent cancellation of previous requests
      concatMap((data: any) => {
        console.log("Processing API call for:", data.id, "with filter:", data.filter);

        return this._domainService.getDomainDetail(data.id, data.filter).pipe(
          // Add retry mechanism for failed requests
          retry({
            count: 2,
            delay: (error, retryCount) => {
              console.log(`API call failed, retry attempt ${retryCount}:`, error);
              return timer(1000 * retryCount); // Exponential backoff: 1s, 2s
            }
          }),
          map((result) => {
            console.log("API call successful:", result);
            return loadDomainDetailsNewData({data: result});
          }),
          catchError((error) => {
            console.error("API call failed after retries:", error);
            // Return a more informative error action instead of dummy action
            return of(loadDomainDetailsNewData({
              data: {
                results: [],
                total_count: 0,
                error: 'Failed to load data. Please try again.',
                status: 'error'
              }
            }));
          })
        );
      })
    );
  });
}
