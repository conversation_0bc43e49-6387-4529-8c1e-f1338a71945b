<div class="ifp-container" *ngIf="!isModal">
  <app-ifp-breadcrumbs [pageData]="pageData"></app-ifp-breadcrumbs>
</div>

<ng-container *ngIf="(termsData$ | async) as terms">
  <div class="ifp-static" [ngClass]="{'ifp-static--modal': isModal}">
    <div [ngClass]="{'ifp-container': !isModal}">
    <div class="ifp-static__head">
        <h1 class="ifp-module-heading">{{(terms.title ? terms.title
          : '') | translate}}</h1>
        <div *ngIf="isModal">
          <span *ngIf="isLanguage" (click)="languageChange(selectedLanguage === 'en' ? 'ar': 'en')" class="ifp-link">{{selectedLanguage === 'en' ? 'عربي': 'English'}}</span>
          <em class="ifp-icon ifp-icon-round-cross ifp-static__close" (click)="onButtonClick(false)"></em>
        </div>
      </div>
      <ng-container *ngIf="terms.sections.length > 0">
        <div class="ifp-static__cnt">
          <div class="ifp-static__box" *ngFor="let section of terms.sections">
            <h2 class="ifp-static__title">{{section.sectionTitle ? section.sectionTitle : ''}}</h2>
            <ng-container *ngIf="section.subsections.length > 0">
              <div class="ifp-static__content" *ngFor="let subsection of section.subsections">
                <h3 class="ifp-static__sub-title">{{(subsection.subsectionTitle ? subsection.subsectionTitle : '')}}</h3>
                <p class="ifp-static__desc"
                  [innerHTML]="(subsection.subsectionContent ? subsection.subsectionContent : '') | lineBreak"></p>
              </div>
            </ng-container>
          </div>
          <ifp-button [label]="'Accept' | translate" class="ifp-static__btn"
            (ifpClick)="onButtonClick(true, termData.tcVersion)" *ngIf="isModal && !isAccepted"></ifp-button>
        </div>
      </ng-container>
    </div>
  </div>
</ng-container>

<!-- <div class="ifp-tnc-modal__btn-sec" *ngIf="!isAccepted">
  <ifp-button [label]="'Accept' | translate" class="ifp-insight__btn"
    (ifpClick)="onButtonClick(true, termData.tcVersion)"></ifp-button>
</div> -->
