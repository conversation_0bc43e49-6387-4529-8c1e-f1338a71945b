import { Component, EventEmitter, Input, Output, signal } from '@angular/core';
import { SafeUrl } from '@angular/platform-browser';
import { TranslateModule } from '@ngx-translate/core';
import { IfpDbFileUploaderComponent } from 'src/app/dashboard-builder/molecule/ifp-db-file-uploader/ifp-db-file-uploader.component';
import { fileFormats } from 'src/app/dashboard-builder/molecule/ifp-db-file-uploader/ifp-db-file-uploader.constants';
import { IfpButtonComponent } from '../../../scad-insights/ifp-widgets/ifp-atoms/ifp-button/ifp-button.component';
import { buttonClass } from 'src/app/scad-insights/core/constants/button.constants';
import { NgClass } from '@angular/common';
import { IfpUploadBarComponent } from '../../molecule/ifp-upload-bar/ifp-upload-bar.component';
import { IfpDropdownComponent } from '../../../scad-insights/ifp-widgets/ifp-atoms/ifp-dropdown/ifp-dropdown.component';
import { IfpPrepUploadService } from '../../data-prep/ifp-data-prep/ifp-data-prep-upload/services/ifp-prep-upload.service';
import { SubSink } from 'subsink';
import { ApiStatus } from 'src/app/scad-insights/core/constants/api-status.constants';
import { ToasterService } from 'src/app/scad-insights/core/services/tooster/ToastrService.service';
import { Router } from '@angular/router';
import { IfpAdvancePrepService } from '../../data-prep/ifp-advance-prep/service/ifp-advance-prep.service';
import { IfpPrepService } from '../../data-prep/ifp-data-prep/ifp-prep-service';
@Component({
    selector: 'ifp-prep-upload-data',
    templateUrl: './ifp-prep-upload-data.component.html',
    styleUrl: './ifp-prep-upload-data.component.scss',
    providers: [IfpPrepUploadService],
    imports: [TranslateModule, IfpDbFileUploaderComponent, IfpButtonComponent, NgClass, IfpUploadBarComponent, IfpDropdownComponent]
})
export class IfpPrepUploadDataComponent {

  @Output() fileUpload: EventEmitter<UploadedFile> = new EventEmitter<UploadedFile>();
  @Output() removeFile: EventEmitter<File[]> = new EventEmitter<File[]>();
  @Output() openLibrary = new EventEmitter();
  @Output() uploadCompletedProced = new EventEmitter();
  @Output() indicatorClickEvent = new EventEmitter();
  @Output() selectSheet: EventEmitter<{ file: UploadedFile, path: string, sheet: string, sheetList?: string[] }> = new EventEmitter<{ file: UploadedFile, path: string, sheet: string, sheetList?: string[] }>();
  @Output() excelUploaded = new EventEmitter();
  @Output() excelUploadedError = new EventEmitter();
  @Output() disableEvent = new EventEmitter();
  @Input() allowedExtensions: string[] = fileFormats.excelFormats;
  @Input() headingEnable = false;
  @Input() progress = signal(0);
  @Input() disableProcesed = false;
  @Input() loader = false;
  @Input() loaderFile = false;
  @Input() currentFile = signal('');
  @Input() rotate = false;
  @Input() indicatorEnable = true;
  @Input() enableProcessBtn = true;
  @Input() hideDelete = false;
  @Input() size?: number;
  @Input() error = false;
  @Input() disableAll = false;
  @Input() subHeading = 'Select your preferred source to upload the data';
  @Input() heading = 'Select Data Source';
  @Input() sheetList: string[] = [];
  @Input() selectedWorksheet: string = '';
  @Input() uploadedFile!: UploadedFile;
  @Input() s3Path!: string;
  public buttonClass = buttonClass;
  public subs: SubSink = new SubSink();



  constructor(private _prepService: IfpPrepUploadService, private _toaster: ToasterService, private _router: Router, public _prepCommonService: IfpAdvancePrepService, public _ifpPrepService: IfpPrepService) {
  }

  onProceed() {
    this.uploadCompletedProced.emit();
  }

  uploadFile(item: UploadedFile) {
    const filename = item.file[0].name;
    if (filename.split('.').pop() === 'xlsx') {
      this.uploadedFile = item;
      this.getSheetList(item.file[0]);
    } else {
      this.disableProcesed=true;
      this.sheetList = [];
      this.error = false;
      this.selectedWorksheet= '';
      this.disableEvent.emit(true);
      this.fileUpload.emit(item);
    }
    this.currentFile.set(filename);
    this.size = item.file[0].size;
  }

  deleteFile(item: File[]) {
    this.removeFile.emit(item);
    this.currentFile.set('');
    this.sheetList = [];
    this.size = undefined;
    this.disableProcesed=true;
    this.disableEvent.emit(true);
  }

  clearSheets() {
    this.sheetList = [];
    this.selectedWorksheet= '';
  }

  onLibraryClick() {
    this.openLibrary.emit();
  }

  onIndicatorClick() {
    this.indicatorClickEvent.emit();
  }

  getSheetList(file: File) {
    const uploadData = new FormData();
    uploadData.append('file', file);
    this.error = false;
    this.subs.add(
      this._prepService.getSheetList(uploadData).subscribe({
        next: data => {
          if (data?.status === ApiStatus.ok) {
            this.error = false;
            this.disableProcesed=false;
            this.progress.set(100);
            this.sheetList = data?.body?.sheet_names;
            this.disableEvent.emit(this.sheetList && this.sheetList.length === 0);
            this.s3Path = data?.body?.s3_path;
            this.selectedWorksheet = this.sheetList?.[0];
            this.selectSheet.emit({ file: this.uploadedFile, path: this.s3Path, sheet: this.selectedWorksheet, sheetList: this.sheetList });
            this.excelUploaded.emit({ file: this.uploadedFile, path: this.s3Path, sheet: this.selectedWorksheet, sheetList: this.sheetList });
          }
        }, error: err => {
          this.excelUploadedError.emit();
          this.sheetList = [];
          this.selectedWorksheet= '';
          this.errorHandler(err?.error);
          this.disableEvent.emit(true);
        }
      })
    );
  }

  errorHandler(error: any) {
    this.error = true;
    this._ifpPrepService.disableSidebar.set(true);
    this._ifpPrepService.uploadedFileResponse.set(null);
    if (typeof error === 'string' ) {
      if (error!== '') {
        this._toaster.error(error, 10000);
        this.disableProcesed=true;
        this.disableEvent.emit(true);
      }
    } else {
      for (const key in error) {
        if (Object.prototype.hasOwnProperty.call(error, key)) {
          const element = error[key];
          let data = '';
          if (typeof element === 'string' ) {
            data = element;
          } else {
            element.forEach((elementValue:string) => {
              data =  `${data} ${elementValue}`;
            });
          }
          if (data!== '') {
            this._toaster.error(data, 10000);
            this.disableProcesed=true;
            this.disableEvent.emit(true);
          }
        }
      }
    }
  }

  selectWorkSheet(sheet: string) {
    this.selectedWorksheet = sheet;
    this.selectSheet.emit({ file: this.uploadedFile, path: this.s3Path, sheet: this.selectedWorksheet });
  }
}

export interface UploadedFile {
  file: File[];
  url: SafeUrl;
}
