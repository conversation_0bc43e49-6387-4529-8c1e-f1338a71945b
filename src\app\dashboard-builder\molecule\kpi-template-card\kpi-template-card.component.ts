import { NgClass } from '@angular/common';
import { Component, inject, input, InputSignal, OnChanges, output, SimpleChanges, viewChild } from '@angular/core';
import { FormBuilder, FormControl, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { IfpKebabMenuComponent } from "../ifp-kebab-menu/ifp-kebab-menu.component";
import { KebabMenuOption } from '../ifp-kebab-menu/ifp-kebab-menu.interface';
@Component({
  selector: 'ifp-kpi-template-card',
  imports: [ReactiveFormsModule, NgClass, IfpKebabMenuComponent],
  templateUrl: './kpi-template-card.component.html',
  styleUrl: './kpi-template-card.component.scss',
  host: {
    '[class.ifp-kpi-temp--icon]': 'isIcon()',
    '[class.ifp-kpi-temp--center]': 'isCenter()',
    '[class.ifp-kpi-temp--right]': 'isRight()',
    '[class.ifp-kpi-temp--selected]': 'isSelected()'
  }
})
export class KpiTemplateCardComponent implements OnChanges {
  private readonly _formBuilder: FormBuilder = inject(FormBuilder);

  selectEvent = output<SelectActionEvent>();

  cardConfig: InputSignal<Record<string, any>> = input.required();
  name = input('');
  isIcon = input(false);
  alignment = input<string>('')
  isCenter = input(false);
  isRight = input(false);
  isSelected = input(false);
  unit = input('%');

  kpiCardForm: FormGroup = new FormGroup({});
  defaultTemplate: Record<string, any> = {
    label: 'Label',
    value: 'Value',
    valueSecondary: 'Label: Value 2',
    valueTertiary: 'Label: Value 3',
    compareValue: {
      value: 20,
      isInverted: false
    }
  }
  requiredFields: string[] = ['label', 'value'];
  kebabOptions: KebabMenuOption[] = [{
      name: 'Edit',
      event: 'edit',
      icon: 'ifp-icon-edit',
      disabled: false
    }]

  ngOnChanges(changes: SimpleChanges) {
    if(changes['cardConfig']) {
      this.initForm();
    }
  }

  initForm() {
    const keys = Object.keys(this.cardConfig());
    if(keys.length) {
      keys.forEach((key: string) => {
        this.kpiCardForm.addControl(key, new FormControl(this.defaultTemplate[key] ?? null, [this.requiredFields.includes(key) ? Validators.required : Validators.nullValidator]))
      });
    }
  }

  onSelectOptions(event: string) {
    this.selectEvent.emit({event: event, template: {name: this.name(), config: this.kpiCardForm.value}});
  }
}

export interface SelectActionEvent {
  event: string;
  template: {
    name: string;
    config: Record<string, any>;
  }
}
