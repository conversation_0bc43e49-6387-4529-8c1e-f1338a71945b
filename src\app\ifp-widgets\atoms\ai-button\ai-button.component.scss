@use "../../../../assets/ifp-styles/abstracts/index" as *;
:host{
  display: inline-block;
}
.ifp-ai-button {
  &__btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    color: inherit;
    padding: $spacer-1 $spacer-3;
    background-color: transparent;border: 1px solid $ifp-color-grey-2;
    cursor: pointer;
    border-radius: 5px;
  }
  &__icon {
    margin-inline-end: $spacer-2;
  }
  &__pad-6 {
    padding: 6px $spacer-3;
  }

  &--ai {

    background-image: linear-gradient(158deg, #126DEF,#3EBEF2, #8A7DFA,#0154E9);
    border-radius: 5px;
    padding: 2px;
    overflow: hidden;

    .ifp-ai-button {
      &__outer{
        background-color: $ifp-color-section-white;
        border-radius: 4px;
      }

    &__btn {
      background-image: radial-gradient(ellipse at top, #126DEF, #3EBEF2, #8A7DFA 50%, #0154E9 84%);
      background-size: cover;
      color: #8A7DFA;
      -webkit-text-fill-color: transparent;
      text-fill-color: transparent;
      -webkit-background-clip: text;
      background-clip: text;
      display: inline-block;
      border: 1px solid transparent;
      border-radius: 10px;
      object-fit: cover;
      background-size: cover;
      background-repeat: no-repeat;     }}
      &--30 {
        background-image: radial-gradient(ellipse at top left, #126DEF4d -50%, #3EBEF24d 30%, #8A7DFA4d 60%, #0154E94d 100%);
      }
      &--bordered {
        .ifp-ai-button {
        &__outer-wrap  {
          padding: 1px;
          border-radius: 20px;
          background-image: radial-gradient(ellipse at top left, #126DEF -50%, #3EBEF2 30%, #8A7DFA 60%, #0154E9 100%);
        }
      }
      }

  }
  &--ai-secondary{
    background-image: radial-gradient(ellipse at top left, #126DEF -50%,#3EBEF2 31%, #8A7DFA 60%,#0154E9 100%);
    border-radius: 5px;
    padding: 2px;
    overflow: hidden;

.ifp-ai-button {
    &__btn {
      border: 0;
      color: $ifp-color-white-global;
    }
  }

  }
  &--ai-round{
    padding: $spacer-1;
    border-radius: 20px;
    .ifp-ai-button {
      &--ai {
        border-radius: 20px;
        padding: $spacer-1;
      }
      &__outer {
        border-radius: 20px;
      }
    }

  }
  &--round {
    .ifp-ai-button {
      &__btn {
    border-radius: 50px;
      }}
  }

  &--white {
    .ifp-ai-button {
      &__btn {
        color: $ifp-color-white;
        border: 1px solid $ifp-color-white;
      }
    }
  }
  &--disabled {
    opacity: 0.5;
      color: $ifp-color-white;
      border: 1px solid $ifp-color-grey-2;
      cursor: not-allowed;
      pointer-events: none;
      background-image: none;
    .ifp-ai-button{
      &__btn,  &__outer {
        background-color: $ifp-color-grey-2;
        color: $ifp-color-white;
        border: 1px solid $ifp-color-grey-2;
        cursor: not-allowed;
        pointer-events: none;
    }
  }
  }
}
:host-context([dir="rtl"]) {
  .ifp-ai-button {
    &--disable-translate {
      direction: ltr;
    }
  }
}
