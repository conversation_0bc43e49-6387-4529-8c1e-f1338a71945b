@use "../../../assets/ifp-styles/abstracts/index" as *;

.ifp-dxp-detail {
  &__wrapper {
    padding: $spacer-4;
    margin-top: $spacer-4;
    background-color: $ifp-color-section-white;
    border-radius: 10px;
    box-shadow: 0 4px 29px $ifp-color-black-04;
  }
  &__banner-sec {
    display: flex;
    margin-bottom: $spacer-5;
  }
  &__data {
    width: 50%;
    display: flex;
    flex-direction: column;
    padding-bottom: $spacer-3;
  }
  &__img {
    width: 20%;
    background-size: cover;
    background-position: center;
    border-radius: 10px;
    margin-inline-end: $spacer-4;
    min-height: 200px;
  }
  &__heading {
    font-size: $ifp-fs-7;
    font-weight: $fw-bold;
    margin-bottom: $spacer-2;
  }
  &__desc {
    color: $ifp-color-grey-14;
    margin-bottom: $spacer-3;
  }
  &__entity {
    color: $ifp-color-blue-hover;
    margin-top: auto;
  }
  &__subtitle {
    font-size: $ifp-fs-4;
    font-weight: $fw-semi-bold;
    margin-bottom: $spacer-3;
  }
  &__modal {
    background-color: $ifp-color-white;
    border-radius: 10px;
  }
  &__modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: $spacer-3 $spacer-5;
    border-bottom: 1px solid $ifp-color-grey-13;
  }
  &__modal-body {
    padding: $spacer-4 $spacer-5;
  }
  &__modal-title {
    font-size: $ifp-fs-5;
    font-weight: $fw-bold;
    position: relative;
    &::before {
      content: "";
      width: 100%;
      height: 3px;
      background-color: $ifp-color-blue-hover;
      position: absolute;
      bottom: (-$spacer-3 - 2px);
      left: 0;
    }
  }
  &__modal-close {
    cursor: pointer;
    font-size: $ifp-fs-4;
    opacity: 0.7;
    transition: 0.3s;
    &:hover {
      opacity: 1;
    }
  }
  &__modal-dictionary-list {
    display: flex;
    flex-direction: column;
    gap: $spacer-2;
    margin-top: $spacer-2;
  }
  &__modal-dictionary-row {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: $spacer-4;
    padding: $spacer-1 0;
    border-bottom: 1px solid $ifp-color-grey-6;
    &:last-child {
      border-bottom: none;
    }
  }
  &__modal-dictionary-header {
    font-weight: $fw-bold;
    background: $ifp-color-grey-5;
    border-radius: 4px;
    border-bottom: 2px solid $ifp-color-grey-5;
    padding: $spacer-1 0;
  }
  &__modal-dictionary-col {
    flex: 1 1 0;
    min-width: 0;
    word-break: break-word;
    font-size: $ifp-fs-3;
  }
  &__metadata-item {
    margin-bottom: $spacer-2;
    &:last-child {
      margin-bottom: $spacer-0;
    }
  }
  &__modal-table {
    display: block;
    max-height: 60vh;
    padding-inline-end: $spacer-2;
    @include ifp-scroll-y($ifp-color-grey-bg, $ifp-color-grey-8, 8px, 8px);
  }
}

// .ifp-dxp-detail__modal {
//   display: flex;
//   flex-direction: column;
//   max-height: 80vh;
// }

// .ifp-dxp-detail__modal-header {
//   flex: 0 0 auto;
//   /* existing header styles */
// }

// .ifp-dxp-detail__modal-body {
//   flex: 1 1 auto;
//   overflow-y: auto;
//   max-height: 60vh;
// }
