import { cloneDeep } from 'lodash';
import { CanvasTool, NodeData } from './../../data-prep/ifp-data-prep/interface/ifp-data-prep.interface';
import { CommonModule, DOCUMENT, NgClass } from '@angular/common';
import { ChangeDetectionStrategy, ChangeDetectorRef, Component, ElementRef, EventEmitter, Inject, Input, OnChanges, OnInit, Output, Renderer2, ViewChild, WritableSignal, inject, signal } from '@angular/core';
import Drawflow from 'drawflow';
import { Tool<PERSON>, Tools } from '../../data-prep/ifp-data-prep/interface/ifp-data-prep.interface';
import { advancedTools, canvasTools, nodeAnchors } from '../../data-prep/ifp-data-prep/ifp-data-prep.constant';
import { v4 as uuidv4 } from 'uuid';
import { selectNodeStoreAdvance } from '../../data-prep/ifp-advance-prep/store/advance-node-store/advance-node.store';
import { IfpAdvancePrepService } from '../../data-prep/ifp-advance-prep/service/ifp-advance-prep.service';
import { toolName } from '../../data-prep/ifp-data-prep/constants/ifp-state.contants';
import { TranslateService } from '@ngx-translate/core';
import { connectionType } from '../../data-prep/ifp-data-prep/constants/if-preps.constants';


@Component({
    selector: 'ifp-prep-advance-playground',
    templateUrl: './ifp-prep-advance-playground.component.html',
    styleUrl: './ifp-prep-advance-playground.component.scss',
    changeDetection: ChangeDetectionStrategy.OnPush,
    imports: [
    CommonModule,
    NgClass
]
})
export class IfpPrepAdvancePlaygroundComponent implements OnInit, OnChanges {


  @ViewChild('draggableElement') draggableElement!: ElementRef;
  @ViewChild('minmapRect') minmapRect!: ElementRef;
  @ViewChild('drawflowPlayGround', { static: true }) drawflowPlayGround!: ElementRef;
  @ViewChild('minimap', { static: true }) minimap!: ElementRef;
  @ViewChild('canvas', { static: true }) canvas!: ElementRef;
  @ViewChild('content') content!: ElementRef;

  @Output() toggleFullscreen: EventEmitter<boolean> = new EventEmitter<boolean>();
  @Output() selectNode: EventEmitter<{ tool: string, description: string }> = new EventEmitter<{ tool: string, description: string }>();
  @Output() connectionCreated: EventEmitter<ConnectionNode> = new EventEmitter<ConnectionNode>();
  @Output() connectionRemoved: EventEmitter<ConnectionNode> = new EventEmitter<ConnectionNode>();
  @Output() nodeCreated: EventEmitter<NodeData> = new EventEmitter<NodeData>();
  @Output() nodeRemoved: EventEmitter<NodeData> = new EventEmitter<NodeData>();
  @Output() nodeSelected: EventEmitter<NodeData> = new EventEmitter<NodeData>();
  @Output() nodeClickEnd: EventEmitter<NodeData | null> = new EventEmitter<NodeData | null>();
  @Output() emitRunMessage: EventEmitter<boolean> = new EventEmitter<boolean>();
  @Output() selectionRemoved: EventEmitter<boolean> = new EventEmitter<boolean>();


  @Input() removeSelection!: boolean;
  @Input() rightPanelWidth: number = 0;
  @Input() canvasTool!: CanvasTool;
  @Input() previewSpace: string | boolean | null = false;
  id: any = null;
  editor: any = null;
  transform: string = '';
  mobileItemSelec: string = '';
  mobilelastMove: any = null;

  public tools: Toolbar[] = advancedTools;
  private isDragging: boolean = false;
  private isDraggingMini: boolean = false;
  private offsetX: number = 0;
  private offsetY: number = 0;
  private offsetXMini: number = 0;
  private offsetYMini: number = 0;
  public isGridEnable: boolean | string | number = false;
  public toggleScreenEnable: boolean = false;
  public currentZoom: number | string = 1;
  private previousScrollPosition: number = 0;
  public selectedNode!: string;
  public previousZoom: number = 5;
  readonly advanceStore = inject(selectNodeStoreAdvance);
  public alltools: Tools[] = [];
  public selectedNodeData!: NodeData | null;
  public errorNode:WritableSignal<string>=signal('');
  private errorElement:any;
  private errorMessageElement:any;
  playGroundPositions: BoundryRect | null = null;



  constructor(private _elementRef: ElementRef, private _renderer: Renderer2, @Inject(DOCUMENT) private document: Document, private _cdr: ChangeDetectorRef,
    private _prepAdvanceService: IfpAdvancePrepService, private el: ElementRef, private renderer: Renderer2, private _translate: TranslateService) {
    this._prepAdvanceService.initWorkFlow.subscribe(resp => {
      if (resp) {
        this.callWorkflow();
      }
    });
  }


  ngOnInit(): void {
    this.id = this.drawflowPlayGround?.nativeElement;
    this.editor = new Drawflow(this.id);
    this.editor.force_first_input = true;
    this.editor.zoom_max = 10;
    this.registerEvents(this.editor);
    this.editor.reroute = true;
    this.editor.contextmenu = (e: any) => {
      this.editor.dispatch('contextmenu', e);
      let ePosX;
      let ePosY;
      if (e.type === 'touchstart') {
        ePosX = e.touches[0].clientX;
        ePosY = e.touches[0].clientY;
      } else {
        ePosX = e.clientX;
        ePosY = e.clientY;
      }
      e.preventDefault();
      if (this.editor.editor_mode === 'fixed' || this.editor.editor_mode === 'view') {
        return;
      }
      if (this.editor.precanvas.getElementsByClassName('drawflow-delete').length) {
        this.editor.precanvas.getElementsByClassName('drawflow-delete')[0].remove();
      }
      if (this.editor.node_selected || this.editor.connection_selected) {
        const deletebox = document.createElement('div');
        deletebox.classList.add('drawflow-delete');
        deletebox.innerHTML = 'x';
        if (this.editor.node_selected) {
          this.editor.node_selected.appendChild(deletebox);
        }
        if (this.editor.connection_selected) {
          deletebox.style.top = `${ePosY * ( this.editor.precanvas.clientHeight / (this.editor.precanvas.clientHeight * (+this.currentZoom))) - (this.editor.precanvas.getBoundingClientRect().y *  ( this.editor.precanvas.clientHeight / (this.editor.precanvas.clientHeight * (+this.currentZoom))) )}px`;
          deletebox.style.left = `${ePosX * ( this.editor.precanvas.clientWidth / (this.editor.precanvas.clientWidth * (+this.currentZoom))) - (this.editor.precanvas.getBoundingClientRect().x *  ( this.editor.precanvas.clientWidth / (this.editor.precanvas.clientWidth * (+this.currentZoom))) )}px`;
          this.editor.precanvas.appendChild(deletebox);
        }
      }
    };
    // this.editor.drawflow = this.drawflow();
    this.editor.start();
    this.alltools = [];
    if (this.tools?.length) {
      this.tools.forEach(element => {
        this.alltools.push(...element.menu);
      });
    }
    this.editor.container.addEventListener('touchstart', (e: any) => {
      if (this.editor.node_selected !== null || this.editor.connection_selected !== null) {
        this.editor.contextmenu(e);
      }
    });
  }

  ngOnChanges() {
    if (this.removeSelection) {
      this.resetAllActiveStles();
    }
    if (this.canvasTool.type != '') {
      this.setCanvasToolConfig();
    }
  }

  setCanvasToolConfig() {
    switch (this.canvasTool.type) {
    case canvasTools.grid:
      this.isGridEnable = this.canvasTool.value;
      break;
    default:
      this.zoom(this.canvasTool.value);
    }
  }



  positionMobile(ev: any) {
    this.mobilelastMove = ev;
  }

  allowDrop(ev: { preventDefault: () => void; }) {
    ev.preventDefault();
  }

  drag(ev: any) {
    if (ev.type === 'touchstart') {
      this.mobileItemSelec = ev.target
        .closest('.drag-drawflow')
        .getAttribute('data-node');
    } else {
      ev.dataTransfer.setData('node', ev.target.getAttribute('data-node'));
    }
  }

  drop(ev: any) {
    if (!ev) {
      return;
    }
    if (ev.type === 'touchend') {
      const parentdrawflow = document
        .elementFromPoint(
          this.mobilelastMove.touches[0]?.clientX,
          this.mobilelastMove.touches[0]?.clientY
        )
        ?.closest('#drawflow');
      if (parentdrawflow != null) {
        this.addNodeToDrawFlow(
          this.mobileItemSelec,
          this.mobilelastMove.touches[0]?.clientX,
          this.mobilelastMove.touches[0]?.clientY
        );
      }
      this.mobileItemSelec = '';
    } else {
      ev.preventDefault();
      const data = ev.dataTransfer.getData('node');
      this.addNodeToDrawFlow(data, ev.clientX, ev.clientY);
    }
  }

  addNodeToDrawFlow(name: string, pos_x: number, pos_y: number) {
    const keys = name.split('-');
    const objectId = uuidv4();
    const selectedNodeMainIndex = this.tools.findIndex(x => x.key == keys[1]);
    const selectedTool = this.tools[selectedNodeMainIndex];
    const selectedMenuIndex = selectedTool.menu.findIndex(x => x.key == keys[0]);
    const key = selectedTool.menu[selectedMenuIndex]?.key;
    const leftNodeCount = selectedTool.menu[selectedMenuIndex]?.leftNodeCount;
    const rightNodeCount = selectedTool.menu[selectedMenuIndex]?.rightNodeCount;
    const description = selectedTool.menu[selectedMenuIndex]?.description;
    pos_x = (pos_x * (this.editor.precanvas.clientWidth / (this.editor.precanvas.clientWidth * this.editor.zoom))) - (this.editor.precanvas.getBoundingClientRect().x * (this.editor.precanvas.clientWidth / (this.editor.precanvas.clientWidth * this.editor.zoom)));
    pos_y = (pos_y * (this.editor.precanvas.clientHeight / (this.editor.precanvas.clientHeight * this.editor.zoom))) - (this.editor.precanvas.getBoundingClientRect().y * (this.editor.precanvas.clientHeight / (this.editor.precanvas.clientHeight * this.editor.zoom)));
    this.editor?.addNode(
      key,
      leftNodeCount,
      rightNodeCount,
      pos_x,
      pos_y,
      selectedTool.menu[selectedMenuIndex]?.key,
      {
        objectId: objectId,
        type: selectedTool.menu[selectedMenuIndex].type,
        leftNodeCount: leftNodeCount,
        rightNodeCount: rightNodeCount,
        description: description,
        pos_x: pos_x,
        pos_y: pos_y,
        selectedNodeMainIndex: selectedNodeMainIndex
      },
      this.getTemplate(selectedTool.menu[selectedMenuIndex], selectedNodeMainIndex)
    );
    if (selectedTool.menu[selectedMenuIndex].anchorTexts?.length) {
      this.getAnchorText(key, selectedNodeMainIndex, selectedMenuIndex);
    }
    if (selectedTool.menu[selectedMenuIndex].anchorInputTexts?.length) {
      this.getInputAnchorText(key, selectedNodeMainIndex, selectedMenuIndex);
    }
  }

  getAnchorText(key: string, pIndex: number, mIndex: number) {
    const elements = this._elementRef.nativeElement.querySelectorAll(`.${key}`);
    elements.forEach((elem: { children: { children: Iterable<unknown> | ArrayLike<unknown>; }[]; }) => {
      if (elem.children[2].children) {
        Array.from(elem.children[2].children).forEach((element: any, index: number) => {
          if (this.tools[pIndex]?.menu[mIndex]?.anchorTexts?.[index]) {
            const textValue = this.tools[pIndex].menu[mIndex].anchorTexts?.[index] ?? '';
            this.renderer.setAttribute(element, 'data-anchor', this._translate.instant(textValue));
          }
        });
      }
    });

  }

  getInputAnchorText(key: string, pIndex: number, mIndex: number) {
    const elements = this._elementRef.nativeElement.querySelectorAll(`.${key}`);
    elements.forEach((elem: { children: { children: Iterable<unknown> | ArrayLike<unknown>; }[]; }) => {
      if (elem.children[0].children) {
        Array.from(elem.children[0].children).forEach((element: any, index: number) => {
          if (this.tools[pIndex]?.menu[mIndex]?.anchorInputTexts?.[index]) {
            const textValue = this.tools[pIndex].menu[mIndex].anchorInputTexts?.[index] ?? '';
            this.renderer.setAttribute(element, 'data-anchor', this._translate.instant(textValue));
          }
        });
      }
    });

  }

  showpopup(e: { target: { closest: (arg0: string) => { (): any; new(): any; style: { (): any; new(): any; zIndex: string; }; }; children: { style: { display: string; }; }[]; }; }) {
    e.target.closest('.drawflow-node').style.zIndex = '9999';
    e.target.children[0].style.display = 'block';
    this.transform = this.editor.precanvas.style.transform;
    this.editor.precanvas.style.transform = '';
    this.editor.precanvas.style.left = `${this.editor.canvas_x}px`;
    this.editor.precanvas.style.top = `${this.editor.canvas_y}px`;
    this.editor.editor_mode = 'fixed';
  }

  closemodal(e: { target: { closest: (arg0: string) => { (): any; new(): any; style: { (): any; new(): any; zIndex: string; }; }; parentElement: { parentElement: { style: { display: string; }; }; }; }; }) {
    e.target.closest('.drawflow-node').style.zIndex = '2';
    e.target.parentElement.parentElement.style.display = 'none';
    this.editor.precanvas.style.transform = this.transform;
    this.editor.precanvas.style.left = '0px';
    this.editor.precanvas.style.top = '0px';
    this.editor.editor_mode = 'edit';
  }

  registerEvents(editor: any): void {
    let objectId: string = '';
    editor.on('click', (data: any) => {
      let selectedNodeMainId: any;
      this.selectedNodeData = null;
      if (data.target.offsetParent) {
        selectedNodeMainId = data.target.offsetParent.id.split('-')[1];
      }
      const target = data.target as HTMLElement;
      const classList = target.classList;
      let selectedAnchorArr: string[] = [];
      if ((classList.contains('output') || classList.contains('input')) && classList.length === 2) {
        selectedAnchorArr = classList[1].split('_');
      }
      const targetNodes: any = target.getAttribute('data-node');
      if (targetNodes) {
        const keys = targetNodes.split('-');
        const selectedItem = this.tools[keys[1]]?.menu.find(x => x.key == keys[0]);
        if (selectedItem && selectedItem != null) {
          this.resetAllActiveStles();
          // ** add active styles //
          this.selectNode.emit({ tool: keys[0], description: selectedItem.description });
          this.selectedNode = keys[0];
          this.removeSelection = false;
          const element = !data.target.classList.contains(`ifp-p-adv__${selectedItem.key}`) ? data.target.parentElement : data.target;
          this._renderer.addClass(element, 'ifp-p-adv__tool--active');
          this._renderer.setStyle(element, 'background-color', selectedItem?.color);
          const nodeData = this.getNodeDatas(editor?.getNodeFromId(selectedNodeMainId));
          this._renderer.addClass(element.parentElement.parentElement, 'ifp-p-adv__node--active');
          this.selectedNodeData = nodeData;
          this.clearNodeError(nodeData.objectId);
          if (nodeData) {
            this.nodeSelected.emit(nodeData);
          }
        }
      }
      if (selectedNodeMainId) {
        this.triggerPreview(editor, selectedNodeMainId, targetNodes, selectedAnchorArr);
      }
    });
    editor.on('connectionCreated', (nodeDetail: Connection) => {
      this.restrictConnection(nodeDetail, editor);
      const sourceNode = this.getNodeDatas(editor.getNodeFromId(nodeDetail.output_id));
      const targetNode = this.getNodeDatas(editor.getNodeFromId(nodeDetail.input_id));
      const sourceAnchor = +nodeDetail.output_class.split('_')[1];
      const destinationAnchor = +nodeDetail.input_class.split('_')[1];
      this.connectionCreated.emit({ inputNode: sourceNode, outputNode: targetNode, sourceAnchor, destinationAnchor, sourceOutputCount: sourceNode.rightNodeCount, destinationInputCount: targetNode.leftNodeCount });
    });
    editor.on('connectionRemoved', (nodeDetail: Connection) => {
      const sourceNode = this.getNodeDatas(editor.getNodeFromId(nodeDetail.input_id));
      const targetNode = this.getNodeDatas(editor.getNodeFromId(nodeDetail.output_id));
      this.advanceStore.removeConnection(targetNode.objectId, sourceNode.objectId, 'Remove connection');
      const sourceAnchor = +nodeDetail.output_class.split('_')[1];
      const destinationAnchor = +nodeDetail.input_class.split('_')[1];
      this.connectionRemoved.emit({ inputNode: sourceNode, outputNode: targetNode, sourceAnchor, destinationAnchor, sourceOutputCount: sourceNode.rightNodeCount, destinationInputCount: targetNode.leftNodeCount });
      this._prepAdvanceService.nodeChangeDetect.next('node removed');
    });
    editor.on('nodeCreated', (id: string) => {
      const nodeData = this.getNodeDatas(editor.getNodeFromId(id));
      const toolValue = this._prepAdvanceService.getDefualtToolConfig(nodeData.type);
      if (nodeData.type == connectionType.outputTool) {
        toolValue.config = {
          is_download: false,
          name: `Untitled_${nodeData.objectId}`,
          type: 'csv'
        };
      }
      if (nodeData.type == connectionType.inputTool) {
        if (!this._prepAdvanceService.availablePreviewNodes) {
          this._prepAdvanceService.availablePreviewNodes = {};
        }
        this._prepAdvanceService.availablePreviewNodes[nodeData.objectId] = {
          run: true
        };
      }
      this.advanceStore.addNode(nodeData.objectId, nodeData.name, toolValue.type, toolValue.config, '', { pos_x: nodeData.pos_x, pos_y: nodeData.pos_y, selectedNodeMainIndex: nodeData.selectedNodeMainIndex, nodeId: id});
      this.nodeCreated.emit(nodeData);
      this._prepAdvanceService.nodeChangeDetect.next('node added');
      if (this.advanceStore.data().nodes?.length === 1) {
        this.setActiveNode(id);
      }
    });
    editor.on('nodeSelected', (id: string) => {
      const nodeData = this.getNodeDatas(editor.getNodeFromId(id));
      this.selectedNodeData = nodeData;
      objectId = nodeData.objectId;
    });
    editor.on('nodeRemoved', () => {
      this.advanceStore.removeNode(objectId, 'removed');
      objectId = '';
      this._prepAdvanceService.currentNodeId.set(null);
      this._prepAdvanceService.nodeChangeDetect.next('removed');
      this.selectedNodeData = null;
      this._prepAdvanceService.clearPreviewData();
      this._prepAdvanceService.showPreview.set(false);
      this.nodeRemoved.emit();
    });
    editor.on('mouseUp', (_event: MouseEvent) => {
      if (this.selectedNodeData) {
        this.nodeClickEnd.emit(this.selectedNodeData);
      }
    });
    editor.on('nodeMoved', (id: string) => {
      const elm = this.document.getElementById(`node-${id}`);
      const nodeData = this.getNodeDatas(editor.getNodeFromId(id));
      const nodeStateData = cloneDeep(this.advanceStore.selectNodeValue().nodeValue(nodeData.objectId ?? ''));
      nodeStateData.settings.pos_x = elm?.offsetLeft;
      nodeStateData.settings.pos_y = elm?.offsetTop;
      this.advanceStore.updateNode(nodeStateData.object_id, nodeStateData.name, nodeStateData.type, nodeStateData.configuration, '', nodeStateData.settings);
    });
  }


  setActiveNode(id: string) {
    const nodeElement = document.getElementById(`node-${id}`);
    if (nodeElement) {
      this._renderer.addClass(nodeElement, 'ifp-p-adv__node--active');
      const toolKey = this.advanceStore.data().nodes?.[0]?.name;
      const selectedTool = this.tools.find(tool => tool.menu.some(menuItem => menuItem?.key === toolKey)
      );
      if (selectedTool) {
        const filterColor = selectedTool.menu.find(menuItem => menuItem?.key === toolKey)?.color;
        const innerContentElement = nodeElement.querySelector('.ifp-p-adv__tool');
        if (innerContentElement) {
          this._renderer.setStyle(innerContentElement, 'background-color', filterColor);
          this._renderer.addClass(innerContentElement, 'ifp-p-adv__tool--active');
        }
      }
    }
  }

  restrictConnection(nodeDetail: Connection, editor: any) {
    const nodeInfo = editor.getNodeFromId(nodeDetail.input_id);
    if (nodeInfo.inputs[nodeDetail.input_class].connections.length > 1) {
      const removeConnectionInfo = nodeInfo.inputs[nodeDetail.input_class].connections[1];
      editor.removeSingleConnection(removeConnectionInfo.node, nodeDetail.input_id, removeConnectionInfo.input, nodeDetail.input_class);
    }
  }

  triggerPreview(editor: any, selectedNodeMainId: number, targetNodes: any, selectedAnchorArr: string[]) {
    const nodeData = this.getNodeDatas(editor.getNodeFromId(selectedNodeMainId));
    let nodeId = nodeData.objectId;
    const nodeConfigData = this.advanceStore.selectNodeValue().nodeValue(nodeId ?? '');
    if (nodeConfigData.type == connectionType.outputTool) {
      selectedAnchorArr = [nodeAnchors.input, '1'];
    } else if (nodeConfigData.type == connectionType.inputTool) {
      selectedAnchorArr = [nodeAnchors.output, '1'];
    }
    const connection = nodeConfigData?.configuration?.connection?.connection_type;
    const isSource = toolName.source == targetNodes?.split('-')[0] && connection == 'dataset' ? true : false;
    // if (!configData?.settings?.configUpdated && !isSource) {
    //   return;
    // }
    let sourceId: string = '';
    if (isSource) {
      const nodeConfig = this.advanceStore.selectNodeValue().nodeValue(nodeData.objectId);
      sourceId = nodeConfig.configuration.connection.path;
      if (!sourceId ) {
        this._prepAdvanceService.isNoRecordFound.set(true);
        this._prepAdvanceService.source.set(true);
        this._prepAdvanceService.clearPreviewData();
        return;
      }
    } else {
      this._prepAdvanceService.source.set(false);
    }
    if (!isSource && connection != 'ifp') {
      const currentConnectionCount: number = this.advanceStore.selectNodeDestinationConnection().destinationConnections(nodeData.objectId).length;
      if (currentConnectionCount <= 0) {
        if (nodeData.type != connectionType.inputTool) {
          this._prepAdvanceService.clearPreviewData();
        }
        return;
      }
    }
    let selectedAnchor = selectedAnchorArr?.length ? parseInt(selectedAnchorArr[1]) : (nodeData.type == connectionType.joinTool ? 2 : 1);
    if (selectedAnchorArr?.length && selectedAnchorArr[0] == nodeAnchors.input) {
      const sourceNodes: any = this.advanceStore.selectNodeDestinationConnection().destinationConnections(nodeId);
      const anchorIndex = sourceNodes.findIndex((x: { destination_anchor: string; }) => x.destination_anchor == selectedAnchorArr[1]);
      if (anchorIndex >= 0) {
        nodeId = sourceNodes[anchorIndex].source;
        selectedAnchor = sourceNodes[anchorIndex].source_anchor;
      }
    }
    this._prepAdvanceService.showPreview.set(true);
    this._prepAdvanceService.currentNodeAndAnchor.next({ nodeId: nodeId, anchor: selectedAnchor, source: isSource, sourceId: sourceId });
  }

  getNodeDatas(node: any) {
    const nodeData = {
      objectId: node.data.objectId,
      type: node.data.type,
      name: node.name,
      description: node.data.description,
      leftNodeCount: node.data.leftNodeCount,
      rightNodeCount: node.data.rightNodeCount,
      pos_y: node.data.pos_y,
      pos_x: node.data.pos_x,
      selectedNodeMainIndex: node.data.selectedNodeMainIndex
    };
    return nodeData;
  }


  resetAllActiveStles() {
    const toolDvElements = this._elementRef.nativeElement.querySelectorAll('.ifp-p-adv__tool');

    toolDvElements.forEach((element: HTMLElement) => {
      this._renderer.removeClass(element, 'ifp-p-adv__tool--active');
      this._renderer.removeClass(element.parentElement?.parentElement, 'ifp-p-adv__tool--active');
      this._renderer.setStyle(element, 'background-color', '#fff');
    });
    this.selectionRemoved.emit(true);
    this._cdr.detectChanges();
  }


  // ** for get nodes //

  getTemplate(tool: Tools, index: number) {
    let dynamicHtml = '';
    if (tool) {
      dynamicHtml = `<div  class="ifp-p-adv__tool ifp-p-adv__${tool.key}"  data-node="${tool.key}-${index}">
    <em class="ifp-icon ${tool.icon}" style="color:${tool?.color}" data-node="${tool.key}-${index}"></em>
    <p class="ifp-p-adv__icon-txt" data-node="${tool.key}-${index}">${this._translate.instant(tool.key)}</p>
    <p class="ifp-p-adv__tool-error"></p>
     </div>`;
    }
    return dynamicHtml;
  }


  onMouseDown(event: MouseEvent | any) {
    if (event?.target?.['id'] !== 'zoom') {
      this.isDragging = true;
    }
    this.offsetX = event.clientX;
    this.offsetY = event.clientY;
  }

  onMouseUp() {
    this.isDragging = false;
  }



  onMouseMove(event: MouseEvent) {
    if (this.isDragging) {
      if (this.canvas.nativeElement) {
        this.playGroundPositions = this.canvas.nativeElement.getBoundingClientRect();
      }
      let toolX: number = 0;
      let toolY: number = 0;
      const toolBoundry: BoundryRect = this.draggableElement.nativeElement.getBoundingClientRect();
      if (this.playGroundPositions?.left && this.playGroundPositions?.right) {
        if (toolBoundry.x <= (this.playGroundPositions.left + 20) && event.clientX <= (this.playGroundPositions.left + 20)) {
          toolX = this.playGroundPositions.left;
        }
        if (toolBoundry.x >= (((this.playGroundPositions.right - toolBoundry.width) - this.rightPanelWidth) - 20) && event.clientX >= (((this.playGroundPositions.right - toolBoundry.width) - this.rightPanelWidth) - 20)) {
          toolX = (this.playGroundPositions.right - toolBoundry.width) - this.rightPanelWidth;
        }
      }
      if (this.playGroundPositions?.top && this.playGroundPositions?.bottom) {
        const headerElement = this.renderer.selectRootElement('.ifp-header', true);
        const playGroundTopHeight: number = this.playGroundPositions.top;
        if (toolBoundry.y <= playGroundTopHeight && event.clientY <= playGroundTopHeight) {
          toolY = playGroundTopHeight;
        }

        const playGroundHeight: number = ((this.playGroundPositions.height - (!this.toggleScreenEnable ? headerElement.offsetHeight : 0)) - (toolBoundry.height / 2));
        if (toolBoundry.y >= playGroundHeight || event.clientY >= playGroundHeight) {
          toolY = playGroundHeight;
        }
      }
      toolX = toolX && toolX != 0 ? toolX : event.clientX;
      toolY = toolY && toolY != 0 ? toolY : event.clientY;
      const toolbar = this.draggableElement.nativeElement as HTMLElement;
      toolbar.style.left = `${toolX}px`;
      toolbar.style.top = `${toolY}px`;
      // this.offsetX = toolX;
      // this.offsetY = event.clientY;
    }
  }


  toggleScreen() {
    this.toggleScreenEnable = !this.toggleScreenEnable;
    this.toggleFullscreen.emit(true);
    if (!this.toggleScreenEnable) {
      // Reset position after exiting fullscreen
      this._renderer.setStyle(this.draggableElement.nativeElement, 'top', '500px');
      this._renderer.setStyle(this.draggableElement.nativeElement, 'left', '1000px');
    }
    // if (this.canvas.nativeElement) {
    //   this.playGroundPositions = this.canvas.nativeElement.getBoundingClientRect();
    // }
  }

  preventDrag(event: MouseEvent) {
    event.stopPropagation();
  }


  zoom(zoomValue: any) {
    // this.editor.zoom_reset();
    this.currentZoom = parseInt(zoomValue) / 10;
    // this.editor.zoom_value = Math.abs(event.target.value) / 10;
    if (zoomValue > this.previousZoom) {
      this.editor.zoom_in();
    } else {
      this.editor.zoom_out();
    }
    this.previousZoom = zoomValue;
    this._cdr.detectChanges();
  }

  reRouteConnection() {
    this.editor.reroute_fix_curvature = true;
  }

  openMinimap() {
    const clonedDiv = this.id.cloneNode(true);
    this.minimap.nativeElement.appendChild(clonedDiv);
  }

  onMiniMouseDown(event: MouseEvent) {
    this.isDraggingMini = true;
    this.offsetXMini = event.clientX;
    this.offsetYMini = event.clientY;
  }

  onMiniMouseUp() {
    this.isDraggingMini = false;
  }

  onMiniMouseMove(event: MouseEvent) {
    if (this.isDraggingMini) {
      const dx = event.clientX - this.offsetXMini;
      const dy = event.clientY - this.offsetYMini;
      const toolbar = this.minmapRect.nativeElement as HTMLElement;
      const parentDiv = toolbar.parentElement as HTMLElement | null;

      if (parentDiv) {
        const newLeft = Math.min(Math.max(toolbar.offsetLeft + dx, 0), parentDiv.clientWidth - toolbar.offsetWidth);
        const newTop = Math.min(Math.max(toolbar.offsetTop + dy, 0), parentDiv.clientHeight - toolbar.offsetHeight);
        toolbar.style.left = `${newLeft}px`;
        toolbar.style.top = `${newTop}px`;
      }
      this.offsetXMini = event.clientX;
      this.offsetYMini = event.clientY;


      // ** calculation //
      const scroledPercentage = (this.minmapRect.nativeElement.offsetLeft / this.minimap.nativeElement.offsetWidth) * 100;
      const bgScrollPixel = (scroledPercentage / 100) * (this.drawflowPlayGround.nativeElement.offsetWidth * 4);
      this.drawflowPlayGround.nativeElement.children[0].style.transform = `translate(-${bgScrollPixel}px, 0px)`;
      this._cdr.detectChanges();
    }
  }

  callWorkflow() {
    const config = this.advanceStore.selectNodeStoreAdvance().data();
    const dynamicObject: any = {};
    if (config?.nodes?.length) {
      config.nodes.forEach((element: any, index: number) => {
        const toolIndex = this.alltools.findIndex(x => x.type == element.type);
        dynamicObject[index + 1] = {
          id: index + 1,
          name: this.alltools[toolIndex].key,
          data: {
            objectId: element.object_id,
            type: element.type,
            leftNodeCount: this.alltools[toolIndex].leftNodeCount,
            rightNodeCount: this.alltools[toolIndex].rightNodeCount,
            description: this.alltools[toolIndex].description
          },
          class: this.alltools[toolIndex].key,
          typenode: false,
          html: this.getTemplate(this.alltools[toolIndex], element.settings.selectedNodeMainIndex),
          inputs: this.getInputs(element.object_id, this.alltools[toolIndex].leftNodeCount),
          outputs: this.getOutputs(element.object_id, this.alltools[toolIndex].rightNodeCount),
          pos_x: element.settings.pos_x,
          pos_y: element.settings.pos_y
        };
      });
    }
    const workFlowImport = {
      'drawflow': {
        'Home': {
          'data': dynamicObject
        }
      }
    };
    this.editor?.clearModuleSelected();
    setTimeout(() => {
      this.editor.import(workFlowImport);
    }, 300);

  }


  getInputs(objectId: string, nodeCount: number = 0) {
    const config = this.advanceStore.selectNodeStoreAdvance().data();
    const inputConnections = this.advanceStore.selectNodeStoreAdvance().data().connections.filter(x => x.destination == objectId);
    const inputs: any = Array.from({ length: nodeCount }, (_, i) => ({ [`input_${i + 1}`]: { connections: [] } }))
      .reduce((acc, curr) => ({ ...acc, ...curr }), {});
    inputConnections?.forEach(({ destination_anchor, source, source_anchor }) => {
      inputs[`input_${destination_anchor}`] = {
        connections: [
          {
            node: (config.nodes.findIndex(x => x.object_id == source) + 1).toString(),
            input: `output_${source_anchor}`
          }
        ]
      };
    });

    return inputs;
  }

  getOutputs(objectId: string, nodeCount: number = 0) {
    const config = this.advanceStore.selectNodeStoreAdvance().data();
    const outputConnections = this.advanceStore.selectNodeStoreAdvance().data().connections.filter(x => x.source == objectId);
    const outputs: any = Array.from({ length: nodeCount }, (_, i) => ({ [`output_${i + 1}`]: { connections: [] } }))
      .reduce((acc, curr) => ({ ...acc, ...curr }), {});

    outputConnections?.forEach(({ source_anchor, destination, destination_anchor }) => {
      outputs[`output_${source_anchor}`] = {
        connections: [
          {
            node: (config.nodes.findIndex(x => x.object_id == destination) + 1).toString(),
            input: `input_${destination_anchor}`
          }
        ]
      };
    });

    return outputs;
  }

  setHeight(height: any) {
    this.content.nativeElement.style.height = `${height}px`;
    this._cdr.detectChanges();
  }

  showNodeError(nodeId:string, message:string){
    this.errorNode.set(nodeId);
    const nodeData = this.advanceStore.selectNodeValue().nodeValue(nodeId ?? '');
    this.errorElement= this.document.getElementById(`node-${nodeData.settings.nodeId}`);
    this.errorMessageElement = this.errorElement?.children[1].children[0].children[2];
    this._renderer.setAttribute(this.errorMessageElement, 'data-error', message);
    this._renderer.addClass(this.errorElement, 'ifp-p-adv__node--error');
  }

  clearNodeError(nodeId:string){
    if(this.errorNode() == nodeId){
      this._renderer.setAttribute(this.errorMessageElement, 'data-error', '');
      this._renderer.removeClass(this.errorElement, 'ifp-p-adv__node--error');
      this.errorNode.set('');
    }
  }

}

interface Connection {
  output_id: string;
  input_id: string;
  output_class: string;
  input_class: string;
}

export interface ConnectionNode {
  inputNode: NodeData;
  outputNode: NodeData;
  sourceAnchor: number;
  destinationAnchor: number;
  sourceOutputCount: number;
  destinationInputCount: number;
}

export interface BoundryRect {
  bottom: number,
  height: number,
  left: number,
  right: number,
  top: number,
  width: number,
  x: number,
  y: number
}
