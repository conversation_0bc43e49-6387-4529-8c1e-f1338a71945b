<div class="ifp-db-view" [ngClass]="{'ifp-db-view--preview': mode === 'preview'}">
  <div class="ifp-container">
    <div class="ifp-db-view__head">
      @if (mode === 'detail') {
      <span (click)="location.back()" class="ifp-link ifp-db__header-back" [title]="'Back' | translate"><em
          class="ifp-icon ifp-icon-left-arrow"></em> {{'Back' | translate}}</span>
      }
      @if (mode === 'preview') {
      <div class="ifp-db-view__message">
        <p class="ifp-db-view__message-text">{{message | translate}}</p>
        <div class="ifp-db-view__message-btn-sec">
          <ifp-button class="ifp-db-view__message-btn" [label]="'Yes'" (ifpClick)="onMessageResponse(true)"
            [buttonClass]="buttonClass.primary"></ifp-button>
          <ifp-button class="ifp-db-view__message-btn" [label]="'No'" (ifpClick)="onMessageResponse(false)"
            [buttonClass]="buttonClass.secondary" [buttonColor]="buttonColor.blue"></ifp-button>
        </div>
      </div>
      }
      <div class="ifp-db-view__header-right">
        <div class="ifp-db-view__btn-round">
          <em class="ifp-icon ifp-icon-download-line"></em>
          <span class="ifp-db-view__btn-round-text">{{'Export' | translate}}</span>
        </div>
      </div>
    </div>
    <span class="ifp-db-view__tag">{{mode | translate}}</span>
    <div class="ifp-db-view__body">
      <!-- <img src="../../../../assets/images/dashboard-builder/preivew-dummy.png" alt="" width="100%"> -->
      <!-- <ng-content select=".ifp-db"></ng-content> -->
      <app-ifp-dashboard-builder ></app-ifp-dashboard-builder>
    </div>
  </div>
</div>
