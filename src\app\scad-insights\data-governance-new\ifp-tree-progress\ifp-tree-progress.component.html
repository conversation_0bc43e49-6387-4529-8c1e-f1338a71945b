<div class="ifp-root" [ngClass]="{'ifp-root--vertical': isVertical()}">
  <div class="ifp-root__progress-wrapper">

    @if (isVertical()) {
      @for (data of progressData(); track $index) {
        <div class="ifp-root__progress" [ngStyle]="{'width': (isVertical() ? '100%': (data.percent)+'%'), 'height': (isVertical() ? (data.percent)+'%' : 'auto'), 'background-color': data.color}"></div>
      }
    } @else {
    <svg class="ifp-root__progress-ring" [attr.height]="size()" #circle>
      @for (data of progressData(); track $index) {
        <circle class="ifp-root__progress-circle"
        fill="transparent"
        [attr.r]="size()/2"
        [attr.cx]="size()/2"
        [attr.cy]="size()/2"
        [ngStyle]="{'stroke': data.color, 'strokeDasharray': circumference}"
        />
      }
    </svg>
    }
  </div>
  <div class="ifp-root__outer">
    <div class="ifp-root__inner">
      <div class="ifp-root__value-wrapper">
        <p class="ifp-root__title">{{title() | translate}}</p>
        <p class="ifp-root__value">{{value() | number}}</p>
      </div>
    </div>
  </div>
</div>
