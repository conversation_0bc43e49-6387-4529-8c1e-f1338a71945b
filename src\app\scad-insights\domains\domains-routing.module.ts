import { NgModule, inject } from '@angular/core';
import { ActivatedRouteSnapshot, RouterModule, RouterStateSnapshot, Routes } from '@angular/router';

import { HomeModule } from '../home/<USER>';
import { AuthGuardService } from '../core/guard/auth.guard';
import { DomainDetailsNewComponent } from './domain-details-new/domain-details-new.component';

const routes: Routes = [
  { path: '', component: HomeModule, canActivate: [(route: ActivatedRouteSnapshot, state: RouterStateSnapshot) => inject(AuthGuardService).canActivate(route, state)]},
  { path: ':domain/:id', component: DomainDetailsNewComponent, canActivate: [(route: ActivatedRouteSnapshot, state: RouterStateSnapshot) => inject(AuthGuardService).canActivate(route, state)] }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class DomainsRoutingModule { }
