import { NgModule, inject } from '@angular/core';
import { ActivatedRouteSnapshot, Router, RouterModule, RouterStateSnapshot, Routes } from '@angular/router';
import { IfpPagesComponent } from 'src/app/layout/ifp-pages/ifp-pages.component';
import { AuthGuardService } from 'src/app/scad-insights/core/guard/auth.guard';
import { IfpPrepService } from './data-prep/ifp-data-prep/ifp-prep-service';
import { Store } from '@ngrx/store';
import { loadCreateNodeClear, loadNode } from './data-prep/ifp-data-prep/store/node-store/node-store.actions';
import { IfpExploratoryService } from './ifp-exploratory/services/ifp-exploratory.service';
import { slaService } from '../scad-insights/core/services/sla/sla.service';
import { permission30 } from '../scad-insights/core/services/sla/permission.contant';
export const ifpAnalyticsroutes: Routes = [{
  path: '', component: IfpPagesComponent,
  providers: [IfpPrepService],
  canDeactivate: [() => {
    inject(IfpPrepService).clearAllValues();
  }, () => {
    inject(Store).dispatch(loadNode[loadCreateNodeClear]({}));
  }],
  children: [
    {
      path: 'analytics/data-preparation',
      loadComponent: () => import('src/app/ifp-analytics/data-prep/ifp-data-prep/ifp-data-prep.component').then((m: typeof import('src/app/ifp-analytics/data-prep/ifp-data-prep/ifp-data-prep.component')) => m.IfpDataPrepComponent),
      canActivate: [(route: ActivatedRouteSnapshot, state: RouterStateSnapshot) => inject(AuthGuardService).canActivate(route, state)],
      resolve: [() => inject(slaService).checkPermission(permission30['dataPrepBasic'])],
      children: [
        {
          path: '',
          redirectTo: 'upload-data',
          pathMatch: 'full'
        },
        {
          path: 'upload-data',
          loadComponent: () => import('src/app/ifp-analytics/data-prep/ifp-data-prep/ifp-data-prep-upload/ifp-data-prep-upload.component').then((m: typeof import('src/app/ifp-analytics/data-prep/ifp-data-prep/ifp-data-prep-upload/ifp-data-prep-upload.component')) => m.IfpDataPrepUploadComponent),
          canActivate: [(route: ActivatedRouteSnapshot, state: RouterStateSnapshot) => inject(AuthGuardService).canActivate(route, state)],
          resolve: [() => inject(slaService).checkPermission(permission30['dataPrepBasic'])]
        },
        {
          path: 'select-data',
          loadComponent: () => import('src/app/ifp-analytics/data-prep/ifp-data-prep/ifp-prep-select-data/ifp-prep-select-data.component').then((m: typeof import('src/app/ifp-analytics/data-prep/ifp-data-prep/ifp-prep-select-data/ifp-prep-select-data.component')) => m.IfpPrepSelectDataComponent),
          canActivate: [(route: ActivatedRouteSnapshot, state: RouterStateSnapshot) => inject(AuthGuardService).canActivate(route, state)],
          resolve: [() => inject(slaService).checkPermission(permission30['dataPrepBasic'])]
        },
        {
          path: 'cleaning-data',
          loadComponent: () => import('src/app/ifp-analytics/data-prep/ifp-data-prep/ifp-cleaning/ifp-cleaning.component').then((m: typeof import('src/app/ifp-analytics/data-prep/ifp-data-prep/ifp-cleaning/ifp-cleaning.component')) => m.IfpCleaningComponent),
          canActivate: [(route: ActivatedRouteSnapshot, state: RouterStateSnapshot) => inject(AuthGuardService).canActivate(route, state)],
          resolve: [() => inject(slaService).checkPermission(permission30['dataPrepBasic'])]
        },
        {
          path: 'append-data',
          loadComponent: () => import('src/app/ifp-analytics/data-prep/ifp-data-prep/ifp-data-prep-append/ifp-data-prep-append.component').then((m: typeof import('src/app/ifp-analytics/data-prep/ifp-data-prep/ifp-data-prep-append/ifp-data-prep-append.component')) => m.IfpDataPrepAppendComponent),
          canActivate: [(route: ActivatedRouteSnapshot, state: RouterStateSnapshot) => inject(AuthGuardService).canActivate(route, state)],
          resolve: [() => inject(slaService).checkPermission(permission30['dataPrepBasic'])]
        },
        {
          path: 'add-column',
          loadComponent: () => import('src/app/ifp-analytics/data-prep/ifp-data-prep/ifp-data-prep-add-column/ifp-data-prep-add-column.component').then((m: typeof import('src/app/ifp-analytics/data-prep/ifp-data-prep/ifp-data-prep-add-column/ifp-data-prep-add-column.component')) => m.IfpDataPrepAddColumnComponent),
          canActivate: [(route: ActivatedRouteSnapshot, state: RouterStateSnapshot) => inject(AuthGuardService).canActivate(route, state)],
          resolve: [() => inject(slaService).checkPermission(permission30['dataPrepBasic'])]
        },
        {
          path: 'save',
          loadComponent: () => import('src/app/ifp-analytics/data-prep/ifp-data-prep/ifp-data-prep-save/ifp-data-prep-save.component').then((m: typeof import('src/app/ifp-analytics/data-prep/ifp-data-prep/ifp-data-prep-save/ifp-data-prep-save.component')) => m.IfpDataPrepSaveComponent),
          canActivate: [(route: ActivatedRouteSnapshot, state: RouterStateSnapshot) => inject(AuthGuardService).canActivate(route, state)],
          resolve: [() => inject(slaService).checkPermission(permission30['dataPrepBasic'])]
        }
      ]
    },
    {
      path: 'analytics/prep-library',
      loadComponent: () => import('src/app/ifp-analytics/data-prep/ifp-data-prep/ifp-prep-library/ifp-prep-library.component').then((m: typeof import('src/app/ifp-analytics/data-prep/ifp-data-prep/ifp-prep-library/ifp-prep-library.component')) => m.IfpPrepLibraryComponent),
      canActivate: [(route: ActivatedRouteSnapshot, state: RouterStateSnapshot) => inject(AuthGuardService).canActivate(route, state)],
      resolve: [() => inject(slaService).checkPermission(permission30['dataPrepBasic'])]
    },
    {
      path: 'analytics/advance',
      // providers: [IfpAdvancePrepService],
      loadComponent: () => import('src/app/ifp-analytics/data-prep/ifp-advance-prep/ifp-advance-prep.component').then((m: typeof import('src/app/ifp-analytics/data-prep/ifp-advance-prep/ifp-advance-prep.component')) => m.IfpAdvancePrepComponent),
      canActivate: [(route: ActivatedRouteSnapshot, state: RouterStateSnapshot) => inject(AuthGuardService).canActivate(route, state)],
      resolve: [() => inject(slaService).checkPermission(permission30['dataPrepAdv'])]
    },
    {
      path: 'analytics/exploratory',
      loadComponent: () => import('src/app/ifp-analytics/ifp-exploratory/ifp-exploratory.component').then((m: typeof import('src/app/ifp-analytics/ifp-exploratory/ifp-exploratory.component')) => m.IfpExploratoryComponent),
      canActivate: [(route: ActivatedRouteSnapshot, state: RouterStateSnapshot) => inject(AuthGuardService).canActivate(route, state)],
      resolve: [() => inject(slaService).checkPermission(permission30['dataExploration'])],
      providers: [IfpExploratoryService],
      children: [
        {
          path: '',
          redirectTo: 'upload-data',
          pathMatch: 'full'
        },
        {
          path: 'upload-data',
          loadComponent: () => import('src/app/ifp-analytics/ifp-exploratory/upload-exploratory/upload-exploratory.component').then((m: typeof import('src/app/ifp-analytics/ifp-exploratory/upload-exploratory/upload-exploratory.component')) => m.UploadExploratoryComponent),
          canActivate: [(route: ActivatedRouteSnapshot, state: RouterStateSnapshot) => inject(AuthGuardService).canActivate(route, state)],
          resolve: [() => inject(slaService).checkPermission(permission30['dataExploration'])]
        },
        {
          path: 'select',
          loadComponent: () => import('src/app/ifp-analytics/ifp-exploratory/ifp-exploratory-select-page/ifp-exploratory-select-page.component').then((m: typeof import('src/app/ifp-analytics/ifp-exploratory/ifp-exploratory-select-page/ifp-exploratory-select-page.component')) => m.IfpExploratorySelectPageComponent),
          canActivate: [(route: ActivatedRouteSnapshot, state: RouterStateSnapshot) => inject(AuthGuardService).canActivate(route, state)],
          resolve: [() => inject(slaService).checkPermission(permission30['dataExploration'])]
        },
        {
          path: 'data-analysis',
          loadComponent: () => import('src/app/ifp-analytics/ifp-exploratory/ifp-exploratory-data-analysis/ifp-exploratory-data-analysis.component').then((m: typeof import('src/app/ifp-analytics/ifp-exploratory/ifp-exploratory-data-analysis/ifp-exploratory-data-analysis.component')) => m.IfpExploratoryDataAnalysisComponent),
          canActivate: [(route: ActivatedRouteSnapshot, state: RouterStateSnapshot) => inject(AuthGuardService).canActivate(route, state)],
          resolve: [() => inject(slaService).checkPermission(permission30['dataExploration'])]
        },
        {
          path: 'run',
          loadComponent: () => import('src/app/ifp-analytics/ifp-exploratory/ifp-run-modal/ifp-run-modal.component').then((m: typeof import('src/app/ifp-analytics/ifp-exploratory/ifp-run-modal/ifp-run-modal.component')) => m.IfpRunModalComponent),
          canActivate: [(route: ActivatedRouteSnapshot, state: RouterStateSnapshot) => inject(AuthGuardService).canActivate(route, state)],
          resolve: [() => inject(slaService).checkPermission(permission30['dataExploration'])]
        },
        {
          path: 'data-result',
          loadComponent: () => import('src/app/ifp-analytics/ifp-exploratory/ifp-exploratory-auto-ml-analyze/ifp-exploratory-auto-ml-analyze.component').then((m) => m.IfpExploratoryAutoMlAnalyzeComponent),
          canActivate: [(route: ActivatedRouteSnapshot, state: RouterStateSnapshot) => inject(AuthGuardService).canActivate(route, state)],
          resolve: [() => inject(slaService).checkPermission(permission30['dataExploration'])]
        },
        {
          path: 're-analysis',
          loadComponent: () => import('src/app/ifp-analytics/ifp-exploratory/ifp-exploratory-auto-ml-analyze/ifp-exploratory-auto-ml-analyze.component').then((m) => m.IfpExploratoryAutoMlAnalyzeComponent),
          canActivate: [(route: ActivatedRouteSnapshot, state: RouterStateSnapshot) => inject(AuthGuardService).canActivate(route, state)],
          resolve: [() => inject(slaService).checkPermission(permission30['dataExploration'])]
        }
      ]
    },
    {
      path: 'analytics/auto-ml',
      loadComponent: () => import('src/app/ifp-analytics/ifp-exploratory/ifp-exploratory.component').then((m: typeof import('src/app/ifp-analytics/ifp-exploratory/ifp-exploratory.component')) => m.IfpExploratoryComponent),
      canActivate: [(route: ActivatedRouteSnapshot, state: RouterStateSnapshot) => inject(AuthGuardService).canActivate(route, state)],
      resolve: [() => inject(slaService).checkPermission(permission30['autoML'])],
      providers: [IfpExploratoryService],
      children: [
        {
          path: '',
          redirectTo: 'upload',
          pathMatch: 'full'
        },
        {
          path: 'upload-data',
          loadComponent: () => import('src/app/ifp-analytics/ifp-exploratory/upload-exploratory/upload-exploratory.component').then((m: typeof import('src/app/ifp-analytics/ifp-exploratory/upload-exploratory/upload-exploratory.component')) => m.UploadExploratoryComponent),
          canActivate: [(route: ActivatedRouteSnapshot, state: RouterStateSnapshot) => inject(AuthGuardService).canActivate(route, state)],
          resolve: [() => inject(slaService).checkPermission(permission30['dataExploration'])]
        },
        {
          path: 'run',
          loadComponent: () => import('src/app/ifp-analytics/ifp-exploratory/ifp-run-modal/ifp-run-modal.component').then((m: typeof import('src/app/ifp-analytics/ifp-exploratory/ifp-run-modal/ifp-run-modal.component')) => m.IfpRunModalComponent),
          canActivate: [(route: ActivatedRouteSnapshot, state: RouterStateSnapshot) => inject(AuthGuardService).canActivate(route, state)],
          resolve: [() => inject(slaService).checkPermission(permission30['autoML'])]
        },
        {
          path: 'data-result',
          loadComponent: () => import('src/app/ifp-analytics/ifp-exploratory/ifp-exploratory-auto-ml-analyze/ifp-exploratory-auto-ml-analyze.component').then((m) => m.IfpExploratoryAutoMlAnalyzeComponent),
          canActivate: [(route: ActivatedRouteSnapshot, state: RouterStateSnapshot) => inject(AuthGuardService).canActivate(route, state)],
          resolve: [() => inject(slaService).checkPermission(permission30['autoML'])]
        },
        {
          path: 're-analysis',
          loadComponent: () => import('src/app/ifp-analytics/ifp-exploratory/ifp-exploratory-auto-ml-analyze/ifp-exploratory-auto-ml-analyze.component').then((m) => m.IfpExploratoryAutoMlAnalyzeComponent),
          canActivate: [(route: ActivatedRouteSnapshot, state: RouterStateSnapshot) => inject(AuthGuardService).canActivate(route, state)],
          resolve: [() => inject(slaService).checkPermission(permission30['autoML'])]
        }
      ]
    },
    {
      path: 'my-Scenarios',
      loadComponent: () => import('src/app/ifp-analytics/ifp-exploratory/ifp-auto-ml-scenario-library/ifp-auto-ml-scenario-library.component').then((m) => m.IfpAutoMlScenarioLibraryComponent),
      canActivate: [(route: ActivatedRouteSnapshot, state: RouterStateSnapshot) => inject(AuthGuardService).canActivate(route, state)],
      resolve: [() => inject(slaService).checkPermission(permission30['autoML'])]
    },
    {
      path: 'scenario-details',
      loadComponent: () => import('src/app/ifp-analytics/ifp-exploratory/ifp-scenario-details/ifp-scenario-details.component').then((m) => m.IfpScenarioDetailsComponent),
      canActivate: [(route: ActivatedRouteSnapshot, state: RouterStateSnapshot) => inject(AuthGuardService).canActivate(route, state)],
      resolve: [() => inject(slaService).checkPermission(permission30['autoML'])]
    }
  ]
}];
@NgModule({
  imports: [RouterModule.forChild(ifpAnalyticsroutes)],
  exports: [RouterModule]
})
export class IfpAnalyticsRoutingModule { }
