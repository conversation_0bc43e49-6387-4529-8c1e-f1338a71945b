<div class="ifp-ai-insight-cr-sentiment-card " [ngClass]="{'ifp-ai-insight-cr-sentiment-card--insight': insights()}">
  <div class="ifp-ai-insight-cr-sentiment-card__impact ">
    <ifp-highcharts [enableAnimation]="enableAnimation()" [donutCenter]="20" [width]="250" [height]="250" [plotOptions]="plot" [chartName]="'donutChart'" [donutCenterFunc]="centerValue" [data]="seriesData()" class="ifp-total-user-donut__chart"></ifp-highcharts>
  </div>
  <div class="ifp-ai-insight-cr-sentiment-card__rating ">
    <div>
      @for (item of leagents; track $index) {
        @if (item.key === status.neg) {
          <div  class="ifp-ai-insight-cr-sentiment-card__rating-outer"><em class="ifp-icon ifp-icon-SmileySad ifp-ai-insight-cr-sentiment-card__rating-icon ifp-ai-insight-cr-sentiment-card__rating--red"></em><span class="ifp-ai-insight-cr-sentiment-card__rating-name">{{item.status_name| translate}}</span> <span class="ifp-ai-insight-cr-sentiment-card__rating-value">{{item.value}} %</span></div>
        } @else if (item.key === status.neu) {
          <div class="ifp-ai-insight-cr-sentiment-card__rating-outer"><em class="ifp-icon ifp-icon-SmileyMeh  ifp-ai-insight-cr-sentiment-card__rating-icon ifp-ai-insight-cr-sentiment-card__rating--yellow"></em><span class="ifp-ai-insight-cr-sentiment-card__rating-name">{{item.status_name | translate}}</span> <span  class="ifp-ai-insight-cr-sentiment-card__rating-value">{{item.value}} %</span></div>
        }@else {
          <div class="ifp-ai-insight-cr-sentiment-card__rating-outer"><em class="ifp-icon ifp-icon-Smiley  ifp-ai-insight-cr-sentiment-card__rating-icon ifp-ai-insight-cr-sentiment-card__rating--green"></em><span n class="ifp-ai-insight-cr-sentiment-card__rating-name">{{item.status_name| translate}}</span> <span  class="ifp-ai-insight-cr-sentiment-card__rating-value">{{item.value}} %</span></div>
        }
      }
    </div>

  </div>
</div>
