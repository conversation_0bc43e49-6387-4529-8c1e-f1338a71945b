import { Component, EventEmitter, Input, OnChanges, Output, SimpleChanges } from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';
import { IfpKebabMenuComponent } from '../ifp-kebab-menu/ifp-kebab-menu.component';
import { DatePipe } from '@angular/common';
import { dateFormat } from 'src/app/scad-insights/core/constants/date.constants';
import { IfpCheckboxComponent } from '../../../scad-insights/ifp-widgets/ifp-atoms/ifp-checkbox/ifp-checkbox.component';
import { IfpTooltipDirective } from 'src/app/scad-insights/core/directives/ifp-tooltip.directive';
import { KebabMenuOption } from '../ifp-kebab-menu/ifp-kebab-menu.interface';
import { kebabMenuOptions } from '../ifp-kebab-menu/ifp-kebab-menu.constants';

@Component({
    selector: 'ifp-db-list-card',
    templateUrl: './ifp-db-list-card.component.html',
    styleUrl: './ifp-db-list-card.component.scss',
    imports: [TranslateModule, IfpKebabMenuComponent, DatePipe, IfpCheckboxComponent, IfpTooltipDirective]
})
export class IfpDbListCardComponent implements OnChanges {

  @Input() title: string = '';
  @Input() thumb: string = '../../../../assets/images/dashboard-builder/preivew-dummy.png';
  @Input() date: string | null = '';
  @Input() id!: string;
  @Input() isQuoetsRemove: boolean = false;
  @Input() thumbType!: string;
  @Input() selectedTab!: string;
  @Input() recipientsCount!: number | undefined;
  @Input() selectedNodes: any = [];

  @Output() cardClicked: EventEmitter<string> = new EventEmitter<string>();
  @Output() cardOptionSelected: EventEmitter<string> = new EventEmitter<string>();
  @Output() selectCardShare: EventEmitter<{ id: string, checked: boolean }> = new EventEmitter<{ id: string, checked: boolean }>();

  public dateFormat = dateFormat;
  public kebabOptions: KebabMenuOption[] = kebabMenuOptions;

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['thumb'] && this.isQuoetsRemove) {
      this.thumb = `data:${this.thumbType};base64,${this.thumb.replace(/"/g, '')}`;
    }
    const editIndex = this.kebabOptions?.findIndex(x => x?.event === 'edit');
    if (editIndex !== undefined) {
      const isDashboardTab = changes['selectedTab'] && this.selectedTab !== 'Dashboards';
      this.kebabOptions[editIndex].disabled = isDashboardTab;
    }
  }

  onClickCard() {
    this.cardClicked.emit(this.id);
  }

  onSelectOptions(event: string) {
    this.cardOptionSelected.emit(event);
  }

  selectNode(event: any) {
    this.selectCardShare.emit({ id: this.id, checked: event.target.checked });
  }

  getChecked(id: string) {
    return this.selectedNodes.find((x: string) => x == id)
  }
}
