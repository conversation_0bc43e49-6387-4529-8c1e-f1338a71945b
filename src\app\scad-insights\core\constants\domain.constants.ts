import { NewsLetterObj } from '../interface/domain.interface';

export const domainTabType = {
  analytical: 'analytical_apps',
  indicator: 'indicator',
  report: 'reports',
  glossery: 'Glossery'
};

export const classifications = {
  analyticalApps: 'analytical_apps',
  ifpIndicators: 'Bayaan Portfolio Indicators',
  officialStatistics: 'official_statistics',
  reports: 'reports',
  innovativeStatistics: 'experimental_statistics',
  compareStatistics: 'Compare',
  spatialAnalytics: 'Spatial Analytics',
  innovativeInsights: 'Innovative Insights',
  officialInsights: 'Official Insights',
  ExperimentalStatistics: 'Experimental Statistics',
  searchAnalyticalApps: 'Analytical Apps',
  searchReports: 'Reports'
};

export const newsLetterEn:NewsLetterObj = {
  iconClass: 'ifp-icon-news-letter',
  id: 'newsLetter',
  isSelected: false,
  name: 'Executive Insights Report',
  nodeCount: 0,
  route: '/',
  showTree: false,
  nodes: [],
  isCustom: true
};

export const newsLetterAr:NewsLetterObj = {
  iconClass: 'ifp-icon-news-letter',
  id: 'newsLetter',
  isSelected: false,
  name: 'النشرة التحليلية للمستوى التنفيذي',
  nodeCount: 0,
  route: '/',
  showTree: false,
  nodes: [],
  isCustom: true
};
