import { CdkDropList } from '@angular/cdk/drag-drop';
import { ComponentRef, ElementRef, Injectable, ViewContainerRef } from '@angular/core';
import {Subject } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class IfpDragDropService {

  public dagItem!: CdkDropList | null | any;
  public status: boolean= false;
  public container!: ElementRef | null;
  public source!: HTMLElement | null;
  public dragIndex!:  number;
  public dropIndex!:  number;
  public dropContainer!: Record<string, string| number | []> | [] | string | any;
  public dropContainerExtra!: Record<string, string| number | []> | [] | string | any;
  public dropContainerIndex!:    number;
  public dropContainerTemp!: Record<string, string| number | []> | [] | string | any;
  public dropContainerTempExtra!: Record<string, string| number | []> | [] | string | any;
  public dropContainerIndexTemp!:    number;
  public dragIndexAndContainerData!:  IndexAndContainerData;
  public dropIndexAndContainerData!:  IndexAndContainerData;
  public subject:Subject<DragAndDropIndexAndContainerData> = new Subject<DragAndDropIndexAndContainerData>();
  public update:Subject<DragAndDropIndexAndContainerData> = new Subject<DragAndDropIndexAndContainerData>();
  public cdkDrag: boolean = true;
  public leave =  false;
  public sidebar!: null | boolean;

  public outside: boolean = false;
  public outsideValue: string = '';
  public outsideTemplate!: ComponentRef<any>;
  public reference!: ViewContainerRef;

  /**
   * reset all variable
   *
   * @memberof AxDragDropService
   */
  reset() {
    this.cdkDrag = false;
    this.dagItem = null;
    this.container = null;
    this.source = null;
    this.sidebar= null;
    this.dropContainer = undefined;
  }

  /**
   *used to update the status of drag globally
   *
   * @param {boolean} status
   * @memberof AxDragDropService
   */
  updateStatus(status: boolean) {
    this.status = status;
  }


  /**
 * used to clear the temporary  ui
 *
 * @memberof AxDragDropService
 */
  clearOutsideTemplate() {
    this.reference?.clear();
  }
}
export interface DragAndDropIndexAndContainerData {
    dragIndexAndContainerData: IndexAndContainerData;
    dropIndexAndContainerData: IndexAndContainerData;
  }

export interface IndexAndContainerData {
    index: number ;
    data?: DragEventData;
    containerIndex: number;
    containerData: Record<string, string| number | []> | [] | string | any;
    containerDataExtra?: Record<string, string| number | []> | [] | string | any;
  }
export interface DragEventData {
    index: number ;
    data: Record<string, string| number | []> | [] | string | any
  }
