import { IfpDynamicComponent } from './../../molecule/ifp-dynamic/ifp-dynamic.component';
import { Component, Input, Output, ViewChild, EventEmitter, OnChanges, SimpleChanges, InputSignal, input } from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';
import { ifpColors } from 'src/app/scad-insights/core/constants/color.constants';
import { dynamicComponents, options } from './ifp-data-table.constant';
import { IfpLoadComponentViewDirective } from 'src/app/scad-insights/core/directives/ifp-load-component-view.directive';
import { CustomEventEmit } from '../../data-prep/ifp-data-prep/interface/ifp-data-prep.interface';
import { NgClass, TitleCasePipe } from '@angular/common';
import { IfpTooltipDirective } from 'src/app/scad-insights/core/directives/ifp-tooltip.directive';
import { IfpNoDataComponent } from '../../../scad-insights/ifp-widgets/ifp-molecules/ifp-no-data/ifp-no-data.component';
import { IfpCardLoaderComponent } from "../../../scad-insights/ifp-widgets/ifp-atoms/ifp-card-loader/ifp-card-loader.component";

@Component({
    selector: 'ifp-data-table',
    imports: [TranslateModule, IfpDynamicComponent, NgClass, IfpTooltipDirective, IfpNoDataComponent, TitleCasePipe, IfpCardLoaderComponent],
    templateUrl: './ifp-data-table.component.html',
    styleUrl: './ifp-data-table.component.scss'
})
export class IfpDataTableComponent implements OnChanges {
  @ViewChild(IfpLoadComponentViewDirective, { static: true }) container!: IfpLoadComponentViewDirective;

  @Input() tableHead?: string[];
  @Input() tableData: any[] = [];
  @Input() rowClickEnable =false;
  @Input() selectedIndex: null| number  = null;
  @Input() headerSettings?: Record<string, any>;
  @Input() translation = true;
  @Input() headingTranslate = true;
  public isTitleUpperCase: InputSignal<boolean> = input(false);
  @Output() customEventEmitter = new EventEmitter();
  @Output() rowEventEmitter = new EventEmitter();
  @Output() sortEvent = new EventEmitter();
  @Output() colEvent = new EventEmitter();
  public isDashboard: InputSignal<boolean> = input<boolean>(false);
  public sortTableHead: InputSignal<TableHeadWithSort[]> = input<TableHeadWithSort[]>([]);
  public loader: InputSignal<boolean> = input<boolean>(false);
  public component: any;
  public color = ifpColors;
  public dynamicComponents: any = dynamicComponents;
  public option = options;
  public sortValue?: string;
  public sortValueString?: string;
  public acceding?: boolean;



  constructor() { }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['rowClickEnable']) {
      this.selectedIndex = null;
    }
  }

  onCustomEvent(event: CustomEventEmit, index: number) {
    event['index'] = index;
    this.customEventEmitter.emit(event);
  }


  onRowClick(event: any, index: number) {
    if (this.rowClickEnable) {
      event['index'] = index;
      this.selectedIndex = index;
      this.rowEventEmitter.emit(event);
    }
  }

  colClick(col: any, row: any) {
    if (col?.click) {
      this.colEvent.emit({col: col, row: row});
    }
  }

  sort(name: string, sort: boolean) {
    console.log(this.headerSettings);

    if (this.sortValueString === name && this.acceding === sort) {
      name = '';
      this.sortValue = '';
    } else {
      this.sortValue = sort ? `-${name}` : name;
    }
    this.sortValueString = name;
    this.acceding = sort;
    this.sortEvent.emit({sortValue: this.sortValue, sortEventName: name, sortEvent: sort });
  }

}

export interface TableHeadWithSort {
  title: string;
  sort?: boolean;
  filter?: boolean;
}

export interface DataTableSortEvent {
  sortValue: string;
  sortEventName: string;
  sortEvent: boolean;
  }
