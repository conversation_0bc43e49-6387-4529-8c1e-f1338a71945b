<div class="ifp-calendar">
  <div class="ifp-calendar__header-wrapper">
    <div class="ifp-calendar__filter-actions">
      <!-- <div class="ifp-calendar__today-button"><em class="ifp-icon ifp-icon-calender1"></em>{{'Today' | translate}}</div> -->
      <span class="ifp-calendar__current-date">{{ monthNames[currentMonth] }}, {{ currentYear }}</span>
      <em class="ifp-icon ifp-icon-left-arrow ifp-calendar__button" (click)="prevMonth()"></em>
      <em class="ifp-icon ifp-icon-right-arrow ifp-calendar__button" (click)="nextMonth()"></em>
    </div>

    <div class="ifp-calendar__status-wrapper">
      @for (status of statusList; track $index) {
      <div class="ifp-calendar__status"><span class="ifp-calendar__status-round"
          [style.background-color]="status.color"></span> <span
          class="ifp-calendar__status-label">{{status.name}}</span></div>
      }
    </div>
  </div>

  
  <div class="ifp-calendar__body-wrapper">
    <div class="ifp-calendar__day-wrapper">
      @for (day of weekdays; track $index) {
      <span class="ifp-calendar__day">{{day | translate}}</span>
      }
    </div>

    <div class="ifp-calendar__date-wrapper">
      @for (day of calendarDays; track $index) {
      <div class="ifp-calendar__cell" [ngClass]="{
          'ifp-calendar__cell--disabled': !day.currentMonth,
          'ifp-calendar__cell--today': (today.getDate() === day.date) && (monthNames[today.getMonth()] === monthNames[currentMonth]),
          'ifp-calendar__cell--event': day?.isEvent
          }">
        <div class="ifp-calendar__cell-header">
          <span class="ifp-calendar__date">{{ day.date }}</span>
          <em class="ifp-icon ifp-icon-link-curve ifp-calendar__event-link" (click)="openEventDetails(day)"
            [appIfpTooltip]="'View details' | translate"></em>
        </div>
        <div class="ifp-calendar__progress-sec">
          @for (status of day?.isEvent?.status; track $index) {
          <ifp-progress-value [progressDecimalCount]="0" [height]="'10px'" class="ifp-calendar__progess"
            [progress]="day.isEvent?.totalCount || 0" [progressValue]="day.isEvent?.totalCount ? (status.count / (day.isEvent?.totalCount ?? 0)) * 100 : 0" [color]="_dataGovernanceService.checkColor(status.name)"
            [percentage]="true" [value]="false">
          </ifp-progress-value>
          }
          <!-- <ifp-progress-value [progressDecimalCount]="0" [height]="'10px'" class="ifp-calendar__progess"
            [progress]="700" [progressValue]="70" [color]="'#FAC656'">
          </ifp-progress-value>
          <ifp-progress-value [progressDecimalCount]="0" [height]="'10px'" class="ifp-calendar__progess"
            [progress]="600" [progressValue]="60" [color]="'#D1D5DA'">
          </ifp-progress-value>
          <ifp-progress-value [progressDecimalCount]="0" [height]="'10px'" class="ifp-calendar__progess"
            [progress]="2050" [progressValue]="100" [color]="'#5DB14E'">
          </ifp-progress-value> -->
        </div>
        @if (day?.isEvent) {
        <p class="ifp-calendar__event" [appIfpTooltip]="day?.isEvent?.name ?? ''">{{day?.isEvent?.name ?? ''}}</p>
        }
      </div>
      }
    </div>
  </div>
</div>
