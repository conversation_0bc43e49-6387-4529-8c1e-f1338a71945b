import { <PERSON><PERSON><PERSON><PERSON><PERSON>ni<PERSON>, ChangeDetector<PERSON><PERSON>, <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, OnInit, ViewChild, signal } from '@angular/core';
import { ActivatedRoute, Params, Router } from '@angular/router';
import { IfpDataSummaryComponent, TableSettings, tableTypes } from 'src/app/ifp-analytics/organism/ifp-data-summary/ifp-data-summary.component';
import { IfpPrepUploadDataComponent, UploadedFile } from 'src/app/ifp-analytics/organism/ifp-prep-upload-data/ifp-prep-upload-data.component';
import { CardData } from 'src/app/shared/molecule/ifp-tool-card/ifp-tool-card.component';
import { SubSink } from 'subsink';
import { basicTools } from '../ifp-data-prep.constant';
import { TranslateModule } from '@ngx-translate/core';
import { IfpPrepService } from '../ifp-prep-service';
import { IfpDataPreviewComponent } from '../../../organism/ifp-data-preview/ifp-data-preview.component';
import { IfpPrepUploadService } from './services/ifp-prep-upload.service';
import { ApiStatus } from 'src/app/scad-insights/core/constants/api-status.constants';
import { ToasterService } from 'src/app/scad-insights/core/services/tooster/ToastrService.service';
import { SubscriptionLike } from 'rxjs';
import { FileResponePrep } from '../interface/ifp-data-prep.interface';
import { connectionType, prepsApiEndpoints } from '../constants/if-preps.constants';
import { Store } from '@ngrx/store';
import { loadCreateNodeClear, loadNode } from '../store/node-store/node-store.actions';
import { MetadataColumnPrep, PreivewPrep } from 'src/app/ifp-analytics/organism/ifp-data-preview/interface/ifp-data-prep.intreface';
import { workFlowState } from '../constants/ifp-state.contants';
import { environment } from 'src/environments/environment';
import { DownLoadService } from 'src/app/scad-insights/core/services/download-service/download.service';
import { IfpImportIndicatorsComponent } from '../../../../dashboard-builder/organism/ifp-import-indicators/ifp-import-indicators.component';
import { IfpPrepPopupComponent } from '../../../organism/ifp-prep-popup/ifp-prep-popup.component';
import { IfpModalComponent } from '../../../../scad-insights/ifp-widgets/ifp-organism/ifp-modal/ifp-modal.component';
import { StreamingService } from 'src/app/scad-insights/core/services/http/streaming.service';
import { ifpColors } from 'src/app/scad-insights/core/constants/color.constants';
import { Location } from '@angular/common';
import { IfpAdvancePrepService } from '../../ifp-advance-prep/service/ifp-advance-prep.service';

@Component({
    selector: 'ifp-data-prep-upload',
    templateUrl: './ifp-data-prep-upload.component.html',
    styleUrl: './ifp-data-prep-upload.component.scss',
    providers: [IfpPrepUploadService],
    imports: [IfpPrepUploadDataComponent, IfpDataSummaryComponent, TranslateModule, IfpDataPreviewComponent, IfpImportIndicatorsComponent, IfpPrepPopupComponent, IfpModalComponent]
})
export class IfpDataPrepUploadComponent implements OnInit, OnDestroy, AfterViewInit {
  @ViewChild('modalSuccess') modal!: IfpModalComponent;


  private subs: SubSink = new SubSink();
  public libraryId!: string;
  public step!: string;
  public actionCards: CardData[] = basicTools[0].menu;
  public uploadDataProgess = signal(0);
  public downloadSubscribe!: SubscriptionLike;
  public uploadResponse!: FileResponePrep;
  public disableProcesed: boolean = true;
  public summery: MetadataColumnPrep[] = [];
  public loaderProcess = false;
  public summeryLoader = true;
  public allowedSheetType: string = 'xlsx';
  public fileName = signal('');
  private cancelUploads = false;
  public summeryTableSettings: TableSettings = {
    withId: true,
    setting: [
      {
        key: 'name',
        name: 'Variable'
      },
      {
        key: 'type',
        name: 'Data Type',
        type: tableTypes.bracket,
        secondKey: 'type_description'
      },
      {
        key: 'Stats / Values',
        name: 'Stats / Values',
        type: tableTypes.table,
        tableSettings: [{
          key: 'min_value',
          funName: (column, row) => row.type === 'string' ?   'Total count': 'Min',
          name: '',
          fun: (column, row) => row.type === 'string' ?   row.null_count + row.valid_count : row.min_value
        },
        {
          key: 'median',
          funName: (column, row) => row.type === 'string' ?   'Unique values count' : 'Median',
          name: '',
          fun: (column, row) =>  row.type === 'string' ?   row.unique_count : row.median
        },
        {
          key: 'max_value',
          funName: (column, row) => row.type === 'string' ?   'Unique values percentage' : 'Max',
          name: '',
          fun: (column, row) =>  row.type === 'string' ?  `${row.unique_percentage}%` : row.max_value
        }
        ]
      },
      {
        key: 'valid_count',
        name: 'Non-null Values',
        type: tableTypes.progress,
        precentageKey: 'valid_percentage',
        color: ifpColors.green
      },
      {
        key: 'null_count',
        name: 'Null Values',
        type: tableTypes.progress,
        precentageKey: 'null_percentage',
        reverse: true

      }
    ]
  };

  public disableAll = false;



  public summeryStreemSubscription!: SubscriptionLike;

  public controller!: AbortController;

  public loadedSummery = false;
  public notErrorFile = false;
  public indicatorModalOpen = signal(false);
  public success = signal(false);
  public upload = signal(false);
  public fileSize?: number;
  public loaderFile = false;
  public error = false;
  public sheet = false;
  public sheetDetails!: { file: UploadedFile, path: string, sheet: string };
  public totalRow?: number;
  public totalColumn?: number;

  constructor(private _router: Router, private _prepService: IfpPrepUploadService, public _prepCommonService: IfpPrepService, private _route: ActivatedRoute, private _toast: ToasterService, private _store: Store, private _downloadService: DownLoadService,
    private _cdr: ChangeDetectorRef, private _stremService: StreamingService, private store: Store,
    private _location: Location, public advancePrepService:IfpAdvancePrepService
  ) {


  }


  ngOnInit() {
    this._prepCommonService.processDisable.set(true);
    this._prepCommonService.isSummary.set(false);
    this.subs.add( this._prepCommonService.processEvent.subscribe((data: string) => {
      if (data !== '') {
        this._prepCommonService.loaderProcess.set(true);
        if (this.step === 'summary') {
          this.uploadProccessedsummery();
        } else {
          this.uploadDataUpdate();
        }
      }

    }));
    this.subs.add(
      this._route.queryParams.subscribe((params: Params) => {
        // if (this._prepCommonService.isSummary()) {
        //   this.step = 'summary';
        //   this.libraryId = this._prepCommonService.uploadedFileResponse()?.id ?? '';
        //   this.callSummery();
        // }
        if ((params['summary'] === 'true' || this._prepCommonService.isSummary()) && this._prepCommonService.uploadedFileResponse()?.id) {
          this._prepCommonService.isSummary.set(true);
          this.step = 'summary';
          this.libraryId = this._prepCommonService.uploadedFileResponse()?.id ?? '';
          this.callSummery();
        } else {
          this.step = '';
          if (!this.loadedSummery) {
            this._prepCommonService.processDisable.set(true);
          }
        }
        if (params['id']) {
          this.libraryId = params['id'];
          this.step = 'summary';
          this.callSummery();
          this.fileSize = undefined;
          this._prepCommonService.disableSidebar.set(true);
          this._store.dispatch(loadNode['[prepComponent]CreateConnection']({ id: params['id'] ?? '', typeData: connectionType.dataset, earseData: true }));
        }
        if (params['library'] && this._prepCommonService.fileName() && this._prepCommonService.fileName() !== '') {
          this.disableProcesed = false;
          this._prepCommonService.processDisable.set(false);
          this.uploadResponse = this._prepCommonService.uploadedFileResponse() ?? {};
          this.libraryId = this._prepCommonService.uploadedFileResponse()?.id ?? '';
        }
      })
    );
  }





  ngAfterViewInit(): void {
    if (this.step === 'summary') {
      this.upload.set(true);
      this._cdr.detectChanges();
      this.modal?.removeModal();
      this.modal?.createElement();
      this._cdr.detectChanges();
    }

  }


  back() {
    this.loadedSummery = false;
    this._router.navigate(['analytics/data-preparation/upload-data']);
    this.step = '';
    this.disableProcesed = false;
    this._prepCommonService.processDisable.set(false);
  }

  closeImport() {
    this.loaderProcess = false;
    this.cancelUploads = true;
    // this._prepCommonService.uploadedFileResponse.set(null);
    // this._prepCommonService.fileName.set('');
    this.modal.removeModal();
    this.indicatorModalOpen.set(false);
  }

  closeSummeryModal() {
    this._prepCommonService.isSummary.set(true);
    this.modal.removeModal();
    this.upload.set(false);
    this._router.navigate(['/analytics/data-preparation/upload-data'], { replaceUrl: true, queryParams: { summary: 'true' }});
  }



  callSummery() {
    this._prepCommonService.processDisable.set(true);
    this.summeryLoader = true;
    this.summeryStreemSubscription = this._stremService.connectToServerSentEvents(`${environment.prepbaseUrl + prepsApiEndpoints.summeryStream + this.libraryId}/`)
      .subscribe({
        next: (data: { data: string, ctrl: AbortController }) => {
          const dataValue = JSON.parse(data.data);
          this.controller = data.ctrl;
          if ('completed' == dataValue.status) {
            data.ctrl.abort();
            this.summeryStreemSubscription.unsubscribe();
            this.subs.add(
              this._prepCommonService.getMethodRequest(prepsApiEndpoints.libraryDataset + this.libraryId + prepsApiEndpoints.libraryPreview, { limit: 1, offset: 1, page: 1 }).subscribe((item: PreivewPrep) => {
                if (item.file) {
                  this.uploadResponse = {
                    id: this.libraryId,
                    name: item.name,
                    file: item.file,
                    owner: item.owner,
                    storage_backend: item.storage_backend
                  };
                  this._prepCommonService.fileName.set(item.name);
                  this._prepCommonService.uploadedFileResponse.set({
                    name: item.name,
                    file: item.file,
                    owner: item.owner,
                    storage_backend: item.storage_backend,
                    id: item.object_id
                  });
                }
                this._prepCommonService.fileName.set(item.name);
                this.summery = item.metadata?.columns ?? [];
                this.summeryLoader = false;
                this._prepCommonService.processDisable.set(false);
                this.totalRow = item.total_rows;
                this.totalColumn = item.total_columns;
              })
            );
          }
        },
        error: () => {
          this._stremService.close();
          this.summeryStreemSubscription.unsubscribe();
        }
      }
      );

  }

  disableEvent(event: boolean) {
    this._prepCommonService.processDisable.set(event);
  }

  addAllIndicators(indicatorId: { id: string; cols: number; rows: number; y: number; x: number; key: string; type: string; indicatorId: string; title: string; }) {
    this.cancelUploads = false;
    this._prepCommonService.uploadedFileResponse.set({
      name: indicatorId.title,
      file: indicatorId.type,
      owner: '',
      storage_backend: '',
      id: indicatorId.indicatorId ?? indicatorId.id
    });
    this._prepCommonService.fileName.set(indicatorId.title);
    const uploadData = {
      name: indicatorId.title,
      indicator_id: indicatorId.indicatorId ?? indicatorId.id,
      storage_backend: 's3'
    };
    this._prepService.getIndicatorUpload(uploadData).subscribe({
      next: data => {
        this.libraryId = data?.id;
        this._prepCommonService.uploadedFileResponse.set(data);
        this.uploadResponse = data;
        this.uploadProccessed(false, data?.id, true);
      },
      error: error => {
        error?.error?.non_field_errors?.forEach((element:string) => {
          this._toast.error(element);
        });

      }
    });

  }


  openLibrary() {
    if (!this._prepCommonService.uploadedFileResponse()?.id) {
      this._prepCommonService.fileName.set('');
    }

    /*  used to replace url in the current state */
    this._location.replaceState('analytics/data-preparation/upload-data', 'library=true', this._location.getState());
    this._router.navigateByUrl('analytics/prep-library?source=prep');
  }

  onSelectWorksheet(event: { file: UploadedFile, path: string, sheet: string }) {
    this.sheet = true;
    this.sheetDetails =event;
    this.disableProcesed = false;
    this._prepCommonService.processDisable.set(false);
    this.error = false;
  }

  clearFile() {
    this._prepCommonService.disableSidebar.set(true);
    this._prepCommonService.uploadedFileResponse.set(null);
    this._prepCommonService.isSummary.set(false);
    this.loadedSummery = false;
  }

  onFileUpload(file: UploadedFile, path: string = '', sheet: string = '') {
    this.error = false;
    this.sheet = false;
    this.disableProcesed = true;
    this._prepCommonService.processDisable.set(true);
    this.uploadDataProgess.set(0);
    this.loadedSummery = false;
    this._prepCommonService.disableSidebar.set(true);
    this._prepCommonService.uploadedFileResponse.set(null);
    const uploadData = new FormData();
    const nameFile = path !== '' ? path : file.file[0].name;
    uploadData.append('name', nameFile);
    uploadData.append('storage_backend', 's3');

    this.fileName.set(file.file[0].name.replace(/^.*[\\/]/, ''));
    const fileType = this.fileName().split('.').pop() ?? '';
    if (fileType === this.allowedSheetType) {
      uploadData.append('sheet_name', sheet);
    } else {
      uploadData.append('file', file.file[0]);
    }
    this.fileSize = file.file[0].size;
    this._prepCommonService.fileName.set(file.file[0].name);
    this.loaderFile = true;
    return new Promise((resolve, reject) => {
      this.downloadSubscribe = this._prepService.getUploadData(uploadData).subscribe({
        next: data => {
          if (data?.type == 1) {
            this.uploadDataProgess.set(Math.floor((data?.loaded / data?.total) * 100) - 10);
          } else if (data?.type == 4) {
            if (data?.status == ApiStatus.created) {
              this.libraryId = data?.body?.id;
              this._prepCommonService.uploadedFileResponse.set(data.body);
              this.uploadResponse = data.body;
              this.disableProcesed = false;
              this._prepCommonService.processDisable.set(false);
              this.summery = [];
              this._toast.success('Data uploaded and saved to the Library!');
              this.notErrorFile = true;
              this.uploadDataProgess.set(100);
              this.loaderFile = false;
              resolve(data.body);
            }

          }
        }, error: err => {
          const error = err?.error;
          this.errorHandler(error);
          this.notErrorFile = false;
          this.uploadResponse = {};
          this.loaderFile = false;
          this._prepCommonService.uploadedFileResponse.set(null);
          this.error = true;
          reject();
        }
      });
    });
    //  this._router.navigate(['analytics/data-preparation/upload-data']);
  }

  errorHandler(error: any) {
    if (typeof error === 'string' ) {
      if (error!== '') {
        this._toast.error(error);
        this.disableProcesed = true;
        this._prepCommonService.processDisable.set(true);
      }
    } else {
      for (const key in error) {
        if (Object.prototype.hasOwnProperty.call(error, key)) {
          const element = error[key];
          let data = '';
          if (typeof element === 'string' ) {
            data = element;
          } else {
            element.forEach((elementValue:string) => {
              data =  `${data} ${elementValue}`;
            });
          }
          if (data!== '') {
            this._toast.error(data);
            this.disableProcesed = true;
            this._prepCommonService.processDisable.set(true);
          }
        }
      }
    }
  }

  deleteFile() {
    this.sheet = false;
    this.downloadSubscribe?.unsubscribe();
    this.error = false;
    if (this.uploadResponse  ) {
      if (this.notErrorFile && !this.sheet) {
        this.deleteLibrary(this.uploadResponse.id ?? '');
      }
      this.notErrorFile = true;
      this._prepCommonService.uploadedFileResponse.set(null);
      this._prepCommonService.fileName.set('');
    }
    this.sheet = false;
    this.uploadResponse = {};
    this.store.dispatch(loadNode[loadCreateNodeClear]({ config: '' }));
    this.disableProcesed = true;
    this._prepCommonService.disableSidebar.set(true);
  }


  deleteLibrary(id: string) {
    this.subs.add(
      this._prepCommonService.getDeleteRequest(`${prepsApiEndpoints.libraryDelete}${id}`).subscribe({
        next: () => {
          this._toast.success('File deleted successfully');
        }, error: err => {
          // const error = err?.error;
          // this.errorHandler(error);
        }
      })
    );
  }

  indicatorClickEvent() {
    this.indicatorModalOpen.set(true);
    this._cdr.detectChanges();
    this.modal.removeModal();
    this.modal.createElement();
  }

  uploadProccessedsummery() {
    if (this.loadedSummery) {
      this.step = 'complete';
      this.success.set(true);
      this._cdr.detectChanges();
      this.modal?.removeModal();
      this.modal.createElement();
      this._prepCommonService.disableSidebar.set(false);
      this._prepCommonService.loaderProcess.set(false);
    } else {
      this.uploadProccessed(true, this.uploadResponse?.id);
    }

  }

  closeSucessModal() {
    this.modal.removeModal();
    this.success.set(false);
    this._prepCommonService.isSummary.set(true);
    this.step = 'summary';
  }

  uploadProccessed(fromSummery: boolean = false, id?: string, ifp?: boolean) {
    this._prepCommonService.loaderProcess.set(true);
    this.loaderProcess = true;
    this._prepCommonService.processActive.set(true);
    this._store.dispatch(loadNode['[prepComponent]CreateConnection']({ id: id ?? '', typeData: connectionType.dataset, earseData: true }));
    this.disableAll = true;
    const subsValue = this._prepCommonService.processStatus.subscribe(data => {
      if (this.cancelUploads) {
        subsValue?.unsubscribe();
        this.disableAll = false;
        return;
      }
      if (data?.workflow_status?.toUpperCase() === workFlowState.completed?.toUpperCase()) {
        this.disableAll = false;
        subsValue.unsubscribe();
        this._prepCommonService.isShowPreview = true;
        this._prepCommonService.disableSidebar.set(false);
        this.step = fromSummery ? 'complete' : 'summary';
        if (this.step === 'summary') {
          this._prepCommonService.disableSidebar.set(true);
        }
        if (this.step === 'complete') {

          this.success.set(true);
          this._cdr.detectChanges();
          this.modal?.removeModal();
          this.modal.createElement();
        } else if (!fromSummery) {
          this.callSummery();
          this.loadedSummery = true;
          this.upload.set(true);
          this._cdr.detectChanges();
          this.modal.removeModal();
          this.modal.createElement();
          this._cdr.detectChanges();
        }
        if (ifp) {
          this.modal.removeModal();
          this.indicatorModalOpen.set(false);
          this.success.set(true);
          this._cdr.detectChanges();
          this.modal?.removeModal();
          this.modal.createElement();
        }
        this.loaderProcess = false;
        this._prepCommonService.loaderProcess.set(false);
      } else if (data?.workflow_status === workFlowState.error) {
        this.disableAll = false;
        this.loaderProcess = false;
        this._prepCommonService.loaderProcess.set(false);
        if (ifp) {
          this.modal.removeModal();
          this.indicatorModalOpen.set(false);
        }
      }
    });
  }

  uploadDataUpdate() {
    if ( this.sheet) {
      this.onFileUpload(this.sheetDetails.file, this.sheetDetails.path, this.sheetDetails.sheet).then(() => {
        this.uploadProccessed(false, this._prepCommonService.uploadedFileResponse()?.id);
      }).catch(()=> {
        return;
      });
    } else {
      this.uploadProccessed(false, this._prepCommonService.uploadedFileResponse()?.id);
    }

  }

  downloadPreivew() {
    this.subs.add(this._prepCommonService.getDownloadRequest(`${environment.prepbaseUrl}${prepsApiEndpoints.libraryDataset}${this.libraryId}${prepsApiEndpoints.summeryDownload}`).subscribe({
      next: data => {
        const matches = (data.headers.get('Content-Disposition'))?.match(/"(.*?)"/);
        const nameValue = matches ? matches[1] : '.xlsx';
        this._downloadService.downloadFiles(data.body, nameValue);
      },
      error: err => {
        const error = err?.error;
        this.errorHandler(error);
      }
    }));
  }


  prevStep() {
    this._router.navigate(['analytics/data-preparation/upload-data']);
    this.step = '';
    this.disableProcesed =false;
    this._prepCommonService.processDisable.set(false);
    // this._prepCommonService.uploadedFileResponse.set(null);
    // this._prepCommonService.fileName.set('');
    // this.libraryId = '';
    // this.uploadResponse = {};
    this._prepCommonService.disableSidebar.set(true);
  }

  onCardSelected(_card: CardData) {
    //
  }

  buttonClick(_event: CardData) {
    this.modal?.removeModal();
    this.success.set(false);
  }

  ngOnDestroy(): void {
    this.downloadSubscribe?.unsubscribe();
    this.subs.unsubscribe();
    this.modal?.removeModal();
    this.controller?.abort();
    this._prepCommonService.processEvent.next('');
  }
}
