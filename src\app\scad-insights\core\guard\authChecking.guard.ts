import { Injectable } from '@angular/core';
import {  Router } from '@angular/router';
import { IFPMsalService } from '../services/IFP-msal.service';
@Injectable({
  providedIn: 'root'
})
export class AuthGuardCheckingService {

  constructor( private _router: Router, private _msal: IFPMsalService) {
  }

  canActivate(): boolean {
    if (!this._msal.getToken || ( this._msal.tokenExp && (new Date() >= new Date(this._msal.tokenExp))) ) {
      return true;
    }
    this._router.navigate(['/']);
    return false;
  }


}
