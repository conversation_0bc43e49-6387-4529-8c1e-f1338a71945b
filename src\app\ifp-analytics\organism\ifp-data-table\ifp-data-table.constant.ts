
export const  dynamicComponents:any= {
  'dropDown': {
    class: 'ifp-dropdown',
    component: () => import('src/app/dashboard-builder/molecule/ifp-db-dropdown/ifp-db-dropdown.component').then(mod => mod.IfpDbDropdownComponent)
  }
};

export const options:any = [
  {content_type: 'abc', id: 1, name: 'Numeric(Recommended)', icon_path: '', nodeCount: 1, isSelected: true, domains: [], nodes: [], showTree: false, key: ''},
  {content_type: 'abc', id: 1, name: 'String(Recommended)', icon_path: '', nodeCount: 1, isSelected: false, domains: [], nodes:[], showTree: false, key: ''},
  {content_type: 'abc', id: 1, name: 'Int(Recommended)', icon_path: '', nodeCount: 1, isSelected: false, domains: [], nodes:[], showTree: false, key: ''},
  {content_type: 'abc', id: 1, name: '<PERSON>ar<PERSON><PERSON>(Recommended)', icon_path: '', nodeCount: 1, isSelected: false, domains: [], nodes:[], showTree: false, key: ''}
];


export const imputeOptions:any = [

  {content_type: { 'method': 'median'}, id: 3, name: 'Replace with Median (Recommended)', icon_path: '', nodeCount: 1, isSelected: true, domains: [], nodes: [], showTree: false, key: 'median'},
  {content_type: {
    'method': 'max'
  }, id: 1, name: 'Replace with Max', icon_path: '', nodeCount: 1, isSelected: true, domains: [], nodes: [], showTree: false, key: 'max'},
  {content_type: {
    'method': 'min'
  }, id: 2, name: 'Replace with Min', icon_path: '', nodeCount: 1, isSelected: true, domains: [], nodes: [], showTree: false, key: 'min'},
  {content_type: {'method': 'mean'}, id: 4, name: 'Replace with Mean', icon_path: '', nodeCount: 1, isSelected: true, domains: [], nodes: [], showTree: false, key: 'mean'},
  {content_type: {'method': 'custom'}, id: 5, name: 'Replace with Custom', icon_path: '', nodeCount: 1, isSelected: true, domains: [], nodes: [], showTree: false, key: 'custom'}
];
