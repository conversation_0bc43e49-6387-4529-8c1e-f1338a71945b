import { Component, EventEmitter, Input, On<PERSON><PERSON>roy, OnInit, Output, signal } from '@angular/core';
import { IfpAnalyticLineChartComponent } from '../../../scad-insights/ifp-widgets/ifp-molecules/ifp-analytic-line-chart/ifp-analytic-line-chart.component';
import { ChartDataScenario } from '../ifp-scenario-details/ifp-scenario-details.component';
import { TranslateModule } from '@ngx-translate/core';
import { buttonClass } from 'src/app/scad-insights/core/constants/button.constants';
import { IfpNoDataComponent } from '../../../scad-insights/ifp-widgets/ifp-molecules/ifp-no-data/ifp-no-data.component';
import { IfpSpinnerComponent } from '../../../scad-insights/ifp-widgets/ifp-molecules/ifp-spinner/ifp-spinner.component';
import { ApiService } from 'src/app/scad-insights/core/services/api.service';
import { SubSink } from 'subsink';
import { autoMlApiEndpoints } from '../constants/auto-ml.constant';

@Component({
    selector: 'ifp-exploratory-sample-chart',
    imports: [IfpAnalyticLineChartComponent, TranslateModule, IfpNoDataComponent, IfpSpinnerComponent],
    templateUrl: './ifp-exploratory-sample-chart.component.html',
    styleUrl: './ifp-exploratory-sample-chart.component.scss'
})
export class IfpExploratorySampleChartComponent implements OnInit, OnDestroy {
  @Input() chartDataApi!: PreviewChart;
  @Input() chartLoader = true;
  @Input() currentId = '';
  @Output() backButton =  new EventEmitter();
  @Output() runButton =  new EventEmitter();
  public chartData: ChartDataScenario[] = [];
  public yAxisLabel = signal('');
  public xAxisLabel = signal('');
  public buttonClass = buttonClass;
  public subs = new SubSink();

  constructor(private _autoMlService:ApiService) {}

  ngOnInit(): void {
    this.callChart();

  }

  callPreview() {
    const data = this.chartDataApi;
    this.chartData = [];
    const chartData: ChartDataScenario =  {
      'name': data.y_axis,
      'data': [


      ],
      'actualValue': [],
      'monthValues': [

      ],
      'yearValues': [

      ],
      'dashStyle': 'Solid',
      'type': 'line',
      'zIndex': 1,
      'identifier': data.y_axis,
      'lineWidth': 3,
      'color': '#3BD6AD',
      'marker': {
        'fillColor': '#3BD6AD',
        'lineWidth': 3,
        'lineColor': '#3BD6AD',
        'symbol': 'circle',
        'enabled': true
      }
    };
    data.data.forEach(dataValue => {
      chartData.data.push([this.getConvertDate( dataValue.date), dataValue.value]);
    });
    this.chartData.push(chartData);
    this.yAxisLabel.set( data.y_axis);
    this.xAxisLabel.set( data.x_axis);
  }


  // used to convert date to utc format
  getConvertDate(dateValue: string) {
    const date = new Date(dateValue);

    // Get the UTC timestamp
    return Date.UTC(date.getUTCFullYear(), date.getUTCMonth(), date.getUTCDate(),
      date.getUTCHours(), date.getUTCMinutes(), date.getUTCSeconds());
  }


  // get preview chartList
  callChart() {
    this.chartLoader = true;
    this.subs.add(
      this._autoMlService.getMethodRequest(autoMlApiEndpoints.exploratoryPreviewChart +this.currentId +autoMlApiEndpoints.exploratoryPreviewChartView, {}, true).subscribe((data:PreviewChart) => {
        this.chartDataApi = data;
        this.chartLoader = false;
        this.callPreview();
      })
    );
  }

  ngOnDestroy(): void {
    this.subs.unsubscribe();
  }
}
export interface PreviewChart {
  name: string;
  data: Datum[];
  freequency: string;
  x_axis: string;
  y_axis: string;
  workflow_meta: Workflowmeta;
}

interface Workflowmeta {
  frequency: string;
  frequency_range: number;
  target_variable: string;
  time_stamp_variable: string;
}

interface Datum {
  value: number;
  date: string;
}
