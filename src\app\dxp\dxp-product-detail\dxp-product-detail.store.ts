// import { signalStore, withState, withMethods, patchState } from '@ngrx/signals';
// import { inject } from '@angular/core';
// import { ApiService } from 'src/app/scad-insights/core/services/api.service';
// import { dxpApi } from '../dxp.constants';

// export interface DxpProductDetailState {
//   loading: boolean;
//   error: string | null;
//   data: any;
//   sampleData?: any;
// }

// const initialState: DxpProductDetailState = {
//   loading: false,
//   error: null,
//   data: null,
// };

// export const dxpProductDetailStore = signalStore(
//   { providedIn: 'root' },
//   withState(initialState),
//   withMethods((store) => {
//     // inject ApiService in a valid injection context
//     const apiService = inject(ApiService);
//     return {
//       async fetchProductDetail(productId: string) {
//         patchState(store, { loading: true, error: null });
//         try {
//           const response = await apiService.getMethodRequest(
//             `${dxpApi.productDetail}${productId}?cache=true`
//           ).toPromise();
//           console.log('DXP Product Detail Response:', response);
//           patchState(store, { data: response, loading: false });
//         } catch (error: any) {
//           patchState(store, { error: error?.message || 'Error fetching product detail', loading: false });
//         }
//       },
//       async fetchSampleData(productId: string, assetId: string) {
//         patchState(store, { loading: true, error: null });
//         try {
//           const endpoint = `content-type/dxp/product/${productId}/asset/${assetId}/sample`;
//           const response = await apiService.getMethodRequest(endpoint).toPromise();
//           console.log('Sample Data Response:', response);
//           patchState(store, { sampleData: response, loading: false });
//         } catch (error: any) {
//           patchState(store, { error: error?.message || 'Error fetching sample data', loading: false });
//         }
//       },
//       clear() {
//         patchState(store, { sampleData: undefined });
//       }
//     };
//   })
// );

import { signalStore, withState, withMethods, patchState } from '@ngrx/signals';
import { inject } from '@angular/core';
import { ApiService } from 'src/app/scad-insights/core/services/api.service';
import { dxpApi } from '../dxp.constants';
import { lastValueFrom } from 'rxjs';

export interface DxpProductDetailState {
  loading: boolean;
  error: string | null;
  data: any;
}

export interface DxpSampleDataState {
  loading: boolean;
  error: string | null;
  data: any;
}

const initialState: DxpProductDetailState = {
  loading: true,
  error: null,
  data: null
};

const sampleDataState: DxpSampleDataState = {
  loading: true,
  error: null,
  data: undefined,
};

export const dxpProductDetailStore = signalStore(
  { providedIn: 'root' },
  withState(initialState),
  withMethods((store) => {
    const apiService: ApiService = inject(ApiService);
    return {
      async fetchProductDetail(productId: string) {
        patchState(store, { loading: true, error: null });
        try {
          const response = await lastValueFrom(apiService.getMethodRequest(
            `${dxpApi.productDetail}${productId}?cache=true`
          ), initialState.data);
          patchState(store, { data: response, loading: false });
        } catch (error: any) {
          patchState(store, { error: error?.message ?? 'Error fetching product detail', loading: false });
        }
      },

      // async fetchSampleData(productId: string, assetId: string) {
      //   patchState(store, { loading: true, error: null });
      //   try {
      //     const endpoint = `content-type/dxp/product/${productId}/asset/${assetId}/sample`;
      //     const response = await lastValueFrom(apiService.getMethodRequest(endpoint));
      //     patchState(store, { sampleData: response, loading: false });
      //   } catch (error: any) {
      //     patchState(store, { error: error?.message || 'Error fetching sample data', loading: false });
      //   }
      // },

      // clear() {
      //   patchState(store, { sampleData: undefined });
      // }
    };
  })
);

export const sampleDataStore = signalStore(
  { providedIn: 'root' },
  withState(sampleDataState),
  withMethods((store) => {
    const apiService: ApiService = inject(ApiService);
    return {
      async fetchSampleData(productId: string, assetId: string) {
        patchState(store, { loading: true, error: null });
        try {
          const endpoint = `content-type/dxp/product/${productId}/asset/${assetId}/sample`;
          const response = await lastValueFrom(apiService.getMethodRequest(endpoint));
          patchState(store, { data: response, loading: false });
        } catch (error: any) {
          patchState(store, { error: error?.message || 'Error fetching sample data', loading: false });
        }
      },

      clear() {
        patchState(store, { data: undefined });
      }
    }
  })
)
