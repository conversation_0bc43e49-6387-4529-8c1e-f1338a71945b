import { Routes } from '@angular/router';

export const dxpRoutes: Routes = [
  {
    path: '',  loadComponent: () => import('src/app/dxp/dxp-landing-page/dxp-landing-page.component').then((m: typeof import('src/app/dxp/dxp-landing-page/dxp-landing-page.component')) => m.DxpLandingPageComponent),
    children: [
      {
        path:'',
        loadComponent: () => import('src/app/dxp/dxp-indicator/dxp-indicator.component').then((m: typeof import('src/app/dxp/dxp-indicator/dxp-indicator.component')) => m.DxpIndicatorComponent),
      },
      {
        path:'catalogue',
        loadComponent: () => import('src/app/dxp/dxp-catalog/dxp-catalog.component').then((m: typeof import('src/app/dxp/dxp-catalog/dxp-catalog.component')) => m.DxpCatalogComponent),
      }
    ]
  },
    {
    path:'visualization-wizard',
    loadComponent: () => import('src/app/dxp/dxp-visualization-wizard/dxp-visualization-wizard.component').then((m: typeof import('src/app/dxp/dxp-visualization-wizard/dxp-visualization-wizard.component')) => m.DxpVisualizationWizardComponent),
  },
  {
    path:'admin-panel',
    loadComponent: () => import('src/app/dxp/dxp-admin-panel/dxp-admin-panel.component').then((m: typeof import('src/app/dxp/dxp-admin-panel/dxp-admin-panel.component')) => m.DxpAdminPanelComponent),
  },
  {
    path:'product',
    loadComponent: () => import('src/app/dxp/dxp-product-detail/dxp-product-detail.component').then((m: typeof import('src/app/dxp/dxp-product-detail/dxp-product-detail.component')) => m.DxpProductDetailComponent),
  },
  {
    path:'publish',
    loadComponent: () => import('src/app/dxp/dashboard-publish/dashboard-publish.component').then((m: typeof import('src/app/dxp/dashboard-publish/dashboard-publish.component')) => m.DashboardPublishComponent),
  }
];


