import { ComponentFixture, TestBed } from '@angular/core/testing';

import { GenAiDashboardUploadComponent } from './gen-ai-dashboard-upload.component';

describe('GenAiDashboardUploadComponent', () => {
  let component: GenAiDashboardUploadComponent;
  let fixture: ComponentFixture<GenAiDashboardUploadComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [GenAiDashboardUploadComponent]
    })
    .compileComponents();

    fixture = TestBed.createComponent(GenAiDashboardUploadComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
