<div class="ifp-irg-compare" [class]="'ifp-irg-compare--'+currentTheme()">
  @if (headerEnable()) {
  <div class="ifp-ai-report__module-header">
    <h2 class="ifp-ai-report__module-title">{{heading() | translate}}</h2>
    @if (preview()) {
      <ifp-ai-button [disableTranslate]="true" [iconClass]="'ifp-icon-edit'" [theme]="'ifp-ai-button--round'" [label]="'Edit'" (ifpClick)="edit.emit()"></ifp-ai-button>
    }
  </div>
}
  <!-- comparison table start -->
   <div class="ifp-irg-compare__table-outer">
    <div class="ifp-irg-compare__table">
      <!-- comparison table header start -->
      <div class="ifp-irg-compare__table-row " [ngClass]="{'ifp-irg-compare__table-row--head': currentTheme() !=='normal'}">
        @for (item of header; track $index; let index = $index) {

        @if ( index ===1 ) {
        <div class="ifp-irg-compare__table-col " [ngClass]="{'ifp-irg-compare__table-col--official': currentTheme() !=='normal'}">
          <!-- @if (currentTheme() !=='normal') {

              <em class="ifp-icon ifp-icon-verifyed-tick ifp-irg-compare__table-icon"></em>

          } -->
          {{item| translate}}
        </div>
      } @else {
        <div class="ifp-irg-compare__table-col">{{item| translate}}</div>
      }
      }
      </div>
      <!-- comparison table header end -->

      <!-- comparison table body start -->
      @for (itemComparision of compairData()?.rows; track $index) {
      <div class="ifp-irg-compare__table-row">
        @for (item of keys; track $index; let index = $index) {
              @if ( index ===1 ) {
                <div class="ifp-irg-compare__table-col "
                [ngClass]="{'ifp-irg-compare__table-col--official': currentTheme() !=='normal'}"
                >{{(itemComparision[item].value ?? itemComparision[item] ) | number:'1.0-3'}}

                @if ('latest_value' === item  &&itemComparision['current_period'] ) {
                  <div>
                    ({{itemComparision['current_period'] }})
                  </div>
                }
                @if (itemComparision[item].progress == false) {
                  <em class="ifp-icon ifp-icon-arrow-us-down ifp-irg-compare__table-icon ifp-irg-compare__table-icon--red"></em>
                } @else if(itemComparision[item].progress == true){
                  <em class="ifp-icon ifp-icon-arrow-us-up ifp-irg-compare__table-icon ifp-irg-compare__table-icon--green"></em>
                }
              </div>
              } @else {
              <div class="ifp-irg-compare__table-col">
                @if ('indicator' === item ) {
                  {{(itemComparision[item].value ??itemComparision[item] ) }}
                  @if(itemComparision['unit'] && itemComparision['unit'] !== '') {
                 ({{itemComparision['unit']}})
                  }
                } @else {
                  {{(itemComparision[item].value ??itemComparision[item] )| number:'1.0-3' }}
                  @if (item === 'percentage_change')  {  %}
                  @if (item === 'percentage_change')  {
                    @if (itemComparision['percentage_change_progress'] == false) {
                      <em class="ifp-icon ifp-icon-arrow-us-down ifp-irg-compare__table-icon ifp-irg-compare__table-icon--red"></em>
                    } @else if(itemComparision['percentage_change_progress'] == true){
                      <em class="ifp-icon ifp-icon-arrow-us-up ifp-irg-compare__table-icon ifp-irg-compare__table-icon--green"></em>
                    }

                  }
                  @if ('previous_value' === item  && itemComparision['previous_period']) {
                    <div>
                      ({{itemComparision['previous_period'] }})
                    </div>
                  }
                }
                @if (itemComparision[item].progress == false) {
                  <em class="ifp-icon ifp-icon-arrow-us-down ifp-irg-compare__table-icon ifp-irg-compare__table-icon--red"></em>
                } @else if(itemComparision[item].progress == true){
                  <em class="ifp-icon ifp-icon-arrow-us-up ifp-irg-compare__table-icon ifp-irg-compare__table-icon--green"></em>
                }

              </div>
            }

          }

      </div>
    }
      <!-- comparison table body end -->


    </div>
  </div>
  <!-- comparison table end -->

</div>
