import { Injectable } from '@angular/core';
import { HttpService } from '../http/http.service';
import { domainNavigationApi } from '../../apiConstants/domains.api.constants';
import { Subject } from 'rxjs';
import { BasicService } from '../basicFunction.service';

@Injectable({
  providedIn: 'root'
})
export class DomainsService {

  clearSearch$ = new Subject<boolean>();
  clickDomain$ = new Subject<string>();


  constructor(private _http: HttpService, private _basic: BasicService) { }

  getCategories(searchString:string) {
    const endPoint= searchString ? `${domainNavigationApi.domainNavigationList}?search=${searchString}` : domainNavigationApi.domainNavigationList;
    return this._http.get(endPoint);
  }

  getNewsLetter() {
    return this._http.get(domainNavigationApi.newsLetter);
  }

  getDomainList(id: string) {
    return this._http.get(domainNavigationApi.domainList + id);
  }

  getDomainIconList() {
    return this._http.get(domainNavigationApi.domainIconList);
  }

  getDomainIconListPreview(token: string, lang='en') {
    return this._http.previewGet(domainNavigationApi.domainIconList, token ,lang);
  }

  getDomainIconDetail(id: number) {
    return this._http.get(domainNavigationApi.domainIconDetail + id);
  }

  getSubDomainDetail(id: number) {
    return this._http.get(domainNavigationApi.subDomainDetails + id);
  }

  getSubThemeDetail(id: number) {
    return this._http.get(domainNavigationApi.subThemeDetails + id);
  }

  getFilter(contentType: string, id: string) {
    return this._http.get(domainNavigationApi.filter + contentType + domainNavigationApi.filterApi + id);
  }

  getDomainDetailClassification(id: number, classification: string = '') {
    return this._http.get(`${domainNavigationApi.domainClassifications}${id}?classification=${classification}`);
  }

  getDomainDetailFilter(id: any, classificationId: any) {
    return this._http.get(`${domainNavigationApi.domainFilter + id}?classification=${classificationId}`);
  }

  getDomainDetail(id: any, params: any = {}) {
    const paramsValue = this._basic.getObjectAsParams(params);
    return this._http.get(domainNavigationApi.domainListV2 + id + paramsValue);
  }

  getLiveabilityDashboard(payload: { id: number, parent_category: string, category: string }) {
    let endpoint = domainNavigationApi.liveability + payload.id;
    if (payload.parent_category) {
      endpoint = `${endpoint}?parent_category=${payload.parent_category}`;
    }
    if (payload.category) {
      endpoint = `${endpoint}&category=${payload.category}`;
    }
    return this._http.get(endpoint);
  }

}
