import { Component, inject } from '@angular/core';
import { IfpBgPageComponent } from "../../scad-insights/ifp-widgets/ifp-organism/ifp-bg-page/ifp-bg-page.component";
import { IfpBreadcrumbsComponent } from "../../scad-insights/ifp-widgets/ifp-molecules/ifp-breadcrumbs/ifp-breadcrumbs.component";
import { PageData } from 'src/app/scad-insights/core/interface/molecule/breadcrumb.interface';
import { TranslateModule } from '@ngx-translate/core';
import { IFPHighChartsComponent } from "../../scad-insights/ifp-widgets/charts/ifp-highcharts.component";
import { IfpDropdownComponent } from "../../scad-insights/ifp-widgets/ifp-atoms/ifp-dropdown/ifp-dropdown.component";
import { DxpKpiCustomFilter } from '../dxp-visualization-wizard-toolbar/dxp-visualization-filter-form/dxp-visualization-filter-form.component';
import { IfpIconTextComponent } from "../../scad-insights/ifp-widgets/ifp-molecules/ifp-icon-text/ifp-icon-text.component";
import { IfpUserTagComponent } from "../../scad-insights/ifp-widgets/ifp-atoms/ifp-user-tag/ifp-user-tag.component";
import { IfpUserTagGroupComponent, UserDetail } from "../../scad-insights/ifp-widgets/ifp-molecules/ifp-user-tag-group/ifp-user-tag-group.component";
import { IfpButtonComponent } from "../../scad-insights/ifp-widgets/ifp-atoms/ifp-button/ifp-button.component";
import { buttonClass } from 'src/app/scad-insights/core/constants/button.constants';
import { DatePipe, NgClass } from '@angular/common';
import { IfpInputAutoResizeDirective } from 'src/app/scad-insights/core/directives/ifp-input-auto-height.directive';
import { IFPMsalService } from 'src/app/scad-insights/core/services/IFP-msal.service';
import { IfpAbbreviationTagComponent } from "../../scad-insights/ifp-widgets/ifp-atoms/ifp-abbreviation-tag/ifp-abbreviation-tag.component";
import { FormsModule } from '@angular/forms';
import { dateFormat } from 'src/app/scad-insights/core/constants/date.constants';

@Component({
  selector: 'ifp-dxp-admin-panel',
  imports: [IfpBgPageComponent, IfpBreadcrumbsComponent, TranslateModule, IFPHighChartsComponent, IfpDropdownComponent, IfpIconTextComponent, IfpUserTagComponent, IfpUserTagGroupComponent, IfpButtonComponent, NgClass, IfpInputAutoResizeDirective, IfpAbbreviationTagComponent, FormsModule, DatePipe],
  templateUrl: './dxp-admin-panel.component.html',
  styleUrl: './dxp-admin-panel.component.scss'
})
export class DxpAdminPanelComponent {

  private readonly _msalService: IFPMsalService = inject(IFPMsalService);
  public currentUserEmail = this._msalService.getLoginData.account.username;
  public currentUserName = this._msalService.getLoginData.account.name;
  public chartData = {
    category: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
    series: [{
      name: 'Sample Data',
      data: [29.9, 71.5, 106.4, 129.2, 144.0, 176.0, 135.6, 148.5, 216.4, 194.1, 95.6, 54.4]
  }]
  };

  pageData: PageData[] = [
    {
      title: 'Home',
      route: '/home'
    },
    {
      title: 'Government Affairs',
      route: '/dxp'
    },
    {
      title: 'Abu Dhabi Executive Office (ADEO)',
      route: ''
    }
  ];
  public sharedUserList: UserDetail[] = [
    {
      name: 'Ahmad Nadheem',
      email: '<EMAIL>'
    },
    {
      name: 'Test User',
      email: '<EMAIL>'
    },
    {
      name: 'Nadheem Ahmad',
      email: '<EMAIL>'
    },
    {
      name: 'User Test',
      email: '<EMAIL>'
    }
  ];

  public comments: Comment[] = [
  {
    id: 264,
    email: "<EMAIL>",
    comment: "Please recheck the the date added in the KPI",
    date: "2025-05-13T06:02:18.000Z",
    username: "Layla Al Mazrouei"
  },
  {
    id: 263,
    email: "<EMAIL>",
    comment: "Updated please check",
    date: "2025-05-13T06:01:20.000Z",
    username: "Mohamed Asarudheen"
  }
]
  public dateFormat = dateFormat;
  public title = 'Employee productivity (% of GDP at constant prices)';
  public description = 'Measures the economic output generated per employee, adjusted for inflation, as a percentage of GDP.';
  public filters: DxpKpiCustomFilter[] = [];
  public buttonClass = buttonClass;
  public hideSideBar: boolean = true;
  public newComment: string = '';

  addComment() {
    this.comments.push({
      email: this.currentUserEmail,
      comment: this.newComment.trim(),
      date: new Date().toString(),
      username: this.currentUserName
    });
    this.newComment = '';
  }

}



interface Comment {
  id?: number;
  email: string;
  comment: string;
  date: string;
  username: string;
}
