import { Component, inject, OnInit, signal } from '@angular/core';
import { IfpBgPageComponent } from '../../scad-insights/ifp-widgets/ifp-organism/ifp-bg-page/ifp-bg-page.component';
import { IfpBreadcrumbsComponent } from '../../scad-insights/ifp-widgets/ifp-molecules/ifp-breadcrumbs/ifp-breadcrumbs.component';
import { PageData } from 'src/app/scad-insights/core/interface/molecule/breadcrumb.interface';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { IFPHighChartsComponent } from '../../scad-insights/ifp-widgets/charts/ifp-highcharts.component';
import { IfpDropdownComponent } from '../../scad-insights/ifp-widgets/ifp-atoms/ifp-dropdown/ifp-dropdown.component';
import { DxpKpiCustomFilter } from '../dxp-visualization-wizard-toolbar/dxp-visualization-filter-form/dxp-visualization-filter-form.component';
import { IfpIconTextComponent } from '../../scad-insights/ifp-widgets/ifp-molecules/ifp-icon-text/ifp-icon-text.component';
import { IfpUserTagComponent } from '../../scad-insights/ifp-widgets/ifp-atoms/ifp-user-tag/ifp-user-tag.component';
import {
  IfpUserTagGroupComponent,
  UserDetail,
} from '../../scad-insights/ifp-widgets/ifp-molecules/ifp-user-tag-group/ifp-user-tag-group.component';
import { IfpButtonComponent } from '../../scad-insights/ifp-widgets/ifp-atoms/ifp-button/ifp-button.component';
import { buttonClass } from 'src/app/scad-insights/core/constants/button.constants';
import { DatePipe, NgClass } from '@angular/common';
import { IfpInputAutoResizeDirective } from 'src/app/scad-insights/core/directives/ifp-input-auto-height.directive';
import { IFPMsalService } from 'src/app/scad-insights/core/services/IFP-msal.service';
import { IfpAbbreviationTagComponent } from '../../scad-insights/ifp-widgets/ifp-atoms/ifp-abbreviation-tag/ifp-abbreviation-tag.component';
import { FormsModule } from '@angular/forms';
import { dateFormat } from 'src/app/scad-insights/core/constants/date.constants';
import { IfpToggleButtonComponent } from 'src/app/scad-insights/ifp-widgets/ifp-atoms/ifp-toggle-button/ifp-toggle-button.component';
import { ApiService } from 'src/app/scad-insights/core/services/api.service';
import {
  approveConst,
  approveStatus,
  dxpApi,
  generalizedRoles,
} from '../dxp.constants';
import { ActivatedRoute, Router } from '@angular/router';
import { SubSink } from 'subsink';
import {
  ConditionDxp,
  DxpComments,
  DxpDetail,
  DxpFilterConfig,
  DxpFilterPanel2,
  GroupDxp,
  LegendPanelDxpDetail,
} from '../dxp.interface';
import { IfpSpinnerComponent } from 'src/app/scad-insights/ifp-widgets/ifp-molecules/ifp-spinner/ifp-spinner.component';
import { ifpColors } from 'src/app/scad-insights/core/constants/color.constants';
import { IfpNoDataComponent } from 'src/app/scad-insights/ifp-widgets/ifp-molecules/ifp-no-data/ifp-no-data.component';
import { IfpMarkDownComponent } from 'src/app/scad-insights/ifp-chat-bot/ifp-mark-down/ifp-mark-down.component';
import { DxpValidationPopUpComponent } from '../dxp-validation-pop-up/dxp-validation-pop-up.component';
import { IfpModalComponent } from 'src/app/scad-insights/ifp-widgets/ifp-organism/ifp-modal/ifp-modal.component';
import { IfpCardLoaderComponent } from "src/app/scad-insights/ifp-widgets/ifp-atoms/ifp-card-loader/ifp-card-loader.component";

@Component({
  selector: 'ifp-dxp-admin-panel',
  imports: [
    IfpBgPageComponent,
    IfpBreadcrumbsComponent,
    TranslateModule,
    IFPHighChartsComponent,
    IfpDropdownComponent,
    IfpIconTextComponent,
    IfpUserTagComponent,
    IfpUserTagGroupComponent,
    IfpButtonComponent,
    NgClass,
    IfpInputAutoResizeDirective,
    IfpAbbreviationTagComponent,
    FormsModule,
    DatePipe,
    IfpToggleButtonComponent,
    IfpSpinnerComponent,
    IfpNoDataComponent,
    IfpMarkDownComponent,
    DxpValidationPopUpComponent,
    IfpModalComponent,
    IfpCardLoaderComponent
],
  templateUrl: './dxp-admin-panel.component.html',
  styleUrl: './dxp-admin-panel.component.scss',
})
export class DxpAdminPanelComponent implements OnInit {
  private readonly _msalService: IFPMsalService = inject(IFPMsalService);
  public currentUserEmail = this._msalService.getLoginData.account.username;
  public currentUserName = this._msalService.getLoginData.account.name;
  public _apiService = inject(ApiService);
  public _activeRoute = inject(ActivatedRoute);

  public data = signal<DxpDetail | undefined>(undefined);
  public enableReview = signal(false);
  public loader = signal(true);
  public chartLoader = signal(false);
  public chartType:Record<string, string> = {
    line: 'line',
    column:'column',
    bar:'bar',
    pie:'pie',
    donut:'pie'
  }

  pageData: PageData[] = [
    {
      title: 'Home',
      route: '/home',
    },
    {
      title: 'Government Affairs',
      route: '/dxp',
    },
    {
      title: 'Abu Dhabi Executive Office (ADEO)',
      route: '',
    },
  ];

  public comments = signal<DxpComments[] | []>([]);
  public dateFormat = dateFormat;
  public buttonClass = buttonClass;
  public hideSideBar = signal(true);
  public newComment: string = '';
  public currentStatus = signal('');
  public subs = new SubSink();
  public id = signal('');
  public filter: GroupDxp[] = [];
  public approveConst = approveConst;
  public enableChat = signal(false);
  private _translate = inject(TranslateService);
  public legends: Highcharts.LegendOptions = {
    rtl: this._translate.currentLang === 'ar',
    itemStyle: {
      fontSize: '14px', // set the desired font size here,
      fontFamily: 'Noto Sans',
      fontWeight: '600',
    },
    enabled: true,
    align: 'center',
  };

  public xAxis = signal<Highcharts.XAxisOptions>({});
  public approveStatus = approveStatus;
  public generalizedRoles = generalizedRoles;
  public defaultLegend = signal<LegendPanelDxpDetail | undefined>(undefined);
  addComment() {
    if (this.currentStatus() && this.currentStatus() !== '') {
      this.currentStatusUpdate();
      this.currentStatus.set('');
    } else {
      this.addCommentApi(this.newComment.trim());
      this.newComment = '';
    }
  }

  addCommentApi(data: string) {
    this.subs.add(
      this._apiService
        .postMethodRequest(
          dxpApi.getCommentList(this.data()?.approvalRequest?.id ?? ''),
          {
            content: data,
          }
        )
        .subscribe((data) => {
          this.getComments();
        })
    );
  }

  legendChanges(event: LegendPanelDxpDetail) {
    this.defaultLegend.set(event);
    this.filterData();
  }

  currentStatusUpdate(status = this.currentStatus()) {
    this.subs.add(
      this._apiService
        .postMethodRequest(
          dxpApi.approve(this.data()?.approvalRequest?.id ?? ''),
          {
            action: status,
          }
        )
        .subscribe((data) => {
          if (
            status !== approveConst.claim &&
            status !== approveConst.unclaim
          ) {
            this.addCommentApi(this.newComment.trim());
          } else {
            this.getComments();
          }

          this.newComment = '';
        })
    );
  }
  detailApiCall() {
    this.subs.add(
      this._apiService
        .getMethodRequest(dxpApi.getDetails(this.id()))
        .subscribe((data: DxpDetail) => {
          this.enableReview.set(
            data?.approvalRequest?.assigneeId ? true : false
          );
          this.data.set(data);
          this.data()?.filterPanel.forEach((filterData) => {
            filterData['selected'] = [filterData.default];
          });
          this.defaultLegend.set(
            this.data()?.legendPanel?.find((data) => data.default)
          );
          this.xAxis.set({
            lineColor: '#D9DCDD',
            tickColor: '#D9DCDD',
            type: 'category',
            tickLength: 1,
            categories: data?.series?.xAxis?.categories ?? [],
            gridLineWidth: 30,
            lineWidth: 1,
            gridLineColor: '#F8F8F8',
            tickPosition: 'outside',
            startOnTick: false,
            endOnTick: false,
            offset: 30,
            title: {
              text: '',
              style: {
                fontSize: '15px',
                fontWeight: '500',
                color: ifpColors.primaryGrey,
              },
            },
            labels: {
              formatter: (value: any) => {
                return value.value;
              },
            },
          });
          this.getComments();
          this.loader.set(false);
        })
    );
  }

  changeSubFilter(event: string[]) {
    this.filterData();
  }

  getComments() {
    this.subs.add(
      this._apiService
        .getMethodRequest(
          dxpApi.getCommentList(this.data()?.approvalRequest?.id ?? '')
        )
        .subscribe((data: DxpComments[]) => {
          this.comments.set(data);
          this.enableChat.set(true);
        })
    );
  }

  changeFilter(data: string[], filter: DxpFilterPanel2) {
    filter['selected'] = data;
    const conditions: ConditionDxp[] = [];
    this.data()?.filterPanel.forEach((filterData) => {
      conditions.push({
        column: filterData.column,
        data_type: filterData.data_type,
        comparator: 'in',
        value: filterData?.selected ?? [],
      });
    });

    this.filter = [{ conditions: conditions, operator: 'and' }];
    this.filterData();
  }

  filterData() {
    this.chartLoader.set(true);
    this.subs.add(
      this._apiService
        .postMethodRequest(
          dxpApi.getFilterData(
            this.data()?.product.id ?? '',
            this.data()?.asset?.id ?? ''
          ),
          {
            filters: {
              groups: [
                ...(this.data()?.visualizationConfig?.source_filter?.groups ??
                  []),
                ...this.filter,
              ],
              global_operator:
                this.data()?.visualizationConfig?.source_filter
                  ?.global_operator,
            },
            x_axis:
              this.data()?.visualizationConfig?.chart_configuration?.x_axis,
            y_axis:
              this.data()?.visualizationConfig?.chart_configuration?.y_axis,
            legend: {
              column: this.defaultLegend()?.column,
              data_type: this.defaultLegend()?.data_type,
              selected_values: this.defaultLegend()?.selected_values,
            },
          }
        )
        .subscribe((dxpFilterData: DxpFilterConfig) => {
          this.data.update((data) => {
            this.chartLoader.set(false);
            if (data) {
              data['series'] = dxpFilterData?.series;
              this.xAxis.set({
                lineColor: '#D9DCDD',
                tickColor: '#D9DCDD',
                type: 'category',
                tickLength: 1,
                categories: this.data()?.series?.xAxis?.categories ?? [],
                gridLineWidth: 30,
                lineWidth: 1,
                gridLineColor: '#F8F8F8',
                tickPosition: 'outside',
                startOnTick: false,
                endOnTick: false,
                offset: 30,
                title: {
                  text: '',
                  style: {
                    fontSize: '15px',
                    fontWeight: '500',
                    color: ifpColors.primaryGrey,
                  },
                },
                labels: {
                  formatter: (value: any) => {
                    return value.value;
                  },
                },
              });
            }
            return data;
          });
        })
    );
  }

  ngOnInit(): void {
    this.subs.add(
      this._activeRoute.params.subscribe((params) => {
        this.id.set(params['id']);
        this.detailApiCall();
      })
    );
  }
}
