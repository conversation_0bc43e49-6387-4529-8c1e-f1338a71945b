@use '../../../../assets/ifp-styles/abstracts' as *;
.ifp-domain-detail {
  &__heading-wrapper {
    display: flex;
    font-size: $ifp-fs-11;
    font-weight: $fw-bold;
    margin: 0 (-$spacer-1);
    align-items: center;
  }
  &__heading,&__heading-icon {
    margin: 0 $spacer-1;
    font-size: inherit;
  }
  &__head-img{
    margin-right: $spacer-2;
  }
  &__indicator-tab {
    padding-bottom: $spacer-3;
  }
  &__arrow{
    font-size: $ifp-fs-8;
  }
  &__sub-title{
    font-size:$ifp-fs-8 ;
    font-weight: $fw-bold;
    margin-left: $spacer-5;
    margin-top: $spacer-2;
  }
  &__indicator {
    margin-bottom:$spacer-3 ;
    background-color: $ifp-color-white;
    ::ng-deep {
      .ifp-section {
        border: none;
        margin: $spacer-0 (-$spacer-2);
      }
    }
  }
}

:host-context([dir="rtl"]){
  .ifp-domain-detail {

    &__head-img{
      margin-right: $spacer-2;
      margin-left: 0;
    }
    &__sub-title{
      margin-right:  $spacer-5;
      margin-left: 0;
    }
  }
}

@include mobile-tablet {
  .ifp-domain-detail {
    &__heading-wrapper {
      font-size: $ifp-fs-6;
    }
  }
}
