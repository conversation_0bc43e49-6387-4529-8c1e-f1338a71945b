import { NgClass } from '@angular/common';
import { Component, forwardRef, input, model, OnDestroy, OnInit, output } from '@angular/core';
import { ControlValueAccessor, FormControl, NG_VALUE_ACCESSOR, ReactiveFormsModule } from '@angular/forms';
import { Subscription } from 'rxjs';

@Component({
  selector: 'ifp-ai-input',
  imports: [ReactiveFormsModule,NgClass],
  templateUrl: './ai-input.component.html',
  styleUrl: './ai-input.component.scss',
  providers:[ {
    provide: NG_VALUE_ACCESSOR,
    multi: true,
    useExisting: forwardRef(() => AiInputComponent),
  },]
})
export class AiInputComponent implements ControlValueAccessor, OnInit, OnDestroy{
  public theme = input<string>('');
  public input= input<boolean>(true);
  public animation =input(false);
  public disable = input(false);
  public submitInput = output();
  public inputValue = new FormControl('');
  public formControlSubscription!: Subscription;
  // Allow the input to be disabled, and when it is make it somewhat transparent.
  public disabled = model(false);

  // Function to call when the rating changes.
  onChange = (inputValue: string | null) => {};

  // Function to call when the input is touched (when a star is clicked).
  onTouched = () => {};

  // Allows Angular to update the model (rating).
  // Update the model and changes needed for the view here.
  writeValue(inputValue: string | null): void {
    this.inputValue.setValue(inputValue);
    this.onChange(this.inputValue.value);
  }

  // Allows Angular to register a function to call when the model (rating) changes.
  // Save the function as a property to call later here.
  registerOnChange(fn: (inputValue: string | null) => void): void {
    this.onChange = fn;
  }

  // Allows Angular to register a function to call when the input has been touched.
  // Save the function as a property to call later here.
  registerOnTouched(fn: () => void): void {
    this.onTouched = fn;
  }

  // Allows Angular to disable the input.
  setDisabledState(isDisabled: boolean): void {
    this.disabled.set(isDisabled);
  }
  ngOnInit(): void {
    this.formControlSubscription = this.inputValue.valueChanges.subscribe(value => {
      this.onTouched();
      this.onChange(value);
    });

  }

  submitClick(event: KeyboardEvent) {
    if(event.key === "Enter" || event.key === "Tab") {
          this.submitInput.emit();
         ( event.target as HTMLElement).blur();
    }

  }
  ngOnDestroy(): void {
    this.formControlSubscription.unsubscribe();
  }
}
