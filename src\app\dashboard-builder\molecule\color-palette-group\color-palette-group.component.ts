import { NgClass } from '@angular/common';
import { Component, input, model, output, Signal, viewChild } from '@angular/core';
import { ifpColors } from 'src/app/scad-insights/core/constants/color.constants';

@Component({
  selector: 'ifp-color-palette-group',
  imports: [NgClass],
  templateUrl: './color-palette-group.component.html',
  styleUrl: './color-palette-group.component.scss'
})
export class ColorPaletteGroupComponent {

  public colorPicker: Signal<HTMLInputElement> = viewChild.required('colorPicker');

  public selectColor = output<string>();

  public colorList = input(Object.values(ifpColors));
  public customSelectedColor = 'transparent';
  public selectedColor = model<string>(ifpColors.white)

  onSelectColorPicker(event: any) {
    this.customSelectedColor = event.value;
    this.selectedColor.set(event.value);
  }

  onSelectColor(color: string) {
    this.selectedColor.set(color);
  }
}
