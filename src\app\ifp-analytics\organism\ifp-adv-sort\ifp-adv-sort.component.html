@if (!workFlowRunning() && !emptyConnection() && currentConection() && sourceData() && columnList && columnList.length !==0 && !errorMessageForTools) {
  <ifp-data-tool-accordian [title]="'New Sort'" [enableCheckBox]="false" [isAccordianExpanded]="true" class="ifp-adv-tool__accordian ifp-adv-tool__accordian--tool-box">
    <div class="ifp-accord-box__content">
      <form [formGroup]="sortForm" class="ifp-adv-sort__form">
        <div class="ifp-adv-tool__edit-outer">
          <p class="ifp-adv-tool__label">{{'Select Column' | translate}}</p>
          @if (columnList && columnList.length) {
            <app-ifp-dropdown class="ifp-adv-tool__dropdown" [singleDefaultSelect]="false" [selectedValue]="selectedColumn" [key]="'name'" [dropDownItems]="columnList"
            (dropDownItemClicked)="selectColumn($event)" formControlName="column" [disableSingleValue]="true" [disableTranslation]="true"></app-ifp-dropdown>
            @if (getControls['column'].invalid && submitted()) {
              <p class="ifp-adv-tool__error"><em class="ifp-icon ifp-icon-exclamation-round"></em>{{'Please select a column' | translate}}</p>
            }
          }
        </div>

        <div class="ifp-adv-tool__edit-outer">
          <p class="ifp-adv-tool__label">{{'Data Type' | translate}}: <span class="ifp-adv-tool__label-value">{{selectedDatatype | translate}}</span></p>
        </div>

        <div class="ifp-adv-tool__edit-outer">
          <p class="ifp-adv-tool__label">{{'Sort Order' | translate}}</p>
          <app-ifp-dropdown class="ifp-adv-tool__dropdown" [singleDefaultSelect]="false" [selectedValue]="selectedSort" [key]="'name'" [dropDownItems]="sortOrder"
            (dropDownItemClicked)="selectOrder($event)" formControlName="order" [disableSingleValue]="true" [disableTranslation]="false">
          </app-ifp-dropdown>
          @if ((submitted()) && getControls['order'].errors?.['required']) {
            <p class="ifp-adv-tool__error"><em class="ifp-icon ifp-icon-exclamation-round"></em>{{'Please select an aggregation method' | translate}}</p>
          }
        </div>
        @if (isSelectActive) {
          <div class="ifp-adv-tool__update">
            <div class="ifp-adv-tool__action-inner">
              <ifp-button [label]="'cancel'" (ifpClick)="resetForm()" class="ifp-adv-tool__button ifp-adv-tool__action-btn" [buttonClass]="buttonClass.secondary"></ifp-button>
              <ifp-button [label]="'Update'" (ifpClick)="addSort()" class="ifp-adv-tool__button ifp-adv-tool__action-btn" [buttonClass]="buttonClass.primary"></ifp-button>
            </div>
          </div>
        } @else {
          <ifp-button [label]="'Add Sort'" (ifpClick)="addSort()" class="ifp-adv-tool__button"
          [buttonClass]="(sortForm.valid ? buttonClass.secondary : buttonClass.disabled)+ ' '+ buttonClass.large" [iconClass]="'ifp-icon-plus'"></ifp-button>
        }
      </form>
    </div>
  </ifp-data-tool-accordian>
  @if (sortList.length) {
    <div class="ifp-adv-tool__content ifp-adv-tool__content--grey">
      <p class="ifp-adv-tool__sub-title">{{'Sorted Columns' | translate}}</p>
      @for (sort of sortList; track sort) {
        <div class="ifp-adv-tool__card" (click)="selectSort(sort, $index)">
        <div class="ifp-adv-tool__card-head">
          <p class="ifp-adv-tool__card-title">{{sort.column | translate}}</p>
          <div class="ifp-adv-tool__card-icons">
            <em class="ifp-icon ifp-icon-minus-round ifp-adv-tool__remove" (click)="removeSort($index)"></em>
          </div>
        </div>
        <p class="ifp-adv-tool__card-text" >{{'Sort by' | translate}}: <span class="ifp-adv-tool__card-bold">{{sort.order.name | translate}}</span></p>
        </div>
      }
    </div>
    }
} @else {
  @if(errorMessageForTools) {
    {{errorMessageForTools}}
  } @else if(workFlowRunning()) {
   <div class="ifp-center-loader">
      <app-ifp-spinner></app-ifp-spinner>
      <p>     {{'Workflow Running' | translate}} </p>
    </div>

  } @else if (emptyConnection()){
    <p class="ifp-adv-tool__error"> <em class="ifp-icon ifp-icon-exclamation-round">

    </em>
    {{'Source must have a connection!' | translate}}
    </p>
  } @else if (!sourceData()) {
    <p class="ifp-adv-tool__error"> <em class="ifp-icon ifp-icon-exclamation-round">

    </em>
    {{'You must upload at least one source!' | translate}}
    </p>
  } @else if(!currentConection()){
    <p class="ifp-adv-tool__error"> <em class="ifp-icon ifp-icon-exclamation-round">

    </em>
      {{'Node must have atleast one connection' | translate}}
    </p>
  }
   @else {
    <app-ifp-spinner></app-ifp-spinner>
  }

}

