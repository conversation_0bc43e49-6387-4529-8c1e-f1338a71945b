import { Component, input, InputSignal, output, signal } from '@angular/core';
import { DxpAccordianComponent, SelectedProduct } from "../widgets/dxp-accordian/dxp-accordian.component";
import { TranslateModule } from '@ngx-translate/core';
import { IfpSearchComponent } from "../../scad-insights/ifp-widgets/ifp-atoms/ifp-search/ifp-search.component";
import { loaderType } from 'src/app/scad-insights/core/constants/loader.constants';
import { IfpCardLoaderComponent } from "../../scad-insights/ifp-widgets/ifp-atoms/ifp-card-loader/ifp-card-loader.component";
import { IfpNoDataComponent } from "../../scad-insights/ifp-widgets/ifp-molecules/ifp-no-data/ifp-no-data.component";
import { ProductDetail } from '../dxp.interface';

@Component({
  selector: 'ifp-dxp-popup',
  imports: [DxpAccordianComponent, TranslateModule, IfpSearchComponent, IfpCardLoaderComponent, IfpNoDataComponent],
  templateUrl: './dxp-popup.component.html',
  styleUrl: './dxp-popup.component.scss'
})
export class DxpPopupComponent {
  public close = output();
  public searchAsset = output<string>();
  public selectedProductDetail = output<SelectedProduct>();

  public assetList: InputSignal<ProductDetail[]> = input.required();
  public loader = input(true);

  public searchString = signal('');
  public loaderType = loaderType;

  searchResult(value: string ){
    this.searchString.set(value);
    this.searchAsset.emit(value);
  }

  onSelecteProduct(product: SelectedProduct) {
    this.selectedProductDetail.emit(product);
  }
}

