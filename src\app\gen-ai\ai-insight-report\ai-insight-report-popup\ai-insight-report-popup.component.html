@if (data()) {
<div class="ifp-ai-insight-report-popup">
  <div class="ifp-ai-insight-report-popup__header">
    <h3 class="ifp-ai-insight-report-popup__head">{{title()}}</h3>
    <em class="ifp-icon ifp-icon-cross ifp-ai-insight-report-popup__close" (click)="close.emit()"></em>
  </div>
  @if (genarateAllPop()) {
  <div class="ifp-ai-insight-report-popup__body">
    <div class="ifp-ai-insight-report-popup__genarater-badge">
      <ifp-ai-input [disable]="modelAnimate()" [animation]="modelAnimate()" [formControl]="description" [input]="false"
        [theme]="'ifp-ai-input--ai--30 ifp-ai-input--ai--bordered ifp-ai-input--ai'"></ifp-ai-input>
    </div>

  </div>
  } @else {
  <div class="ifp-ai-insight-report-popup__body">
    @if ('keyInsightsOnly' != type()) {
    @if ('table' != type()){
    <div class="ifp-ai-insight-report-popup__body-sub">
      <div class="ifp-ai-insight-report-popup__fields">
        @if ('decription' != type() && 'header' != type()){
        <p class="ifp-ai-insight-report-popup__fields-name">
          {{'Sub heading'  }}
        </p>
        <div class="ifp-ai-insight-report-popup__text-wrapper  ifp-ai-insight-report-popup__text-area--bottom">
          <input class="ifp-ai-insight-report-popup__text-area ifp-ai-insight-report-popup__text-border"
            [formControl]="subHeading" />
        </div>
        }
        <p class="ifp-ai-insight-report-popup__fields-name">
          {{'Description'  }}
        </p>
        <div class="ifp-ai-insight-report-popup__text-wrapper ">
          @if (markdown()) {
            <ifp-mark-down-editor  [formControl]="description"></ifp-mark-down-editor>
          } @else {
            <textarea class="ifp-ai-insight-report-popup__text-area  ifp-ai-insight-report-popup__text-border" rows="6"
            [formControl]="description"></textarea>
          }


        </div>
      </div>
      @if ( type() !== 'header' &&'decription' != type()) {
      <div class="ifp-ai-insight-report-popup__card">
        @if ( type() !== 'keyInsights') {
        <h6 class="ifp-ai-insight-report-popup__sub-heading">
          {{subTitle() }}
        </h6>
        }
        <!-- <ifp-highcharts  [width]="null" [data]="[{name: 'Volatile Economic Environment', 'data': [30.5, 31.2, 32.1, 33.0, 33.5, 34.0], 'dashStyle': 'Solid', 'type': 'line', 'zIndex': 1, 'lineWidth': 2, 'color': '#FF5733', 'marker': {'fillColor': '#FF5733', 'lineWidth': 2, 'lineColor': '#FFFFFF', 'symbol': 'circle', 'enabled': true}, 'category': ['2024-09', '2024-10', '2024-11', '2024-12', '2025-01', '2025-02'], 'yAxisLabel': 'Temperature (\u00b0C)', 'xAxisLabel': 'Time (Monthly)'}]"></ifp-highcharts> -->
        <ifp-ai-chart-card class="ifp-ai-insight-card__chart" [insightReport]="true" [isCustom]="true" [isDatalabel]="true"
          [customChartData]="chart()?.charts?.[0]"></ifp-ai-chart-card>
      </div>
      }
      <ifp-ai-insight-report-genarate-badge #badge1 [disable]="modelAnimate()" [animation]="modelAnimate()" [disableTranslate]="true"
        (genarate)="this.genarateParticulardata($event, sectionOne())"
        [enableCardView]="true"></ifp-ai-insight-report-genarate-badge>
    </div>
    }
    }
    @if ('decription' != type()) {
    <h6 class="ifp-ai-insight-report-popup__sub-heading">
      {{subTitle() }}
      <!-- @if ('table' == type()|| 'keyInsightsOnly' == type() ) {
        <ifp-ai-button [label]="'Generate All'"  (ifpClick)="genarateAll.emit(type())"  [iconClass]="'ifp-icon-ai'"  [theme]="'ifp-ai-button--ai-round  ifp-ai-button--ai-secondary'" ></ifp-ai-button>
      } -->
    </h6>
    }
    @if ('table' != type() && 'decription' != type() ) {
    <div class="ifp-ai-insight-report-popup__body-sub">
      @if ('keyInsights' == type() || 'keyInsightsOnly' == type()) {

      @for (item of keyInsights(); track $index) {
      <ifp-ai-insight-analysis-card [customChartData]="item.chart?.charts?.[0]" [popup]="true" [shadow]="false"
        class="ifp-ai-insight-report-popup__sub-card" [title]="item.heading" [description]="item.text"
        [statusDown]="item.whether_positive_trend" [badge]="false"></ifp-ai-insight-analysis-card>
      }
      }
      @if ( 'header' == type()) {
      @for (item of drivers(); track $index) {
      <ifp-ai-insight-indicator-card [consolidated]="consolidated()" [firstPeroid]="item.curr_period?? ''"
        [id]="item?.domain?.id ?? ''" [name]="item?.domain?.name ?? ''" [secondPeroid]="item.pre_period ?? ''"
        class="ifp-ai-insight-report-popup__sub-card" [arrowDirection]="item.whether_positive_trend"
        [colorDirection]="item.whether_positive_trend" [percentage]="item.value" [indicatorName]="item.indicator_name"
        [title]="item.heading"></ifp-ai-insight-indicator-card>
      }
      }
      @if ('keyInsightsOnly' != type()) {
      <ifp-ai-insight-report-genarate-badge [disableTranslate]="true" #badge2 [disable]="modelAnimate()" [animation]="modelAnimate()"
        [enableCardView]="true"
        (genarate)="this.genarateParticulardata($event, sectionTwo())"></ifp-ai-insight-report-genarate-badge>
      }
    </div>
    }
    @if ('table' == type()) {
    <ifp-ai-insight-compare-module [compairData]="insightComparison()" class="ifp-ai-insight-report-popup__sub-table"
      [currentTheme]="'normal'" [headerEnable]="false"></ifp-ai-insight-compare-module>
    }
    @if ('table' == type()){
    <!-- <span  class="ifp-ai-insight-report-popup__badge-text">Do you want to generate all the content?</span> -->
    <ifp-ai-insight-report-genarate-badge [disableTranslate]="true" #badge3 [disable]="modelAnimate()" [animation]="modelAnimate()"
      (genarate)="this.genarateParticulardata($event, sectionTwo())"></ifp-ai-insight-report-genarate-badge>
    }
  </div>
  }

  <div class="ifp-ai-insight-report-popup__footer">
    <div class="ifp-ai-insight-report-popup__footer-btns">
      <ifp-button  [disableTranslate]="true"  class="ifp-ai-insight-report-popup__footer-btn" [buttonClass]="buttonClass.secondary"
        [label]="'Cancel'" (ifpClick)="close.emit()"></ifp-button>
      @if (genarateAllPop()) {
      <ifp-ai-button  [disableTranslate]="true" [loader]="modelAnimate()" class="ifp-ai-insight-report-popup__footer-btn" [label]="'Generate'"
        (ifpClick)="genarateParticulardata(description.value ?? '', 'all')" [iconClass]="'ifp-icon-ai'" [disabled]="description.value?.trim() === ''"
        [theme]="' ifp-ai-button--ai-secondary '"></ifp-ai-button>
      }@else {
      <ifp-button  [disableTranslate]="true" class="ifp-ai-insight-report-popup__footer-btn" [buttonClass]="buttonClass.primary" [label]="'Submit'"
        (ifpClick)="saveEvent()"></ifp-button>
      }

    </div>
  </div>
</div>
}
