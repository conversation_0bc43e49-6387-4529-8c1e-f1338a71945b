@use "../../../../assets/ifp-styles/abstracts/index" as *;
.ifp-auto-imp-score {
  background-color: $ifp-color-section-white;
  border-radius: 10px;
  overflow: hidden;
  &__head-wrapper {
    display: flex;
  justify-content: space-between;
  padding: $spacer-4 $spacer-5;
  align-items: center;
  @include desktop-sm {
    padding: $spacer-3 $spacer-4;
    flex-wrap: wrap;
  }
  }
  &__head {
    font-size: $ifp-fs-6;
    font-weight:  $fw-bold;
    @include desktop-sm {
      font-size: $ifp-fs-4;
    }
  }
  &__btn-analyze {
    margin-inline-end: $spacer-4;
  }
  &__content {
    border-top: 1px solid $ifp-color-grey-13;
    padding: $spacer-4 $spacer-5;
    @include desktop-sm {
      padding: $spacer-3 $spacer-4;
    }
  }
  &__content-head {
    display: flex;
    justify-content: space-between;
    &--align-right {
      justify-content: right;
    }
  }
  &__itr {
    display: flex;
    &--variables{
    margin-top: $spacer-3;

    }
  }
  &__itr-outer {
    padding-bottom: $spacer-2;
    padding-inline-end: 1px;
    @include ifp-scroll-x(transition, $ifp-color-grey-7, 5px, 5px);
  }
  &__itr-wrapper {
    padding:  $spacer-0 $spacer-5 $spacer-4;
    @include desktop-sm {
      padding: $spacer-0 $spacer-4 $spacer-4;
    }
  }
  &__chart {
    &:last-child {
      margin-inline-end: $spacer-0;
    }
    margin: $spacer-0 $spacer-2;
    width: calc(22% - (2 * $spacer-2));
    min-width: calc(22% - (2 * $spacer-2));
    max-width: calc(22% - (2 * $spacer-2));
    display: block;
  }
  &__variable-wrapper {
    margin-inline-end: $spacer-2;
    min-width: calc(12% - ($spacer-2));
    max-width: calc(12% - ($spacer-2));

  }
  &__sub-head {
    margin-top: $spacer-3;
    font-weight: $fw-medium;
    color:$ifp-color-grey-9;
    font-size: $ifp-fs-4;
  }
  &__variables,
  &__variable-head {
    border-radius: 10px;
  }
  &__variables{
    border: 1px solid $ifp-color-grey-13;
    overflow: hidden;
  }
  &__variable-head {
    background-color: $ifp-color-pale-grey;
    font-size: $ifp-fs-3;
    display: flex;
   align-items: center;
   justify-content: center;
   font-weight: $fw-semi-bold;
   height: 160px;

  }
  &__variable{

    align-items: center;
    padding: $spacer-2 $spacer-3;
    border-bottom: 1px solid $ifp-color-grey-13;
    color: $ifp-color-primary-grey;
    height: 40px;
    font-size: $ifp-fs-3;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    &:last-child {
      border-bottom:  unset
    }
  }
  &__result-tabs {
    display: flex;
    border-bottom: 1px solid $ifp-color-grey-13;
  }
  &__result-tab {
    display: flex;
    margin-inline-end: $spacer-5;
    font-size: $ifp-fs-3;
    padding-bottom: $spacer-2;
    font-weight: $fw-medium;
    color: $ifp-color-grey-14;
    cursor: pointer;
    &--active {
      border-bottom: 3px solid $ifp-color-blue-menu;
      color: $ifp-color-black;
      font-size: $ifp-fs-4;
      font-weight: $fw-bold;
    }
  }
  &__result-name-outer {
    display: flex;
  }
  &__highchart {
    margin-top: $spacer-4;
    display: block;
  }
  &__result-divider {
    border-bottom: 1px solid $ifp-color-grey-7;
    margin: $spacer-0 (-$spacer-5) $spacer-4;
  }
  &__head-btn-wrapper {
    display: flex;
  }
}

@include desktop-xl {
  .ifp-auto-imp-score {
    &__chart {
      min-width: calc(33.33% - (2 * $spacer-2));
      max-width: calc(33.33% - (2 * $spacer-2));
    }
  }
}

@include desktop-lg {
  .ifp-auto-imp-score {
    &__variable-wrapper {
      min-width: calc(33.33% - ($spacer-2));
      max-width: calc(33.33% - ($spacer-2));
    }
  }
}

@include desktop-sm {
  .ifp-auto-imp-score {
    &__chart {
      min-width: calc(50% - (2 * $spacer-2));
      max-width: calc(50% - (2 * $spacer-2));
    }
    &__variable-wrapper {
      min-width: calc(50% - ($spacer-2));
      max-width: calc(50% - ($spacer-2));
    }
    &__head-btn-wrapper {
      margin-top: $spacer-3;
    }
  }
}

@include mobile {
  .ifp-auto-imp-score {
    &__chart,  &__variable-wrapper {
      min-width: calc(100% - 24px);
      max-width: calc(100% - 24px);
    }
  }
}
:host-context(.ifp-dark-theme) {
    .ifp-auto-imp-score__variable-head{
         background-color:  $ifp-color-grey-7;
      }
}

:host::ng-deep {
  .ifp-auto-imp-score {
    &__table {
      .ifp-data-table__col {
        padding: $spacer-3 $spacer-7;
      }
    }
  }
}

:host-context([dir="rtl"]) {
  .ifp-auto-imp-score__variable{
      direction: ltr;
    }
}
