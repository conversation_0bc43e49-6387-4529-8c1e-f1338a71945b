<div class="ifp-sv">

  <ifp-tree-progress class="ifp-sv__root-main" [title]="'SCAD Production'" [value]="data.scad_production"
    [progressData]="progressData()" [isVertical]="true" [isSemiCircle]="false"></ifp-tree-progress>

  <div class="ifp-sv__branch-wrapper">

    <div class="ifp-sv__branch ifp-sv__branch--top">

      <div class="ifp-sv__box ifp-sv__box--root">
        <p class="ifp-sv__text">{{'Bayaan Production' | translate}}</p>
        <p class="ifp-sv__box-count">{{data.bayaan_production}}</p>
      </div>

      <div class="ifp-sv__sub-branch">
        @for (item of data.bayaan_domain; track $index) {
        <div class="ifp-sv__box">
          <p class="ifp-sv__text">{{item.TOPIC_NAME_ENGLISH | translate}}</p>
          <p class="ifp-sv__box-count">{{item.VALUE}}</p>
        </div>
        }
      </div>

    </div>

    <div class="ifp-sv__branch ifp-sv__branch--bottom">

      <div class="ifp-sv__box ifp-sv__box--root">
        <p class="ifp-sv__text">{{'Not Approved' | translate}}</p>
        <p class="ifp-sv__box-count">{{data.not_approved}}</p>
      </div>

      <div class="ifp-sv__sub-branch">
        <div class="ifp-sv__box-wrapper">
          @for (item of data.not_approved_list; track $index) {
          <div class="ifp-sv__box">
            <p class="ifp-sv__text">{{item.NOT_APPROVED_STATUS | translate | titlecase}}</p>
            <p class="ifp-sv__box-count">{{item.VALUE}}</p>
          </div>
          }
        </div>

        <div class="ifp-sv__table-wrapper">
          <div class="ifp-sv__table-inner">
            <table class="ifp-sv__table">

              <tr class="ifp-sv__table-row ifp-sv__table-row--head">
                <th class="ifp-sv__table-col">{{'Department' | translate}}</th>
                <th class="ifp-sv__table-col">{{'Ticket Not Approved' | translate}}</th>
                <th class="ifp-sv__table-col">{{'Business Exception' | translate}}</th>
                <th class="ifp-sv__table-col">{{'Value Check' | translate}}</th>
                <th class="ifp-sv__table-col">{{'Indicators' | translate}}</th>
              </tr>

              @for (item of data.not_approved_table; track $index) {
              <tr class="ifp-sv__table-row">
                <td class="ifp-sv__table-col">{{item.RESPONSIBLE_EN | translate}}</td>
                <td class="ifp-sv__table-col">{{item.ticket_approval}}
                  <!-- <ifp-status-tag [name]="item.TICKET_APPROVAL == 1 ? 'Approved' : 'Rejected'" [color]="item.TICKET_APPROVAL == 1 ? '#5DB14E' : '#F3593A'"></ifp-status-tag> -->
                </td>
                <td class="ifp-sv__table-col">{{item.business_exception}}
                  <!-- <ifp-status-tag [name]="item.BUSINESS_EXCEPTION == 1 ? 'Approved' : 'Rejected'" [color]="item.BUSINESS_EXCEPTION == 1 ? '#5DB14E' : '#F3593A'"></ifp-status-tag> -->
                </td>
                <td class="ifp-sv__table-col">{{item.value_check}}
                  <!-- <ifp-status-tag [name]="item.BUSINESS_EXCEPTION == 1 ? 'Approved' : 'Rejected'" [color]="item.BUSINESS_EXCEPTION == 1 ? '#5DB14E' : '#F3593A'"></ifp-status-tag> -->
                </td>
                <td class="ifp-sv__table-col">{{item.indicator_count}}</td>
              </tr>
              }
            </table>
          </div>
        </div>
      </div>

    </div>

  </div>
</div>
