import { Component, input, model, signal, viewChild, output, linkedSignal, Signal, WritableSignal, Input } from '@angular/core';
import { CdkDragDrop, CdkDrag, CdkDropList, moveItemInArray, transferArrayItem, CdkDragStart, CdkDragEnd } from '@angular/cdk/drag-drop';
import { CommonModule, NgClass } from '@angular/common';
import { IfpMultiTagDropdownComponent } from '../../../scad-insights/ifp-widgets/ifp-atoms/ifp-multi-tag-dropdown/ifp-multi-tag-dropdown.component';
import { DxpLableDropdownComponent } from '../dxp-label-dropdown/dxp-label-dropdown.component';
import { FormGroup, FormArray, AbstractControl } from '@angular/forms';

@Component({
  selector: 'ifp-dxp-label-drag',
  imports: [CdkDropList, NgClass, CommonModule, IfpMultiTagDropdownComponent, DxpLableDropdownComponent],
  templateUrl: './dxp-label-drag.component.html',
  styleUrl: './dxp-label-drag.component.scss',
})
export class DxpLabelDragComponent {
  public isDragging = model(false);
  public limit = model(-1);
  public dragEnterEvent  = signal(false);
  public dropdownList = input<
    {
      dropdown: string[] | Record<string, string>[];
      name: string;
      icon: string;
      aggregations?: string[];
      selectedAggregation?: string;
    }[]
  >([]);
  public connectedto = input<
    string | CdkDropList<any> | (string | CdkDropList<any>)[]
  >([]);
  public dropdownRef = viewChild<
    string | CdkDropList<any> | (string | CdkDropList<any>)
  >('dropdown');
  
  // Add output event emitter for drop events
  public dropEvent = output<CdkDragDrop<any>>();
  public aggregationSelected = output<{aggregation: string, index: number}>();
  public selectedItemsArr: any[][] = [];
  public selectedItems : WritableSignal<any[][]> = linkedSignal(() => {
    return this.dropdownList().map(item => {
      if (!item || !item.aggregations) return [];
      return [{
        name: item.name,
        type: 'aggregation',
        aggregationOptions: this.getAggregationOptions(item.aggregations),
        dropdownValue: item.selectedAggregation
      }];
    });
  });

  @Input() axisControl!: FormGroup | FormArray;

  ngOnInit() {
    // this.updateSelectedItemsArr();
    
    
  }

  updateSelectedItemsArr() {
    const list = this.dropdownList();
    this.selectedItemsArr = list.map(item => {
      if (!item || !item.aggregations) return [];
      return [{
        name: item.name,
        type: 'aggregation',
        aggregationOptions: this.getAggregationOptions(item.aggregations),
        dropdownValue: item.selectedAggregation
      }];
    });
  }

  getRef() {
    return this.dropdownRef() ?? '';
  }
  drop(
    event: CdkDragDrop<
    {
      dropdown: string[] | Record<string, string>[];
      name: string;
      icon: string;
      aggregations?: string[];
      selectedAggregation?: string;
    }[],
      any,
      any
    >
  ) {
    // Emit the drop event to parent component
    this.dropEvent.emit(event);
    console.log(event);
    
    if (event.previousContainer === event.container) {
      moveItemInArray(
        this.dropdownList(),
        event.previousIndex,
        event.currentIndex
      );
    } else {
      const previousContainerData = event.previousContainer.data[event.previousIndex];
      transferArrayItem(
        event.previousContainer.data,
        event.container.data,
        event.previousIndex,
        event.currentIndex
      );
      event.previousContainer.data.splice(event.previousIndex, 0, previousContainerData);
    }
    this.isDragging.set(false);
    this.dragEnterEvent.set(false);
    this.updateSelectedItemsArr();
  }
   dragStart(event: CdkDragStart) {
      this.isDragging.set(true);

    }

    dragStop(event: CdkDragEnd) {
      this.isDragging.set(false);

    }
    dragEnter() {
     this.dragEnterEvent.set(true);
    }

    dragExit() {
      this.dragEnterEvent.set(false);
    }

    removeItem(index: number) {
      this.dropdownList().splice(index, 1);
    }

    onAggregationSelected(aggregation: string | Record<string, string>, index: number) {
      const aggName = typeof aggregation === 'string' ? aggregation : aggregation['name'];
      this.aggregationSelected.emit({ aggregation: aggName, index });
    }


    getAggregationOptions(aggregations: string[]) {
      console.log(aggregations.map(agg => ({
        name: agg, 
        type: 'aggregation',
        x_aggregations: [],
        y_aggregations: []
      })));
      
      return aggregations.map(agg => ({
        name: agg, 
        type: 'aggregation',
        x_aggregations: [],
        y_aggregations: []
      }));
    }

    getSelectedItems(index: number) {
      const item = this.dropdownList()[index];
      if (!item || !item.aggregations) return [];
      return [{
        name: item.name,
        type: 'aggregation',
        aggregationOptions: this.getAggregationOptions(item.aggregations),
        dropdownValue: item.selectedAggregation
      }];
    }
print(item: any) {
  console.log(item);
  
}

isFormArray(control: any): control is FormArray {
  return control && typeof control.at === 'function';
}
}
