@use "../../../../assets/ifp-styles/abstracts/index" as *;

.ifp-color-palette {
  &__wrapper {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    margin: $spacer-0 (-$spacer-2);
  }
  &__tick {
    font-size: $ifp-fs-7;
    color: $ifp-color-blue-hover;
    position: absolute;
    top: -13px;
    right: -10px;
    opacity: 0;
    transition: 0.3s;
  }
  &__input {
    width: 100%;
    height: 100%;
    opacity: 0;
    cursor: pointer;
  }
  &__item {
    width: calc(9.09% - (2 * $spacer-2));
    min-width: 40px;
    height: 40px;
    border-radius: 15px;
    margin: $spacer-2;
    position: relative;
    border: 1px solid $ifp-color-black-08;
    cursor: pointer;
    &--input {
      background-image: url(../../../../assets/images/dashboard-builder/custom-palette.png);
      background-size: 115%;
      background-repeat: no-repeat;
      background-position: center;
    }
    &--selected {
      .ifp-color-palette {
        &__item {
          border: 1px solid $ifp-color-blue-hover;
        }
        &__tick {
          opacity: 1;
        }
      }
    }
    &--dotted {
      border: 1px dashed $ifp-color-grey-14;
    }
  }
}
