import { Component, input, output } from '@angular/core';
import { AiButtonComponent } from '../../../ifp-widgets/atoms/ai-button/ai-button.component';
import { AiInsightIndicatorCardComponent } from '../ai-insight-indicator-card/ai-insight-indicator-card.component';
import { TranslateModule } from '@ngx-translate/core';

@Component({
  selector: 'ifp-gen-ai-key-header',
  imports: [
    AiButtonComponent,
    AiInsightIndicatorCardComponent,
    TranslateModule,
  ],
  templateUrl: './gen-ai-key-header.component.html',
  styleUrl: './gen-ai-key-header.component.scss',
})
export class GenAiKeyHeaderComponent {
  public summery = input('');
  public date = input('');
  public consolidated = input(false);
  public drivers = input<Drivers[]>([]);
  public preview = input(false);
  public summeryEdit = output();
  public updateSummeryValueEvent = output<string>();

  updateSummeryValue(event: Event) {
    this.updateSummeryValueEvent.emit(
      (event.target as HTMLInputElement).value ?? ''
    );
  }

  openSumeryEdit() {
    this.summeryEdit.emit();
  }
}

export interface Drivers {
  indicator_name: string;
  heading: string;
  value: number;
  whether_positive_trend: boolean;
  domain?: {
    name?: string;
    id?: string;
  };

  pre_period?: string;
  curr_period?: string;
}
