import { Injectable, signal } from '@angular/core';
import { HttpService } from '../http/http.service';
import { Router } from '@angular/router';
import { userLogApi } from './usage-dashboard.constants';
import { catchError, of, tap } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class UsageDashboardStatusService {
  public usageDashboardStatus = signal(false);
  public usageDashboardStatusApi = signal(false);
  constructor(private _http: HttpService, private _router: Router) {

  }

  usageDashboardStatusCheck(root: boolean = true) {
    if (!this.usageDashboardStatusApi()) {
      return this._http.get(userLogApi.statusCheck).pipe(tap((data) => {
        if (!data.status) {
          this.usageDashboardStatus.set(false);
          if (root) {
            this._router.navigateByUrl('/404');
          }
        } else {
          this.usageDashboardStatus.set(true);
        }
        this.usageDashboardStatusApi.set(true);
      }),
      catchError(() => {
        this.usageDashboardStatus.set(false);
        if (root) {
          this._router.navigateByUrl('/404');
        }
        this.usageDashboardStatusApi.set(true);
        return of({ status: false });
      })
      );
    }
    return of({ status: this.usageDashboardStatus() });


  }

}
