import { Component, Input, OnChanges } from '@angular/core';
import { IfpProgressValueComponent } from '../../molecule/ifp-progress-value/ifp-progress-value.component';
import { IfpMlColorPipe } from '../ifp-ml-color.pipe';
import { DecimalPipe, NgClass } from '@angular/common';
import { IterationList, SelectedColumnMetaAutoMl } from '../ifp-exploratory-auto-ml-analyze/ifp-exploratory-auto-ml-analyze.component';
import { IfpTooltipDirective } from 'src/app/scad-insights/core/directives/ifp-tooltip.directive';
import { TranslateModule } from '@ngx-translate/core';

@Component({
    selector: 'ifp-auto-ml-column-chart',
    imports: [
    IfpProgressValueComponent, IfpMlColorPipe,
    NgClass, IfpTooltipDirective, DecimalPipe,
    TranslateModule],
    templateUrl: './ifp-auto-ml-column-chart.component.html',
    styleUrl: './ifp-auto-ml-column-chart.component.scss'
})
export class IfpAutoMlColumnChartComponent  implements OnChanges{




  @Input() columnList: string[] = [

  ];

  @Input() iterationItem?: IterationList;

  @Input() topLabel!:string;
  @Input() bar = false;
  @Input() selection = false;

  public columnsListObject?:Record<string, SelectedColumnMetaAutoMl > = {};


  ngOnChanges(): void {
    // used for setup value for the table and bar chart in the gauge  chart card

    this.iterationItem?.metadata?.['selected column meta'].forEach(element => {
      if (this.columnsListObject) {
        this.columnsListObject[element.column] = element;
      }

    });

  }

}
