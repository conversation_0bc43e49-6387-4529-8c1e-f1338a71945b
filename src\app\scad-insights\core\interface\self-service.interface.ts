export interface Classification {
  dark_icon: string;
  id: string;
  light_icon: string;
  name: string;
  nodeCount: number;
  showTree: boolean;
  checked: boolean;
  key?: string;
}

export interface Domain {
  icon: string;
  id: string;
  light_icon: string;
  name: string;
  checked: boolean
}

export interface Theme {
  id: string;
  name: string;
  checked: boolean;
  showScreener: boolean;
  subthemes: []
}

export interface SubTheme {
  id: string;
  name: string;
  checked: boolean;
  showScreener: boolean;
}

export interface Product {
  count: string;
  pid: string;
  checked: boolean;
  title: string;
}

export interface Items {
  category: [];
  content_type: string;
  id: string;
  note: string;
  title: string;
  app_type?: string;
  APP_TYPE?: string;
  content_classification_key?: string;
}
