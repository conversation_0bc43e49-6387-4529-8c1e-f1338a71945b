import { Component, inject, input, model, OnDestroy, OnInit, signal, WritableSignal } from '@angular/core';
import { IFPHighChartsComponent } from '../../scad-insights/ifp-widgets/charts/ifp-highcharts.component';
import { TranslateModule } from '@ngx-translate/core';
import { GenAiDashboardQusArcComponent } from '../gen-ai-dashboard-qus-arc/gen-ai-dashboard-qus-arc.component';
import { GenAiDashboardDatePickerComponent } from '../gen-ai-dashboard-date-picker/gen-ai-dashboard-date-picker.component';
import { SubSink } from 'subsink';
import { genAiTestingApi, questionDropDown } from '../constents/gen-ai-testing.constant';
import { IfpNoDataComponent } from "../../scad-insights/ifp-widgets/ifp-molecules/ifp-no-data/ifp-no-data.component";
import { ApiGenAiService } from 'src/app/scad-insights/core/services/api-gen-ai.service';
import { ShortNumberPipe } from 'src/app/scad-insights/core/pipes/shortNumber.pipe';
import { FormControl } from '@angular/forms';
import { KeyValuePipe, TitleCasePipe } from '@angular/common';
import { Chart } from 'highcharts/highcharts.src';
import { GenAiDashboardRatingComponent } from "../gen-ai-dashboard-rating/gen-ai-dashboard-rating.component";
import { PanelDropdownOptions } from 'src/app/ifp-analytics/molecule/ifp-panel-dropdown/ifp-panel-dropdown.component';
import { IfpDropdownComponent } from "../../scad-insights/ifp-widgets/ifp-atoms/ifp-dropdown/ifp-dropdown.component";

@Component({
  selector: 'ifp-gen-ai-dashboard-landing-page',
  imports: [IFPHighChartsComponent, TranslateModule, GenAiDashboardQusArcComponent, GenAiDashboardDatePickerComponent, IfpNoDataComponent, ShortNumberPipe,  GenAiDashboardRatingComponent, IfpDropdownComponent],
  templateUrl: './gen-ai-dashboard-landing-page.component.html',
  styleUrl: './gen-ai-dashboard-landing-page.component.scss'
})
export class GenAiDashboardLandingPageComponent implements OnInit,OnDestroy{
  public modelName = model('');
  public modalDropdown = input<PanelDropdownOptions[]>();
  public _apiService = inject(ApiGenAiService);
  public subs =  new SubSink();
  public averageQuestion =signal(0);
  public startForm =  new FormControl();
  public endForm =  new FormControl();
  public gaugeChartData = {
    name: 'Accuracy',
    data: 0,
    star:0
  };

  public chart =  (chart: Chart) => {};


  public analyticMetrixGauge: Record<string, {name: string; data: number[]}[]> = {};
  public analyticMatrixOptions = questionDropDown;
  public axisStyle = {
    fontSize: '1.2rem',
    color: '#6A7180',
    lineHeight: '1.4rem',
    fontWeight: '500',
    fontFamily: 'Noto Sans'
  }

  public lineChartData:{
    type: string;
    name: string;
    data?:number[][];
    color: string;
    marker: {
        enabled: boolean;
    };
}[] =[{
    type: 'area',
    name: '',
    data: [],
    color: '#2687FD',
    marker: {
      enabled: false
    }
  }];
    public weeklyAccurary:{
    type: string;
    name: string;
    data?:number[][];
    color: string;

}[] =[{
    type: 'area',
    name: '',
    data: [],
    color: '#2687FD',

  }];

  public barChartDataComplexity:WritableSignal<{
    name: string;
    data?: (string | number)[][];
    color: string;
}[]>  =signal<{
    name: string,
    data?:(string | number)[][],
    color: string,

  } []>( [{
    name: 'Complex',
    data: [],
    color: '#FAC656',
  }]);

  public barChartDataCategory =signal<{
    name: string,
    data?:(string | number)[][],
    color: string,

  } []>( [{
    name: 'Complex',
    data: [],
    color: '#FAC656',
  }]);

  public yAxis : Highcharts.YAxisOptions={
    lineWidth: 1,
    lineColor: '#e6e6e6',
    gridLineDashStyle: 'Dot',
    gridLineColor: '#e6e6e6',
    tickWidth: 0,
    tickLength: 0,
    title: {
      text: null
    },
    labels: {
      style: this.axisStyle
    }
  };

  public   plotOptions = {
    column: {
      borderRadius: 5,
      color: '#0C4A8E'
    }
  };

  public legends:   Highcharts.LegendOptions = {
    enabled: true
  };

    public seriesDataStyle: WritableSignal<
    {
      name: string;
      data: {
        name: string;
        y: number;
        color: string;
      }[];
    }[]
  > = signal([
    {
      name: 'Style',
      data: [
        { name: 'Style', y: 0, color: '#fafafa' },
      ],
    },
  ]);

  public xAxis:  Highcharts.XAxisOptions =  {
    lineWidth: 1,
    lineColor: '#e6e6e6',
    tickLength: 0,
    categories: undefined,
    type: 'category',
    labels: {
      style: this.axisStyle
    }
  };

  public tooltip: Highcharts.TooltipOptions =  {
    style: {
      zIndex: 9999 // Set the z-index of the tooltip
    },
    positioner: (boxWidth: number, boxHeight: number, point: {plotX: number, plotY: number}) => {
      return {
        x: point.plotX -( boxWidth /3),
        y: point.plotY - boxHeight - 10 // Adjust the offset as needed
      };
    },
    animation: false,
    hideDelay: 0,
    backgroundColor: '#182234',
    borderRadius: 10,
    shadow: false,
    distance: 0,
    useHTML: true,
    outside: true,
    formatter: (a) =>{
      const point  = a.chart.hoverPoint;
      return `<div class="ifp-chart-tooltip__usage"> <div>${point?.name}</div>  ${point?.y} </>`;
    }
  };

  public dateValue!:{ startDate: Date | null; endDate: Date | null; }
  public params:{start_date?: string; end_date?: string; model: string;} = {
    model: this.modelName()
  }

  public analyticsMetrix: Record<string, any> = {};

  ngOnInit(): void {
    this.modelName.set(this.modalDropdown()?.[0].key ?? '')
    this.params ={
      model: this.modelName()
    }
    this.callApis();

  }
    dropdownVersion(dropdown: PanelDropdownOptions) {
     this.modelName.set(dropdown.key);
     this.params['model']=this.modelName();
     this.callApis();
  }

  date(date:{ startDate: Date | null; endDate: Date | null; }) {
    this.dateValue = date;
    this.params = {
      model: this.modelName(),
      start_date: this.formatDateBack(this.dateValue.startDate ?? new Date()),
      end_date: this.formatDateBack(this.dateValue.endDate ?? new Date())
    }
    this.callApis();
  }
  callApis() {
    this.callAccuracy();
    this.callAverageTime();
    this.callAccuracyComplexity();
    this.callAccuracyCategory();
    this.callResponseLatency();
    this.callWeeklyAccurary();
    this.callAnalyticMetrix(this.analyticMatrixOptions.evalAnsAccuracy,'accurate');
    this.callAnalyticMetrix(this.analyticMatrixOptions.evalAnsRelevance,'relevant');
    this.callAnalyticMetrix(this.analyticMatrixOptions.evalAnsStyle,'pass');
  }




  callAccuracy() {
    this.subs.add(this._apiService.getMethodRequest(genAiTestingApi.accuracy, this.params).subscribe((data:{accuracy: number}) => {
      this.gaugeChartData ={
        name: 'Accuracy',
        data: data.accuracy,
        star:data.accuracy
      };
    }));
  }
  callWeeklyAccurary() {
    this.subs.add(this._apiService.getMethodRequest(genAiTestingApi.weeklyAccuracy, this.params).subscribe((accuracy:Record<string,number>) => {
      const lineChart:number[][] = [];
      for (const key in accuracy) {
        if (Object.prototype.hasOwnProperty.call(accuracy, key)) {
          const element = accuracy[key];
        lineChart.push([this.getConvertDate(key), element]);
      this.weeklyAccurary =[{
        type: 'line',
        name: 'Accuracy over the week',
        data: lineChart,
        color: '#2687FD'
      }];

        }
      }
    }));
  }
  callAverageTime() {
    this.subs.add(this._apiService.getMethodRequest(genAiTestingApi.averageQuestion, this.params).subscribe((data:{number_of_questions: number}) => {
      this.averageQuestion.set(data.number_of_questions);
    }));
  }

  callAccuracyComplexity() {
    this.subs.add(this._apiService.getMethodRequest(genAiTestingApi.accuracyComplexity, this.params).subscribe((data:AccuracyComplexity[]) => {

        const complex: {
          name: string,
          data:(string | number)[][],
          color: string,
        } = {
          name: 'Complex',
          data: [],
          color: '#FAC656',
        };
        const simple : {
          name: string,
          data:(string | number)[][],
          color: string,
        }= {
          name: 'Simple',
          data: [],
          color: '#00B9FF',
        };
        data.forEach(data=> {
          complex.data.push([data.month, data.complex])
          simple.data.push([data.month, data.simple])
        });
        this.barChartDataComplexity.set([simple,complex]);

    }));
  }

  callAccuracyCategory() {
    this.subs.add(this._apiService.getMethodRequest(genAiTestingApi.accuracyCategory, this.params).subscribe((data:AccuracyCategory[]) => {

        const accuracy: {
          name: string,
          data:(string | number)[][],
          color: string,
        } = {
          name: 'Data',
          data: [],
          color: '#19C1C1',
        };
        const category : {
          name: string,
          data:(string | number)[][],
          color: string,
        }= {
          name: 'Policy',
          data: [],
          color: '#AFCF3C',
        };
        data.forEach(data=> {
          accuracy.data.push([data.month, data.data])
          category.data.push([data.month, data.policy])
        });
        this.barChartDataCategory.set([accuracy,category]);


    }));
  }

  callResponseLatency() {
    this.subs.add(this._apiService.getMethodRequest(genAiTestingApi.responseLatency, this.params).subscribe((data:{
      date: string;
      average_response_time: number;
    }[]) => {
      const lineChart:number[][] = [];
      data.forEach(latency=> {
        lineChart.push([this.getConvertDate(latency.date), latency.average_response_time]);
      });

      this.lineChartData =[{
        type: 'area',
        name: 'Average Time Of Response',
        data: lineChart,
        color: '#2687FD',
        marker: {
          enabled: false
        }
      }];
    }));
  }

  // used to convert date to utc format
  getConvertDate(dateValue: string) {
    const date = new Date(dateValue);
    // Get the UTC timestamp
    return Date.UTC(date.getUTCFullYear(), date.getUTCMonth(), date.getUTCDate(),
      date.getUTCHours(), date.getUTCMinutes(), date.getUTCSeconds());

  }

  formatDateBack(date: Date) {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0'); // Get month and pad with 0 if needed
    const day = String(date.getDate()).padStart(2, '0'); // Get day and pad with 0 if needed
    return `${year}-${month}-${day}`;
  }

  callAnalyticMetrix(metricCategory: string, value: string) {
    const params = {...this.params,option_value:value}
    this.subs.add(
      this._apiService.getMethodRequest(`${genAiTestingApi.analytics}${metricCategory}`,params).subscribe({
        next: (data: Record<string, any>) => {
          // this.analyticsMetrix = {...this.analyticsMetrix, ...data};
          const name = metricCategory.split('_').pop() ?? '';
          this.analyticMetrixGauge = {...this.analyticMetrixGauge, ...{[metricCategory]: [{name: name.toUpperCase(), data: [data[metricCategory]]}]}};
          if(this.analyticMatrixOptions.evalAnsStyle === metricCategory) {
            this.seriesDataStyle.set([
    {
      name: 'Style',
      data: [
           { name: 'Style', y: data[metricCategory], color: ' #54af58' },

      ],
    },
  ]);
         this.chart = (chart: Chart) => {

      const centerText = `<div text-anchor="middle"  class="ifp-high-chart__donut-center">
      <span   class="ifp-high-chart__donut-heading" >${data[metricCategory]}</span>

  </div>`;
      if ((chart as any).centerLabel) {
        (chart as any).centerLabel.destroy();

      }

      // Create the center label if it doesn't exist
      (chart as any).centerLabel = chart.renderer.label(centerText, 0, 0)
        .attr({
          zIndex: 5
        })
        .add();

      // Center the label based on the chart's dimensions
      const bbox = (chart as any).centerLabel.getBBox();

      (chart as any).centerLabel.attr({
        x: chart.plotLeft + (chart.plotWidth / 2),
        y: (chart.plotHeight / 2)  // Adjust for vertical centering
      });

    };
          }

        }
      })
    )
  }

  ngOnDestroy(): void {
    this.subs.unsubscribe();
  }
}





interface AccuracyComplexity {
  month: string;
  simple: number;
  complex: number;
}

interface AccuracyCategory {
  month: string;
  policy: number;
  data: number;
}
