import { CanvasTool, Node } from './../ifp-data-prep/interface/ifp-data-prep.interface';
import { connectionType, connectionTypeConfigs } from './../ifp-data-prep/constants/if-preps.constants';
import { AfterViewInit, ChangeDetectionStrategy, ChangeDetectorRef, Component, ElementRef, OnDestroy, OnInit, Renderer2, ViewChild, WritableSignal, inject, signal } from '@angular/core';
import { IfpPrepToolbarComponent } from '../../organism/ifp-prep-toolbar/ifp-prep-toolbar.component';
import { FileResponePrep, NodeData, Toolbar, Tools } from '../ifp-data-prep/interface/ifp-data-prep.interface';
import { advancedTools, canvasTools } from '../ifp-data-prep/ifp-data-prep.constant';
import { IfpPrepHeaderComponent } from '../../organism/ifp-prep-header/ifp-prep-header.component';
import { ActivatedRoute, Router } from '@angular/router';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { SubSink } from 'subsink';
import { ConnectionNode, IfpPrepAdvancePlaygroundComponent } from '../../molecule/ifp-prep-advance-playground/ifp-prep-advance-playground.component';
import { IfpDataSelectToolComponent } from '../../organism/ifp-adv-select-tool/ifp-adv-select-tool.component';
import { IfpAdvanceRightPanelComponent } from '../../molecule/ifp-advance-right-panel/ifp-advance-right-panel.component';
import { IfpAdvAggregateToolComponent } from '../../organism/ifp-adv-aggregate-tool/ifp-adv-aggregate-tool.component';
import { IfpDataCleaningComponent } from '../../molecule/ifp-data-cleaning/ifp-data-cleaning.component';
import { IfpAdvJoinToolComponent } from '../../organism/ifp-adv-join-tool/ifp-adv-join-tool.component';
import { IfpAdvAppendToolComponent } from '../../organism/ifp-adv-append-tool/ifp-adv-append-tool.component';
import { IfpAdvSourceToolComponent } from '../../organism/ifp-adv-source-tool/ifp-adv-source-tool.component';
import { IfpAdvDestinationToolComponent } from '../../organism/ifp-adv-destination-tool/ifp-adv-destination-tool.component';
import { IfpAdvFilterToolComponent } from '../../organism/ifp-adv-filter-tool/ifp-adv-filter-tool.component';
import { ifpColors } from 'src/app/scad-insights/core/constants/color.constants';
import { IfpAdvCalculationToolComponent } from '../../organism/ifp-adv-calculation-tool/ifp-adv-calculation-tool.component';
import { selectNodeStoreAdvance } from './store/advance-node-store/advance-node.store';
import { IfpAdvancePrepService } from './service/ifp-advance-prep.service';
import { prepsApiEndpoints } from '../ifp-data-prep/constants/if-preps.constants';
import { SubscriptionLike } from 'rxjs';
import { StreamingService } from 'src/app/scad-insights/core/services/http/streaming.service';
import { environment } from 'src/environments/environment';
import { WorkFlowStatusStream } from '../ifp-data-prep/ifp-prep-library/intreface/ifp-prep-library.interface';
import { workFlowState } from '../ifp-data-prep/constants/ifp-state.contants';
import { ToasterService } from 'src/app/scad-insights/core/services/tooster/ToastrService.service';
import { ColumnPrep, ColumnUpstream, PrepColumnInterface } from '../ifp-data-prep/ifp-prep-select-data/interface/prep-selection.interface';
import { IfpModalComponent } from '../../../scad-insights/ifp-widgets/ifp-organism/ifp-modal/ifp-modal.component';
import { IfpRemoveCardComponent } from '../../../scad-insights/ifp-widgets/ifp-atoms/ifp-remove-card/ifp-remove-card.component';
import { IfpAdvancePrepPreviewComponent } from './ifp-advance-prep-preview/ifp-advance-prep-preview.component';
import { DatePipe, NgClass } from '@angular/common';
import { IfpAdvImputeComponent } from '../../organism/ifp-adv-impute/ifp-adv-impute.component';
import { IfpPrepSaveModalComponent } from '../../organism/ifp-prep-save-modal/ifp-prep-save-modal.component';
import { IfpAdvSortComponent } from '../../organism/ifp-adv-sort/ifp-adv-sort.component';
import { HeaderComponent } from 'src/app/scad-insights/shared/header/header.component';
import { cloneDeep } from 'lodash';
import { DownLoadService } from 'src/app/scad-insights/core/services/download-service/download.service';
import { IfpPrepService } from '../ifp-data-prep/ifp-prep-service';
import { IfpModalService } from 'src/app/scad-insights/ifp-widgets/ifp-organism/ifp-modal/ifp-modal.service';
import { ToastContainerDirective } from 'ngx-toastr';
import { UsageDashboardLogService } from 'src/app/scad-insights/core/services/usage-dashboard-log/usage-dashboard-log.service';
import { logType } from 'src/app/scad-insights/core/services/usage-dashboard-log/usage-dashboard.constants';


@Component({
    selector: 'ifp-advance-prep',
    templateUrl: './ifp-advance-prep.component.html',
    styleUrl: './ifp-advance-prep.component.scss',
    changeDetection: ChangeDetectionStrategy.OnPush,
    providers: [selectNodeStoreAdvance],
    imports: [
    IfpPrepToolbarComponent,
    IfpPrepHeaderComponent,
    TranslateModule,
    IfpPrepAdvancePlaygroundComponent,
    IfpDataSelectToolComponent,
    IfpAdvanceRightPanelComponent,
    IfpAdvAggregateToolComponent,
    IfpDataCleaningComponent,
    IfpAdvJoinToolComponent,
    IfpAdvAppendToolComponent,
    IfpAdvSourceToolComponent,
    IfpAdvDestinationToolComponent,
    IfpAdvFilterToolComponent,
    IfpAdvCalculationToolComponent,
    IfpModalComponent,
    IfpRemoveCardComponent,
    IfpAdvancePrepPreviewComponent,
    IfpAdvImputeComponent,
    IfpPrepSaveModalComponent,
    IfpAdvSortComponent,
    NgClass
]
})
export class IfpAdvancePrepComponent implements OnInit, AfterViewInit, OnDestroy {

  // @HostListener('window:beforeunload', ['$event'])
  // unloadNotification($event: any): void {
  //   $event.returnValue = true;
  // }

  readonly store = inject(selectNodeStoreAdvance);

  @ViewChild('fullscreenDiv') fullscreenDiv!: ElementRef;
  @ViewChild('confirmModal') confirmModal!: IfpModalComponent;
  @ViewChild('resetModal') resetModal!: IfpModalComponent;
  @ViewChild('toolRef') toolRef!: { onSave(): () => void; checkValidation(): () => boolean; downloadFile(): () => void; };
  @ViewChild('saveWorkflowModal') saveWorkflowModal!: IfpModalComponent;
  @ViewChild('content') content!: ElementRef;
  @ViewChild('toolBar') toolBar!: ElementRef;
  @ViewChild('header') headerComponent!: HeaderComponent;
  @ViewChild('toolBar') toolBarComponent!: IfpPrepToolbarComponent;
  @ViewChild('rightPanel') rightPanel!: { getNativeElement(): () => void; };
  @ViewChild('advPreview') advPreview!: IfpAdvancePrepPreviewComponent;
  @ViewChild('playGround') playGround!: IfpPrepAdvancePlaygroundComponent;
  @ViewChild(ToastContainerDirective, { static: true })
    toastContainer: ToastContainerDirective | undefined;


  public tools: Toolbar[] = advancedTools;
  public selectedTool: Tools = this.tools[0].menu[0];
  public subs: SubSink = new SubSink();
  public status!: string;
  public mobileItemSelec: any;
  public selectedNode!: NodeData | null;
  public color = ifpColors;
  public showPreview: boolean = false;
  public removeSelection: boolean = false;
  public dataSetResponse!: FileResponePrep | null;
  public upStreamColumnList: ColumnPrep[] = [];
  public allSourceColumnList!: Record<string, ColumnUpstream>;
  public workFlowId: WritableSignal<string> = signal('');
  public currentConnectionCount: number = 0;
  public fileName!: string;
  public rightPanelWidth: number = 0;
  public document = document;
  public headerDynamicWidth: number = 0;
  public collapsed: boolean = false;
  public showRunMessage: boolean = false;
  public activeNode?: string = '';
  public sourceConnection: boolean = false;
  public sourceLimit: number = 0;
  public resetErrorMessage!: string;
  public canvasTool: CanvasTool = { type: '', value: '' };
  public columnList = [
    {
      id: 1,
      name: 'Person ID',
      dataType: 'string',
      isSelected: true
    },
    {
      id: 2,
      name: 'Age',
      dataType: 'integer',
      isSelected: true
    },
    {
      id: 3,
      name: 'Region',
      dataType: 'string',
      isSelected: true
    },
    {
      id: 4,
      name: 'Monthly Expense',
      dataType: 'integer',
      isSelected: true
    }
  ];

  public targetColumnList = [
    {
      id: 1,
      name: 'target_Person_ID',
      dataType: 'string',
      isSelected: true
    },
    {
      id: 2,
      name: 'target_Age',
      dataType: 'integer',
      isSelected: true
    },
    {
      id: 3,
      name: 'target_Region',
      dataType: 'string',
      isSelected: true
    }
  ];

  public dataTypes = [
    {
      name: 'string'
    },
    {
      name: 'integer'
    },
    {
      name: 'varchar'
    }
  ];

  public comparatorList = [
    {
      value: 'matches',
      color: this.color.greenDark
    },
    {
      value: 'does not match',
      color: this.color.red
    }
  ];

  public tabItems: Record<string, string | boolean>[] = [
    {
      label: 'Basic Mode',
      action: 'basic',
      isActive: false,
      iconClass: 'ifp-icon-cube'
    },
    {
      label: 'Expert Mode',
      action: 'expert',
      buttonClass: 'primary',
      isActive: true,
      iconClass: 'ifp-icon-bulb'
    }
  ];

  public selectedTab: Record<string, string | boolean> = this.tabItems[1];
  public eventSourceSubscription!: SubscriptionLike;
  public controller!: AbortController;
  public additionalHeight!: number;
  public initialHeight!: number;
  public detectChange: boolean = false;

  public downstreamColumn: WritableSignal<PrepColumnInterface[]> = signal([]);
  public upstreamColumn: WritableSignal<any> = signal({});
  public connectionEmpty = signal(false);
  public connectedToSource = signal(false);
  public disableWorkFlow = signal(true);
  public currentNodeConnections = signal(false);
  public sourceDataUploaded: WritableSignal<boolean> = signal(false);
  public selectedNodeCurrent!: NodeData | null;
  private observerResize!: ResizeObserver;
  private observerToolResize!: ResizeObserver;
  public columUpstreamSub!: SubscriptionLike;
  public columDownStreamSub!: SubscriptionLike;
  public sourceReplaceErrorMsg!: string;
  private sessionId!: string;
  public errorMessageForTools?: string;
  constructor(public prepService: IfpAdvancePrepService, public _eventSourceService: StreamingService, private _toast: ToasterService, private _cdr: ChangeDetectorRef,
    private _route: ActivatedRoute, private _renderer: Renderer2, private _router: Router, private _downloadService: DownLoadService, private _translate: TranslateService, public _prepService: IfpPrepService,
    private _modalService: IfpModalService, private log: UsageDashboardLogService, private _datePipe:DatePipe
  ) {
    this._route.queryParams.subscribe(resp=>{
      if (resp['payload']){
        this.showPrompt();
      }
    });
  }


  ngOnInit(): void {
    // this.callUpstreamAndDownStream();
    this._prepService.logs.set([]);
    this.prepService.clearAllValues();
    this.stopWorkflow();
    this._toast.setOverlay(this.toastContainer);
    this.prepService.clearPreviewData();
    if (this.sessionId) {
      this.log.logEnds(this.sessionId, this.log.currentTime );
    }
    this.sessionId = this.log.createUUid;
    this.log.logStart(this.sessionId, logType.advancePrep, this.log.currentTime );
  }

  ngAfterViewInit(): void {
    this.observerResize = new ResizeObserver(entries => {
      const width = entries[0].contentRect.width;
      this._renderer.setStyle(this.content.nativeElement, 'width', `calc(100% - ${width}px)`);
    });
    this.observerResize?.observe?.(this.toolBar.nativeElement);
  }

  // getSelectedTool(tool: Tools) {
  //   this.selectedTool = tool;
  //   if (tool.url) { /* empty */ }
  // }


  // toolDragged(_event: any) {
  //   this.mobileItemSelec = _event;
  // }

  onMouseDown(event: MouseEvent, resizableDiv: HTMLElement): void {
    this.handleDragStart(event.clientY, resizableDiv);
  }

  onTouchStart(event: TouchEvent, resizableDiv: HTMLElement): void {
    const touch = event.touches[0]; // Get the first touch point
    this.handleDragStart(touch.clientY, resizableDiv);
    event.preventDefault(); // Prevent default touch behavior
  }

  private handleDragStart(initialY: number, resizableDiv: HTMLElement): void {
    const initialHeight = resizableDiv.offsetHeight;
    if (!this.initialHeight) {
      this.initialHeight = initialHeight;
    }
    this.additionalHeight = this.initialHeight > initialHeight ? this.initialHeight - initialHeight + 300 : 300;
    this.detectChange = !this.detectChange;

    const onMove = (moveEvent: MouseEvent | TouchEvent) => {
      const clientY = this.getClientY(moveEvent);
      const newHeight = initialHeight + (clientY - initialY);
      if (newHeight < window.innerHeight) {
        resizableDiv.style.height = `${newHeight}px`;
        this.playGround.setHeight(newHeight);
      }
      if (newHeight < this.initialHeight) {
        resizableDiv.style.height = `${newHeight}px`;
        const setHeight = this.additionalHeight - (clientY - initialY);
        this.advPreview.setHeight(setHeight > 300 ? setHeight : 300);
      }
    };

    const onEnd = () => {
      document.removeEventListener('mousemove', onMove);
      document.removeEventListener('mouseup', onEnd);
      document.removeEventListener('touchmove', onMove);
      document.removeEventListener('touchend', onEnd);
    };

    document.addEventListener('mousemove', onMove);
    document.addEventListener('mouseup', onEnd);
    document.addEventListener('touchmove', onMove);
    document.addEventListener('touchend', onEnd);
  }

  private getClientY(event: MouseEvent | TouchEvent): number {
    return (event instanceof MouseEvent) ? event.clientY : event.touches[0].clientY;
  }

  callUpstreamAndDownStream() {
    this.checkNodeConditions();
    if (this.selectedNode?.name !== 'source') {
      this.callColumnUpStrem();
    }
    if (this.selectedNode?.name !== 'destination' && this.selectedNode?.name !== 'source') {

      /**
     * removed due to unwanted api call. In advance it is not using
      */
      // this.callColumnDownStrem();
    }
  }

  checkNodeConditions() {
    this.sourceConnection = false;
    if (this.store.selectNodeStoreAdvance().data()?.connections?.length !== 0) {
      this.connectionEmpty.set(false);
    } else {
      this.connectionEmpty.set(true);
    }
    this.sourceConnection = false;
    this.store.selectSourceNodes().sourceNode().findIndex(data => {
      this.store.selectNodeStoreAdvance().data().connections.forEach(connection => {
        if (connection.source === data.object_id) {
          this.sourceConnection = true;
        }
        if (data.configuration?.connection?.path && data.configuration?.connection?.path !== '') {
          this.sourceDataUploaded.set(true);
        } else {
          this.sourceDataUploaded.set(false);
        }
      });
    });
    this.currentConnectionCount = 0;
    if (this.store.selectNodeDestinationConnection().destinationConnections(this.prepService.currentNodeId() ?? '').length !== 0) {
      this.currentConnectionCount = this.store.selectNodeDestinationConnection().destinationConnections(this.prepService.currentNodeId() ?? '').length;
      this.currentNodeConnections.set(true);
    } else {
      this.currentNodeConnections.set(false);
    }
    const currentNode = this.store.selectNodeValue().nodeValue(this.prepService.currentNodeId() ?? '');
    this.sourceLimit = (currentNode.type == connectionType.joinTool || currentNode.type == connectionType.unionTool) ? 2 : 1;
    this.disableWorkFlow.set(true);
    if (this.sourceConnection && !this.connectionEmpty() && this.sourceDataUploaded()) {
      this.disableWorkFlow.set(false);
    }
  }

  toggleFullscreen() {
    const elem = this.fullscreenDiv.nativeElement;
    if (!document.fullscreenElement) {
      elem.requestFullscreen().catch((_err: { message: any; }) => {
        console.error(`Error attempting to enable full-screen mode: ${_err.message}`);
      });
    } else {
      document.exitFullscreen();
    }
  }

  stopWorkflow() {
    this.prepService.postMethodRequest(`${prepsApiEndpoints.workflows}flush/`).subscribe(_resp => {
      this.subs.unsubscribe();
      this.prepService.streamId.set('');
      this.controller?.abort();
      this.eventSourceSubscription?.unsubscribe();
      this.prepService.cancelWorkFlow.next(true);
      this.prepService.workflowRunningStatus.set(false);
    });
  }


  startWorkFlow() {
    if (this.prepService.workflowRunningStatus()) {
      this.stopWorkflow();
      return;
    }
    if (this.selectedNode?.name == 'destination') {
      this.prepService.destinationSaveModelOpen.next(true);
      return;
    }
    this.callWorkFlow();
  }


  callWorkFlow(isDownload: boolean = true, hideError: boolean = false) {
    if (this?.toolRef && !this.prepService.isConfigSave()) {
      this.toolRef?.onSave();
    }
    this.prepService.logs.set([]);
    setTimeout(() => {
      this.prepService.showPreview.set(true);
      const prepNodeData = this.getPayLoadData(true);
      if (this.checkJoinValidation(prepNodeData.nodes) && !hideError) {
        this._toast.error(this._translate.instant('At least one join condition is required.'));
        return;
      }
      const allNodes = this.store.selectNodeStoreAdvance().data();
      const isFail = allNodes.nodes.length != prepNodeData.nodes.length;
      if (isFail) {
        this._toast.warning(this._translate.instant('Some nodes are not connected. Please connect them or remove the nodes from the canvas.'));
      }
      this.prepService.processStatus.next(null);
      this.prepService.workflowRunningStatus.set(true);
      this.subs.add(
        this.prepService.postMethodRequest(prepsApiEndpoints.workflowrun, prepNodeData).subscribe({
          next:
            (workflow: {
              workflow: string
            }) => {
              this.prepService.streamId.set(workflow.workflow);
              this.eventSourceSubscription = this._eventSourceService.connectToServerSentEvents(`${environment.prepbaseUrl + prepsApiEndpoints.workflowStatus + workflow.workflow}/`)
                .subscribe({
                  next: (data: { data: string, ctrl: AbortController }) => {
                    this.prepService.workflowRunningStatus.set(true);
                    this.controller = data.ctrl;
                    if (data?.data) {
                      const convertJsonData: WorkFlowStatusStream = JSON.parse(data.data);
                      if (convertJsonData?.logs && convertJsonData?.logs?.length !==0) {
                        const currentLogs:{
                          key: string;
                          title: string;
                          value: string;
                        }[][] =[];
                        convertJsonData?.logs?.forEach((element:{
                          timestamp: string;
                          log_level: string;
                          message: string;
                        }) => {
                          currentLogs.push([{ key: 'Message', title: 'Message', value: element.message}, { key: 'Time', title: 'Time', value: this._datePipe.transform(new Date(element.timestamp), 'h:mm:ss a') ?? '' }, { key: 'Status', title: 'Status', value: element.log_level}]);
                        });
                        this.prepService.logs.set(currentLogs);
                      }
                      this.prepService.processStatus.next(convertJsonData);
                      if (workFlowState.force_stop == convertJsonData.workflow_status) {
                        this.prepService.workflowRunningStatus.set(false);
                        data.ctrl.abort();
                        return;
                      }
                      if (workFlowState.completed == convertJsonData.workflow_status) {
                        this.prepService.nodeChangeDetect.next('');
                        data.ctrl.abort();
                        this.eventSourceSubscription.unsubscribe();
                        this.prepService.workflowRunningStatus.set(false);
                        this.prepService.isWorkFlowStarted.set(true);
                        this._toast.success(this._translate.instant('Workflow completed successfully!'));
                        if (this.prepService.currentNodeId()) {
                          const nodeData = this.store.selectNodeValue().nodeValue(this.prepService.currentNodeId() ?? '');
                          // as per afsharf suggestion
                          if (nodeData.type == connectionType.outputTool) {
                            const previousNode = this.store.selectNodeDestinationConnection().destinationConnections(this.prepService.currentNodeId() ?? '');
                            this.prepService.currentNodeAndAnchor.next({
                              nodeId: previousNode[0].source, anchor: nodeData.type == connectionType.joinTool ? 2 : 1, source: nodeData.type == connectionType.inputTool,
                              sourceId: nodeData.type == connectionType.inputTool ? nodeData.configuration?.connection.path : '', afterWorkflow: true
                            });
                          } else {
                            this.prepService.currentNodeAndAnchor.next({
                              nodeId: this.prepService.currentNodeId(), anchor: nodeData.type == connectionType.joinTool ? 2 : 1, source: nodeData.type == connectionType.inputTool,
                              sourceId: nodeData.type == connectionType.inputTool ? nodeData.configuration?.connection.path : '', afterWorkflow: true
                            });
                          }

                          this._cdr.detectChanges();
                        }

                        if (isDownload) {
                          this.callDownload();
                        }
                        this.resetColumnApi();
                      } else if (workFlowState.error == convertJsonData.workflow_status) {
                        if (!hideError) {
                          this._toast.error(this._translate.instant('Something went wrong! Please try again.'));
                        }
                        this.prepService.showPreview.set(false);
                        data.ctrl.abort();
                        this.eventSourceSubscription.unsubscribe();
                        this.prepService.workflowRunningStatus.set(false);
                      }

                    }
                  },
                  error: error => {
                    this.resetColumnApi();
                    this._eventSourceService.close();
                    this.eventSourceSubscription.unsubscribe();
                    this.prepService.workflowRunningStatus.set(false);
                    this.prepService.showPreview.set(false);
                    if (!hideError) {
                      this._toast.error(error.error.error);
                    }
                  }
                }
                );
            },
          error: (errorMessage) => {
            this.prepService.workflowRunningStatus.set(false);
            this.prepService.availablePreviewNodes = {};
            this.resetColumnApi();
            this.prepService.logs.set([[{ key: 'Message', title: 'Message', value: errorMessage?.error?.message ?? (errorMessage?.error.error ?? '')}, { key: 'Time', title: 'Time', value: this._datePipe.transform(new Date(), 'h:mm:ss a') ?? ''}, { key: 'Status', title: 'Status', value: 'ERROR'}]]);
            if (!hideError) {
              this.playGround.showNodeError(errorMessage?.error?.node_id, errorMessage?.error?.message);
              this._toast.error(errorMessage?.error?.message ?? (errorMessage?.error.error ?? ''));
            }
          }
        })
      );
    }, 300);
  }

  checkJoinValidation(nodes: Node[]) {
    const joinTools = nodes.filter(x => x.type == connectionType.joinTool);
    return joinTools.some(x => !x.configuration.type || x.configuration.type == '');
  }

  resetColumnApi() {
    this.callColumnUpStrem();
    this.prepService.isConfigSave.set(false);
  }

  callColumnDownStrem() {
    const prepNodeData = this.getPayLoadData();
    this.columDownStreamSub?.unsubscribe();
    this.columDownStreamSub = this.prepService.postMethodRequest(`${prepsApiEndpoints.selectNode}${this.activeNode}${prepsApiEndpoints.coloumDownsterm}`, prepNodeData).subscribe({
      next: (dataColumn: PrepColumnInterface[]) => {
        this.downstreamColumn.set(dataColumn);
        this.columDownStreamSub.unsubscribe();
      }
    });
  }

  callColumnUpStrem() {
    this.errorMessageForTools = undefined;
    this.upStreamColumnList = [];
    this.allSourceColumnList = {};
    if (this.activeNode == '' || !this.activeNode || this.prepService.workflowRunningStatus() || this.currentConnectionCount < this.sourceLimit) {
      return;
    }
    const prepNodeData = this.getPayLoadData();
    const sourceNodes = prepNodeData.nodes.filter(x => x.type == connectionType.inputTool);
    if (sourceNodes.some(x => x.configuration.connection && x.configuration.connection.path == '')) {
      this.prepService.clearPreviewData();
      this.prepService.showPreview.set(false);
      this._toast.error('The source is missing in some fields!');
      return;
    }
    if (!prepNodeData.nodes.find(x => x.object_id == this.activeNode)) {
      return;
    }
    this.upStreamColumnList = [];
    this.allSourceColumnList = {};
    this.columUpstreamSub?.unsubscribe();
    this.columUpstreamSub = this.prepService.postMethodRequest(`${prepsApiEndpoints.selectNode}${this.activeNode}${prepsApiEndpoints.coloumUpstrem}?advance=true`, prepNodeData).subscribe(
      {
        next: value => {
          this.upstreamColumn.set(value);
          this.allSourceColumnList = value;
          const inputNodes: any = this.store.selectNodeDestinationConnection().destinationConnections(this.activeNode ?? '');
          const key: any = `columns_${inputNodes[0]?.source_anchor}`;
          this.upStreamColumnList = this.upstreamColumn()[Object.keys(this.upstreamColumn())?.[0]]?.[key] ?? [];
          this._cdr.detectChanges();
          this.columUpstreamSub?.unsubscribe();
        },
        error: error => {
          this.columUpstreamSub?.unsubscribe();
          if (error?.error?.error) {
            this._toast.error(error.error.error);
          }
          if (error?.error?.data?.processable === false) {
            this.errorMessageForTools = error.error.error;
          }

        }
      });


  }


  nodeClickEnd(event: NodeData | null) {
    this.activeNode = event?.objectId;
    if (this.prepService.currentNodeId() == event?.objectId) {
      return;
    }
    this.selectedNodeCurrent = event;
    this.selectedNode = null;
    this._cdr.detectChanges();
    this.selectedNode = this.selectedNodeCurrent;
    this._cdr.detectChanges();
    this.goToNextStep();
  }

  goToNextStep() {
    this.rightPanelWidth = 0;
    const rightComponent: any = this.rightPanel?.getNativeElement();
    this.rightPanelWidth = rightComponent?.nativeElement?.offsetWidth;
    this.prepService.currentNodeId.set(this.selectedNode?.objectId ?? null);
    this.prepService.currentNode.set(this.selectedNode);
    this.prepService.nodeChangeDetect.next('node selected');
    this.callUpstreamAndDownStream();
  }


  onDataSelect() {
    this.prepService.showPreview.set(true);
    this.dataSetResponse = this.prepService.uploadedFileResponse();
    if (this.dataSetResponse?.id) {
      this.prepService.currentNodeAndAnchor.next({ nodeId: this.prepService.currentNodeId(), anchor: 1, source: true, sourceId: this.dataSetResponse?.id, afterWorkflow: true });
    }

    // this.columnList = this.dataSetResponse.map((column: FileResponePrep) => {

    // });
  }

  saveEvent(event: { name: string, type: string }) {
    const workFlowData = this.getPayLoadData();
    const allNodes = this.store.selectNodeStoreAdvance().data();
    if (allNodes.nodes.length != workFlowData.nodes.length) {
      this._toast.warning(this._translate.instant('Some nodes are not connected. Please connect them or remove the nodes from the canvas.'));
    }
    workFlowData.name = event.name;
    workFlowData.replace_workflow = event.type == 'new' ? '' : this.prepService.selectedWorkflow();
    this.subs.add(this.prepService.postMethodRequest(`${prepsApiEndpoints.workflowsDraft}`, workFlowData).subscribe({
      next: (_data) => {
        this._toast.success('Workflow Saved Successfully');
        this.saveWorkflowModal.removeModal();
        this._modalService.removeAllModal();
        this.workFlowId.set(_data.workflow);
        this.fileName = '';
      },
      error: error => {
        this._toast.error(error.error.error);
      }
    }));
  }

  removePanel(event: any = '') {
    if (event?.type == 'cancel') {
      return;
    }
    this.selectedNode = null;
    this.rightPanelWidth = 0;
    this.removeSelection = true;
    this.activeNode = '';
    this.prepService.currentNodeId.set(null);
    this.prepService.clearPreviewData();
  }

  selectionRemoved(_event: boolean) {
    this.removeSelection = false;
  }

  onCreateConnection(nodeData: ConnectionNode) {
    this.store.addConnection(nodeData.inputNode.objectId, nodeData.outputNode.objectId,
      'connectionCreated', nodeData.sourceAnchor, nodeData.destinationAnchor, nodeData.sourceOutputCount, nodeData.destinationInputCount);
    this.prepService.nodeChangeDetect.next('new connection added');
    this.checkNodeConditions();
    if (this?.toolRef && !this.prepService.isConfigSave()) {
      this.toolRef?.onSave();
    }
    this.callColumnUpStrem();
  }

  onRemoveNode() {
    this.activeNode = '';
    this.selectedNode = null;
  }

  closeProceedModal(event: boolean) {
    if (!event) {
      this.toolRef?.onSave();
    }
    this.goToNextStep();
    this.confirmModal.removeModal();
    this._modalService.removeAllModal();
  }

  closeSaveWorkFlowModel() {
    this.saveWorkflowModal.removeModal();
    this._modalService.removeAllModal();
    this.fileName = '';
  }

  openWorkFlow() {
    setTimeout(() => {
      if (this.prepService.selectedWorkflow()) {
        this.setDraftDataToStore();
        this.prepService.clearPreviewData();
        this.prepService.showPreview.set(false);
      }
    }, 50);

  }

  createSaveWorkflowModel() {
    if (this?.toolRef && !this.prepService.isConfigSave()) {
      this.toolRef?.onSave();
    }
    const workFlowData = this.store.selectNodeStoreAdvance().data();
    if (workFlowData.name) {
      this.fileName = workFlowData.name;
    }
    this.saveWorkflowModal.createElement();
  }

  setDraftDataToStore() {
    this.prepService.getMethodRequest(`${prepsApiEndpoints.workflows + this.prepService.selectedWorkflow()}/draft-details`).subscribe({
      next: next => {
        this.store.updateStore({
          name: 'Workflow',
          description: '',
          nodes: [],
          connections: []
        });
        if (next.nodes?.length > 0) {
          this.prepService.availablePreviewNodes = {};
          next.nodes.forEach((element: {
            type: string; object_id: string | number;
          }) => {
            if (element.type == connectionType.inputTool) {
              this.prepService.availablePreviewNodes[element.object_id] = {
                prview: true
              };
            }
          });

        }
        this.store.updateStore(next);
        this.prepService.initWorkFlow.next(true);
        this.disableWorkFlow.set(false);
      },
      error: _error => {
        this._toast.error(_error.message);
      }
    });
  }

  collapsedEvent(_event: boolean) {
    this.collapsed = _event;

  }

  getPayLoadData(isWorkflow: boolean = false) {
    const prepNodeData = cloneDeep(this.store.selectNodeStoreAdvance().data());
    const connections = prepNodeData.connections.flatMap(conn => [conn.source, conn.destination]);
    const connectedNodes: any = [];
    if (isWorkflow) {
      this.prepService.availablePreviewNodes = {};
    }
    prepNodeData.nodes.forEach((element: Node) => {
      if (isWorkflow) {
        this.prepService.availablePreviewNodes[element.object_id] = {
          prview: true
        };
      }
      if (connections.includes(element.object_id)) {
        if (element.type != connectionType.joinTool && element.type != connectionType.unionTool) {
          connectedNodes.push(element);
        } else if (prepNodeData.connections.filter(y => y.destination == element.object_id).length > 1) {
          connectedNodes.push(element);
        }
      }
    });
    prepNodeData.nodes = connectedNodes;
    const objectIds = prepNodeData.nodes.flatMap(conn => [conn.object_id]);
    prepNodeData.connections = prepNodeData.connections.filter(z => objectIds.includes(z.destination));
    return prepNodeData;
  }

  openBasicTool(_event: boolean) {
    const activeButton: any = document.activeElement;
    activeButton?.blur();
    this.confirmModal.createElement();
  }

  openBasic() {
    this.confirmModal.removeModal();
    this._modalService.removeAllModal();
    this.stopWorkflow();
    this._router.navigate(['/analytics/data-preparation/upload-data']);
    this._prepService.uploadedFileResponse.set(null);
  }

  removeConfirmModal() {
    this.confirmModal.removeModal();
    this._modalService.removeAllModal();
    this.selectedTab = this.tabItems[1];
    this._cdr.detectChanges();
  }

  resetModalClose() {
    this.resetModal.removeModal();
    this._modalService.removeAllModal();
  }

  callDownload() {
    let index = 0;
    const nodeData = this.getPayLoadData();
    const allNodes = this.store.selectNodeStoreAdvance().data();
    if (allNodes.nodes.length != nodeData.nodes.length) {
      this._toast.warning(this._translate.instant('Some nodes are not connected. Please connect them or remove the nodes from the canvas.'));
    }
    if (nodeData.nodes?.length && nodeData.nodes.some(x => x.type == connectionType.outputTool)) {
      nodeData.nodes.forEach(element => {
        if (element.type == connectionType.outputTool) {
          index = index + 1;
          this.downloadFile(element, index);
        }
      });
    }
  }

  downloadFile(node: Node, index: number) {
    const subscribe = this.prepService.getDownloadRequest(`${environment.prepbaseUrl}${prepsApiEndpoints.datasetDownlaod}${node.object_id}/download/csv/`).subscribe({
      next: data => {
        // const matches = (data.headers.get('Content-Disposition'))?.match(/"(.*?)"/);
        const name = node.configuration.name ?? 'Untitled';
        const nameValue = `${name}_${index}`;
        this._downloadService.downloadFiles(data.body, nameValue);
        this._toast.success(`${nameValue} ${this._translate.instant('Download completed successfully!')}`);
        this.prepService.workflowRunningStatus.set(false);
        subscribe.unsubscribe();
      }
    });
  }

  nodeCreated(event: any) {
    const nodes = this.store.selectNodeStoreAdvance().data().nodes;
    if (nodes.length == 1) {
      this.prepService.currentNodeId.set(event.objectId);
      this.selectedNode = event;
    }
  }

  toggleCanvasTool(event: CanvasTool) {
    if (event.type == canvasTools.fullScreen) {
      this.toggleFullscreen();
      return;
    }
    this.canvasTool = event;
  }

  openResetModel(errorMessage: string) {
    this.resetErrorMessage = errorMessage;
    this.resetModal.createElement();
  }

  resetToolConfig() {
    const data = this.store.selectNodeStoreAdvance().data();
    if (data.nodes?.length > 0) {
      data.nodes.forEach(tool => {
        if (tool.type != connectionType.inputTool) {
          tool.configuration = connectionTypeConfigs[tool.type].config;
          const settings = {
            configUpdated: false,
            pos_x: tool.settings.pos_x,
            pos_y: tool.settings.pos_y,
            selectedNodeMainIndex: tool.settings.selectedNodeMainIndex
          };
          tool.settings = settings;
        }
      });
      this.prepService.isConfigSave.set(true);
      this.store.updateStore(data);
      this.callWorkFlow(false, true);
      this.resetModal.removeModal();
      this._modalService.removeAllModal();
    }
  }


  showPrompt() {
    const userInput = window.prompt('Please enter payload:' );
    if (userInput !== null) {
      this.store.updateStore({
        name: 'Workflow',
        description: '',
        nodes: [],
        connections: []
      });
      if (JSON.parse(userInput).nodes?.length > 0) {
        this.prepService.availablePreviewNodes = {};
        JSON.parse(userInput).nodes.forEach((element: {
          type: string; object_id: string | number;
        }) => {
          if (element.type == connectionType.inputTool) {
            this.prepService.availablePreviewNodes[element.object_id] = {
              prview: true
            };
          }
        });

      }

      setTimeout(() => {
        this.store.updateStore(JSON.parse(userInput));
        this.prepService.initWorkFlow.next(true);
        this.disableWorkFlow.set(false);
      }, 1000);

    } else {
      console.log('Prompt was canceled');
    }
  }


  ngOnDestroy(): void {
    this.subs.unsubscribe();
    this.columDownStreamSub?.unsubscribe();
    this.columUpstreamSub?.unsubscribe();
    this.controller?.abort();
    this.stopWorkflow();
    this.store.updateStore({
      name: 'Workflow',
      description: '',
      nodes: [],
      connections: []
    });
    if (this.sessionId) {
      this.log.logEnds(this.sessionId, this.log.currentTime );
    }
  }
}

