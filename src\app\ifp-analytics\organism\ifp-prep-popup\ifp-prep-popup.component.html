<div class="ifp-prep-popup">
  <div class="ifp-prep-popup__content">
    <div class="ifp-prep-popup__content-header">
      <em class="ifp-icon ifp-icon-cross ifp-prep-popup__header-icon" (click)="close()"></em>
    </div>
    <div class="ifp-prep-popup__content-body">
      <div>
        <div class="ifp-prep-popup__icon">
          <em class="ifp-icon ifp-icon-tick"></em>
        </div>
      </div>

      <h2 class="ifp-prep-popup__heading">
        {{heading | translate}}
      </h2>
      @if (fileName) {
        <h5 class="ifp-prep-popup__desc">
          <span class="ifp-prep-popup__filename">{{'File name' | translate}}: {{fileName | translate}} </span>{{desc | translate}}
        </h5>
      }


      <div  class="ifp-prep-popup__sub-body">

        @if(imageTitlePosition === 'top') {
          <h3 class="ifp-prep-popup__sub-title">
            {{subTitle | translate}}
          </h3>
        }

        <h6 class="ifp-prep-popup__sub-desc">
          {{subDesc | translate}}
        </h6>
        @if(imageTitlePosition === 'bottom') {
        <h3 class="ifp-prep-popup__sub-title ifp-prep-popup__sub-title--bottom">
          {{subTitle | translate}}
        </h3>
      }
        @if (type === 'image') {
          <div class="ifp-prep-popup__img-warpper-summery">
            <img class="ifp-prep-popup__img" [src]="imageUrl"/>
            <ifp-button class="ifp-prep-popup__btn-summery" (ifpClick)="summery()" [buttonClass]="buttonClass.primaryLight" [label]="imageText" [iconClass]="processedIcon ?  'ifp-icon-rightarrow': undefined"></ifp-button>

          </div>
        } @else if (type === 'btn') {
          <div class="ifp-prep-popup__btn-wrapper-export">
            <div  class="ifp-prep-popup__btn-export">
              <ifp-button class="ifp-prep-popup__btn-summery" [buttonClass]="buttonClass.primaryLight" [label]="'Export Now'" [iconPosition]="position.left" [iconClass]="'ifp-icon-download-thick'" (ifpClick)="exportClick()"></ifp-button>

              <ifp-button class="ifp-prep-popup__btn-summery" [buttonClass]="buttonClass.transpreantBlue" [label]="'Continue to Data Prep'" [iconClass]="'ifp-icon-rightarrow'" (ifpClick)="continuesPrep()"></ifp-button>
            </div>

          </div>

        }
         @else {
          <div class="ifp-prep-popup__btn-divi">
          <div class="ifp-prep-popup__btn-wrapper-common">
            @for (item of popupButtonList; track $index) {
              <ifp-tool-card (selectCard)="buttonEvent($event)" class="ifp-prep-popup__tool-card" [size]="'xsm'" [cardData]="item" [isFontIcon]="true"></ifp-tool-card>
            }


          </div>
          @if (orButton) {
            <p class="ifp-adv-tool__divider"><span class="ifp-adv-tool__divider-text">{{'OR' | translate}}</span></p>
          }
          @if (processButton) {
          <ifp-button class="ifp-prep-popup__btn-summery" [buttonClass]="processButtonDisabled ?  buttonClass.disabled: buttonClass.primaryLight" [label]="processText" [iconPosition]="position.right" [iconClass]="processIcon" (ifpClick)="processClick()"></ifp-button>
          }
        </div>
        }
        <div>

        </div>

      </div>

    </div>
    <div  class="ifp-prep-popup__footer">
    </div>

  </div>

</div>
