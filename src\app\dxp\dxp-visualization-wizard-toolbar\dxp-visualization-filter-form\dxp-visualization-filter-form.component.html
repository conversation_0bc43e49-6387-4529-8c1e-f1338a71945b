<form class="ifp-filter-form__new-filter" [formGroup]="addFilterForm">
  <div class="ifp-filter-form__filter-item">
    <p class="ifp-filter-form__filter-label">{{'Column' | translate}}</p>
    <app-ifp-db-dropdown formControlName="column" class="ifp-adv-tool__dropdown" [isMultiSelect]="false" [placeholder]="'Enter KPI Name'" [options]="columnList()" (singleSelected)="onSelectColumn($event)" [key]="'name'" [defaultSelect]="false"></app-ifp-db-dropdown>
  </div>

  <div class="ifp-filter-form__filter-item">
    <p class="ifp-filter-form__filter-label">{{'Filter Label' | translate}}</p>
    <input type="text" [placeholder]="'Enter Filter Name' | translate" formControlName="label" class="ifp-input">
  </div>

  <div class="ifp-filter-form__filter-item">
    <p class="ifp-filter-form__filter-label">{{'Default Filter Value' | translate}}</p>
    <app-ifp-db-dropdown [isMultiSelect]="false" [disabled]="!columnfiltervalues().length" [options]="columnfiltervalues()" (singleSelected)="onSelectDefault($event)" [placeholder]="'Select Default Filter Value'" [defaultSelect]="false" [key]="'id'" formControlName="values"></app-ifp-db-dropdown>
  </div>
  <div class="ifp-filter-form__btn-sec">
    <ifp-button [label]="'Add Filter'" [iconClass]="'ifp-icon-plus'" (ifpClick)="onAddFilter()" [buttonClass]="buttonClass.normalAplabetic +' '+ (addFilterForm.valid ? buttonClass.primary : buttonClass.disabled)" class="ifp-filter-form__filter-btn"></ifp-button>
    <ifp-button [label]="'Cancel'" [iconClass]="'ifp-icon-cross'" (ifpClick)="onCancelFilter()" [buttonClass]="buttonClass.normalAplabetic +' '+ buttonClass.secondary" class="ifp-filter-form__filter-btn"></ifp-button>
  </div>
</form>
