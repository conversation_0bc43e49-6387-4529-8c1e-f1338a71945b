<app-ifp-db-file-uploader #uploader (removeFile)="removeFile()" [enableInfo]="true" (fileUpload)="uploadFile($event)" [hideDelete]="true" class="ifp-gen-ai-dashboard-upload__browse" [dragOnly]="true" [iconBackgroung]="'ifp-file-upload__blue'" [supportText]="'Supported file formats'" [dragAndDropText]="'Drag & Drop File Or'"   [isImage]="false" [allowedExtensions]="allowedExtensions"  >

  <ul  class="ifp-gen-ai-dashboard-upload__info-quses info">
    <div  class="ifp-gen-ai-dashboard-upload__info-title">{{'Instructions Before Upload'| translate}}</div>
    <li class="ifp-gen-ai-dashboard-upload__info-qus" (click)="downloadFile()">
      {{'Make sure that the uploaded file aligns with the predefined template ,else'  | translate}}
         <u  class="ifp-gen-ai-dashboard-upload__info-under-line">{{'download from here'  | translate}}</u>
    </li>
  </ul>
</app-ifp-db-file-uploader>
@if (fileData().name && fileData.name !=='') {
  <ifp-upload-bar  (closeEvent)="removeFile();uploader.deleteFile()" [fileName]="fileName" [error]="fileError()" [width]="fileProgress" [size]="fileData().size" class="ifp-gen-ai-dashboard-upload__upload-bar" ></ifp-upload-bar>
}

