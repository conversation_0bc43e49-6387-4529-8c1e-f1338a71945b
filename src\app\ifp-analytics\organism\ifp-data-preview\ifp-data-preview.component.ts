import { ChangeDetector<PERSON><PERSON>, Component, ElementRef, EventEmitter, Input, OnDestroy, OnInit, Output, ViewChild, Renderer2, AfterViewInit } from '@angular/core';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { IfpDataTableComponent } from '../ifp-data-table/ifp-data-table.component';
import { IfpPanelDropdownComponent, PanelDropdownOptions } from '../../molecule/ifp-panel-dropdown/ifp-panel-dropdown.component';
import { NgClass } from '@angular/common';
import { IfpTooltipDirective } from 'src/app/scad-insights/core/directives/ifp-tooltip.directive';
import { IfpProbabilityCardComponent } from '../../molecule/ifp-probability-card/ifp-probability-card.component';
import { ifpColors } from 'src/app/scad-insights/core/constants/color.constants';
import { SubSink } from 'subsink';
import { IfpPrepService } from '../../data-prep/ifp-data-prep/ifp-prep-service';
import { prepsApiEndpoints } from '../../data-prep/ifp-data-prep/constants/if-preps.constants';
import { FileResponePrep } from '../../data-prep/ifp-data-prep/interface/ifp-data-prep.interface';
import { MetadataColumnPrep, PreivewPrep, PreviewNodeData, TypeProbability } from './interface/ifp-data-prep.intreface';
import { PaginationComponent } from '../../../scad-insights/ifp-widgets/ifp-molecules/pagination/pagination.component';
import { DownLoadService } from 'src/app/scad-insights/core/services/download-service/download.service';
import { ToasterService } from 'src/app/scad-insights/core/services/tooster/ToastrService.service';
import { environment } from 'src/environments/environment';
import { workFlowState } from '../../data-prep/ifp-data-prep/constants/ifp-state.contants';
import { selectNodePrepResponse } from '../../data-prep/ifp-data-prep/store/node-store/node-store.selectors';
import { Store } from '@ngrx/store';
import { IfpNoDataComponent } from '../../../scad-insights/ifp-widgets/ifp-molecules/ifp-no-data/ifp-no-data.component';
import { IfpSpinnerComponent } from '../../../scad-insights/ifp-widgets/ifp-molecules/ifp-spinner/ifp-spinner.component';
import { SubscriptionLike } from 'rxjs';
import { buttonClass } from 'src/app/scad-insights/core/constants/button.constants';
import { IfpTabComponent } from '../../../scad-insights/ifp-widgets/ifp-molecules/ifp-tab/ifp-tab.component';

@Component({
    selector: 'ifp-data-preview',
    templateUrl: './ifp-data-preview.component.html',
    styleUrl: './ifp-data-preview.component.scss',
    imports: [TranslateModule, IfpPanelDropdownComponent, IfpDataTableComponent, NgClass, IfpTooltipDirective, IfpProbabilityCardComponent, PaginationComponent, IfpNoDataComponent, IfpSpinnerComponent, IfpTabComponent]
})
export class IfpDataPreviewComponent implements OnInit, OnDestroy, AfterViewInit{

  @ViewChild('drager') drager!: ElementRef;
  @ViewChild('table') table!: ElementRef;
  @ViewChild('preview') preview!: ElementRef;
  @ViewChild('log') log!: ElementRef;
  @Output() changeView: EventEmitter<string> = new EventEmitter<string>();
  @Output() backEvent = new EventEmitter();

  @Input() showProbabilty: boolean = false;
  @Input() tableHead: string[] = [];
  @Input() tableData!: any[];
  @Input() tableView: 'normal' | 'detail' = 'normal';
  @Input() probabilityheading  = ['Categorical', 'Numeric', 'ID', 'Boolean'];
  @Input()  probabilityvalues: number[] = [];
  @Input() fullScreen = false;

  public uniqueValue = 0;
  public uniqueName = '';
  public uniqueColor = this.getColor(0);
  public offset: number = 0;
  public page: number = 1;
  public limit: number = 10;
  public size: number = 10;
  public subs = new SubSink();
  public dataSetResponse!:FileResponePrep | null;
  public dataValue!:PreivewPrep;
  public metaData = false;
  public firstLoad = false;
  public responseTime: string = '0';
  public metaDataValue!: MetadataColumnPrep[];
  public dragged = false;
  public pagination = false;
  public totalKeys:number=0;

  public columListDropdown: PanelDropdownOptions[] = [];
  public previewTableData: {heading: string[]; data: any} = {heading: [], data: []};
  public tableHeadNormal: string[] = [];
  public tableHeadDetail: string[] = ['Column Name', 'Data Type'];
  public currentSelectedValue: PanelDropdownOptions[] = [];
  public tableDataNormal:Record<string, string>[][] = [];
  public tableDataDetail:Record<string, any>[][] = [];
  public probalilityName = '';
  public loader = false;
  public previousY: number | null = null;
  public previewApi!:SubscriptionLike;
  public previewApiWorkFlow!:SubscriptionLike;
  public storeSub!:SubscriptionLike;
  public processSub!:SubscriptionLike;
  public totalRows:number | undefined=0;
  public modeSelected: string = 'preview';
  public buttonClass = buttonClass;
  public logHeading = ['Message', 'Time', 'status'];
  public selectedTabView!: { event: { key: string }; index: number };
  public menuTabs = [
    {
      name: 'Preview',
      key: 'preview'
    },
    {
      name: 'Log',
      key: 'log'
    }
  ];

  constructor(private _cdr: ChangeDetectorRef, public _preService:IfpPrepService,  private _downloadService: DownLoadService, private _toast: ToasterService, private store: Store, private _render: Renderer2,   private _translate: TranslateService) {}

  ngOnInit() {
    if (this.menuTabs.length) {
      this.selectedTabView = {
        event: {
          key: this.menuTabs[0].key ?? ''
        },
        index: 0
      };
    }
    if ( this._preService.uploadedFileResponse()) {
      this.dataSetResponse =  this._preService.uploadedFileResponse();
    }
    // if (this._preService.processStatus) {
    //   if (this._preService.currentNodeId() !== null &&  this._preService.workFlowID() !== null)  {
    //     this.callWorkflow();
    //   } else {
    //     this.callDatasetPreview();
    //   }
    // }
    this.subs.add(this._preService.processStatus.subscribe(() => {
      this._cdr.detectChanges();
      this.log.nativeElement.scrollTo(0, this.log.nativeElement.scrollHeight);
    }));

    this.storeSub =  this.store.select(selectNodePrepResponse).subscribe(() => {
      this.processSub?.unsubscribe();
      this.processSub =  this._preService.processStatus.subscribe(dataStatus=> {
        if (dataStatus == null) {
          this.tableDataDetail = [];
          this.loader = true;
          this.size =0;
        }
        if (dataStatus?.workflow_status === workFlowState.completed) {
          this.offset = 0;
          this.page = 1;
          this.limit= 10;
          this.size = 10;
          this.callWorkflow();
        } else if (dataStatus?.workflow_status === workFlowState.error) {
          this.loader =false;
        }
      });

    });
  }

  goBack() {
    this.backEvent.emit();
  }

  ngAfterViewInit(): void {
    if (!this.fullScreen) {
      this._render.setStyle(this.table.nativeElement, 'max-height', `${this._preService.previewWidth()}px`);

      this._render.listen( this.drager.nativeElement, 'mousedown', (e) => {
        this.dragged = true;
        this.previousY =  e.clientY;
      });


      this._render.listen( 'window', 'mouseup', () => {
        this.dragged = false;
      });

      this._render.listen( this.drager.nativeElement, 'touchstart', (e) => {
        this.dragged = true;
        this.previousY =  e.clientY;
      });


      this._render.listen( 'window', 'touchend', () => {
        this.dragged = false;
      });
      this._render.listen( 'window', 'dragend', () => {
        this.dragged = false;
      });

      this._render.listen( 'body', 'touchmove', (event) => {
        const currentY = event.clientY;
        this.dragSetup(currentY);
      });

      this._render.listen( 'body', 'mousemove', (event) => {
        const currentY = event.clientY;
        this.dragSetup(currentY);
      });
    }
  }

  dragSetup(currentY: number) {
    if (this.previousY !== null && this.dragged) {
      if (currentY > this.previousY) {
        const offset = (this.table.nativeElement.offsetHeight -15) < 300 ? 300 : (this.table.nativeElement.offsetHeight -15);
        this._render.setStyle(this.table.nativeElement, 'max-height', `${offset}px`);
        this._preService.previewWidth.set(offset);
        window.scrollTo(0, window.scrollY - 15);
        this._cdr.detectChanges();
      } else if (currentY < this.previousY) {

        const offset =  (this.table.nativeElement.offsetHeight +15 ) < 300 ? 300 : (this.table.nativeElement.offsetHeight +15 );
        this._render.setStyle(this.table.nativeElement, 'max-height', `${offset}px`);
        this._preService.previewWidth.set(offset);
        window.scrollTo(0, window.scrollY + 15 );
        this._cdr.detectChanges();
      }
    }
    this.previousY = currentY;
  }

  callDatasetPreview() {
    this.loader = true;
    const startTime = performance.now();
    this.previewApi?.unsubscribe();
    this.previewApi =   this._preService.getMethodRequest(prepsApiEndpoints.libraryDataset  + this.dataSetResponse?.id + prepsApiEndpoints.libraryPreview, {limit: this.limit, offset: this.offset, page: this.page} ).subscribe({next: (data:PreivewPrep)=> {
      const endTime = performance.now();
      const responseTime = endTime - startTime;
      this.responseTime = responseTime.toFixed(0);
      this.dataValue = data;
      this.size = data.count;
      if (data?.metadata?.columns) {
        this.metaData = true;
        this.metaDataValue = this.dataValue.metadata?.columns ?? [];
      }
      this.setUpTableValue();
      this.setUpMetaDataNormal();
      this.firstLoad = true;
      this._preService.previewTableData = {
        heading: this.tableHeadNormal,
        data: this.tableDataNormal
      };
      this.loader = false;
    }
    });
  }

  callWorkflow() {
    this.loader = true;
    const startTime = performance.now();
    this.previewApiWorkFlow?.unsubscribe();
    this.previewApiWorkFlow =   this._preService.getMethodRequest(prepsApiEndpoints.getNode  + this._preService.previousNodeId()+ prepsApiEndpoints.workflowPreview, {limit: this.limit, offset: this.offset, page: this.page} ).subscribe({next: (data:PreviewNodeData)=> {

      this.loader = false;
      const endTime = performance.now();
      const responseTime = endTime - startTime;
      this.responseTime = responseTime.toFixed(0);
      this.dataValue = {
        count: data.count,
        next: '',
        previous: null,
        records: data.records,
        object_id: this._preService.previousNodeId() ?? '',
        owner: '',
        created_at: '',
        deleted_at: null,
        name: data.name,
        description: '',
        storage_backend: '',
        status: '',
        file: ''
      };
      this.metaDataValue = data.result_metadata;
      this.totalRows = data.total_rows;
      if (data?.records?.length && data?.records?.length !==0) {
        this.totalKeys = Object.keys(data.records[0]).length;
      }
      this.size =  this.dataValue.count;
      if (data.result_metadata) {
        this.metaData = true;
      }
      this.setUpTableValue();
      this.setUpMetaDataNormal();
      this.firstLoad = true;
      this._preService.previewTableData = {
        heading: this.tableHeadNormal,
        data: this.tableDataNormal
      };
    }
    });
  }

  downloadFileClick() {
    if (this._preService.workFlowID()) {
      this.downloadFileNode();
    } else {
      this.downloadFile();
    }
  }

  downloadFile() {
    this.subs.add(this._preService.getDownloadRequest(`${environment.prepbaseUrl}/v1/dataset/${this.dataSetResponse?.id}/download/csv/`).subscribe( {next: data=> {
      // const matches = (data.headers.get('Content-Disposition'))?.match(/"(.*?)"/);
      const nameValue = `${this.dataSetResponse?.name ?? ''} - preview.xlsx`;
      // const nameValue = matches ? matches[1] : '.xlsx';
      this._downloadService.downloadFiles(data.body, nameValue);
    },
    error: err => {
      const error = err?.error;
      this.errorHandler(error);
    }
    }) );
  }


  downloadFileNode() {
    const selectedValue = this.currentSelectedValue.map(data=>  `"${data.value}"`);
    this.subs.add(this._preService.getDownloadRequest(`${environment.prepbaseUrl}${prepsApiEndpoints.getNode}${this._preService.previousNodeId()}${prepsApiEndpoints.nodePreviewDownload}?selected_columns=[${encodeURIComponent(selectedValue.toString())}]`).subscribe( {next: data=> {
      // const matches = (data.headers.get('Content-Disposition'))?.match(/"(.*?)"/);
      const nameValue = `${this.dataSetResponse?.name ?? ''} - preview.xlsx`;
      // const nameValue = matches ? matches[1] : '.xlsx';
      this._downloadService.downloadFiles(data.body, nameValue);
    },
    error: err => {
      const error = err?.error;
      this.errorHandler(error);
    }
    }) );
  }


  humanize(str: string) {
    let i;
    const frags = str.split('_');
    for (i=0; i<frags.length; i++) {
      frags[i] = frags[i].charAt(0).toUpperCase() + frags[i].slice(1);
    }
    return frags.join(' ');
  }

  onPageChange(offset: {page: number, offset: number}) {
    this.offset = offset.offset;
    this.page = offset.page;
    this.pagination = true;
    if (this._preService.previousNodeId() !== null &&  this._preService.workFlowID() !== null)  {
      this.callWorkflow();
    } else {
      this.callDatasetPreview();
    }
  }

  limitChanged(event: number) {
    this.offset = 0;
    this.page = 1;
    this.limit = event;
    this.pagination = true;
    if (this._preService.previousNodeId() !== null &&  this._preService.workFlowID() !== null)  {
      this.callWorkflow();
    } else {
      this.callDatasetPreview();
    }
  }

  errorHandler(error: any) {
    if (typeof error === 'string' ) {
      if (error!== '') {
        this._toast.error(error);
      }
    } else {
      for (const key in error) {
        if (Object.prototype.hasOwnProperty.call(error, key)) {
          const element = error[key];
          let data = '';
          if (typeof element === 'string' ) {
            data = element;
          } else {
            element.forEach((elementValue:string) => {
              data =  `${data} ${elementValue}`;
            });
          }
          if (data!== '') {
            this._toast.error(data);
          }
        }
      }
    }
  }

  setUpTableValue(){
    if (!this.pagination) {
      this.columListDropdown = [];
      if (this.metaDataValue) {
        const keys = this.metaDataValue;
        keys.forEach((data: MetadataColumnPrep) => {
          this.columListDropdown.push({key: data.name, value: data.name, checked: true});
        });
      } else {
        const keys =  Object.keys(this.dataValue.records[0]??{});
        keys.forEach((data:string ) => {
          this.columListDropdown.push({key: data, value: data, checked: true});
        });
      }
      this.currentSelectedValue =  this.columListDropdown;
    }
    this.pagination = false;
    this.setTable();
  }

  setTable() {
    this.tableDataNormal = [];
    this.tableHeadNormal = [];
    this.dataValue.records.forEach((element, index) => {
      this.tableDataNormal.push([]);
      this.currentSelectedValue.forEach(selectedValue => {
        this.tableDataNormal[index].push(
          { key: selectedValue.key, title: element[selectedValue.key], value: element[selectedValue.key], type: 'default' }
        );
      });
    });
    this.currentSelectedValue.forEach(data=> {
      this.tableHeadNormal.push(data.key);
    });
  }

  multiSelected(event: PanelDropdownOptions[] ){
    this.currentSelectedValue = event;
    this.setTable();
    this.setUpMetaDataNormal();
    this.setTableView(this.tableView);
  }


  setUpMetaDataNormal() {
    this.tableDataDetail = [];
    this.metaDataValue?.forEach((element:MetadataColumnPrep) => {
      this.currentSelectedValue.forEach(data=> {
        if (data.key === element.name) {
          this.tableDataDetail.push(
            [
              { key: 'name', title: 'Column Name', value: element.name ?? '-', type: 'default' },
              { key: 'type', title: 'Data Type', value: `${this._translate.instant(element.type ?? '-')} (${this._translate.instant(element.type_description ?? '')})`, type: 'default' }
              // {
              //   key: 'uniqueValues',
              //   title: 'Unique Values',
              //   type: element.unique_percentage !== undefined ? 'custom': 'default',
              //   value: '-',
              //   renderComponent: () => import('src/app/ifp-analytics/molecule/ifp-progress-value/ifp-progress-value.component').then(mode => mode.IfpProgressValueComponent),
              //   inputValues: {
              //     progress: element.unique_percentage ?? 0,
              //     progressValue: element.unique_count ?? 0,
              //     color: ifpColors.secondaryBlue
              //   }
              // },
              // {
              //   key: 'nullValues',
              //   title: 'Null Values',
              //   type: element.null_percentage !== undefined? 'custom': 'default',
              //   value: '-',
              //   renderComponent: () => import('src/app/ifp-analytics/molecule/ifp-progress-value/ifp-progress-value.component').then(mode => mode.IfpProgressValueComponent),
              //   inputValues: {
              //     progress: element.null_count,
              //     progressValue: element.null_percentage,
              //     color: ifpColors.secondaryBlue
              //   }
              // },
              // {
              //   key: 'sampleValue',
              //   title: 'Sample Value',
              //   value: element.sample_value !== undefined ?element.sample_value : '-',
              //   type: 'default'
              // },
              // { key: 'maxLength', title: 'Max Length', value: element.max_length !== undefined? element.max_length : '-', type: 'default' },
              // { key: 'minLength', title: 'Min Length', value: element.min_length !== undefined? element.min_length : '-', type: 1 },
              // { key: 'probability', title: element.name ?? '-', value: element.type_probability, type: 'hidden' }
            ]);
        }

      });
    });


  }

  rowClick(event: {row: any, index: number}) {
    if (this.tableView !== 'normal') {
      this.probabilityvalues = [];
      this. uniqueValue = 0;
      this. uniqueName = '';
      const probabilityData: TypeProbability = event.row[7].value;
      this.probabilityvalues.push(parseFloat(probabilityData.categorical.toFixed(2)));
      this.probabilityvalues.push(parseFloat(probabilityData.numeric.toFixed(2)));
      this.probabilityvalues.push(parseFloat(probabilityData.id.toFixed(2)));
      this.probabilityvalues.push(parseFloat(probabilityData.boolean.toFixed(2)));
      this.uniqueValue = parseFloat(probabilityData.uniqueness.toFixed(2));
      this. uniqueName = probabilityData.uniqueness_category;
      this.uniqueColor = this.getColor(this.uniqueValue);
      this.probalilityName =  event.row[7].title;
    }

  }

  getColor(value: number) {
    if (0.66<= value &&   value<= 1) {
      return ifpColors.green;
    } else if (0.33<= value &&   value<= 0.66) {
      return ifpColors.yellow;
    }
    return ifpColors.red;

  }

  setTableView(type: 'normal' | 'detail') {
    this.tableView = type;
    this.changeView.emit(type);
    this.probalilityName = '';
    this.probabilityvalues = [];
    if (type === 'detail') {
      this._preService.previewTableData = {
        heading: this.tableHeadDetail,
        data: this.tableDataDetail
      };
      return;
    }
    this._preService.previewTableData = {
      heading: this.tableHeadNormal,
      data: this.tableDataNormal
    };

  }

  setHeight(height: any) {
    this.table.nativeElement.style.maxHeight = `${height}px`;
    let updateHeight = 0;
    if (window.innerHeight < this.preview.nativeElement.offsetHeight) {
      updateHeight = 0;
    } else {
      updateHeight = height;
      this.preview.nativeElement.style.setProperty('--after-height', this.table.nativeElement.scrollHeight < updateHeight ? (`${(updateHeight - this.table.nativeElement.scrollHeight) / 2}px`) : 0);
      this._cdr.detectChanges();
    }
  }

  changeTabView(event: SelectedTabView) {
    this.selectedTabView = event;
    this.modeSelected = event.event.key;
  }

  ngOnDestroy(): void {
    this.subs.unsubscribe();
    this.previewApi?.unsubscribe();
    this.previewApiWorkFlow?.unsubscribe();
    this.storeSub?.unsubscribe();
    this.processSub?.unsubscribe();
  }
}

interface SelectedTabView {
  event: { key: string };
  index: number;
}
