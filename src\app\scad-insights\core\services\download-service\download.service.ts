import { Injectable, Renderer2, RendererFactory2 } from '@angular/core';

@Injectable({
  providedIn: 'root'
})
export class DownLoadService{

  private render!: Renderer2;
  constructor(renderFactory : RendererFactory2) {
    this.render = renderFactory.createRenderer(null, null);
  }

  downloadFiles(res: Blob | {file:string; name:string}| any, name: string) {

    const url = window.URL.createObjectURL(res);

    const a =  this.render.createElement('a');

    this.render.appendChild(document.body, a);

    a.setAttribute('style', 'display: none');

    a.href = url;

    a.download = name;

    a.click();

    window.URL.revokeObjectURL(url);

    a.remove();

  }

  downloadFilesWithoutBlob(url: string) {



    const a =  this.render.createElement('a');

    this.render.appendChild(document.body, a);

    a.setAttribute('style', 'display: none');

    a.href = url;

    // a.download = name;

    a.click();
    this.render.removeChild(document.body, a);

  }
}
