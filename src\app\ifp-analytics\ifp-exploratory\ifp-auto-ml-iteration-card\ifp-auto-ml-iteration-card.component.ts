import { Component, EventEmitter, Input, Output, ViewChild } from '@angular/core';
import { IfpDataToolAccordianComponent } from '../../molecule/ifp-data-tool-accordian/ifp-data-tool-accordian.component';
import { IfpAutoMlChartDataComponent } from '../ifp-auto-ml-chart-data/ifp-auto-ml-chart-data.component';
import { NgClass } from '@angular/common';
import { TranslateModule } from '@ngx-translate/core';
import { IfpRemoveCardComponent } from '../../../scad-insights/ifp-widgets/ifp-atoms/ifp-remove-card/ifp-remove-card.component';
import { IfpModalComponent } from '../../../scad-insights/ifp-widgets/ifp-organism/ifp-modal/ifp-modal.component';
import { IterationList } from '../ifp-exploratory-auto-ml-analyze/ifp-exploratory-auto-ml-analyze.component';

@Component({
    selector: 'ifp-auto-ml-iteration-card',
    imports: [IfpDataToolAccordianComponent, IfpAutoMlChartDataComponent, NgClass, TranslateModule, IfpRemoveCardComponent, IfpModalComponent, TranslateModule],
    templateUrl: './ifp-auto-ml-iteration-card.component.html',
    styleUrl: './ifp-auto-ml-iteration-card.component.scss'
})
export class IfpAutoMlIterationCardComponent {
  @ViewChild('alertModal') alertModal!: IfpModalComponent;
  @Input({required: true}) chartValueName: string = '';
  @Input() chartHeading: string = 'Accuracy';
  @Input({required: true})  chartValue: number = 0;
  @Input() badgeData: {column: string, variable_type: string}[] = [];
  @Input() chartList:{ name: string;
    value:  number | string; string?: boolean;} [] = [];

  @Input() aiText = '';
  @Input() isAccordianExpanded = false;
  @Input() chartTitle = '';
  @Input() alertText: string = 'Do you want to remove?';
  @Input() iterationItem!: IterationList;
  @Input() deleteIcon:string | boolean = 'ifp-icon-trash';
  @Input() centerValue = '';
  @Input() centerName = '';
  @Output() valueChangeEvent =  new EventEmitter();
  @Output() expandedEventEmit =  new EventEmitter();
  @Output() deleteEventEmit =  new EventEmitter();

  public selectedValues = {
    selected: 'selected variable',
    ignored: 'automatically ignored variable',
    unselected: 'ignored variable'
  };

  // used emit edited title value to parent
  valueChange(value: string) {
    this.chartTitle = value;
    this.valueChangeEvent.emit(value);
  }

  // event trigger while expanding the value
  expandedEvent(event: boolean) {
    this.expandedEventEmit.emit(event);
  }

  // event emit while deleting value
  deleteEvent() {
    this.alertModal.createElement();
  }

  // open and close delete model
  openOrCloseModel(event: boolean) {
    this.alertModal.removeModal();
    if (event) {
      this.deleteEventEmit.emit({id: this.iterationItem.object_id});
    }
  }
}
