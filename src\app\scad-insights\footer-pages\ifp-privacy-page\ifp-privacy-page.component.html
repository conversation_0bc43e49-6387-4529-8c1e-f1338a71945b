<div class="ifp-container">
  <app-ifp-breadcrumbs [pageData]="pageData"></app-ifp-breadcrumbs>
</div>

<div class="ifp-static" *ngIf="privacy">
  <div class="ifp-container">
    <h1 class="ifp-module-heading">{{privacy?.title}}</h1>
    <ng-container *ngIf="privacy?.content_data?.items.length"></ng-container>
    <div class="ifp-static__box" *ngFor="let item of privacy.content_data.items">
      <h2 class="ifp-static__title">{{item?.section_title}}</h2>
      <ng-container *ngIf="item.subsections.length"></ng-container>
      <div class="ifp-static__content" *ngFor="let subsection of item.subsections">
        <h3 class="ifp-static__sub-title">{{subsection?.title}}</h3>
        <p class="ifp-static__desc" [innerHTML]="(subsection?.content ? subsection.content : '') | lineBreak"></p>
      </div>
    </div>
  </div>
</div>
