@use "../../../../assets/ifp-styles/abstracts/index" as *;

:host {
  display: block;
}

.ifp-option-box {
  max-height: 250px;
  @include ifp-scroll-y(transparent, $ifp-color-grey-1, 4px, 8px);
  &__opt-wrapper {
    display: flex;
    align-items: center;
    padding: $spacer-2 $spacer-0;
    &:last-child {
      margin-bottom: $spacer-0;
    }
  }
  &__percentage {
    margin-inline-start: $spacer-2;
  }
  &__sub-title {
    font-weight: $fw-semi-bold;
  }
  &__opt-label {
    font-weight: $fw-medium;
    width: 100%;
  }
  &--border {
    border-radius: 7px;
    border: 1px solid $ifp-color-grey-7;
    padding: $spacer-2 $spacer-0;
    .ifp-option-box {
      &__opt-wrapper {
        padding: $spacer-2 $spacer-3;
        margin-bottom: $spacer-0;
      }
    }
    &.ifp-option-box--header {
      padding: $spacer-0;
      .ifp-option-box__opt-wrapper:first-child {
        border-bottom: 1px solid $ifp-color-grey-7;
      }
    }
  }
  &--wrapper {
    .ifp-option-box{
      &__opt-wrapper {
        border-radius:7px ;
        padding: $spacer-3;
      border: 1px solid $ifp-color-grey-7;
      margin-top: $spacer-2;


      &:last-child {
        margin-bottom: $spacer-2;
      }
    }
    &__opt-label{
      &--selected {
        border: 1px solid $ifp-color-blue-menu;
        background-color: $ifp-color-blue-menu;
      }
      &--limit {
        opacity: .7;
        cursor: not-allowed;
      }
    }
  }
  }
  &--progress {
    .ifp-option-box__opt-label {
      max-width: 60%;
      margin-inline-end: $spacer-4;
    }
  }
}
:host::ng-deep {
  .ifp-option-box {
    &--wrapper {
      .ifp-option-box__opt-label--limit {
        .ifp-checkbox__label {
          cursor: not-allowed;
        }
      }

      .ifp-option-box{
        &__opt-label{
          .ifp-checkbox__label {
            color: $ifp-color-secondary-grey;
            &::before {
              border: 2px solid $ifp-color-grey-14;
            }
          }
          &--selected {
            .ifp-checkbox__label {
              color: $ifp-color-white-global;
              &::before {
                background-color: $ifp-color-white-global !important;
              }
              &::after {
                border-color:   $ifp-color-blue-menu;
              }
            }
          }
      }}
    }
  }
}
