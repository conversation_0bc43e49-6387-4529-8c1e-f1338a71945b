@if (selectedItems() && selectedItems().length) {
@for (item of selectedItems(); let k=$index; track item) {
<div class="ifp-dxp-label-dropdown " appOutsideClick (outsideClick)="outsideClick()">
  <em [class]="' ifp-dxp-label-dropdown__icon ifp-icon '+ icon()"></em>
  <p class="ifp-dxp-label-dropdown__text">{{item.name}}</p>
  <div class="ifp-dxp-label-dropdown__dropdown" (click)="item.isOpen = !item.isOpen ">
    <p class="ifp-dxp-label-dropdown__dropdown-label"> {{item.dropdownValue ? item.dropdownValue : ''}}</p>

    <em class="ifp-dxp-label-dropdown__arrow ifp-icon ifp-icon-down-arrow"></em>
    @if (item.isOpen) {
    <div class="ifp-dxp-label-dropdown__dropdown-list">

      <ul class="ifp-dxp-label-dropdown__dropdown-list-ul">
        @for (opt of item[optionArrayKey()]; let i=$index; track opt) {
        <li (click)="selectItem(opt, k);isOpen.set(!isOpen())" class="ifp-dxp-label-dropdown__dropdown-list-li"
          [ngClass]="{'ifp-dxp-label-dropdown__dropdown-list--active':( opt && opt[dropdownKey()??''] ? opt[dropdownKey()??''] : (opt ?? ''))== dropdownValue}">
          {{opt && opt[dropdownKey()??''] ? opt[dropdownKey()??''] : (opt ?? '') }}</li>
        }
      </ul>

    </div>
    }
  </div>
  @if (hideCross()) {
  <em class="ifp-dxp-label-dropdown__cross ifp-icon ifp-icon-cross" (click)="close()"></em>
  }
</div>
}
}