@use "../../../../assets/ifp-styles/abstracts/index" as *;
.ifp-login{
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  background-size: cover;
  background-position: center;
  &__card {
    margin-left: $spacer-2 ;
    margin-right: $spacer-2;
    background-color: var(--ifp-color-white);
    border-radius: 10px;
    width: 100%;
    padding:  $spacer-6;
    border: solid var(--ifp-color-grey-7) 1px;
    overflow: hidden;
    max-height: 700px;
    max-width: 600px;
    position: relative;
    box-shadow: 0 0 30px #00000029;
}
&__icons {
  display: flex;
  justify-content:space-between;
  align-items: center;
  margin-bottom: $spacer-7;
}
&__btn-wrapper {
  max-width: 280px;
  margin: $spacer-0 auto;
}
&__bottom {
  display: flex;
  justify-content: center;
  align-items:  flex-end;
  position: absolute;
  text-align: center;
  bottom: 0;
  width: 110%;
  height: 40vh;
  background-color: $ifp-color-white;
  border-top-left-radius: 50% 60%;
  border-top-right-radius: 50% 60%;

}
&__bottom-value{
  margin-bottom: 50px;
}
// &__btn-login{
//   display: block;
// }
&__title,&__sub-title {
  text-align: center;
  margin: $spacer-2 $spacer-7;
}
&__sub-title {

  margin-bottom: $spacer-7;
}
&__title {
  font-size: $ifp-fs-13;
  font-weight: $fw-bold;
}
&__divider {
  text-align: center;
  position: relative;
  margin: $spacer-3 $spacer-0;
  &::before {
    content: "";
    width: 100%;
    height: 1px;
    background-color: $ifp-color-grey-7;
    position: absolute;
    top: 50%;
    left: 0;
  }
}
&__divider-text {
  color: $ifp-color-tertiary-text;
  padding: $spacer-0 $spacer-3;
  display: inline-block;
  background-color: $ifp-color-white;
  position: relative;
}
&__btn-icon {
  width: 25px;
  height: auto;
  margin-inline-end: $spacer-2;
}
}
:host::ng-deep {
  .ifp-login__btn-wrapper {
    .ifp-btn{
      font-size: $ifp-fs-4;
      width: 100%;
      min-height: 45px;
      padding: ($spacer-2 + 2px) $spacer-3;
      span {
        font-size: inherit;
      }
      .ifp-icon {
        font-size: $ifp-fs-6;
      }
      // &:hover {
      //   em {
      //   transform: translateX(16px);
      //   }
      // }
    }
  }
}
