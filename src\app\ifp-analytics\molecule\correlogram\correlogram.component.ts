import { NgStyle } from '@angular/common';
import { Component, Input } from '@angular/core';
@Component({
    selector: 'ifp-correlogram',
    imports: [NgStyle],
    templateUrl: './correlogram.component.html',
    styleUrl: './correlogram.component.scss'
})
export class CorrelogramComponent {
  @Input() correlogram: string[] = [];
  @Input() correlogramData?: Record<string, Record<string, {
    color?: string;
    value:number;
    name: string;
  }> >;

  getValue(index: number) {
    return  Array(index);
  }

}
