import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root'
})
export class BasicService {

  getObjectAsParams(object:any={}, starting: string= '?') {
    let currentParams = starting;
    for (const key in object) {
      if (Object.prototype.hasOwnProperty.call(object, key)) {
        const element = object[key];
        currentParams = `${currentParams + key}=${element}&`;
      }
    }
    return currentParams;
  }
}
