
import { Injectable } from '@angular/core';
import { Router} from '@angular/router';
import { environment } from 'src/environments/environment';
@Injectable({
  providedIn: 'root'
})
export class DevGuardService {

  constructor( private _router: Router) {
  }

  canActivate() {

    if (environment.env === 'dev') {
      return true;
    }
    this. routeToAuth();
    return false;
  }

  routeToAuth() {
    this._router.navigate(['/404']);
  }

}
