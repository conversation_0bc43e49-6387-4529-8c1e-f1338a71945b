
@if(columnOptions.length !== 0 && !loaderProcess()&& workflow()) {
  <div class="ifp-clean">
    <div class="ifp-clean__header-wrapper">
      <div class="ifp-clean__header">
        <h2 class="ifp-clean__title"> {{'Clean Data' | translate}}</h2>
        <h5 class="ifp-clean__sub-title"> {{'Select the columns you want to clean and selectively remove data to enhance accuracy and consistency' | translate}}.</h5>
      </div>
      <!-- <ifp-button [loader]="loaderProcess()" [label]="'Process' | translate" class="ifp-clean__button" [buttonClass]="selectedCols.length ===0  ||  nullValues.length ===0 ? buttonClass.disabled : buttonClass.primaryLight"
      [iconClass]="'ifp-icon-rightarrow'" (ifpClick)="proceed()"></ifp-button> -->
    </div>
    <div  class="ifp-clean__tabs">
      <div class="ifp-clean__item">
        <em class="ifp-icon ifp-icon-rightarrow ifp-clean__arrow"></em>
        <div class="ifp-clean__flex">
        <p class="ifp-clean__label">{{'Select the columns to clean' | translate}}<span style="color: #BA0202">*</span></p>
        </div>
        <ifp-option-box [translation]="false" [showBorder]="false" [isHeader]="true" class="ifp-clean__option-box" (selectOption)="getSelectedColumns($event)" [boxOptions]="columnOptions"
        [reduceLabelWidth]="true" [showNullPercentage]="true"></ifp-option-box>
      </div>
      <div class="ifp-clean__item">
        <div class="ifp-clean__flex">
          <p class="ifp-clean__label">{{'Remove Missing Values' | translate}}<span  class="ifp-clean__sub-label">
            {{'(Select the Columns to Continue)' | translate }}
          </span> </p>
          <div class="ifp-clean__info">
            <em class="ifp-icon ifp-icon-info-round"></em>
            <div class="ifp-clean__info-box ">
              <p class="ifp-clean__info-desc">{{'Missing represents the absence of any value or information, which is different than a blank or empty space, which still holds some visual representation in the data' | translate}}.</p>
            </div>
          </div>
        </div>
        <ifp-option-box  class="ifp-clean__option-box" [showBorder]="false" [ngClass]="{'ifp-clean__option-box--disable': selectedCols.length === 0}"  (selectOption)="getSelectedNull($event)" [boxOptions]="nullValueOptions"></ifp-option-box>
      </div>
    </div>
    </div>
} @else {
  @if(loader || loaderProcess() ||  !workflow()) {
    <div class="ifp-center-loader">
      <app-ifp-spinner></app-ifp-spinner>
    </div>
  } @else {
    <div class="ifp-clean">
      <app-ifp-no-data [message]="'No data available!'"></app-ifp-no-data>
    </div>

  }

}
<app-ifp-modal [overlayClass]="'ifp-modal__bg-grey'" #modalSucess >

  <ifp-prep-popup (closePopUp)="closeSucessModal()"   (buttonEventValue)="buttonClick($event)" [heading]="'Clean Data'" [desc]="''"  [fileName]="_prepService.uploadedFileResponse()?.name ?? ''"  [subTitle]="'Explore More'" [subDesc]="'What would you like to do next?'" ></ifp-prep-popup>

    </app-ifp-modal>
