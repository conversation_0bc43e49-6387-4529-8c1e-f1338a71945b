<div class="ifp-container">
  <app-ifp-breadcrumbs [pageData]="pageData"></app-ifp-breadcrumbs>
</div>
<div class="ifp-scenario-driver">
    <div class="ifp-container">
        <app-ifp-scenario-driver-card [analyticalId]="analyticalId" (realatedScenarios)="getRelatedScenarios($event)" (pageDataEmit)="createPageData($event)"></app-ifp-scenario-driver-card>
    </div>

    <div class="ifp-container ifp-scenario-driver__related" *ngIf="relatedScenarions?.length > 0 && relatedScenarions[0] !== ''">
    <ifp-carousel (slideEvent)="slideClicked()" [viewButton]="false" [label]="'Related Scenario Drivers' | translate" [customOption]="customOptions" [class]="carousalClass" #leftContainer>
      <ng-template *ngFor="let item of relatedScenarions; let index =index" ifp-carouselItem>
        <ifp-analysis-card [addMyApps]="false" [firstLoader]="true" [id]="item" [contentType]="'analytical-apps'"
          [delay]="300" [isRedirectTop]="true" [isRedirectTop]="true" [small]="index !== active" (resizedEvent)="resizedEvent($event, index)"></ifp-analysis-card>
      </ng-template>
    </ifp-carousel>
    </div>
</div>
