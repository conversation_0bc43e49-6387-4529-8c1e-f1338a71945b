import { createAction, createActionGroup, props } from '@ngrx/store';
import { PrepNode, PrepSelectConfiguration } from '../../interface/ifp-data-prep.interface';

export const loadNodeName = '[createNodeComponent]CreateNode';
export const loadCreateNodeSuccessSelectName= '[prepComponent]CreateNodeAddDupload';
export const loadCreateConnectionUploadCreateName = '[prepComponent]CreateNodeUploadCreate';
export const loadCreateConnectionName = '[prepComponent]CreateConnection';
export const loadCreateConnectionRemoveName = '[prepComponent]CreateConnectionRemove';
export const loadCreateConnectionUploadUpdateName = '[prepComponent]CreateNodeUploadUpdate';
export const loadRemoveConnectionUploadUpdateName = '[prepComponent]CreateNodeUploadRemove';
export const loadCreateNodeSuccessImputName = '[prepComponent]CreateNodeImputeCreate';
export const loadCreateNodeSuccessImputUpdateName = '[prepComponent]CreateNodeImputeUpdate';
export const loadCreateNodeSuccessImputRemoveName = '[prepComponent]CreateNodeImputeRemove';
export const loadCreateNodeSuccessUnionCreate = '[prepComponent]CreateNodeUnionCreate';
export const loadCreateNodeSuccessUnionRemove = '[prepComponent]CreateNodeUnionRemove';
export const loadCreateNodeSuccessUnionUpdate = '[prepComponent]CreateNodeUnionUpdate';
export const loadCreateNodeDestination = '[prepComponent]CreateNodeDestination';

export const loadCreateNodeSuccessColumnCreate = '[prepComponent]CreateNodeColumnCreate';
export const loadCreateNodeSuccessColumnRemove = '[prepComponent]CreateNodeColumnRemove';
export const loadCreateNodeSuccessColumnUpdate = '[prepComponent]CreateNodeColumnUpdate';


export const loadCreateNodeSuccessCleaningCreate = '[prepComponent]CreateNodeCleaningCreate';
export const loadCreateNodeSuccessCleaningRemove = '[prepComponent]CreateNodeCleaningRemove';
export const loadCreateNodeSuccessCleaningUpdate = '[prepComponent]CreateNodeCleaningUpdate';
export const loadCreateNodeClear = '[prepComponent]CreateNodeClear';
export const loadNode = createActionGroup({
  source: 'Create Node prep',
  events: {
    [loadCreateNodeSuccessSelectName]: props<{ config: PrepSelectConfiguration}>(),
    [loadCreateConnectionName]: props<{id: string, earseData: boolean, typeData?: string}>(),
    [loadCreateConnectionRemoveName]: props<{ objectId: string}>(),
    [loadCreateConnectionUploadCreateName]: props<{data: PrepNode }>(),
    [loadCreateConnectionUploadUpdateName]: props<{config: PrepSelectConfiguration, objectId: string}>(),
    [loadRemoveConnectionUploadUpdateName]: props<{ objectId: string}>(),
    [loadCreateNodeSuccessImputName]: props<{ config: PrepSelectConfiguration}>(),
    [loadCreateNodeSuccessImputUpdateName]: props<{ config: PrepSelectConfiguration, objectId: string}>(),
    [loadCreateNodeSuccessImputRemoveName]: props<{ objectId: string}>(),
    [loadCreateNodeSuccessUnionRemove]: props<{ objectIdData: string, objectIdUnion: string}>(),
    [loadCreateNodeSuccessUnionCreate]: props<{ config: PrepSelectConfiguration}>(),
    [loadCreateNodeSuccessUnionUpdate]: props<{ config: PrepSelectConfiguration, objectId: string}>(),

    [loadCreateNodeSuccessColumnRemove]: props<{ objectId: string}>(),
    [loadCreateNodeSuccessColumnCreate]: props<{ config: PrepSelectConfiguration}>(),
    [loadCreateNodeSuccessColumnUpdate]: props<{ config: PrepSelectConfiguration, objectId: string}>(),

    [loadCreateNodeSuccessCleaningRemove]: props<{ objectId: string}>(),
    [loadCreateNodeSuccessCleaningCreate]: props<{ config: PrepSelectConfiguration}>(),
    [loadCreateNodeSuccessCleaningUpdate]: props<{ config: PrepSelectConfiguration, objectId: string}>(),

    [loadCreateNodeDestination]: props<{ config: PrepSelectConfiguration}>(),
    [loadCreateNodeClear]: props<{ config?: ''}>()
  }
});
export const loadCreateNodeSuccess = createAction(loadNodeName);


