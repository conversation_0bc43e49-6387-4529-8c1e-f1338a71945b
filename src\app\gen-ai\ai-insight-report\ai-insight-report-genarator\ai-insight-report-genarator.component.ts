import { Component, inject, input, signal, viewChild } from '@angular/core';
import { IfpButtonComponent } from '../../../scad-insights/ifp-widgets/ifp-atoms/ifp-button/ifp-button.component';
import { IfpSpinnerComponent } from '../../../scad-insights/ifp-widgets/ifp-molecules/ifp-spinner/ifp-spinner.component';
import { IfpNoDataComponent } from '../../../scad-insights/ifp-widgets/ifp-molecules/ifp-no-data/ifp-no-data.component';
import { PaginationComponent } from '../../../scad-insights/ifp-widgets/ifp-molecules/pagination/pagination.component';
import { IfpModalComponent } from '../../../scad-insights/ifp-widgets/ifp-organism/ifp-modal/ifp-modal.component';
import { IfpRemoveCardComponent } from '../../../scad-insights/ifp-widgets/ifp-atoms/ifp-remove-card/ifp-remove-card.component';
import { IfpTooltipDirective } from 'src/app/scad-insights/core/directives/ifp-tooltip.directive';
import { TranslateModule } from '@ngx-translate/core';
import { IfpSearchComponent } from '../../../scad-insights/ifp-widgets/ifp-atoms/ifp-search/ifp-search.component';
import { DatePipe, NgClass } from '@angular/common';
import { KebabMenuOption } from 'src/app/dashboard-builder/molecule/ifp-kebab-menu/ifp-kebab-menu.interface';
import {
  buttonClass,
  buttonIconPosition,
} from 'src/app/scad-insights/core/constants/button.constants';
import { IfpTabComponent } from '../../../scad-insights/ifp-widgets/ifp-molecules/ifp-tab/ifp-tab.component';
import { Router } from '@angular/router';
import { SubSink } from 'subsink';
import { ApiService } from 'src/app/scad-insights/core/services/api.service';
import { aiInsightApi } from '../constants/ai-insight-api.contants';
import { IfpDbDropdownComponent } from '../../../dashboard-builder/molecule/ifp-db-dropdown/ifp-db-dropdown.component';
import { debounceTime, Subject, Subscription } from 'rxjs';

@Component({
  selector: 'ifp-ai-insight-report-genarator',
  imports: [
    IfpButtonComponent,
    IfpSpinnerComponent,
    IfpNoDataComponent,
    PaginationComponent,
    TranslateModule,
    NgClass,
    IfpModalComponent,
    IfpRemoveCardComponent,
    IfpTooltipDirective,
    IfpSearchComponent,
    IfpTabComponent,
    DatePipe,
    IfpDbDropdownComponent,
  ],
  templateUrl: './ai-insight-report-genarator.component.html',
  styleUrl: './ai-insight-report-genarator.component.scss',
})
export class AiInsightReportGenaratorComponent {
  public modal = viewChild<IfpModalComponent>('modal');
  public isModal = input(false);
  public currentTabIndex = 0;
  private _api = inject(ApiService);
  public subs = new SubSink();
  public callTable!: Subscription;
  public search: string = '';
  public buttonClass = buttonClass;
  public buttonIconPosition = buttonIconPosition;
  public searchObject = new Subject<string>();
  public table: {
    id: string;
    report_name: string;
    quarter: string;
    status: {
      color: string;
      display_name: string;
    };
    created_date: string;
  }[] = [];

  public tableHeader?: { key: string; name: string }[] = [];

  public kebabOptions: KebabMenuOption[] = [
    {
      name: 'Rename',
      event: 'rename',
      icon: 'ifp-icon-edit',
      disabled: false,
    },
    {
      name: 'Delete',
      event: 'delete',
      icon: 'ifp-icon-trash',
      disabled: false,
    },
  ];
  public offsetPage = 1;
  public offset: number = 0;
  public limit: number = 10;
  public _router = inject(Router);
  public editInput = false;
  public editIndex = 0;
  public editObjectId = '';
  public tabs = [
    { name: 'Datasets', event: 'dataset', disabled: false },
    { name: 'Workflows', event: 'workflow', disabled: false },
  ];
  public currentSelection = this.tabs[0].event;
  public loader = false;

  public sortValue?: string;
  public sortValueString?: string;
  public acceding?: boolean;
  public data = [
    { name: 'Domain Reports', key: 'domain' },
    { name: 'Consolidated Report', key: 'consolidate' },
  ];
  public options = [
    {
      key: 'All',
      value: 'All',
      checked: false,
    },
    {
      key: 'Active',
      value: 'Active',
      checked: false,
    },
    {
      key: 'In Progress',
      value: 'In Progress',
      checked: false,
    },
  ];

  public quarterOptions = [
    {
      key: null,
      value: 'All',
      checked: false,
    },
    {
      key: '1',
      value: 'Q1',
      checked: false,
    },
    {
      key: '2',
      value: 'Q2',
      checked: false,
    },
    {
      key: '3',
      value: 'Q3',
      checked: false,
    },
    {
      key: '4',
      value: 'Q4',
      checked: false,
    },
  ];
  public totalCount = 0;
  public doamainType = this.data[0].key;
  public reportStatus: {
    id: string;
    object_id: string;
    value: string;
    color: string;
  }[] = [];
  public ConsolidatedStatus: {
    id: string;
    object_id: string;
    value: string;
    color: string;
  }[] = [];
  public status: string = '';
  public statusConsolidated: string = '';
  public quarter: string = '';

  public disableTranslate = signal(true);
  onBack() {
    this.editInput = false;
    this._router.navigateByUrl('/home');
  }

  searchEvent(key: string) {
    this.search = key;
    this.offsetPage = 1;
    this.offset = 0;
    this.searchObject.next(this.doamainType);
  }

  sort(name: string, sort: boolean) {
    if (sort) {
      this.sortValue = `${name}-DESC`;
    } else {
      this.sortValue = `${name}-ASC`;
    }
    this.sortValueString = name;
    this.acceding = sort;
    this.callInsList(this.doamainType);
  }
  selectedTabEvent(event: {
    event: {
      name: string;
      key: string;
    };
    index: number;
  }) {
    this.status = '';
    this.statusConsolidated='';
    this.currentTabIndex = event.index;
    this.callInsList(event.event.key);
  }

  searchCall() {
    this.searchObject.pipe(debounceTime(300)).subscribe((data: string) => {
      this.callInsList(data);
    });
  }

  callInsList(type: string) {
    this.doamainType = type;
    const data: {
      report_type: string;
      offset: number;
      limit: number;
      search?: string;
      status?: string;
      quarter?: string;
      sort?: string;
    } = {
      report_type: type,
      offset: this.offset,
      limit: this.limit,
    };
    if (this.sortValueString && this.sortValueString !== '') {
      data['sort'] = this.sortValue;
    }
    if (this.search && this.search !== '') {
      data['search'] = this.search;
    }

    if (this.quarter && this.quarter !== '') {
      data['quarter'] = this.quarter;
    }
    if ((this.status && this.status !== '')||(this.statusConsolidated && this.statusConsolidated !== '') ) {
      data['status'] = this.currentTabIndex==0 ? this.status : this.statusConsolidated;
    }

    this.callTable = this._api
      .getMethodRequest(aiInsightApi.insightList, data)
      .subscribe(
        (data: {
          headers: {
            name: string;
            key: string;
          }[];
          count: number;
          data: {
            id: string;
            report_name: string;
            quarter: string;
            status: {
              color: string;
              display_name: string;
            };
            created_date: string;
          }[];
        }) => {
          this.totalCount = data.count;
          this.table = data.data;
          this.tableHeader = data.headers;
          this.callTable.unsubscribe();
        }
      );
  }
  callStatusList() {
    this.subs.add(
      this._api
        .getStatusRequest(aiInsightApi.getStatusList)
        .subscribe((data: any) => {
          this.reportStatus = data.data;
        })
    );
  }
  callStatusListConsolidated() {
    this.subs.add(
      this._api
        .getStatusRequest(aiInsightApi.getStatusConsolidatedList)
        .subscribe((data: any) => {
          this.ConsolidatedStatus = data.data;
        })
    );
  }

  gotoPage(id: string) {
    if (this.data[this.currentTabIndex].key == this.data[0].key) {
      this._router.navigateByUrl('/insight-report-generator/' + id);
    } else if (this.data[this.currentTabIndex].key == this.data[1].key) {
      this._router.navigateByUrl(
        '/consolidated-insight-report-generator/' + id
      );
    }
  }

  onPageChange(event: any) {
    this.offsetPage = event + 1;
    this.offset = event;
    this.callInsList(this.doamainType);
  }

  limitChanged(event: any) {
    this.offsetPage = 1;
    this.offset = 0;
    this.limit = event;
    // console.log("limit", this.limit);
  }
  ngOnInit(): void {
    this.callInsList(this.data[0].key);
    this.callStatusList();
    this.callStatusListConsolidated();
    this.searchCall();
  }

  closeModalEvent(event: Event) {}

  selectStatus(event: any) {
    this.status = event.display_name ? event.display_name : '';
    this.callInsList(this.data[this.currentTabIndex].key);
  }


  selectStatusConsolidated(event: any) {
    this.statusConsolidated = event.display_name ? event.display_name : '';
    this.callInsList(this.data[this.currentTabIndex].key);
  }

  selectQuarter(event: any) {
    this.quarter = event.key ? event.key : '';
    this.callInsList(this.data[this.currentTabIndex].key);
  }

  ngOnDestroy(): void {
    this.subs.unsubscribe();
    this.callTable.unsubscribe();
  }
}
