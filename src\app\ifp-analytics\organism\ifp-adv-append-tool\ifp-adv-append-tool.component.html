@if (!workFlowRunning() && !emptyConnection() && currentConnectionCount >= 2 && sourceData() && appendForm && !errorMessageForTools) {
<!-- <div class="ifp-adv-tool__action">
  <div class="ifp-adv-tool__action-inner">
    <ifp-button [label]="'cancel'" (ifpClick)="onCancel()" class="ifp-adv-tool__button ifp-adv-tool__action-btn" [buttonClass]="buttonClass.secondary"></ifp-button>
    <ifp-button [label]="'Save'" (ifpClick)="onSave()" class="ifp-adv-tool__button ifp-adv-tool__action-btn"  ></ifp-button>
  </div>
</div> -->
<form [formGroup]="appendForm">
<!-- <app-ifp-check-box   class="ifp-adv-tool__checkbox ifp-adv-tool__checkbox--radio" [label]="'Append columns in Target table' | translate" [type]="'radio'" [name]="'appendType'" (checked)="selectAppendType(appendKeys.columnType)"  [checkedData]="isColumn === appendKeys.columnType"></app-ifp-check-box>
<app-ifp-check-box   class="ifp-adv-tool__checkbox ifp-adv-tool__checkbox--radio" [label]="'Append rows under Target table' | translate" [type]="'radio'" [name]="'appendType'" (checked)="selectAppendType(appendKeys.rowType)" [checkedData]="isColumn === appendKeys.rowType"></app-ifp-check-box> -->

<!-- @if(isColumn == appendKeys.columnType) {
  <div class="ifp-adv-tool__content ifp-adv-tool__content--border">
    <p class="ifp-adv-tool__sub-title">{{'Data from the Source columns will be retrieved and integrated into the Target dataset.' | translate}}</p>
    <div formArrayName="leftColumns" class="ifp-adv-append__accordian">
      @for (item of leftColumnsArrays.controls; let i = $index; track item) {
        <ng-container [formGroup]="item">
      <ifp-data-tool-accordian [title]="item.value.columnName" [isChecked]="item.value.checked" class="ifp-adv-tool__accordian"
      formControlName="checked">
        <div class="ifp-accord-box__content">
          <div class="ifp-adv-tool__edit-outer">
            <p class="ifp-adv-tool__label">{{'Rename column' | translate}}</p>
            <input type="text" formControlName="rename" class="ifp-adv-tool__input" [placeholder]="'Enter here' | translate">
          </div>
        </div>
      </ifp-data-tool-accordian>
      </ng-container>
    }
  </div>
  <p class="ifp-adv-tool__sub-title">{{'Data from the Target columns will be retrieved and integrated into the Target dataset.' | translate}}</p>
  <div formArrayName="rightColumns">
    @for (item of rightColumns.controls; let i = $index; track item) {
      <ng-container [formGroup]="item">
      <ifp-data-tool-accordian [title]="item.value.columnName" [isChecked]="item.value.checked" class="ifp-adv-tool__accordian"
      formControlName="checked">
        <div class="ifp-accord-box__content">
          <div class="ifp-adv-tool__edit-outer">
            <p class="ifp-adv-tool__label">{{'Rename column' | translate}}</p>
            <input type="text" formControlName="rename" class="ifp-adv-tool__input" [placeholder]="'Enter here' | translate">
          </div>
        </div>
      </ifp-data-tool-accordian>
      </ng-container>
    }
  </div>
  </div> -->

  <!-- <div class="ifp-adv-tool__content ifp-adv-tool__content--grey">
    <div class="ifp-adv-tool__checkbox-dropdown" [ngClass]="{'ifp-adv-tool__checkbox-dropdown--active': loopSource}">
      <app-ifp-check-box class="ifp-adv-tool__checkbox" [label]="'Loop Source to Fit Target Size' | translate" [checkedData]="loopSource" (checked)="onEnableLoopSource($event)"></app-ifp-check-box>
      <em class="ifp-icon ifp-icon-down-arrow"></em>
    </div>
    <div class="ifp-adv-tool__dropdown-list">
      @for (column of columnList; track $index) {
        <div class="ifp-adv-tool__item-box">
          <em class="ifp-icon ifp-icon-dragable"></em>
          <app-ifp-check-box class="ifp-adv-tool__checkbox" [label]="column.name | translate" (checked)="setCaseSensitive($event, column)" [checkedData]="column.isSelected"></app-ifp-check-box>
        </div>
      }
    </div>
  </div> -->
<!-- } @else { -->
  <div class="ifp-adv-tool__content ">
    <p class="ifp-adv-tool__sub-title">{{'Data from the Source rows will be appended under the Target dataset.' | translate}}</p>
    <div formArrayName="mappedColumns" class="ifp-adv-append__accordian">
      @for (item of mappedColumns.controls; let i = $index; track item) {
        <ng-container [formGroup]="item">
      <ifp-data-tool-accordian [disableTranslation]="true" [title]="item.value.columnName" [isChecked]="item.value.checked" class="ifp-adv-tool__accordian"
      formControlName="checked" >
        <div class="ifp-accord-box__content">
          <div class="ifp-adv-tool__edit-outer">
            <p class="ifp-adv-tool__label">{{'Map Target Column' | translate}}</p>
            @if (rightTableColumn() && rightTableColumn().length) {
              <app-ifp-db-dropdown [disableTranslation]="true" formControlName="mapedColumn" class="ifp-adv-tool__dropdown" [isMultiSelect]="false" [options]="getTargetOptions(item.value.columnName)" (singleSelected)="selectTarget($event, item.value.columnName)"  [key]="'name'"
              [defaultSelect]="item.value?.mapedColumn ? true: false" [disableTranslation]="true"></app-ifp-db-dropdown>
            }
          </div>
        </div>
      </ifp-data-tool-accordian>
    </ng-container>
    }
    </div>
  </div>

  <!-- <div class="ifp-adv-tool__content  ifp-adv-append__content ifp-adv-tool__content--grey">
    <app-ifp-check-box class="ifp-adv-tool__checkbox ifp-adv-tool__checkbox--radio" [label]="'Sequentially append the data as it is' | translate" [type]="'radio'" [name]="'fromDataset'" (checked)="bulkAppendType(false)" [checkedData]="!fromDataset"></app-ifp-check-box>
    <div class="ifp-adv-tool__checkbox-dropdown" [ngClass]="{'ifp-adv-tool__checkbox-dropdown--active': fromDataset}">
      <app-ifp-check-box class="ifp-adv-tool__checkbox ifp-adv-tool__checkbox--radio" [label]="'Fill gaps for missing items in Target dataset rows' | translate" [type]="'radio'" [name]="'fromDataset'" (checked)="bulkAppendType(true)" [checkedData]="fromDataset"></app-ifp-check-box>
      <em class="ifp-icon ifp-icon-down-arrow"></em>
    </div>
    <div class="ifp-adv-tool__dropdown-list">
      @for (column of sourceColumnList; track $index) {

        <ifp-data-tool-accordian [title]="column.name" [isChecked]="column.isSelected" [isDraggable]="true" class="ifp-adv-tool__accordian">
          <div class="ifp-accord-box__content">
            <div class="ifp-adv-tool__edit-outer">
              <p class="ifp-adv-tool__label">{{'Map Target' | translate}}</p>
              <app-ifp-db-dropdown class="ifp-adv-tool__dropdown" [isMultiSelect]="false" [options]="leftTableColumn()" (singleSelected)="selectTarget()" [selectedSingleItem]="column.dataType" [key]="'name'"></app-ifp-db-dropdown>
            </div>
          </div>
        </ifp-data-tool-accordian>
      }
      <app-ifp-check-box class="ifp-adv-tool__checkbox" [label]="'Sequentially append the rest of the data' | translate" [checkedData]="isAllSequential" (checked)="selectAllSequential($event)"></app-ifp-check-box>
    </div>
  </div> -->
<!-- } -->
</form>
} @else {
  @if(errorMessageForTools) {
    {{errorMessageForTools}}
  } @else if(workFlowRunning()) {
  <div class="ifp-center-loader">
    <app-ifp-spinner></app-ifp-spinner>
    <p> {{'Workflow Running' | translate}} </p>
  </div>

  } @else if (emptyConnection()){
  <p class="ifp-adv-tool__error"> <em class="ifp-icon ifp-icon-exclamation-round">

    </em>
    {{'Please choose a source and target' | translate}}
  </p>
  } @else if (!sourceData()) {
  <p class="ifp-adv-tool__error"> <em class="ifp-icon ifp-icon-exclamation-round">

    </em>
    {{'Must upload atleast two source' | translate}}
  </p>
  } @else if(currentConnectionCount < 2){ <p class="ifp-adv-tool__error"> <em class="ifp-icon ifp-icon-exclamation-round">

    </em>
    {{'Node must have atleast two connection' | translate}}
    </p>
    }
    @else {
    <app-ifp-spinner></app-ifp-spinner>
    }

    }


