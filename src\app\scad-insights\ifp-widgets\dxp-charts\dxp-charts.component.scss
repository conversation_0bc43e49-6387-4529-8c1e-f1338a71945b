@use '../../../../assets/ifp-styles/abstracts' as *;

.ifp-chart-container {
  display: flex;
  flex-direction: column;
  gap: $spacer-3;
}

.ifp-chart-dropdown {
  display: flex;
  justify-content: flex-end;
  margin-bottom: $spacer-2;
  
  .ifp-dropdown {
    min-width: 200px;
    max-width: 300px;
  }
}

:host ::ng-deep {
  .ifp-high-chart{
    &__donut-center {
      text-align: center;
    }
    &__donut-heading {
      font-size: $ifp-fs-14;
      font-weight: $fw-bold;
    }
    &__donut-value {
      font-size:$ifp-fs-4;
      fill:$ifp-color-grey-14;
    }
    &__donut-value-bold {
      font-size:$ifp-fs-4;
      font-weight: $fw-bold;
    }
  }
  .ifp-line-doted-axis {
    .highcharts-tick {
      stroke-linecap: round;
      cursor: pointer;
    }
    .ifp-high-chart__wrapper {
      &_dot-band{
        fill: transparent;
        stroke-dasharray: 10 10;
        stroke: #D9DCDD;
    }
  }
  }
}

// Add a specific class for the pie chart wrapper
.ifp-high-chart__pie-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100vh; // Fill viewport height
  min-height: 600px;
  min-width: 600px;
}

// Make Highcharts container and SVG fill parent and center chart ONLY for pie chart
:host ::ng-deep .ifp-high-chart__pie-wrapper .highcharts-container,
:host ::ng-deep .ifp-high-chart__pie-wrapper .highcharts-root {
  width: 100% !important;
  height: 100% !important;
  margin: 0 auto !important;
  display: block !important;
}

// Restore original wrapper for other charts
.ifp-high-chart {
  &__wrapper {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    min-height: 300px;
    min-width: 300px;
  }
}

:host-context(.ifp-dark-theme) {
  ::ng-deep {
    .ifp-high-chart{
      &__donut-value-bold {
      fill: $ifp-color-white-global;
      }
    }
  }

 }
