import { ChangeDetector<PERSON>ef, Component, EventEmitter, inject, Input, OnChang<PERSON>, OnDestroy, OnInit, Output, signal, SimpleChanges, WritableSignal } from '@angular/core';
import { IfpDbDropdownComponent } from '../../../dashboard-builder/molecule/ifp-db-dropdown/ifp-db-dropdown.component';
import { TranslateModule } from '@ngx-translate/core';
import { ColumnList } from '../../data-prep/ifp-advance-prep/interface/ifp-advance-prep.interface';
import { IfpDataToolAccordianComponent } from '../../molecule/ifp-data-tool-accordian/ifp-data-tool-accordian.component';
import { buttonClass } from 'src/app/scad-insights/core/constants/button.constants';
import { ColumnPrep } from '../../data-prep/ifp-data-prep/ifp-prep-select-data/interface/prep-selection.interface';
import { selectNodeStoreAdvance } from '../../data-prep/ifp-advance-prep/store/advance-node-store/advance-node.store';
import { FormArray, FormBuilder, FormControl, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { connectionType } from '../../data-prep/ifp-data-prep/constants/if-preps.constants';
import { IfpAdvancePrepService } from '../../data-prep/ifp-advance-prep/service/ifp-advance-prep.service';
import { ToasterService } from 'src/app/scad-insights/core/services/tooster/ToastrService.service';
import { appendKeys } from './ifp-adv-append.constants';
import { IfpSpinnerComponent } from '../../../scad-insights/ifp-widgets/ifp-molecules/ifp-spinner/ifp-spinner.component';
import { cloneDeep } from 'lodash';


@Component({
    selector: 'ifp-adv-append-tool',
    templateUrl: './ifp-adv-append-tool.component.html',
    styleUrl: './ifp-adv-append-tool.component.scss',
    imports: [IfpDbDropdownComponent, TranslateModule, IfpDataToolAccordianComponent,
    FormsModule, ReactiveFormsModule, IfpSpinnerComponent]
})
export class IfpAdvAppendToolComponent implements OnChanges, OnInit, OnDestroy {

  @Output() cancel = new EventEmitter();
  @Output() save = new EventEmitter();

  @Input({ required: true }) sourceColumnList!: any;
  @Input() currentNodeId?: string = '';
  @Input() targetColumnList!: ColumnList[];
  @Input({ required: true }) currentConnectionCount: number = 0;
  @Input() sourceData: WritableSignal<boolean> = signal(false);
  @Input() workFlowRunning = signal(false);
  @Input() emptyConnection = signal(true);
  @Input() currentConection: WritableSignal<boolean> = signal(false);
  @Input() errorMessageForTools?: string;

  public isColumn: string = 'append_columns';
  public loopSource: boolean = true;
  public fromDataset: boolean = false;
  public isAllSequential: boolean = true;
  public buttonClass = buttonClass;
  public appendForm!: FormGroup;
  public leftTableColumn: WritableSignal<ColumnPrep[]> = signal([]);
  public rightTableColumn: WritableSignal<ColumnPrep[]> = signal([]);
  public isConfigUpdated: WritableSignal<boolean> = signal(false);
  public readonly advanceStore = inject(selectNodeStoreAdvance);
  public appendKeys = appendKeys;
  public nodeData: any;
  public selectedTargetColumns: any[] = [];

  constructor(private _cdr: ChangeDetectorRef, private _fb: FormBuilder, private _prepService: IfpAdvancePrepService
    , private _toaster: ToasterService
  ) { }



  ngOnInit(): void {
    this.appendForm = this._fb.group({
      appendType: [appendKeys.rowType, Validators.required],
      leftColumns: this._fb.array([]),
      rightColumns: this._fb.array([]),
      mappedColumns: this._fb.array([])
    });
  }


  ngOnChanges(changes: SimpleChanges): void {
    if (changes['sourceColumnList'] && this.sourceColumnList && (Object.keys(this.sourceColumnList).length > 0) && this.currentConnectionCount >= 2) {
      this.setTableColumns();
      this.setInitialColumns();
      this.setNodeData();
      this._cdr.detectChanges();
    }
  }

  setNodeData() {
    this.nodeData = cloneDeep( this.advanceStore.selectNodeValue().nodeValue(this.currentNodeId ?? ''));
    this.nodeData.configuration.mapping = this.nodeData.configuration?.mapping ?? this.getRowConfiguration()?.mapping;
  }

  selectAppendType(event: string) {
    this.appendForm.controls['appendType'].setValue(event);
    this.isColumn = event;
  }

  onEnableLoopSource(event: boolean) {
    this.loopSource = event;
  }

  setCaseSensitive(event: boolean, item: ColumnList) {
    item.isSelected = event;
  }

  selectAllSequential(event: boolean) {
    this.isAllSequential = event;
  }

  selectTarget(event: any, title: string) {
    setTimeout(() => {
      const index = this.selectedTargetColumns.findIndex(x => x.key === title);
      index >= 0 ? this.selectedTargetColumns[index].value = event.name : this.selectedTargetColumns.push({ key: title, value: event.name });
    }, 100);
  }

  getTargetOptions(title: string) {
    return this.rightTableColumn().map((element: any) => ({
      ...element,
      isHide: this.selectedTargetColumns.some(x => x?.key !== title && x?.value === element.name)
    }));
  }

  bulkAppendType(event: boolean) {
    this.fromDataset = event;
    if (!event) {
      this.isAllSequential = false;
    }
  }

  onCancel() {
    this.cancel.emit();
  }

  onSave() {
    const config: any = this.getRowConfiguration();
    this.advanceStore.updateNode(this.currentNodeId ?? '', 'Appendtool updateded', connectionType.unionTool, config, 'Appendtool updateded', { configUpdated: true });
    this._prepService.nodeChangeDetect.next('node updated');
    this.setNodeData();
    this.save.emit();
  }

  get leftColumnsArrays(): FormArray<FormGroup<ColumnsArray>> {
    return this.appendForm?.get('leftColumns') as FormArray<FormGroup<ColumnsArray>>;
  }

  get rightColumns(): FormArray<FormGroup<ColumnsArray>> {
    return this.appendForm?.get('rightColumns') as FormArray<FormGroup<ColumnsArray>>;
  }

  get mappedColumns(): FormArray<FormGroup<ColumnsArray>> {
    return this.appendForm?.get('mappedColumns') as FormArray<FormGroup<ColumnsArray>>;
  }

  setTableColumns() {
    this.resetVariables();
    const sourceNodes: any = this.advanceStore.selectNodeDestinationConnection().destinationConnections(this.currentNodeId ?? '');

    const sourceIndex = sourceNodes.findIndex((x: { destination_anchor: number; }) => x.destination_anchor == 1);
    const targetIndex = sourceNodes.findIndex((x: { destination_anchor: number; }) => x.destination_anchor == 2);

    if (sourceIndex >= 0 && targetIndex >= 0) {
      const sourceId = sourceNodes[sourceIndex].source;
      const targetId = sourceNodes[targetIndex].source;

      const sourceColumn = `columns_${sourceNodes[sourceIndex].source_anchor}`;
      const targetColumn = `columns_${sourceNodes[targetIndex].source_anchor}`;
      if (this.sourceColumnList && (Object.keys(this.sourceColumnList).length > 0)) {
        this.leftTableColumn?.set(this.sourceColumnList?.[sourceId]?.[sourceColumn]);
        this.rightTableColumn?.set(this.sourceColumnList?.[targetId]?.[targetColumn]);
      }

    }

  }

  resetVariables() {
    this.leftTableColumn.set([]);
    this.rightTableColumn?.set([]);
    this.leftColumnsArrays?.clear();
    this.rightColumns?.clear();
    this.mappedColumns?.clear();
  }

  addColumn(element: ColumnPrep) {
    const item = this._fb.group({
      columnName: this._fb.control(element.name, Validators.required),
      checked: this._fb.control(element.checked),
      rename: this._fb.control(element.rename),
      mapedColumn: this._fb.control(element.mapedColumn ?? '')
    });
    return item;
  }

  setInitialColumns() {
    this.selectedTargetColumns = [];
    const nodeData = this.advanceStore.selectNodeValue().nodeValue(this.currentNodeId ?? '');

    this.leftTableColumn()?.forEach(element => {
      element.checked = !nodeData?.configuration?.mapping?.length ? (this.checkSameTitle(element.name) ? true : false) : this.checkSelectedOrNot(nodeData?.configuration?.mapping, element.name, this.isColumn);
      element.mapedColumn = this.getMappedColumns(nodeData?.configuration?.mapping, element.name) ?? this.checkSameTitle(element.name);
      // }
      if (element.mapedColumn) {
        const data = {
          key: element.name,
          value: element.mapedColumn.name
        };
        this.selectedTargetColumns.push(data);
      }
      this.leftColumnsArrays?.push(this.addColumn(element));
      this.mappedColumns?.push(this.addColumn(element));
    });

    this.rightTableColumn()?.forEach(element => {
      element.checked = !nodeData?.configuration?.mapping?.target?.length ? true : this.checkSelectedOrNot(nodeData?.configuration?.mapping?.target, element.name, this.isColumn);
      element.rename = this.getRenameColumns(nodeData?.configuration?.mapping?.target, element.name);
      this.rightColumns?.push(this.addColumn(element));
    });
    this._cdr.detectChanges();
  }

  checkSameTitle(leftSourceName: string) {
    return this.rightTableColumn()?.find(x => x?.name == leftSourceName);
  }

  checkSelectedOrNot(data: SelectColumns[], column: string, type: string) {
    const objKey = appendKeys.source_name;
    return data?.find((x: any) => x[objKey] == column) ? true : false;
  }

  getRenameColumns(data: SelectColumns[], column: string) {
    const found = data?.find(x => x.original_name === column);
    return found ? found.rename_to : '';
  }

  getMappedColumns(data: SelectColumns[], column: string): ColumnPrep | undefined {
    const found = data?.find(x => x?.source === column);
    return found?.target ? this.rightTableColumn()?.find(x => x.name === found?.target) : undefined;
  }

  getColumnConfiguration() {
    const data = {
      type: appendKeys.columnType,
      column: {
        source: this.getColumnValues(this.leftColumnsArrays, appendKeys.columnType),
        target: this.getColumnValues(this.rightColumns, appendKeys.columnType)
      }
    };
    return data;
  }

  getRowConfiguration() {
    const data = {
      type: appendKeys.rowType,
      mapping: this.getColumnValues(this.mappedColumns, appendKeys.rowType)
    };
    return data;
  }

  getColumnValues(fArray: FormArray<FormGroup<ColumnsArray>>, type: string) {
    const data: Record<string, number>[] = [];
    if (fArray?.value?.length) {
      fArray.value.forEach((element: any, index: number) => {
        if (element.checked) {
          const obj: Record<string, any> = {
            [appendKeys.source_name]: element.columnName
          };
          if (element.mapedColumn && type == appendKeys.rowType) {
            obj['target'] = element.mapedColumn.name;
          }
          if (index == 0 && this.rightTableColumn().length == 1) {
            obj['target'] = this.rightTableColumn()[0].name;
          }
          data.push(obj);
        }
      });
    }
    return data;
  }





  ngOnDestroy(): void {
    if (!this._prepService.isConfigSave() && this.sourceColumnList && (Object.keys(this.sourceColumnList).length > 0)) {
      this.onSave();
    }
  }

}

export interface SelectColumns {
  original_name?: string;
  rename_to?: string;
  target?: string;
  source?: string;
}

export interface ColumnsArray {
  columnName: FormControl,
  checked: FormControl,
  rename: FormControl,
  mapedColumn: FormControl
}

