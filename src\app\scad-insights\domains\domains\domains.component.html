<div class="ifp-domain">
  <div class="ifp-domain__wrapper">
    <div class="ifp-domain__nav">
      <app-ifp-tab (selectedTabEvent)="searchString=undefined;handleTabSwitch($event.event)"
        [tabData]="classificationData" [selectedTab]="selectedTabIndex" [isNavBar]="true" [classification]="true"
        [hideCount]="true" [analyticClass]="analyticsClasses.domainNavigationClassification"
        [isNotDisable]="true"></app-ifp-tab>
      <div class="ifp-domain__search">
        <ifp-search class="ifp-domain__search-input" [boxType]="true" [showPlaceHolder]="true"
          (searchEvent)="searchValue($event)" [isKeypress]="true" #searchBox [onSearch]="searchString"></ifp-search>
      </div>
    </div>
    @if (domainLabelData && domainLabelData.length && !loader) {
      <div class="ifp-domain__sidebar-wrapper">
        <div class="ifp-domain__sidebar-container ifp-domain__sidebar-container--domain">
          <div class="ifp-domain__sidebar ifp-domain__sidebar--domain"
            [ngClass]="{'ifp-domain__sidebar--active': domainLabelData.length}">
            <ul class="ifp-domain__sidebar-list">
              <ng-container *ngFor="let domain of domainLabelData; index as i">
                <li class="ifp-domain__sidebar-item" [ngClass]="{'ifp-domain__sidebar-item--active': domain.isSelected}"
                  (pointerover)="isDesktop ? selectSubDomain(domain,i) : null"
                  (click)="!isDesktop ? selectSubDomain(domain,i) : null">
                  <span class="ifp-domain__sidebar-inner">
                    <app-ifp-category-label [analyticClass]="analyticsClasses.domainNavigationItem"
                      (click)="linkClick(domain, types.domain)" (linkClick)="linkClick($event, types.domain)"
                      [params]="true" [labelData]="domain" [classification]="true" [hideCount]="true" [iconWidth]="20"
                      [iconHeight]="20" [hideLink]="true"></app-ifp-category-label></span><em
                    class="ifp-icon ifp-icon-right-arrow"></em>
                </li>
              </ng-container>
            </ul>
          </div>
        </div>

        @if (domainLabelData.length) {
        @if (subDomainLabelData.length) {
        <div class="ifp-domain__sidebar-container"
          [ngClass]="{'ifp-domain__sidebar-container--extend': !selectedTab.showTree || selectedDomain.id === 'census'}">
          <div class="ifp-domain__sidebar"
            [ngClass]="{'ifp-domain__sidebar--sub-domain': subDomainLabelData[0].showTree, 'ifp-domain__sidebar--active': subDomainLabelData.length}">
            @if (selectedDomain.id === 'census') {
              <ifp-census-domain-nav class="ifp-domain__census-nav"></ifp-census-domain-nav>
            } @else {
            <ul class="ifp-domain__sidebar-list">
              <li class="ifp-domain__sidebar-item ifp-domain__sidebar-item--back" (click)="subDomainLabelData = []"><span
                  class="ifp-domain__item-inner"><em class="ifp-icon ifp-icon-leftarrow"></em>{{'Back' |
                  translate}}</span></li>
              <li class="ifp-domain__sidebar-item" *ngFor="let subDomain of subDomainLabelData; let i=index"
                (pointerover)="isDesktop ? filterSubtheme(subDomain, i) : null"
                (click)="!isDesktop ? filterSubtheme(subDomain, i) : null"
                [ngClass]="{'ifp-domain__sidebar-item--active' : subDomain?.content_type === contentType['analytical_apps'] ? false: subDomain.isSelected, 'ifp-domain__sidebar-item--last': !selectedTab.showTree }">
                <span class="ifp-domain__sidebar-inner"><app-ifp-category-label
                    [analyticClass]="analyticsClasses.domainNavigationItem"
                    (click)="linkClick(subDomain, types.subdomain)" (linkClick)="linkClick($event, types.subdomain)"
                    [labelData]="setNodeCount(subDomain,'subthemes')" [classification]="true" [params]="true"
                    [lightOnly]="true" [hideLink]="true"
                    [isTag]="subDomain.screener ? 'Screener' : (subDomain?.category?.name ? subDomain?.category?.name : '')"
                    [tagTooltip]="subDomain.screener ? screenerInfo : ''" [hideCount]="true"></app-ifp-category-label>
                  <span class="ifp-domain__count"
                    *ngIf="subDomain?.nodeCount && selectedTab?.key !== this.classification.innovativeStatistics">{{!searchString
                    ? subDomain?.nodeCount : checkCount(subDomain.subthemes,subThemeLabelData)}}</span>
                </span>
                <em class="ifp-icon ifp-icon-right-arrow" *ngIf="selectedTab.showTree"></em>
              </li>
            </ul>
            }
          </div>
        </div>
        }
        @if (subThemeLabelData.length) {
        <div class="ifp-domain__sidebar-container">
          <div class="ifp-domain__sidebar ifp-domain__sidebar--subtheme"
            [ngClass]="{'ifp-domain__sidebar--active': subThemeLabelData.length}">
            <ul class="ifp-domain__sidebar-list">
              <li class="ifp-domain__sidebar-item ifp-domain__sidebar-item--back" (click)="subThemeLabelData = []"><span
                  class="ifp-domain__item-inner"><em class="ifp-icon ifp-icon-leftarrow"></em> {{'Back' |
                  translate}}</span></li>
              <li class="ifp-domain__sidebar-item" *ngFor="let subDomain of subThemeLabelData"
                (pointerover)="isDesktop ? setSubtheme(subDomain) : null"
                (click)="!isDesktop ? setSubtheme(subDomain) : null"
                [ngClass]="{'ifp-domain__sidebar-item--active': subDomain.isSelected}">
                <span class="ifp-domain__sidebar-inner"><app-ifp-category-label
                    [analyticClass]="analyticsClasses.domainNavigationItem" (click)="linkClick(subDomain, types.subTheme)"
                    [params]="true" (linkClick)="linkClick($event, types.subTheme)"
                    [labelData]="setNodeCount(subDomain,'products')" [classification]="true" [lightOnly]="true"
                    [hideCount]="true" [hideLink]="true" [isTag]="subDomain.screener ? 'Screener' : ''"
                    [tagTooltip]="screenerInfo"></app-ifp-category-label>
                  <span class="ifp-domain__count" *ngIf="subDomain?.nodeCount">{{!searchString ? subDomain?.nodeCount :
                    checkCount(subDomain.products,productData)}}</span>
                </span>
                <em class="ifp-icon ifp-icon-right-arrow" *ngIf="subDomain?.products?.length"
                  [ngStyle]="{'display': subDomain?.products?.length ? 'block' : 'none'}"></em>
              </li>
            </ul>
          </div>
        </div>
        }
        @if (productData?.length > 0) {
        <div class="ifp-domain__sidebar-container ifp-domain__sidebar-container--product">
          <div class="ifp-domain__sidebar ifp-domain__sidebar--product"
            [ngClass]="{'ifp-domain__sidebar--active': productData?.length > 0}">
            <ul class="ifp-domain__sidebar-list">
              <li class="ifp-domain__sidebar-item ifp-domain__sidebar-item--back" (click)="productData = []"><span
                  class="ifp-domain__item-inner"><em class="ifp-icon ifp-icon-leftarrow"
                    style="transform: none;"></em>{{'Back' | translate}}</span></li>
              <li class="ifp-domain__sidebar-item" *ngFor="let product of productData; index as i"
                (click)="setIndicator(product)" [ngClass]="{'ifp-domain__sidebar-item--active': product.isOpen}">
                <div class="ifp-domain__sidebar-item-inner">
                  <span class="ifp-domain__sidebar-inner"><app-ifp-category-label
                      [analyticClass]="analyticsClasses.domainNavigationItem" [params]="true"
                      [labelData]="setNodeCount(product, 'nodes')" [classification]="true" [lightOnly]="true"
                      [hideCount]="true" [hideLink]="true"></app-ifp-category-label>
                    <span class="ifp-domain__count" *ngIf="product?.nodeCount">{{!searchString ? product?.nodeCount :
                      checkNodeCount(product.nodes)}}</span>
                    <em class="ifp-icon ifp-icon-right-arrow"
                      [ngStyle]="{'display': product?.nodes?.length ? 'block' : 'none'}"></em>
                  </span>
                </div>
                <ul class="ifp-domain__accordion-body">
                  <li class="ifp-domain__accordion-item" *ngFor="let indicator of indicatorData"
                    (click)="openDetailPage(indicator)" (linkClick)="openDetailPage(indicator)">
                    <app-ifp-category-label [analyticClass]="analyticsClasses.domainNavigationItem" [params]="true"
                      [labelData]="indicator" [classification]="true" [lightOnly]="true" [hideCount]="true"
                      [hideLink]="true"></app-ifp-category-label>
                  </li>
                </ul>
              </li>
            </ul>
          </div>
        </div>
        }
        } @else {
        <div class="ifp-domain__no-data">
          <app-ifp-no-data [message]="'No Data Available' | translate"></app-ifp-no-data>
        </div>
        }
      </div>
    } @else if (loader) {
      <div class="ifp-domain__loading">
        <div class="ifp-domain__loading-spinner">
          <div class="spinner-border text-primary" role="status">
            <span class="sr-only">Loading...</span>
          </div>
          <p class="ifp-domain__loading-text">{{ 'Loading content...' | translate }}</p>
        </div>
      </div>
    } @else {
      <div class="ifp-domain__no-data">
        <app-ifp-no-data [message]="'No Data Available' | translate"></app-ifp-no-data>
      </div>
    }
  </div>
</div>
