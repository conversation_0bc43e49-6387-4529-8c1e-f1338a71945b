@if (this.tableDataDetail.length !=0 || summeryLoader() || _preService.isNoRecordFound()) {
<div class="ifp-data-preview" [ngClass]="{'ifp-data-preview__full': fullScreen}" #preview>
  <div class="ifp-data-preview__menu">
    @if(!_preService.workflowRunningStatus()) {
    <app-ifp-tab [tabData]="menuTabs" [isSmall]="true" [tooltipDisabled]="true" [selectionType]="'key'" [selectedTab]="selectedTabView.event.key" [showIcon]="false" (selectedTabEvent)="changeTabView($event)" class="ifp-data-preview__switch-view"></app-ifp-tab>
    }
    <!-- <ifp-button class="ifp-data-preview__menu-btn" [buttonClass]="modeSelected === 'preview' ? buttonClass.primary : buttonClass.secondary" [label]="'Preview'" (ifpClick)="modeSelected = 'preview'"></ifp-button>
    <ifp-button class="ifp-data-preview__menu-btn" [buttonClass]="modeSelected === 'log' ? buttonClass.primary : buttonClass.secondary" [label]="'Log'" (ifpClick)="modeSelected = 'log'"></ifp-button> -->
  </div>
  @if (modeSelected === 'preview'&& !_preService.workflowRunningStatus()) {
    <div class="ifp-data-preview__table"
    [ngClass]="{'ifp-data-preview__table--adjust': showProbabilty&&  _preService.tableView !== 'normal'}">

    @if (_preService.previewTableData.data.length !=0) {
    <div class="ifp-data-preview__toolbar">
      <h3 class="ifp-data-preview__title">{{'Preview' | translate}}</h3>
      <div class="ifp-data-preview__tools">
        <span class="ifp-data-preview__duration">{{'Loaded in' | translate}} {{responseTime}} ms</span>
        <ifp-panel-dropdown class="ifp-data-preview__dropdown" [options]="columListDropdown"
          (multiSelected)="multiSelected($event)" [key]="'value'"></ifp-panel-dropdown>
        @if(metaData) {
        <em class="ifp-icon ifp-icon-col-view ifp-data-preview__tool"
          [ngClass]="{'ifp-data-preview__tool--active': _preService.tableView === 'normal'}" (click)="setTableView('normal')"
          [appIfpTooltip]="'View Data' | translate"></em>

        <em class="ifp-icon ifp-icon-detail-view-fill ifp-data-preview__tool"
          [ngClass]="{'ifp-data-preview__tool--active': _preService.tableView === 'detail'}" (click)="setTableView('detail')"
          [appIfpTooltip]="'Column Details' | translate"></em>
        }

        <em [appIfpTooltip]="'Download Preview' | translate"
          class="ifp-icon ifp-icon-download-line ifp-data-preview__tool" (click)="downloadFileClick()"></em>
      </div>
    </div>
    }
    <div class="ifp-data-preview__inner" [ngClass]="{'ifp-data-preview__full-inner': fullScreen}" #table>
      @if (_preService.previewTableData.data.length !=0 && !loader()) {
      <ifp-data-table [translation]="false" [headingTranslate]="_preService.tableView === 'detail'" [rowClickEnable]="_preService.tableView !== 'normal'"
        [tableHead]="_preService.previewTableData.heading" [tableData]="_preService.previewTableData.data"
        (rowEventEmitter)="rowClick($event)" class="ifp-data-preview__table"></ifp-data-table>
      } @else if (_preService.previewTableData.data.length !=0 && loader()) {
      <app-ifp-card-loader class="ifp-loader" [type]="'table'"></app-ifp-card-loader>
      } @else if (_preService.isNoRecordFound()) {
        @if (_preService.source()) {
          <app-ifp-no-data [message]="'Please upload a file'"></app-ifp-no-data>
        } @else {
          <app-ifp-no-data [message]="'No Data Available'"></app-ifp-no-data>
        }

      }
      @else {
      <app-ifp-no-data [message]="'Please run the workflow to view the preview.'"></app-ifp-no-data>
      }


    </div>
    @if( _preService.tableView === 'normal' && _preService.previewTableData.data.length !=0) {
    <div class="ifp-data-preview__pagination-wrapper">
      <app-pagination class="ifp-data-preview__pagination" [offset]="offset" [limit]="limit" [customPagination]="true"
        [size]="size" (pageChange)="onPageChange($event)" (limitChange)="limitChanged($event)"></app-pagination>
        @if (totalRows) {
          <p class="ifp-data-preview__desc">{{'Showing' | translate}} <span class="ifp-data-preview__strong">{{offset+1}}</span> {{'to' | translate}} <span class="ifp-data-preview__strong">{{(page * limit) < previewLimit ? (page * limit) : previewLimit}}</span> {{'of' | translate}}
            {{previewLimit}}{{' entries. The data contains a total of' | translate}} <span class="ifp-data-preview__strong">{{totalRows}}</span> {{'rows and' | translate}} <span class="ifp-data-preview__strong">{{totalKeys}}</span>
            {{'columns.' |translate}}</p>
        }

    </div>
    }
  </div>
  @if (showProbabilty && _preService.tableView !== 'normal') {
  <ifp-probability-card class="ifp-data-preview__prob" [uniqueColor]="uniqueColor" [uniqueName]="uniqueName"
    [uniqueValues]="uniqueValue" [name]="probalilityName" [catogory]="probabilityheading"
    [chartValues]="probabilityvalues"></ifp-probability-card>
  }
  } @else {
    <div class="ifp-data-preview__toolbar">
      <h3 class="ifp-data-preview__title">{{'Log' | translate}}</h3>
    </div>
    <div class="ifp-data-preview__inner" #log >
      @if (_preService.logs().length !=0) {
      <ifp-data-table [translation]="false"
        [tableHead]="logHeading" [tableData]="_preService.logs()"
        class="ifp-data-preview__table ifp-data-preview__table--logs"></ifp-data-table>
      } @else if (_preService.logs().length ==0 && _preService.workflowRunningStatus()) {
      <app-ifp-card-loader class="ifp-loader" [type]="'table'"></app-ifp-card-loader>
      }
      @else {
      <app-ifp-no-data [message]="'Please run the workflow to view the Logs.'"></app-ifp-no-data>
      }


    </div>
  }

</div>
}
