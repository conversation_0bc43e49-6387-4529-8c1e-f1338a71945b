// export const formulas = [
//   {
//     'category': 'Strings',
//     'functions': [
//       {
//         'name': 'Concat',
//         'syntax': 'concat("stringA","stringB")',
//         'parameter_count': 2,
//         'example': 'concat("<PERSON>","<PERSON>")',
//         'description': 'Concatenate two strings'
//       },
//       {
//         'name': 'Contains',
//         'syntax': 'contains("stringA","stringB")',
//         'parameter_count': 2,
//         'example': 'contains("Quick","uick")',
//         'description': 'Checks if stringA contains stringB'
//       },
//       {
//         'name': 'CountWords',
//         'syntax': 'countWords("string")',
//         'parameter_count': 1,
//         'example': 'countWords("Quick Brown Fox")',
//         'description': 'Counts the number of words in a string'
//       },
//       {
//         'name': 'EndsWith',
//         'syntax': 'endsWith("string","suffix", caseInsensitive=0)',
//         'parameter_count': 3,
//         'example': 'endsWith("Quick Brown Fox","Fox", caseInsensitive=0)',
//         'description': 'Checks if string ends with suffix, optionally case insensitive'
//       },
//       {
//         'name': 'FindString',
//         'syntax': 'findString("string","substring")',
//         'parameter_count': 2,
//         'example': 'findString("Quick Brown Fox","Brown")',
//         'description': 'Finds the position of the substring in the string'
//       },
//       {
//         'name': 'GetWord',
//         'syntax': 'getWord("string", index)',
//         'parameter_count': 2,
//         'example': 'getWord("Quick Brown Fox", 1)',
//         'description': 'Gets the word at the specified index in the string'
//       },
//       {
//         'name': 'Length',
//         'syntax': 'length("string")',
//         'parameter_count': 1,
//         'example': 'length("Quick Brown Fox")',
//         'description': 'Returns the length of the string'
//       },
//       {
//         'name': 'Replace',
//         'syntax': 'replace("string","target","replacement")',
//         'parameter_count': 3,
//         'example': 'replace("Quick Brown Fox","Brown","Red")',
//         'description': 'Replaces all occurrences of the target string with the replacement string'
//       },
//       {
//         'name': 'ToLower',
//         'syntax': 'toLower("string")',
//         'parameter_count': 1,
//         'example': 'toLower("Quick Brown Fox")',
//         'description': 'Converts the string to lower case'
//       },
//       {
//         'name': 'ToString',
//         'syntax': 'toString(number)',
//         'parameter_count': 1,
//         'example': 'toString(number)',
//         'description': 'Converts a number to string'
//       },
//       {
//         'name': 'ToUpper',
//         'syntax': 'toUpper("string")',
//         'parameter_count': 1,
//         'example': 'toUpper("Quick Brown Fox")',
//         'description': 'Converts the string to upper case'
//       }
//     ]
//   },
//   {
//     'category': 'Numbers',
//     'functions': [
//       {
//         'name': 'Absolute',
//         'syntax': 'abs(number)',
//         'parameter_count': 1,
//         'example': 'abs(-5)',
//         'description': 'Returns the absolute value of a number'
//       },
//       {
//         'name': 'Avg',
//         'syntax': 'avg(column)',
//         'parameter_count': 1,
//         'example': 'avg(salary)',
//         'description': 'Calculates the average of a column'
//       },
//       {
//         'name': 'Ceiling',
//         'syntax': 'ceil(number)',
//         'parameter_count': 1,
//         'example': 'ceil(4.3)',
//         'description': 'Returns the smallest integer greater than or equal to the given number'
//       },
//       {
//         'name': 'Cosine',
//         'syntax': 'cos(number)',
//         'parameter_count': 1,
//         'example': 'cos(0)',
//         'description': 'Returns the cosine of the given number'
//       },
//       {
//         'name': 'Factorial',
//         'syntax': 'factorial(n)',
//         'parameter_count': 1,
//         'example': 'factorial(5)',
//         'description': 'Returns the factorial of the given number'
//       },
//       {
//         'name': 'Floor',
//         'syntax': 'floor(number)',
//         'parameter_count': 1,
//         'example': 'floor(4.6)',
//         'description': 'Returns the largest integer less than or equal to the given number'
//       },
//       {
//         'name': 'Hyperbolic Cosine',
//         'syntax': 'cosh(number)',
//         'parameter_count': 1,
//         'example': 'cosh(0)',
//         'description': 'Returns the hyperbolic cosine of the given number'
//       },
//       {
//         'name': 'Logarithm Base 10',
//         'syntax': 'log10(number)',
//         'parameter_count': 1,
//         'example': 'log10(100)',
//         'description': 'Returns the base 10 logarithm of a number'
//       },
//       {
//         'name': 'Logarithm',
//         'syntax': 'log(number)',
//         'parameter_count': 1,
//         'example': 'log(100)',
//         'description': 'Returns the natural logarithm of a number'
//       },
//       {
//         'name': 'Max',
//         'syntax': 'max(column)',
//         'parameter_count': 1,
//         'example': 'max(salary)',
//         'description': 'Calculates the maximum value of a column'
//       },
//       {
//         'name': 'Min',
//         'syntax': 'min(column)',
//         'parameter_count': 1,
//         'example': 'min(salary)',
//         'description': 'Calculates the minimum value of a column'
//       },
//       {
//         'name': 'Modulo',
//         'syntax': 'mod(dividend, divisor)',
//         'parameter_count': 2,
//         'example': 'mod(10, 3)',
//         'description': 'Returns the remainder of the division of two numbers'
//       },
//       {
//         'name': 'Pi',
//         'syntax': 'pi()',
//         'parameter_count': 0,
//         'example': 'pi()',
//         'description': 'Returns the value of Pi (π)'
//       },
//       {
//         'name': 'Power',
//         'syntax': 'pow(base, exponent)',
//         'parameter_count': 2,
//         'example': 'pow(2, 3)',
//         'description': 'Returns the value of a base raised to the power of an exponent'
//       },
//       {
//         'name': 'RAND',
//         'syntax': 'rand()',
//         'parameter_count': 0,
//         'example': 'rand()',
//         'description': 'Returns a random floating-point number in the range [0, 1)'
//       },
//       {
//         'name': 'Random Integer',
//         'syntax': 'randint(min_value, max_value)',
//         'parameter_count': 2,
//         'example': 'randint(1, 10)',
//         'description': 'Returns a random integer between the specified minimum and maximum values, inclusive'
//       },
//       {
//         'name': 'Round',
//         'syntax': 'round(number, decimals)',
//         'parameter_count': 2,
//         'example': 'round(3.14159, 2)',
//         'description': 'Returns the given number rounded to the specified number of decimal places'
//       },
//       {
//         'name': 'Sine',
//         'syntax': 'sin(angle)',
//         'parameter_count': 1,
//         'example': 'sin(0)',
//         'description': 'Returns the sine of the given angle (in radians)'
//       },
//       {
//         'name': 'Hyperbolic Sine',
//         'syntax': 'sinh(angle)',
//         'parameter_count': 1,
//         'example': 'sinh(0)',
//         'description': 'Returns the hyperbolic sine of the given angle'
//       },
//       {
//         'name': 'Square Root',
//         'syntax': 'sqrt(number)',
//         'parameter_count': 1,
//         'example': 'sqrt(16)',
//         'description': 'Returns the square root of the given number'
//       },
//       {
//         'name': 'Tangent',
//         'syntax': 'tan(angle)',
//         'parameter_count': 1,
//         'example': 'tan(0)',
//         'description': 'Returns the tangent of the given angle (in radians)'
//       },
//       {
//         'name': 'Hyperbolic Tangent',
//         'syntax': 'tanh(angle)',
//         'parameter_count': 1,
//         'example': 'tanh(0)',
//         'description': 'Returns the hyperbolic tangent of the given angle'
//       },
//       {
//         'name': 'toNumber',
//         'syntax': 'toNumber(string)',
//         'parameter_count': 1,
//         'example': 'toNumber("1234")',
//         'description': 'Converts a string to number'
//       }
//     ]
//   },
//   {
//     'category': 'Conditional',
//     'functions': [
//       {
//         'name': 'If',
//         'syntax': 'if(condition, true_value, false_value)',
//         'parameter_count': 3,
//         'example': 'if(condition, true_value, false_value)',
//         'description': 'Evaluates a condition and returns one value if the condition is true and another value if the condition is false'
//       }
//     ]
//   }
// ];
