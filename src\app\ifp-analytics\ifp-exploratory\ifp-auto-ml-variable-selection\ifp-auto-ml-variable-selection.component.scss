@use "../../../../assets/ifp-styles/abstracts/index" as *;
.ifp-auto-selection {
  background-color: $ifp-color-section-white;
  border-radius: 10px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  &__head-wrapper {
    display: flex;
  justify-content: flex-end;
  padding: $spacer-3 $spacer-5;
    @include desktop-sm {
      padding: $spacer-3 $spacer-4;
  }
  }

  &__content {
    border-top: 1px solid $ifp-color-grey-13;
    padding: $spacer-4 $spacer-5;
    @include desktop-sm {
      padding: $spacer-3 $spacer-4;
  }
  }

  &__badge-wrapper {
    display: flex;
    flex-wrap: wrap;
    margin: $spacer-0 (-$spacer-2) ;
  }
  &__badge {
    white-space: nowrap;
    margin:$spacer-3 $spacer-2 $spacer-0 ;
    padding: $spacer-2 $spacer-3 ;
    border-radius: 120px ;
    font-size: $ifp-fs-4;
    font-weight:$fw-medium;
    cursor: pointer;
    &--selected {
      background-color: $ifp-color-blue-menu !important;
      color: $ifp-color-white-global !important;
      border-color: $ifp-color-blue-menu !important;
    }
    @include desktop-sm {
      margin: $spacer-2 $spacer-1  $spacer-0;
      padding: $spacer-1 $spacer-3;
      font-size: $ifp-fs-2;
    }
  }
  &__badge-outer {
 //   min-height: 230px;
    @include desktop-sm {
      min-height: 20px;
    }
  }
  &__heading {
    font-size: $ifp-fs-11;
    font-weight:$fw-bold;
    margin-bottom: $spacer-4;
    color: $ifp-color-primary-grey;
    @include desktop-sm  {
      font-size: $ifp-fs-6;
    }
  }
  &__indicator-heading{
    font-size: $ifp-fs-4;
    font-weight:$fw-semi-bold;
    color: $ifp-color-primary-grey;
    margin-bottom: $spacer-3;
  }
  &__indicator {
    display: flex;
    border: 1px solid $ifp-color-grey-13;
    border-radius: 10px;
    overflow: hidden;
  }
  &__indication {
    width: 16.66%;
    height: 10px;
    display: block;
  }
  &__icon-badge-wrapper {
    display: flex;
  }
  &__icon-badge-text{
    color: $ifp-color-grey-14;
    margin-inline-end: $spacer-4;
    margin-inline-start: $spacer-2;
  }
  &__icon-badge-value {
    font-weight: $fw-medium;
    color: $ifp-color-primary-grey;
  }
  &__icon-badge-wrapper {
    margin-top: $spacer-5;
  }
  &__icon-badge-sub{
    display: flex;
    align-items: center;
  }
  &__icon-badge {
    font-size: $ifp-fs-4;
    padding:  $spacer-4 $spacer-3;
    border-radius: 10px;
    display: flex;
    width:calc(50% - $spacer-3);
    background-color: $ifp-color-pale-grey;
    align-items: center;
    &:first-child{
      margin-inline-end: $spacer-6;
    }
  }
  &__icon {
    font-size: $ifp-fs-5;
    color: $ifp-color-grey-14;
  }
  &__indicator-widget {
    margin-top: $spacer-2;
    display: flex;
    justify-content: space-between;
  }
  &__indicator-letter {
    width: 16.66%;
    text-align: center;
    color: $ifp-color-grey-14;
  }
  &__btn-analyze {
    margin-inline-end: $spacer-2;
  }
  &__icon-outer {
    padding: $spacer-3 $spacer-5;
  //  margin-top: auto;
  }
  &__no-data{
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
  }
&__limit {
  display: flex;
  align-items: center;

}
&__limit-icon {
  margin-inline-end:  $spacer-2;
}
}
:host-context(.ifp-dark-theme) {
  .ifp-auto-selection {
    &__icon-badge-text{
      color: $ifp-color-white-global;
    }
}
}
