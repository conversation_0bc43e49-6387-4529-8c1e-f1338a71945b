import { IFPMsalService } from 'src/app/scad-insights/core/services/IFP-msal.service';
import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, Router, RouterStateSnapshot } from '@angular/router';
import { authTypes, localStorageKeys } from '../constants/auth.constants';
@Injectable({
  providedIn: 'root'
})
export class AuthGuardService {

  constructor(private _msal: IFPMsalService, private _router: Router) {
  }

  async canActivate(route: ActivatedRouteSnapshot, state: RouterStateSnapshot) {
    if (this._msal.getToken && this._msal.tokenExp && (new Date() < new Date(this._msal.tokenExp))) {
      return true;
    }
    this._msal.authMethod = localStorage.getItem(localStorageKeys.loginType);
    if (this._msal.authMethod == null || !this._msal.authMethod) {
      this._router.navigate(['/login']);
    }
    // if (this._msal.authMethod == authTypes.msal) {
    //   if(this._msal.msalInit()) {
    //     await this._msal.initialize();
    //   }
    //   await this._msal.handleRedirectPromise();
    // }

    this._msal.authCheckStatus = true;
    this._msal.setCurrentUrl(state.url);
    this.routeToAuth();

    return false;
  }

  routeToAuth() {
    this._msal.removeToken();
    if (this._msal.authMethod === authTypes.msal) {
      this._router.navigate(['/auth']);
    } else {
      this._router.navigate(['/login']);
      // this._uaePassService.navigateToUAEPass();
      // const baseUrl = environment.env === 'staging' || environment.env === 'prod' ? window.origin : 'https://bayaan-staging.scad.gov.ae';
      // window.location.href = `https://stg-id.uaepass.ae/idshub/authorize?redirect_uri=${baseUrl}/auth/upass/callback&client_id=scadbayaanw_web_stage&response_type=code&scope=urn:uae:digitalid:profile:general`;
    }
  }

}
