import { Injectable } from '@angular/core';
import { HttpService } from './http/http.service';
import { ToasterService } from './tooster/ToastrService.service';

@Injectable({
  providedIn: 'root'
})
export class ApiService {

  constructor(private _http: HttpService, private __toast: ToasterService) { }

  getParams(params: Record<string, any>): string {
    return params
      ? Object.entries(params)
        .map(([key, value]) =>
          `${encodeURIComponent(key)}=${encodeURIComponent(String(value))}`
        )
        .join('&')
      : '';
  }


  getMethodRequest(url: string, params?: any, isExternalServer: boolean = false) {
    const query = this.getParams(params);
    return this._http.get(`${url}${query === '' ? query : `?${query}`}`, isExternalServer);
  }

  getStatusRequest(url: string, isExternalServer: boolean = false) {
    return this._http.get(`${url}`, isExternalServer);
  }

  getMethodPreview(url: string, params?: any, token: string = '', lang: string = 'en', isExternalServer: boolean = false) {
    const query = this.getParams(params);
    return this._http.previewGet(`${url}${query === '' ? query : `?${query}`}`, token, lang, isExternalServer);
  }


  postMethodRequest(url: string, data?: any, doc?: { file?: File, key?: string }, isExternalServer: boolean = false, pagination?: { limit: number, offset: number }) {
    if (doc?.file) {
      const formData = new FormData();
      formData.append(doc?.key ?? '', doc.file, 'signature.png');
      formData.append('data', JSON.stringify(data));
      return this._http.post(`${url}`, formData);
    }
    if (pagination?.limit && pagination.offset) {
      url = url + '?limit=' + pagination.limit + '&offset=' + pagination.offset;
    }
    return this._http.post(`${url}`, data, isExternalServer);
  }


  putMethodRequest(url: string, data?: any, isExternalServer: boolean = false) {
    return this._http.put(`${url}`, data, isExternalServer);
  }

  patchMethodRequest(url: string, data?: any, isExternalServer: boolean = false) {
    return this._http.patch(`${url}`, data, isExternalServer);
  }

  getDownloadRequest(url: string, params?: any) {
    const query = this.getParams(params);
    return this._http.fileDownloadWithHeader(`${url}${query === '' ? query : `?${query}`}`);
  }

  getDeleteRequest(url: string, params?: any, isExternalServer: boolean = true) {
    const query = this.getParams(params);
    return this._http.delete(`${url}${query === '' ? query : `?${query}`}`, isExternalServer);
  }

  deleteWithoutQuery(url: string) {
    return this._http.delete(url);
  }

  getUploadData(url: string, data: FormData, isExternalServer: boolean = true) {
    return this._http.fileUpload(url, data, isExternalServer);
  }

  errorHandler(error: any) {
    if (typeof error === 'string') {
      if (error !== '') {
        this.__toast.error(error);
      }
    } else if (error?.message) {
      this.__toast.error(error?.message);
    } else if (error?.error) {
      this.__toast.error(error?.error);
    } else {
      for (const key in error) {
        if (Object.prototype.hasOwnProperty.call(error, key)) {
          const element = error[key];
          let data = '';
          if (typeof element === 'string') {
            data = element;
          } else {
            element?.forEach((elementValue: string) => {
              data = `${data} ${elementValue}`;
            });
          }
          if (data !== '') {
            this.__toast.error(data);
          }
        }
      }
    }

  }


  checkColor = (status: any): string => {
    if (!status) { return '#ed5564' }
    status = status.toLowerCase().replace(/\s/g, "");
    const statusColors: { [key: string]: string } = {
      'open': '#87B32E',
      'confidential': '#EAB75C',
      'sensitive': '#E73F53'
    };

    return statusColors[status] || '#ed5564';
  };
}
