import { Component, input } from '@angular/core';
import { IfpTreeProgressComponent, ProgressData } from "../ifp-tree-progress/ifp-tree-progress.component";
import { DecimalPipe, NgClass, TitleCasePipe } from '@angular/common';
import { TranslateModule } from '@ngx-translate/core';

@Component({
  selector: 'ifp-data-tree-diagram',
  imports: [IfpTreeProgressComponent, NgClass, TranslateModule, DecimalPipe, TitleCasePipe],
  templateUrl: './ifp-data-tree-diagram.component.html',
  styleUrl: './ifp-data-tree-diagram.component.scss'
})
export class IfpDataTreeDiagramComponent {
  public totalCount = input<number>(0);
  public title = input<string>('Total Indicators');
  public showPercentage = input<boolean>(false);
  public treeData = input<ProgressData[]>([
    {
      name: 'Statistical Indicators',
      value: 2550,
      color: '#2687FD'
    },
    {
      name: 'Bayaan Indicators',
      value: 15000,
      color: '#3BD6AD'
    },
    {
      name: 'Bayaan Indicators',
      value: 4500,
      color: '#FAC656'
    }
  ]);
}
