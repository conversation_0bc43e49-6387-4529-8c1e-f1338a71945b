
import { EntityState } from '@ngrx/entity';
import { DomainsNav } from 'src/app/scad-insights/core/interface/domains.interface';
export const categoryStateName = 'categoryComponent';
export const domainStateName = 'domainComponent';
export const newsLetterStateName = 'newsLetterComponent';

export interface CategoryState {
  data: Record<string, any>;
  status?: boolean;
  errorMessage?: string;
  loader?: boolean;
}

export interface DomainState {
  data: Record<string, any>;
  status?: boolean;
  errorMessage?: string;
  loader?: boolean;
}

export interface DomainNavigationState {
  id: string;
  data: DomainsNav;
}


export interface DomainDetailFilterState extends EntityState<DomainNavigationState> {
  id: string;
  data: DomainsNav;
}




export const domainReducer = {

};


export interface NewsLetterState {
  data: Record<string, any>;
  status?: boolean;
  errorMessage?: string;
  loader?: boolean;
}
