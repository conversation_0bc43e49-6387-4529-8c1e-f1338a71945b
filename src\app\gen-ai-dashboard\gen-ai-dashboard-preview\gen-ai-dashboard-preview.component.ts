import { buttonClass } from 'src/app/scad-insights/core/constants/button.constants';
import { Component, computed, inject, input, model, OnDestroy, OnInit, output, signal } from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';
import { GenAiDashboardQuestionComponent } from '../gen-ai-dashboard-question/gen-ai-dashboard-question.component';
import { IfpButtonComponent } from '../../scad-insights/ifp-widgets/ifp-atoms/ifp-button/ifp-button.component';
import { SubSink } from 'subsink';
import { genAiTestingApi, questionDropDown } from '../constents/gen-ai-testing.constant';
import { IfpNoDataComponent } from "../../scad-insights/ifp-widgets/ifp-molecules/ifp-no-data/ifp-no-data.component";
import { v6 as uuid } from 'uuid';
import { GenAiFormQuestion } from '../gen-ai-dashboard-qus-create/gen-ai-dashboard-qus-create.component';
import { PaginationComponent } from "../../scad-insights/ifp-widgets/ifp-molecules/pagination/pagination.component";
import { ApiGenAiService } from 'src/app/scad-insights/core/services/api-gen-ai.service';
import { ToastrService } from 'ngx-toastr';
import { StreamingService } from 'src/app/scad-insights/core/services/http/streaming.service';
import { EventSourceMessage } from '@microsoft/fetch-event-source';
import { environment } from 'src/environments/environment';
import { IfpCardLoaderComponent } from "../../scad-insights/ifp-widgets/ifp-atoms/ifp-card-loader/ifp-card-loader.component";
import { loaderType } from 'src/app/scad-insights/core/constants/loader.constants';
import { IfpProgressBarComponent } from "../../ifp-analytics/atom/ifp-progress-bar/ifp-progress-bar.component";
import { PanelDropdownOptions } from "../../ifp-analytics/molecule/ifp-panel-dropdown/ifp-panel-dropdown.component";
import { IfpDropdownComponent } from "../../scad-insights/ifp-widgets/ifp-atoms/ifp-dropdown/ifp-dropdown.component";
import { slaService } from 'src/app/scad-insights/core/services/sla/sla.service';

@Component({
  selector: 'ifp-gen-ai-dashboard-preview',
  imports: [TranslateModule, GenAiDashboardQuestionComponent,
    IfpButtonComponent, IfpNoDataComponent, PaginationComponent, IfpCardLoaderComponent,
    IfpProgressBarComponent, IfpDropdownComponent],
  templateUrl: './gen-ai-dashboard-preview.component.html',
  styleUrl: './gen-ai-dashboard-preview.component.scss'
})
export class GenAiDashboardPreviewComponent implements OnInit, OnDestroy {
  public goBackEvent = output();
  public dataRunEvent = output();
  public heading = model('Preview');
  public numberOfQuestion = model(0);
  public responseOfQuestion = model(0);
  public runId = model('');
  public preview = input(false);
  public modelName = input('');
  public modalDropdown = input<PanelDropdownOptions[]>();

  public versionsValue = {
    key: 'latest',
    value: 'latest',
    checked: false,
  };
  public edit = signal(false);


  public responseOfQuestionEnable = model(true);
  public offset = 0;

  public _slacheck = inject(slaService)
  public _apiService = inject(ApiGenAiService);
  private _toaster = inject(ToastrService);
  private _eventSourceService = inject(StreamingService);
  public subs = new SubSink();
  public buttonClass = buttonClass;

  public questionList = signal<GenAIQuestion[]>([]);
  public runStatus = signal(false);
  public questionResponse = signal<Record<string, QuestionResponse>>({});
  public questionResponseValue = signal<Record<string, GenAiFormQuestion>>({

  });
    public questionResponseChange = signal<Record<string, GenAiFormQuestion>>({

  });
  public versionSwitchAccess = signal(false);
  public updateValue = signal(false);
  public dropdown: Record<string, DropdownOptionGenAi[]> = {};
  public dropdownObject: Record<string, Record<string, DropdownOptionGenAi>> = {};

  public questionResponseArray = computed(() => {
    this.updateValue();
    return Object.values(this.questionResponseValue());
  });

  public pages = { page: 1 };
  public loaderQuestion = signal(false);
  public questionDropDown = questionDropDown;
  public loaderType = loaderType;
  public loadedQuestion = signal(0);
  public validation = signal(false);
  public ctrl: AbortController | null = null;
  public ctrl2: AbortController | null = null;
  ngOnInit(): void {
    // Call all dropdown APIs and wait for all to complete before adding a question
    const dropdownNames = [
      questionDropDown.referenceQComplexity,
      questionDropDown.sourceData,
      questionDropDown.dataClassification,
      questionDropDown.referenceQType,
      questionDropDown.evalAnsAccuracy,
      questionDropDown.evalAnsRelevance,
      questionDropDown.evalAnsStyle
    ];

    Promise.all(
      dropdownNames.map(name => this.fetchDropdownOptions(name))
    ).then(() => {
      this.addQuestion();
    });
    this.checkVersionSwitch();
  }



  fetchDropdownOptions(name: string) {
    return new Promise((resolve, reject) => {
      this.subs.add(
        this._apiService.getMethodRequest(genAiTestingApi.dropdownType + name).subscribe((data: DropdownInterfaceGenAi) => {
          this.dropdown[data.category] = data.options;
          this.dropdownObject[data.category] = data.options.reduce((acc: Record<string, DropdownOptionGenAi>, item: DropdownOptionGenAi) => {
            acc[item.id] = { id: item.id, value: item.value, display_name: item.display_name };
            return acc;
          }, {});
          resolve(true);
        })
      );
    });
  }

  submitAllEdit() {
    this.runStatus.set(true);
    this.edit.set(false);
    for (const key in  this.questionResponseChange()) {
      if (Object.prototype.hasOwnProperty.call( this.questionResponseChange(), key)) {
        const element =  this.questionResponseChange()[key];
        this.questionList.update(data => {
              data[element.id ?? -1]['loader']=true;
              delete data[element.id ?? -1]['ai_answer'];
                data[element.id ?? -1]['question']=data[element.id ?? -1]['question'];
                      data[element.id ?? -1]['expected_answer']=data[element.id ?? -1]['expected_answer'];
          return data;
        });

      }
    }

    this.callRunQuestionPatchStream();
  }

  CancelEdit(){
    this.edit.set(false);
  }

  dropdownVersion(dropdown: PanelDropdownOptions) {
    this.versionsValue = dropdown;
  }


  callPreviewData() {
    this.subs.add(this._apiService.getMethodRequest(genAiTestingApi.preview + this.runId(), this.pages).subscribe((data: { items: previewResponse[], count: number }) => {
      this.numberOfQuestion.set(data.count);
      this.questionList.set(data.items);
    }));
  }

  deleteQuestion(event: string) {
    this.updateValue.set(true);
    this.questionResponseValue.update(value => {
      delete value[event];
      return value;
    });
    this.updateValue.set(false);
  }
  saveAndAddQuestion(event: GenAiFormQuestion) {
    this.saveQuestion(event);
    this.addQuestion();
  }

  pageChange(event: { page: number }) {
    this.pages.page = event.page;
    if (this.preview()) {
      this.callPreviewData();
    } else {
      this.callQuestion();
    }
  }

  goBack() {
    this.goBackEvent.emit();
  }

  addQuestion() {
    this.updateValue.set(true);
    const uuidValue = uuid();
    this.questionResponseValue.update(value => {
      value[uuidValue] = {
        object_id: uuidValue,
        question: '',
        type: this.dropdown[questionDropDown.referenceQType]?.[0]?.id ?? '',
        complexity: this.dropdown[questionDropDown.referenceQComplexity]?.[0]?.id ?? '',
        source: this.dropdown[questionDropDown.sourceData]?.[0]?.id ?? '',
        data_classification: this.dropdown[questionDropDown.dataClassification]?.[0]?.id ?? '',
        expected_answer: '',
        save: false
      };
      return value;
    });
    this.updateValue.set(false);
  }
  saveQuestion(event: GenAiFormQuestion) {
    this.updateValue.set(true);
    this.questionResponseValue.update(value => {
      value[event.object_id] = event;
      return value;
    });
    this.updateValue.set(false);
  }

  saveChange(event: GenAiFormQuestion) {
    this.questionResponseValue.update(value => {
      value[event.object_id] = event;
      return value;
    });
  }


  questionChangeDetect(event:{event: GenAiFormQuestion, id: number}) {
    this.questionResponseChange.update(value => {
      value[event.event.object_id] ={ ...event.event, id:event.id};
      return value;
    });
  }



  ngOnChanges(): void {
    if(this.responseOfQuestionEnable()) {
  this.numberOfQuestion.set(0);
      this.questionList.set([]);
      this.pages.page = 1;
    } else { 
    if (this.runId() && this.runId() !== '' && !this.preview()) {
      this.callQuestion();
    } else if (this.runId() && this.runId() !== '' && this.preview()) {
      this.callPreviewData();
    } else if (this.runId() === '') {
      this.numberOfQuestion.set(0);
      this.questionList.set([]);
    }
    }

    this.versionsValue = this.modalDropdown()?.[0] ?? {
      key: 'latest',
      value: 'latest',
      checked: false,
    };;
  }


  checkVersionSwitch() {
    this.subs.add(this._slacheck.checkEvaluvateStatus().subscribe((data: { access: true }) => {
      this.versionSwitchAccess.set(data.access);
    }));
  }

  run() {
    this.questionResponse.set({});
    this.questionList.set([]);
    if (this.responseOfQuestionEnable()) {
      this.updateValue.set(true);
      this.updateValue.set(false);
      let validation = false;
      this.questionResponseArray().forEach(data => {
        if (data.question?.trim() === '') {
          validation = true;
        }
      });
      if (validation) {
        this._toaster.error('Mandatory fields missing');
        this.validation.set(true);
        return;
      }

      this.loaderQuestion.set(true);
      this.numberOfQuestion.set(this.questionResponseArray().length);
      this.callRunAddQuestionStream();

      // this.callRunAddQuestion();
    } else {
      // this.callRunQuestion();

      this.loaderQuestion.set(true);
      this.callRunQuestionStream();
    }
    this.dataRunEvent.emit();
  }

  addRating(rating: QuestionResponse) {
    this.questionResponse.update(data => {
      data[rating.question_object_id] = rating;
      return data;
    });
    this.responseOfQuestion.set(Object.values(this.questionResponse()).length)
  }

  ratingUpdate(event: {
    question_object_id: string;
    rating: number;
  }) {
    if (this.questionResponse()[event.question_object_id]) {
      this.questionResponse.update(value => {
        if (value[event.question_object_id]) {
          value[event.question_object_id].rating = event.rating;
        }
        return value;
      });
    }
  }
  submitAllReview() {

    this.subs.add(this._apiService.postMethodRequest(genAiTestingApi.evaluationRun + this.runId() + genAiTestingApi.rating, Object.values(this.questionResponse())).subscribe({
      next: (data: GenAIQuestions) => {
        this.goBackEvent.emit();
      },
      error: () => {
        this._toaster.error('Something went wrong! Please try again.');
      }
    }))
  }


  // callRunQuestion() {
  //   this.subs.add(this._apiService.postMethodRequest(genAiTestingApi.evaluationRun+ this.runId(),{model:this.modelName()} ).subscribe({next:(data:GenAIQuestions) => {
  //     this.runStatus.set(true);
  //     this.numberOfQuestion.set(data.count);
  //     this.questionList.set(data.items);
  //     this.heading.set('Questionnaire');
  //   } ,
  //   error: () => {
  //     this._toaster.error('Something went wrong! Please try again.');
  //   }}))
  // }

  callRunQuestionStream() {
    this.loadedQuestion.set(0);
    this.subs.add(this._eventSourceService.connectToServerSentEventsPost(`${environment.genAiBaseUrl}/v2/` + genAiTestingApi.evaluation + 'run-v2/' + this.runId() + '?version=' + this.versionsValue.key, { model: this.versionsValue.key }).subscribe({
      next: ({ msg, ctrl }: { msg: EventSourceMessage, ctrl: AbortController }) => {
        const data: GenAIQuestion = JSON.parse(msg.data);
        this.runStatus.set(true);
        if (data.status === 'starting') {
          return;
        }
        if (this.questionList() && this.questionList().length && this.questionList()[this.questionList().length - 1].status === 'inprogress') {
          this.questionList.update(value => {
            value[this.questionList().length - 1] = data;
            return value;
          });
          if (this.questionList().length == this.numberOfQuestion()) {
            this.loaderQuestion.set(false);
            ctrl.abort();

          }

        } else {
          this.questionList.update(value => {
            value.push(data);
            return value;
          });

        }
        if (data.status === 'completed') {
          this.loadedQuestion.set(this.loadedQuestion() + 1);
        }
        if (this.questionList().length == this.numberOfQuestion() && data.status === 'completed') {
          this.loaderQuestion.set(false);
          ctrl.abort();

        }
        this.heading.set('Questionnaire');
      },
      error: () => {
        this._toaster.error('Something went wrong! Please try again.');
      }
    }))
  }
  callRunAddQuestionStream() {
    this.runStatus.set(true);
    this.heading.set('Questionnaire');
    this.loadedQuestion.set(0);
    this.subs.add(this._eventSourceService.connectToServerSentEventsPost(`${environment.genAiBaseUrl}/v2/` + genAiTestingApi.evaluation + 'run-v2?version=' + this.versionsValue.key, {
      model: this.versionsValue.key, questions: this.questionResponseArray()
    }).subscribe({
      next: ({ msg, ctrl }: { msg: EventSourceMessage, ctrl: AbortController }) => {
        const data: GenAIQuestion = JSON.parse(msg.data);

        this.responseOfQuestionEnable.set(false);

        // this.runId.set(data.run_id ?? '');
        this.runId.set(data.run_id ?? '');
        if (data.status === 'starting') {
          return;
        }
        if (this.questionList() && this.questionList().length && this.questionList()[this.questionList().length - 1].status === 'inprogress') {
          this.questionList.update(value => {
            value[this.questionList().length - 1] = data;
            return value;
          });
          if (this.questionList().length == this.numberOfQuestion()) {
            this.loaderQuestion.set(false);
            ctrl.abort();

          }

        } else {
          this.questionList.update(value => {
            value.push(data);
            return value;
          });


        }
        if (data.status === 'completed') {
          this.loadedQuestion.set(this.loadedQuestion() + 1);
        }
        if (this.questionList().length == this.numberOfQuestion() && data.status === 'completed') {
          this.loaderQuestion.set(false);
          this.loadedQuestion.set(this.loadedQuestion() + 1);
          ctrl.abort();

        }

      },
      complete: () => {
        this.loaderQuestion.set(false);
      },
      error: () => {
        this._toaster.error('Something went wrong! Please try again.');
      }
    }
    ))
  }

  callRunQuestionPatchStream() {
    this.loadedQuestion.set(0);
    this.subs.add(this._eventSourceService.connectToServerSentEventsPatch(`${environment.genAiBaseUrl}/v2/` + genAiTestingApi.evaluation + 'run-v2/' + this.runId() + '?version=' + this.versionsValue.key, { model: this.versionsValue.key , questions: Object.values(this.questionResponseChange()) }).subscribe({
      next: ({ msg, ctrl }: { msg: EventSourceMessage, ctrl: AbortController }) => {
        const data: GenAIQuestion = JSON.parse(msg.data);
        this.runStatus.set(true);
        if (data.status === 'starting') {
          return;
        }
          if (data.status === 'completed') {
            const value = this.questionResponseChange()[data.object_id]
             this.questionList.update(dataValue => {
              dataValue[value.id ?? -1]=data;
          return dataValue;
        });
        }


       console.log(data);
      },
      error: () => {
        this._toaster.error('Something went wrong! Please try again.');
      }
    }))
  }


  // callRunAddQuestion() {
  //   this.subs.add(this._apiService.postMethodRequest(genAiTestingApi.evaluation+genAiTestingApi.run+'?version='+this.versionsValue.value, {
  //      model: this.modelName(),questions: this.questionResponseArray().slice(0, -1)
  //   }).subscribe({next:(data:GenAIQuestions) => {
  //     this.runStatus.set(true);
  //     this.numberOfQuestion.set(data.count);
  //     this.questionList.set(data.items);
  //     this.heading.set('Questionnaire');
  //     this.responseOfQuestionEnable.set(false);
  //     this.runId.set(data.run_id ?? '');
  //   },   complete: () => {
  //     this.loaderQuestion.set(false);
  //   },
  //   error: () => {
  //     this._toaster.error('Something went wrong! Please try again.');
  //   }}
  // ))
  // }


  callQuestion() {
    this.subs.add(this._apiService.getMethodRequest(genAiTestingApi.evaluationRun + this.runId() + genAiTestingApi.question, this.pages).subscribe((data: GenAIQuestions) => {
      this.numberOfQuestion.set(data.count);
      this.questionList.set(data.items);
    }))
  }
  ngOnDestroy(): void {
    this.subs.unsubscribe();
    if (this.ctrl) {
      this.ctrl.abort();
    }
    if (this.ctrl2) {
      this.ctrl2.abort();
    }
  }

}
interface GenAIQuestions {
  items: GenAIQuestion[];
  count: number;
  run_id?: string;
}

export interface GenAIQuestion {
  object_id: string;
  question: string;
  type: string | number;
  complexity: string | number;
  source: string | number;
  data_classification: string | number;
  expected_answer?: string;
  ai_answer?: Aianswer;
  save?: boolean;
  rating?: Rating;
  status?: string;
  run_id?: string;
  loader?: boolean;
}

export interface QuestionResponse {
  question_object_id: string;
  is_satisfied: boolean;
  rating: number;
  additional_feedback: string;
}

export interface Aianswer {
  object_id: string;
  answer: string;
  source: string | number;
  data_classification: string | number;
  response_time?: number;
}

export interface DropdownInterfaceGenAi {
  category: string;
  options: DropdownOptionGenAi[];
}

export interface DropdownOptionGenAi {
  id: number;
  value: string;
  display_name: string;
}
interface previewResponse {
  object_id: string;
  question: string;
  type: string;
  complexity: string;
  source: string;
  data_classification: string;
  expected_answer: string;
  ai_answer: Aianswer;
  rating: Rating;
}



interface Rating {
  metrics?: Metrics;
  is_satisfied?: boolean;
  rating?: number;
  additional_feedback?: string;
}

interface Metrics {
  evaluation_answer_accuracy?: Evaluationansweraccuracy;
  evaluation_answer_relevance?: Evaluationansweraccuracy;
  evaluation_answer_response_style?: Evaluationansweraccuracy;
}

interface Evaluationansweraccuracy {
  id?: number;
  value?: string;
  display_name?: string;
}
