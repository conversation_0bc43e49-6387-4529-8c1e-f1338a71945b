export interface ProductDetail {
  id: string;
  name: string;
  organization: Assets;
  image: string;
  assets: Assets[];
}

export interface Assets {
  id: string;
  name: string;
  createdAt: string;
}

export interface DxpPlotData {
  chartType: string;
  title: string;
  subtitle: string;
  xAxis: string;
  yAxis: string;
  series: {
    xAxis: {
      categories: string[];
    };
    series: {
      name: string;
      data: number[];
    }[];
  };
  tooltip: {
    enabled: boolean;
    shared: boolean;
  };
  plotOptions: Record<string, any>; // need to change - currently exact type is unknow.
}

export interface ConvertedChartData {
  category: string[];
  series: {
    name: string;
    data: number[];
  }[]
  xAxisLabel?:string;
  yAxisLabel?:string;
}

export interface DxpUser {
  id: string;
  name: string;
  email: string;
}

export interface DxpUserListingResponse {
  total: number;
  totalPages: number;
  data: DxpUser[];
}

export interface ListingPageData {
  totalCount: number;
  page: number;
  limit: number;
  data: ListingPageItem[];
}

export interface ListingPageItem {
  objectId: string;
  component_title: string;
  component_subtitle: string;
  sourceAssetId: string;
  sourceProductId: string;
  visualizationConfig: VisualizationConfig;
  updated: string;
  createdAt: string;
  createdById: string;
  createdBy: CreatedBy;
  approvalRequest: ApprovalRequest;
}

export interface VisualizationConfig {
  source_filter: SourceFilter;
  chart_configuration: ChartConfiguration;
}

export interface SourceFilter {
  groups: SourceFilterGroup[];
  global_operator: string;
}

export interface SourceFilterGroup {
  conditions: SourceFilterCondition[];
  operator: string;
}

export interface SourceFilterCondition {
  column: string;
  comparator: string;
  data_type: string;
  value: any;
}

export interface ChartConfiguration {
  x_axis: ChartAxis;
  y_axis: ChartAxis;
  filterPanel: ChartFilterPanel[];
}

export interface ChartAxis {
  label: string;
  axis: ChartAxisDetail;
}

export interface ChartAxisDetail {
  column: string;
  data_type: string;
  aggregator: string;
}

export interface ChartFilterPanel {
  column: string;
  comparator: string;
  value: any;
  data_type: string;
}

export interface CreatedBy {
  name: string;
  designation: string;
  email: string;
}

export interface ApprovalRequest {
  id: string;
  status: string;
}
