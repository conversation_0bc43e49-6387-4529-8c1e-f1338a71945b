export interface ProductDetail {
  id: string;
  name: string;
  organization: Assets;
  image: string;
  assets: Assets[];
}

export interface Assets {
  id: string;
  name: string;
  createdAt: string;
}

export interface DxpPlotData {
  chartType: string;
  title: string;
  subtitle: string;
  xAxis: string;
  yAxis: string;
  series: {
    xAxis: {
      categories: string[];
    };
    series: {
      name: string;
      data: number[];
    }[];
  };
  tooltip: {
    enabled: boolean;
    shared: boolean;
  };
  plotOptions: Record<string, any>; // need to change - currently exact type is unknow.
}

export interface ConvertedChartData {
  category: string[];
  series: {
    name: string;
    data: number[];
  }[]
  xAxisLabel?:string;
  yAxisLabel?:string;
}

export interface DxpUser {
  id: string;
  name: string;
  email: string;
}

export interface DxpUserListingResponse {
  total: number;
  totalPages: number;
  data: DxpUser[];
}

export interface ListingPageData {
  totalCount: number;
  page: number;
  limit: number;
  data: ListingPageItem[];
}

export interface ListingPageItem {
  objectId: string;
  component_title: string;
  component_subtitle: string;
  sourceAssetId: string;
  sourceProductId: string;
  visualizationConfig: VisualizationConfig;
  updated: string;
  createdAt: string;
  createdById: string;
  createdBy: CreatedBy;
  approvalRequest: ApprovalRequest;
}

export interface VisualizationConfig {
  source_filter: SourceFilter;
  chart_configuration: ChartConfiguration;
}

export interface SourceFilter {
  groups: SourceFilterGroup[];
  global_operator: string;
}

export interface SourceFilterGroup {
  conditions: SourceFilterCondition[];
  operator: string;
}

export interface SourceFilterCondition {
  column: string;
  comparator: string;
  data_type: string;
  value: any;
}

export interface ChartConfiguration {
  x_axis: ChartAxis;
  y_axis: ChartAxis;
  filterPanel: ChartFilterPanel[];
}

export interface ChartAxis {
  label: string;
  axis: ChartAxisDetail;
}

export interface ChartAxisDetail {
  column: string;
  data_type: string;
  aggregator: string;
}

export interface ChartFilterPanel {
  column: string;
  comparator: string;
  value: any;
  data_type: string;
}

export interface CreatedBy {
  name: string;
  designation: string;
  email: string;
}

export interface DxpDetail {
  id: number;
  objectId: string;
  title: string;
  subTitle: string;
  sourceAssetId: string;
  sourceProductId: string;
  visualizationConfig: VisualizationConfigDxpDetail;
  updatedAt: string;
  createdAt: string;
  createdBy: CreatedByDxpDetail;
  approvalRequest: ApprovalRequest;
  userAccess: CreatedByDxpDetail[];
  is_draft: boolean;
  user: User;
  product: Product;
  asset: Product;
  filterPanel: DxpFilterPanel2[];
  series: Series2;
  legendPanel: LegendPanelDxpDetail[];
}

export interface LegendPanelDxpDetail {
  column: string;
  data_type: string;
  selected_values: string[];
  default: boolean;
  options: string[];
}
interface Series2 {
  xAxis: XAxis;
  series: Series[];
}

interface Series {
  name: string;
  data: number[];
}

interface XAxis {
  categories: string[];
}

export interface DxpFilterPanel2 {
  column: string;
  data_type: string;
  label: string;
  default: string;
  options: string[];
  selected?: string[];
}

interface Product {
  id: string;
  displayName: string;
}

interface User {
  id: string;
  name: string;
  generalizedRole: string;
}

interface ApprovalRequest {
  id: string;
  status: string;
  assigneeId: string;
}

interface CreatedByDxpDetail {
  name: string;
  id: string;
}

interface VisualizationConfigDxpDetail {
  source_filter: Sourcefilter;
  chart_configuration: Chartconfiguration;
}

interface Chartconfiguration {
  default_chart_type: string;
  x_axis: Xaxis;
  y_axis: Xaxis;
  filterPanel: FilterPanel[];
  legendPanel: LegendPanel[];
}

interface LegendPanel {
  column: string;
  data_type: string;
  selected_values: string[];
  default: boolean;
}

interface FilterPanel {
  column: string;
  data_type: string;
  label: string;
  default: string;
}

interface Xaxis {
  label: string;
  axis: Axis;
}

interface Axis {
  column: string;
  data_type: string;
  aggregator: string;
}

interface Sourcefilter {
  groups: GroupDxp[];
  global_operator: string;
}

export interface GroupDxp {
  conditions: ConditionDxp[];
  operator: string;
}

export interface ConditionDxp {
  column: string;
  data_type: string;
  comparator: string;
  value: string | string[];
}


export interface DxpFilterConfig {
  chartType: string;
  title: string;
  subtitle: string;
  xAxis: string;
  yAxis: string;
  series: Series2;
  tooltip: Tooltip;
  plotOptions: PlotOptions;
}

interface PlotOptions {
}

interface Tooltip {
  enabled: boolean;
  shared: boolean;
}

interface Series2 {
  xAxis: XAxis;
  series: Series[];
}

interface Series {
  name: string;
  data: number[];
}

interface XAxis {
  categories: string[];
}

export interface DxpComments {
  id: string;
  requestId: string;
  content: string;
  commentType: string;
  createdAt: string;
  updatedAt: string;
  userDetails?: UserDetails;
}

interface UserDetails {
  name: string;
  email: string;
  role: string;
}