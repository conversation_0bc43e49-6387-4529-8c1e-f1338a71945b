<div class="ifp-gen-ai-dashboard-qus-create" [formGroup]="form">
   <p class="ifp-gen-ai-dashboard-qus-create__text">{{'QUESTION' | translate}}
    @if (!addQuestionEnable()) {
      <em class="ifp-icon ifp-icon-save ifp-gen-ai-dashboard-qus-create__save" [appIfpTooltip]="'save' | translate" (click)="saveEvent()"></em>
    } @else if(enableDelete()) {
      <em class="ifp-icon ifp-icon-trash  ifp-gen-ai-dashboard-qus-create__delete" [appIfpTooltip]="'delete' | translate" (click)="deleteEvent()"></em>
    }
</p>
     <textarea formControlName="question" class="ifp-gen-ai-dashboard-qus-create__textarea" rows="1" appIfpInputAutoResize></textarea>
     @if (form.controls.question.invalid && (submit || validation())) {
      <p class="ifp-input-error ifp-gen-ai-dashboard-qus-create__error"><em class="ifp-icon ifp-icon-exclamation-round"></em>{{'Please enter a valid Question' | translate}}</p>
     }


   <p class="ifp-gen-ai-dashboard-qus-create__text">{{'ANSWER' | translate}}</p>
   <textarea formControlName="expected_answer" class="ifp-gen-ai-dashboard-qus-create__textarea ifp-gen-ai-dashboard-qus-create__textarea--answer" appIfpInputAutoResize></textarea>
   @if (form.controls.expected_answer.invalid && (submit || validation())) {
    <p class="ifp-input-error ifp-gen-ai-dashboard-qus-create__error"><em class="ifp-icon ifp-icon-exclamation-round"></em>{{'Please enter a valid Answer' | translate}}</p>
   }

   <!-- <div class="ifp-gen-ai-dashboard-qus-create__dropdown-wrapper">
    <div class="ifp-gen-ai-dashboard-qus-create__dropdown">
      <span  class="ifp-gen-ai-dashboard-qus-create__dropdown-text">
        {{'Type' | translate}}:
      </span>
      <app-ifp-dropdown [key]="'display_name'"   formControlName="type" class="ifp-gen-ai-dashboard-qus-create__drop" [dropDownItems]="dropdown()[dropdownConstant.referenceQType]"></app-ifp-dropdown>
    </div>
    <div class="ifp-gen-ai-dashboard-qus-create__dropdown">
      <span  class="ifp-gen-ai-dashboard-qus-create__dropdown-text">
        {{'Complexity' | translate}}:
      </span>
      <app-ifp-dropdown [key]="'display_name'"   formControlName="complexity"  class="ifp-gen-ai-dashboard-qus-create__drop" [dropDownItems]="dropdown()[dropdownConstant.referenceQComplexity]"></app-ifp-dropdown>
    </div>
    <div class="ifp-gen-ai-dashboard-qus-create__dropdown">
      <span  class="ifp-gen-ai-dashboard-qus-create__dropdown-text">
        {{'Source' | translate}}:
      </span>
      <app-ifp-dropdown [key]="'display_name'"   formControlName="source" class="ifp-gen-ai-dashboard-qus-create__drop" [dropDownItems]="dropdown()[dropdownConstant.sourceData]"></app-ifp-dropdown>

    </div>
    <div class="ifp-gen-ai-dashboard-qus-create__dropdown">
      <span  class="ifp-gen-ai-dashboard-qus-create__dropdown-text">
        {{'Data classification' | translate}}:
      </span>
      <app-ifp-dropdown [key]="'display_name'"   formControlName="data_classification" class="ifp-gen-ai-dashboard-qus-create__drop" [dropDownItems]="dropdown()[dropdownConstant.dataClassification]"></app-ifp-dropdown>
    </div>


   </div> -->
   @if (addQuestionEnable()) {
    <div class="ifp-gen-ai-dashboard-qus-create__plus" (click)="addQuestion()">
      <em class="ifp-icon-plus-light ifp-icon ifp-gen-ai-dashboard-qus-create__plus-icon"></em>
     </div>
   }

</div>
