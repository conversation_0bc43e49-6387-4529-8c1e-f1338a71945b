
<div class="ifp-auto-selection">
    <div class="ifp-auto-selection__head-wrapper">
      <!-- <div>
        <ifp-button [label]="'Iteration Analysis'" [iconClass]="'ifp-icon-detial-view'" [buttonClass]="buttonClass.secondary" class="ifp-auto-selection__btn-analyze" (ifpClick)="moveBack()"></ifp-button>

        <ifp-button [label]="'Run Model'" [iconClass]="'ifp-icon-play-round'" [buttonClass]="selectedValueIndex.length !==0 &&  iterationList.length <5?buttonClass.primary :buttonClass.disabled" class="ifp-auto-selection__btn-run" (ifpClick)="runModelEvent()"></ifp-button>

      </div> -->
       </div>
    <div class="ifp-auto-selection__content">
      <h1 class="ifp-auto-selection__heading"> {{'Select the variables you want to include in the analysis' | translate }}</h1>
      <div class="ifp-auto-selection__limit" >
        <em class="ifp-icon ifp-icon-exclamation-round ifp-auto-selection__limit-icon"></em>
        {{"You can select a maximum of 10 variables at a time, some variables may not be available if they contain null values." | translate}}
      </div>
      <div class="ifp-auto-selection__badge-outer" >
        <div class="ifp-auto-selection__badge-wrapper" >
          @for (item of badgeData; track item;let index = $index) {

            <div class="ifp-auto-selection__badge" [style.background]="item.value | ifpMlColor"
            [style.border]="item.value | ifpMlColor: false: 'border'"
            [style.color]="item.value | ifpMlColor: false: 'color'"
              [ngClass]="{'ifp-auto-selection__badge--selected': selectedValueIndex.includes(index)}" (click)="selections(item, index)">
              {{item.name}}
            </div>

          } @empty {
               <div  class="ifp-auto-selection__no-data">
              <app-ifp-no-data [message]="'No variables available!'" [isTransparent]="true"></app-ifp-no-data>
            </div>
          }


        </div>
      </div>


    <div>

    </div>


  </div>
  <div  class="ifp-auto-selection__icon-outer">
    <div class="ifp-auto-selection__indicator-wrapper">
      <h5 class="ifp-auto-selection__indicator-heading">
        {{'Correlation Strength' | translate}}
      </h5>
      <div class="ifp-auto-selection__indicator">
        @for (item of indicatorValues; track $index) {
          <span class="ifp-auto-selection__indication"  [style.background]="item | ifpMlColor" >

          </span>
        }

      </div>
      <div class="ifp-auto-selection__indicator-widget">
        <div class="ifp-auto-selection__indicator-letter">
          1
        </div>
        <div class="ifp-auto-selection__indicator-letter">
          -1
        </div>

      </div>

    </div>
    <div class="ifp-auto-selection__icon-badge-wrapper" >
      <div class="ifp-auto-selection__icon-badge">
        <div class="ifp-auto-selection__icon-badge-sub">
          <em class="ifp-icon ifp-icon-target ifp-auto-selection__icon">

          </em>
          <p class="ifp-auto-selection__icon-badge-text">
              {{'Target variable'| translate}}
          </p>
        </div>

            <p  class="ifp-auto-selection__icon-badge-value">
              {{targetValue}}
        </p>
      </div>
      <div class="ifp-auto-selection__icon-badge">
        <div  class="ifp-auto-selection__icon-badge-sub">
          <em class="ifp-icon ifp-icon-timer ifp-auto-selection__icon">

          </em>
          <p class="ifp-auto-selection__icon-badge-text">
              {{'Time stamp variable'| translate}}
          </p>
        </div>

            <p  class="ifp-auto-selection__icon-badge-value">
              {{timeStamp}}
        </p>
      </div>
      <div>
    </div>

  </div>
  </div>
</div>

