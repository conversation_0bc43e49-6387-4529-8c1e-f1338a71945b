import { Ng<PERSON>lass, SlicePipe} from '@angular/common';
import { Component, input, output } from '@angular/core';
import { IfpTooltipDirective } from 'src/app/scad-insights/core/directives/ifp-tooltip.directive';
@Component({
  selector: 'ifp-ifp-chip-group',
  imports: [IfpTooltipDirective, NgClass, SlicePipe],
  templateUrl: './ifp-chip-group.component.html',
  styleUrl: './ifp-chip-group.component.scss'
})
export class IfpChipGroupComponent {
  allData = input<Record<string, string>>();
  group = input([]);
  arrow = input(false);
  limit = input(2);
  arrowClicked =  output<Record<string, string> | undefined>();
}
