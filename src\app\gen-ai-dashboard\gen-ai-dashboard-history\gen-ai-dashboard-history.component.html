
<div class="ifp-gen-ai-history">
   <div  class="ifp-gen-ai-history__heading">
    <span class="ifp-gen-ai-history__heading-text">
      {{'Recent' | translate}}
    </span>

      <em class="ifp-icon ifp-icon-cross ifp-gen-ai-history__close" (click)="back.emit()"></em>
  </div>
  <div class="ifp-gen-ai-history__body"  infiniteScroll
  [infiniteScrollDistance]="0.1"
      [scrollWindow]="false"
      [infiniteScrollContainer]="this._host.nativeElement"
  (scrolled)="onPageChange()">
    @for (item of historyData(); track item; let index = $index;) {
      <div  class="ifp-gen-ai-history__bar"  (click)="optionSelected(item.object_id)">
        <div class="ifp-gen-ai-history__values">
          <p   class="ifp-gen-ai-history__name">{{item.evaluated_by}}</p>
          <p   class="ifp-gen-ai-history__date"> {{ item.created_at | date: 'dd MMM yyyy' }}</p>
        </div>
        <div class="ifp-gen-ai-history__status">
          <em class="ifp-icon ifp-icon-right-arrow ifp-gen-ai-history__arrow"></em>
        </div>
      </div>
      } @empty {
        @if(loader()) {
          <div class="ifp-center-loader">
            <app-ifp-spinner></app-ifp-spinner>
          </div>

        } @else {
          <app-ifp-no-data [isTransparent]="true"></app-ifp-no-data>
        }
      }
  </div>

</div>

