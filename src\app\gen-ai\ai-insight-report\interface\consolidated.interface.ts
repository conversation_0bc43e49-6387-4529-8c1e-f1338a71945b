import {
  InsightComparison,
  InsightSentimentanalysis,
  KeyinsightInsights,
  workflow,
} from './insights.interface';

export interface ConsolidatedInsights {
  keyDriversData: KeyDriversDataConsolidated;
}

export interface KeyDriversDataConsolidated {
  title?: Title;
  quarter?: string;
  key_drivers?: Keydrivers;
  domains?: DomainConsolidated[];
  report_id?: number;
  report_type?: string;
  published_date?: string;
  status?: string;
  workFlow?: workflow;
}

export interface DomainConsolidated {
  domain: DomainInsights;
  impact: ImpactInsights;
  key_insights: KeyinsightInsights[];
  comparison: InsightComparison;
  sentiment: InsightSentimentanalysis;
  report_id?: string;
}

export interface ImpactInsights {
  heading: string;
  text: string;
  heading_bkp: string;
  text_bkp: string;
  chart: Chart;
}

interface Chart {
  charts: CustomChart[];
  isTimePeriod: boolean;
  dashboardTitle: string;
}

interface CustomChart {
  name: string;
  data: number[];
  dashStyle: string;
  type: string;
  zIndex: number;
  lineWidth: number;
  color: string;
  marker: Marker;
  category: string[];
  yAxisLabel: string;
  xAxisLabel: string;
}

interface Marker {
  fillColor: string;
  lineWidth: number;
  lineColor: string;
  symbol: string;
  enabled: boolean;
}

export interface DomainInsights {
  name: string;
  id: number;
  report_id: string;
}

interface Keydrivers {
  ai_summary_bkp: string;
  ai_summary: string;
  drivers: Driver[];
}

interface Driver {
  name: string;
  id: string;
  icon: string;
  light_icon: string;
  indicator_name: string;
  heading: string;
  value: number;
  whether_positive_trend: boolean;
}

interface Title {
  name: string;
}
