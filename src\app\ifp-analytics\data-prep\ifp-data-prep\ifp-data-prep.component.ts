import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>mponent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, OnInit, Renderer2, ViewChild, WritableSignal, effect, signal } from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';
import { IfpPrepToolbarComponent } from '../../organism/ifp-prep-toolbar/ifp-prep-toolbar.component';
import { IfpPrepHeaderComponent } from '../../organism/ifp-prep-header/ifp-prep-header.component';
import { Toolbar, Tools } from './interface/ifp-data-prep.interface';
import { basicTools } from './ifp-data-prep.constant';
import { ActivatedRoute, Params, Router, RouterOutlet } from '@angular/router';
import { SubSink } from 'subsink';
import { IfpToggleSidebarComponent } from 'src/app/ifp-widgets/ifp-molecules/ifp-toggle-sidebar/ifp-toggle-sidebar.component';
import { Ng<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from '@angular/common';
import { IfpTooltipDirective } from 'src/app/scad-insights/core/directives/ifp-tooltip.directive';
import { IfpPrepService } from './ifp-prep-service';
import { IfpDataPreviewComponent } from '../../organism/ifp-data-preview/ifp-data-preview.component';
import { Store } from '@ngrx/store';
import { selectNodePrepResponse } from './store/node-store/node-store.selectors';
import { loadCreateNodeSuccess } from './store/node-store/node-store.actions';
import { connectionType, prepsApiEndpoints } from './constants/if-preps.constants';
import { SubscriptionLike } from 'rxjs';
import { StreamingService } from 'src/app/scad-insights/core/services/http/streaming.service';
import { environment } from 'src/environments/environment';
import { WorkFlowStatusStream } from './ifp-prep-library/intreface/ifp-prep-library.interface';
import { prepStatusStore, workFlowState } from './constants/ifp-state.contants';
import { ToasterService } from 'src/app/scad-insights/core/services/tooster/ToastrService.service';
import { NodeState } from './store/node-store/node-store.state';
import { IfpModalComponent } from '../../../scad-insights/ifp-widgets/ifp-organism/ifp-modal/ifp-modal.component';
import { IfpRemoveCardComponent } from '../../../scad-insights/ifp-widgets/ifp-atoms/ifp-remove-card/ifp-remove-card.component';
import { IfpModalService } from 'src/app/scad-insights/ifp-widgets/ifp-organism/ifp-modal/ifp-modal.service';
import { ApiService } from 'src/app/scad-insights/core/services/api.service';
import { UsageDashboardLogService } from 'src/app/scad-insights/core/services/usage-dashboard-log/usage-dashboard-log.service';
import { logType } from 'src/app/scad-insights/core/services/usage-dashboard-log/usage-dashboard.constants';

@Component({
    selector: 'ifp-data-prep',
    templateUrl: './ifp-data-prep.component.html',
    styleUrl: './ifp-data-prep.component.scss',
    providers: [
        DatePipe
    ],
    imports: [TranslateModule, IfpPrepToolbarComponent, IfpPrepHeaderComponent, RouterOutlet, IfpToggleSidebarComponent, NgClass, IfpTooltipDirective,
    IfpDataPreviewComponent, DatePipe, IfpModalComponent, IfpRemoveCardComponent]
})
export class IfpDataPrepComponent implements OnInit, AfterViewInit, OnDestroy {

  @HostListener('window:beforeunload', ['$event'])
  unloadNotification($event: any): void {
    $event.returnValue = true;
  }

  @ViewChild('processFlow') processFlow!: ElementRef;
  @ViewChild('prepcontent') prepcontent!: ElementRef;
  @ViewChild('prepToolBar') prepToolBar!: ElementRef;
  @ViewChild('content') content!: ElementRef;
  @ViewChild('saveWorkflowModal') saveWorkflowModal!: IfpModalComponent;
  @ViewChild('advPreview') advPreview!: IfpDataPreviewComponent;
  @ViewChild('resizableDiv') resizableDiv!: ElementRef;


  public tools: Toolbar[] = basicTools;
  public basic = true;
  public selectedTool: WritableSignal<Tools> = signal(this.tools[0].menu[0]);
  public subs: SubSink = new SubSink();
  public status!: string;
  public summary: boolean = false;
  public workFlowId: string | null = null;
  private eventSourceSubscription!: SubscriptionLike;
  private observerResize!: ResizeObserver;
  private observerToolResize!: ResizeObserver;
  public nodeData!: NodeState;
  public workFlowStatus = workFlowState;
  public controller!: AbortController;
  public isExpertMode: boolean = false;
  public additionalHeight!: number;
  public initialHeight!: number;
  public detectChange: boolean = false;
  public tabItems: any = [
    {
      label: 'Basic Mode',
      action: 'basic',
      buttonClass: 'primary',
      isActive: true,
      iconClass: 'ifp-icon-cube'
    },
    {
      label: 'Expert Mode',
      action: 'expert',
      isActive: false
    }
  ];

  public selectedTab: Record<string, string |boolean> = this.tabItems[0];
  private sessionId!: string;

  constructor(private _router: Router,
    private _params: ActivatedRoute, public _prepService: IfpPrepService,
    private store: Store, private _eventSourceService: StreamingService, private _renderer: Renderer2, private _toast: ToasterService,     private _modalService: IfpModalService, private _apiService: ApiService, private log: UsageDashboardLogService, private _datePipe:DatePipe) {
    effect(() => {
      if (['summary', 'source'].includes(this.selectedTool().key) && this._prepService.uploadedFileResponse()?.id) {
        this.tools[0].menu[0].enabled = true;
        this.tools[1].menu[0].enabled = true;
      } else {
        this.tools[0].menu[0].enabled = false;
        this.tools[1].menu[0].enabled = false;
      }
    });
  }

  ngOnInit() {
    this.stopWorkflow(false);
    this._prepService.logs.set([]);
    this.store.dispatch(loadCreateNodeSuccess());
    this.subs.add(
      this._params.queryParams.subscribe((params: Params) => {
        this.summary = params['summary'] && this._prepService.uploadedFileResponse()?.id ? true : false;
        if (params['status']) {
          this.status = params['status'];
        }
      }),
      this._router.events.subscribe(() => {
        const url = this._router.url.split('?')[0];
        // this.tools.forEach(data => {
        //   data.menu.find((tool: Tools) => {
        //     if (`/${tool.url}` === url || `${tool.url}` === url) {
        //       this.selectedTool.set(tool);
        //     }
        //   });
        // });
        for (const data of this.tools) {
          let foundTool;
          if (this._router.url.includes('summary') && this._prepService.uploadedFileResponse()?.id) {
            foundTool = data.menu.find((tool: Tools) => tool.key === 'summary');
          } else {
            foundTool = data.menu.find((tool: Tools) => `/${tool.url}` === url || `${tool.url}` === url
            );
          }
          if (foundTool) {
            this.selectedTool.set(foundTool);
            break;
          }
        }
        this.initialHeight = 0;

      })
    );
    this.workFlowId = this._prepService.workFlowID();
    this.subs.add(
      this.store.select(selectNodePrepResponse).subscribe(dataState => {
        this._prepService.logs.set([]);
        const nodeLength = dataState.data.nodes.length;
        this.nodeData = dataState;
        this._prepService.processStatus.next(null);
        if (nodeLength != 0) {
          this._prepService.currentNodeIdBeforeWorkFlow.set(dataState.data.nodes[nodeLength - 1]?.object_id);
          if (this._prepService.processActive()) {
            const nodeData = dataState.data.nodes[nodeLength - 1];
            const nodeDataPrev = dataState.data.nodes[nodeLength - 2];
            if (dataState.currentStatus == prepStatusStore.datasetAdd && nodeData.type === connectionType.inputTool) {
              const dataSetLength = Object.values(this._prepService.currentdataSetObjectId()).length;
              this._prepService.currentdataSetObjectId.set({ [dataSetLength + 1]: nodeData.object_id });
            }
            this._prepService.processActive.set(false);
            this._prepService.disableSidebar.set(true);
            this.subs.add(
              this._prepService.postMethodRequest(prepsApiEndpoints.workflowrun, dataState.data).subscribe({
                next:
                  (workflow: {
                    workflow: string
                  }) => {
                    // this.subs.add(this._prepService.getMethodStreamRequest( `${prepsApiEndpoints.workflowStatus +  this.workFlowId}/`).subscribe(data => {
                    //   console.log(data, 'status');
                    // }));
                    this._prepService.workflowStarted.set(true);
                    this._prepService.previousNodeId.set(this._prepService.currentNodeId());
                    this._prepService.currentNodeId.set(nodeData.object_id);
                    this.eventSourceSubscription = this._eventSourceService.connectToServerSentEvents(`${environment.prepbaseUrl + prepsApiEndpoints.workflowStatus + workflow.workflow}/`)
                      .subscribe({
                        next: (data: { data: string, ctrl: AbortController }) => {
                          if (data.data) {
                            const convertJsonData: WorkFlowStatusStream = JSON.parse(data.data);
                            if (convertJsonData?.logs && convertJsonData?.logs?.length !==0) {
                              const currentLogs:{
                                key: string;
                                title: string;
                                value: string;
                              }[][] =[];
                              convertJsonData?.logs?.forEach((element:{
                                timestamp: string;
                                log_level: string;
                                message: string;
                              }) => {
                                currentLogs.push([{ key: 'Message', title: 'Message', value: element.message}, { key: 'Time', title: 'Time', value: this._datePipe.transform(new Date(element.timestamp), 'h:mm:ss a') ?? '' }, { key: 'Status', title: 'Status', value: element.log_level}]);
                              });
                              this._prepService.logs.set(currentLogs);
                            }
                            this._prepService.processStatus.next(convertJsonData);
                            this.controller = data.ctrl;
                            if (workFlowState.completed == convertJsonData.workflow_status) {
                              this._prepService.workFlowID.set(workflow.workflow);
                              this.workFlowId = workflow.workflow;
                              data.ctrl.abort();
                              this.eventSourceSubscription.unsubscribe();
                              if (nodeData.type !== connectionType.inputTool) {
                                this._prepService.disableSidebar.set(false);
                              }
                            } else if (workFlowState.error == convertJsonData.workflow_status) {
                              this._toast.error('An error occurred in the workflow!');
                              data.ctrl.abort();
                              this.eventSourceSubscription.unsubscribe();
                              if (nodeDataPrev?.object_id) {
                                this._prepService.currentNodeId.set(nodeDataPrev.object_id);
                              }
                              if (nodeData.type !== connectionType.inputTool) {
                                this._prepService.disableSidebar.set(false);
                              }
                            }

                          }
                        },
                        error: () => {
                          this._prepService.workFlowID.set(workflow.workflow);
                          this._prepService.currentNodeData.next(dataState.data);
                          this._eventSourceService.close();
                          this._prepService.disableSidebar.set(false);
                          if (nodeDataPrev?.object_id) {
                            this._prepService.currentNodeId.set(nodeDataPrev.object_id);
                          }
                          if (nodeData.type !== connectionType.inputTool) {
                            this._prepService.disableSidebar.set(false);
                          }
                          this.eventSourceSubscription.unsubscribe();
                        }
                      }
                      );

                  },
                error: (errorMessage) => {
                  this._apiService.errorHandler(errorMessage?.error?.message? errorMessage?.error?.message : ( errorMessage?.error?.error ?? errorMessage?.error));
                  this._prepService.processStatus.next({ workflow_status: workFlowState.error });
                  if (nodeData.type !== connectionType.inputTool) {
                    this._prepService.disableSidebar.set(false);
                  }
                  this._prepService.logs.set([[{ key: 'Message', title: 'Message', value: errorMessage?.error?.message ?? (errorMessage?.error.error ?? '')}, { key: 'Time', title: 'Time', value: this._datePipe.transform(new Date(), 'h:mm:ss a') ?? ''}, { key: 'Status', title: 'Status', value: 'ERROR'}]]);
                }
              })
            );
          } else {
            const nodeData = dataState.data.nodes[nodeLength - 1];
            this._prepService.currentNodeId.set(nodeData.object_id);
          }
        }

      })
    );
    if (this.sessionId) {
      this.log.logEnds(this.sessionId, this.log.currentTime );
    }
    this.sessionId = this.log.createUUid;
    this.log.logStart(this.sessionId, logType.basicPrep, this.log.currentTime );
  }

  ngAfterViewInit(): void {
    this.observerResize = new ResizeObserver(entries => {
      const width = entries[0].contentRect.width;
      this._renderer.setStyle(this.prepcontent.nativeElement, 'width', `calc(100% - ${width}px)`);
    });
    this.observerResize.observe(this.processFlow.nativeElement);
    this.observerToolResize = new ResizeObserver(entries => {
      const width = entries[0].contentRect.width;
      this._renderer.setStyle(this.content.nativeElement, 'width', `calc(100% - ${width}px)`);
    });
    this.observerToolResize.observe(this.prepToolBar.nativeElement);
  }

  resizePreview() {
    this.resizableDiv.nativeElement.style.height = null;
  }

  onMouseDown(event: MouseEvent, resizableDiv: HTMLElement): void {
    this.handleDragStart(event.clientY, resizableDiv);
  }

  onTouchStart(event: TouchEvent, resizableDiv: HTMLElement): void {
    const touch = event.touches[0]; // Get the first touch point
    this.handleDragStart(touch.clientY, resizableDiv);
    event.preventDefault(); // Prevent default touch behavior
  }

  private handleDragStart(initialY: number, resizableDiv: HTMLElement): void {
    const initialHeight = resizableDiv.offsetHeight;
    if (!this.initialHeight) {
      this.initialHeight = initialHeight;
    }
    this.additionalHeight = this.initialHeight > initialHeight ? this.initialHeight - initialHeight + 300 : 300;
    this.detectChange = !this.detectChange;

    const onMove = (moveEvent: MouseEvent | TouchEvent) => {
      const clientY = this.getClientY(moveEvent);
      const newHeight = initialHeight + (clientY - initialY);
      if (newHeight < this.initialHeight) {
        resizableDiv.style.height = `${newHeight}px`;
        const setHeight = this.additionalHeight - (clientY - initialY);
        this.advPreview.setHeight(setHeight > 300 ? setHeight : 300);
      }
    };

    const onEnd = () => {
      document.removeEventListener('mousemove', onMove);
      document.removeEventListener('mouseup', onEnd);
      document.removeEventListener('touchmove', onMove);
      document.removeEventListener('touchend', onEnd);
    };

    document.addEventListener('mousemove', onMove);
    document.addEventListener('mouseup', onEnd);
    document.addEventListener('touchmove', onMove);
    document.addEventListener('touchend', onEnd);
  }

  private getClientY(event: MouseEvent | TouchEvent): number {
    return (event instanceof MouseEvent) ? event.clientY : event.touches[0].clientY;
  }

  getSelectedTool(tool: Tools) {
    this.resizePreview();
    this.selectedTool.set(tool);
    if (tool.url) {
      if (tool.type === 'SummaryTool') {
        this._prepService.isSummary.set(true);
        this._router.navigate([tool.url], { replaceUrl: true, queryParams: { summary: 'true' } });
      } else {
        this._prepService.isSummary.set(false);
        this._router.navigate([tool.url], { replaceUrl: true });
      }
      // this._router.navigate([tool.url], { replaceUrl: true });
    }
  }

  goToExpertMode() {
    const activeButton: any =  document.activeElement;
    activeButton?.blur();
    let nodeCount = 0;
    this.store.select(selectNodePrepResponse).subscribe(dataState => {
      nodeCount = dataState.data.nodes.length;
    });
    if (nodeCount > 0) {
      this.saveWorkflowModal.createElement();
    } else {
      this.stopWorkflow();
      this._router.navigate(['analytics/advance']);
      this._prepService.uploadedFileResponse.set(null);
      this._prepService.fileName.set('');
    }
  }

  closeSaveModel() {
    this.isExpertMode = false;
    this.saveWorkflowModal.removeModal();
    this._modalService.removeAllModal();
  }

  goToNextPage() {
    this.saveWorkflowModal.removeModal();
    this.isExpertMode = true;
    setTimeout(() => {
      this.stopWorkflow();
      this._router.navigate(['analytics/advance']);
      this._prepService.uploadedFileResponse.set(null);
      this._prepService.fileName.set('');
    }, 300);

  }

  stopWorkflow(init: boolean = true) {
    this.subs.add( this._prepService.postMethodRequest(`${prepsApiEndpoints.workflows}flush/`).subscribe(_resp => {
      if (init) {
        this.subs.unsubscribe();
        this.controller?.abort();
        this.eventSourceSubscription?.unsubscribe();
      }
    }));
  }




  ngOnDestroy() {
    this.subs.unsubscribe();
    this.observerResize?.unobserve(this.processFlow.nativeElement);
    this.observerToolResize?.unobserve(this.processFlow.nativeElement);
    this.controller?.abort();
    if (this.sessionId) {
      this.log.logEnds(this.sessionId, this.log.currentTime );
    }
  }

}
