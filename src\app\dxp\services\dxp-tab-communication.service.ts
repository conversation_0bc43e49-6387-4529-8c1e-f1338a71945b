import { Injectable } from '@angular/core';
import { BehaviorSubject } from 'rxjs';

export interface TabSelectionData {
  param: string;
  subMenus?: any[];
  selectedMenuItem?: any;
  tabKey?: string;        // Added: Store the main tab key (e.g., 'my-kpis')
  subMenuKey?: string;    // Added: Store the submenu key (e.g., 'my-kpis-complete')
  userRole?: string;      // Added: Store the user role (e.g., 'BUILDER')
}

export interface FilterData {
  entitiesFilterData: any[];
  creatorsFilterData: any[];
}

@Injectable({
  providedIn: 'root'
})
export class DxpTabCommunicationService {
  private tabSelectionSubject = new BehaviorSubject<TabSelectionData>({ param: '' });
  public tabSelection$ = this.tabSelectionSubject.asObservable();

  private filterDataSubject = new BehaviorSubject<FilterData>({
    entitiesFilterData: [],
    creatorsFilterData: []
  });
  public filterData$ = this.filterDataSubject.asObservable();

  constructor() { }

  // Method to emit tab selection data from parent to child
  emitTabSelection(data: TabSelectionData) {
    this.tabSelectionSubject.next(data);
  }

  // Method to get current tab selection
  getCurrentTabSelection(): TabSelectionData {
    return this.tabSelectionSubject.value;
  }

  // Method to emit filter data from parent to child
  emitFilterData(data: FilterData) {
    console.log(data);

    this.filterDataSubject.next(data);
  }

  // Method to get current filter data
  getCurrentFilterData(): FilterData {
    return this.filterDataSubject.value;
  }
}
