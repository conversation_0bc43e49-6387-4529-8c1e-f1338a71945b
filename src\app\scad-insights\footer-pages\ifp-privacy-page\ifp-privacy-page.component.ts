import { CommonModule } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { Title } from '@angular/platform-browser';
import { TranslateModule } from '@ngx-translate/core';
import { PageData } from 'src/app/scad-insights/core/interface/molecule/breadcrumb.interface';
import { LineBreakPipe } from 'src/app/scad-insights/core/pipes/lineBreak.pipe';
import { FooterService } from 'src/app/scad-insights/core/services/footer/footer.service';
import { IfpBreadcrumbsComponent } from 'src/app/scad-insights/ifp-widgets/ifp-molecules/ifp-breadcrumbs/ifp-breadcrumbs.component';
import { SubSink } from 'subsink';
import { IFPMsalService } from '../../core/services/IFP-msal.service';
import { title } from '../../core/constants/header.constants';

@Component({
    selector: 'app-ifp-privacy-page',
    templateUrl: './ifp-privacy-page.component.html',
    styleUrls: ['./ifp-privacy-page.component.scss'],
    imports: [CommonModule, TranslateModule, IfpBreadcrumbsComponent, LineBreakPipe]
})

export class IfpPrivacyPageComponent implements OnInit {

  public subs: SubSink = new SubSink();
  public privacy!: any;

  constructor(private _titleService: Title, private _footerService: FooterService, private _msalService:IFPMsalService) {
    this._titleService.setTitle(`${title.bayaan} | Privacy`);

    (window as any)?.dataLayer?.push({
      'event': 'page_load',
      'page_title_var': 'Privacy',
      'userId': this._msalService.convertEmail(this._msalService.getLoginData.account.username)
    });

  }

  ngOnInit() {
    this.subs.add(
      this._footerService.getPrivacy().subscribe((res: any) => {
        if (res) {
          this._titleService.setTitle(`${title.bayaan} | ${res?.title}`);
          if (res?.title) {
            (window as any)?.dataLayer?.push({
              'event': 'page_load',
              'page_title_var': res?.title,
              'userId': this._msalService.convertEmail(this._msalService.getLoginData.account.username)
            });
          }
          this.privacy = res;
          this.pageData = [
            {
              title: 'Home',
              route: '/home'
            },
            {
              title: res?.title,
              route: ''
            }
          ];
        }
      })
    );
  }

  pageData: PageData[] = [
    {
      title: 'Home',
      route: '/home'
    },
    {
      title: 'Privacy Policy',
      route: ''
    }
  ];

}
