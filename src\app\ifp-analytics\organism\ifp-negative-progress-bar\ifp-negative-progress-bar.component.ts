import { DecimalPipe } from '@angular/common';
import { Component, Input } from '@angular/core';

@Component({
    selector: 'ifp-negative-progress-bar',
    imports: [DecimalPipe],
    templateUrl: './ifp-negative-progress-bar.component.html',
    styleUrl: './ifp-negative-progress-bar.component.scss'
})
export class IfpNegativeProgressBarComponent {
@Input() value = 0;
@Input() color = '';
@Input() format = '1.0-2';
}
