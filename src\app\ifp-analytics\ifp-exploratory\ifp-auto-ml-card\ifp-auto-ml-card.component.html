<div #cardAnalytic class="ifp-analysis__outer">
  <ifp-card class="ifp-auto-ml-card">
    <ifp-analysis-card-header [feIcon]="true" [icons]="[{icon: 'ifp-icon-auto-ml', color: '#5DB14E'}]" [isExpandShow]="true"  (resized)="resize($event)" (openInNewTab)="urlRedirect()" [enableNotification]="false" [addMyAppsLanding]="true" [isLink]="false" [link]="undefined" [small]="true"></ifp-analysis-card-header>
    <div class="ifp-analysis ifp-auto-ml-card__wrapper ifp-analysis--hybrid">
      <div class="ifp-analysis__left ifp-auto-ml-card__left" [ngClass]="{'ifp-analysis__left--minimize': !this.small()}">
        <div>
          <h3 class="ifp-analysis__heading ifp-auto-ml-card__heading" [appIfpTooltip]="name"
          [disableTooltip]="name ? name.length < textLimit : true" [delay]="3000" [extraSpaceTop]="-10">{{name ?
          name.charAt(0).toUpperCase() + name.slice(1): '' | translate }}</h3>
        </div>

        <p class="ifp-analysis__substitle">{{subTitle | quotRemove}}</p>
        <div class="ifp-analysis__txt-icons">
          @if(publish) {
            <div class="ifp-analysis__txt-icon">
              <ifp-icon-text [icon]="'ifp-icon-calender'" [text]="publish" [key]="'Updated date'"></ifp-icon-text>
            </div>
          }

@if(tagName &&  tagName !== '') {
  <div class="ifp-analysis__txt-icon ifp-auto-ml-card__footer" >
    <ifp-button [buttonClass]="'ifp-btn--disabled ifp-btn--md ifp-btn--capitalize'"
      [label]="tagName"></ifp-button>
      <em [appIfpTooltip]="'Delete' | translate" [extraSpaceTop]="22" class="ifp-icon ifp-icon-trash" (click)="removeScenarioModal()"></em>
  </div>
}

        </div>
      </div>
      @if(!small()) {
        <div class="ifp-analysis__right">
          @if (!loading()) {
            <div class="ifp-analysis__rate-card" >
              <div class="ifp-analysis__chart">
                @if (chart()) {
                  <app-ifp-analytic-line-chart [chartData]="chartData" [legend]="false"
                  #chartComponent [height]="250" [isDatalabel]="false"></app-ifp-analytic-line-chart>
                }

              </div>
            </div>
          } @else {
            <app-ifp-card-loader class="ifp-loader" [type]="'chart'"></app-ifp-card-loader>
          }


        </div>
      }


    </div>
  </ifp-card>
</div>
<app-ifp-modal [zIndex]="1205" [overlayIndex]="1204" #modal>
  <app-ifp-remove-card [text]="'Do you want to remove?'" (firstButtonEvent)="closeModalEvent($event)"
    (secondButtonEvent)="closeModalEvent($event)">
  </app-ifp-remove-card>
</app-ifp-modal>
