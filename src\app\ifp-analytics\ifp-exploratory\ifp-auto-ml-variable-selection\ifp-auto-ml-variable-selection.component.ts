import { buttonClass } from 'src/app/scad-insights/core/constants/button.constants';
import { Component, EventEmitter, Input, Output } from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';
import { NgClass } from '@angular/common';
import { IfpMlColorPipe } from '../ifp-ml-color.pipe';
import { IfpNoDataComponent } from 'src/app/scad-insights/ifp-widgets/ifp-molecules/ifp-no-data/ifp-no-data.component';
import { IterationList } from '../ifp-exploratory-auto-ml-analyze/ifp-exploratory-auto-ml-analyze.component';

@Component({
    selector: 'ifp-auto-ml-variable-selection',
    imports: [TranslateModule, NgClass, IfpMlColorPipe, IfpNoDataComponent],
    templateUrl: './ifp-auto-ml-variable-selection.component.html',
    styleUrl: './ifp-auto-ml-variable-selection.component.scss'
})
export class IfpAutoMlVariableSelectionComponent {

  @Input()  badgeData:{name: string, value: number}[] = [];
  @Input() iterationList:IterationList[] = [];
  @Input() targetValue = '';
  @Input() timeStamp = '';
  @Output() selectedEvent =  new EventEmitter();
  @Output() runModel =  new EventEmitter();
  @Output() moveBackEvent =  new EventEmitter();
  public indicatorValues = [1, 0.50, 0.25, -0.25, -0.50, -1];
  public buttonClass =buttonClass;
  public selectedValueIndex: number[]  = [];
  public selectedValues: string[]  = [];

  // used to select value from the variable list
  selections(item:{
    name: string;
    value: number;
}, indexItem: number ) {
    const index = this.selectedValueIndex.indexOf(indexItem);
    const indexValue =this.selectedValues.indexOf(item.name);
    if (this.selectedValues.length >=10 &&index === -1) {
      return;
    }

    if (index > -1) {
      this.selectedValueIndex.splice(index, 1);
    } else {
      this.selectedValueIndex.push(indexItem);
    }
    if (indexValue > -1) {
      this.selectedValues.splice(index, 1);
    } else {
      this.selectedValues.push(item.name);
    }
    this.selectedEvent.emit({
      currentSelectedValue: item,
      selectedList: this.selectedValues,
      selectedIndex: this.selectedValueIndex
    });
  }

  // emit value for run model

  runModelEvent() {
    this.runModel.emit();
  }

  // emit value when click iteration

  moveBack() {
    this.moveBackEvent.emit();
  }
}
