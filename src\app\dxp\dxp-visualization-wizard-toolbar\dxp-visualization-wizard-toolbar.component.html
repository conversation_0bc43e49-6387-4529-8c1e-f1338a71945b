<div class="ifp-vis-toolbar">
  <p class="ifp-vis-toolbar__heading">{{'Card Configuration' | translate}}</p>
  <p class="ifp-vis-toolbar__desc">{{'Customize your chart visualization' | translate}}</p>
  <ul class="ifp-vis-toolbar__tabs">
    @for (menu of tabList(); track $index) {
      <li class="ifp-vis-toolbar__tab-item" [ngClass]="{'ifp-vis-toolbar__tab-item--active': selectedTabItem().key === menu.key}" (click)="onSelectTabItem($index)"><em class="ifp-icon ifp-vis-toolbar__tab-icon" [class]="menu.icon"></em>{{menu.title | translate}}</li>
    }
  </ul>
  <div class="ifp-vis-toolbar__body">
    <p class="ifp-vis-toolbar__tool-desc">{{selectedTabItem().description | translate}}</p>

    @if (selectedTabItem().key === 'filter') {
      <!-- filter start -->
      <div class="ifp-vis-toolbar__filter-sec">
        @if (isAddFilter && columnList().length) {
          <ifp-dxp-visualization-filter-form [selectedProductDetails]="selectedProductDetails()" [columnList]="columnList()" (addFilter)="addNewFilter($event)" (cancelFilter)="isAddFilter = false"></ifp-dxp-visualization-filter-form>
        } @else {
          @for (filter of addedFilters; track $index) {
            <ifp-dxp-visualization-filter-card class="ifp-vis-toolbar__filter-card" [filter]="filter" (removeCard)="onRemoveCard($index)"></ifp-dxp-visualization-filter-card>
          } @empty {
            <!-- <app-ifp-no-data [message]="'No filters added!'" [isTransparent]="true"></app-ifp-no-data> -->
            <div class="ifp-vis-toolbar__no-data">
              <p class="ifp-vis-toolbar__empty-message">{{'No filters added!' | translate }}</p>
              <ifp-button [label]="'Add Filter'" [buttonClass]="buttonClass.primary +' '+buttonClass.normalAplabetic" [iconClass]="'ifp-icon-plus'" (ifpClick)="openNewFilter()" class="ifp-vis-toolbar__filter-btn"></ifp-button>
            </div>
          }
          @if (addedFilters.length) {
            <ifp-button [label]="'New Filter'" [buttonClass]="(!columnList().length?buttonClass.disabled:buttonClass.primary) +' '+buttonClass.normalAplabetic" [iconClass]="'ifp-icon-plus'" (ifpClick)="openNewFilter()" class="ifp-vis-toolbar__filter-btn ifp-vis-toolbar__filter-btn--below"></ifp-button>
          }
        }
      </div>
      <!-- filter end -->
    } @else if (selectedTabItem().key === 'series') {
      <div class="ifp-vis-toolbar__series-sec">
        <app-ifp-db-dropdown
          [isMultiSelect]="true"
          [placeholder]="'Select Legend'"
          [options]="columnList() || []"
          (multiSelected)="onLegendMultiSelected($event)"
          [key]="'name'"
          [defaultSelect]="false"
          [multipleSelectedItems]="selectedLegends"
          class="ifp-vis-toolbar__legend-dropdown"
          (close)="onLegendDropdownClose()"
        ></app-ifp-db-dropdown>
        @if (selectedLegends.length) {
          <div class="ifp-vis-toolbar__selected-list">
            <label class="ifp-vis-toolbar__default-label">Choose Legend:</label>
            @for (legend of selectedLegends; track $index; let i = $index) {
              <div class="ifp-vis-toolbar__selected-item">
                <input type="radio" name="defaultLegend" [value]="legend" [(ngModel)]="defaultLegend" (change)="onDefaultLegendChange(legend)" />
                <span>{{legend.name}}</span>
              </div>
            }
          </div>
        }
      </div>
    } @else {
      @for(content of selectedTabItem().content; let i = $index; track content.key) {
        <ifp-accordion [heading]="content.title | translate" class="ifp-vis-toolbar__accordion"
          (selectAccordian)="expandAccordian($event,content)" [isExpanded]="content.isExpand">
          <div class="ifp-accordion__body-content">
            @switch(content.key) {
              @default {}

              @case('chartsType') {
                <div class="ifp-vis-toolbar__chart-set">
                  @for (chart of chartTypes; track chart.key; let i = $index) {
                  <ifp-icon-selector [icon]="chart"
                  (selectIcon)="updateChartType($event)"

                    class="ifp-vis-toolbar__chart-icon" ></ifp-icon-selector>
                  }
                </div>
              }

              @case ('title') {
                <input type="text" class="ifp-input" [(ngModel)]="cardTitle">
              }

              @case ('description') {
                <textarea class="ifp-input-textarea" [(ngModel)]="cardDescription"></textarea>
              }
            }
          </div>
        </ifp-accordion>
      }
    }
  </div>
</div>
