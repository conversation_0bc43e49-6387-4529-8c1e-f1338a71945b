<div class="ifp-data-tree" [ngClass]="{'ifp-data-tree--single': treeData().length === 1, 'ifp-data-tree--double': treeData().length === 2}">
  <div class="ifp-data-tree__box-wrapper">
    @for (data of treeData(); track $index) {
      <div class="ifp-data-tree__box">
        <p class="ifp-data-tree__box-title"><em class="ifp-icon ifp-icon-statistical-indicators ifp-data-tree__box-icon"></em>{{data.name | titlecase}}</p>
        <div class="ifp-data-tree__box-value">
          <p class="ifp-data-tree__value-text">{{data.value  | number}}</p>
          @if (showPercentage()) {
            <span class="ifp-data-tree__value-percent" [style.color]="data.color">{{((data.value/totalCount()) * 100).toFixed(1)}}%</span>
          }
        </div>
        <span class="ifp-data-tree__box-tag" [style.backgroundColor]="data.color"></span>
      </div>
    }
  </div>
  <ifp-tree-progress [progressData]="treeData()" [value]="totalCount()" [title]="title()" class="ifp-data-tree__progress"></ifp-tree-progress>

</div>
