<div class="ifp-ai-report">
  <img
    [src]="(_themeService.defaultTheme$ | async)!=='dark' ? '../../../assets/images/dashboard-builder/landing-bg.png' : '../../../assets/images/prep-help/landing_page_dark.svg'"
    alt="" class="ifp-ai-report__bg">
  @if (!previewExternal()) {
  <div class="ifp-container ifp-ai-report__breadcrumbs">
    <app-ifp-breadcrumbs [disableTranslate]="disableTranslate()"  [pageData]="pageData"></app-ifp-breadcrumbs>
  </div>
  }

  @if (loader()) {
  <app-ifp-spinner class="ifp-ai-report__loader"></app-ifp-spinner>
  }@else {
  @if (!previewExternal()) {
  <div class="ifp-container ifp-container--sm ifp-ai-report__header">
    <h1 class="ifp-ai-report__heading">{{'AI Insight Report Generator' }}</h1>
    <p class="ifp-ai-report__desc">{{'AI Insight Report Generator simplifies data analysis by leveraging artificial
      intelligence to produce clear, actionable insights in comprehensive reports, enabling smarter decision-making with
      ease.' }}</p>
      <app-ifp-stepper class="ifp-ai-report__stepper" [showCount]="true" [pending]="completedSteps()+1" [gradientTheme]="true" [count]="false" [completed]="completedSteps()" [stepData]="stepsData()"></app-ifp-stepper>
  </div>
  }
  <!-- Report content start -->
  <div class="ifp-container ifp-container--sm">

    <div class="ifp-ai-report__main">
      <div class="ifp-ai-report__main-title">
        <div class="ifp-ai-report__main-title-text">
          {{'Bayaan AI Insight Report' }}
        </div>
        <div class="ifp-ai-report__main-download-wrap">
          <div class="ifp-ai-report__date-badge">{{report()?.quarter ??''}}</div>
          @if (!previewExternal()&&previewNot()) {
          <ifp-button [disableTranslate]="disableTranslate()" class="ifp-ai-report__main-download" [label]="'Download'" [loader]="downloadProgress()"
            [buttonClass]="(downloadProgress() ? buttonClass.disabled : buttonClass.primary+' '+ buttonIconPosition.left ) " [iconClass]="downloadProgress() ? '':'ifp-icon-download'"
            (ifpClick)="downloadReport()"></ifp-button>
          }
        </div>

      </div>
      <!-- Main head start -->
      <div class="ifp-ai-report__main-head">
        <div class="ifp-ai-report__main-head-left">
          @if (domainIconSelector$ | async; as icon) {
          @if ((_themeService.defaultTheme$ | async) === 'dark') {
          <img [src]="icon?.body?.icon ??'../../../../assets/images/icon-placeholder.png'" alt="" width="25px"
            height="25px" class="ifp-ai-report__domain-icon">
          } @else {
          <img [src]="icon?.body?.light_icon ?? '../../../../assets/images/icon-placeholder.png'" width="25px"
            height="25px" alt="" class="ifp-ai-report__domain-icon">
          }
          }
          <p class="ifp-ai-report__domain-name">{{report()?.domain?.name ?? ''}}</p>
        </div>
        <div class="ifp-ai-report__main-head-right">
          @if (previewNot()) {
          <ifp-ai-button  [disableTranslate]="disableTranslate()" [iconClass]="'ifp-icon-ai'" [theme]="'ifp-ai-button--round ifp-ai-button--ai'"
            [label]="'Regenerate'" (ifpClick)="regenarateAll()"></ifp-ai-button>
            @if (report()?.status === 'active' || report()?.status === 'ticket_creation_failed') {
          <ifp-button [disableTranslate]="disableTranslate()" class="ifp-ai-report__btn" [label]="'Send for review'"
          [buttonClass]="loaderSubmitProgress() ? buttonClass.disabled : buttonClass.primary+' '+ buttonIconPosition.left" [loader]="loaderSubmitProgress()"
          [iconClass]="loaderSubmitProgress() ? '':'ifp-icon-send-triangle'"
             [iconClass]="'ifp-icon-send-triangle'"
            (ifpClick)="submit()"></ifp-button>
            }
          }
          @if (genaratedPreview()){
          <ifp-button [disableTranslate]="disableTranslate()" [label]="'Cancel'" [buttonClass]="buttonClass.secondary+' '+ buttonIconPosition.left"
            (ifpClick)="cancelGenerate()"></ifp-button>
          <ifp-button [disableTranslate]="disableTranslate()" class="ifp-ai-report__btn" [label]="'Submit'" [buttonClass]="buttonClass.primary"
            (ifpClick)="submitGenerate()"></ifp-button>
          }

        </div>
      </div>
      <!-- Main head end -->

      <!-- Key drivers module start -->
      <ifp-gen-ai-key-header [preview]="previewNot()" (summeryEdit)="callEditHeader()"
        [summery]="report()?.key_drivers?.ai_summary ?? ''"
        [drivers]="report()?.key_drivers?.drivers ?? []"></ifp-gen-ai-key-header>


      <!-- Impact module start -->
      <div class="ifp-ai-report__impact ifp-ai-report__side-spacing">
        <div class="ifp-ai-report__module-header">
          <h2 class="ifp-ai-report__module-title">{{'Impact' }}</h2>
          @if (previewNot()) {
          <ifp-ai-button [iconClass]="'ifp-icon-edit'" [theme]="'ifp-ai-button--round'" [label]="'Edit'" [disableTranslate]="true"
            (ifpClick)="callKeyInsightsEdit()"></ifp-ai-button>
          }
        </div>
        <div class="ifp-ai-report__section">
          <div class="ifp-ai-report__impact-card">
            <p class="ifp-ai-report__title">{{report()?.impact?.chart?.dashboardTitle ?? ''}}</p>
            @if (report()?.impact?.chart?.charts?.[0]) {
            <ifp-ai-chart-card [enableAnimation]="previewNot()" [insightReport]="true" [isCustom]="true" [isDatalabel]="true"
              [customChartData]="report()?.impact?.chart?.charts?.[0]
            "></ifp-ai-chart-card>
            }

          </div>
          <div class="ifp-ai-report__impact">
            <p class="ifp-ai-report__title">{{report()?.impact?.heading ?? ''}}</p>
            <!-- <p class="ifp-ai-report__desc"> -->
              <!-- {{report()?.impact?.text ?? '' }} -->

            <!-- </p> -->
            <ifp-mark-down  class="ifp-ai-report__matrics-markdown" [data]="report()?.impact?.text ?? ''"></ifp-mark-down>
            <!-- <p class="ifp-ai-report__desc">
              {{'Consumer spending has declined, with retail sales growth dropping from 5.2% in Q3 to 2.1% in Q4. The increased cost of borrowing has led consumers to cut back on discretionary spending, prioritizing essential goods and services instead. Higher interest rates have also affected borrowing activity, as mortgage applications fell by 17.9%, and credit card debt growth slowed from 8.5% to 6.3%, indicating reduced reliance on short-term credit.' }}
            </p>
            <p class="ifp-ai-report__desc">
              {{'Rising interest rates have also weakened consumer confidence, with the Consumer Confidence Index declining by 6.3 points, as individuals anticipate further economic uncertainty. Many are delaying large purchases such as homes and cars due to higher financing costs. This cautious approach has contributed to a slowdown in key sectors like real estate, retail, and automobile sales.' }}
            </p> -->
          </div>

        </div>
        <div class="ifp-ai-report__clear"></div>

        <p class="ifp-ai-report__title ifp-ai-report__title--mar-0">{{'Key Insights:' }}</p>
        <div class="ifp-ai-report__insight-card-wrapper">
          @for (item of report()?.key_insights | slice:0:3; track $index) {

          <ifp-ai-insight-analysis-card [enableAnimation]="previewNot()" [customChartData]="item.chart?.charts?.[0]"
            [shadow]="true" [title]="item.heading" [description]="item.text" [statusDown]="item.whether_positive_trend"
            [badge]="false" class="ifp-ai-report__insight-card"></ifp-ai-insight-analysis-card>
          }

        </div>
      </div>
      <!-- Impact module end -->
      @if (report()?.comparison?.rows?.length !==0 && report()?.comparison?.rows?.length) {
      <!-- Comparison module start -->
      <div class="ifp-ai-report__compare">
        <div class="ifp-ai-report__compare-inner ifp-ai-report__module-curve">
          <ifp-ai-insight-compare-module [preview]="previewNot()" [compairData]="report()?.comparison"
            (edit)="callComparisonEdit()" class="ifp-ai-report__compare-main"></ifp-ai-insight-compare-module>
        </div>
      </div>
      <!-- Comparison module end -->
      }

      <!-- Sentiment Analysis start -->
      <div class=" ifp-ai-report__side-spacing">
        <div class="ifp-ai-report__module-header">
          <h2 class="ifp-ai-report__module-title">{{'Sentiment Analysis' }}</h2>
          @if (previewNot()) {
          <ifp-ai-button  [disableTranslate]="disableTranslate()" [iconClass]="'ifp-icon-edit'" [theme]="'ifp-ai-button--round'" [label]="'Edit'"
            (ifpClick)="callSentimentEdit()"></ifp-ai-button>
          }
        </div>
        <div class="ifp-ai-report__module-body ">
          <div class="ifp-ai-report__sec-1">
            <ifp-ai-insight-consolidated-report-sentiment-card [enableAnimation]="previewNot()" [insights]="true"
              [sentimentanalysis]="report()?.sentiment"></ifp-ai-insight-consolidated-report-sentiment-card>
          </div>
          <div class="ifp-ai-report__sec-2">
            <!-- @if (report()?.sentiment?.chart?.charts?.[0]) {
            <div  class="ifp-ai-report__card ifp-ai-report__sentimental-card">
              <p class="ifp-ai-report__title">{{'Sentiment Analysis'}}</p>
              <ifp-ai-chart-card [enableAnimation]="previewNot()" [height]="220" class="ifp-ai-insight-card__chart"  [insightReport]="true" [isCustom]="true" [customChartData]="report()?.sentiment?.chart?.charts?.[0]
            "></ifp-ai-chart-card>
            </div>
          } -->
            <p class="ifp-ai-report__title">{{'Key Insights:' }}</p>

            <p class="ifp-ai-report__desc">
              {{report()?.sentiment?.key_insights_desc}}
            </p>
          </div>
        </div>
        <p class="ifp-ai-report__source">{{'Data sources' }} : <span
            class="ifp-ai-report__sources">{{report()?.sentiment?.data_source}}</span>
        </p>
      </div>
      <ifp-ai-insight-report-footer [date]="report()?.published_date ?? ''"></ifp-ai-insight-report-footer>
      <!-- Sentiment Analysis end -->
    </div>
  </div>
  <!-- Report content end -->
  }
</div>

<app-ifp-modal #modal [modalClass]="'ifp-modal__template-help'">
  <ifp-ai-insight-report-popup [markdown]="markdown()"  [modelAnimate]="modelAnimate()" [data]="popupData()" [sectionOne]="sectionOne"
    [sectionTwo]="sectionTwo" [genarateAllPop]="previewGenaratePopup()" #popup (save)="save($event)"
    (genarate)="genarate($event)" [title]="title()" [subTitle]="subTitle()" [type]="modalType()"
    (close)="cancel()"></ifp-ai-insight-report-popup>
</app-ifp-modal>
