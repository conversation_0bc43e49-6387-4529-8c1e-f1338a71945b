import { Injectable } from '@angular/core';
import { environment } from 'src/environments/environment';
import { authTypes, localStorageKeys, uaePassProd, uaePassStaging } from '../../constants/auth.constants';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { SubSink } from 'subsink';
import { uaePassRefreshTokenApi } from 'src/app/scad-insights/user-onboarding/user-onboarding.constants';
import { accessToken, idToken } from '../../constants/msalConfig.constants';
import { DecodeJwtTokenService } from '../decode-jwt-token.service';
import { CookieService } from '../cookie.service';

@Injectable({
  providedIn: 'root'
})
export class UaePassService {

  private readonly isProd = environment.env === 'prod';
  private readonly baseUrl = this.isProd ? window.origin : 'https://bayaan-staging.scad.gov.ae';
  private readonly uaePassConfig = this.isProd ? uaePassProd : uaePassStaging;
  private refreshTokenTimeout!: ReturnType<typeof setTimeout>;
  private readonly apiBaseUrl = environment.baseUrl + environment.apiVersion;

  public uaePassAuthUrl = `${this.uaePassConfig.authBaseUrl}?redirect_uri=${this.baseUrl}/auth/upass/callback&client_id=${this.uaePassConfig.clientId}&response_type=code&scope=urn:uae:digitalid:profile:general`;
  public uaePassToken: string | null = null;
  public subs: SubSink = new SubSink();

  constructor(private readonly _http: HttpClient, private readonly _jwtTokenService: DecodeJwtTokenService, private readonly _cookie: CookieService) {}
  // , private _msalService: IFPMsalService

  navigateToUAEPass() {
    window.location.href = this.uaePassAuthUrl;
  }

  uaePassLogout(redirectUrl: string = '') {
    const redirectUri = redirectUrl !== '' ? this.baseUrl+redirectUrl : `${this.baseUrl}/login`;
    window.location.href = `${this.uaePassConfig.logoutBaseUrl}?redirect_uri=${redirectUri}`;
    this.stopRefreshTokenTimer();
    if (redirectUrl !== '/user-register') {
      this.clearAll();
    }
  }

  clearAll() {
    sessionStorage.clear();
    localStorage.clear();
    this.uaePassToken = null;
    this._cookie.deleteAllCookies();
  }

  get tokenExp() {
    if (this.uaePassToken && this.uaePassToken != 'undefined') {
      return + this._jwtTokenService.getTokenData(this.uaePassToken)?.exp * 1000;
    }
    return null;
  }

  get experTime(): number {
    if (this.tokenExp) {
      const expires = new Date(this.tokenExp);
      const timeout = expires.getTime() - Date.now() - (60 * 1000);
      return timeout;
    }
    return -1;
  }

  // used to start refersh tokern timmmer
  startRefreshTokenTimer() {
    localStorage.setItem(localStorageKeys.loginType, authTypes.uaePass);
    if (this.refreshTokenTimeout) {
      clearTimeout(this.refreshTokenTimeout);
    }
    this.refreshWorker();
  }

  stopRefreshTokenTimer() {
    if (this.refreshTokenTimeout) {
      clearTimeout(this.refreshTokenTimeout);
    }
  }

  refreshWorker() {
    if (this.experTime !== -1) {
      this.refereshTimeout();
    }
  }

  // used to refresh using normal timer
  refereshTimeout() {
    if (this.experTime !== -1) {
      this.refreshTokenTimeout = setTimeout(() => {
        this.callrefreshtoken();
      }, this.experTime);
    }
  }

  callrefreshtoken() {
    if (this.refreshTokenTimeout) {
      clearTimeout(this.refreshTokenTimeout);
    }
    this.acquireUaePassTokenSilent().then(() => {
      this.refereshTimeout();
    }).catch(() => {
      this.uaePassLogout();
    });
  }

  acquireUaePassTokenSilent() {
    return new Promise<UaePassRefreshTokens>((resolve, reject) => {
      const headers = new HttpHeaders({
        'x-upass-refresh-token': localStorage.getItem(accessToken) ?? ''
      });
      const api = this.apiBaseUrl+uaePassRefreshTokenApi;
      this.subs.add(
        this._http.get(api, { headers }).subscribe({
          next: (resp: any) => {
            this.uaePassToken = resp.apiToken;
            localStorage.setItem(idToken, this.uaePassToken ?? '');
            localStorage.setItem(accessToken, resp.refreshToken);
            resolve(resp);
          },
          error: (error) => {
            reject(new Error(error?.message));
          }
        })
      );
    });
  }
}

interface UaePassRefreshTokens {
  apiToken: string;
  refreshToken: string;
}
