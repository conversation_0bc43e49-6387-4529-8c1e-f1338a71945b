import { TranslateModule } from '@ngx-translate/core';
import { HttpClient, HttpHandler } from '@angular/common/http';

import { ComponentFixture, TestBed } from '@angular/core/testing';
import { SlaCertificateComponent } from './sla-certificate.component';
import { ActionsSubject, ReducerManager, ReducerManagerDispatcher, StateObservable, Store, StoreModule } from '@ngrx/store';
import { IFPMsalService } from 'src/app/scad-insights/core/services/IFP-msal.service';
import { appReducer } from 'src/app/scad-insights/store/app.state';



describe('slaComponent', () => {
  let component : SlaCertificateComponent;
  let fixture : ComponentFixture<SlaCertificateComponent>;


  beforeEach(async() => {
    await TestBed.configureTestingModule({
      imports: [
        SlaCertificateComponent,
        StoreModule.forRoot(appReducer),
        TranslateModule.forRoot({
          defaultLanguage: 'en'
        })
      ],
      providers: [HttpClient, HttpHandler, Store, StateObservable, ActionsSubject, ReducerManager, IFPMsalService, ReducerManagerDispatcher, StoreModule]
    }).compileComponents();

    fixture = TestBed.createComponent(SlaCertificateComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();

  });
  it('Check component', ()=>{
    expect(component).toBeDefined();
  });
});
