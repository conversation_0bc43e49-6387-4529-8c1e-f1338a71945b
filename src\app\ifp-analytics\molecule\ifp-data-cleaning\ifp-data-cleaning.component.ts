import { cloneDeep } from 'lodash';
import { ChangeDetectionStrategy, Component, EventEmitter, inject, Input, OnChang<PERSON>, OnD<PERSON>roy, Output, signal, WritableSignal } from '@angular/core';
import { IfpOptionBoxComponent } from '../ifp-option-box/ifp-option-box.component';
import { TranslateModule } from '@ngx-translate/core';
import { IfpCheckBoxComponent } from '../../../scad-insights/ifp-widgets/ifp-atoms/ifp-check-box/ifp-check-box.component';
import { IfpDropdownComponent } from '../../../scad-insights/ifp-widgets/ifp-atoms/ifp-dropdown/ifp-dropdown.component';
import { NgClass } from '@angular/common';
import { BoxOption } from '../../data-prep/ifp-data-prep/interface/ifp-data-prep.interface';
import { buttonClass } from 'src/app/scad-insights/core/constants/button.constants';
import { IfpSpinnerComponent } from '../../../scad-insights/ifp-widgets/ifp-molecules/ifp-spinner/ifp-spinner.component';
import { ColumnPrep } from '../../data-prep/ifp-data-prep/ifp-prep-select-data/interface/prep-selection.interface';
import { selectNodeStoreAdvance } from '../../data-prep/ifp-advance-prep/store/advance-node-store/advance-node.store';
import { FormControl, FormGroup, ReactiveFormsModule } from '@angular/forms';
import { IfpAdvancePrepService } from '../../data-prep/ifp-advance-prep/service/ifp-advance-prep.service';
import { ToasterService } from 'src/app/scad-insights/core/services/tooster/ToastrService.service';
import { connectionType } from '../../data-prep/ifp-data-prep/constants/if-preps.constants';
import { advanceMessage } from '../../data-prep/ifp-data-prep/ifp-data-prep.constant';
import { IfpDataToolAccordianComponent } from '../ifp-data-tool-accordian/ifp-data-tool-accordian.component';

@Component({
    selector: 'ifp-data-cleaning',
    templateUrl: './ifp-data-cleaning.component.html',
    styleUrl: './ifp-data-cleaning.component.scss',
    changeDetection: ChangeDetectionStrategy.OnPush,
    imports: [
        IfpOptionBoxComponent,
        TranslateModule,
        IfpCheckBoxComponent,
        IfpDropdownComponent,
        NgClass,
        IfpSpinnerComponent,
        ReactiveFormsModule,
        IfpDataToolAccordianComponent
    ]
})
export class IfpDataCleaningComponent implements OnChanges, OnDestroy {
  @Input() columnList !: ColumnPrep[];
  @Input() workFlowRunning = signal(false);
  @Input() emptyConnection = signal(true);
  @Input() currentNodeId?: string = '';
  @Input() currentConection: WritableSignal<boolean> = signal(false);
  @Input() sourceData: WritableSignal<boolean> = signal(false);
  @Input() isConfigUpdated: WritableSignal<boolean> = signal(false);
  @Input() errorMessageForTools?: string;

  @Output() cancel = new EventEmitter();
  @Output() save = new EventEmitter();

  public replaceList: string[] = ['NULL', 'EMPTY'];
  public replaceValueList: string[] = ['0', 'Blanks'];
  public uniqueValueList: { name: string, value: string }[] = [{ name: 'First occurrence', value: 'first_occurrence' }, { name: 'Last occurrence', value: 'last_occurrence' }];
  public selectedCols: string[] = [];
  public columnOptions: BoxOption[] = [];
  public buttonClass = buttonClass;
  public advanceMessage = advanceMessage;
  public enabledFeature: Record<string, boolean> = {
    replaceValue: false,
    entry: false,
    retainUniqueValue: false,
    removeDuplicates: false
  };

  public savedValue = false;
  public nullValueOptions: BoxOption[] = [
    {
      label: 'Remove columns with only NULL values',
      selected: false,
      name: 'all_null_columns'
    },
    {
      label: 'Remove rows with only NULL values',
      selected: false,
      name: 'all_null_rows'
    },
    {
      label: 'Remove columns with any NULL value',
      selected: false,
      name: 'any_null_columns'
    },
    {
      label: 'Remove rows with any NULL value',
      selected: false,
      name: 'any_null_rows'
    }
  ];

  public characterOptions: BoxOption[] = [
    {
      label: 'All Whitespace',
      selected: false,
      name: 'all_whitespaces'
    },
    {
      label: 'Leading and Trailing Whitespace',
      selected: false,
      name: 'leading_trailing_whitespaces'
    },
    {
      label: 'Tabs and Line-breaks',
      selected: false,
      name: 'tabs_linebreaks'
    },
    {
      label: 'Punctuations',
      selected: false,
      name: 'punctuations'
    },
    {
      label: 'Double Quotes',
      selected: false,
      name: 'double_quotes'
    }
  ];

  public replaceForm = new FormGroup({
    type: new FormControl<string>('NULL'),
    with: new FormControl<string>('0'),
    find_entry: new FormGroup({
      entry: new FormControl<string>(''),
      with: new FormControl<string>('')
    })
  });

  public removeDuplicateForm = new FormGroup({
    selectedColumns: new FormControl([]),
    uniqueValue: new FormControl({ name: 'First occurrence', value: 'first_occurence' })
  });

  public nodeData: Record<string, any> = {};


  public readonly advanceStore = inject(selectNodeStoreAdvance);

  constructor(public _prepService: IfpAdvancePrepService, public _toaster: ToasterService) {
    let isChange!: boolean;
    this.replaceForm.valueChanges.subscribe((_changes) => {
      this.isConfigUpdated.set(isChange);
    });
    this.removeDuplicateForm.valueChanges.subscribe((_changes) => {
      this.isConfigUpdated.set(isChange);
    });
  }


  ngOnChanges() {
    if (this.columnList?.length > 0) {
      this.setDefualtVariables();
      this.updateNode();
    }
  }

  setDefualtVariables() {
    this.enabledFeature = {
      replaceValue: false,
      entry: false,
      retainUniqueValue: false,
      removeDuplicates: false
    };
  }

  updateNode() {
    this.selectedCols =[];
    const nodeData = cloneDeep(this.advanceStore.selectNodeValue().nodeValue(this.currentNodeId ?? '').settings);
    this.nodeData = cloneDeep(nodeData);
    if (nodeData?.remove_characters) {
      this.characterOptions = cloneDeep(nodeData.remove_characters);
    } else {
      this.characterOptions.map(x => x.selected = false);
      this.characterOptions = cloneDeep(this.characterOptions);
    }
    if (nodeData?.remove_nulls) {
      this.nullValueOptions = cloneDeep(nodeData.remove_nulls);
    } else {
      this.nullValueOptions.map(x => x.selected = false);
      this.nullValueOptions = cloneDeep(this.nullValueOptions);
    }
    this.columnOptions = [];
    if (this.columnList?.length) {
      this.columnList.forEach((col: ColumnPrep, index: number) => {
        if (nodeData?.columns) {
          const value = nodeData.columns;
          const indexNode = value.findIndex((data: { label: string }) => data.label === col.name);
          this.columnOptions.push({ label: col.name, selected: indexNode != -1 ? value[indexNode].selected : true, id: index });
          if (this.columnOptions[this.columnOptions.length - 1].selected) {
            this.selectedCols.push(col.name);
          }
        } else {
          this.columnOptions.push({ label: col.name, selected: col.isSelected ?? true, id: index });
          if (this.columnOptions[this.columnOptions.length - 1].selected) {
            this.selectedCols.push(col.name);
          }
        }
      });
    }


    if (nodeData?.replace) {
      this.replaceForm = new FormGroup({
        type: new FormControl<string>(nodeData?.replace.type ?? 'NULL'),
        with: new FormControl<string>(nodeData?.replace.with ?? '0'),
        find_entry: new FormGroup({
          entry: new FormControl<string>(nodeData?.replace?.find_entry?.entry ?? ''),
          with: new FormControl<string>(nodeData?.replace?.find_entry?.with ?? '')
        })
      });
    } else {
      setTimeout(() => {
        this.replaceForm.reset();
      }, 300);

    }
    if (nodeData?.removeDuplicateForm) {
      this.removeDuplicateForm = new FormGroup({
        selectedColumns: new FormControl(nodeData?.removeDuplicateForm.selectedColumns),
        uniqueValue: new FormControl(nodeData?.removeDuplicateForm.uniqueValue)
      });
    } else {
      this.removeDuplicateForm.controls['selectedColumns'].setValue([]);
      this.removeDuplicateForm.controls['uniqueValue'].setValue({ name: '', value: '' });
    }

    // else {
    //   setTimeout(() => {
    //     this.removeDuplicateForm.reset();
    //   }, 300);

    // }
    if (nodeData?.enabledFeature) {
      this.enabledFeature = nodeData?.enabledFeature;
    } else {
      this.nodeData = {
        columns: cloneDeep(this.columnOptions),
        replace: cloneDeep(this.replaceForm.value),
        remove_nulls: cloneDeep(this.nullValueOptions),
        remove_characters: cloneDeep(this.characterOptions),
        enabledFeature: cloneDeep(this.enabledFeature),
        removeDuplicateForm: cloneDeep(this.removeDuplicateForm.value)

      };
    }
  }

  setReplaceValue(event: boolean) {
    this.enabledFeature['replaceValue'] = event;
  }

  setReplaceValueEntry(event: boolean) {
    this.enabledFeature['entry'] = event;
  }


  getSelectedColumns(selectedCols: BoxOption[]) {
    this.selectedCols = [];
    this.selectedCols = selectedCols.map((col: BoxOption) => col.label);
    const selectedData: never[] | null = [];
    this.removeDuplicateForm.controls.selectedColumns.value?.forEach(data => {
      if (this.selectedCols.includes(data)) {
        selectedData.push(data);
      }
    });
    this.removeDuplicateForm.controls.selectedColumns.setValue(selectedData);
  }

  onCancel() {
    this.cancel.emit();
  }



  onSave() {
    const config: CleanInterface = {
      columns: [],
      remove_nulls: {},
      remove_characters: {},
      replace: {}
    };
    this.columnOptions.forEach(data => {
      if (data.selected) {
        config.columns.push(data.label);
      }
    });
    if (this.enabledFeature['replaceValue']) {
      config.replace = {
        type: this.replaceForm.value.type ?? 'NULL',
        with: this.replaceForm.value.with == 'Blanks' ? '' : '0'
      };
    }
    if (this.enabledFeature['entry'] && config.replace) {
      config.replace['find_entry'] = {
        entry: this.replaceForm.value.find_entry?.entry !=null ? this.replaceForm.value.find_entry?.entry :'',
        with: this.replaceForm.value.find_entry?.with
      };
    }

    this.nullValueOptions.map(data => {
      return config.remove_nulls[data.name ?? ''] = data.selected;
    });

    this.characterOptions.map(data => {
      return config.remove_characters[data.name ?? ''] = data.selected;
    });
    const setting = {
      columns: this.columnOptions,
      replace: this.replaceForm.value,
      remove_nulls: this.nullValueOptions,
      remove_characters: this.characterOptions,
      enabledFeature: this.enabledFeature,
      removeDuplicateForm: this.removeDuplicateForm.value,
      configUpdated: true
    };
    if (this.enabledFeature['removeDuplicates']) {
      config['remove_duplicates'] = {
        columns: this.removeDuplicateForm.value.selectedColumns,
        type: this.removeDuplicateForm.value.uniqueValue?.value
      };
    }
    // this.savedValue = true;
    this.advanceStore.updateNode(this.currentNodeId ?? '', 'cleansingTool updated', connectionType.cleansingTool, config, 'cleansingTool updated', setting);
    this._prepService.nodeChangeDetect.next('node updated');
    // this._toaster.success('Data Saved Successfully');
    this.save.emit();
    this.updateNode();
  }

  ngOnDestroy(): void {
    if (!this._prepService.isConfigSave() && this.columnList?.length > 0) {
      this.onSave();
    }
  }
}
interface CleanInterface {
  columns: string[];
  replace?: Partial<{
    type: string | null;
    with: string | null;
    find_entry: Partial<{
      entry: string | null;
      with: string | null;
    }>;
  }>;
  remove_nulls: Record<string, boolean>;
  remove_characters: Record<string, boolean>;
  remove_duplicates?: Record<string, any>;
}

