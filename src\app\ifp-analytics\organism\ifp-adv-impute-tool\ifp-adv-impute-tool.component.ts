import { Component, EventEmitter, inject, Input, OnChanges, Output, signal, WritableSignal } from '@angular/core';
import { IfpOptionBoxComponent } from '../../molecule/ifp-option-box/ifp-option-box.component';
import { TranslateModule } from '@ngx-translate/core';
import { IfpCheckBoxComponent } from '../../../scad-insights/ifp-widgets/ifp-atoms/ifp-check-box/ifp-check-box.component';
import { IfpDataToolAccordianComponent } from '../../molecule/ifp-data-tool-accordian/ifp-data-tool-accordian.component';
import { IfpDbDropdownComponent } from '../../../dashboard-builder/molecule/ifp-db-dropdown/ifp-db-dropdown.component';
import { DbDropDown } from 'src/app/dashboard-builder/molecule/ifp-db-dropdown/ifp-db-dropdown.interface';
import { NgClass } from '@angular/common';
import { IfpButtonComponent } from '../../../scad-insights/ifp-widgets/ifp-atoms/ifp-button/ifp-button.component';
import { buttonClass } from 'src/app/scad-insights/core/constants/button.constants';
import { FormArray, FormBuilder, FormControl, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { ColumnPrep } from '../../data-prep/ifp-data-prep/ifp-prep-select-data/interface/prep-selection.interface';
import { CdkDropList, CdkDrag, CdkDropListGroup, CdkDragDrop, moveItemInArray, transferArrayItem } from '@angular/cdk/drag-drop';
import { selectNodeStoreAdvance } from '../../data-prep/ifp-advance-prep/store/advance-node-store/advance-node.store';
import { connectionType } from '../../data-prep/ifp-data-prep/constants/if-preps.constants';
import { IfpAdvancePrepService } from '../../data-prep/ifp-advance-prep/service/ifp-advance-prep.service';
import { ToasterService } from 'src/app/scad-insights/core/services/tooster/ToastrService.service';

@Component({
  selector: 'ifp-adv-impute-tool',
  standalone: true,
  templateUrl: './ifp-adv-impute-tool.component.html',
  styleUrl: './ifp-adv-impute-tool.component.scss',
  imports: [IfpOptionBoxComponent, TranslateModule, IfpCheckBoxComponent, IfpDataToolAccordianComponent, IfpDbDropdownComponent, NgClass, IfpButtonComponent, ReactiveFormsModule, CdkDropList, CdkDrag, CdkDropListGroup]
})
export class IfpAdvImputeToolComponent implements OnChanges {

  @Output() cancel = new EventEmitter();
  @Output() save = new EventEmitter();

  @Input({required: true}) columnList!: ColumnPrep[];
  @Input() dataTypes: DbDropDown[] = [];
  @Input() workFlowRunning = signal(false);
  @Input() emptyConnection = signal(true);
  @Input() currentNodeId: WritableSignal<string | null> = signal('');
  @Input() currentConection: WritableSignal<boolean> = signal(false);
  @Input() sourceData: WritableSignal<boolean> = signal(false);


  public isIncomingNull: boolean = true;
  public isPopulateEmptyCell: boolean = false;
  public isGenerateNewCol: boolean = false;
  public buttonClass = buttonClass;
  public columnListView: ColumnPrep[] = [];
  public outputColumnList: ColumnPrep[] = [];
  public isSaved: boolean = false;
  public isSaveClicked: boolean = false;
  public readonly advanceStore = inject(selectNodeStoreAdvance);
  public formdata!: FormArray;
  public imputeForm: FormGroup = new FormGroup({
    columns: new FormArray([])
  });

  public imputeOptions = [
    {
      label: 'Replace with Median',
      method: 'median'
    },
    {
      label: 'Replace with Min',
      method: 'min'
    },
    {
      label: 'Replace with Max',
      method: 'max'
    },
    {
      label: 'Replace with Mean',
      method: 'mean'
    },
    {
      label: 'Replace with Custom',
      method: 'custom'
    }
  ];

  constructor(private _formbuilder: FormBuilder, private _prepService: IfpAdvancePrepService, private _toaster: ToasterService) {}

  ngOnChanges() {
    if (!this.isSaved) {
      const nodeData = this.advanceStore.selectNodeValue().nodeValue(this.currentNodeId() ?? '');
      this.columnListView = nodeData?.settings?.inputCols ?? (this.columnList ? JSON.parse(JSON.stringify(this.columnList)) : []);
      if (nodeData?.settings?.outputCols) {
        const outputCols = nodeData.settings.outputCols;
        this.outputColumnList = [];
        this.outputColumnList = this.columnList.filter(
          (element: ColumnPrep) => outputCols.some((x: ColumnPrep) => x.name === element.name)
        );
        if (this.outputColumnList.length) {
          this.formdata = this.imputeForm.get('columns') as FormArray<FormGroup<ImputeFormData>>;
          this.formdata.clear();
          outputCols.forEach((item: ColumnPrep) => {
            const formElement = this.createFormElement(item);
            this.formdata.push(formElement);
          });
          this.imputeForm.setControl('columns', this.formdata);
        }
      }
    }
  }

  createFormElement(element: ColumnPrep) {
    return this._formbuilder.group({
      name: new FormControl(element.name),
      type: new FormControl(element.type ?? null),
      impute_options: new FormControl(element?.impute_options?.length ? element.impute_options : this.imputeOptions),
      method: new FormControl(element?.method ?? null),
      isNull: new FormControl(element?.user_specified ? element.user_specified.length === 0 : true),
      user_specified: new FormControl(element?.user_specified ?? null),
      customImputeValue: new FormControl(element?.customImputeValue ?? null),
      isFillCol: new FormControl(element?.column_name ? element.column_name.length === 0 : true),
      column_name: new FormControl(element?.column_name ?? null)
    });
  }

  get getControls() {
    return ((this.imputeForm.get('columns') as FormArray).controls) as FormGroup<ImputeFormData>[];
  }

  updateFormControl(index: number, patchValues: Record<string, any>, controlName: string, validator: Validators | null) {
    const myFormControl = this.formdata.at(index) as FormControl<FormGroup<ImputeFormData>>;
    this.formdata.at(index).patchValue(patchValues);
    const control = myFormControl.get(controlName);
    if (validator) {
      control?.addValidators([Validators.required]);
    } else {
      control?.removeValidators([Validators.required]);
    }
    myFormControl.updateValueAndValidity();
    this.imputeForm.setControl('columns', this.formdata);
  }

  valueToReplace(status: boolean, index: number) {
    const patchValues = {
      isNull: status,
      user_specified: status ? null : undefined
    };
    this.updateFormControl(index, patchValues, 'user_specified', status ? null : Validators.required);
  }

  onSetOutput(status: boolean, index: number) {
    const patchValues = {
      isFillCol: status,
      column_name: status ? undefined : null
    };
    this.updateFormControl(index, patchValues, 'column_name', status ? null : Validators.required);
  }

  onSelectMethod(method: DbDropDown, index: number) {
    const patchValues = method.method === 'custom' ? { method: method } : { method: method, customImputeValue: null };
    this.updateFormControl(index, patchValues, 'customImputeValue', method.method === 'custom' ? Validators.required : null);
  }

  onCancel() {
    this.cancel.emit();
  }

  drop(event: CdkDragDrop<ColumnPrep[]>, action: 'add' | 'remove') {
    if (event.previousContainer === event.container) {
      moveItemInArray(this.formdata.controls, event.previousIndex, event.currentIndex);
    } else {
      transferArrayItem(
        event.previousContainer.data,
        event.container.data,
        event.previousIndex,
        event.currentIndex
      );
      if (action === 'add') {
        const item = event.container.data[event.currentIndex];
        this.formdata = this.imputeForm.get('columns') as FormArray<FormGroup<ImputeFormData>>;
        this.formdata.push(this.createFormElement(item));
        const myFormControl = this.formdata.at(event.currentIndex) as FormControl<FormGroup<ImputeFormData>>;
        myFormControl.get('method')?.addValidators([Validators.required]);
        myFormControl.updateValueAndValidity();
        this.imputeForm.setControl('columns', this.formdata);
      } else {
        this.formdata.removeAt(event.previousIndex);
      }
    }
  }

  onSave() {
    this.isSaveClicked = true;
    if (this.imputeForm.valid) {
      const config: {columns: Record<string, any>} = {columns: {}};
      this.imputeForm.value.columns.forEach((element: any) => {
        config.columns[element.name] = {
          method: element.method.method,
          value: element.customImputeValue,
          user_specified: element.user_specified,
          column_name: element.column_name
        };
      });
      this.isSaved = true;
      this.advanceStore.updateNode(this.currentNodeId() ?? '', 'Impute Tool', connectionType.imputeTool, config, 'Impute tool updated', {inputCols: this.columnListView, outputCols: this.formdata.value, configUpdated: true});
      this._prepService.nodeChangeDetect.next('node updated');
      // this._toaster.success('Data Saved Successfully');
      this.save.emit();
    } else {
      this._toaster.error('Invalid Inputs');
    }
  }


}

interface ImputeFormData {
  name: FormControl;
  type: FormControl;
  impute_options: FormControl;
  method: FormControl;
  isNull: FormControl;
  user_specified: FormControl;
  customImputeValue: FormControl;
  isFillCol: FormControl;
  column_name: FormControl;
}
