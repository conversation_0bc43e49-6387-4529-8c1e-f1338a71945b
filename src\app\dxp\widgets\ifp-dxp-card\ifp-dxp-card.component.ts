import { Component, input, output } from '@angular/core';

@Component({
  selector: 'ifp-dxp-card',
  imports: [],
  templateUrl: './ifp-dxp-card.component.html',
  styleUrl: './ifp-dxp-card.component.scss'
})

export class IfpDxpCardComponent {
  public cardClick = output<string>();
  public image = input('../../../../assets/images/dxp-sample-image.jpg');
  public title = input('');
  public subTitle = input('');
  public id = input('');

  onCardClick() {
    this.cardClick.emit(this.id());
  }
}
