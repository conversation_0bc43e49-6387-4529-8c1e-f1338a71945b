import { Injectable } from '@angular/core';
import { AbstractControl, FormControl, ValidationErrors } from '@angular/forms';

@Injectable({
  providedIn: 'root'
})
export class ValidationService {

  noWhitespaceValidator(control: FormControl): ValidationErrors | null {
    const isWhitespace = (control.value || '').trim().length === 0;
    return isWhitespace ? { whitespace: true } : null;
  }

  phoneNumberValidator(control: FormControl, country: string = 'uae') {
    let phoneRegex:RegExp;
    if (country === 'uae') {
      // phoneRegex = /^(?:\+971|971|0)?(?:50|51|52|54|55|56|58)\d{7}$/; // with country code
      phoneRegex = /^(?:50|51|52|54|55|56|58)\d{7}$/;
    } else {
      phoneRegex = /^\d{9}$/;
    }

    if (!phoneRegex.test(control.value)) {
      return { phoneNumber: { valid: false } };
    }
    return null;
  }

  emiratesIdValidator(control: FormControl) {
    const eidRegex = /^784?\d{13}$/g;
    if (!eidRegex.test(control.value)) {
      return { eid: { valid: false } };
    }
    return null;
  }

  emailDomainValidator (allowedDomain: string) {
    return (control: AbstractControl): ValidationErrors | null => {
      if (!control.value) {
        return null; // Don't validate empty values
      }
      if (control.value.split('@')[1].toLowerCase() != allowedDomain.toLowerCase()) {
        return { domain: { valid: false } };
      }
      return null;
    };
  }

  emailMultipleDomainValidator (allowedDomain: string[]) {
    const domains = allowedDomain.map((domain: string) => domain.toLowerCase());
    return (control: AbstractControl): ValidationErrors | null => {
      if (!control.value) {
        return null; // Don't validate empty values
      }
      if (!domains.includes(control.value.split('@')[1].toLowerCase())) {
        return { domain: { valid: false } };
      }
      return null;
    };
  }

}
