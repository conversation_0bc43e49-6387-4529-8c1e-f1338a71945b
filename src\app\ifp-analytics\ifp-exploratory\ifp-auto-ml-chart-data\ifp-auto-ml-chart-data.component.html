<div  #chart class="ifp-chart-card" [ngClass]="{'ifp-chart-card--remove': removeBorderAndBackground, 'ifp-chart-card--selection' :  selection , 'ifp-chart-card--chart-list' : !chartListValue}">
  <div  class="ifp-chart-card__chart">
    @if (topLabel) {
      <div  class="ifp-chart-card__top-label">
        <span  class="ifp-chart-card__circle">
          <span  class="ifp-chart-card__inner-circle">

          </span>
        </span>
        <div  class="ifp-chart-card__label">
          {{topLabel | translate}}
        </div>
        @if (number) {
          <span class="ifp-chart-card__number">{{number}}</span>
        }

      </div>

    }

    <ifp-gauge-chart [height]="100" [width]="100"  [bottom]="!chartListValue" [uniqueColor]="chartColor" [uniqueHead]="chartHeading" [uniqueName]="chartValueName" [value]="chartValue <0 ? 0 : chartValue" [isShowText]="true"></ifp-gauge-chart>
    <div  class="ifp-chart-card__score"><span>{{'Score' | translate}} : <span class="ifp-chart-card__score-text">{{(chartValue *100) | number: '1.0-3'}} %</span></span>  <app-ifp-info [iconColor]="(_themeService.defaultTheme$ | async)==='dark' ? '#BEBEBE' : '#364151'" class="ifp-chart-card__info" [infoContent]="'Score is the weighted average of key objectives, scaled from 0% to 100%, reflecting overall model performance with a focus on generalizability over a single metric.' | translate"></app-ifp-info> </div>
  </div>
  @if (chartListValue) {
    <div class="ifp-chart-card__ac-measure">
      <!-- <div class="ifp-chart-card__ac-wrapper">
        <div class="ifp-chart-card__name">
          {{item.name}}
        </div>
        <div class="ifp-chart-card__value">
          {{item.value  | number: '1.0-4'}}
        </div>
      </div> -->
      @for (item of chartList; track $index) {
        <div class="ifp-chart-card__ac-wrapper">
          <div class="ifp-chart-card__name">
            {{item.name | translate}}
          </div>
          <div class="ifp-chart-card__value">
            @if(item.string ) {

              {{item.value.toString()| translate}}
            } @else {

              {{item.value  | number: '1.0-4'}}
            }
          </div>
        </div>

      }

    </div>
  }

</div>
