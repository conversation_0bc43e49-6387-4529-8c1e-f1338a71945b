@use "../../../../assets/ifp-styles/abstracts/index" as *;

.ifp-search-round {
  display: flex;
  justify-content: space-between;
  padding: $spacer-2;
  border: 1px solid $ifp-color-grey-3;
  background-color: $ifp-color-white;
  border-radius: 40px;
  &__input {
    padding: $spacer-0 $spacer-4;
    display: block;
    width: 100%;
    background-color: transparent;
    color: $ifp-color-black;
    @include placeholder($ifp-color-tertiary-text);
  }
  &__icon {
    width: 35px;
    height: 35px;
    min-width: 35px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: $ifp-color-green;
    color: $ifp-color-white-global;
    cursor: pointer;
    transition: 0.3s;
    opacity: 0.5;
    pointer-events: none;
    &--enable {
      opacity: 1;
      pointer-events: auto;
    }
  }
}
