import { role } from 'src/app/scad-insights/user-onboarding/control-panel/ifp-access-control/ifp-access-control.constants';
import { Injectable, signal, WritableSignal } from '@angular/core';
import { HttpService } from './http/http.service';
import { slaApis } from '../apiConstants/sla-api.constants';
import { Router } from '@angular/router';

@Injectable({
  providedIn: 'root'
})
export class NdaCheckService {
  public ndaStatus: WritableSignal<boolean> = signal(false);
  public role: WritableSignal<string> = signal(role.normalUser);

  constructor(private _http: HttpService, private _router:Router) {
  }

  isNdaAccepted() {
    return this._http.get(slaApis.checkNda).subscribe({
      next: (data) => {
        this.ndaStatus.set(data.isNdaAccepted);
        this.role.set(data.role);
        if (!data.isNdaAccepted) {
          this._router.navigate(['/accept-nda']);
        }
      }, error: () => {
        // temporary fix for unwanted api after fixing api 404 error we can remove this  this.ndaStatus.set(data.isNdaAccepted);
        this.ndaStatus.set(true);
      //   console.log(error?.error?.message);
      //   this._router.navigate(['/login']);
      }
    });
  }

  onNdaAccept(data: any) {
    return this._http.post(slaApis.acceptNda, data);
  }
}
