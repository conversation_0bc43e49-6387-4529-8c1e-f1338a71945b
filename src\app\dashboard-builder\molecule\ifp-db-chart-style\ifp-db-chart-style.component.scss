@use "../../../../assets/ifp-styles/abstracts/index" as *;
.ifp-chart-style {
  &__spacing-box {
    background-color: $ifp-color-violet-light;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
  }
  &__spacing {
    position: relative;
    text-align: center;
    .ifp-icon {
      background-color: $ifp-color-active-blue;
      color: $ifp-color-white-global;
      font-size: $ifp-fs-1;
      border-radius: 20px;
    }
    &--x {
      display: flex;
      align-items: center;
      justify-content: center;
      min-width: 24px;
      width: 24px;
      .ifp-icon {
        padding: $spacer-2 calc($spacer-1 / 2);
      }
    }
    &--y {
      text-align: center;
      width: 100%;
      height: 24px;
      line-height: 24px;
      .ifp-icon {
        padding: calc($spacer-1 / 2) $spacer-2;
      }
      &:first-child {
        .ifp-icon {
          margin-bottom: -10px;
        }
      }
    }
  }
  &__content-box {
    background-color: $ifp-color-white;
    border: 1px dashed $ifp-color-active-blue;
    width: calc(100% - 48px);
    min-height: 130px;
  }
  &__spacing-text {
    color: $ifp-color-blue-med;
    display: block;
  }

  &__props {
    margin-top: $spacer-4;
  }
  &__props-item {
    margin-bottom: $spacer-2;
    &:last-child {
      margin-bottom: $spacer-2;
    }
  }
  &__props-heading {
    color: $ifp-color-black;
    font-weight: $fw-semi-bold;
    margin-bottom: $spacer-3;
  }
}
