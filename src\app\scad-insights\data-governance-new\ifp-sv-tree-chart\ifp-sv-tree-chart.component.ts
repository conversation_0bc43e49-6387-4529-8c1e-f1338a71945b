import { TitleCasePipe } from '@angular/common';
import { Component, Input } from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';

@Component({
  selector: 'ifp-sv-tree-chart',
  standalone: true,
  imports: [TranslateModule, TitleCasePipe],
  templateUrl: './ifp-sv-tree-chart.component.html',
  styleUrl: './ifp-sv-tree-chart.component.scss'
})
export class IfpSvTreeChartComponent {

  @Input() data: any;

}
