import { Directive, ElementRef, EventEmitter, HostListener, Input, OnChanges, Output, SimpleChanges } from '@angular/core';
import { Router } from '@angular/router';
import { ListData } from 'src/app/scad-insights/ifp-widgets/ifp-molecules/ifp-list-box/ifp-list-box.component';
import { FormatterService } from '../services/formater/formatter.service';
import { appType } from '../constants/contentType.constants';
import { cloneDeep } from 'lodash';
import { HeaderService } from '../services/header/header.service';
import { ThemeService } from '../services/theme/theme.service';
import { environment } from 'src/environments/environment';
import { externalUrlsDarkDashboard, externalUrlsDashboard, externalUrlsDashboardAr } from '../constants/chart.constants';
import { TranslateService } from '@ngx-translate/core';
import { Location } from '@angular/common';
import { duplicateIndicators } from '../../shared/header/duplicateSearchResult.const';

@Directive({
  selector: '[appSearchSuggestion]',
  standalone: true
})
export class SearchSuggestionDirective implements OnChanges {
  @Input() suggestions!: any[];
  @Input() suggestionContentTypes: any = [];
  @Input() suggestionDomains: any = [];
  @Input() searchQuery!: string;
  @Input() resultLength!: number;
  @Input() isGlobalSearch: boolean = true;
  @Input() isFocusInput: boolean = false;

  @Output() setValue: EventEmitter<string> = new EventEmitter<string>();

  private suggestionList: HTMLElement | null = null;
  private suggestionListWrapper: HTMLElement | null = null;
  public externalUrls: any = externalUrlsDashboard;
  public externalUrlsDark: any = externalUrlsDarkDashboard;
  public externalUrlsAr: any = externalUrlsDashboardAr;
  public resultsDisplayed: number = 0;

  constructor(private elementRef: ElementRef, private _router: Router, private _formater: FormatterService,
    private headerService: HeaderService, private router: Router, private _themeService: ThemeService, private _translateService: TranslateService,
    public location: Location) { }

  @HostListener('input')
  onInput(): void {
    this.showSuggestions();
  }

  @HostListener('document:click', ['$event.target'])
  onClickOutside(targetElement: HTMLElement): void {
    const clickedInside = this.elementRef.nativeElement.contains(targetElement);
    if (!clickedInside) {
      this.hideSuggestions();
    }
  }

  ngOnChanges(_changes: SimpleChanges) {
    this.resultsDisplayed = 0;
    this.showSuggestions();
  }

  private showSuggestions(): void {
    this.hideSuggestions();
    if (!this.suggestionList) {
      this.suggestionList = document.createElement('div');
      this.suggestionList.classList.add('ifp-header__search-list');
      if (!this.isGlobalSearch) {
        this.suggestionList.classList.add('ifp-suggestion--general');
      }
      this.suggestionListWrapper = document.createElement('div');
      this.suggestionListWrapper.classList.add('ifp-suggestion__wrapper');
      this.suggestionList?.appendChild(this.suggestionListWrapper);
      this.elementRef.nativeElement.parentElement.appendChild(this.suggestionList);
    }
    if (this.suggestions?.length > 0 && this.searchQuery !== '') {
      this.suggestionList.classList.add('ifp-header__search-list--active');
    }

    if (this.isGlobalSearch) {
      this.suggestionContentTypes?.forEach((element: {
        isSelected: boolean; contentType: string | null, machineName: string | null, items: any[]
      }) => {
        const suggestionType = document.createElement('div');
        suggestionType.classList.add('ifp-suggestion__inner');
        const cntTypeHeader = document.createElement('p');
        cntTypeHeader.classList.add('ifp-suggestion__type');
        cntTypeHeader.textContent = element?.contentType;
        const machineName = element.machineName?.toLowerCase();
        cntTypeHeader.addEventListener('click', () => {
          element = cloneDeep(element);
          element.isSelected = true;
          this.headerService.selectedCatogory = element;
          this.headerService.filterSearch$.next(true);
          if (machineName === 'glossary') {
            this.router.navigate(['/glossary'], { queryParams: { query: this.searchQuery } });
          } else {
            this.router.navigate(['/search']);
          }
        });
        if (element.items.length) {
          suggestionType.appendChild(cntTypeHeader);
          for (let j = 0; j < element.items.length; j++) {
            if (j < (10 / (this.suggestionContentTypes.length))) {
              const suggestionItem = document.createElement('p');
              suggestionItem.classList.add('ifp-suggestion__item');
              const title = element?.items[j]?.title?.toLocaleLowerCase();
              const filterValueLength =  duplicateIndicators.findIndex((dataValue: string) => {
                return dataValue.toLocaleLowerCase() === title;
              } );
              if ( filterValueLength !== -1) {
                suggestionItem.textContent = `${element.items[j].title} - ${element?.items[j].topic?.name}`;
              } else {
                suggestionItem.textContent = element.items[j].title;
              }
              const item = element.items[j];
              suggestionItem.addEventListener('click', () => {
                if (machineName === 'glossary') {
                  this.router.navigate(['/glossary'], { queryParams: { query: suggestionItem.textContent } });
                } else {
                  this.goTODetail(item);
                }
                this.hideSuggestions();
              });
              suggestionType.appendChild(suggestionItem);
              this.resultsDisplayed++;
            }
          }
        }
        this.suggestionListWrapper?.appendChild(suggestionType);
      });

      if (this.resultLength > this.resultsDisplayed ) {
        this.resultsDisplayed = 0;
        const viewMoreContainer = document.createElement('div');
        viewMoreContainer.classList.add('ifp-suggestion__view-more');
        const viewMore = document.createElement('span');
        viewMore.classList.add('ifp-link');
        viewMore.innerHTML = `${this._translateService.instant('View All')} <em class="ifp-icon ifp-icon-right-arrow"></em>`;
        viewMore.addEventListener('click', () => {
          this.router.navigate(['/search']);
          setTimeout(() => {
            this.updateQueryParams(this.searchQuery);
          }, 1000);
        });
        viewMoreContainer.appendChild(viewMore);
        this.suggestionList.appendChild(viewMoreContainer);
      }
    } else if (this.suggestions?.length) {
      const filteredList = this.suggestions.filter((item: string) => item.toLowerCase().startsWith(this.searchQuery?.toLowerCase())).sort((a, b) => a.localeCompare(b));
      if (this.isFocusInput) {
        this.suggestionList.classList.add('ifp-header__search-list--active');
      }
      if (filteredList.length) {
        this.getSuggestions(filteredList);
      } else {
        this.hideSuggestions();
      }
    }

    // DOMAINS HIDDEN FOR NOW. DON'T REMOVE!!!
    /*
    const suggestionFilter = document.createElement('div');
    suggestionFilter.classList.add('ifp-suggestion');
    this.suggestionList?.appendChild(suggestionFilter);
    const cntDomainTypeHeader = document.createElement('p');
    cntDomainTypeHeader.classList.add('ifp-suggestion__header');
    cntDomainTypeHeader.textContent = this._translate.instant('Domain');
    suggestionFilter.appendChild(cntDomainTypeHeader);
    const suggestionDomainFilter = document.createElement('div');
    suggestionDomainFilter.classList.add('ifp-suggestion__outer');
    suggestionFilter.appendChild(suggestionDomainFilter);

    for (const property in this.suggestionDomains) {
      const suggestionContentType = document.createElement('div');
      suggestionContentType.classList.add('ifp-suggestion__tag');
      suggestionContentType.classList.add('ifp-btn');
      suggestionContentType.classList.add('ifp-btn--tertiary');
      suggestionContentType.classList.add('ifp-btn--white');
      suggestionContentType.textContent = property;
      suggestionContentType.addEventListener('click', () => {
        this.headerService.selectedDomain = property;
        this.headerService.filterSearchDomain$.next(true);
        if(this.suggestionDomains[property][0]?.type === 'glossary') {
          this.router.navigate(['/glossary'], {queryParams: {filterBy: property}});
        } else {
          this._router.navigate(['/search']);
        }
      });
      suggestionDomainFilter?.appendChild(suggestionContentType);
    }
    */

  }

  getSuggestions(list: string[]) {
    const suggestionType = document.createElement('div');
    for (const element of list) {
      const suggestionItem = document.createElement('p');
      suggestionItem.classList.add('ifp-suggestion__item');
      const item = element;
      suggestionItem.textContent = item;
      suggestionItem.addEventListener('click', () => {
        this.selectSuggestion(item);
      });
      suggestionType.appendChild(suggestionItem);
    }
    this.suggestionListWrapper?.appendChild(suggestionType);
  }

  updateQueryParams(searchData: string) {
    const currentUrl = this.location.path();
    const urlTree = this.router.parseUrl(currentUrl);
    const queryParams = { searchData: searchData };
    urlTree.queryParams = queryParams;
    this.location.replaceState(urlTree.toString());
  }



  public hideSuggestions(): void {
    if (this.suggestionList) {
      this.suggestionList.remove();
      this.suggestionList = null;
    }
  }

  private selectSuggestion(suggestion: string): void {
    this.elementRef.nativeElement.value = suggestion;
    this.setValue.emit(suggestion);
  }


  goTODetail(item: ListData) {
    const type = item?.appType ? item?.appType : item?.type;
    const isExternal = type === 'basket_insights' || type === 'eci_insights';
    if (item?.appType == appType.liveability) {
      this.router.navigate(['/liveability-dashboard/', item.id]);
    }
    if (type === 'publications') {
      let topicName = item.topic?.name;
      if (topicName) {
        topicName = topicName.replace(/ /g, '-').toLowerCase();
      }
      const topicId = item.id ? (`${item.id}`).replace(/"/g, '') : '';
      const queryParams = {
        tabName: 'Reports', domainId: topicId, key: 'reports'
      };
      this.router.navigate([`/domain-exploration/${topicName}/${topicId}`], { queryParams: queryParams });
    }
    if (isExternal) {
      let externalUrl = type ? `${environment.baseUrl}/${this._themeService.defaultTheme === 'light' ? this.externalUrls[type] : this.externalUrlsDark[type]}` : '';
      if (type === 'eci_insights') {
        externalUrl = externalUrl + (localStorage.getItem('lang') ? localStorage.getItem('lang') : 'en');
      }
      window.open(externalUrl, '_self');
      return;
    }
    const isVisa = (type === appType.officialInsights.type) || (type === appType.innovativeInsights.type);
    if (item?.type && item.id) {
      const newType = type ? type.replace(/-/g, '_').toLowerCase() : undefined;
      const indicatorUrl = this._formater.setIndicatorUrl(newType, item.id);
      if (isVisa || item?.type === appType.scad_official_indicator.name) {
        const queryParams = isVisa ? {
          visa: true, contentType: type, domainId: item.id, visaView: 'innovative'
        } : { visa: false, contentType: type, domainId: item.id };
        this._router.navigate([indicatorUrl], { queryParams: queryParams });
      } else if (type === 'glossary') {
        this._router.navigate(['/glossary'], { queryParams: { query: item.title } });
      } else {
        this._router.navigate([indicatorUrl]);
      }
    }
  }
}
