import { Injectable } from '@angular/core';
import { myApps } from '../../apiConstants/myapps-api.constants';
import { HttpService } from '../http/http.service';
import { AddMyApps, RemoveDashboardMyApps } from '../../interface/myapps.interface';
import { Store } from '@ngrx/store';
import { getStatisticsInsights } from 'src/app/scad-insights/store/statistics-insights/statistics-insights-list.action';
import { tap } from 'rxjs';
import { classifications } from '../../constants/domain.constants';
import { ShareAppsData } from 'src/app/scad-insights/ifp-widgets/ifp-organism/ifp-share-modal/ifp-share-modal.interface';

@Injectable({
  providedIn: 'root'
})
export class MyService {

  constructor( private _http: HttpService, private store: Store) { }

  getMyApps() {
    return this._http.get( myApps.myAppsLandingPage).pipe(tap( (data: any) => {
      const classification = data.classificationNodes ? data?.classificationNodes : {};
      for (const key in classification) {
        if (Object.prototype.hasOwnProperty.call(classification, key)) {
          if (key !== classifications.analyticalApps && key !== classifications.reports && key !== classifications.compareStatistics  && key !== classifications.innovativeInsights && key !==  classifications.officialInsights){
            const ids = classification[key];
            this.store.dispatch(getStatisticsInsights({id: ids, name: key}));
          }
        }
      }
    }));
  }

  getDashboardList() {
    return this._http.get(myApps.NewMyAppsApiDashboard);
  }

  getCurrentDashboard(id: string) {
    return this._http.get(myApps.getDashboardData+ id);
  }

  getCurrentDashboardDelete(id: string) {
    return this._http.post(`${myApps.deleteDashboard+ id}`);
  }


  createDashboard(data: any) {
    return this._http.post(myApps.createDashboard, data);
  }

  editDashboard(id:string, data: any) {
    return this._http.post(myApps.editDashboard+ id, data);
  }

  getMyAppsAll() {
    return this._http.get( myApps.mappedList);
  }

  unsubscribeMyAppsMapped(data: AddMyApps) {
    return this._http.post( myApps.addAndList, data);
  }

  removeFromDashboard(data: RemoveDashboardMyApps, id: string) {
    return this._http.post( myApps.deleteDashboardNodes + id +myApps.subDeleteDashboardNodes, data);
  }

  setMyAppsMapped(data: AddMyApps) {
    return this._http.post( myApps.addAndList, data);
  }

  submit(data: AddMyApps) {
    return this._http.post( myApps.addAndList, data);
  }

  draft(data: AddMyApps) {
    return this._http.post( myApps.draftApi, data);
  }

  draftList() {
    return this._http.get( myApps.draftListApi);
  }

  draftRemove(id: string) {
    return this._http.post( myApps.draftListRemove+ id);
  }

  draftNodeList(id: string) {
    return this._http.get(myApps.draftNodeList+id);
  }

  myAppsResize(id: string) {
    const nodeId = {
      nodeId: id
    };
    return this._http.post(myApps.myAppsResize, nodeId);
  }

  saveMyApps(data: any) {
    return this._http.post(myApps.saveMyApps, data);
  }

  saveReset() {
    return this._http.post(myApps.resetMyApps);
  }

  shareApps(data: ShareAppsData) {
    return this._http.post(myApps.shareApps, data);
  }

  getRecievedApp() {
    return this._http.get(myApps.recievedApps);
  }

  getSentApp() {
    return this._http.get(myApps.sentApps);
  }

  viewSharedApp(token: string) {
    return this._http.get(`${myApps.viewShared}?token=${token}`);
  }

  deleteSharedApp(type:string, id:string){
    return this._http.delete(`${myApps.deleteSharedApp}/${type}/${id}`);
  }

  readApp(id:string){
    return this._http.post(`${myApps.readApp}/${id}`);
  }

}
