import { Component } from '@angular/core';
import * as Highcharts from 'highcharts';
import HighchartsMore from 'highcharts/highcharts-more';
import HighchartsExporting from 'highcharts/modules/exporting';
import HighchartsData from 'highcharts/modules/data';

HighchartsMore(Highcharts);
HighchartsExporting(Highcharts);
HighchartsData(Highcharts);

@Component({
  selector: 'app-geo-areaspline',
  templateUrl: './geo-areaspline.component.html',
  styleUrls: ['./geo-areaspline.component.scss'],
  standalone: true,
})
export class GeoAreasplineComponent {

  ngOnInit() {
    this.createChart();
  }

  createChart() {
    const chartOptions: Highcharts.Options = {
      chart: {
        type: 'areaspline',
        renderTo: 'areaSpline',
        // height: '40%'
      },
      credits: {
        enabled: false
      },
      exporting: {
        enabled: false
      },
      legend: {
        layout: 'horizontal',
        align: 'left',
        verticalAlign: 'top'
      },
      title: {
        text: ''
      },
      xAxis: {
        categories: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul'],
      },
      yAxis: {
        title: {
          text: ''
        },
        startOnTick: false,
        endOnTick:false
      },
      plotOptions: {
        areaspline: {
          pointStart: 0,
          lineWidth: 2,
          fillOpacity: 0.3,
          marker: {
            enabled: true,
            symbol: 'circle', // Marker symbol
            states: {
              hover: {
                enabled: true // Enable marker hover effect
              }
            }
          },
        },
      },
      series: [
        // @ts-ignore
        {
          pointPlacement: 'on',
          marker: {
            // @ts-ignore
            enabled: false
          },
          name: 'Temporary',
          // @ts-ignore
          data: [20, 30, 40, 35, 25, 45, 50],
        },
        // @ts-ignore
        {
          pointPlacement: 'on',
          marker: {
            enabled: false
          },
          name: 'Permanent',
          // @ts-ignore
          data: [10, 20, 30, 25, 15, 35, 40]
        },
        // @ts-ignore
        {
          pointPlacement: 'on',
          marker: {
            enabled: false
          },
          name: 'Other',
          // @ts-ignore
          data: [5, 10, 20, 15, 5, 25, 30]
        }
      ]
    };

    //@ts-ignore
    // Highcharts.theme = {
    //   colors: ['#90CAF9', '#64B5F6', '#42A5F5', '#2196F3', '#1E88E5', '#1976D2', '#1565C0', '#0D47A1'], // Update with your desired colors
    //   chart: {
    //     backgroundColor: '#212121', // Update with your desired background color
    //     style: {
    //       fontFamily: '\'Roboto\', sans-serif' // Update with your desired font
    //     }
    //   },
    //   legend: {
    //     itemStyle: {
    //       color: '#FFFFFF' // Update with your desired legend text color
    //     }
    //   },
    //   xAxis: {
    //     gridLineColor: '#757575', // Update with your desired grid line color
    //     labels: {
    //       style: {
    //         color: '#FFFFFF' // Update with your desired label color
    //       }
    //     }
    //   },
    //   yAxis: {
    //     gridLineColor: '#757575', // Update with your desired grid line color
    //     labels: {
    //       style: {
    //         color: '#FFFFFF' // Update with your desired label color
    //       }
    //     }
    //   },
    //   plotOptions: {
    //     series: {
    //       borderColor: '#FFFFFF' // Update with your desired series border color
    //     }
    //   }
    // };

    // Highcharts.setOptions(Highcharts.theme);
    Highcharts.chart(chartOptions);
  }

}
