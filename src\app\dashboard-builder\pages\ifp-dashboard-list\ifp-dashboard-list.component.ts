import { Async<PERSON>ip<PERSON>, DatePipe, Location } from '@angular/common';
import { AfterViewInit, Component, inject, <PERSON><PERSON><PERSON><PERSON>, OnInit, ViewChild } from '@angular/core';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { IfpButtonComponent } from 'src/app/scad-insights/ifp-widgets/ifp-atoms/ifp-button/ifp-button.component';
import { buttonClass, buttonIconPosition } from 'src/app/scad-insights/core/constants/button.constants';
import { IfpDbListCardComponent } from '../../molecule/ifp-db-list-card/ifp-db-list-card.component';
import { DashboardService } from 'src/app/scad-insights/core/services/create-dashboard/dashboard.service';
import { DashboadListItem } from '../dashboard.interface';
import { ActivatedRoute, Params, Router } from '@angular/router';
import { PaginationComponent } from '../../../scad-insights/ifp-widgets/ifp-molecules/pagination/pagination.component';
import { Subject, debounceTime } from 'rxjs';
import { SubSink } from 'subsink';
import { IfpCardLoaderComponent } from '../../../scad-insights/ifp-widgets/ifp-atoms/ifp-card-loader/ifp-card-loader.component';
import { IfpNoDataComponent } from '../../../scad-insights/ifp-widgets/ifp-molecules/ifp-no-data/ifp-no-data.component';
import { ToasterService } from 'src/app/scad-insights/core/services/tooster/ToastrService.service';
import { IfpModalComponent } from '../../../scad-insights/ifp-widgets/ifp-organism/ifp-modal/ifp-modal.component';
import { IfpRemoveCardComponent } from '../../../scad-insights/ifp-widgets/ifp-atoms/ifp-remove-card/ifp-remove-card.component';
import { ThemeService } from 'src/app/scad-insights/core/services/theme/theme.service';
import { IfpShareModalComponent } from '../../../scad-insights/ifp-widgets/ifp-organism/ifp-share-modal/ifp-share-modal.component';
import { ShareAppsData, ShareResponse } from 'src/app/scad-insights/ifp-widgets/ifp-organism/ifp-share-modal/ifp-share-modal.interface';
import { LabelData } from 'src/app/scad-insights/core/interface/atom/ifp-category-label.interface';
import { dbdlistTabs } from '../../organism/ifp-chart-toolbar/ifp-chart-toolbar.constants';
import { TabChangeEvent } from '../../organism/ifp-chart-toolbar/ifp-chart-toolbar.interface';
import { UsageDashboardLogService } from 'src/app/scad-insights/core/services/usage-dashboard-log/usage-dashboard-log.service';
import { logType } from 'src/app/scad-insights/core/services/usage-dashboard-log/usage-dashboard.constants';
import { IfpHorizontalTabComponent } from "../../../scad-insights/ifp-widgets/ifp-molecules/ifp-horizontal-tab/ifp-horizontal-tab.component";
import { IfpSearchComponent } from "../../../scad-insights/ifp-widgets/ifp-atoms/ifp-search/ifp-search.component";
import { IfpDbTemplateListComponent } from "../ifp-db-template-list/ifp-db-template-list.component";

@Component({
  selector: 'ifp-dashboard-list',
  templateUrl: './ifp-dashboard-list.component.html',
  styleUrl: './ifp-dashboard-list.component.scss',
  imports: [TranslateModule, IfpButtonComponent, IfpDbListCardComponent, DatePipe, PaginationComponent,
    IfpCardLoaderComponent, IfpNoDataComponent, IfpModalComponent, IfpRemoveCardComponent, IfpShareModalComponent, IfpHorizontalTabComponent, IfpSearchComponent, IfpDbTemplateListComponent,
    AsyncPipe]
})
export class IfpDashboardListComponent implements OnInit, AfterViewInit, OnDestroy {

  @ViewChild('modal') modal!: IfpModalComponent;
  @ViewChild('shareAppsModal') shareAppsModal!: IfpModalComponent;
  @ViewChild('templateListModal') templateListModal!: IfpModalComponent;

  public buttonClass = buttonClass;
  public buttonIconPosition = buttonIconPosition;

  public isFilterApplied: boolean = false;
  public totalCount: number = 0;
  public dashboardList: DashboadListItem[] = [];
  public search = '';
  public offsetPage = 1;
  public offset: number = 0;
  public limit: number = 10;
  public subs = new SubSink();
  private searchInput = new Subject<string>();
  public searchKeyword!: string;
  public isLoading: boolean = false;
  public currentRemoveText!: string;
  private deleteId!: string;
  public recentlyAddedEnabled: string = 'desc';
  public selectedDashboards: string[] = [];
  public dashboardTabs: LabelData[] = dbdlistTabs;
  public openTemplateListModal: boolean = false;
  public selectedTabView: { event: { name: string }; index: number } = {
    event: {
      name: dbdlistTabs[0].name
    },
    index: 0
  };

  private sessionId!: string;
  private readonly _router: ActivatedRoute = inject(ActivatedRoute);

  constructor(public location: Location, private _dashboardService: DashboardService, private router: Router, private _toaster: ToasterService,
    private _translate: TranslateService, public _themeService: ThemeService, private _tooter: ToasterService, private log: UsageDashboardLogService) {
    localStorage.removeItem(this._dashboardService.selectedCards);
    this.subs.add(
      this.searchInput
        .pipe(debounceTime(800))
        .subscribe((value) => {
          this.searchKeyword = value;
          this.offsetPage = 1;
          this.getDashboards();
        }),
      // this._router.queryParams.subscribe((value: Params) => {
      //   this.openTemplateListModal = value['create'] && value['create'] === 'true';
      // })
    );
  }


  onSearch(keyword: string) {
    // if (!keyword) {
    //   return;
    // }
    this.searchKeyword = keyword;
    this.searchInput.next(keyword);
  }


  ngOnInit(): void {
    this.getDashboards();
    if (this.sessionId) {
      this.log.logEnds(this.sessionId, this.log.currentTime);
    }
    this.sessionId = this.log.createUUid;
    this.log.logStart(this.sessionId, logType.dashboard, this.log.currentTime);

  }

  ngAfterViewInit(): void {
    if (this.openTemplateListModal) {
      this.templateListModal.createElement();
    }
  }

  onApplyFilter() {
    this.isFilterApplied = !this.isFilterApplied;
    // filter code
    if (this.sessionId) {
      this.log.logEnds(this.sessionId, this.log.currentTime);
    }
    this.sessionId = this.log.createUUid;
    this.log.logStart(this.sessionId, logType.dashboard, this.log.currentTime);
  }

  getDashboards() {
    this.isLoading = true;
    this._dashboardService.getDashboards(this.offsetPage, this.limit, this.searchKeyword, this.selectedTabView.event.name, this.recentlyAddedEnabled).subscribe({
      next: resp => {
        this.dashboardList = resp.data;
        if (!this.recentlyAddedEnabled) {
          this.dashboardList = this.dashboardList.reverse();
        }
        this.totalCount = resp.totalCount;
        this.isLoading = false;
      },
      error: _error => {
        this.isLoading = false;
      }
    });
  }

  goToDashboardBuilder() {
    this.router.navigate(['store/dashboard-builder']);
    // this.templateListModal.createElement();
  }

  onPageChange(event: any) {
    this.offset = event + 1;
    this.offsetPage = (event / this.limit) + 1;
    this.getDashboards();

  }

  limitChanged(event: any) {
    this.offsetPage = 1;
    this.offset = 1;
    this.limit = event;
    // console.log("limit", this.limit);
  }

  openDetail(id: string) {
    const list: any = {};
    const dataIndex = this.dashboardList.findIndex(x => x.id == id);
    if (this.selectedTabView.event.name === 'Sent') {
      list.key = this._translate.instant('Recipients');
      list.value = this.dashboardList[dataIndex].recepientEmails?.toString();
    } else if (this.selectedTabView.event.name === 'Received') {
      list.key = this._translate.instant('Received from');
      list.value = this.dashboardList[dataIndex].shareEmail;
    }
    this.router.navigate(['store/dashboard-builder'], { queryParams: { id: id, tab: this.selectedTabView.event.name, sendData: JSON.stringify(list) } });
  }

  editOrDelete(event: any, data: any) {
    if (event == 'edit') {
      this.editDashboard(data.id);
    }
    if (event == 'delete') {
      this.opendeleteModel(data);
    }
  }

  editDashboard(id: string) {
    this.router.navigate(['store/dashboard-builder'], { queryParams: { id: id, mode: 'edit', tab: this.selectedTabView.event.name } });
  }

  deleteDashboard() {
    this._dashboardService.deleteDashboard(this.deleteId, this.selectedTabView.event.name).subscribe(_resp => {
      this._toaster.success('Dashboard deleted Successfully');
      this.getDashboards();
    });
  }

  recentlyAdded() {
    if (this.recentlyAddedEnabled == 'desc') {
      this.recentlyAddedEnabled = 'asc';
    } else {
      this.recentlyAddedEnabled = 'desc';
    }
    this.getDashboards();
  }

  goToStore() {
    this.router.navigate(['store']);
  }

  opendeleteModel(data: any) {
    this.deleteId = data.id;
    this.currentRemoveText = `${this._translate.instant('Do you want to remove the')} ${data.name}? `;
    this.modal.createElement();
  }

  closeModel(event: any) {
    if (event) {
      this.deleteDashboard();
    }
    this.modal.removeModal();
  }


  // ####### for share functions start ####### //

  updateSelectDashboard(event: { id: string, checked: boolean }) {
    if (event.checked) {
      this.selectedDashboards.push(event.id);
    } else {
      this.selectedDashboards = this.selectedDashboards.filter(x => x !== event.id);
    }
  }

  shareSelected() {
    this.shareAppsModal.createElement();
  }

  closeShare() {
    this.shareAppsModal.removeModal();
  }



  onShareApps(data: ShareAppsData) {
    data.dashboards = this.selectedDashboards;
    data.shareNodes = undefined;
    this.subs.add(
      this._dashboardService.shareApps(data).subscribe((resp: ShareResponse) => {
        if (resp) {
          this._tooter.success(resp.message);
          this.selectedDashboards = [];
        }
      })
    );
  }

  changeTabView(event: TabChangeEvent) {
    if (event) {
      this.selectedTabView = event;
      this.resetPages();
    }
  }


  resetPages() {
    this.offsetPage = 1;
    this.offset = 1;
    this.searchKeyword = '';
    this.dashboardList = [];
    this.getDashboards();
  }

  closeTemplateListModal() {
    this.templateListModal.removeModal();
    this.router.navigateByUrl('/store/dashboards');
  }

  ngOnDestroy(): void {
    if (this.sessionId) {
      this.log.logEnds(this.sessionId, this.log.currentTime);
    }
  }
}


