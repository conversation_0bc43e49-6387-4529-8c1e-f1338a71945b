@use "../../../../assets/ifp-styles/abstracts/index" as *;

:host {
  display: block;
}

.ifp-templ-list {
  background-color: $ifp-color-white;
  border-radius: 10px;

  &__text,
  &__file-icon {
    color: $ifp-color-tertiary-text;
  }

  &__text {
    font-size: $ifp-fs-5;
  }

  &__file-icon {
    margin-bottom: $spacer-3;
  }

  &__db-card {
    text-align: center;
    width: calc(25% - (2 * $spacer-2));
    margin: $spacer-0 $spacer-2;
    cursor: pointer;
    border: 2px solid $ifp-color-grey-bg;
    border-radius: 15px;
    overflow: hidden;
    transition: 0.3s;
    .ifp-icon {
      display: block;
      font-size: 5rem;
    }

    &--blank {
      display: flex;
      align-items: center;
      justify-content: center;
      border-width: 1px;
      background-color: rgba(231, 242, 255, 0.3);
    }
    &--active {
      border-color: $ifp-color-blue-menu;
      .ifp-templ-list {
        &__file-icon,
        &__db-name,
        &__text {
          color: $ifp-color-blue-menu;
        }
      }
    }
    &:hover {
      box-shadow: 0 0 16px $ifp-color-black-16;
    }
  }

  &__body-wrapper,
  &__header,
  &__footer {
    padding: $spacer-4;
  }

  &__body-wrapper {
    border-bottom: 1px solid $ifp-color-grey-3;
    min-height: 50vh;
  }

  &__card-wrapper {
    display: flex;
    flex-wrap: wrap;
    margin: $spacer-0 (-$spacer-2);
  }

  &__header {
    border-bottom: 1px solid $ifp-color-grey-3;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .ifp-icon {
      cursor: pointer;
    }
  }

  &__title {
    font-size: $ifp-fs-6;
    font-weight: $fw-bold;
  }

  &__card-body {
    padding: ($spacer-2 + 2);
    min-height: 200px;
    background-color: $ifp-color-grey-bg;
  }

  &__card-footer {
    padding: $spacer-3;
    text-align: left;
  }

  &__db-name {
    font-size: $ifp-fs-5;
    font-weight: $fw-bold;
  }

  &__lits-title {
    font-size: $ifp-fs-4;
    font-weight: $fw-semi-bold;
    color: $ifp-color-grey-14;
    margin-bottom: $spacer-3;
  }

  &__footer {
    display: flex;
    justify-content: flex-end;
  }

  &__btn {
    margin: $spacer-0 $spacer-2;
  }

}
