@use "../../../../assets/ifp-styles/abstracts/index" as *;
:host{
  display: block;
}
.ifp-exp-pre {
  background-color: $ifp-color-section-white;
  border-radius: 10px;
  overflow: hidden;
  height: 100%;
  padding: $spacer-2;
  &__heading {
    font-size: $ifp-fs-7;
  }
  &__heading-wrapper {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: $spacer-4;
  }
  &__loader {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 400px;
  }
&__heading-sub {
  font-size: $ifp-fs-7;
  font-weight: $fw-bold;
}
&__btn-analyze {
  margin-inline-end: $spacer-3;
}
&__btn-wrapper {
  display: flex;
  margin-inline-start: $spacer-3;
}
}

@include mobile-tablet {
  .ifp-exp-pre {
    &__heading-wrapper {
      padding: $spacer-4 $spacer-3;
    }
  }
}
