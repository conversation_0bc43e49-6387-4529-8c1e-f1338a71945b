export const dxpApi = {
  listEntityKpi: 'content-type/dxp/entity-kpi',
  entityKpiChart: (objectId: string) => `content-type/dxp/entity-kpi/${objectId}`,
  product: 'content-type/dxp/product',
  image: 'content-type/dxp/image/',
  // dataSet: 'content-type/dxp/asset/',
  column: (productId: string) => `content-type/dxp/product/${productId}/asset/`,
  productDetail: 'content-type/dxp/product/',
  query: (productId: string, assetId: string) => `content-type/dxp/product/${productId}/asset/${assetId}/query`,
  distinctValues: (productId: string, assetId: string) => `content-type/dxp/product/${productId}/asset/${assetId}/distinct-values`,
  userOnboardingUsers: 'user-onboarding/entity/users',
  approve: (id: string)=> `approval-requests/${id}/actions`,
  getDetails: (id: string)=> `content-type/dxp/entity-kpi/${id}`,
  getFilterData: (productId: string, assetId: string)=> `content-type/dxp/product/${productId}/asset/${assetId}/query`,
  getCommentList: (approvalRequestId: string)=> `approval-requests/${approvalRequestId}/comments`,
  getSideMenuList: 'content-type/dxp/entity-kpi-sidebar',
  approvalAction: (approvalRequestId: string) => `approval-requests/${approvalRequestId}/actions`,
  sendForApproval: 'approval-requests'
}

export const approveConst = {
  approve : 'approve',
  reject: 'reject',
  revert: 'revert',
  claim: 'claim',
  unclaim: 'unclaim'
}
export const approveStatus= {
  approve : 'approved',
  pending: 'pending',
  rejected: 'rejected',
  unpublished: 'unpublished'
}

export const generalizedRoles= {
  approver : 'APPROVER',
  builder:  'BUILDER',
  explorer: 'EXPLORER'
}

