import { Injectable, WritableSignal, signal } from '@angular/core';
import { HttpService } from 'src/app/scad-insights/core/services/http/http.service';
import { FileResponePrep, NodeData, ToolValues } from '../../ifp-data-prep/interface/ifp-data-prep.interface';
import { BehaviorSubject, Subject } from 'rxjs';
import { WorkFlowStatusStream } from '../../ifp-data-prep/ifp-prep-library/intreface/ifp-prep-library.interface';
import { connectionTypeConfigs } from '../../ifp-data-prep/constants/if-preps.constants';

@Injectable({
  providedIn: 'root'
})
export class IfpAdvancePrepService {
  public uploadedFileResponse: WritableSignal<FileResponePrep | null> = signal(null);
  public currentNodeId: WritableSignal<string | null> = signal(null);
  public currentNode: WritableSignal<NodeData | null> = signal(null);
  public nodeChangeDetect = new Subject<string | null>();
  public showPreview = signal(false);
  public processStatus = new BehaviorSubject<null | WorkFlowStatusStream>(null);
  public destinationSaveModelOpen = new BehaviorSubject<boolean>(false);
  public cancelWorkFlow = new BehaviorSubject<boolean>(false);
  public initWorkFlow = new BehaviorSubject<boolean>(false);
  public deleteDataSet: WritableSignal<string | null> = signal('');
  public isWorkFlowStarted: WritableSignal<boolean> = signal(false);
  public selectedWorkflow: WritableSignal<string> = signal('');
  public previewNode:WritableSignal<string | null> = signal(null);
  public previewAnchor:WritableSignal<number> = signal(0);
  public availablePreviewNodes!: Record<string, any>;
  public tableView: 'normal' | 'detail' = 'normal';
  // public sheetList: string[] = [];
  // public selectedWorksheet: string = '';
  // public s3Path!: string;
  public currentNodeAndAnchor = new BehaviorSubject<null | {
    nodeId: string | null;
    anchor: number;
    source: boolean;
    sourceId: string;
    afterWorkflow?:boolean;
  }>(null);

  public previewTableData: { heading: string[]; data: any } = { heading: [], data: [] };
  public workflowRunningStatus = signal(false);
  public isAnyConnections = signal(false);
  public workFlowID: WritableSignal<string | null> = signal(null);
  public isConfigSave: WritableSignal<boolean> = signal(false);
  public previewWidth = signal(310);
  public streamId: WritableSignal<string> = signal('');
  public isNoRecordFound: WritableSignal<boolean> = signal(false);
  public source: WritableSignal<boolean> = signal(false);
  public logs: WritableSignal<{
    key: string;
    title: string;
    value: string;
  }[][]> = signal([]);

  constructor(private _http: HttpService) { }

  getParams(params: any) {
    return params ? Object.entries(params).map(([key, value]) => `${key}=${value}`).join('&') : '';
  }

  getMethodRequest(url: string, params?: any) {
    const query = this.getParams(params);
    return this._http.get(`${url}${query === '' ? query : `?${query}`}`, true);
  }

  getMethodStreamRequest(url: string, params?: any) {
    const query = this.getParams(params);
    return this._http.getSream(`${url}${query === '' ? query : `?${query}`}`, true);
  }

  getDeleteRequest(url: string, params?: any) {
    const query = this.getParams(params);
    return this._http.delete(`${url}${query === '' ? query : `?${query}`}`, true);
  }

  postMethodRequest(url: string, data?: any) {
    return this._http.post(`${url}`, data, true);
  }

  putMethodRequest(url: string, data?: any) {
    return this._http.put(`${url}`, data, true);
  }

  getDataSet(url: string) {
    return this._http.get(`${url}`, true);
  }


  getDownloadRequest(url: string, params?: any) {
    const query = this.getParams(params);
    return this._http.fileDownloadWithHeader(`${url}${query === '' ? query : `?${query}`}`);
  }

  getDefualtToolConfig(toolName: string) {
    let toolValues: ToolValues = { toolName: '', type: '', config: '' };
    if (toolName) {
      const selectedTool = connectionTypeConfigs[toolName];
      if (selectedTool) {
        toolValues = selectedTool;
      }
    }
    return toolValues;
  }

  forceStopWorkflow(url: string) {
    return this._http.post(`${url}`, {}, true);
  }

  clearPreviewData() {
    this.previewTableData = {
      heading: [],
      data: []
    };
    this.previewNode.set(null);
    this.previewAnchor.set(0);
  }


  clearAllValues() {
    this.currentNodeAndAnchor.next(null);
  }
}


