<div class="ifp-kpi-edit">
  <div class="ifp-kpi-edit__modal-header">
    <h2 class="ifp-kpi-edit__modal-head">{{'Edit KPI' | translate}}</h2>
    <em class="ifp-icon ifp-icon-cross ifp-kpi-edit__modal-close" (click)="onCloseModal()"></em>
  </div>

  <div class="ifp-kpi-edit__modal-body">
    <app-ifp-tab [tabData]="tabData" class="ifp-kpi-edit__tab" [tooltipDisabled]="true" [selectedTab]="selectedTab" (selectedTabEvent)="tabClick($event.index)"></app-ifp-tab>

    @if(this.tabData[this.selectedTab].key === 'template') {

      <div class="ifp-kpi-edit__content ifp-kpi-edit__content--template">
        @for (content of tabAccordianContent.template; track $index) {
          <ifp-accordion [heading]="content.title | translate" class="ifp-kpi-edit__accordion"
            (selectAccordian)="expandAccordian($event,content)" [isExpanded]="content.isExpand">
            <div class="ifp-accordion__body-content">
              @switch (content.key) {
                @case ('template') {
                  <ifp-db-value-card-templates [(selectedTemplate)]="selectedTemplate" class="ifp-kpi-edit__template"></ifp-db-value-card-templates>
                }
                @case ('source') {
                  <div class="ifp-kpi-edit__source">
                    <p class="ifp-kpi-edit__label">{{'Select Source From' | translate}}</p>

                    <div class="ifp-kpi-edit__source-list">
                    @for (source of sourceList(); track $index) {
                      <div class="ifp-kpi-edit__source-item ifp-kpi-edit__source-item--select">
                        <em class="ifp-icon ifp-kpi-edit__source-icon" [class]="source.icon"></em>
                        <p class="ifp-kpi-edit__source-title">{{source.label | translate}}</p>
                        <em class="ifp-icon ifp-icon-right-arrow ifp-kpi-edit__source-arrow"></em>
                      </div>
                    }
                    </div>

                    <p class="ifp-kpi-edit__label">{{'Added Data Source' | translate}}</p>
                    <div class="ifp-kpi-edit__source-item">
                      <em class="ifp-icon ifp-icon-table-margin ifp-kpi-edit__data-icon"></em>
                      <p class="ifp-kpi-edit__source-title">{{'Economy Analysis' | translate}}</p>
                    </div>
                  </div>
                }
                @case ('columns') {
                  <div class="ifp-kpi-edit__column-sec">
                    <p class="ifp-kpi-edit__label">{{'Data Column' | translate}}</p>
                      <ifp-multi-tag-dropdown [options]="possibleAggregationColumns()" [multiSelect]="false" [optionArrayKey]="'x_aggregations'" (dropDownItemClicked)="selectXaxis($event)"></ifp-multi-tag-dropdown>
                  </div>

                  <div class="ifp-kpi-edit__column-sec">
                    <p class="ifp-kpi-edit__column-heading">{{'Show Value As'}}</p>
                    <div class="ifp-kpi-edit__dropdown-wrapper">
                      <p class="ifp-kpi-edit__label">{{'Primary Value' | translate}}:</p>
                      <app-ifp-db-dropdown class="ifp-kpi-edit__value-dropdown" [options]="valueList" [isMultiSelect]="false" (singleSelected)="selectPrimaryValue($event)"
                      [key]="'name'"></app-ifp-db-dropdown>
                    </div>
                    @if (selectedTemplate().config['valueSecondary']) {
                      <div class="ifp-kpi-edit__dropdown-wrapper">
                      <p class="ifp-kpi-edit__label">{{'Secondary Values' | translate}}:</p>
                      <app-ifp-db-dropdown class="ifp-kpi-edit__value-dropdown" [options]="valueList" [isMultiSelect]="false" (singleSelected)="selectSecondaryValue($event)"
                      [key]="'name'"></app-ifp-db-dropdown>
                      @if (selectedTemplate().config['valueTertiary']) {
                      <app-ifp-db-dropdown class="ifp-kpi-edit__value-dropdown" [options]="valueList" [isMultiSelect]="false" (singleSelected)="selectTertiaryValue($event)"
                      [key]="'name'"></app-ifp-db-dropdown>
                      }
                    </div>
                    }
                  </div>

                  @if(selectedTemplate().config['compareValue']) {
                    <div class="ifp-kpi-edit__column-sec">
                    <p class="ifp-kpi-edit__column-heading">{{'Comparison Indicators'}}</p>
                    <div class="ifp-kpi-edit__dropdown-wrapper">
                      <p class="ifp-kpi-edit__label">{{'Compare' | translate}}:</p>
                      <div class="ifp-kpi-edit__compare-wrapper">
                        <app-ifp-db-dropdown class="ifp-kpi-edit__value-dropdown" [options]="valueList" [isMultiSelect]="false" (singleSelected)="selectCompareValue($event)" [key]="'name'"></app-ifp-db-dropdown>
                        <p class="ifp-kpi-edit__label">{{'With' | translate}}</p>
                        <app-ifp-db-dropdown class="ifp-kpi-edit__value-dropdown" [options]="valueList" [isMultiSelect]="false" (singleSelected)="selectCompareWithValue($event)" [key]="'name'"></app-ifp-db-dropdown>
                      </div>
                    </div>
                  </div>
                  }
                }
                @default {

                }
              }
            </div>
          </ifp-accordion>
        }
      </div>
    } @else {
      <div class="ifp-kpi-edit__content ifp-kpi-edit__content--style">
        <div class="ifp-kpi-edit__style-wrapper">
          <p class="ifp-kpi-edit__label">{{'Theme' | translate}}</p>
          <ifp-color-palette-group [(selectedColor)]="theme"></ifp-color-palette-group>
        </div>
        <div class="ifp-kpi-edit__style-wrapper">
          <p class="ifp-kpi-edit__label">{{'Alignment' | translate}}</p>
          <ifp-text-alignment-selector class="ifp-kpi-edit__alignment" [(selectedAlignment)]="selectedTextAlignment"></ifp-text-alignment-selector>
        </div>
      </div>
    }
  </div>

  <div class="ifp-kpi-edit__modal-footer">
    <ifp-button [label]="'Cancel' | translate" (ifpClick)="cancelModal()" [buttonClass]="buttonClass.secondary" class="ifp-kpi-edit__footer-btn"></ifp-button>
    <ifp-button [label]="'Apply' | translate" (ifpClick)="onSave()" class="ifp-kpi-edit__footer-btn"></ifp-button>
  </div>
</div>
