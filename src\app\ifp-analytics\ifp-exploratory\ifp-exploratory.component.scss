@use "../../../assets/ifp-styles/abstracts/index" as *;
.ifp-exprloratory {
  &__header {
    background-color: $ifp-color-section-white;
    border-bottom: 1px solid $ifp-color-grey-7;
  }
  &__stepper {
    display: block;
    padding: $spacer-4 $spacer-6;
  }
  &__divider {
    border-bottom: 1px solid $ifp-color-grey-8;
  }
  &__sub-header {
    padding: $spacer-3 $spacer-0;
    justify-content: space-between;
    display: flex;
    align-items: center;
  }
  &__btn {
    margin-inline-end: $spacer-2 !important;
    font-size: $ifp-fs-4;
    font-weight: $fw-medium;
    padding: $spacer-2  $spacer-4;
    display: inline-flex;
    align-items: center;
    border-radius: 5px;
    text-transform: uppercase;
    margin: $spacer-0 $spacer-3;
    transition: 0.3s;
    &--rnd {
      border-radius: 15px;
    }
    &--secondary {
      text-align: center;
      color: $ifp-color-active-blue;
      border: 1px solid $ifp-color-active-blue;
      cursor: pointer;
      margin: 0;
      &:hover {
        background-color: $ifp-color-active-blue;
        color: $ifp-color-white;
      }
    }
}
&__btn-text {
  display: flex;
  align-items: center;
}
&__btn-icon {
  font-size: $ifp-fs-6;
  margin-right: $spacer-2;
  transition: 0.3s;
}
&__process-btn-sec {
    margin-inline-end: $spacer-2;
}

&__btn-wrapper {
  display: flex;
}
}
:host-context(.ifp-header__fixed) {
  .ifp-exprloratory {
    &__header--subheader {
      position: sticky;
      top: $ifp-header-height-sticky;
      left: 0;
      z-index: 9;
      box-shadow: 0 4px 16px $ifp-color-black-16;
      @include slide-down-below-header();
    }
  }
}
