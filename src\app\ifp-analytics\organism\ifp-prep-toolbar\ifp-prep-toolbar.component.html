@if (changeToolBar) {
  <div #toolBar class="ifp-prep-toolbar" [ngClass]="{'ifp-prep-toolbar--collapse-basic': isCollapsed, 'ifp-prep-toolbar--disable': isDisabled, 'ifp-prep-toolbar--accordian': isAccordian}">
  <div class="ifp-prep-toolbar__head-sec">
    <h2 class="ifp-prep-toolbar__title">{{'Tools' | translate}}</h2>
    <div class="ifp-prep-toolbar__collapse" [ngClass]="{'ifp-prep-toolbar__collapse--collapse': isCollapsed}" [appIfpTooltip]="(isCollapsed ? 'Expand' : 'Collapse')| translate" (click)="isCollapsed = !isCollapsed"></div>
    <!-- <em class="ifp-icon ifp-icon-left-arrow ifp-prep-toolbar__toggle"
      [appIfpTooltip]="(isCollapsed ? 'Expand' : 'Collapse')| translate" [zIndex]="1000" [extraSpaceTop]="2"
      (click)="isCollapsed = !isCollapsed; autoClose = !autoClose"></em> -->
  </div>
  <!-- <ifp-search [boxType]="true" class="ifp-prep-toolbar__search" (searchEvent)="searchEvent($event)" [onSearch]="search"
    [isKeypress]="true" ></ifp-search> -->
  <ul class="ifp-prep-toolbar__tools-list" [ngClass]="{'ifp-prep-toolbar__tools-list--accordian': false}">
    @for(item of searchData; track $index) {
    <li class="ifp-prep-toolbar__group" [ngClass]="{'ifp-prep-toolbar__group--active': item.isSelected}">
      @if(item.key ) {
      <div class="ifp-prep-toolbar__group-wrapper" (click)="item.isSelected = !item.isSelected">
        <span class="ifp-prep-toolbar__group-name-basic">{{item.key | translate}}</span>
      </div>
      }
      <ul class="ifp-prep-toolbar__group-inner ifp-prep-toolbar__group-inner--basic">
        @for (tool of item.menu; track tool.key) {
        <li class="ifp-prep-toolbar__tool-basic" [attr.data-node]="tool.key+'-'+ item.key"
          [ngClass]="{'ifp-prep-toolbar__tool-basic--active': selectedTool === tool && !isAccordian, 'ifp-prep-toolbar__tool--drag-overflow': isDragable, 'ifp-prep-toolbar__tool-basic--enabled': tool.enabled}"
          [draggable]="isDragable" (dragstart)="drag($event)" (click)="onToolSelected(tool)">
          <em class="ifp-icon ifp-prep-toolbar__icon" [class]="tool.icon" [style.color]="tool?.color ?? ''" [appIfpTooltip]="(isCollapsed ? tool.name : '')| translate" [disableTooltip]="!isCollapsed"></em>
          <span class="ifp-prep-toolbar__tool-name-basic">{{tool.name | translate}}</span>
          <app-ifp-info class="ifp-prep-toolbar__tool-info" [iconColor]="(_themeService.defaultTheme$| async) === 'dark' ? '#e1e2e3' : '#364151'" [position]="'right'" [infoContent]="tool.description" [widthInfo]="'200px'"></app-ifp-info>
        </li>
        }
      </ul>
    </li>
    }
  </ul>
</div>
} @else {
  <div class="ifp-prep-toolbar" #toolBar
  [ngClass]="{'ifp-prep-toolbar--collapse': isCollapsed, 'ifp-prep-toolbar--disable': isDisabled, 'ifp-prep-toolbar--accordian': isAccordian}">
  <div class="ifp-prep-toolbar__head-sec">
    <h2 class="ifp-prep-toolbar__title">{{'Tools' | translate}}</h2>
    <div class="ifp-prep-toolbar__collapse" [ngClass]="{'ifp-prep-toolbar__collapse--collapse': isCollapsed}" [appIfpTooltip]="(isCollapsed ? 'Expand' : 'Collapse')| translate" (click)="collapsed()"></div>
    <!-- <em class="ifp-icon ifp-icon-left-arrow ifp-prep-toolbar__toggle"
      [appIfpTooltip]="(isCollapsed ? 'Expand' : 'Collapse')| translate" [zIndex]="1000" [extraSpaceTop]="2"
      (click)="collapsed(); autoClose = !autoClose"></em> -->
  </div>
  <!-- <ifp-search [boxType]="true" class="ifp-prep-toolbar__search" (searchEvent)="searchEvent($event)" [onSearch]="search"
    [isKeypress]="true"></ifp-search> -->
  <ul class="ifp-prep-toolbar__tools-list" [ngClass]="{'ifp-prep-toolbar__tools-list--accordian': false}">
    @for(item of searchData; track $index) {
    <li class="ifp-prep-toolbar__group" [ngClass]="{'ifp-prep-toolbar__group--active': item.isSelected}">
      @if(item.key && isAccordian) {
      <div class="ifp-prep-toolbar__group-wrapper" (click)="item.isSelected = !item.isSelected">
        <em class="ifp-icon ifp-prep-toolbar__icon" [class]="item.icon" [style.color]="item?.color ?? '#3267ff'"></em>
        <span class="ifp-prep-toolbar__group-name">{{item.key | translate}}</span>
      </div>
      }
      <ul class="ifp-prep-toolbar__group-inner">
        @for (tool of item.menu; track tool.key) {
        <li class="ifp-prep-toolbar__tool" [attr.data-node]="tool.key+'-'+ item.key"
          [ngClass]="{'ifp-prep-toolbar__tool--active': selectedTool === tool && !isAccordian, 'ifp-prep-toolbar__tool--drag-overflow': isDragable}"
          [draggable]="isDragable" (dragstart)="drag($event)" (click)="onToolSelected(tool)">
          <em class="ifp-icon ifp-prep-toolbar__icon" [class]="tool.icon" [style.color]="tool?.color ?? ''" [appIfpTooltip]="(isCollapsed ? tool.name : '')| translate" [disableTooltip]="!isCollapsed"></em>
          <span class="ifp-prep-toolbar__tool-name">{{tool.name | translate}}</span>
        </li>
        }
      </ul>
    </li>
    }
  </ul>
</div>
}
<!-- @if (!isCollapsed){
  <div class="toolbar-overlay" (click)="isCollapsed = !isCollapsed"></div>
} -->


