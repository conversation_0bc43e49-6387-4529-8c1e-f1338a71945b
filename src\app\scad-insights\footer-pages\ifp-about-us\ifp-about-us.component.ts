import { CommonModule } from '@angular/common';
import { Component, ElementRef, OnInit, ViewChild } from '@angular/core';
import { Title } from '@angular/platform-browser';
import { Store } from '@ngrx/store';
import { TranslateModule } from '@ngx-translate/core';
import { Observable, tap } from 'rxjs';
import { PageData } from 'src/app/scad-insights/core/interface/molecule/breadcrumb.interface';
import { IfpBreadcrumbsComponent } from 'src/app/scad-insights/ifp-widgets/ifp-molecules/ifp-breadcrumbs/ifp-breadcrumbs.component';
import { getAboutUs } from 'src/app/scad-insights/store/footer/about-us/about-us.action';
import { selectAboutusResponse } from 'src/app/scad-insights/store/footer/about-us/about-us.selector';
import { AboutUsState } from 'src/app/scad-insights/store/footer/about-us/about-us.state';
import { IFPMsalService } from '../../core/services/IFP-msal.service';
import { title } from '../../core/constants/header.constants';

@Component({
    selector: 'app-ifp-about-us',
    templateUrl: './ifp-about-us.component.html',
    styleUrls: ['./ifp-about-us.component.scss'],
    imports: [CommonModule, IfpBreadcrumbsComponent, TranslateModule]
})
export class IfpAboutUsComponent implements OnInit {

  @ViewChild('aboutVideo') aboutVideo!: ElementRef;
  public playVideo: boolean = false;
  public aboutUsData$: Observable<AboutUsState> = this.store.select(selectAboutusResponse).pipe(
    tap(data => {
      if (data) {
        this._titleService.setTitle(`${title.bayaan} | ${data?.title}`);
        if (data?.title) {
          (window as any)?.dataLayer?.push({
            'event': 'page_load',
            'page_title_var': data?.title
          });
        }
        this.pageData = [
          {
            title: 'Home',
            route: '/home'
          },
          {
            title: data?.title,
            route: ''
          }
        ];
      }

    })
  );

  constructor(private store: Store, private _titleService: Title, private _msalService:IFPMsalService) {
    this._titleService.setTitle(`${title.bayaan} | About Us`);

    (window as any)?.dataLayer?.push({
      'event': 'page_load',
      'page_title_var': ' About Us',
      'userId': this._msalService.convertEmail(this._msalService.getLoginData.account.username)
    });

  }

  ngOnInit() {
    this.store.dispatch(getAboutUs());
  }

  toggleVideo() {
    this.playVideo = !this.playVideo;
    if (this.playVideo) {
      this.aboutVideo?.nativeElement.play();
    } else {
      this.aboutVideo?.nativeElement.pause();
    }
  }

  pageData: PageData[] = [
    {
      title: 'Home',
      route: '/home'
    },
    {
      title: 'About Us',
      route: ''
    }
  ];
}
