
<div class="ifp-login" [ngStyle]="{'background-image': 'url('+backgroundImage+')' }">



<div class="ifp-login__bottom">
  <p class="ifp-login__bottom-value">{{('copyrightGeneral' | translate:{year: currentYear})}}</p>
</div>

<div class="ifp-login__card">
  <div class="ifp-login__icons">
    <ifp-img class="ifp-login__left-icon" [width]="150" [darkIcon]="'assets/images/bayaan-logo-blue.svg'" [lightIcon]="'assets/images/bayaan-logo-white.svg'" [alt]="'Bayaan'"> </ifp-img>
    <a href="https://www.scad.gov.ae/" target="_blank" [title]="'Statistics Center Abu Dhabi'"><ifp-img  class="ifp-login__right-icon" [width]="150" [darkIcon]="'assets/images/scad-logo.svg'" [lightIcon]="'assets/images/scad-logo-white.svg'" [alt]="'STATISTICS CENTRE'"> </ifp-img></a>
  </div>

    <!-- <h1 class="ifp-login__title">{{'You have been logged out.'| translate}}</h1>
    <p class="ifp-login__sub-title">{{'Please login again to access the application' |translate}}</p> -->

    <div class="ifp-login__btn-wrapper">
      <ifp-button  class="ifp-login__btn-login" [label]="'Login with Microsoft'" [buttonClass]="buttonClass.primary +' '+ buttonIconPosition.left+' '+buttonClass.normalAplabetic" [iconClass]="'ifp-icon-apps'" (ifpClick)="login()"></ifp-button>

      <div class="ifp-login__divider">
        <span class="ifp-login__divider-text">{{'OR'| translate}}</span>
      </div>

      <button (click)="uaePassLogin()" class="ifp-btn ifp-btn--uae-pass ifp-btn--aplhabetic ifp-login__btn-login"><img src="../../../../assets/images/uae-pass-icon-white.svg" alt="icon" class="ifp-login__btn-icon">{{'Sign in with UAE PASS' | translate}}</button>
<!--
      <ifp-button  class="ifp-login__btn-login" [buttonClass]="buttonClass.secondary" [label]="'Login with UAE Pass'" [iconClass]="'ifp-icon-rightarrow'" (ifpClick)="uaePassLogin()"></ifp-button> -->
    </div>
  </div>
</div>





