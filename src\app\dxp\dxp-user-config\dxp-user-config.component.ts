import { Component, input, signal, inject } from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';
import { DxpKpiCardComponent } from "../dxp-kpi-card/dxp-kpi-card.component";
import { IfpUserTagGroupComponent, UserDetail } from "../../scad-insights/ifp-widgets/ifp-molecules/ifp-user-tag-group/ifp-user-tag-group.component";
import { IfpPanelDropdownComponent, PanelDropdownOptions } from "../../ifp-analytics/molecule/ifp-panel-dropdown/ifp-panel-dropdown.component";
import { map, Subject, debounceTime } from 'rxjs';
import { ApiService } from 'src/app/scad-insights/core/services/api.service';
import { dxpApi } from '../dxp.constants';
import { DxpUserListingResponse } from '../dxp.interface';

@Component({
  selector: 'ifp-dxp-user-config',
  imports: [TranslateModule, DxpKpiCardComponent, IfpUserTagGroupComponent, IfpPanelDropdownComponent],
  templateUrl: './dxp-user-config.component.html',
  styleUrl: './dxp-user-config.component.scss'
})
export class DxpUserConfigComponent {
public cardTitle = input('title');
public cardDescription = input('desc');
public cardFilters = input([
    {
        "column": "Def",
        "filterOptions": [
            {
                "id": "Def-value 1",
                "value": "Def-value 1",
                "index": 0
            },
            {
                "id": "Def-value 2",
                "value": "Def-value 2"
            },
            {
                "id": "Def-value 3",
                "value": "Def-value 3",
                "index": 2
            },
            {
                "id": "Def-value 4",
                "value": "Def-value 4"
            }
        ],
        "filterLabel": "asdad",
        "defaultValue": "Def-value 1"
    },
    {
        "column": "Abc",
        "filterOptions": [
            {
                "id": "Abc-value 1",
                "value": "Abc-value 1"
            },
            {
                "id": "Abc-value 2",
                "value": "Abc-value 2"
            },
            {
                "id": "Abc-value 3",
                "value": "Abc-value 3",
                "index": 2
            },
            {
                "id": "Abc-value 4",
                "value": "Abc-value 4"
            }
        ],
        "filterLabel": "fasffa",
        "defaultValue": "Abc-value 3"
    },
    {
        "column": "Def",
        "filterOptions": [
            {
                "id": "Def-value 1",
                "value": "Def-value 1",
                "index": 0
            },
            {
                "id": "Def-value 2",
                "value": "Def-value 2"
            },
            {
                "id": "Def-value 3",
                "value": "Def-value 3",
                "index": 2
            },
            {
                "id": "Def-value 4",
                "value": "Def-value 4"
            }
        ],
        "filterLabel": "asdada",
        "defaultValue": "Def-value 3"
    }
]);
public userConfig = input<any>('userConfig');
  public sharedUserListSelected = signal<UserDetail[]>([]);
  public sharedUserList = signal<PanelDropdownOptions[]>([]) ;
  public currentSelection =signal<PanelDropdownOptions[]>([]);
  public userListingData: DxpUserListingResponse | null = null;

  private readonly _apiService = inject(ApiService);
  private searchSubject = new Subject<string>();

  ngOnInit() {
    console.log(this.userConfig());
    
    this.getUserListingData('', 1);
    this.searchSubject.pipe(debounceTime(300)).subscribe(term => {
      this.getUserListingData(term, 1);
    });
  }

  getUserListingData(search: string, page: number) {
    const selected = this.sharedUserListSelected();
    this._apiService.getMethodRequest(dxpApi.userOnboardingUsers, { search, page }).subscribe({
      next: (res: DxpUserListingResponse) => {
        this.userListingData = res;
        const mapped = (res.data || []).map(user => ({
          value: user.name,
          key: user.email,
          id: user.id,
          checked: selected.some(sel => sel.email === user.email)
        }));
        this.sharedUserList.set(mapped);
        console.log('User listing data:', res);
      },
      error: (err) => {
        console.error('Failed to fetch user listing data:', err);
      }
    });
  }

  removeData(event:{tag:UserDetail, index: number}){
    this.sharedUserListSelected.update(data => {
      data.splice(event.index, 1);
      return data;
    })
   this.currentSelection.set(
    this.sharedUserListSelected().map(data => {
      return { key: data.email,id:data.id, value: data.name, checked: true };
    })
   );
   this.sharedUserList.update(data => {

    data.forEach(user => {
     user.checked = false;
    this.sharedUserListSelected().forEach(value => {

        if(value.email === user.key ) {
            user.checked = true;
        }
    });
    });

    return data;
   })
  }

  onUserSearch(term: string) {
    this.searchSubject.next(term);
  }


  dropDownItemMultiClicked(event: PanelDropdownOptions[]) {
    this.sharedUserListSelected.set(event.map(data => {return {name: data.value, email:data.key,id:data.id || ''}}));
      if (this.sharedUserListSelected()?.length <= 0) {
      this.sharedUserList.update(data=> {
        data[0].checked = true
       return  data;
      });
      this.currentSelection.set([this.sharedUserList()[0]]);
      this.sharedUserListSelected.set([{name: this.sharedUserList()[0].value , email:this.sharedUserList()[0].key,id:this.sharedUserList()[0].id || ''}]);
    }
    }

  }

  
