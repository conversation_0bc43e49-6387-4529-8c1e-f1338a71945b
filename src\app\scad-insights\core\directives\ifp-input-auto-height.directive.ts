import { Directive, ElementRef, HostListener, OnInit } from '@angular/core';

@Directive({
  selector: '[appIfpInputAutoResize]',
  standalone: true
})
export class IfpInputAutoResizeDirective implements OnInit {

  constructor(private readonly element: ElementRef) { }

  @HostListener(':input')
  onInput() {
    this.resize();
  }

  ngOnInit() {
    if (this.element.nativeElement.scrollHeight) {
      setTimeout(() => {
        this.resize();
      });
    }
  }

  resize() {
    this.element.nativeElement.style.height = '0';
    this.element.nativeElement.style.height = `${this.element.nativeElement.scrollHeight+1}px`;
  }

}
