
<div class="ifp-prep-source">

  @if (step === 'summary' ||  _prepCommonService.isSummary()) {
    @defer (on immediate)  {
      <div class="ifp-prep-source__summary">
        <ifp-data-summary [totalColumn]="totalColumn" [enableProcessBtn]="false" [totalRow]="totalRow" [loaderPage]="summeryLoader" [subtitle]="'View the statistics summary to understand the core characteristics and patterns of your dataset.'" (downloadPreviewValue)="downloadPreivew()" [loader]="loaderProcess" [settings]="summeryTableSettings"  [data]="summery" (proceed)="uploadProccessedsummery()" (back)="prevStep()"></ifp-data-summary>
      </div>
    }
  } @else {
    <ifp-prep-upload-data  (excelUploaded)="clearFile()"  (excelUploadedError)="clearFile()" [disableAll]="disableAll" [error]="error" [loaderFile]="loaderFile" [size]="fileSize" [rotate]="true" [hideDelete]="loaderProcess" (indicatorClickEvent)="indicatorClickEvent()" [currentFile]="this._prepCommonService.fileName" [loader]="loaderProcess" [disableProcesed]="disableProcesed" (uploadCompletedProced)="uploadDataUpdate() " [progress]="uploadDataProgess" class="ifp-prep-source__upload" (openLibrary)="openLibrary()" (fileUpload)="onFileUpload($event)" (selectSheet)="onSelectWorksheet($event)" (removeFile)="deleteFile()"
      [enableProcessBtn]="false" (disableEvent)="disableEvent($event)"></ifp-prep-upload-data>
  }
  <!-- @switch (step) {
    @default {
      <ifp-prep-upload-data  (excelUploaded)="clearFile()"  (excelUploadedError)="clearFile()" [disableAll]="disableAll" [error]="error" [loaderFile]="loaderFile" [size]="fileSize" [rotate]="true" [hideDelete]="loaderProcess" (indicatorClickEvent)="indicatorClickEvent()" [currentFile]="this._prepCommonService.fileName" [loader]="loaderProcess" [disableProcesed]="disableProcesed" (uploadCompletedProced)="uploadDataUpdate() " [progress]="uploadDataProgess" class="ifp-prep-source__upload" (openLibrary)="openLibrary()" (fileUpload)="onFileUpload($event)" (selectSheet)="onSelectWorksheet($event)" (removeFile)="deleteFile()"
      [enableProcessBtn]="false" (disableEvent)="disableEvent($event)"></ifp-prep-upload-data>
    }
    @case ('summary' || _prepCommonService.isSummary()) {
      @defer (on immediate)  {
        <div class="ifp-prep-source__summary">
          <ifp-data-summary [totalColumn]="totalColumn" [enableProcessBtn]="false" [totalRow]="totalRow" [loaderPage]="summeryLoader" [subtitle]="'View the statistics summary to understand the core characteristics and patterns of your dataset.'" (downloadPreviewValue)="downloadPreivew()" [loader]="loaderProcess" [settings]="summeryTableSettings"  [data]="summery" (proceed)="uploadProccessedsummery()" (back)="prevStep()"></ifp-data-summary>
        </div>
      }

    } -->
    <!-- @case('browse') {
         } -->
    <!-- @case ('complete') { -->
      <!-- @defer  (on immediate) {
      <div class="ifp-prep-source__complete-wrapper">
        <h2 class="ifp-prep-source__heading">{{'What would you like to do with the data' | translate}}?</h2>
        <div class="ifp-prep-source__card-wrapper">
          @for (card of actionCards; track card.key; let i = $index) {
            @if (i !== 0) {
              <ifp-tool-card [cardData]="card" class="ifp-prep-source__action-card" [isFontIcon]="true" [size]="'sm'" (selectCard)="onCardSelected($event)"></ifp-tool-card>
            }
          }
        </div>
      </div>
    } -->
    <!-- }
  } -->

</div>
@if (step === 'complete' && !_prepCommonService.isSummary()) {
  @defer (on immediate)  {
  <ifp-data-preview  (backEvent)="back()" [fullScreen]="'complete' === step " class="ifp-prep-data__preview"></ifp-data-preview>
  }
}
@if(indicatorModalOpen()) {
<app-ifp-modal [overlayClass]="'ifp-modal__bg-grey'" #modalSuccess  [modalClass]="'ifp-modal__import-indicators'">
    <app-ifp-import-indicators [heading]="'Select the data you want to upload'" [loader]="loaderProcess" (cancel)="closeImport()" [noDataText]="'No Data Available'" [isSingleSelect]="true" [primaryButtonText]="'Add Indicator Data' | translate" (addToDashboard)="addAllIndicators($event)" [importType]="'browse'"
    [isPrep]="true" [isHideOfficialScreener]="false"></app-ifp-import-indicators>
  </app-ifp-modal>
} @else if (upload() && !_prepCommonService.isSummary()) {
  <app-ifp-modal [overlayClass]="'ifp-modal__bg-grey'" #modalSuccess >
  <ifp-prep-popup  (closePopUp)="closeSummeryModal()"  (buttonEventValue)="closeSummeryModal()" [type]="'image'" [heading]="'Data Uploaded Successfully!'" [desc]="''" [fileName]="_prepCommonService.uploadedFileResponse()?.name ?? ''"  [subTitle]="'Data Exploratory Summary'" [subDesc]="'Gain comprehensive insights into data distribution for further analysis.'" [buttonText]="'View Data Exploratory Summary'"></ifp-prep-popup>
</app-ifp-modal>
}@else if (success()) {
  <app-ifp-modal [overlayClass]="'ifp-modal__bg-grey'" #modalSuccess >
  <ifp-prep-popup (closePopUp)="closeSucessModal()"   (buttonEventValue)="buttonClick($event)" [heading]="'How do you want to proceed with the data?'" [desc]="''"  [fileName]="_prepCommonService.uploadedFileResponse()?.name ?? ''"  [subTitle]="'Data Preparation Toolkit'" [subDesc]="'Refine your dataset with ease for accurate analysis.'" [buttonText]="'View Data Exploratory Summary'"></ifp-prep-popup>
</app-ifp-modal>
}



