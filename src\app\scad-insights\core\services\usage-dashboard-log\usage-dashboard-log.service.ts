import { Injectable } from '@angular/core';
import { HttpService } from '../http/http.service';
import { logType, userLogApi } from './usage-dashboard.constants';
import { v4 as uuid } from 'uuid';
import { timeout } from 'rxjs';
import { DeviceDetectorService } from 'ngx-device-detector';
@Injectable({
  providedIn: 'root'
})
export class UsageDashboardLogService {
  public sessions: string[] = [];

  constructor(private _http: HttpService, private deviceService: DeviceDetectorService) {
    window.addEventListener('beforeunload', () => {
      const time = this.currentTime;
      if (this.sessions.length !== 0) {
        this.sessions.forEach(session => {
          this.logEnds(session, time);
        });
      }
    });
  }

  async logMap(sessionId: string, sessionType: string, time: number, district: string, domain: string) {
    const position = await this.getPosition();
    const values: LogInterFace = {
      sessionId: sessionId,
      sessionType: sessionType,
      time: time,
      data1: domain,
      data2: district,
      lat: position?.lat,
      long: position?.lng,
      device: this.getDeviceInfo()
    };
    const index = this.sessions.indexOf(sessionId);
    if (index !== -1) {
      this.sessions.slice(0, index);
    }

    return new Promise((resolve) => {
      const subs = this._http.post(userLogApi.logStart, values).pipe(timeout(10000)).subscribe({
        next: () => {
          subs.unsubscribe();
          resolve(true);
        },
        error: () => {
          subs.unsubscribe();
          resolve(false);
        }
      });
    });


  }


  async logStart(sessionId: string, sessionType: string, time: number, nodeId?: number, indicatorName?: string, fileType?: string) {
    const position = await this.getPosition();
    const values: LogInterFace = {
      sessionId: sessionId,
      sessionType: sessionType,
      time: time,
      lat: position?.lat,
      long: position?.lng,
      device: this.getDeviceInfo()
    };
    if (nodeId) {
      if (sessionType === logType.download) {
        values['data1'] = indicatorName;
        values['data3'] = nodeId;
        values['downloadType'] = fileType?.toLowerCase();
      } else {
        values['nodeId'] = nodeId;
      }
    }
    this.sessions.push(sessionId);

    return new Promise((resolve) => {
      const subs = this._http.post(userLogApi.logStart, values).pipe(timeout(10000)).subscribe({
        next: () => {
          subs.unsubscribe();
          resolve(true);
        },
        error: () => {
          subs.unsubscribe();
          resolve(false);
        }
      });
    });



  }

  async logEnds(sessionId: string, time: number) {
    const position = await this.getPosition();
    const values: LogEndInterface = {
      sessionId: sessionId,
      time: time,
      lat: position?.lat,
      long: position?.lng,
      device: this.getDeviceInfo()
    };

    const subs = this._http.post(userLogApi.logEnds, values).pipe(timeout(10000)).subscribe({
      next: () => {
        subs.unsubscribe();
      },
      error: () => {
        subs.unsubscribe();
      }
    });

  }

  get createUUid() {
    return uuid();
  }

  get currentTime() {
    return Math.floor(new Date().getTime() / 1000);
  }

  getPosition(): Promise<any> {
    return new Promise((resolve) => {
      navigator.geolocation.getCurrentPosition(resp => {
        resolve({ lng: resp.coords.longitude, lat: resp.coords.latitude });
      },
        err => {
          resolve('');
        });
    });

  }

  getDeviceInfo() {
    return {
      ...this.deviceService.getDeviceInfo(),
      mobile: this.deviceService.isMobile(),
      tablet: this.deviceService.isTablet(),
      desktop: this.deviceService.isDesktop()
    };
  }
}
interface LogInterFace {
  sessionId: string;
  sessionType: string;
  nodeId?: number;
  time: number;
  data1?: string;
  data2?: string;
  data3?: number;
  lat?: number,
  long?: number,
  device?: Device,
  downloadType?: string;
}

interface LogEndInterface {
  sessionId: string;
  time: number;
  lat?: number,
  long?: number,
  device?: Device
}

interface Device {
  device: any,
  mobile: boolean,
  tablet: boolean,
  desktop: boolean
}
