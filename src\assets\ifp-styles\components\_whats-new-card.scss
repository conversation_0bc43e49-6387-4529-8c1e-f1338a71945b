@use '../../../assets/ifp-styles/abstracts' as *;

.ifp-whats-new-card {
  $card-left-width: 180px;
  width: 100%;
  height: 100%;
  position: relative;

  &__header {
    display: flex;
    justify-content: space-between;
    margin-bottom: $spacer-3;
  }

  &__icon-wrapper {
    display: flex;
    align-items: center;
  }

  &__type {
    margin-left: $spacer-1;
    color: $ifp-color-blue-hover;
    font-size: 2rem;

    &.ifp-icon-conical-flask {
      position: relative;
      top: 1px;
    }
  }

  &__buttons {
    margin: $spacer-0 (-$spacer-1);
    display: flex;
    align-items: flex-start;
  ::ng-deep .ifp-btn__icon {
    font-size: $ifp-fs-6;
  }
}

&__btn {
  margin: $spacer-0 $spacer-1;
}

&__rating {
  font-size: 1.2rem;
  margin: $spacer-1 $spacer-0;

  &>* {
    font-size: inherit;
  }

  .ifp-icon {
    display: inline-block;
    margin-right: $spacer-1;
  }

  &--grey {
    color: $ifp-color-grey-6;
  }
}

&__rating-value {
  font-weight: $fw-bold;
}

&__value-range {
  display: flex;
  align-items: flex-end;
  margin: 0px -2px;
}

&__value {
  font-weight: $fw-bold;
  font-size: $ifp-fs-8;
  margin: 0px 2px;
}

&__unit {
  margin-left: $spacer-1;
  font-size: $ifp-fs-2;
}

&__range {
  font-size: $ifp-fs-2;
  margin: 0px 2px;
}

&__name {
  font-size: $ifp-fs-3;
  font-weight: $fw-bold;
  min-height: 37px;
  @include lineLimit(2);
  user-select: none;

  &--census {
    margin: $spacer-2 $spacer-0;
  }

  &--census
  &::first-letter {
    text-transform: capitalize;
  }
}

&__dropdown {
  margin-bottom: 12px;
  font-size: $ifp-fs-2;
}

&__rect {
  cursor: pointer;
  display: inline-block;
  min-height: 2rem;
  border: 2px solid $ifp-color-black;
  border-radius: 5px;
  transition: 0.3s;
  margin: 1px $spacer-1;
  opacity: 0.5;
  &:hover {
    opacity: 1;
  }

  &--small {
    width: 13px;
    min-width: 13px;
  }

  &--large {
    width: 26px;
    min-width: 26px;
  }
}

&__fill {
  opacity: 1;
  border-color: $ifp-color-blue-hover;
  cursor: default;
}

&__body {
  display: flex;

  &--expanded {
    .ifp-whats-new-card {
      &__left .ifp-whats-new-card__name {
        opacity: 0.3;
        visibility: hidden;
      }

      &__right .ifp-whats-new-card__name {
        padding-left: $spacer-5;
      }
    }
  }
}

&__left {
  min-width: $card-left-width;
  max-width: $card-left-width;

  &:only-child {
    max-width: none;
  }

  &:last-child {
    margin-bottom: $spacer-0;
  }
}

&__right {
  padding-left: $spacer-2;
  width: calc(100% - $card-left-width);
}

&__download {
  display: block;
  margin-left: auto;

  ::ng-deep .ifp-btn em {
    font-size: 2rem;
  }
}

&__remove {
  position: absolute;
  bottom: $spacer-3;
  right: 15px;
  width: calc(100% - 30px);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

&__checbox-disabled {
  display: block;
  opacity: .4;
  pointer-events: none;
}

&__txt-icon {
  margin-top: $spacer-1;
}



&__filter {
  font-size: $ifp-fs-2;
  margin-top: $spacer-1;
  color: $ifp-color-tertiary-text;
  @include lineLimit(2);
}

&__remove-btn {
  margin-inline-end: $spacer-2;
  position: relative;
  top: 1px;
}
&__footer-icons {
  display: flex;
  align-items: center;
  margin-inline-start: auto;
}

&__sync {
  padding-inline-start: $spacer-3;
  border-inline-start: 1px solid $ifp-color-grey-7;
  margin-inline-start: $spacer-3;
}

&--dxp {
  .ifp-whats-new-card {
    &__txt-icon {
      display: flex;
      align-items: center;
    }
    &__remove {
      position: static;
    }
  }
}
// &__key {
//   font-size: $ifp-fs-2;
//   margin-right: $spacer-2;
//   padding-right: $spacer-2;
//   border-right: 1px solid $ifp-color-tertiary-text;
//   &:last-child {
//     margin-right: $spacer-0;
//     padding-right: $spacer-0;
//     border-right: none;
//   }
// }

// &__key-title {
//   font-size: inherit;
// }

&::ng-deep {
  .ifp-card {
    background-color: $ifp-color-section-white;
    padding: $spacer-3;
  }
  .ifp-whats-new-card__remove-btn {
    .ifp-btn {
      font-size: $ifp-fs-6;
    }
  }
}
}

:host::ng-deep {
  .ifp-card {
    overflow: unset;
    padding: $spacer-3;
  }

  .ifp-whats-new-card {
    &--download {
      .ifp-card {
        padding-bottom: $spacer-6 + $spacer-3;
      }
    }

    &__icon-wrapper {
      .ifp-domain-icon__img {
        min-width: 20px !important;
        width: 20px;
      }
    }

    &__btn {
      .ifp-btn {
        &--black {
          opacity: 0.5;

          &:hover {
            color: $ifp-color-black;
            opacity: 1;
          }
        }

        &--blue {
          opacity: 1;
        }
      }
    }

    &__analysis {
      .ifp-card {
        padding: $spacer-4 $spacer-3 $spacer-3;
        display: flex;
        flex-direction: column;
      }
    }
  }
  .ifp-whats-new-card__sync,
  .ifp-analysis__sync {
    .ifp-sync-btn {
      font-size: $ifp-fs-2;
    }
  }
}

.background-div {
  position: fixed;
  top: 0;
  left: 0;
  width: 1000px;
  height: 100%;
  z-index: -10000000;
  background-color: #f0f0f0;
}

.ifp-analysis {
  &--hybrid {
    min-height: 140px;

    .ifp-analysis {
      &__heading {
        margin-bottom: $spacer-3;
      }

      &__btn {
        margin-top: auto;
      }

      &__chart {
        min-height: 0;
      }

      &__remove {
        position: absolute;
        bottom: 0;
        right: 10px;
      }
    }
  }
}

ifp-analysis-card-header {
  margin-bottom: $spacer-3;
}

:host-context(.ifp-myapps-sidebar),
:host-context(.ifp-sidebar__preview) {
  .ifp-whats-new-card {
    &__body {
      display: block;

      &--expanded {
        .ifp-whats-new-card__left {
          .ifp-whats-new-card__name {
            display: -webkit-box;
            visibility: visible;
            opacity: 1;
          }
        }
      }
    }

    &__left {
      min-width: 0;
      max-width: none;
    }

    &__right {
      width: 100%;
      margin-top: $spacer-2;
      padding-left: $spacer-0;

      .ifp-whats-new-card__name {
        display: none;
      }
    }
  }
}

@include only-desktop-xl {
  .ifp-whats-new-card {
    &__buttons {
      ::ng-deep .ifp-btn__icon {
        font-size: $ifp-fs-4;
      }
    }

    &__type {
      font-size: $ifp-fs-4;
    }

    &__rect {
      min-height: 1.5rem;
      margin: $spacer-0 2px;

      &--large {
        width: 22px;
        min-width: 22px;
      }
    }
  }

  :host::ng-deep {
    .ifp-whats-new-card {
      &__icon-wrapper {
        .ifp-domain-icon__img {
          min-width: 16px !important;
          width: 16px;
        }
      }
    }
  }
}

@include mobile {
  .ifp-whats-new-card {
    &__name {
      display: -webkit-box;
    }

    &__body {
      display: block;

      &--expanded {
        .ifp-whats-new-card__left {
          .ifp-whats-new-card__name {
            display: -webkit-box;
            visibility: visible;
            opacity: 1;
          }
        }
      }
    }

    &__left {
      min-width: 0;
      max-width: none;
    }

    &__right {
      width: 100%;
      margin-top: $spacer-2;
      padding-left: $spacer-0;

      .ifp-whats-new-card__name {
        display: none;
      }
    }
  }
}

:host-context(.ifp-dark-theme) {
  .ifp-whats-new-card {
    &::ng-deep {
      .ifp-card {
        border-color: $ifp-color-white;
      }
    }
  }
}

:host-context([dir="rtl"]) {
  .ifp-whats-new-card {
    &__type {
      margin-right: $spacer-1;
      margin-left: $spacer-0;
    }

    &__unit {
      margin-right: $spacer-1;
      margin-left: $spacer-0;
    }

    &__body {
      &--expanded {
        .ifp-whats-new-card {
          &__right .ifp-whats-new-card__name {
            padding-right: $spacer-5;
            padding-left: $spacer-0;
          }
        }
      }
    }

    &__right {
      padding-right: $spacer-2;
      padding-left: $spacer-0;
    }

    &__download {
      margin-right: auto;
      margin-left: $spacer-0;
    }

    &__remove {
      left: 15px;
      right: auto;
    }

    &__rating {
      direction: ltr;
      text-align: right;

      .ifp-icon {
        margin-right: $spacer-0;
        margin-left: $spacer-1;
      }
    }
  }

  .ifp-analysis {
    &--hybrid {
      .ifp-analysis {
        &__remove {
          left: 10px;
          right: auto;
        }
      }
    }
  }
}
