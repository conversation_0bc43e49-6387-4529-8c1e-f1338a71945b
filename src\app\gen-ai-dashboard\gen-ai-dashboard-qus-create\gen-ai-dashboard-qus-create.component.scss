@use "../../../assets/ifp-styles/abstracts/index" as *;
.ifp-gen-ai-dashboard-qus-create {
  position: relative;
  background-color: $ifp-color-grey-18;
  border-radius: 10px;
  padding:$spacer-3 ;
  &__text {
    color: $ifp-color-grey-14;
    margin-bottom: $spacer-2;
    display: flex;
    justify-content: space-between;
    align-items: flex-end;
  }
  &__save {
    width: 32px;
    height: 32px;
    border: 1px solid $ifp-color-grey-13;
    display: flex;
    justify-content: center;
    align-items: center;
    top: 2px;
    border-radius: 50%;
    cursor: pointer;
    margin-inline-end: $spacer-2;
    &:last-child {
      margin-inline-end: $spacer-0;
    }
    &::before {
      position: relative;
      top: 2px;
    }
  }
  &__error{
    margin-bottom: $spacer-3;
  }
  &__textarea {
    display: block;
    width: 100%;
    padding: ($spacer-2 + 2px) $spacer-3;
    resize: none;
    border-radius: 5px;
    color: $ifp-color-secondary-grey;
    margin-bottom: $spacer-3;
    font-size: $ifp-fs-4;
    font-weight: $fw-bold;
    border: 1px solid $ifp-color-grey-7;
    min-height: 45px;
    background-color: $ifp-color-section-white;
    &--answer {
      min-height: 150px;
    }
  }
  &__dropdown-wrapper {
    display: flex;
    flex-wrap: wrap;
  }
  &__dropdown{
    width: 50%;
    display: flex;
    align-items: center;
    margin-bottom: $spacer-3;
    &--100 {
      width: 100%;
    }
  }
  &__dropdown-text {
    min-width: 150px;
    color: $ifp-color-grey-14;
  }
  &__input {
    border: 1px solid $ifp-color-grey-7;
    padding: $spacer-2;
    border-radius: 10px;
    margin-inline-start: $spacer-3;
    color: $ifp-color-grey-14;
    background-color: $ifp-color-section-white;
  }
  &__plus {
    background-color: $ifp-color-blue-menu;
    border-radius: 50%;
    padding: $spacer-3;
    width: 40px ;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: $ifp-color-white;
    position: absolute;
    left: calc(50% - 20px) ;
    bottom: -20px;
    cursor: pointer;
  }
  &__plus-icon {
    position: relative;
    top:2px;
  }
}
:host::ng-deep {
  .ifp-gen-ai-dashboard-qus-create {
    &__drop {
      .ifp-dropdown {
        min-width: 240px;
       max-width: 240px;
      }
    }
  }
}

:host-context(.ifp-dark-theme) {

}
