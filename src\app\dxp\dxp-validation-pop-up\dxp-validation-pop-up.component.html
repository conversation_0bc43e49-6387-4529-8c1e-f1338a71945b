<div class="ifp-dxp-validation-pop-up" [ngClass]="{'ifp-dxp-validation-pop-up--successPopup': successPopup()}">
  <div class="ifp-dxp-validation-pop-up__icons-cross">
    <em class="ifp-icon ifp-icon-cross" (click)="close.emit()"></em>
</div>
  <div class="ifp-dxp-validation-pop-up__icons-wrapper">
    <div class="ifp-dxp-validation-pop-up__icons">  <em [class]="'ifp-icon '+icon()"></em></div>
  </div>
  <h3 class="ifp-dxp-validation-pop-up__title">{{heading()| translate }}</h3>
  <p class="ifp-dxp-validation-pop-up__desc"> {{desc() | translate}}</p>
  @if (!successPopup()) {
      <div  class="ifp-dxp-validation-pop-up__text-wrapper">
    <div class="ifp-dxp-validation-pop-up__text">
{{'While working on visualizations in Bayaan using your data product, we encountered a few issues that are affecting the accuracy and usability of the visuals. Could you please look into the following and let us know once they’re resolved?

Identified Issues:

Inconsistent Date Formats – The datetime field contains mixed formats (e.g., YYYY-MM-DD and DD/MM/YYYY), which is causing parsing issues during visualization.
Null Values in Key Dimensions – Certain dimension fields expected to be complete contain nulls, which breaks group-by aggregations.
Duplicate Records – The dataset includes duplicates that skew aggregation metrics such as totals and averages.
Incorrect Data Types – Some numeric fields are stored as strings, preventing correct aggregations and filtering.
Unexpected Category Values – Categorical fields include unexpected or misspelled values (e.g., "Completed", "compeleted", "complete"), affecting chart grouping.'| translate}}
    </div>
<div>
  <div class="ifp-dxp-validation-pop-up__btns">
    <span class="ifp-dxp-validation-pop-up__save-as-Draft">{{'Save as Draft'| translate}}</span>
    <div>
      <ifp-button [buttonClass]="buttonClass.secondary " class="ifp-dxp-validation-pop-up__btn" [label]="'Delete' | translate"></ifp-button>
      <ifp-button [buttonClass]="buttonClass.hoverBlue" class="ifp-dxp-validation-pop-up__btn" [label]="'Report Issue' | translate"></ifp-button>
    </div>
  </div>


</div>
</div>
  } @else {
        <div class="ifp-dxp-validation-pop-up__success-btns">
      <ifp-button [buttonClass]="buttonClass.secondary " class="ifp-dxp-validation-pop-up__btn" [label]="'Go back to Listing' | translate"></ifp-button>
      <ifp-button [buttonClass]="buttonClass.hoverBlue" class="ifp-dxp-validation-pop-up__btn" [label]="'Create new KPI' | translate"></ifp-button>
    </div>
  }

</div>
