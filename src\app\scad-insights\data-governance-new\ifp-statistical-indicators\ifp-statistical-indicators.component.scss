@use "../../../../assets/ifp-styles/abstracts/index" as *;
$line-width: $spacer-3;
%border-left {
  content: "";
  width: 8px;
  position: absolute;
  top: 50%;
  left: 0;
  transform: translateY(-50%);
  border-radius: 0 5px 5px 0;
}
.ifp-sv {
  $gap: $spacer-4;
  $root-connect-width: 110px;
  display: flex;
  align-items: center;
  border-radius: 10px;
  background-color: $ifp-color-section-white;
  padding: $spacer-4;
  overflow: hidden;
  &__box-count {
    font-size: $ifp-fs-9;
    font-weight: $fw-bold;
    margin-top: $spacer-1;
  }
  &__text {
    font-size: $ifp-fs-4;
    color: $ifp-color-grey-14;
    font-weight: $fw-regular;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  &__root-main {
    margin-inline-start: -10%;
  }
  &__branch-wrapper {
    width: 100%;
    margin-inline-start: $root-connect-width;
    position: relative;
    z-index: 1;
    &::after {
      content: "";
      width: $root-connect-width;
      height: 50%;
      background-image: url(../../../../assets/images/data-governance/curve-join.svg);
      background-size: contain;
      background-position: center;
      background-repeat: no-repeat;
      position: absolute;
      top: 50%;
      right: 100%;
      transform: translateY(-50%);
    }
  }
  &__branch-root {
    min-width: 200px;
    box-shadow: 0 2px 2px $ifp-color-black-16;
    padding: $spacer-3;
    margin-inline-end: $spacer-3;
    border-radius: 10px;
  }
  &__box-wrapper {
    width: calc(33.33% - (2 * $spacer-3));
    margin: $spacer-0 $spacer-3;
    display: flex;
    flex-direction: column;
    justify-content: center;
    .ifp-sv__box {
      width: 100%;
      margin: $spacer-2 $spacer-0;
    }
  }
  &__box {
    width: calc(33.33% - (2 * $spacer-3));
    padding: $spacer-3 $spacer-3 $spacer-3 $spacer-5;
    border: 3px solid $ifp-color-pale-grey;
    border-radius: 20px;
    margin: $gap $spacer-3;
    position: relative;
    background-color: $ifp-color-white;
    box-shadow: 0 48px 50px -30px rgba(212, 220, 229, 0.46);
    &::after {
      height: 50px;
      @extend %border-left;
    }
  }
  &__sub-branch {
    display: flex;
    width: 100%;
  }
  &__branch {
    height: 335px;
    display: flex;
    align-items: center;
    margin: $spacer-0 (-$spacer-3);
    &--top {
      .ifp-sv {
        &__box {
          &::after {
            background-color: $ifp-color-green;
          }
          &::before {
            content: "";
            width: 30px;
            border-inline-end: 6px solid $ifp-color-pale-grey;
            position: absolute;
            left: 50%;
            transform: translateX(-50%);
            margin-inline-start: -6px;
          }
          &:nth-child(odd)::before {
            height: $gap + 6px;
            border-bottom: 6px solid $ifp-color-pale-grey;
            border-bottom-right-radius: 40px;
            top: 100%;
          }
          &:nth-child(even)::before {
            height: $gap + 6px;
            bottom: 100%;
            border-top: 6px solid $ifp-color-pale-grey;
            border-top-right-radius: 40px;
          }
          &--root::before {
            content: none;
          }
        }
        &__sub-branch {
          flex-wrap: wrap;
          height: 100%;
          flex-direction: column;
          justify-content: center;
          position: relative;
          &::before {
            content: "";
            width: calc(100% - (33.33%/2) + $spacer-2);
            height: 6px;
            background-color: $ifp-color-pale-grey;
            position: absolute;
            top: 50%;
            left: (-$spacer-3);
            transform: translateY(-50%);
          }
        }
      }
    }
    &--bottom {
      .ifp-sv {
        &__box {
          &::after {
            background-color: $ifp-color-orange-dark;
          }
          &--root::before {
            content: none;
          }
        }
        &__branch-root {
          background-color: $ifp-color-violet-2;
        }
        &__box-wrapper {
          position: relative;
          &::after {
            content: "";
            width: 37px;
            height: 69%;
            background-image: url(../../../../assets/images/data-governance/curve-join-3.svg);
            background-size: contain;
            background-position: center;
            background-repeat: no-repeat;
            position: absolute;
            top: 50%;
            right: 100%;
            transform: translateY(-50%);
          }
        }
      }
    }
  }
  &__table-wrapper {
    width: calc(66.66% - (2 * $spacer-3));
    margin: $spacer-2 $spacer-3;
    padding: $spacer-2;
    border: 1px solid $ifp-color-grey-13;
    border-radius: 20px;
  }
  &__table-inner {
    max-height: 325px;
    position: relative;
    border-inline-end: $spacer-2 + 2px;
    @include ifp-scroll-y(transparent, $ifp-color-grey-7, 10px, 10px)
  }
  &__table {
    width: 100%;
    color: $ifp-color-primary-grey;
    position: relative;
  }
  &__table-col {
    color: $ifp-color-grey-14;
    padding: $spacer-3;
    border-bottom: 1px solid $ifp-color-grey-13;
    text-align: center;
    &:first-child {
      text-align: start;
      width: 50%;
    }
  }
  &__table-row {
    &--head {
      color: $ifp-color-white-global;
      .ifp-sv__table-col {
        font-size: $ifp-fs-2;
        color: $ifp-color-secondary-grey;
        font-weight: $fw-semi-bold;
        text-transform: uppercase;
        background-color: $ifp-color-pale-grey;
        border-bottom: none;
        padding: ($spacer-2 + 2px) $spacer-3;
        position: sticky;
        top: 0;
        left: 0;
        vertical-align: middle;
        &:first-child {
          border-radius: 10px 0 0 10px;
          &::after {
            @extend %border-left;
            height: 23px;
            background-color: $ifp-color-orange-dark;
          }
        }
        &:last-child {
          border-radius: 0 10px 10px 0;
        }
      }
    }
    &:last-child {
      .ifp-sv__table-col {
        border-bottom: none;
      }
    }
  }
  &__table-tick {
    color: $ifp-color-green-dark;
  }
  &__table-cross {
    color: $ifp-color-red;
  }
}

:host-context([dir="rtl"]) {
  .ifp-sv {
    &::after {
      left: auto;
      right: 200px + $spacer-4 - 4px;
      transform: translateY(-50%) rotateY(180deg);
    }
    &__branch {
      &--top {
        .ifp-sv__box {
          &::before {
            left: auto;
            right: 50%;
            transform: translateX(50%);
          }
          &:nth-child(odd)::before {
            border-bottom-right-radius: 0;
            border-bottom-left-radius: 40px;
          }
          &:nth-child(even)::before {
            border-top-right-radius: 0;
            border-top-left-radius: 40px;
          }
        }
        .ifp-sv__sub-branch::before {
          left: auto;
          right: (-$spacer-3);
        }
      }
    }
    &__table-row {
      &--head {
        .ifp-sv__table-col {
          left: auto;
          right: 0;
          &:first-child {
            border-radius: 0 10px 10px 0;
          }
          &:last-child {
            border-radius: 10px 0 0 10px;
          }
        }
      }
    }
  }
}



