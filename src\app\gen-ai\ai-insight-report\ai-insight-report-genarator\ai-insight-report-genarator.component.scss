@use "../../../../assets/ifp-styles/abstracts/index" as *;

.ifp-library {
  &__table {
    background-color: $ifp-color-white;
    padding: $spacer-5 $spacer-5;
    border-radius: $spacer-5;
    box-shadow: -20px 1  130px rgba(153, 160, 158, 0.9);
  }
  &__head {
    display: flex;
    align-items: center;
    margin-bottom: $spacer-3;
    .ifp-module-heading {
      font-size: $ifp-fs-12 ;
      margin: $spacer-0 auto;
    }
  }
  &__head-back {
    display: flex;
    align-items: center;
  }
  &__head-back-text {
    line-height: 1;
    font-weight: 600;
    margin-left: $spacer-1;
  }
  &__head-icon{
    position: relative;
    top:.5px
  }
  &__title {
    .ifp-module-title {
      color: $ifp-color-primary-grey;
      margin: $spacer-0 auto;
    }
  }
  &__title-sec {
    display: flex;
    justify-content: space-between;

    flex-wrap: wrap;
    &--top-mr {
      margin-top: $spacer-3;
    }
  }
  &__col {
    display: flex;
    align-items: center;
    padding: $spacer-0 $spacer-5;
    width: 300px;
    &--1 {
      width: 50%;
    }
    &--2 {
      justify-content: space-between;
    }
    &--3 {
      // width: 20%;
      justify-content: flex-end;
    }
  }
  &__item {
    max-width: 100%;
    overflow: hidden;
  }
  &__col-img {
    width: 40px;
    min-width: 40px;
  }
  &__col-name {
    margin-bottom: $spacer-1;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    &:last-child {
      margin-bottom: $spacer-0;
    }
  }
  &__date,
  &__name {
    font-size: $ifp-fs-4;
  }
  &__name {
    font-weight: $fw-semi-bold;
  }
  &__row {
    padding: $spacer-3 $spacer-0;
    background-color: $ifp-color-white;
    border-bottom: 1px solid $ifp-color-grey-7;
    display: flex;
    // margin-bottom: $spacer-3;
    &:last-child {
      margin-bottom: $spacer-0;
    }
    &--head {
      border-radius: 10px;
      border: 0;
      background-color: $ifp-color-pale-grey;
      .ifp-library {
        &__col {
          .ifp-icon {
            margin-inline-start: $spacer-3;
          }
          &--2 {
            justify-content: flex-start;
            // border-left: 1px solid;
          }
        }
      }
    }
  }
  &__date {
    color: $ifp-color-grey-9;
    .ifp-icon {
      margin-right: $spacer-2;
    }
  }
  &__button {
    margin: $spacer-0 $spacer-3;
  }
  &__btn-sec {
    display: flex;
    align-items: center;
  }
  &__pagination {
    display: block;
    margin-top: $spacer-3;
  }
  &__tab {
    margin-bottom: $spacer-4;
  }
  &__col-img-wrapper {
    text-align: center;
    margin-inline-end: $spacer-3;
  }
  &__file-type{
    font-size: $ifp-fs-2;
    color: $ifp-color-tertiary-text;
  }
  &__sort-icon{
    display: block;
    font-size: $spacer-2;
    cursor: pointer;
    &--active {
      color: $ifp-color-blue-hover;
      cursor: default;
    }
  }
  &__tab{
    align-items: center;
    display: flex;
    justify-content: end;
    margin-bottom: $spacer-3;
  }

  &__table-body {
    max-height: calc(100vh - 350px);
    min-height: 300px;
    @include ifp-scroll(transparent, $ifp-color-grey-1, 4px, 8px);
  }
  &__col-badge {
    border-radius: 20px;
    padding: $spacer-2 $spacer-3;
    color: $ifp-color-white-global;
  }
  &__status {
    display: flex;
align-items: center;
margin-bottom: $spacer-4;
  }
  &__status-dropdown {
    margin-inline-start: $spacer-2;
    margin-inline-end: $spacer-4;
  }
  &__status-text {
    color: $ifp-color-grey-1;
    margin-inline-end: $spacer-1;
  }
  &__search {
    margin-bottom: $spacer-4;
  }
  &__desc {
    padding: $spacer-0 $spacer-9;
    margin-bottom: $spacer-3;
  }
}

:host::ng-deep {
  .ifp-library {
    &__search {
      .ifp-search-box--box-type {
        height: 40px;
      }
    }
    &__tab {
      .ifp-category-label__txt {
        font-size: $ifp-fs-5;
        font-weight: $fw-semi-bold;
      }
    }
  }
}
:host-context([dir="rtl"]) {
  .ifp-library {
    direction: ltr;
  }
  .ifp-icon-leftarrow {
    display: inline-block;
    transform: rotate(180deg);
  }

}

@include  tablet-horizondal {
  .ifp-library {
    &__tab {
      justify-content: center;
    }


  }
}


@include mobile-tablet {
  .ifp-library__col {
    padding: $spacer-0 12px;
    width: 33.33%;
  }
}
