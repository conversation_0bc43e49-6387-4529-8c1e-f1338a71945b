<div class="ifp-prep-header" [ngClass]="{'ifp-prep-header--adv': isExpertMode}" #header>
  @if(descriptionEnable) {
    <div class="ifp-prep-header__sec-1">
      <h2 class="ifp-prep-header__title">{{title | translate}}</h2>
      <p class="">{{description | translate}}</p>
    </div>
  }
  @if (isExpertMode) {
    <div class="ifp-prep-header__sec-1">
      <div class="ifp-beta-title">
        <!-- @if ((themeService.defaultLang$|async) === 'en') {
          <img src="../../../assets/images/beta-icon.svg" alt="BETA" class="ifp-beta-icon">
        } @else {
          <img src="../../../assets/images/beta-icon-arabic.svg" alt="BETA" class="ifp-beta-icon">
        } -->
        <p class="ifp-prep-header__desc" ><em class="ifp-icon ifp-icon-info-round" [appIfpTooltip]="'Drag the tool onto the canvas to begin.' | translate" ></em></p>
      </div>
    </div>
  } @else {
    <div class="ifp-prep-header__sec-1">
      <!-- @if ((themeService.defaultLang$|async) === 'en') {
        <img src="../../../assets/images/beta-icon.svg" alt="BETA" class="ifp-beta-icon">
      } @else {
        <img src="../../../assets/images/beta-icon-arabic.svg" alt="BETA" class="ifp-beta-icon">
      } -->
    </div>
  }

  <div class="ifp-prep-header__sec-2">
    @if (isExpertMode) {
      <div  class="ifp-prep-header__icon-toggle">
        @if(isToggletools) {
          <em #toolsIcon class="ifp-icon ifp-icon-all-tools ifp-prep-header__icon-tool-modal" [ngClass]="{'ifp-prep-header__icon-tool-modal-active': showToggletools}" (click)="showToggletools = !showToggletools"></em>
        }
        @if(isToggletools ? showToggletools : true) {
        <div #tools class="ifp-prep-header__icon-wrapper" [ngClass]="{'ifp-prep-header__icon-wrapper-modal': isToggletools}">
          <em class="ifp-icon ifp-icon-zoom-out ifp-prep-header__icon-tool" (click)="toggleTool(canvasTools.zoomOut)" [appIfpTooltip]="'Zoom out' | translate" [placement]="'topLeft'" [zIndex]="1000"></em>
          <em class="ifp-icon ifp-icon-zoom-in ifp-prep-header__icon-tool" (click)="toggleTool(canvasTools.zoomIn)" [appIfpTooltip]="'Zoom in' | translate" [placement]="'topLeft'" [zIndex]="1000"></em>
          <em class="ifp-icon ifp-icon-grid ifp-prep-header__icon-tool" (click)="toggleTool(canvasTools.grid)" [appIfpTooltip]="'Grid' | translate" [placement]="'topLeft'" [zIndex]="1000"></em>
          <em class="ifp-icon ifp-icon-fullscreen-box ifp-prep-header__icon-tool ifp-prep-header__icon-tool--hide-ipad" (click)="toggleTool(canvasTools.fullScreen)" [appIfpTooltip]="'Full screen' | translate" [placement]="'topLeft'" [zIndex]="1000" ></em>
          <em class="ifp-icon ifp-icon-save ifp-prep-header__icon-tool" [ngClass]="{'ifp-prep-header__sec-item--disabled' : disableWorkFlow()}" (click)="save()" [appIfpTooltip]="'Save' | translate" [placement]="'topLeft'" [zIndex]="1000"></em>
        </div>
        }
      </div>
      <!-- <a (click)="openBasic()" class="ifp-prep-header__btn ifp-prep-header__btn--secondary ifp-prep-header__sec-item"><span class="ifp-prep-header__btn-text"><em class="ifp-icon ifp-icon-bulb ifp-prep-header__btn-icon"></em>{{'Basic Mode' | translate}}</span></a> -->
      <a (click)="goToLaibrary()" class="ifp-prep-header__btn ifp-prep-header__btn--secondary ifp-prep-header__sec-item" [ngClass]="{'ifp-prep-header__sec-item-icon': isToggletools}"><span class="ifp-prep-header__btn-text"><em class="ifp-icon ifp-icon-library ifp-prep-header__btn-icon"></em>{{(isToggletools ? '' : 'My Bayaan Workflows') | translate}}</span></a>
      @if(!isToggletools) {
        <app-ifp-tab-button class="ifp-prep-header__sec-item-tab" [tabItems]="tabItems" (ifpTabClick)="onModeChange(true)"></app-ifp-tab-button>
      } @else {
        <app-ifp-db-dropdown [isMultiSelect]="false" [options]="tabItems" (singleSelected)="onModeChange(false, $event)"
        [selectedSingleItem]="selectedTab" [defaultSelect]="false" [key]="'label'" [iconEnable]="true"></app-ifp-db-dropdown>

      }
      <ifp-button class="ifp-prep-header__button" (ifpClick)="openHelp()" [label]="'User Manual' | translate"
      [buttonClass]="buttonClass.secondary" [iconPosition]="postion.right" [iconClass]="'ifp-icon-info-round'"></ifp-button>
      <ifp-button (ifpClick)="runEvent()" class="ifp-prep-header__sec-item" [label]="workFlowRunning() ? ('Cancel' | translate):('Run' | translate)" [iconClass]="workFlowRunning() ? 'ifp-icon-round-cross' : 'ifp-icon-play-round'" [buttonClass]="buttonClass.primary+' '+buttonClass.large + (disableWorkFlow()&& !workFlowRunning() ? (' '+buttonClass.disabled): '')"></ifp-button>
          }
    @if(!isExpertMode) {
      <!-- <div class="ifp-prep-header__btn ifp-prep-header__btn--rnd ifp-prep-header__sec-item" [ngClass]="{'ifp-prep-header__btn--active': isExpertMode}" (click)="onModeChange(true)">
        <span class="ifp-prep-header__btn-text"><em class="ifp-icon ifp-icon-bulb ifp-prep-header__btn-icon"></em>{{'Expert Mode' | translate}}</span>
      </div> -->
      @if(!isToggletools) {
        <app-ifp-tab-button [tabItems]="tabItems" (ifpTabClick)="onModeChange(true)"></app-ifp-tab-button>
      } @else {
        <app-ifp-db-dropdown [isMultiSelect]="false" [options]="tabItems" (singleSelected)="onModeChange(false, $event)"
        [selectedSingleItem]="selectedTab" [defaultSelect]="false" [key]="'label'" [iconEnable]="true"></app-ifp-db-dropdown>

      }
      <ifp-button class="ifp-prep-header__button" (ifpClick)="openHelp()" [label]="'User Manual' | translate"
    [buttonClass]="buttonClass.secondary" [iconPosition]="postion.right" [iconClass]="'ifp-icon-info-round'"></ifp-button>
    @if(_prepService.showProcessBtn()) {
      <ifp-button [iconClass]="'ifp-icon-rightarrow'" [loader]="_prepService.loaderProcess()" (ifpClick)="process()" class="ifp-prep-header__process-btn" [label]=" 'Process' | translate" [buttonClass]="buttonClass.primary+' '+buttonClass.large + (_prepService.processDisable()? (' '+buttonClass.disabled): '')"></ifp-button>

    }

    }

    <!-- <em class="ifp-icon ifp-icon-help-circle-outline ifp-prep-header__icon ifp-prep-header__sec-item" (click)="openHelp()"></em> -->
  </div>

</div>
<app-ifp-modal [modalClass]="'ifp-modal__template-help'" #help [overlayClass]="'ifp-modal__bg-grey'">
    <ifp-help (closeHelp)="closeHelp()" [helpData]="isExpertMode ? advancedHelpData : basicHelpData" [mainHead]="isExpertMode ?  'Discover What Advance Data Prep Can Do': 'Discover What Data Prep Can Do'"></ifp-help>
</app-ifp-modal>

<app-ifp-modal [overlayClass]="'ifp-modal__bg-grey'" [modalClass]="'ifp-modal__import-indicators'" [isClose]="true" #libraryListModal>
  @if (isLibraryModelOpen()) {
  <ifp-prep-library [isModal]="true" [isAdvance]="true" [viewTab]="false" (selectFile)="selectFile($event)" (closeModal)="closeModel($event)"
    ></ifp-prep-library>
  }
</app-ifp-modal>
