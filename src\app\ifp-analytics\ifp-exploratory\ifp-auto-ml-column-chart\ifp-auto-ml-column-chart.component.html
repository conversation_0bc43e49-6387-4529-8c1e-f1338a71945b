<div class="ifp-auto-ml-cl">

@if (bar) {
  <div  class="ifp-auto-ml-cl__bar-wrapper">
    @for (item of columnList; track item) {
      @if (this.columnsListObject?.[item]) {
        <div class="ifp-auto-ml-cl__bar">
          <div class="ifp-auto-ml-cl__bar-overlay"  [appIfpTooltip]="('% of important score-' | translate) +( ((this.columnsListObject?.[item]?.importance_score ?? 0) * 100) | number: '1.0-1')">

          </div>
          <ifp-progress-value [disableTooltip]="true"    [color]="'#3BD6AD'" [value]="false" [percentage]="true" [progress]="((this.columnsListObject?.[item]?.importance_score ?? 0) * 100)" [progressValue]="((this.columnsListObject?.[item]?.importance_score ?? 0) * 100)" [rect]="true"></ifp-progress-value>

        </div>

      } @else {
        <div  class="ifp-auto-ml-cl__bar">
          -
        </div>
      }

    }

  </div>
}@else {
  <div  class="ifp-auto-ml-cl__number-wrapper" [ngClass]="{'ifp-auto-ml-cl__number-wrapper--selection': selection}">
    @for (item of columnList; track item) {
      @if (this.columnsListObject?.[item]) {
        <div class="ifp-auto-ml-cl__number ifp-auto-ml-cl__number--black"  [appIfpTooltip]="('value of important score-' | translate)+ ' ' +this.columnsListObject?.[item]?.importance_score" [style.background]="((this.columnsListObject?.[item]?.importance_score ?? 0) * 100)| ifpMlColor: true">
          {{this.columnsListObject?.[item]?.importance_score}}
        </div>
      } @else {
        <div  class="ifp-auto-ml-cl__number">
          -
        </div>
      }

    }

  </div>
}



</div>
