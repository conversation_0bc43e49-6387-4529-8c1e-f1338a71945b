import { SubSink } from 'subsink';
import { Directive, Input, ElementRef, HostListener, Renderer2, OnInit, On<PERSON><PERSON>roy, ChangeDetectorRef } from '@angular/core';
import {tooltipMode, tooltipPosition } from '../constants/tooltip.constant';
import { fromEvent} from 'rxjs';
import { GetWidthService } from '../services/width/get-width.service';
import { ThemeService } from '../services/theme/theme.service';
import { PopService } from '../services/popperService/popper.service';
@Directive({
  selector: '[appIfpTooltip]',
  standalone: true
})
export class IfpTooltipDirective implements OnInit, OnDestroy{
  @Input('appIfpTooltip') tooltipTitle: string ='';
  @Input() placement: 'topLeft' | 'bottomRight' | 'bottomLeft' | 'auto' | undefined = 'auto';
  @Input() delay: number = 5000;
  @Input() mode:string = tooltipMode.mode1;
  @Input() disableTooltip: boolean = false;
  @Input() fontSize!: number;
  @Input() element!: ElementRef | any;
  @Input() dynamic =false;
  @Input() paddingMain = 200;
  @Input() extraSpaceBottom = 5;
  @Input() zIndex =998;
  @Input() extraSpaceTop = 10;
  @Input() scrollWidth = true;
  @Input() customElement =false;
  @Input() tooltipHead!: string;
  @Input() textLeft: boolean = false;
  @Input() maxWithNone = false;
  public popper = new PopService();
  public rtl = false;
  public tooltip!: HTMLElement | null | any;
  public tooltipArrow!: HTMLElement | null | any;
  public offset = 6;
  public offsetRight = 17;
  public subs = new SubSink();
  public timeOut!: NodeJS.Timeout;
  public mouserLeave =fromEvent(this.el.nativeElement, 'pointerleave');
  public instance!:{destroy : () => object};

  constructor(private el: ElementRef, private renderer: Renderer2, private _widthService: GetWidthService, private _theme: ThemeService, private _cdr: ChangeDetectorRef) { }

  @HostListener('pointerover', ['$event']) onMouseEnter(event: MouseEvent) {
    if (!this.tooltip && !this.disableTooltip) {
      if (this.dynamic) {
        const size = Math.round(this._widthService.getTextWidth(this.tooltipTitle, this.fontSize));
        const elementWidth = this.customElement ?  this.element?.nativeElement?.clientWidth :  this.el?.nativeElement?.clientWidth;
        if (size>elementWidth) {
          this.show(event);
        }
      } else {
        this.show(event);
      }

    }
  }


  ngOnInit(): void {
    this.subs.add(
      this.mouserLeave
        .subscribe(() => {
          if (this.tooltip ) {
            this.hide();
          }
        }),
      this._theme.defaultLang$.subscribe((lang) => {
        this.rtl = lang==='ar' ? true : false;
      })
    );
  }



  // hide create tooltip
  show(_event:  MouseEvent) {
    this.create();
    // this.setPosition(event);
    this.renderer.addClass(this.tooltip, 'ifp-tooltip--show');
    this.renderer.setStyle(this.tooltip, 'z-index', this.zIndex);
    this.timeOut = setTimeout(()=>{
      this.remove();
    }, this.delay);
  }

  // hide tooltip
  hide() {
    const container = document.fullscreenElement ? document.fullscreenElement : document.body;
    clearTimeout(this.timeOut);
    this.renderer.removeClass(this.tooltip, 'ifp-tooltip--show');
    this.renderer.removeChild(container, this.tooltip);
    this.tooltip = null;
  }

  // create tooltip
  create() {
    const container = document.fullscreenElement ? document.fullscreenElement : document.body;


    this.tooltip = this.renderer.createElement('span');
    if (this.tooltipHead && this.tooltipHead !== '') {
      const title = this.renderer.createElement('span');
      this.renderer.addClass(title, 'ifp-tooltip__title');
      this.renderer.appendChild(title, this.renderer.createText(this.tooltipHead));
      this.renderer.appendChild(this.tooltip, title);
    }
    this.renderer.appendChild(
      this.tooltip,
      this.renderer.createText(this.tooltipTitle)
    );
    this.tooltipArrow =   this.renderer.createElement('span');


    this.renderer.appendChild(container, this.tooltip);

    this.renderer.addClass(this.tooltip, 'ifp-tooltip');
    if (this.maxWithNone) {
      this.renderer.addClass(this.tooltip, 'ifp-tooltip--max-width-none');
    }
    if (this.textLeft) {
      this.renderer.addClass(this.tooltip, 'ifp-tooltip--text-left');
    }

    const hostPos = this.el.nativeElement.getBoundingClientRect();
    const tooltipPos = this.tooltip.getBoundingClientRect();
    const currentPosition = this.calculateTop(hostPos.y, tooltipPos.height) ?tooltipPosition.bottom : tooltipPosition.top;
    this.renderer.addClass( this.tooltipArrow, `ifp-tooltip--${currentPosition}`);
    this.renderer.appendChild(
      this.tooltip,
      this.tooltipArrow
    );
    if (this.instance) {
      this.instance?.destroy();
    }
    this.instance = new PopService().createPopper(this.el.nativeElement, this.tooltip, {
      placement: currentPosition,
      modifiers: [
        {
          name: 'offset',
          options: {
            offset: [0, 5]
          }
        },
        {
          name: 'arrow',
          options: {
            element: this.tooltipArrow,
            padding: 5
          }
        }
      ]
    });
    this._cdr.detectChanges();
  }

  // used to set position left or right of tooltip
  checkLeftAndRight(x: number) {
    if ((window.innerWidth/2 > x)) {
      return 'Left';
    }
    return 'Right';
  }

  // used to set position of tooltip

  // setPosition(event:  MouseEvent) {
  //   const hostPos = this.el.nativeElement.getBoundingClientRect();
  //   const tooltipPos = this.tooltip.getBoundingClientRect();
  //   const container = document.fullscreenElement ? document.fullscreenElement : document.body;
  //   const scrollPos = window.pageYOffset || document.documentElement.scrollTop || container.scrollTop || 0;
  //   if (this.calculateTop(hostPos.y, tooltipPos.height)) {
  //     this.renderer.addClass( this.tooltipArrow, `ifp-tooltip--${tooltipPosition.bottom}`);
  //     this.renderer.setStyle(this.tooltip, 'top', `${hostPos.y + scrollPos+ hostPos.height + this.extraSpaceBottom}px`);
  //   } else {
  //     this.renderer.addClass( this.tooltipArrow, `ifp-tooltip--${tooltipPosition.top}`);
  //     if (this.tooltipHead && this.tooltipHead !== '') {
  //       this.renderer.setStyle(this.tooltip, 'top', `${hostPos.y + scrollPos - hostPos.height - this.extraSpaceTop - (tooltipPos.height/2) - 15}px`);
  //     } else {
  //       this.renderer.setStyle(this.tooltip, 'top', `${hostPos.y + scrollPos - hostPos.height - this.extraSpaceTop}px`);
  //     }
  //   }

  //   if ((window.innerWidth/2 > event.x)) {

  //     const left = hostPos.x - (tooltipPos.width/2) + (hostPos.width/2) + ( this.scrollWidth ?((window.innerWidth - document.documentElement.clientWidth)/2)  * (this.rtl ? -1 : 1): 0);
  //     this.renderer.setStyle( this.tooltipArrow, 'left', `${left< 0 ? `calc(50% + ${left}px)` : '50%'}`);

  //     this.renderer.setStyle(this.tooltipArrow, 'transform', 'translateX(-50%)');
  //     this.renderer.setStyle(this.tooltip, 'left', `${left< 0 ? 0 : left}px`);
  //     this.renderer.setStyle(this.tooltip, 'right', 'unset');
  //   } else {
  //     this.renderer.setStyle(this.tooltip, 'left', 'unset');
  //     this.renderer.setStyle(this.tooltipArrow, 'transform', 'translateX(50%)');
  //     const right =  (container.clientWidth  - hostPos.x -  (hostPos.width/2)- (tooltipPos.width/2) - ( this.scrollWidth ?((window.innerWidth - document.documentElement.clientWidth)/2) * (this.rtl ? -1 : 1) : 0));

  //     this.renderer.setStyle( this.tooltipArrow, 'right', `${right< 0 ? `calc(50% + ${right}px)` : '50%'}`);

  //     this.renderer.setStyle(this.tooltip, 'right', `${right< 0 ? 0 : right}px`);
  //   }
  //   this.renderer.appendChild(
  //     this.tooltip,
  //     this.tooltipArrow
  //   );
  // }

  calculateTop(y: number, height: number)  {
    let bottom = false;
    switch (this.placement) {
    case 'topLeft':
      bottom = false;
      break;
    case 'bottomRight':
      bottom = true;
      break;
    case 'bottomLeft':
      bottom = true;
      break;
    default:
      this.placement = undefined;
      break;
    }
    return this.placement ? bottom : this.paddingMain > y+ height;
  }

  remove() {
    clearTimeout(this.timeOut);
    if (this.tooltip) {
      const container = document.fullscreenElement ? document.fullscreenElement : document.body;
      this.renderer.removeChild(container, this.tooltip);
    }
  }

  ngOnDestroy(): void {
    this.subs.unsubscribe();
    this.  remove();
  }

}
