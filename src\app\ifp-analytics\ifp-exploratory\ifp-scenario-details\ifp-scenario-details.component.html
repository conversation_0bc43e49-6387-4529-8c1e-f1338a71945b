<div class="ifp-scenario">
  <div class="ifp-container">
    <div class="ifp-scenario__heading-outer">
      <ifp-back-button [isGoBack]="true"></ifp-back-button>
      <div class="ifp-scenario__dropdown-wrapper">
        <div class="ifp-scenario__dropdown" (click)="dropdown()" appOutsideClick (outsideClick)="outsideClick()">
          <em class="ifp-icon ifp-icon-library ifp-scenario__dropdown-icon"></em> {{"My Bayaan Scenarios" | translate}}

        </div>
        <ul class="ifp-scenario__dropdown-list"  [ngClass]="{'ifp-scenario__dropdown-list--show': dropdownEnable}" >
          @for (item of scenarioList; track $index) {
            <li class="ifp-scenario__dropdown-item " (click)="goToScenario(item)">
                {{item.name}}
            </li>

          }
          <li class="ifp-scenario__dropdown-item ifp-scenario__dropdown-view " (click)="viewAll()">
            {{'View all' | translate}}     <em class="  ifp-scenario__dropdown-view-icon ifp-icon ifp-icon-rightarrow"></em>
        </li>
        </ul>
      </div>

    </div>
    <div class="ifp-scenario__details">
      @if (!loader()) {
      <div  class="ifp-scenario__header" >
        @if(edit) {
          <app-ifp-input-edit  class="ifp-scenario__edit" (cancelEdit)="cancelEdit()" (validValueChange)="validValueChange($event)"  [data]="heading" [light]="true" [length]="60" ></app-ifp-input-edit>
        }@else {
          <div class="ifp-scenario__header-text" (click)="editHeading()">
            {{heading}}
            @if(isEditScenario) {
              <em class=" ifp-icon ifp-icon-edit ifp-scenario__header-icon" [appIfpTooltip]="'edit' | translate"></em>
            }

          </div>

           }
           @if(isEditScenario) {
           <ifp-button [label]="'Save Scenario'" [buttonClass]="buttonClass.primary" [iconClass]="'ifp-icon-rightarrow'" (ifpClick)="saveScenario()"></ifp-button>
           } @else {
            <ifp-button [label]="'Edit'" [buttonClass]="buttonClass.primary" [iconClass]="'ifp-icon-edit'" (ifpClick)="editScenario()"></ifp-button>
           }
      </div>

      <div  class="ifp-scenario__header-desc" >
        @if(isDescriptionEdit) {
          <app-ifp-input-edit  class="ifp-scenario__desc-edit" (cancelEdit)="descriptionEditCancel()" (validValueChange)="validDescriptionValueChange($event)"  [data]="desc ?? ''" [light]="true" [length]="250" ></app-ifp-input-edit>
        }@else {
          <div class="ifp-scenario__header-desc-text" (click)="editHeadingDescription()">
            {{desc}}
            @if(isEditScenario) {
              <em class=" ifp-icon ifp-icon-edit ifp-scenario__header-desc-icon" [appIfpTooltip]="'edit' | translate"></em>
            }

          </div>

           }

         </div>
        }
      @if (loader()) {
        <div class="ifp-scenario__loader">
          <app-ifp-spinner></app-ifp-spinner>
        </div>
       } @else {

      <div class="ifp-scenario__detail-inner">
        <div class="ifp-scenario__chart">
          <div class="ifp-scenario__rating">
            <div  class="ifp-scenario__rating-wrapper" >
              <div class="ifp-scenario__rating-tooltip" [extraSpaceTop]="-40" [appIfpTooltip]="currentIndicatorValue.toString()"></div>
              <div class="ifp-scenario__rating-text">
                {{'Last Actual Value'  | translate}}
              </div>
              <div class="ifp-scenario__rating-number">
                {{currentIndicatorValue  | shortNumber}}
              </div>
              <!-- <div class="ifp-scenario__icon">
                @if( currentIndicatorValue > currentIndicatorForecastValue) {
                  <em  class="ifp-icon ifp-icon-triangle-up ifp-scenario__icon--up"></em>
              }@else {
                <em  class="ifp-icon ifp-icon-triangle ifp-scenario__icon--down"></em>
              }

              </div> -->



            </div>
            <div  class="ifp-scenario__rating-wrapper" >
              <div class="ifp-scenario__rating-tooltip"  [extraSpaceTop]="-40" [appIfpTooltip]="currentIndicatorForecastValue.toString()"></div>
              <div class="ifp-scenario__rating-text">
                {{'First Forecast Value'  | translate}}
              </div>
              <div class="ifp-scenario__rating-number">
                {{currentIndicatorForecastValue | shortNumber}}
              </div>
              <!-- <div class="ifp-scenario__icon">
              @if( currentIndicatorValue < currentIndicatorForecastValue) {
                <em  class="ifp-icon ifp-icon-triangle-up ifp-scenario__icon--up"></em>
              }@else {
                <em  class="ifp-icon ifp-icon-triangle ifp-scenario__icon--down"></em>
              }
</div> -->
            </div>
            <div  class="ifp-scenario__rating-wrapper" >
              <div class="ifp-scenario__rating-tooltip"  [extraSpaceTop]="-40" [appIfpTooltip]="percentageDifferenceAbs.toString()"></div>
              <div class="ifp-scenario__rating-text">
                {{'% Change' | translate}}
                <!-- {{'% change'  | translate:{int: currentIndicatorDate | date:dateFormat , for:currentIndicatorForecastDate | date:dateFormat } }} -->
              </div>
              <div class="ifp-scenario__rating-number">
                <div class="ifp-scenario__icon">
                  @if( 0 < percentageDifference) {
                    <em  class="ifp-icon ifp-icon-triangle-up ifp-scenario__icon--up"></em>
                  }@else {
                    <em  class="ifp-icon ifp-icon-triangle ifp-scenario__icon--down"></em>
                  }
    </div>       {{percentageDifferenceAbs | number: '1.0-1'}} %
              </div>

            </div>
            <div  class="ifp-scenario__rating-wrapper">
              <div class="ifp-scenario__rating-text">
                {{'Frequency'  | translate}}
              </div>
              <div class="ifp-scenario__rating-number">
                {{frequency | translate}}
              </div>
            </div>
            <div  class="ifp-scenario__rating-wrapper">
              <div class="ifp-scenario__rating-text">
                {{'Forecast Horizon'  | translate}}
              </div>
              <div class="ifp-scenario__rating-number">
                {{forecastHorizon }}
              </div>
            </div>
          </div>
            <app-ifp-analytic-line-chart [height]="600" class="ifp-scenario__chart-ele" [chartData]="chartData" [isRangeSelect]="false" #chartComponent [yaxisLabel]="yAxisLabel()" [xAxisLabel]="xAxisLabel()"></app-ifp-analytic-line-chart>
            </div>
        <div class="ifp-scenario__tool-bar">
          @if (!loader()) {
            <ifp-tool-bar [name]="heading" [enableEditDriver]="isEditScenario" [data]="excelData" (driverUpdated)="driverUpdated($event)" (resetToDefault)="resetToDefault()" (dataRangeSelected)="driverChange($event)" [driverList]="driverValue">
                  <div class="ifp-light-theme">
                    <ifp-scenario-details-print [currentIndicatorValue]="currentIndicatorValue" [currentIndicatorForecastValue]="currentIndicatorForecastValue" [forecastHorizon]="forecastHorizon" [percentageDifference]="percentageDifference" [percentageDifferenceAbs]="percentageDifferenceAbs" [frequency]="frequency" [chartData]="chartData" [yAxisLabel]="yAxisLabel" [xAxisLabel]="xAxisLabel"></ifp-scenario-details-print>
                  </div>
            </ifp-tool-bar>
          }

        </div>

      </div>
    }
    </div>
    <div class="ifp-scenario__disclaimer">
      <span class="ifp-scenario__disclaimer--bold">
      <em class="ifp-icon ifp-icon-info-round"></em>
      {{'Disclaimer' | translate}}
      </span>
      <span>{{'This is an ML-generated Scenario and has not been reviewed by subject matter experts. It is intended only for internal (official) use and must not be shared with external parties.' | translate}}</span>

    </div>

  </div>
</div>
<app-ifp-modal [overlayClass]="'ifp-modal__bg-grey'" #modalSuccess >

  <ifp-prep-popup [imageTitlePosition]="'bottom'" [imageText]="'OK'" [processedIcon]="false" [subTitle]="'Select Driver'" [subDesc]="'Modify the relevant drivers to view changes in the projection'" (buttonEventValue)="closeSuccessModal()" (closePopUp)="closeSuccessModal()" [type]="'image'"  [imageUrl]="'../../../../../../assets/images/prep-popup/scenario.svg'" [heading]="'Scenario plotted successfully!'" [desc]="'Poverty reduction'" [processText]="'Proceed'"  ></ifp-prep-popup>

    </app-ifp-modal>
