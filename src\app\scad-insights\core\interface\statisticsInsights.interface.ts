export interface StatisticsInsights {
  id: string;
  ids: string;
  compareFilters: string[];
  valueFormat: string;
  templateFormat: string;
  viewName: string;
  dimension: Dimension;
  dateStart: string;
  value: string;
  yearlyCompareValue: number;
  yearlyChangeValue: number;
  quarterlyCompareValue: number;
  quarterlyChangeValue: number;
  monthlyCompareValue: number;
  monthlyChangeValue: number;
}

interface Dimension {
  INDICATOR_ID: string;
}



export interface StatisticsInsightsList {
  data: StatisticsInsights;
  status?:  boolean,
  errorMessage?: string,
  loader?:  boolean
  }
