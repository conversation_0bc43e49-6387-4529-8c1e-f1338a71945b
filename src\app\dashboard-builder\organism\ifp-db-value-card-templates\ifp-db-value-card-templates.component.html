<div class="ifp-db-card-template">
  @for (item of valueCardTemplates; track $index) {
    <div class="ifp-db-card-template__item" [ngClass]="{'ifp-db-card-template__item--selected': item.name === selectedTemplate()?.name}" (click)="onSelectTemplate(item.name)">
      <ifp-kpi-template-card [cardConfig]="item.config" [isIcon]="true" [isCenter]="true" [isSelected]="item.name === selectedTemplate()?.name" class="ifp-db-card-template__card"></ifp-kpi-template-card>
      <p class="ifp-db-card-template__card-name">{{'KPI '+ +($index+1)}}</p>
    </div>
  }
</div>
