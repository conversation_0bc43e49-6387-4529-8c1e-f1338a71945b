import { ComponentFixture, TestBed } from '@angular/core/testing';

import { DxpFilterBadgeComponent } from './dxp-filter-badge.component';

describe('DxpFilterBadgeComponent', () => {
  let component: DxpFilterBadgeComponent;
  let fixture: ComponentFixture<DxpFilterBadgeComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [DxpFilterBadgeComponent]
    })
    .compileComponents();

    fixture = TestBed.createComponent(DxpFilterBadgeComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
