
export interface PreivewPrep {
  count: number;
  next: string;
  previous: null;
  records: Record<string, string>[];
  object_id: string;
  owner: string;
  created_at: string;
  deleted_at: null;
  name: string;
  description: string;
  metadata?: Metadata;
  storage_backend: string;
  status: string;
  file: string;
  total_rows?:number;
  total_columns?:number;
}

interface Metadata {
  type: string;
  columns: MetadataColumnPrep[];
  read_commence: string;
  read_complete: string;
  write_commence: string;
  write_complete: string;
}

export interface MetadataColumnPrep {
  mean: null | number;
  name: string;
  type: string;
  type_description?: string;
  stddev: null | number;
  max_value?: number | string;
  min_value?: number | string;
  max_length: number;
  min_length: number;
  null_count: number;
  valid_count: number;
  sample_value: number | string;
  unique_count: number;
  null_percentage: number;
  valid_percentage: number;
  unique_percentage: number;
  type_probability: TypeProbability
}

export interface TypeProbability {
  id: number, boolean: number, numeric: number, categorical: number,
  uniqueness
  : number,
  uniqueness_category
  : string
}



export interface PreviewNodeData {
  name: string;
  type: string;
  result_metadata: MetadataColumnPrep[];
  count: number;
  next: string;
  previous: null;
  records: Record<string, string>[];
  total_rows?:number;
  total_columns?:number;
}


