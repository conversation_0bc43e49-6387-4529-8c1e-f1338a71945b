import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
  name: 'bytesConverter',
  standalone: true
})
export class BytesConverterPipe implements PipeTransform {

  transform(value: any): any {
    return this.formatBytes(value);
  }


  formatBytes(bytes: number, decimals: number = 2) {
    if (!+bytes) {
      return '';
    }
    const k = 1024;
    const dm = decimals < 0 ? 0 : decimals;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return `${parseFloat((bytes / Math.pow(k, i)).toFixed(dm))} ${sizes[i]}`;
  }
}
