import { Component, inject, input, InputSignal, output, signal, viewChild, WritableSignal } from '@angular/core';
import { AiInsightCompareModuleComponent } from "../ai-insight-compare-module/ai-insight-compare-module.component";

import { buttonClass } from 'src/app/scad-insights/core/constants/button.constants';

import { AiInsightAnalysisCardComponent } from "../ai-insight-analysis-card/ai-insight-analysis-card.component";
import { AiButtonComponent } from "../../../ifp-widgets/atoms/ai-button/ai-button.component";
import { TranslateModule } from '@ngx-translate/core';
import { AiInsightConsolidatedReportSentimentCardComponent } from "../ai-insight-consolidated-report-sentiment-card/ai-insight-consolidated-report-sentiment-card.component";
import { InsightComparison, InsightSentimentanalysis, KeyDriversData, KeyinsightInsights } from '../interface/insights.interface';
import {  selectDomainIconGetByIds } from 'src/app/scad-insights/store/domain-icon/domain-icon.selector';
import { Store } from '@ngrx/store';
import { ThemeService } from 'src/app/scad-insights/core/services/theme/theme.service';
import { AsyncPipe } from '@angular/common';
import { IfpAiChartCardComponent } from "../../../scad-insights/ifp-chat-bot/ifp-ai-chart-card/ifp-ai-chart-card.component";
import { DomainConsolidated, DomainInsights, ImpactInsights } from '../interface/consolidated.interface';


@Component({
  selector: 'ifp-ai-insight-report-consolidated-domain',
  imports: [AiInsightCompareModuleComponent, AiInsightAnalysisCardComponent,
    AiButtonComponent, TranslateModule, AiInsightConsolidatedReportSentimentCardComponent, AsyncPipe, IfpAiChartCardComponent],
  templateUrl: './ai-insight-report-consolidated-domain.component.html',
  styleUrl: './ai-insight-report-consolidated-domain.component.scss',
  providers:[AsyncPipe]
})
export class AiInsightReportConsolidatedDomainComponent {
  private _store =  inject(Store);
  public _themeService = inject(ThemeService);
  public impacts = input<KeyinsightInsights[]>([]);
  public domainSection =  input<DomainInsights>();
  public report: InputSignal<DomainConsolidated| undefined> = input();
  public previewNot = input(true);
  public compairData = input<InsightComparison>()
  public sentimentanalysis = input<InsightSentimentanalysis>()
  public callComparisonEdit = output<InsightComparison | undefined>();
  public callImpactEdit = output<{impacts: ImpactInsights  | undefined,keyinsights: KeyinsightInsights[]} | undefined>();
  public callSentimentalEdit = output<InsightSentimentanalysis | undefined>();
  public buttonClass = buttonClass;
  public domainIconSelector$ = this._store.select(selectDomainIconGetByIds(0));

  public disableTranslate = signal(true);
  ngOnChanges(): void {
  this.domainIconSelector$ =  this._store.select(selectDomainIconGetByIds(this.domainSection()?.id ?? 0));

  }
}
