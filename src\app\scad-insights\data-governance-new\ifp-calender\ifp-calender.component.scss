@use '../../../../assets/ifp-styles/abstracts' as *;


:host {
  display: block;
}

.ifp-calendar {
  background-color: $ifp-color-white;
  border-radius: 20px;
  width: 100%;

  &__header-wrapper {
    display: flex;
    justify-content: space-between;
    padding: $spacer-3;
    border-bottom: 1px solid $ifp-color-grey-3;
  }

  &__filter-actions {
    display: flex;
    align-items: center;
  }

  &__today-button,
  &__button {
    border: 1px solid $ifp-color-grey-3;
    border-radius: 5px;
    cursor: pointer;
    transition: 0.3s;
    &:hover {
      color: $ifp-color-white-global;
      background-color: $ifp-color-active-blue;
    }
  }

  &__today-button {
    padding: $spacer-2 $spacer-3;
    font-weight: $fw-semi-bold;
    .ifp-icon {
      margin-inline-end: $spacer-2;
    }
  }

  &__button {
    width: 40px;
    height: 40px;
    line-height: 40px !important;
    text-align: center;
    margin-inline-start: $spacer-2;
  }

  &__current-date {
    font-size: $ifp-fs-6;
    font-weight: $fw-semi-bold;
    margin-inline-end: $spacer-2;
    min-width: 180px;
  }

  &__status-wrapper {
    display: flex;
    align-items: center;
  }

  &__status {
    margin-inline-start: $spacer-3;
  }

  &__status-round {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    display: inline-block;
    margin-inline-end: $spacer-2;
  }

  &__status-label {
    font-size: $ifp-fs-3;
    font-weight: $fw-medium;
  }
  &__body-wrapper {
    padding: $spacer-3;
  }
  &__day-wrapper {
    width: 100%;
    border: 1px solid $ifp-color-grey-3;
    background-color: $ifp-color-pale-grey;
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;
  }
  &__day,
  &__cell {
    width: 14.285%;
  }
  &__day {
    padding: $spacer-3;
    display: inline-block;
    text-align: center;
    font-size: $ifp-fs-4;
    font-weight: $fw-semi-bold;
  }
  &__date-wrapper {
    display: flex;
    flex-wrap: wrap;
  }
  &__date {
    width: 30px;
    height: 30px;
    line-height: 30px;
    text-align: center;
    display: inline-block;
    border-radius: 50%;
  }
  &__event {
    color: $ifp-color-active-blue;
    font-weight: $fw-semi-bold;
    background-color: $ifp-color-blue-1;
    padding: $spacer-2;
    margin-top: auto;
    border-radius: 7px;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
  }
  &__progress-sec,
  &__event-link {
    display: none;
    opacity: 0;
    visibility: hidden;
    transition: 0.3s;
  }
  &__progress-sec {
    margin: auto $spacer-0;
  }
  &__event-link {
    cursor: pointer;
    transition: 0.3s;
    &:hover {
      color: $ifp-color-blue-hover;
    }
  }
  &__cell {
    height: 200px;
    border: 1px solid rgba(209, 213, 218, 0.5);
    padding: $spacer-2;
    display: flex;
    flex-direction: column;
    transition: 0.3s;
    &--event {
      .ifp-calendar {
        &__progress-sec,
        &__event-link {
          display: block;
        }
      }
      &:hover {
        background-color: $ifp-color-grey-18;
        .ifp-calendar__progress-sec,
        .ifp-calendar__event-link {
          opacity: 1;
          visibility: visible;
        }
      }
    }
    &--today {
      .ifp-calendar__date {
        color: $ifp-color-white-global;
        background-color: $ifp-color-blue-hover;
      }
    &.ifp-calendar__cell--event {
      .ifp-calendar {
        &__progress-sec,
        &__event-link {
          opacity: 1;
          visibility: visible;
        }
      }
    }
    }
    &--disabled {
      opacity: 0.5;
      pointer-events: none;
    }
  }
  &__cell-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: $spacer-1;
  }
  &__progess {
    display: block;
    margin-bottom: $spacer-2;
    &:last-child {
      margin-bottom: $spacer-0;
    }
    &::ng-deep {
      .ifp-progress-value {
        justify-content: flex-end;
        &__value,
        &__wrapper {
          padding: $spacer-0;
        }
        &__value {
          min-width: 40px;
          text-align: right;
        }
        &__percent {
          margin: $spacer-0;
        }
        &__progress-bar {
          border-radius: 3px 0 0 3px;
          margin-inline-start: auto;
        }
      }
    }
  }
}

:host-context([dir="rtl"]) {
  .ifp-calendar {
    &__progess {
      &::ng-deep {
        .ifp-progress-value__progress-bar {
          border-radius: 0 3px 3px 0;
        }
      }
    }
  }
}
