import { DecimalPipe } from '@angular/common';
import { Pipe, PipeTransform } from '@angular/core';


@Pipe({
  name: 'customNumber',
  standalone: true
})
export class CustomNumberPipe implements PipeTransform {

  constructor(private numPipe: DecimalPipe) { }

  transform(value: any, args?: string): any {
    let formattedValue = value;
    if (args?.includes('number')) {
      const argType= args.split('_');
      formattedValue = this.numPipe.transform(value, argType[1]);
      formattedValue = formattedValue?.replaceAll(',', '');
      return +formattedValue;
    }
    if (args?.includes('percentage')) {
      const argType= args.split('_');
      formattedValue = (formattedValue / 100) * 100;
      formattedValue = this.numPipe.transform(value, argType[1]);
      formattedValue =  Math.abs(formattedValue) === 0 ? '0%' : `${formattedValue}%`;
    }
    return formattedValue;
  }

}
