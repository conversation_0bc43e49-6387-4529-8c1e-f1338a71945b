
import { AfterViewChecked, ChangeDetectionStrategy, ChangeDetectorRef, Component, Input, OnChanges, SimpleChanges, WritableSignal, signal } from '@angular/core';
import { chartConstants } from 'src/app/scad-insights/core/constants/chart.constants';
import { CustomCardService } from 'src/app/scad-insights/core/services/create-dashboard/custom-card.service';
import { DashboardService } from 'src/app/scad-insights/core/services/create-dashboard/dashboard.service';
import { FilterService } from 'src/app/scad-insights/core/services/filter/filter.service';
import { IfpAnalyticLineChartComponent } from '../../../scad-insights/ifp-widgets/ifp-molecules/ifp-analytic-line-chart/ifp-analytic-line-chart.component';
import { IfpCircularBarChartComponent } from '../../../scad-insights/ifp-widgets/ifp-molecules/ifp-circular-bar-chart/ifp-circular-bar-chart.component';
import { IfpPieChartComponent } from '../../../scad-insights/ifp-widgets/ifp-molecules/ifp-pie-chart/ifp-pie-chart.component';
import { PieChartSeries } from 'src/app/scad-insights/core/interface/page.interface';
import { IfpTableComponent } from '../../../scad-insights/ifp-widgets/ifp-molecules/ifp-table/ifp-table.component';

import { ToasterService } from 'src/app/scad-insights/core/services/tooster/ToastrService.service';

@Component({
    selector: 'ifp-custom-dashboard-card',
    templateUrl: './ifp-custom-dashboard-card.component.html',
    styleUrl: './ifp-custom-dashboard-card.component.scss',
    changeDetection: ChangeDetectionStrategy.OnPush,
    imports: [
        IfpAnalyticLineChartComponent,
        IfpCircularBarChartComponent,
        IfpPieChartComponent,
        IfpTableComponent,

    ]
})
export class IfpCustomDashboardCardComponent implements OnChanges, AfterViewChecked {

  @Input() chartType: string = 'line';
  @Input() xAxisLabel: string = 'A';
  @Input() cntType!: string;
  @Input() selectId!: string;
  @Input() dashboardView!: string;


  private filterKeys: any = [];
  public xAxisCatogory = chartConstants.xAxisCatogory;
  public chartCatogory: string[] = [];
  public seriesData: any = [];
  public chartData: any = [];
  public isChange: WritableSignal<boolean> = signal(false);
  public isVisible: WritableSignal<boolean> = signal(false);
  public selectedPeriod: any = {
    id: 'Latest-Readings',
    label: 'Recent',
    unit: chartConstants.RECENT_LABEL,
    value: '',
    isSelected: true
  };

  constructor(private _customCardService: CustomCardService, private _dashboardService: DashboardService, private filterService: FilterService,
    private _cdr: ChangeDetectorRef, private _toaster: ToasterService) {
    this._dashboardService.settingsChanged.subscribe(resp => {
      if (this._dashboardService.customDashboardCardTools.includes(resp.tools) && resp.id == this.selectId) {
        this.ngOnChanges();
      }
    });
  }



  ngOnChanges(): void {
    this.getChartType();
    const singleDimentionChartTypes: string[] = ['line', 'bar', 'column', 'table', 'circular'];
    if (singleDimentionChartTypes.includes(this.chartType)) {
      this.setFilter();
    } else if (this.chartType == 'pie' || this.chartType == 'doughnut') {
      this.createPieChartData();
    }
  }


  getChartType() {
    const index = this._dashboardService.chartSettings[this.cntType]?.findIndex((x: { id: string; }) => x.id == this.selectId);
    if (index >= 0) {
      const type = this._dashboardService.chartSettings[this.cntType][index].chartType;
      this.chartType = type ? type : this.chartType;
    }

  }


  async setFilter() {
    await this.getData();
    this.selectedPeriod = this._dashboardService.getRecentValue(this.cntType, this.selectId);
    if (this.selectedPeriod.unit == chartConstants.RECENT_LABEL) {
      this.chartData = this.chartData?.slice(0, 12);
    }
    this.seriesData = [];
    const cardIndex = this._dashboardService.chartSettings[this.cntType].findIndex((x: { id: string; }) => x.id == this.selectId);
    const xAxis = this._dashboardService.chartSettings[this.cntType][cardIndex].Xaxis;
    const yAxis = this._dashboardService.chartSettings[this.cntType][cardIndex].Yaxis;
    if (this.chartData?.length > 0) {
      this.chartCatogory = this.chartData.map((x: Record<string, any>) => x[xAxis.key] );
    }

    if (yAxis?.length > 0 && this.chartData?.length > 0) {
      this.seriesData = yAxis.map((element: { key: string | number }, index: number) => {
        const values = this.chartData?.map((value: Record<string, any>) => parseFloat(value[element.key])) || [];
        return {
          color: this._dashboardService.getColors(this.cntType, this.selectId, index),
          data: values,
          type: this.chartType != 'circular' ? this.chartType : 'column',
          name: yAxis[index].name,
          spacing: this._dashboardService.gettingSpaceValues(this.cntType, this.selectId),
          xAxisPositions: this._dashboardService.getXaxisPositions(this.cntType, this.selectId),
          legendPositions: this._dashboardService.getLegendPositions(this.cntType, this.selectId),
          isDatalabel: this._dashboardService.getChartSettings(this.cntType, this.selectId, chartConstants.DATALABEL),
          isPrecise: this._dashboardService.getChartSettings(this.cntType, this.selectId, chartConstants.PRESICE_VALUE)
        };
      });
      this._dashboardService.setSeriesLength(this.cntType, this.selectId, this.seriesData);
      this.isVisible.set(false);
      this.isChange.set(true);
    } else {
      this.seriesData = [];
      this._dashboardService.setSeriesLength(this.cntType, this.selectId, this.seriesData);
      this.isVisible.set(false);
      this.isChange.set(true);
    }
  }


  // **for pie Chart Data creation //
  async createPieChartData() {
    await this.getData();
    this.seriesData = [];
    const chartSeries: PieChartSeries = {
      type: 'pie',
      data: []
    };
    if (this.chartData) {
      const keys: string[] = this.objectKeys(this.chartData[0]);
      if (keys?.length > 0) {
        keys.forEach((element: string, index: number) => {
          const data = {
            name: element,
            y: this.chartData[0][element],
            color: this._dashboardService.getColors(this.cntType, this.selectId, index)
          };
          if (!isNaN(data.y) && data.y != null) {
            chartSeries.data.push(data);
          }
          chartSeries.spacing = this._dashboardService.gettingSpaceValues(this.cntType, this.selectId),
            chartSeries.xAxisPositions = this._dashboardService.getXaxisPositions(this.cntType, this.selectId),
            chartSeries.legendPositions = this._dashboardService.getLegendPositions(this.cntType, this.selectId),
            chartSeries.isDatalabel = this._dashboardService.getChartSettings(this.cntType, this.selectId, chartConstants.DATALABEL),
            chartSeries.isPrecise = this._dashboardService.getChartSettings(this.cntType, this.selectId, chartConstants.PRESICE_VALUE);
        });
      }
      this.seriesData.push(chartSeries);
      this.seriesData.type = 'pie';
      this._dashboardService.setSeriesLength(this.cntType, this.selectId, this.seriesData);
      this.isVisible.set(false);
      this.isChange.set(true);
    }
  }

  objectKeys(obj: any): string[] {
    return obj ? Object.keys(obj) : [];
  }

  getData() {
    return new Promise(async resolve => {
      const index = this._dashboardService.chartSettings[this.cntType]?.findIndex((x: { id: string; }) => x.id == this.selectId);
      if (index >= 0) {
        this.chartData = this.dashboardView ? await this.getDataFromApi(index) : this._dashboardService.chartSettings[this.cntType][index].data;
        resolve(this.chartData);
      }
    });
  }

  getDataFromApi(index: number) {
    return new Promise(resolve => {
      const serviceData = this._dashboardService.chartSettings[this.cntType][index];
      this._customCardService.getCustomData(serviceData.dataObjectId).subscribe({
        next: next => {
          this._dashboardService.chartSettings[this.cntType][index].data = next.records;
          resolve(next.records);
        },
        error: error => {
          this._toaster.error(error.message);
        }
      });
    });
  }

  checkChartCategory(){
    return this.chartCatogory.every(x=> x == '');
  }


  ngAfterViewChecked(): void {
    if (this.isChange()) {
      this.isVisible.set(true);
      this.isChange.set(false);
      this._cdr.detectChanges();
    }
  }

}
