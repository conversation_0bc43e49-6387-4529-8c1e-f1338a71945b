@if (!workFlowRunning() && !emptyConnection() && currentConection() && sourceData() && columnListView && columnListView?.length) {
  <div class="ifp-adv-tool__action">
    <div class="ifp-adv-tool__action-inner">
      <ifp-button [label]="'cancel'" (ifpClick)="onCancel()" class="ifp-adv-tool__button ifp-adv-tool__action-btn" [buttonClass]="buttonClass.secondary"></ifp-button>
      <ifp-button [label]="'Save'" (ifpClick)="onSave()" class="ifp-adv-tool__button ifp-adv-tool__action-btn" [buttonClass]="buttonClass.primary"></ifp-button>
    </div>
  </div>
  <form class="ifp-adv-impute" cdkDropListGroup [formGroup]="imputeForm">
  <div class="ifp-adv-imp__form-wrapper">
    <div class="ifp-adv-tool__content ifp-adv-tool__content--scroll" cdkDropList
      [cdkDropListData]="columnListView"
      cdkDropListSortingDisabled
      (cdkDropListDropped)="drop($event, 'remove')">
        @for (column of columnListView; track index; let index = $index) {
          <ifp-data-tool-accordian [title]="column.name" [enableCheckBox]="false" [isDraggable]="true" class="ifp-adv-tool__accordian" cdkDrag>
            <div class="ifp-accord-box__content">
              <div class="ifp-adv-tool__edit-outer">
                <p class="ifp-adv-tool__label">{{'Data type' | translate}}: <span class="ifp-adv-tool__label-value">{{column.type | translate}}</span></p>
              </div>
            </div>
          </ifp-data-tool-accordian>
        }
    </div>
    <div class="ifp-adv-tool__content">
      <p class="ifp-adv-tool__sub-title">{{'Fields to impute' | translate}}</p>
      <div class="" cdkDropList
        [cdkDropListData]="outputColumnList" cdkDropListSortingDisabled (cdkDropListDropped)="drop($event, 'add')">
      @if (outputColumnList.length) {
        @for (column of getControls; track index; let index = $index) {
          <ifp-data-tool-accordian [title]="column.value.name" [enableCheckBox]="false" [isDraggable]="true" cdkDrag class="ifp-adv-tool__accordian ifp-adv-tool__accordian--multi-sec" [isError]="isSaveClicked && column.invalid">
            <div class="ifp-accord-box__content">
              <div class="ifp-adv-tool__box">
                <div class="ifp-adv-tool__edit-outer">
                  <p class="ifp-adv-tool__label">{{'Data type' | translate}}: <span class="ifp-adv-tool__label-value">{{column.value.type | translate}}</span></p>
                </div>
                <p class="ifp-adv-tool__sub-title">{{'Value to replace' | translate}}</p>
                <app-ifp-check-box class="ifp-adv-tool__checkbox ifp-adv-tool__checkbox--radio"
                [label]="'Null' | translate"
                [type]="'radio'"
                [name]="'incomingValue'+index"
                [id]="'null'+index"
                (checked)="valueToReplace(true, index)"
                [checkedData]="column.value.isNull"></app-ifp-check-box>

                <app-ifp-check-box class="ifp-adv-tool__checkbox ifp-adv-tool__checkbox--radio"
                [label]="'User specified value' | translate"
                [type]="'radio'"
                [name]="'incomingValue'+index"
                [id]="'specified'+index"
                (checked)="valueToReplace(false, index)"
                [checkedData]="!(column.value.isNull)"></app-ifp-check-box>
                <div class="ifp-adv-tool__edit-outer" [ngClass]="{'ifp-adv-tool__edit-outer--disabled': column.value.isNull}">
                  <p class="ifp-adv-tool__label">{{'Enter value' | translate}}</p>
                  <input type="text" class="ifp-adv-tool__input" [formControl]="column.controls['user_specified']">
                  @if (isSaveClicked && column.controls['user_specified'].invalid) {
                    <p class="ifp-adv-tool__error"><em class="ifp-icon ifp-icon-exclamation-round"></em>{{'Please specify a field' | translate}}</p>
                  }
                </div>
              </div>
              <div class="ifp-adv-tool__box">
                <div class="ifp-adv-tool__edit-outer">
                  <p class="ifp-adv-tool__sub-title">{{'Replace with value' | translate}}</p>
                  <app-ifp-db-dropdown class="ifp-adv-tool__dropdown" [isMultiSelect]="false" [formControl]="column.controls['method']" [options]="column.value.impute_options" [defaultSelect]="false" [key]="'label'" (singleSelected)="onSelectMethod($event, index)"></app-ifp-db-dropdown>
                  @if (isSaveClicked && column.controls['method'].invalid) {
                    <p class="ifp-adv-tool__error"><em class="ifp-icon ifp-icon-exclamation-round"></em>{{'Please select an impute method' | translate}}</p>
                  }
                </div>
                @if (column.value.method?.method === 'custom') {
                  <div class="ifp-adv-tool__edit-outer">
                    <p class="ifp-adv-tool__label">{{'New Value' | translate}}</p>
                    <input type="text" class="ifp-adv-tool__input" [formControl]="column.controls['customImputeValue']">
                    @if (isSaveClicked && column.controls['customImputeValue'].invalid) {
                      <p class="ifp-adv-tool__error"><em class="ifp-icon ifp-icon-exclamation-round"></em>{{'Please provide a new value' | translate}}</p>
                    }
                  </div>
                }
              </div>
              <div class="ifp-adv-tool__box">
                <app-ifp-check-box class="ifp-adv-tool__checkbox ifp-adv-tool__checkbox--radio"
                [label]="'Populate empty column cells with values by imputing data' | translate"
                [type]="'radio'"
                [name]="'isFillColumn'+index"
                [id]="'fillColumn'+index"
                (checked)="onSetOutput(true, index)"
                [checkedData]="column.value.isFillCol"></app-ifp-check-box>

                <app-ifp-check-box class="ifp-adv-tool__checkbox ifp-adv-tool__checkbox--radio"
                [label]="'Create a new column' | translate"
                [type]="'radio'"
                [name]="'isFillColumn'+index"
                [id]="'newCol'+index"
                (checked)="onSetOutput(false, index)"
                [checkedData]="!(column.value.isFillCol)"></app-ifp-check-box>
                <div class="ifp-adv-tool__edit-outer" [ngClass]="{'ifp-adv-tool__edit-outer--disabled': column.value.isFillCol}">
                  <p class="ifp-adv-tool__label">{{'New column name' | translate}}</p>
                  <input type="text" class="ifp-adv-tool__input" [formControl]="column.controls['column_name']">
                  @if (isSaveClicked && column.controls['column_name'].invalid) {
                    <p class="ifp-adv-tool__error"><em class="ifp-icon ifp-icon-exclamation-round"></em>{{'Please provide a new column name' | translate}}</p>
                  }
                </div>
              </div>
            </div>
          </ifp-data-tool-accordian>
        }
      } @else {
        <p class="ifp-adv-tool__empty-drop">
          {{'Drop fields to add them to output colums' | translate}}
        </p>
      }
    </div>
    </div>
  </div>
</form>
}
