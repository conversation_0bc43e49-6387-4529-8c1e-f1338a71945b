import { Component, EventEmitter, input, Input, InputSignal, output, Output } from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';
import { IfpAccordionComponent } from '../../molecule/ifp-accordion/ifp-accordion.component';
import { DbToolbarIcon, ToolbarTabMenu, Content, DockItem, AxisDropDown, AxisOptions } from './ifp-chart-toolbar.interface';
import { NgClass } from '@angular/common';
import { axisDropDowns, chartSettingsOptions, chartTypes, dockOptions, singleDimentionData, tabMenu } from './ifp-chart-toolbar.constants';
import { IfpIconSelectorComponent } from '../../atom/ifp-icon-selector/ifp-icon-selector.component';
import { IfpDbIconLibraryComponent, TagItem } from '../../molecule/ifp-db-icon-library/ifp-db-icon-library.component';
import { iconLibrary, tagList } from '../../molecule/ifp-db-icon-library/ifp-db-icon-library.constants';
import { DashboardService } from 'src/app/scad-insights/core/services/create-dashboard/dashboard.service';
import { IfpDbChartStyleComponent } from '../../molecule/ifp-db-chart-style/ifp-db-chart-style.component';
import { IfpChartLegendPropsComponent } from '../../molecule/ifp-chart-legend-props/ifp-chart-legend-props.component';
import { IfpDbTextComponent } from '../../molecule/ifp-db-text/ifp-db-text.component';
import { IfpChartSettingsComponent } from '../../molecule/ifp-chart-settings/ifp-chart-settings.component';
import { IfpChartCardFilterComponent } from '../ifp-chart-card-filter/ifp-chart-card-filter.component';
import { ThemeService } from 'src/app/scad-insights/core/services/theme/theme.service';
import { fileFormats } from '../../molecule/ifp-db-file-uploader/ifp-db-file-uploader.constants';
import { buttonClass, buttonIconPosition } from 'src/app/scad-insights/core/constants/button.constants';
import { dashboardConstants } from 'src/app/scad-insights/core/constants/dashboard.constants';
import { IfpDataAggregationPanelComponent } from "../ifp-data-aggregation-panel/ifp-data-aggregation-panel.component";
import { IfpDbValueCardComponent } from "../ifp-db-value-card-templates/ifp-db-value-card-templates.component";
import { columnAggregations } from '../../pages/dashboard.interface';
import { FormGroup } from '@angular/forms';

@Component({
  selector: 'ifp-chart-toolbar',
  templateUrl: './ifp-chart-toolbar.component.html',
  styleUrl: './ifp-chart-toolbar.component.scss',
  imports: [TranslateModule, IfpAccordionComponent, NgClass, IfpIconSelectorComponent, IfpDbIconLibraryComponent, IfpDbChartStyleComponent, IfpChartLegendPropsComponent,
    IfpDbTextComponent, IfpChartSettingsComponent, IfpChartCardFilterComponent, IfpDataAggregationPanelComponent, IfpDbValueCardComponent]
})
export class IfpChartToolbarComponent {


  @Input() isPieDisabled: boolean = false;
  @Input() selectedCard: any = [];
  @Input() cntType!: string;
  @Input() selectedTextColor!: string;
  @Input() selectedDescriptionColor!: string;
  @Input() dataType!: string;
  @Input() selectedCardData: any = [];
  @Input() allDropDownData: any = [];
  @Input() selectedAllCardData: any = [];
  @Input() isDragged: boolean = false;
  @Input() isFilterPanel: boolean = false;
  @Input() isTextareaExpanded: boolean = false;
  @Input() openDataTool: boolean = false;
  @Input() uploadedFile: any;
  @Output() changeChartType: EventEmitter<DbToolbarIcon> = new EventEmitter<DbToolbarIcon>();
  @Output() closeToolbar: EventEmitter<boolean> = new EventEmitter<boolean>();
  @Output() dockOptionChanged: EventEmitter<string> = new EventEmitter<string>();
  @Output() pinDashboardOutput: EventEmitter<boolean> = new EventEmitter<boolean>();
  @Output() selectTool: EventEmitter<string> = new EventEmitter<string>();

  public tabMenuTypes = tabMenu;

  public selectedTab: number = 0;
  public prevTab: number = this.selectedTab - 1;
  public selectedTabItem: ToolbarTabMenu = this.tabMenuTypes[this.selectedTab];
  public chartTypes: DbToolbarIcon[] = chartTypes;
  public selectedChartType!: DbToolbarIcon;
  public selectedVisualization!: string;

  public iconLibrary: DbToolbarIcon[] = iconLibrary;
  public chartSettingsOption = chartSettingsOptions;
  public tagList: TagItem[] = tagList;
  public chartSeries!: number;
  public seriesTitles: string[] = [];
  public seriesColors: string[] = [];
  public cardTitle!: string;
  public selectedDockItem?: DockItem = dockOptions[this._themeService.defaultLang == 'ar' ? 0 : 1];
  public dockItems: DockItem[] = dockOptions;
  public selectedTextSize!: number;
  public selectedDescriptionTextSize!: number;
  public cardDescription!: string;
  public defualtLang: string | null = 'light';
  public isPinned: boolean = false;
  public axisOptions: AxisOptions[] = axisDropDowns;
  public buttonClass = buttonClass;
  public buttonIconPosition = buttonIconPosition;
  public selectedRows: { value: number, index: number } = { value: 1, index: 0 };
  public addRow: boolean = false;
  public selectedXaxis: AxisDropDown = axisDropDowns[0].options[0];
  public selectedYaxis: AxisDropDown[] = [axisDropDowns[0].options[1]];
  public isDockDropdDown: boolean = false;
  public dashboardConstants = dashboardConstants;
  public chartSettings = {
    isDatalabel: true,
    isPrecise: false
  };

  public tabledata = singleDimentionData;

  public allowedExcelExtensions = fileFormats.excelFormats;
  public rowCount = [
    {
      value: 1
    },
    {
      value: 5
    },
    {
      value: 10
    },
    {
      value: 20
    },
    {
      value: 50
    }
  ];


  // new variables //


  // inputs //
  public possibleAggregationColumns: InputSignal<columnAggregations[]> = input<columnAggregations[]>([])

  // outputs  //
  public createCardActionOutput = output<{value: FormGroup, type: string}>();

  constructor(private _dashboardService: DashboardService, private _themeService: ThemeService) {

  }







  toolbarTabSelect(item: ToolbarTabMenu, index: number) {
    this.selectedTab = index;
    this.selectedTabItem = this.tabMenuTypes[this.selectedTab];
  }

  selectChartType(item: DbToolbarIcon, index: number) {

  }

  selectIconFromLibrary(_icon: DbToolbarIcon) {
    //
  }

  updateValues(event: any, type: string) {

  }

  expandAccordian(_event: boolean, content: Content) {
    this.selectedTabItem.content.forEach((x: Content) => x.isExpand = x.title == content.title ? !x.isExpand : false)
  }



  createPreviewCard(event: {value: FormGroup, type: string}) {
    this.createCardActionOutput.emit(event);
  }







  closeModelToolbar(event: boolean) {
    this.closeToolbar.emit(event);
  }




}
