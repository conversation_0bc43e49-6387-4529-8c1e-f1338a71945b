import { DatePipe, Location, NgClass } from '@angular/common';
import { ChangeDetectorRef, Component, EventEmitter, Input, OnChanges, OnDestroy, OnInit, Output, SimpleChanges, ViewChild } from '@angular/core';
import { ActivatedRoute, Params, Router } from '@angular/router';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { IfpKebabMenuComponent } from 'src/app/dashboard-builder/molecule/ifp-kebab-menu/ifp-kebab-menu.component';
import { KebabMenuOption } from 'src/app/dashboard-builder/molecule/ifp-kebab-menu/ifp-kebab-menu.interface';
import { buttonClass, buttonIconPosition } from 'src/app/scad-insights/core/constants/button.constants';
import { IfpButtonComponent } from 'src/app/scad-insights/ifp-widgets/ifp-atoms/ifp-button/ifp-button.component';
import { IfpSearchComponent } from 'src/app/scad-insights/ifp-widgets/ifp-atoms/ifp-search/ifp-search.component';
import { PaginationComponent } from '../../../../scad-insights/ifp-widgets/ifp-molecules/pagination/pagination.component';
import { SubSink } from 'subsink';
import { IfpPrepService } from '../ifp-prep-service';
import { prepsApiEndpoints } from '../constants/if-preps.constants';
import { PrepLibrary, PrepLibraryResult } from './intreface/ifp-prep-library.interface';
import { ToasterService } from 'src/app/scad-insights/core/services/tooster/ToastrService.service';
import { DownLoadService } from 'src/app/scad-insights/core/services/download-service/download.service';
import { environment } from 'src/environments/environment';
import { IfpNoDataComponent } from '../../../../scad-insights/ifp-widgets/ifp-molecules/ifp-no-data/ifp-no-data.component';
import { IfpSpinnerComponent } from '../../../../scad-insights/ifp-widgets/ifp-molecules/ifp-spinner/ifp-spinner.component';
import { ReactiveFormsModule } from '@angular/forms';
import { IfpInputEditComponent } from '../../../../scad-insights/ifp-widgets/ifp-molecules/ifp-input-edit/ifp-input-edit.component';
import { IfpTabComponent } from '../../../../scad-insights/ifp-widgets/ifp-molecules/ifp-tab/ifp-tab.component';
import { ApiService } from 'src/app/scad-insights/core/services/api.service';
import { FileResponePrep } from '../interface/ifp-data-prep.interface';
import { IfpTooltipDirective } from 'src/app/scad-insights/core/directives/ifp-tooltip.directive';
import { IfpAdvancePrepService } from '../../ifp-advance-prep/service/ifp-advance-prep.service';
import { IfpModalComponent } from '../../../../scad-insights/ifp-widgets/ifp-organism/ifp-modal/ifp-modal.component';
import { IfpRemoveCardComponent } from '../../../../scad-insights/ifp-widgets/ifp-atoms/ifp-remove-card/ifp-remove-card.component';
import { UsageDashboardLogService } from 'src/app/scad-insights/core/services/usage-dashboard-log/usage-dashboard-log.service';
import { logType } from 'src/app/scad-insights/core/services/usage-dashboard-log/usage-dashboard.constants';

@Component({
    selector: 'ifp-prep-library',
    templateUrl: './ifp-prep-library.component.html',
    styleUrl: './ifp-prep-library.component.scss',
    imports: [TranslateModule, IfpSearchComponent, IfpButtonComponent, IfpKebabMenuComponent, PaginationComponent, DatePipe, IfpNoDataComponent, IfpSpinnerComponent, ReactiveFormsModule, IfpInputEditComponent, IfpTabComponent, NgClass, IfpTooltipDirective, IfpModalComponent, IfpRemoveCardComponent,
        NgClass
    ]
})
export class IfpPrepLibraryComponent implements OnInit, OnDestroy, OnChanges {
  @ViewChild('modal') modal!: IfpModalComponent;
  @Input() isModal: boolean = false;
  @Input() viewTab = true;
  @Input() isAdvance = false;
  @Input() isLibrary:boolean=false;
  @Output() selectFile: EventEmitter<string> = new EventEmitter<string>();
  @Output() selectFileAll: EventEmitter<FileResponePrep> = new EventEmitter<FileResponePrep>();
  @Output() closeModal = new EventEmitter();
  @Output() removeFile: EventEmitter<string> = new EventEmitter<string>();

  public offset: number = 0;
  public page: number = 1;
  public limit: number = 10;
  public size: number = 10;
  public prevPage: string = '';
  public targetRoute: string = 'analytics/data-preparation/upload-data';
  public subs: SubSink = new SubSink();
  public prep = false;
  private sessionId!: string;
  constructor(private _translate: TranslateService, public location: Location, private _router: Router, private _libraryService: IfpPrepService, private _toast: ToasterService, private _downloadService: DownLoadService, private _activatedRoute: ActivatedRoute, private _apiService: ApiService, private _prepService: IfpAdvancePrepService, private _cdr: ChangeDetectorRef, private log: UsageDashboardLogService) {
    this.subs.add(
      this._activatedRoute.queryParams.subscribe((event: Params) => {
        this.prevPage = event['source'];
        if (this.prevPage === 'prep') {
          this.viewTab = false;
          this.prep = true;
          if (this.sessionId) {
            this.log.logEnds(this.sessionId, this.log.currentTime );
          }
          this.sessionId = this.log.createUUid;
          this.log.logStart(this.sessionId, logType.advancePrep, this.log.currentTime );
        }
      })
    );
  }


  ngOnChanges(_changes: SimpleChanges): void {
    if (this.isAdvance) {
      this.tabs = [{ name: 'Workflows', event: 'workflow', disabled: false }];
      this.currentSelection = this.tabs[0].event;
    }
  }

  public search: string = '';
  public buttonClass = buttonClass;
  public buttonIconPosition = buttonIconPosition;
  public table: PrepLibraryResult[] = [];
  public kebabOptions: KebabMenuOption[] = [
    {
      name: 'Rename',
      event: 'rename',
      icon: 'ifp-icon-edit',
      disabled: false
    },
    {
      name: 'Download CSV',
      event: 'download-csv',
      icon: 'ifp-icon-download-line',
      disabled: false
    },
    {
      name: 'Download Excel',
      event: 'download-excel',
      icon: 'ifp-icon-download-line',
      disabled: false
    },
    {
      name: 'Delete',
      event: 'delete',
      icon: 'ifp-icon-trash',
      disabled: false
    }
  ];

  public kebabOptionsWorkflow: KebabMenuOption[] = [
    {
      name: 'Delete',
      event: 'delete',
      icon: 'ifp-icon-trash',
      disabled: false
    }
  ];

  public editInput = false;
  public editIndex = 0;
  public editObjectId = '';
  public tabs = [{ name: 'Datasets', event: 'dataset', disabled: false }, { name: 'Workflows', event: 'workflow', disabled: false }];
  public currentSelection = this.tabs[0].event;
  public loader = false;
  public sortValues = {
    create: 'created_at',
    name: 'name'
  };

  public sortValue?: string;
  public sortValueString?: string;
  public acceding?: boolean;
  public deleteLibraryItem!: PrepLibraryResult;

  onPageChange(offset: { page: number, offset: number }) {
    this.offset = offset.offset;
    this.page = offset.page;
    if (this.currentSelection === 'dataset') {
      this.callTable();
    } else {
      this.callWorkFlow();
    }
  }

  limitChanged(event: number) {
    this.offset = 0;
    this.page = 1;
    this.limit = event;
    if (this.currentSelection === 'dataset') {
      this.callTable();
    } else {
      this.callWorkFlow();
    }
  }

  ngOnInit(): void {
    switch (this.prevPage) {
      case 'advance': this.targetRoute = '/analytics/advance';
        break;
      case 'exploration': this.targetRoute = '/analytics/exploratory/upload-data';
        break;
      default: this.targetRoute = '/analytics/data-preparation/upload-data';
        break;
    }
    if (this.currentSelection === 'dataset') {
      this.callTable();
    } else {
      this.callWorkFlow();
    }
  }

  callTable() {
    this.loader = true;
    const value: {
      limit: number, offset: number, search: string, ordering?: string
    } = { limit: this.limit, offset: this.offset, search: this.search };
    if (this.sortValue) {
      value['ordering'] = this.sortValue;
    }
    this.subs.add(
      this._libraryService.getMethodRequest(prepsApiEndpoints.library, value).subscribe((data: PrepLibrary) => {
        this.size = data.count;
        this.table = data.results;
        this.loader = false;
        this._cdr.detectChanges();
      })
    );
  }

  searchEvent(key: string) {
    this.search = key;
    this.limitChanged(this.limit);
  }

  sort(name: string, sort: boolean) {
    if (sort) {
      this.sortValue = `-${name}`;
    } else {
      this.sortValue = name;
    }
    this.sortValueString = name;
    this.acceding = sort;
    this.offset = 0;
    this.page = 1;
    if (this.currentSelection === 'dataset') {
      this.callTable();
    } else {
      this.callWorkFlow();
    }

  }

  onSelectOptions(menuItem: string, item: PrepLibraryResult, index: number) {
    //
    if (menuItem == this.kebabOptions[1].event) {
      this.downloadFile(item.object_id, 'csv');
    } else if (menuItem == this.kebabOptions[2].event) {
      this.downloadFile(item.object_id, 'excel');
    } else if (menuItem == this.kebabOptions[3].event) {
      this.modal.createElement();
      this.deleteLibraryItem = item;
    } else if (menuItem == this.kebabOptions[0].event) {
      this.editIndex = index;
      this.editInput = true;
      this.editObjectId = item.object_id;
    }
  }

  delete() {
    if (this.currentSelection === 'dataset') {
      this.deleteLibrary(this.deleteLibraryItem.object_id);
    } else {
      this.deleteWorkFlow(this.deleteLibraryItem.object_id);
    }
  }

  deleteLibrary(id: string) {
    this.subs.add(
      this._libraryService.getDeleteRequest(`${prepsApiEndpoints.libraryDelete}${id}`).subscribe({
        next: () => {
          this.limitChanged(this.limit);
          this._prepService.deleteDataSet.set(id);
          this.removeFile.emit(id);
          this._toast.success('File deleted successfully!');
          if (this._libraryService.uploadedFileResponse()?.id === id) {
            this._libraryService.uploadedFileResponse.set(null);
            this._libraryService.fileName.set('');
          }
        }, error: err => {
          const error = err?.error;
          this._apiService.errorHandler(error);
        }
      })
    );
  }

  deleteWorkFlow(id: string) {
    this.subs.add(
      this._libraryService.getDeleteRequest(`${prepsApiEndpoints.workflows}${id}${prepsApiEndpoints.workflowDraftDelete}`).subscribe({
        next: () => {
          this.limitChanged(this.limit);
          this._toast.success('File deleted successfully!');
          this._cdr.detectChanges();
        }, error: err => {
          const error = err?.error;
          this._apiService.errorHandler(error);
        }
      })
    );
  }

  cancelEdit() {
    this.editInput = false;
  }

  validValueChange(event: string) {
    this.cancelEdit();
    this.subs.add(
      this._libraryService.putMethodRequest(`${prepsApiEndpoints.libraryDataset}${this.editObjectId}${prepsApiEndpoints.libraryDatasetDownload}`, { name: event }).subscribe({
        next: () => {
          this.limitChanged(this.limit);
          this.editObjectId = '';
          this._toast.success('File renamed successfully!');
          this._cdr.detectChanges();
        }, error: err => {
          const error = err?.error;
          this._apiService.errorHandler(error);
        }
      })
    );
  }


  callWorkFlow() {
    this.loader = true;
    const value: {
      limit: number, offset: number, search: string, ordering?: string
    } = { limit: this.limit, offset: this.offset, search: this.search };
    if (this.sortValue) {
      value['ordering'] = this.sortValue;
    }
    this.subs.add(
      this._libraryService.getMethodRequest(prepsApiEndpoints.workflowDraft, value).subscribe((data) => {
        this.size = data.count;
        this.table = data.results;
        this.loader = false;
        this._cdr.detectChanges();
      })
    );
  }

  selectedTabEvent(event: { event: { name: string, event: string }, index: number }) {
    this.offset = 0;
    this.page = 1;
    this.limit = 10;
    this.search = '';
    this.size = 10;
    this.table = [];
    this.currentSelection = event.event.event;
    if (event.event.event === 'dataset') {
      this.callTable();
    } else {
      this.callWorkFlow();
    }
  }

  downloadFile(url: string, file: string) {
    this._toast.success('Download started!');
    const subscribe = this._libraryService.getDownloadRequest(`${environment.prepbaseUrl}/v1/dataset/${url}/download/${file}/`).subscribe({
      next: data => {
        const matches = (data.headers.get('Content-Disposition'))?.match(/"(.*?)"/);
        const nameValue = matches ? matches[1] : '.xlsx';
        this._downloadService.downloadFiles(data.body, nameValue);
        this._toast.success(`${nameValue} ${this._translate.instant('Download completed successfully!')}`);
        subscribe.unsubscribe();
        this._cdr.detectChanges();
      },
      error: err => {
        const error = err?.error;
        this._apiService.errorHandler(error);
        this._toast.error(`${this._translate.instant('Download failed!')}`);
      }
    });
  }

  openFile(id: string, item: PrepLibraryResult) {
    if (this.currentSelection === 'dataset') {
      this._libraryService.uploadedFileResponse.set({
        name: item.name,
        file: item.file,
        owner: item.owner,
        storage_backend: item.storage_backend,
        id: item.object_id
      });
      if (this.prep) {
        this._libraryService.fileName.set(item.name);
      }
      if (this.isModal) {
        this.selectFile.emit(id);
        this.selectFileAll.emit({
          name: item.name,
          file: item.file,
          owner: item.owner,
          storage_backend: item.storage_backend,
          id: item.object_id,
          created_at: item.created_at,
          type: item.metadata?.type

        });
      } else {
        this.closeModal.emit(true);
        this._router.navigate([this.targetRoute], { queryParams: { id: id, summary: false } });
      }
    } else {
      this.closeModal.emit(true);
      this._prepService.selectedWorkflow.set(id);
      // this._router.navigate(['/analytics/advance']);
    }
  }

  onBack() {
    this.editInput = false;
    if (this.isModal) {
      this.closeModal.emit();
    } else {
      this.location.back();
    }
  }


  closeModalEvent(event: Event) {
    if (event) {
      this.delete();
    }
    this.modal.removeModal();
  }

  ngOnDestroy(): void {
    if (this.sessionId) {
      this.log.logEnds(this.sessionId, this.log.currentTime );
    }
    this.subs.unsubscribe();
  }

}
