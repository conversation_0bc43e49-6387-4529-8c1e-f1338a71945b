<div class="ifp-dxp-label-drag "   cdkDropList  #dropdown="cdkDropList" cdkDropListOrientation="mixed"  [cdkDropListData]="dropdownList()"  [cdkDropListConnectedTo]="connectedto()"  (cdkDropListDropped)="drop($event)"
  (cdkDropListEntered)="dragEnter()" (cdkDropListExited)="dragExit()"
     [ngClass]="{
        'ifp-dxp-label-drag--dragging': isDragging() && (limit() === -1 || limit() > dropdownList().length),
        'ifp-dxp-label-drag--drag-enter' : dragEnterEvent() && (limit() === -1 || limit() > dropdownList().length),
        'ifp-dxp-label-drag--disabled' : limit() !== -1 && limit() <= dropdownList().length &&isDragging()
      }"
    >
    @for (item of dropdownList(); track item; let index = $index) {
      <div class="ifp-dxp-label-drag__item">
        <!-- Removed input for axisControl variable name editing as axis labels are handled in the parent -->
        @if (item.aggregations && item.aggregations.length > 0) {
          <div class="ifp-dxp-label-drag__aggregation">
            <ifp-dxp-label-dropdown
              [selectedItems]="selectedItems()[index]"
              [optionArrayKey]="'aggregationOptions'"
              [dropdownKey]="'name'"
              [hideCross]="true"
              (optionSelected)="onAggregationSelected($event, index)"
              (removeItem)="removeItem(index)">
            </ifp-dxp-label-dropdown>
          </div>
        }
      </div>
    }
</div>

