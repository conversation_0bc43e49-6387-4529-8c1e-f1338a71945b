import { DatePipe } from '@angular/common';
import { Injectable } from '@angular/core';
import { cloneDeep, groupBy } from 'lodash';
import { chartConstants, checkAll } from '../../constants/chart.constants';
import { ifpColors } from '../../constants/color.constants';
import { dateFormat } from '../../constants/date.constants';
import { ThemeService } from '../theme/theme.service';
import { PanelFilter } from '../../interface/filter.interface';
import { TranslateService } from '@ngx-translate/core';
import { DashboardService } from '../create-dashboard/dashboard.service';
import { CustomLetter } from '../../pipes/firstLetter.pipe';

@Injectable({
  providedIn: 'root'
})
export class FilterService {

  private selectedLang: string = 'en';

  constructor(private datePipe: DatePipe, private themeService: ThemeService, private _translateService: TranslateService, private _dashboardService: DashboardService,
    private customLetter: CustomLetter
  ) {
    this.themeService.defaultLang$.subscribe(resp => {
      this.selectedLang = resp;
    });
  }


  filterDefault(data: any, filterPanel: any) {
    let defaults: [] = [];
    if (filterPanel) {
      defaults = data?.filter((filterData: any) => {
        let status = false;
        filterPanel.properties.find((element: any) => {
          if (filterData[element.path]?.toLowerCase() === element.default?.toLowerCase()) {
            status = true;
          } else {
            status = false;
          }
          return !status;
        });
        return status;
      });

    } else {
      defaults = data;
    }
    return defaults;
  }

  applyFilterPanel(data: any, filterPanel: any, filterItem: any) {
    let defaults: [] = [];
    defaults = data.filter((filterData: any) => {
      let status = false;
      filterPanel.properties.find((element: any) => {
        if (filterData[element.path] === filterItem) {
          status = true;
        } else {
          status = false;
        }
        return !status;
      });
      return status;
    });
    return defaults;
  }

  filterIndicatorDetail(data: any, periodFilter: any, sideFilter: any) {
    const chartData: any = [];
    let customXmax: any;
    if (data?.data?.length > 0 && periodFilter.id != 'All' && periodFilter?.unit != chartConstants.RECENT_LABEL) {
      if (sideFilter && sideFilter?.length > 0) {
        const seriesFilter = sideFilter.find((x: { checkbox: boolean; }) => x.checkbox);
        if (seriesFilter.value?.length == 1) {
          let singleData: any = [];
          if (this.selectedLang == 'ar') {
            singleData = data.data.filter((x: any) => x[seriesFilter.path] && x[seriesFilter.path] == seriesFilter.value[0]);
          }
          if (this.selectedLang == 'en') {
            singleData = data.data.filter((x: any) => x[seriesFilter.path] && x[seriesFilter.path].toLowerCase() == seriesFilter.value[0].toLowerCase());
          }
          customXmax = this.getMaxDate(singleData);
        }
      }
      const filterDate: any = periodFilter.unit == 'years' ? new Date(data.xMax).setFullYear(new Date(!customXmax ? data.xMax : customXmax).getFullYear() - periodFilter.value) : new Date(data.xMax).setMonth(new Date(data.xMax).getMonth() - periodFilter.value);
      chartData.push(this.filterAnalyticData(data, filterDate));
    }
    const finalData = this.filterUsingKey((periodFilter.id != 'All' && periodFilter?.unit != chartConstants.RECENT_LABEL ? chartData : [data]), sideFilter);
    if (periodFilter?.unit == chartConstants.RECENT_LABEL && finalData?.length > 0) {
      for (let index = 0; index < finalData.length; index++) {
        finalData[index] = finalData[index]?.slice(-chartConstants.RECENT_COUNT);
      }
    }
    return finalData;
  }


  getMaxDate(data: any[]): any {
    if (!data || data.length === 0) {
      return null;
    }
    // Use reduce to find the maximum date
    return data.reduce((max, item) => {
      const currentDate = new Date(item.OBS_DT);
      // Compare current date with the max date
      if (!max || currentDate > max) {
        return currentDate;
      }
      return max;
    }, null);
  }

  findSelectedOptinValue(sideFilter: any) {
    const seriesFilter = sideFilter.find((x: { checkbox: boolean; }) => x.checkbox);
  }


  filterUsingKey(data: any, sideFilter: any) {
    const sideFilterData: any = [];
    const seriesFilter = sideFilter.find((x: { checkbox: boolean; }) => x.checkbox);
    if (data[0]?.data?.length > 0) {
      seriesFilter?.value.forEach((series: any) => {
        let singleData: any = [];
        if (this.selectedLang == 'ar') {
          singleData = data[0].data.filter((x: any) => x[seriesFilter.path] && x[seriesFilter.path] == series);
        }
        if (this.selectedLang == 'en') {
          singleData = data[0].data.filter((x: any) => x[seriesFilter.path] && x[seriesFilter.path].toLowerCase() == series.toLowerCase());
        }
        sideFilterData.push(this.filterSingleKey(singleData, sideFilter));
      });
    }
    return sideFilter.length > 0 ? sideFilterData : [data[0]?.data];
  }


  filterSingleKey(data: any[], seriesFilter: { path: string | number; value: any[]; checkbox: boolean, label: string, isCFD: boolean }[]) {
    seriesFilter.forEach((element: { checkbox: boolean; path: string | number; value: any[]; label: string, isCFD: boolean }) => {
      if (!element.checkbox) {
        // execlud (Category, Gender, salary) if it's CFDM
        if (element.isCFD) {
          if (element.path != chartConstants.CATEGORY_PATH &&
            element.path != chartConstants.GENDER_PATH &&
            element.path != chartConstants.SALARY_PATH) {
            data = element.path != chartConstants.TIME_PERIOD ? data.filter(x => (element.path == 'OBS_DT' ? this.datePipe.transform(x[element.path], 'yyyy') : x[element.path]?.toLowerCase()) == element.value[0].toLowerCase()) : data.filter(x => (checkAll(element.value[0])) ? element.value.includes((element.path == 'OBS_DT' ? this.datePipe.transform(x[element.path], 'yyyy') : x[element.path])) : x);
          }
        } else {
          data = element.path != chartConstants.TIME_PERIOD ? data.filter(x => (element.path == 'OBS_DT' ? this.datePipe.transform(x[element.path], 'yyyy') : x[element.path]?.toLowerCase()) == element.value[0].toLowerCase()) : data.filter(x => (checkAll(element.value[0])) ? element.value.includes((element.path == 'OBS_DT' ? this.datePipe.transform(x[element.path], 'yyyy') : x[element.path])) : x);
        }

      }
    });
    return data;
  }






  PeriodFilter(data: [], filterType: string, max: string, periodRange: number, key: string | number = 'OBS_DT') {
    if (filterType === 'All') {
      return data;
    }
    const splitMaxDate = max?.split('-');
    let date: any;
    let filterDate: any;
    if (splitMaxDate) {
      date = new Date(+splitMaxDate[0], +splitMaxDate[1], +splitMaxDate[2]);
      filterDate = this.calculateMonth(filterType, new Date(+splitMaxDate[0], +splitMaxDate[1], +splitMaxDate[2]), periodRange);
    }


    const periodData = data?.filter((element: any) => {
      const splitDate = element[key]?.split('-');
      let currentDate: any;
      if (splitDate) {
        currentDate = new Date(splitDate[0], splitDate[1], splitDate[2]);
      }
      return currentDate.valueOf() >= filterDate.valueOf() && currentDate.valueOf() <= date.valueOf();
    });
    return periodData;
  }

  calculateMonth(filterType: string, date: Date, periodRange: number) {
    let months = +periodRange;
    if (filterType === 'years' || filterType === 'year') {
      months = months * 12 * periodRange;
    } else if (filterType === 'quarter' || filterType === 'quarters') {
      months = months * 4 * periodRange;
    }
    date.setMonth(date.getMonth() - months);
    return date;
  }

  formatingTitle(data: any) {
    let title = data.title ? data.title : '';
    const regex = /{([^}]+)}/g;
    if (data?.title) {
      const matches = data.title.match(regex);
      const values = matches?.map((match: string | any[]) => match.slice(1, -1));
      if (values?.length > 0) {
        values.forEach((element: string | number) => {
          if (data[element]) {
            const date = data.templateFormat === 'quarter' ? this.convertDateToQuarter(data[element], 'table') : this.datePipe.transform(data[element], data.templateFormat);
            title = title?.replace(`{${element}}`, date);
          }
        });
      }
    }
    return title;
  }

  getColor(index: number) {
    let color: any;
    switch (index) {
      case 0:
        color = ifpColors.green;
        break;
      case 1:
        color = ifpColors.blue;
        break;
      case 2:
        color = ifpColors.lightBlue;
        break;
      case 3:
        color = ifpColors.red;
        break;
      case 4:
        color = ifpColors.darkPurple;
        break;
      case 5:
        color = ifpColors.skyBlue;
        break;
      case 6:
        color = ifpColors.orange;
        break;
      case 7:
        color = ifpColors.yellow;
        break;
      case 8:
        color = ifpColors.chartBlue;
        break;
      case 9:
        color = ifpColors.chartGreen;
        break;
      case 10:
        color = ifpColors.chartVilot;
        break;
      case 11:
        color = ifpColors.chartBlack;
        break;
      case 12:
        color = ifpColors.chartOrange;
        break;
      default:
        color = ifpColors.grey;
        break;
    }
    return color;
  }


  filterIndicatorForcast(data: any, periodFilter: any) {
    const chartData: any = [];
    let finalData: any = [];
    if (data?.data?.length > 0 && periodFilter.id != 'All' && periodFilter?.unit != chartConstants.RECENT_LABEL && !data.id.includes('forecast')) {
      const filterDate: any = periodFilter.unit == 'years' ? new Date(data.xMax).setFullYear(new Date(data.xMax).getFullYear() - periodFilter.value) : new Date(data.xMax).setMonth(new Date(data.xMax).getMonth() - periodFilter.value);
      chartData.push(this.filterAnalyticData(data, filterDate));
    }
    if (data.id.includes('forecast')) {
      chartData.push(data);
    }
    if (periodFilter?.unit == chartConstants.RECENT_LABEL && data?.data?.length > 0) {
      finalData = cloneDeep(data);
      finalData.data = finalData.data.slice(!data.id.includes('forecast') ? (finalData.data.length > 12 ? -chartConstants.RECENT_COUNT : -4) : -finalData.data.length - 1);
    }
    return periodFilter.id != 'All' && periodFilter?.unit != chartConstants.RECENT_LABEL ? chartData : (periodFilter?.unit == chartConstants.RECENT_LABEL ? [finalData] : [data]);
  }

  filterCompareDetail(data: any, periodFilter: any, sideFilter: any) {
    const customizeData = cloneDeep(data);
    const chartData: any = [];
    if (customizeData?.length > 0) {
      customizeData.forEach((element: { data: string | any[]; xMax: string | number | Date; }) => {
        const chartSeriesData: any = [];
        if (element?.data?.length > 0 && periodFilter.id != 'All' && periodFilter?.unit != chartConstants.RECENT_LABEL) {
          const filterDate: any = periodFilter.unit == 'years' ? new Date(element.xMax).setFullYear(new Date(element.xMax).getFullYear() - periodFilter.value) : new Date(element.xMax).setMonth(new Date(element.xMax).getMonth() - periodFilter.value);
          chartSeriesData.push(this.filterAnalyticData(element, filterDate));
          chartData.push(chartSeriesData[0]);
        }
        if (element?.data?.length > 0 && periodFilter.id == 'All' && periodFilter?.unit != chartConstants.RECENT_LABEL) {
          chartData.push(element);
        }
        if (element?.data?.length > 0 && periodFilter?.unit == chartConstants.RECENT_LABEL) {
          const totalData = cloneDeep(element.data);
          element.data = totalData.slice(-chartConstants.RECENT_COUNT);
          chartData.push(element);
        }
      });
    }
    return chartData;
  }

  convertDateToQuarter(date: any, type: string = 'chart') {
    let label: string = '';
    if (type === 'chart') {
      if ((new Date(date).getMonth() + 1) <= 3 && (new Date(date).getMonth() + 1) > 0) {
        label = `Q1<br>${this.datePipe.transform(new Date(date), 'yyyy')}`;
      } else if ((new Date(date).getMonth() + 1) > 3 && (new Date(date).getMonth() + 1) <= 6) {
        label = `Q2<br>${this.datePipe.transform(new Date(date), 'yyyy')}`;
      } else if ((new Date(date).getMonth() + 1) > 6 && (new Date(date).getMonth() + 1) <= 9) {
        label = `Q3<br>${this.datePipe.transform(new Date(date), 'yyyy')}`;
      } else if ((new Date(date).getMonth() + 1) > 9 && (new Date(date).getMonth() + 1) <= 12) {
        label = `Q4<br>${this.datePipe.transform(new Date(date), 'yyyy')}`;
      } else if ((new Date(date).getMonth() + 1) == 0) {
        label = `Q1<br>${this.datePipe.transform(new Date(date), 'yyyy')}`;
      }
      return label;
    }
    if ((new Date(date).getMonth() + 1) <= 3 && (new Date(date).getMonth() + 1) > 0) {
      label = `Q1 ${this.datePipe.transform(new Date(date), 'yyyy')}`;
    } else if ((new Date(date).getMonth() + 1) > 3 && (new Date(date).getMonth() + 1) <= 6) {
      label = `Q2 ${this.datePipe.transform(new Date(date), 'yyyy')}`;
    } else if ((new Date(date).getMonth() + 1) > 6 && (new Date(date).getMonth() + 1) <= 9) {
      label = `Q3 ${this.datePipe.transform(new Date(date), 'yyyy')}`;
    } else if ((new Date(date).getMonth() + 1) > 9 && (new Date(date).getMonth() + 1) <= 12) {
      label = `Q4 ${this.datePipe.transform(new Date(date), 'yyyy')}`;
    } else if ((new Date(date).getMonth() + 1) == 0) {
      label = `Q1 ${this.datePipe.transform(new Date(date), 'yyyy')}`;
    }
    return label;
  }


  createSeries(data: any, selctedPeriod: any, sideFilter: any) {
    sideFilter = sideFilter.filter((x: { isHide: any; }) => !x?.isHide);
    let filterDate: any;
    const series: any = [];
    const legends: any = [];
    if (selctedPeriod.id != 'All' && selctedPeriod?.unit != chartConstants.RECENT_LABEL) {
      filterDate = selctedPeriod.unit == 'years' ? new Date(data.xMax).setFullYear(new Date(data.xMax).getFullYear() - selctedPeriod.value) : new Date(data.xMax).setMonth(new Date(data.xMax).getMonth() - selctedPeriod.value);
      data.data = this.filterAnalyticData(data, filterDate);
    }
    const seriesFilter = sideFilter.find((x: { checkbox: any; }) => x.checkbox);
    const seriesData = groupBy(data.data, (a: any) => a[seriesFilter?.path]);
    const keys = Object.keys(seriesData);
    if (keys.length) {
      for (const key in seriesData) {
        // eslint-disable-next-line no-prototype-builtins
        if (seriesData.hasOwnProperty(key)) {
          series.push(seriesData[key]);
          legends.push(key);
        }
      }
    }

    const isCFD = sideFilter[0].isCFD;

    if (selctedPeriod?.unit == chartConstants.RECENT_LABEL) {
      for (let index = 0; index < series.length; index++) {
        isCFD ? series[index] = series[index].slice(-chartConstants.RECENT_COUNT_CFDM) :
          series[index] = series[index].slice(-chartConstants.RECENT_COUNT);
      }
    }
    return { data: series, legends: legends };
  }


  filterAnalyticData(data: any, filterDate: any) {
    const filterData = cloneDeep(data);
    filterData.data = [...filterData.data.filter((obj: { OBS_DT: any; }) => new Date(obj.OBS_DT) >= new Date(filterDate))];
    return filterData;
  }

  filterForcastwithOpts(data: any[], filter: PanelFilter[], adjustNowcastData: boolean = true, filterForcast: boolean = false): any[] {
    if (!data?.length || !filter?.length) {
      return [];
    }
    const [nowcastModelTemplate, forcastModelTemplate] = data;
    if (filterForcast) {
      forcastModelTemplate.data = forcastModelTemplate.data.filter((x: { TYPE: string; }) => x.TYPE == 'FORECAST');
    }
    const seriesData: any[] = [];
    const multiSeriesOption = filter.find(x => x.checkbox);
    if (!multiSeriesOption) {
      return seriesData;
    }
    const { value: multiSeriesValues, path: multiSeriesPath } = multiSeriesOption;
    multiSeriesValues.forEach(opt => {
      let nowcastData = nowcastModelTemplate.data.filter(
        (x: Record<string, string>) => x[multiSeriesPath].toLowerCase() === opt.toLowerCase()
      );

      const obsFilter = filter.find(x => x.path == 'OBS_DT');
      if (obsFilter && !obsFilter.value.includes('All') && !obsFilter.value.includes('المجموع')) {
        nowcastData = nowcastData.filter((item: { OBS_DT: string | number | Date; }) => {
          const year: any = this.datePipe.transform(item.OBS_DT, 'yyyy'); // Extract year using DatePipe
          return obsFilter.value.includes(year); // Compare with selected years
        });
      }
      filter.forEach(cnt => {
        if (!cnt.checkbox && cnt.path !== 'OBS_DT') {
          nowcastData = nowcastData.filter((x: Record<string, string>) => cnt.value.includes(x[cnt.path]));
        }
      });
      if (nowcastData.length) {
        const nowcastModel = {
          ...nowcastModelTemplate,
          data: nowcastData,
          seriesidentifier: opt.toLowerCase()
        };
        seriesData.push(nowcastModel);
        let forcastData = adjustNowcastData ? [nowcastData[nowcastData.length - 1], ...forcastModelTemplate.data] : forcastModelTemplate.data;

        forcastData = forcastData.filter(
          (x: Record<string, string>) => x[multiSeriesPath].toLowerCase() === opt.toLowerCase()
        );

        filter.forEach(cnt => {
          if (!cnt.checkbox && cnt.path !== 'OBS_DT') {
            forcastData = forcastData.filter((x: Record<string, string>) => cnt.value.includes(x[cnt.path]));
          }
        });
        const forcastModel = {
          ...forcastModelTemplate,
          data: forcastData,
          seriesidentifier: opt.toLowerCase()
        };
        seriesData.push(forcastModel);
      }
    });
    return seriesData;
  }


  setTableData(data: any, initialFilterData: any) {
    let dynamicObject: any = {};
    let indexData: any = [];
    const tableData: any[] = [];
    let timeUnit = data?.indicatorVisualizations?.visualizationsMeta?.[0]?.timeUnit?.includes('Monthly') ? 'Monthly' : 'Yearly';
    if (initialFilterData?.length > 0) {
      initialFilterData.forEach((element: any) => {
        dynamicObject = {};
        indexData = [];
        element?.forEach((value: Record<string, any>) => {
          if (data.tableFields?.length > 0) {
            data.tableFields.forEach((cell: { label: any; path: any; }) => {
              // if (value[cell.path]) {
              //   dynamicObject[cell.label] = value[cell.path];
              // }
              if (!timeUnit) {
                timeUnit = 'Yearly';
              }
              if (value[cell.path] !== undefined) {
                let key = cell.label;
                if (cell.label === this._translateService.instant('DATE')) {
                  key = timeUnit === 'Yearly' ? this._translateService.instant('Year') : (timeUnit === 'Quarterly' ? this._translateService.instant('Quarter') : 'Date');
                }
                dynamicObject[key] = cell.path === 'OBS_DT' && (timeUnit === 'Yearly' || timeUnit === 'y') ? value['YEAR'] : (cell.path === 'OBS_DT' && timeUnit === 'Quarterly' ? this.convertDateToQuarter(value[cell.path], 'table') : value[cell.path]);
              }
            });
          }
          indexData.push(cloneDeep(dynamicObject));
        });
        tableData.push(indexData);
      });
    }
    return tableData;
  }


  setForcastTableData(initialFilterData: any, data: any) {
    let dynamicObject: any = {};
    let indexData: any = [];
    const tableData: any[] = [];
    if (initialFilterData?.length > 0) {
      initialFilterData.forEach((element: any) => {
        dynamicObject = {};
        indexData = [];
        element.data?.forEach((value: Record<string, any>) => {
          if (data.tableFields?.length > 0) {
            data.tableFields.forEach((cell: { label: any; path: any; }) => {
              if (value[cell.path]) {
                dynamicObject[cell.label] = value[cell.path];
              }
            });
          }
          if (data.type == 'coi' && chartConstants.POPULATION_DOMAIN.includes(data.domain)) {
            dynamicObject['Citizenship'] = element.label;
          }
          indexData.push(cloneDeep(dynamicObject));
        });
        tableData.push(indexData);
      });
    }
    return tableData;
  }


  createQuarterData(initialFilterData: any, isDashboardCard: boolean, cntType: string, id: string, filterKeys: any, chartType: string, data: any) {
    const chartCatogory: (string | undefined)[] = [];
    const chartData: any = [];
    const foracstTooltip: any = [];
    initialFilterData.forEach((series: Record<string, any>[], index: number) => {
      const newChart: any[] = [];
      const catogory: (string | undefined)[] = [];
      series.forEach(element => {
        newChart.push(element['VALUE']);
        catogory.push(this.convertDateToQuarter(element['OBS_DT']));
      });
      const seriesData: any = {
        color: !isDashboardCard ? this.getColor(index) : this._dashboardService.getColors(cntType, id, index),
        data: newChart,
        name: this.customLetter.transform(filterKeys.find((x: { checkbox: boolean; }) => x.checkbox)?.value[index] ? filterKeys.find((x: { checkbox: boolean; }) => x.checkbox)?.value[index] : data.component_title),
        type: chartType,
        marker: {
          enabled: true
        }
      };
      if (isDashboardCard) {
        seriesData.spacing = this._dashboardService.gettingSpaceValues(cntType, id);
        seriesData.xAxisPositions = this._dashboardService.getXaxisPositions(cntType, id);
        seriesData.legendPositions = this._dashboardService.getLegendPositions(cntType, id);
        seriesData.isDatalabel = this._dashboardService.getChartSettings(cntType, id, chartConstants.DATALABEL);
        seriesData.isPrecise = this._dashboardService.getChartSettings(cntType, id, chartConstants.PRESICE_VALUE);
      }
      if (seriesData.data?.length > 0) {
        chartData.push(seriesData);
        chartCatogory.push(...catogory);
      }
      series.forEach(element => {
        foracstTooltip.push({
          MonthLabel: element['CHANGE_QQ'],
          YearLabel: element['CHANGE_YY']
        });
      });
      if (isDashboardCard) {
        this._dashboardService.setSeriesLength(cntType, id, chartData);
      }
    });
    return { chartData: chartData, chartCatogory: chartCatogory, foracstTooltip: foracstTooltip };
  }

  createAutoYaxisLabelData(initialFilterData: any, isDashboardCard: boolean, cntType: string, id: string, filterKeys: any, data: any, chartType: string) {
    const chartData: any = [];
    const foracstTooltip: any = [];
    initialFilterData.forEach((series: Record<string, any>[], index: number) => {
      const newChart = series?.map((element: Record<string, any>) => {
        const splitDate = element['OBS_DT'].split('-');
        return [(Date.UTC(splitDate[0], splitDate[1] - 1, splitDate[2])), element['VALUE']];
      });
      const seriesData: any = {
        color: !isDashboardCard ? this.getColor(index) : this._dashboardService.getColors(cntType, id, index),
        data: newChart,
        name: this.customLetter.transform(filterKeys.find((x: { checkbox: boolean; }) => x.checkbox)?.value[index] ? filterKeys.find((x: { checkbox: boolean; }) => x.checkbox)?.value[index] : (data.indicatorVisualizations.visualizationsMeta?.[0].componentTitle ? data.indicatorVisualizations.visualizationsMeta?.[0].componentTitle : data.component_title)),
        type: chartType,
        marker: {
          enabled: true
        }
      };
      if (isDashboardCard) {
        seriesData.spacing = this._dashboardService.gettingSpaceValues(cntType, id);
        seriesData.xAxisPositions = this._dashboardService.getXaxisPositions(cntType, id);
        seriesData.legendPositions = this._dashboardService.getLegendPositions(cntType, id);
        seriesData.isDatalabel = this._dashboardService.getChartSettings(cntType, id, chartConstants.DATALABEL);
        seriesData.isPrecise = this._dashboardService.getChartSettings(cntType, id, chartConstants.PRESICE_VALUE);
      }
      if (seriesData.data?.length > 0) {
        chartData.push(seriesData);
      }
      series.forEach(element => {
        foracstTooltip.push({
          MonthLabel: element['CHANGE_QQ'],
          YearLabel: element['CHANGE_YY']
        });
      });
      if (isDashboardCard) {
        this._dashboardService.setSeriesLength(cntType, id, chartData);
      }
    });

    return { chartData: chartData, foracstTooltip: foracstTooltip };
  }

  createQuarterForcastData(chartData: any) {
    const chartCatogory: any = [];
    if (chartData?.length > 0) {
      chartData.forEach((element: any, index: number) => {
        // if (index <= 2) {
        chartCatogory.push(...this.convertUtcToDate(element.data));
        // }
        element.data = element.data.map((arr: string | any[]) => arr.slice(1));
      });
    }
    const groupedChartData = groupBy(chartData, (a: any) => a.identifier);
    for (const key in groupedChartData) {
      if (Object.prototype.hasOwnProperty.call(groupedChartData, key)) {
        // eslint-disable-next-line no-loop-func
        groupedChartData[key].forEach((elementValues: any, index: number) => {
          if (elementValues.type == 'arearange' || elementValues.name?.includes('-forecast') || elementValues.name.includes('-تكهن')) {
            elementValues.pointStart = groupedChartData[key][0].data.length - 1;
          }
        });
      }
    }
    return [...new Set(chartCatogory)];
  }


  convertUtcToDate(data: any[]): any[] {
    return (data ?? []).map(item => {
      const convertedDate = this.datePipe.transform(item[0], 'yyyy-MM-dd');
      return this.convertDateToQuarter(convertedDate);
    });
  }

}
