import { cloneDeep } from 'lodash';
import { ChangeDetector<PERSON>ef, Component, EventEmitter, inject, Input, OnChanges, OnDestroy, OnInit, Output, signal, SimpleChanges, WritableSignal } from '@angular/core';
import { IfpCheckBoxComponent } from '../../../scad-insights/ifp-widgets/ifp-atoms/ifp-check-box/ifp-check-box.component';
import { TranslateModule } from '@ngx-translate/core';
import { IfpDataToolAccordianComponent } from '../../molecule/ifp-data-tool-accordian/ifp-data-tool-accordian.component';
import { IfpDbDropdownComponent } from '../../../dashboard-builder/molecule/ifp-db-dropdown/ifp-db-dropdown.component';
import { IfpButtonComponent } from '../../../scad-insights/ifp-widgets/ifp-atoms/ifp-button/ifp-button.component';
import { buttonClass } from 'src/app/scad-insights/core/constants/button.constants';
import { FormArray, FormBuilder, FormControl, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { ColumnPrep, DuplicatedColumn } from '../../data-prep/ifp-data-prep/ifp-prep-select-data/interface/prep-selection.interface';
import { connectionType } from '../../data-prep/ifp-data-prep/constants/if-preps.constants';
import { selectNodeStoreAdvance } from '../../data-prep/ifp-advance-prep/store/advance-node-store/advance-node.store';
import { IfpAdvancePrepService } from '../../data-prep/ifp-advance-prep/service/ifp-advance-prep.service';
import { ToasterService } from 'src/app/scad-insights/core/services/tooster/ToastrService.service';
import { jointKeys } from './ifp-adv-join-tool.constants';
import { SelectColumns } from '../ifp-adv-append-tool/ifp-adv-append-tool.component';
import { IfpSpinnerComponent } from '../../../scad-insights/ifp-widgets/ifp-molecules/ifp-spinner/ifp-spinner.component';
import { Subject } from 'rxjs';
import { SubSink } from 'subsink';
import { columnTypes } from '../../data-prep/ifp-data-prep/ifp-data-prep.constant';


@Component({
    selector: 'ifp-adv-join-tool',
    templateUrl: './ifp-adv-join-tool.component.html',
    styleUrl: './ifp-adv-join-tool.component.scss',
    imports: [IfpCheckBoxComponent, TranslateModule, IfpDataToolAccordianComponent, IfpDbDropdownComponent,
        IfpButtonComponent, FormsModule, ReactiveFormsModule, IfpSpinnerComponent]
})
export class IfpAdvJoinToolComponent implements OnChanges, OnInit, OnDestroy {

  @Output() cancel = new EventEmitter();
  @Output() save = new EventEmitter();

  @Input({ required: true }) columnList!: any;
  @Input({ required: true }) currentConnectionCount: number = 0;
  @Input() currentNodeId?: string = '';
  @Input() sourceData: WritableSignal<boolean> = signal(false);
  @Input() workFlowRunning = signal(false);
  @Input() emptyConnection = signal(true);
  @Input() currentConection: WritableSignal<boolean> = signal(false);
  @Input() isConfigUpdated: WritableSignal<boolean> = signal(false);
  @Input() errorMessageForTools?: string;

  public selectAll: boolean = true;
  public byIdentifier: string = jointKeys.fieldType;
  public buttonClass = buttonClass;
  public joinForm!: FormGroup;
  public leftTableColumn: WritableSignal<ColumnPrep[]> = signal([]);
  public rightTableColumn: WritableSignal<ColumnPrep[]> = signal([]);
  public joinList: { sourceColumn: ColumnPrep | undefined, targetColumn: ColumnPrep | undefined }[] = [];
  public submitted: WritableSignal<boolean> = signal(false);
  public joinConstants = jointKeys;
  public joinIndex: number = 0;
  public changeName = new Subject<string>();
  private subs = new SubSink();
  public duplicatedColumns: DuplicatedColumn[] = [];
  public concatedColumns: ColumnPrep[] = [];

  public readonly advanceStore = inject(selectNodeStoreAdvance);

  constructor(private _fb: FormBuilder, private _cdr: ChangeDetectorRef, public _prepService: IfpAdvancePrepService, private _toaster: ToasterService) {

  }



  ngOnInit(): void {
    this.joinForm = this._fb.group({
      joinType: [jointKeys.fieldType, Validators.required],
      leftIdentifier: ['', Validators.required],
      rightIdentifier: ['', Validators.required],
      leftColumns: this._fb.array([]),
      rightColumns: this._fb.array([])
    });
  }



  ngOnChanges(changes: SimpleChanges): void {
    if (changes['columnList'] && this.columnList) {
      this.joinList = [];
      setTimeout(() => {
        this.setTableColumns();
        this.setInitialColumns();
        this._cdr.detectChanges();
      }, 50);
    }
  }

  onSelectAll(event: boolean, type: string) {
    const form = this.joinForm.get(type) as FormArray<FormGroup<ColumnsArray>>;
    form.controls.forEach((control: FormGroup) => {
      control.patchValue({ checked: event });
    });
  }

  checkSelection(type: string) {
    const form = this.joinForm.get(type) as FormArray<FormGroup<ColumnsArray>>;
    return form.controls.every(x => x.value.checked);
  }

  selectJoinBy(event: string) {
    this.joinForm.controls['joinType'].setValue(event);
    this.byIdentifier = event;
  }


  addColumn(element: ColumnPrep) {
    const item = this._fb.group({
      columnName: this._fb.control(element.name, Validators.required),
      checked: this._fb.control(element.checked),
      rename: this._fb.control(element.rename)
    });
    return item;
  }

  onCancel() {
    this.cancel.emit();
  }

  onSave() {
    let config: any;
    if (this.joinForm.controls['joinType'].value == this.joinConstants.fieldType) {
      config = this.getFieldConfig();
    }
    if (this.joinForm.controls['joinType'].value == this.joinConstants.positionType) {
      config = {
        type: this.joinForm.controls['joinType'].value,
        columns: {
          source: this.getCheckedColumns(this.leftColumnsArrays),
          target: this.getCheckedColumns(this.rightColumns)
        }
      };
    }
    this.advanceStore.updateNode(this.currentNodeId ?? '', 'jointool updateded', connectionType.joinTool, config, 'jointool updateded', { configUpdated: true });
    this._prepService.nodeChangeDetect.next('node updated');
    // this._toaster.success('Data Saved Successfully');
    this.save.emit();
  }

  getFieldConfig() {
    const sourceNodes = this.advanceStore.selectNodeDestinationConnection().destinationConnections(this.currentNodeId ?? '');
    const config = {
      type: this.joinForm.controls['joinType'].value,
      join_by: {
        source: {
          id: sourceNodes[0]?.source,
          columns: this.getSelectedColumns('sourceColumn')
        },
        target: {
          id: sourceNodes[1]?.source,
          columns: this.getSelectedColumns('targetColumn')
        }
      },
      columns: {
        source: this.getCheckedColumns(this.leftColumnsArrays),
        target: this.getCheckedColumns(this.rightColumns)
      }
    };
    return config;
  }


  getCheckedColumns(dataArray: FormArray<FormGroup<ColumnsArray>>): CheckedColumns[] {
    if (!dataArray?.controls?.length) {
      return [];
    }
    return dataArray.controls
      .filter(element => element.value.checked)
      .map(element => {
        const data: CheckedColumns = {
          original_name: element.value.columnName
        };
        if (element.value.rename) {
          data.rename_to = element.value.rename;
        }
        return data;
      });
  }

  getSelectedColumns(input: string): string[] {
    return this.joinList.map((element: Record<string, any>) => element[input].name);
  }

  setInitialColumns() {
    this.joinIndex = 0;
    const nodeData = cloneDeep(this.advanceStore.selectNodeValue().nodeValue(this.currentNodeId ?? ''));
    if (nodeData.configuration.type) {
      this.joinForm?.controls?.['joinType'].setValue(nodeData.configuration.type);
      this.byIdentifier = nodeData.configuration.type;
    }
    const objKey = 'columns';
    if (this.leftTableColumn()?.length && this.rightTableColumn()?.length) {
      this.duplicatedColumns = this.findDuplicatesWithCount(this.leftTableColumn(), this.rightTableColumn());
      this.concatedColumns = [this.leftTableColumn(), this.rightTableColumn()].flatMap(arr => arr);
    }
    if (this.leftTableColumn()?.length) {
      this.leftTableColumn().forEach(element => {
        element.checked = (!nodeData?.configuration?.[objKey]?.source || nodeData?.configuration?.[objKey]?.source.length <= 0) ? true : this.checkSelectedOrNot(nodeData?.configuration?.[objKey]?.source, element.name);
        element.rename = this.getRenameColumns(nodeData?.configuration?.[objKey]?.source, element.name);
        this.leftColumnsArrays?.push(this.addColumn(element));
      });
    }


    if (this.rightTableColumn()?.length) {
      this.rightTableColumn().forEach(element => {
        element.checked = (!nodeData?.configuration?.[objKey]?.target || nodeData?.configuration?.[objKey]?.target.length <= 0) ? true : this.checkSelectedOrNot(nodeData?.configuration?.[objKey]?.target, element.name);
        element.rename = this.getRenameColumns(nodeData?.configuration?.[objKey]?.target, element.name);
        this.rightColumns?.push(this.addColumn(element));
      });
    }
    if (this.joinForm?.controls?.['joinType']?.value == jointKeys.fieldType) {
      this.patchJoinList(nodeData);
    }
  }


  findDuplicatesWithCount(arr1: any[], arr2: any[]) {
    const combined = [...arr1, ...arr2];
    const duplicateCountMap = combined.reduce((acc, item) => {
      acc[item.name] = (acc[item.name] || 0) + 1;
      return acc;
    }, {});

    const duplicatesWithCount = Object.keys(duplicateCountMap)
      .filter(name => duplicateCountMap[name] > 1)
      .map(name => ({
        name,
        count: duplicateCountMap[name],
        increment: -1
      }));

    return duplicatesWithCount;
  }


  checkSelectedOrNot(data: SelectColumns[], column: string) {
    return data?.find((x: any) => x[jointKeys.original_name] == column) ? true : false;
  }

  getRenameColumns(data: SelectColumns[], column: string) {
    let rename = data?.find(x => x.original_name === column)?.rename_to;
    if (!rename && this.duplicatedColumns?.length) {
      const duplicateIndex = this.duplicatedColumns.findIndex(x => x.name == column);
      if (duplicateIndex >= 0) {
        this.duplicatedColumns[duplicateIndex].increment = this.duplicatedColumns[duplicateIndex].increment + 1;
        this.concatedColumns?.forEach(element => {
          if (element.name == `${this.duplicatedColumns[duplicateIndex].name}_${this.duplicatedColumns[duplicateIndex].increment}`) {
            this.duplicatedColumns[duplicateIndex].increment = this.duplicatedColumns[duplicateIndex].increment + 1;
          }
        });
        rename = this.duplicatedColumns[duplicateIndex].increment > 0 ? `${this.duplicatedColumns[duplicateIndex].name}_${this.duplicatedColumns[duplicateIndex].increment}` : '';
      }
    }
    return rename ?? '';
  }

  patchJoinList(nodeData: any) {
    this.joinList = [];
    if (Object.keys(nodeData.configuration.join_by).length >= 2) {
      nodeData.configuration.join_by.source.columns.forEach((element: string, index: number) => {
        const join = {
          sourceColumn: this.leftTableColumn()?.find(x => x.name == element),
          targetColumn: this.rightTableColumn()?.find(x => x.name == nodeData.configuration.join_by.target.columns[index])
        };
        if (join.sourceColumn && join.targetColumn) {
          this.joinList.push(join);
        }
      });
      if (this.joinList?.length) {
        this.patchIdentifire('leftIdentifier', this.joinList[0].sourceColumn);
        this.patchIdentifire('rightIdentifier', this.joinList[0].targetColumn);
      }
    }
  }


  get leftColumnsArrays(): FormArray<FormGroup<ColumnsArray>> {
    return this.joinForm?.get('leftColumns') as FormArray<FormGroup<ColumnsArray>>;
  }

  get rightColumns(): FormArray<FormGroup<ColumnsArray>> {
    return this.joinForm?.get('rightColumns') as FormArray<FormGroup<ColumnsArray>>;
  }

  setTableColumns() {
    this.resetVariables();
    const sourceNodes: any = this.advanceStore.selectNodeDestinationConnection().destinationConnections(this.currentNodeId ?? '');

    const sourceIndex = sourceNodes.findIndex((x: { destination_anchor: number; }) => x.destination_anchor == 1);
    const targetIndex = sourceNodes.findIndex((x: { destination_anchor: number; }) => x.destination_anchor == 2);
    if (sourceIndex >= 0 && targetIndex >= 0) {
      const sourceId = sourceNodes?.[sourceIndex]?.source;
      const targetId = sourceNodes?.[targetIndex]?.source;

      const sourceColumn = `columns_${sourceNodes?.[sourceIndex]?.source_anchor}`;
      const targetColumn = `columns_${sourceNodes?.[targetIndex]?.source_anchor}`;
      if (this.columnList && (Object.keys(this.columnList).length > 0)) {
        this.leftTableColumn?.set(cloneDeep(this.columnList?.[sourceId]?.[sourceColumn]));
        this.rightTableColumn?.set(cloneDeep(this.columnList?.[targetId]?.[targetColumn]));
      }

    }

  }

  resetVariables() {
    this.leftTableColumn.set([]);
    this.rightTableColumn.set([]);
    this.leftColumnsArrays?.clear();
    this.rightColumns?.clear();
  }

  addJoin() {
    this.submitted.set(true);
    if ((this.joinForm.value['leftIdentifier'].type == columnTypes.string && this.joinForm.value['rightIdentifier'].type != columnTypes.string) || (this.joinForm.value['leftIdentifier'].type != columnTypes.string && this.joinForm.value['rightIdentifier'].type == columnTypes.string)
    ) {
      this._toaster.error('The data types of the selected source and target columns do not match. Please choose the correct columns.');
      return;
    }
    if (this.joinForm.value['leftIdentifier'].name && this.joinForm.value['rightIdentifier'].name) {
      const data = {
        sourceColumn: this.joinForm.value['leftIdentifier'],
        targetColumn: this.joinForm.value['rightIdentifier']
      };
      this.joinList.push(data);
      this.patchIdentifire('leftIdentifier', '');
      this.patchIdentifire('rightIdentifier', '');
      this.submitted.set(false);
    }
  }

  patchIdentifire(identifier: string, value: any) {
    this.joinForm?.controls?.[identifier].setValue(value);
  }

  removeJoin(index: number) {
    this.joinList.splice(index, 1);
  }

  get formControls() {
    return this.joinForm.controls;
  }



  checkDisabledStatus(_event: any) {
    setTimeout(() => {
    }, 300);
  }

  updateRename(_event: any) {
    this.changeName.next(_event.target.value);
  }


  ngOnDestroy(): void {
    if (!this._prepService.isConfigSave() && this.columnList && (Object.keys(this.columnList).length > 0)) {
      this.onSave();
    }
  }
}

export interface ColumnsArray {
  columnName: FormControl,
  checked: FormControl,
  rename: FormControl
}

export interface CheckedColumns {
  original_name: string;
  rename_to?: string;
}
