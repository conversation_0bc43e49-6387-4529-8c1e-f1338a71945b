import { Injectable, Renderer2, RendererFactory2 } from '@angular/core';

@Injectable({
  providedIn: 'root'
})
export class GetWidthService {
  private render: Renderer2;

  constructor(rendererFactory: RendererFactory2) {
    this.render = rendererFactory.createRenderer(null, null);
  }

  // function used to get width of text
  getTextWidth(label: string | number, fontsize: number = 10, fontFamilyName: string = 'Noto Sans'): number {
    const text = label?.toString();
    // creating canvas
    const canvas = this.render.createElement('canvas');
    const context =  canvas.getContext('2d');
    // adding font family and fontsize
    context.font = `${fontsize}rem ${fontFamilyName}`;
    // measuring width
    return  context.measureText(`${text}`)?.width;
  }

}
