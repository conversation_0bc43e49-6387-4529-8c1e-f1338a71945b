import { Component, input, model, Output, EventEmitter, ViewChild } from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';
import { DxpKpiCardComponent } from '../dxp-kpi-card/dxp-kpi-card.component';
import { DxpKpiChartSettings, DxpKpiPreviewTabList, DxpVisualizationWizardToolbarComponent } from "../dxp-visualization-wizard-toolbar/dxp-visualization-wizard-toolbar.component";
import { DxpKpiCustomFilter, DxpKpiCustomFilterColumnList } from '../dxp-visualization-wizard-toolbar/dxp-visualization-filter-form/dxp-visualization-filter-form.component';
import { dxpPreviewWizardTabMenu } from '../dxp-visualization-wizard-toolbar/dxp-visualization-wizard.constants';
import { ConvertedChartData } from '../dxp.interface';
import { log } from 'console';
import { SelectedProduct } from '../widgets/dxp-accordian/dxp-accordian.component';
import { IfpCardLoaderComponent } from 'src/app/scad-insights/ifp-widgets/ifp-atoms/ifp-card-loader/ifp-card-loader.component';

@Component({
  selector: 'ifp-dxp-visualization-preview',
  imports: [TranslateModule, DxpKpiCardComponent, DxpVisualizationWizardToolbarComponent, IfpCardLoaderComponent],
  templateUrl: './dxp-visualization-preview.component.html',
  styleUrl: './dxp-visualization-preview.component.scss'
})
export class DxpVisualizationPreviewComponent {
  @ViewChild('toolbarComponent') toolbarComponent!: DxpVisualizationWizardToolbarComponent;
  public tabList: DxpKpiPreviewTabList[] = dxpPreviewWizardTabMenu;
  public cardTitle = model('');
  public cardDescription = model('');
  public cardFilters: DxpKpiCustomFilter[] = [];
  public cardLegends: any[] = [];
  public defaultLegend: any = null;
  // public graphData = input<ConvertedChartData >();
  public graphData = input<ConvertedChartData | undefined>(undefined);
  public columnData = input<any| undefined>(undefined);
  public selectedProductDetails = input<SelectedProduct>();
  public xAxisLabel = input<string>('');
  public yAxisLabel = input<string>('');
  public isLoading = input<boolean>(false);
  public columnList: DxpKpiCustomFilterColumnList[] = [
    {
      name: 'Abc',
      values: [
        {id: 'Abc-value 1', value: 'Abc-value 1'},
        {id: 'Abc-value 2', value: 'Abc-value 2'},
        {id: 'Abc-value 3', value: 'Abc-value 3'},
        {id: 'Abc-value 4', value: 'Abc-value 4'},
      ]
    },
    {
      name: 'Def',
      values: [
        {id: 'Def-value 1', value: 'Def-value 1'},
        {id: 'Def-value 2', value: 'Def-value 2'},
        {id: 'Def-value 3', value: 'Def-value 3'},
        {id: 'Def-value 4', value: 'Def-value 4'},
      ]
    }
  ];
  public noData = input(false);
  public chartName: string='lineChart';
  showGraph: number = 1;
  @Output() kpiCardFiltersChanged = new EventEmitter<any[]>();
  public kpiCardFilters: any[] = [];
  @Output() submitRequested = new EventEmitter<void>();
  @Output() legendsDataChanged = new EventEmitter<any[]>();

  onCardFilterUpdate(filters: DxpKpiCustomFilter[]) {
    this.cardFilters = filters;
    console.log(this.cardFilters);
  }

  onUpdateChart(settings: DxpKpiChartSettings) {
    this.showGraph = 0;
    if (settings.chartType === 'line') {
      this.chartName = 'lineChart';
    } else if (settings.chartType === 'bar') {
      this.chartName = 'barChart';
    } else if (settings.chartType === 'circular') {
      this.chartName = 'circular'
    } else {
      this.chartName = settings.chartType;
    }
    setTimeout(() => {
      this.showGraph = 1;
    }, 1000);
  }

  onRequestGraphData() {
    // This method will be called when the legend dropdown is closed
    // The parent wizard component should handle the full API call with legends
    console.log('Requesting graph data with legends from preview component');
    this.submitRequested.emit();
  }

  onLegendsDataChanged(legends: any[]) {
    // Store legends data for display in KPI card
    this.cardLegends = legends;
    console.log('=== LEGENDS DATA DEBUG ===');
    console.log('Received legends:', legends);
    console.log('Legends length:', legends.length);
    console.log('Stored cardLegends:', this.cardLegends);
    
    // Update default legend based on the received data
    if (legends.length === 1) {
      // Single legend received - this is the new default
      this.defaultLegend = legends[0];
      console.log('Single legend - Default legend updated in preview:', this.defaultLegend);
    } else if (legends.length > 0) {
      // Multiple legends received - check if we need to update default
      if (!this.defaultLegend) {
        // No default set yet, use the first one
        this.defaultLegend = legends[0];
        console.log('Multiple legends - Initial default legend set in preview:', this.defaultLegend);
      } else {
        // Check if current default is still in the legends array
        const currentDefaultExists = legends.find(legend => legend.name === this.defaultLegend.name);
        if (!currentDefaultExists) {
          // Current default not found, use the first one
          this.defaultLegend = legends[0];
          console.log('Default legend updated because previous default not found:', this.defaultLegend);
        }
      }
    }
    
    // Always store all legends for the dropdown, not just the default
    console.log('Final cardLegends array:', this.cardLegends);
    console.log('Final defaultLegend:', this.defaultLegend);
    console.log('=== END LEGENDS DATA DEBUG ===');
    
    // Pass legends data to parent wizard component
    console.log('Legends data received in preview:', legends);
    this.legendsDataChanged.emit(legends);
  }

  onKpiCardFiltersChanged(filters: any[]) {
    console.log('Preview received filters from KPI card:', filters);
    this.kpiCardFilters = filters;
    this.kpiCardFiltersChanged.emit(filters);
    // Optionally, log or trigger further actions here
    // console.log('KPI Card Filters:', this.kpiCardFilters);
  }

  // Add this method to handle filter changes from the toolbar
  onFilterSettingsChange(filters: any[]) {
    console.log(filters);
    this.cardFilters = filters;
    this.kpiCardFiltersChanged.emit(filters);
    this.triggerSubmit();
  }

  // Add this method to handle default legend changes from the KPI card
  onDefaultLegendChange(selectedLegend: any) {
    console.log('Legend changed in preview from KPI card:', selectedLegend);
    this.defaultLegend = selectedLegend;
    // Emit the default legend change to parent component
    this.legendsDataChanged.emit([selectedLegend]);
    // Trigger graph data request
    this.onRequestGraphData();
  }

  // Add this method to handle legend dropdown changes from the KPI card
  onLegendDropdownChanged(selectedLegend: any) {
    console.log('Legend dropdown changed in preview from KPI card:', selectedLegend);
    
    // Emit to toolbar to sync the "Choose Legend" radio buttons
    this.legendsDataChanged.emit([selectedLegend]);
    
    // Also emit to parent wizard component to handle the sync
    this.submitRequested.emit();
  }

  // This method should call the parent's onSubmitClick (if available via Output or service)
  triggerSubmit() {
    this.submitRequested.emit();
    // need to use an Output or service to notify the parent wizard to call onSubmitClick
  }
}
