export const contentType: Record<string, string> = {
  scad_official_indicator: 'statistics-insights',
  'statistics-insights': 'statistics-insights',
  'analytical-apps': 'analytical-apps',
  'analytical_apps': 'analytical_apps',
  'official-insights': 'official-insights',
  'innovative-insights': 'innovative-insights',
  'newsletters': 'newsletters',
  'powerbi-dashboard': 'newsletters',
  'Forecasts': 'Forecasts'
};

export const contentTypeDashboard: Record<string, string> = {
  'innovative-insights': 'experimental_statistics',
  'analytical-apps': 'analytical_apps',
  'statistics-insights': 'official_statistics',
  'scad_official_indicator': 'official_statistics',
  'analytical_apps': 'analytical_apps',
  'compare-statistics': 'compare_statistics',
  'official-insights': 'official-insights'
};

export const indicatorType =  {
  scad: 'scad',
  officialStatics: 'Official Statistics',
  innovativeStatics: 'Experimental Statistics',
  coi: 'coi',
  powerbiDashboard : "powerbi-dashboard"
};

export const appType =  {
  tableau_internal: {
    name: 'tableau_internal',
    urlPrefix: 'dashboard'
  },
  insights_discovery: {
    name: 'insights_discovery',
    urlPrefix: 'insight-discovery'
  },
  scad_official_indicator: {
    name: 'scad_official_indicator',
    urlPrefix: 'statistics-insights'
  },
  internal: {
    name: 'internal',
    urlPrefix: 'scenario-driver'
  },
  officialInsights: {
    name: 'official_insights',
    urlPrefix: 'statistics-insights',
    type: 'official-insights'
  },
  innovativeInsights: {
    name: 'innovative_insights',
    urlPrefix: 'statistics-insights',
    type: 'innovative-insights'
  },
  newsletters: {
    name: 'newsletters',
    urlPrefix: 'common-dashboard'
  },
  basket_insights: 'basket_insights',
  eci_insights: 'eci_insights',
  liveability: 'livability',
  what_if: {
    name: 'what_if',
    urlPrefix: 'whatif'
  },
  correlation: {
    name: 'correlation',
    urlPrefix: 'correlation'
  }
};
