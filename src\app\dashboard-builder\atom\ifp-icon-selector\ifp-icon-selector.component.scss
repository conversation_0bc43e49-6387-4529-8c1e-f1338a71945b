@use "../../../../assets/ifp-styles/abstracts/index" as *;
:host {
  display: block;
}
.ifp-toolbar-icon {
  text-align: center;
  cursor: pointer;
  &__icon .ifp-icon,
  &__name {
    transition: 0.3s;
    color: $ifp-color-grey-6;
  }
  &__icon {
    border: 1px solid $ifp-color-grey-7;
    border-radius: 4px;
    margin-bottom: $spacer-1;
    position: relative;
    height: 65px;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    transition: 0.3s;
    &:last-child {
      margin-bottom: $spacer-0;
    }
    & > .ifp-icon {
      font-size: $ifp-fs-7;
    }
  }
  &__tick {
    opacity: 0;
    visibility: hidden;
    position: absolute;
    top: 0;
    right: 0;
    width: 20px;
    height: 20px;
    background-color: $ifp-color-secondary-blue-dark;
    border-radius: 50%;
    transform: translate(50%, -50%);
    .ifp-icon {
      font-size: $ifp-fs-1;
      color: $ifp-color-white-global;
    }
  }
  &--small {
    .ifp-toolbar-icon {
      &__icon {
        height: 50px;
        & > .ifp-icon {
          font-size: $ifp-fs-5;
        }
      }
    }
  }
  &--dsiabled {
    pointer-events: none;
    .ifp-toolbar-icon {
      &__icon {
        .ifp-icon {
          color: $ifp-color-grey-7;
        }
      }
      &__name {
        color: $ifp-color-grey-7;
      }
    }
  }
  &--active,
  &:hover {
    .ifp-toolbar-icon {
      &__icon {
        border: 1px solid $ifp-color-secondary-blue-dark;
        & > .ifp-icon{
          color: $ifp-color-secondary-blue-dark;
        }
      }
      &__name {
        color: $ifp-color-black;
      }
    }
  }
  &--active {
    .ifp-toolbar-icon {
      &__tick {
        opacity: 1;
        visibility: visible;
      }
    }
  }
}
