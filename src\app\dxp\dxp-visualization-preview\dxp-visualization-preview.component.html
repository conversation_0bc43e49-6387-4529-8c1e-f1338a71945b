<div class="ifp-dxp-preview">
  <div class="ifp-dxp-preview__head">
    <h6 class="ifp-dxp-preview__title">{{'Visualization' | translate}}</h6>
  </div>
  <div class="ifp-dxp-preview__body">
    <div class="ifp-dxp-preview__body-left">
      @if (isLoading() === true) {
        <div class="ifp-dxp-preview__loader">
          <app-ifp-card-loader [type]="'large'"></app-ifp-card-loader>
        </div>
      } @else if (noData()) {
        <div class="ifp-dxp-preview__no-data">
          <div class="ifp-dxp-preview__image-wrapper">
          <img src="../../../assets/images/no-data-chart.svg">
          <p class="ifp-dxp-preview__no-data-text">Add at least one attribute to generate the chart. </p>

          </div>

        </div>

      }@else {
        @if (showGraph && graphData()?.category?.length){
  <ifp-dxp-kpi-card class="ifp-dxp-preview__card" [(title)]="cardTitle" [chartName]="chartName" [graphData]="graphData()??{
      category: [],
      series: [{
        name: '',
        data: []
    }]
    }" [(description)]="cardDescription" [filters]="cardFilters" [legends]="cardLegends" [defaultLegend]="defaultLegend" [xAxisLabel]="xAxisLabel()" [yAxisLabel]="yAxisLabel()" (filtersChanged)="onKpiCardFiltersChanged($event)" (defaultLegendChanged)="onDefaultLegendChange($event)" (legendDropdownChanged)="onLegendDropdownChanged($event)" (requestGraphData)="onRequestGraphData()"></ifp-dxp-kpi-card>
      }
      }

    </div>
    <div class="ifp-dxp-preview__body-right">
      <ifp-dxp-visualization-wizard-toolbar [selectedProductDetails]="selectedProductDetails()" [tabList]="tabList" [columnList]="columnData()" [(cardTitle)]="cardTitle" [(cardDescription)]="cardDescription" (filterSettingsChange)="onFilterSettingsChange($event)" (chartSettingsChange)="onUpdateChart($event)" (requestGraphData)="onRequestGraphData()" (legendsDataChange)="onLegendsDataChanged($event)" #toolbarComponent>

      </ifp-dxp-visualization-wizard-toolbar>
    </div>
  </div>
</div>
