import { ChangeDete<PERSON><PERSON><PERSON>, <PERSON>mponent, <PERSON><PERSON><PERSON><PERSON>, On<PERSON>nit, ViewChild, signal } from '@angular/core';
import { IfpDataTableComponent } from '../../../organism/ifp-data-table/ifp-data-table.component';
import { IfpButtonComponent } from '../../../../scad-insights/ifp-widgets/ifp-atoms/ifp-button/ifp-button.component';
import { IfpDataPreviewComponent } from '../../../organism/ifp-data-preview/ifp-data-preview.component';
import { buttonClass, buttonIconPosition } from 'src/app/scad-insights/core/constants/button.constants';
import { TranslateModule } from '@ngx-translate/core';
import { IfpPrepService } from '../ifp-prep-service';
import { SubSink } from 'subsink';
import { Store } from '@ngrx/store';
import { selectNodePrepResponse } from '../store/node-store/node-store.selectors';
import { prepsApiEndpoints } from '../constants/if-preps.constants';
import { CustomEventEmit } from '../interface/ifp-data-prep.interface';
import { FormArray, FormControl, FormGroup } from '@angular/forms';
import { loadCreateNodeSuccessImputName, loadCreateNodeSuccessImputUpdateName, loadNode, loadRemoveConnectionUploadUpdateName } from '../store/node-store/node-store.actions';
import { prepStatusStore, workFlowState } from '../constants/ifp-state.contants';
import { ColumnUpstream, Nullcolumn } from '../ifp-prep-select-data/interface/prep-selection.interface';
import { Router } from '@angular/router';
import { IfpNoDataComponent } from '../../../../scad-insights/ifp-widgets/ifp-molecules/ifp-no-data/ifp-no-data.component';
import { IfpSpinnerComponent } from '../../../../scad-insights/ifp-widgets/ifp-molecules/ifp-spinner/ifp-spinner.component';
import { IfpPrepPopupComponent } from '../../../organism/ifp-prep-popup/ifp-prep-popup.component';
import { IfpModalComponent } from '../../../../scad-insights/ifp-widgets/ifp-organism/ifp-modal/ifp-modal.component';
import { CardData } from 'src/app/shared/molecule/ifp-tool-card/ifp-tool-card.component';

@Component({
  selector: 'ifp-data-prep-impute',
  standalone: true,
  templateUrl: './ifp-data-prep-impute.component.html',
  styleUrl: './ifp-data-prep-impute.component.scss',
  imports: [IfpDataTableComponent, IfpButtonComponent, IfpDataPreviewComponent, TranslateModule, IfpNoDataComponent, IfpSpinnerComponent, IfpPrepPopupComponent, IfpModalComponent]
})
export class IfpDataPrepImputeComponent implements OnInit, OnDestroy{
  @ViewChild('modalSucess') modalSucess!: IfpModalComponent;
  public tableHead: string[] = ['Fields', 'Method', 'Total rows with Missing values', ''];
  public buttonClass = buttonClass;
  public postion = buttonIconPosition;

  public tableData:any[] = [];
  public loaderProcess = signal(false);
  public loader= true;
  public processed = false;
  public subs = new SubSink();
  public objectId !: string;
  public formControl: FormArray<FormGroup<{ name: FormControl<string | null>; opreation: FormControl<string | null>; customField:  FormControl<string | null>;}>> | FormArray<never>  | any= new FormArray([]);
  public resetView = signal(false);
  constructor(private _router: Router, public _prepService:IfpPrepService, public _store:Store, private _cdr:ChangeDetectorRef) {
  }

  ngOnInit(): void {
    if (!this._prepService.uploadedFileResponse()) {
      this._router.navigate(['analytics/data-preparation/upload-data']);
    } else {
      this._prepService.showPreview.set(true);
      this._store.dispatch(loadNode[loadCreateNodeSuccessImputName]({config: { columns: {}}}));
      this.subs.add(
        this._store.select(selectNodePrepResponse).subscribe(dataState => {
          if (dataState.currentStatus === prepStatusStore.imputeDataLoadeded ) {
            const nodeLength = dataState.data.nodes.length;
            const lastValue = dataState.data.nodes[nodeLength - 1];
            this.objectId =lastValue.object_id;
            this._prepService.processStatus.next(null);

            const column =     this._prepService.postMethodRequest(`${prepsApiEndpoints.selectNode}${lastValue.object_id}${prepsApiEndpoints.coloumUpstrem}`, dataState.data).subscribe((value: Record<string, ColumnUpstream>)=> {
              const dataColumn = Object.values(value)[0];
              const currentValue =  this.formControl.value;
              this.formControl=  new FormArray([]);
              this.tableData = [];
              let nullCount = false;
              if (dataColumn?.null_columns?.[0]?.null_percentage ) {
                nullCount = true;
              }
              dataColumn.null_columns.forEach((dataImputeValue: Nullcolumn, index) => {

                const options:{content_type: Record<string, string>;id:number;name: string;icon_path: string;nodeCount: number;isSelected: boolean;domains:[];nodes:[];showTree:boolean;key:string} [] = [];
                dataImputeValue.impute_options?.forEach(data => {
                  options.push({content_type: { 'method': data.method}, id: 3, name: data.label, icon_path: '', nodeCount: 1, isSelected: true, domains: [], nodes: [], showTree: false, key: data.method}
                  );
                });
                const indexOption = options.findIndex(data => data.content_type['method'] === currentValue?.[index]?.type?.content_type?.method);
                this.formControl.push(
                  new FormGroup(   {
                    name: new FormControl(dataImputeValue.name),
                    type: new FormControl(currentValue?.[index]?.type && indexOption !== -1 ? currentValue[index].type : {content_type: { 'method': dataImputeValue.impute_options[0].method}, id: 3, name: dataImputeValue.impute_options[0].label, icon_path: '', nodeCount: 1, isSelected: true, domains: [], nodes: [], showTree: false, key: dataImputeValue.impute_options[0].method}
                    ),
                    currentValue: new FormControl(currentValue?.[index]?.type && indexOption !== -1 ? currentValue[index].type : {content_type: { 'method': dataImputeValue.impute_options[0].method}, id: 3, name: dataImputeValue.impute_options[0].label, icon_path: '', nodeCount: 1, isSelected: true, domains: [], nodes: [], showTree: false, key: dataImputeValue.impute_options[0].method}
                    ),
                    custom: new FormControl(''),
                    disabled: new FormControl( currentValue?.[index]?.type && indexOption !== -1 ? currentValue[index].type?.content_type?.method!== 'custom' :  dataImputeValue.impute_options[0].method !== 'custom'),
                    options: new FormControl(options)
                  })
                );
                if (nullCount) {
                  this.tableData.push([
                    { key: dataImputeValue.name, title: dataImputeValue.name, value: dataImputeValue.name, type: 'defualt' },
                    {
                      key: 'method',
                      title: 'method',
                      type: 'custom',
                      sortField: 'name',
                      sortType: 'string',
                      renderComponent: () => import('src/app/dashboard-builder/molecule/ifp-db-dropdown/ifp-db-dropdown.component').then(mode => mode.IfpDbDropdownComponent),
                      inputValues: {
                        options: options,
                        isMultiSelect: false,
                        selectedSingleItem: currentValue?.[index]?.type && indexOption !== -1? currentValue[index].type :  options[0],
                        key: 'name',
                        formControls: this.formControl?.controls[index]?.controls?.type
                      },
                      outputs: ['singleSelected', 'multiSelected']
                    },
                    { key: 'calc', title: `${dataImputeValue?.metadata}(${dataImputeValue?.null_percentage < 1 ? '<1' : dataImputeValue?.null_percentage?.toFixed(2)}%)`, value: `${dataImputeValue?.null_count}(${dataImputeValue?.null_percentage < 1 ? '<1' : dataImputeValue?.null_percentage?.toFixed(2)}%)`, type: 'defualt' },
                    {
                      key: 'Custom',
                      title: 'Custom',
                      type: 'custom',
                      sortField: 'name',
                      sortType: 'string',
                      renderComponent: () => import('src/app/ifp-analytics/atom/ifp-input/ifp-input.component').then(mode => mode.IfpInputComponent),
                      inputValues: {
                        formControls: this.formControl?.controls[index]?.controls?.custom,
                        disableForm: this.formControl?.controls[index]?.controls?.disabled
                      },
                      outputs: ['dataChangeEvent']
                    }
                  ]);
                } else {
                  this.tableHead = ['Fields', 'Method', ''];
                  this.tableData.push([
                    { key: dataImputeValue.name, title: dataImputeValue.name, value: dataImputeValue.name, type: 'defualt' },
                    {
                      key: 'method',
                      title: 'method',
                      type: 'custom',
                      sortField: 'name',
                      sortType: 'string',
                      renderComponent: () => import('src/app/dashboard-builder/molecule/ifp-db-dropdown/ifp-db-dropdown.component').then(mode => mode.IfpDbDropdownComponent),
                      inputValues: {
                        options: options,
                        isMultiSelect: false,
                        selectedSingleItem: currentValue?.[index]?.type && indexOption !== -1? currentValue[index].type :  options[0],
                        key: 'name',
                        formControls: this.formControl?.controls[index]?.controls?.type
                      },
                      outputs: ['singleSelected', 'multiSelected']
                    }, {
                      key: 'Custom',
                      title: 'Custom',
                      type: 'custom',
                      sortField: 'name',
                      sortType: 'string',
                      renderComponent: () => import('src/app/ifp-analytics/atom/ifp-input/ifp-input.component').then(mode => mode.IfpInputComponent),
                      inputValues: {
                        formControls: this.formControl?.controls[index]?.controls?.custom,
                        disableForm: this.formControl?.controls[index]?.controls?.disabled
                      },
                      outputs: ['dataChangeEvent']
                    }
                  ]);
                }
              });
              this.loader = false;
              column.unsubscribe();
            } );
          }
        })
      );
    }

  }



  closeSucessModal() {
    this.modalSucess.removeModal();
  }

  buttonClick(_event: CardData) {
    this.modalSucess?.removeModal();
  }

  eventsCustom(event: CustomEventEmit) {
    this.resetView.set(false);
    this.formControl.value?.forEach((element:FormInterface) => {
      if (element.custom !== '') {
        this.resetView.set(true);
      }
      if (element?.type?.content_type?.method !== element?.currentValue?.content_type?.method) {
        this.resetView.set(true);
      }
    });
    if (event?.value?.event !== 'input') {
      if ( event?.value?.key == 'custom') {
        this.formControl.controls[event.index ?? 0].controls.disabled.setValue(false);
      } else {
        this.formControl.controls[event.index ?? 0].controls.disabled.setValue(true);
      }
    }
  }

  reset() {
    this.resetView.set(false);
    this.formControl.value?.forEach((data: {name: string;
      type: {content_type: Record<string, string>};
      custom: string;
      disabled: boolean;
      options: {label: string;method: string;content_type?: {method?: string;};}[];}, index: number) => {
      this.formControl.controls[index?? 0].controls.type.setValue( data?.options[0]);
      this.formControl.controls[index?? 0].controls.custom.setValue('');
      this.formControl.controls[index?? 0].controls.disabled.setValue( data?.options[0]?.content_type?.method !== 'custom');
    });
  }

  proceed() {
    const configration: Record<string, any> = {};
    this.formControl.value?.forEach((element: {name: string;
      type: {content_type: Record<string, string>}; custom: string;
      disabled: boolean;}) => {
      if (element.disabled ) {
        configration[element.name]= {
          method: element.type.content_type?.['method']
        };
      } else if ( element.custom &&  element.custom?.trim() !== '') {
        configration[element.name]= {
          method: element.type.content_type?.['method'],
          value: element.custom
        };
      }
    });
    this._prepService.disableSidebar.set(true);
    this._prepService.processActive.set(true);
    this.loaderProcess.set(true);
    this.tableData =[];
    this._store.dispatch(loadNode[loadCreateNodeSuccessImputUpdateName]({config: { columns: configration}, objectId: this.objectId}));
    const process = this._prepService.processStatus.subscribe(data=> {
      if (data?.workflow_status === workFlowState.completed) {
        this._prepService.isShowPreview = true;
        // this.processed = true;
        this._prepService.disableSidebar.set(false);
        this.loaderProcess.set(false);
        process.unsubscribe();
        this._prepService.processActive.set(false);
        this._store.dispatch(loadNode[loadCreateNodeSuccessImputName]({config: { columns: {}}}));
        this.modalSucess.createElement();
        this._cdr.detectChanges();
      } else if (data?.workflow_status === workFlowState.error) {
        this._prepService.isShowPreview = false;
        this.processed = false;
        this._prepService.disableSidebar.set(false);
        this.loaderProcess.set(false);
        process.unsubscribe();
      }
    });
  }

  ngOnDestroy(): void {
    if ( !this.processed) {
      this._store.dispatch(loadNode[loadRemoveConnectionUploadUpdateName]({ objectId: this.objectId}));
    }
    this.subs.unsubscribe();
    this.modalSucess?.removeModal();
    this._prepService.showPreview.set(false);
  }
}
interface FormInterface {
  name: string;
  type: Type;
  currentValue: CurrentValue;
  custom: string;
  disabled: boolean;
  options: Option[];
}

interface Option {
  content_type: Contenttype;
  id: number;
  name: string;
  icon_path: string;
  nodeCount: number;
  isSelected: boolean;
  domains: any[];
  nodes: any[];
  showTree: boolean;
  key: string;
  index?: number;
}

interface CurrentValue {
  content_type: Contenttype;
  id: number;
  name: string;
  icon_path: string;
  nodeCount: number;
  isSelected: boolean;
  domains: any[];
  nodes: any[];
  showTree: boolean;
  key: string;
}

interface Type {
  content_type: Contenttype;
  id: number;
  name: string;
  icon_path: string;
  nodeCount: number;
  isSelected: boolean;
  domains: any[];
  nodes: any[];
  showTree: boolean;
  key: string;
  index: number;
}

interface Contenttype {
  method: string;
}
