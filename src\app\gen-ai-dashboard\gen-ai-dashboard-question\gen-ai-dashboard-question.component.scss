@use "../../../assets/ifp-styles/abstracts/index" as *;
.ifp-gen-ai-dashboard-question {
  position: relative;
  border: 1px solid $ifp-color-grey-7;
  border-radius: 10px;
  &__qus-badge {
    margin-inline-start: $spacer-2;
  }
  &__qus {
    font-weight: $fw-bold;
  }
  &__qus-outer {
    display: flex;
  }
  &__edit-icons {
    margin-inline-start: auto;
    display: flex;

  }
  &__badge-outer {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  &__badge-wrapper {
    display: flex;
    align-items: center;
    margin: $spacer-2 (-$spacer-3) $spacer-0;
    flex-wrap: wrap;
  }
  &__qus-wrapper {
    padding: $spacer-3 $spacer-5 ;
  }
  &__source{
    font-weight: $fw-bold;
  }
  &__badge-text {
    align-items: center;
    display: flex;
    margin: $spacer-2 $spacer-3;
  }
  &__feed-resp-inner {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid $ifp-color-grey-7;
  }
  &__response-badge {
    padding: $spacer-2 $spacer-5;
    background-color:  $ifp-color-blue-1;
    border-radius: 30px;
    margin-inline-end: $spacer-4;
    color: $ifp-color-blue-menu;
    white-space: nowrap;
  }
  &__feed-resp {
    margin-inline-start: auto;
    display: flex;
    align-items: center;
    &--active{
      .ifp-gen-ai-dashboard-question {
        &__feed-resp-inner {
          background-color: $ifp-color-blue-1;
          color: $ifp-color-primary-blue;
          border: 1px solid $ifp-color-blue-menu;
        }
      }



    }
  }
  &__badge, &__source, &__classification{
    margin-inline-start: $spacer-2;
  }
  &__badge-separator {
    color: $ifp-color-grey-13;
  }
  &__exp-resp-icon {
    margin-inline-end: $spacer-2;
  }
  &__exp-resp-icon ,&__feed-resp-icon{
    position: relative;
    top: 2px;

  }
  &__edit-icon {
    width: 32px;
    height: 32px;
    border: 1px solid $ifp-color-grey-7;
    display: flex;
    justify-content: center;
    align-items: center;
    top: 2px;
    border-radius: 50%;
    cursor: pointer;
    margin-inline-end: $spacer-2;
    &:last-child {
      margin-inline-end: $spacer-0;
    }
    &::before {
      position: relative;
      top: 2px;
    }
  }

&__exp-arrow-icon {
  display: inline-block;
  transition: 0.3s;
  color: $ifp-color-grey-2;
  margin-inline-start: $spacer-2;
  &::before {  position: relative;
    top: 1px;}
}
&__exp-resp {
  color: $ifp-color-blue-menu;
  margin-inline-start: auto;
  padding: 4px 16px;
  cursor: pointer;
  transition: 0.3s;
  display: flex;
  align-items: center;
  white-space: nowrap;
   &--active{

    border-radius: 20px;
    background-color: $ifp-color-blue-1;

     .ifp-gen-ai-dashboard-question__exp-arrow-icon {

      transform: rotate(180deg);
     }

  }
}
  &__response{
  padding: $spacer-3 $spacer-5;
   background-color: $ifp-color-grey-18;
   border-radius: 0 0 10px 10px;
  }

  &__response-text {
    background-color:$ifp-color-white;
    min-height: 100px;
    border-radius: 7px;
    padding: $spacer-3;
  }
  &__prevew-footer {
    display: flex;
    align-items: center;
  }
  &__preview-rating-text {
    font-weight: $fw-bold;
    color: $ifp-color-black;
margin-inline-start: $spacer-2;
  }
  &__preview-rating-icon {
    font-size: 52px;
    color: $ifp-color-orange;
  }
  &__response-text-wrapper {
    background-color: $ifp-color-white;
    border-radius: 7px;
    &--ai {
      padding: $spacer-1;
      position: relative;
      &::after {
        content: "";
        width: 100%;
        height: 100%;
        border-radius: 7px;
        background-image: linear-gradient(30deg, #126DEF 0%, #3EBEF2 31%, #8A7DFA 60%,#0154E9 100% );
        position: absolute;
        top: 0;
        left: 0;
        opacity: 0.2;
      }
      .ifp-gen-ai-dashboard-question__response-text {
        border: 1px solid $ifp-color-blue-menu;
        position: relative;
        z-index: 1;
      }
    }
  }
  &__exp-resp-wrapper {
    margin-bottom: $spacer-2;
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;
  }
  &__resp-type {
    &--time {
      padding-inline-start: $spacer-3;
      margin-inline-start: $spacer-3;
      border-inline-start: 1px solid  $ifp-color-grey-13;
    }
  }
  &__footer-left,
  &__footer-right {
    display: flex;
    align-items: center;
  }
  &__footer {
    padding: $spacer-0 $spacer-3;
    background-color:  $ifp-color-grey-17;
    border-radius: 0 0 10px 10px;
    &--preview {
      display: flex;
      justify-content: space-between;
      align-items: center;
      flex-wrap: wrap;
      padding: $spacer-3 $spacer-5 $spacer-3;
      .ifp-gen-ai-dashboard-question {
        &__footer-left,
        &__footer-right {
          margin-top: $spacer-3;
        }
      }
    }
  }
  &__footer-sec-1,
  &__footer-sec-2 {
    padding: $spacer-4 $spacer-0;
    &--100{
      width: 100%;
    }
  }
  &__footer-sec-1 {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
  }
  &__footer-sec-2 {
    border-top: 1px solid $ifp-color-grey-7;
  }
  &__btn-wrapper {
    display: flex;
    flex-wrap: wrap;
  }
  &__preview-value{
    font-weight: $fw-bold;
    color: $ifp-color-black;
    margin: $spacer-0 $spacer-3;
  }
  &__feedback-value {
    margin-inline-start: $spacer-4;
    font-weight: $fw-bold;
  }
  &__footer-text {
    font-weight: $fw-bold;
    font-size: $ifp-fs-4;
  }
  &__footer-item {
    margin-inline-start: $spacer-4;
  }
  &__footer-title {
    font-size: $ifp-fs-4;
    margin-bottom: $spacer-2;
  }
  &__btn-text {
    color: $ifp-color-grey-14;
  }
  &__btn-sec {
    display: flex;
    align-items: center;
    padding-inline-end: $spacer-4;
    position: relative;
    margin-block: $spacer-2;
    margin-inline-end: $spacer-4;
    &::after {
      content: "";
      width: 1px;
      height: 50%;
      background-color: $ifp-color-grey-13;
      position: absolute;
      top: 50%;
      right: 0;
      transform: translateY(-50%);
    }
    .ifp-gen-ai-dashboard-question__footer-btn {
      margin-inline-start: ($spacer-2 + 2px);
    }
    &:last-child {
      padding-inline-end: $spacer-0;
      margin-inline-end: $spacer-0;
      &::after {
        content: none;
      }
    }
  }
  &__btm-text {
    color: $ifp-color-grey-14;
    margin-inline-end: $spacer-2;
  }
  &__footer-slider-wrapper {
    display: flex;
    background-color: $ifp-color-white;
    align-items: center;
    padding: $spacer-2;
    border-radius: 7px;
    color: $ifp-color-grey-9;
        box-shadow: 0 18.35px 31.66px rgba(122, 128, 135, .20);
        &--preview {
          margin-top: $spacer-0;
          padding: $spacer-3;
        }
  }
  &__footer-status {
    display: flex;
    justify-content: space-between;
    border: 1px solid $ifp-color-grey-7;
    padding: $spacer-2 $spacer-3;
    border-radius: 7px;
    min-width: 150px
  }
  &__footer-rating-text {
    margin: $spacer-0 $spacer-2;

  }
  &__footer-slider {
    display: block;
    width: 200px;
  }
  &__footer-slider-outer {
    margin-inline-end: $spacer-2;
  }
  &__footer-value {
    font-weight: $fw-bold;
    color: $ifp-color-black;
  }
  &__footer-rating-icon {
    font-size: 32px;
  }
  &__icon-blue {
    color: $ifp-color-blue-menu;
  }
  &__preview-feedback {
    color: $ifp-color-blue-hover;
  }
  &__rating {
    margin-inline-start: $spacer-3;
  }
  &__rating-value {
    font-size: $ifp-fs-6;
    font-weight: $fw-bold;
    margin-inline-start: $spacer-3;
  }
    &__expand {
    display: flex;
    justify-content: flex-end;
    margin-top: $spacer-2;
  }
  &__plus {
    background-color: $ifp-color-blue-menu;
    border-radius: 50%;
    padding: $spacer-3;
    width: 40px ;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: $ifp-color-white;
    position: absolute;
    left: calc(50% - 20px) ;
    bottom: -20px;
    cursor: pointer;
  }
  &__plus-icon {
    position: relative;
    top:2px;
  }
  &--feedback {
    display: flex;
    .ifp-gen-ai-dashboard-question{
    &__response {
      width: 50%;
      display: flex;
      flex-direction: column;
      border-radius: 0;
      &:last-child {
        padding-inline-start: $spacer-3;
      }
      &:first-child{
        padding-inline-end: $spacer-3;
      }
    }
    &__response-text-wrapper {
      height: 100%;
    }
  }
  }
}

:host {
  display: block;

  &::ng-deep {
    .customMarkdown{
      h2,
      h4 {
        font-weight: $fw-semi-bold;
        margin: $spacer-0 $spacer-0 $spacer-2;
      }
      p {
        margin-top: $spacer-0;
      }
    }
    .ifp-gen-ai-dashboard-question{
       &__badge {
      .ifp-badge{
        border: 1px solid $ifp-color-grey-14;
        padding: $spacer-2 $spacer-4;
        color: $ifp-color-grey-14;
      &__label {
        color: $ifp-color-grey-14;
      }
    }
    }
    &__classification {
      .ifp-badge {
        padding: $spacer-1 $spacer-2;
      }
    }
    &__footer-btn  {
      .ifp-btn {
        // min-width: 100px;
        .ifp-btn__icon {
          font-size: $ifp-fs-4;
        }
      }
    }
    &__drop {
      .ifp-dropdown {
        max-width: none;
      }
    }
    &__footer {
      .ifp-btn {
        padding: ($spacer-1 + 2px) $spacer-3;
      }
    }
  }
  }
}



:host-context(.ifp-dark-theme) {
  .ifp-gen-ai-dashboard-question{
    &__exp-resp{
         &--active {
          background-color: $ifp-color-section-white;
  }

}
&__feed-resp--active {
  .ifp-gen-ai-dashboard-question {
    &__feed-resp-inner {
      background-color: $ifp-color-section-white;
    }
  }
}
&__response-badge {

  background-color: $ifp-color-section-white;
}
&__footer {
  background-color: $ifp-color-section-white;
}
}
}
