<div class="ifp-container">
  <app-ifp-breadcrumbs [pageData]="pageData"></app-ifp-breadcrumbs>
</div>

<div class="ifp-static ifp-contact">
  <div class="ifp-container">
    <h1 class="ifp-module-heading">{{'Contact Us' | translate}}</h1>
    <div class="ifp-contact__sec">
      <div class="ifp-contact__sec-1" *ngIf="radio">
        <p class="ifp-contact__title">{{'My concern is about' | translate}}:</p>
        <p class="ifp-contact__invalid" *ngIf="ifConcernInvalid"><img src="../../../assets/images/error.svg" alt="">{{'Please provide your concern.' | translate}}</p>
        <div class="ifp-contact__radio">
          <app-ifp-checkbox [type]="'radio'" [id]="'query'" [label]="'I have an Issue/Query'" [name]="'concern'" [enableFor]="true" (checked)="isChecked('CONTACTUS.ISSUE_QUERY')"></app-ifp-checkbox>
        </div>
        <div class="ifp-contact__radio">
          <app-ifp-checkbox [type]="'radio'" [id]="'feature'" [label]="'I have a feature/data model suggestion'" name="'concern'" [enableFor]="true" (checked)="isChecked('CONTACTUS.FEATURE')"></app-ifp-checkbox>
        </div>
        <div>
          <p class="ifp-contact__title">{{'Please elaborate your concern' | translate}}:</p>
          <p class="ifp-contact__invalid" *ngIf="ifTextAreaInvalid"><img src="../../../assets/images/error.svg" alt="">{{'Please fill out this field' | translate}}</p>
          <textarea cols="30" rows="10" class="ifp-textarea" #contactTextArea [(ngModel)]="myConcern" [maxlength]="messageLimit"  title="{{'Please fill out this field' | translate}}" required></textarea>
          <p class="ifp-contact__message-info"><em class="ifp-icon ifp-icon-information"></em>{{'Maximum limit of'| translate}} ({{myConcern.length}}/{{messageLimit}}) {{'characters allowed'| translate}} </p>
        </div>
        <ifp-button [label]="'Submit'" [buttonClass]="subject !== '' && myConcern !== '' ? buttonClass.primary : buttonClass.disabled" class="ifp-contact__button" (ifpClick)="submitForm()"></ifp-button>
      </div>
      <div class="ifp-contact__sec-2">
        <iframe title="" src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3630.5262923204864!2d54.386226275094124!3d24.50186495951346!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x3e5e4161a74fcc95%3A0xc3a515bc5f563cde!2z2YXYsdmD2LIg2KfZhNil2K3Ytdin2KEgLSDYo9io2YjYuNio2YogU3RhdGlzdGljcyBDZW50ZXIgQWJ1IERoYWJp!5e0!3m2!1sen!2sae!4v1690488314159!5m2!1sen!2sae" width="600" height="450" style="border:0;" allowfullscreen="" loading="lazy" referrerpolicy="no-referrer-when-downgrade"></iframe>
        <div class="ifp-contact__location">
          <!-- <h4 class="ifp-contact__address-head">Address</h4> -->
          <p class="ifp-contact__address"><em class="ifp-icon ifp-icon-location-icon"></em>{{'Department of Government Enablement twofour54 Building, Abu Dhabi' | translate}}</p>
          <div class="ifp-contact__address-wrapper">
            <a href="tel:800 555" class="ifp-contact__address ifp-contact__address--link"><em class="ifp-icon ifp-icon-phone"></em>800 555</a>
            <a href="mailto:<EMAIL>" class="ifp-contact__address ifp-contact__address--link"><em class="ifp-icon ifp-icon-mail"></em>{{'<EMAIL>'}}</a>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
