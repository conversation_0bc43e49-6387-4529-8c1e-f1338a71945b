import { Component, EventEmitter, Input, OnChanges, Output, SimpleChanges } from '@angular/core';
import { IfpDbChartPropsComponent } from '../ifp-db-chart-props/ifp-db-chart-props.component';
import { IfpDbDropdownComponent } from '../ifp-db-dropdown/ifp-db-dropdown.component';
import { DashboardService } from 'src/app/scad-insights/core/services/create-dashboard/dashboard.service';
import { IfpToggleButtonComponent } from '../../../scad-insights/ifp-widgets/ifp-atoms/ifp-toggle-button/ifp-toggle-button.component';

@Component({
    selector: 'ifp-chart-legend-props',
    templateUrl: './ifp-chart-legend-props.component.html',
    styleUrl: './ifp-chart-legend-props.component.scss',
    imports: [IfpDbChartPropsComponent, IfpDbDropdownComponent, IfpToggleButtonComponent]
})
export class IfpChartLegendPropsComponent implements OnChanges {

  @Input() selectedCard!: string;
  @Input() cntType!: string;

  @Output() updateLegendPosition: EventEmitter<{ value: string, name: string }> = new EventEmitter<{ value: string, name: string }>();
  @Output() updateLegendEnable: EventEmitter<{ value: boolean }> = new EventEmitter<{ value: boolean }>();

  public alignOptions = [
    {
      name: 'Left',
      value: 'left'
    },
    {
      name: 'Right',
      value: 'right'
    },
    {
      name: 'Center',
      value: 'center'
    }
  ];

  public selectedAlign!: { name: string, value: string };
  public legendEnable: boolean = true;

  constructor(private _dashboardService: DashboardService) { }


  ngOnChanges(changes: SimpleChanges): void {
    if (changes['selectedCard']) {
      const index = this._dashboardService.chartSettings[this.cntType]?.findIndex((x: { id: string; }) => x.id == this.selectedCard);
      if (index >= 0) {
        const indexoObject = this._dashboardService.chartSettings[this.cntType][index];
        this.legendEnable = [false, true].includes(indexoObject.isLegend) ? indexoObject.isLegend : true;
        this.selectedAlign = this.alignOptions[2];
        if (indexoObject.legendPos) {
          const selectIndex = this.alignOptions.findIndex(x => x.value == indexoObject.legendPos);
          this.selectedAlign = this.alignOptions[selectIndex];
        }
      } else {
        this.selectedAlign = this.alignOptions[2];
        this.legendEnable = true;
      }
    }
  }


  getLegendPosition(event: any) {
    this.selectedAlign = this.alignOptions[event.index];
    this.updateLegendPosition.emit(event);
  }

  changeLegendValue(event: boolean) {
    this.legendEnable= event;
    this.updateLegendEnable.emit({ value: event });
  }
}
