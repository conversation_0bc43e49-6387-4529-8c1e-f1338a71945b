import { Injectable } from '@angular/core';
import { HttpService } from '../http/http.service';
import { domainNavigationApi } from '../../apiConstants/domains.api.constants';

@Injectable({
  providedIn: 'root'
})
export class ScreenerService {
  constructor(private http:HttpService ) { }

  screener(data: any, limit: number, page: number, innovative: boolean = false) {
    return this.http.post(`${innovative ? domainNavigationApi.screenerInnovative:  domainNavigationApi.screenerOfficial}?page=${page}&limit=${limit}`, data);
  }

}
