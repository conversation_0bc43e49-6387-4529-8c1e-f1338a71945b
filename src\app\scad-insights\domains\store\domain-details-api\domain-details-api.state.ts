import { EntityState, createEntityAdapter } from '@ngrx/entity';
import { DomainDetailsNew, DomainNodes } from 'src/app/scad-insights/core/interface/domain.interface';

export const domainDetailsApiStateName = 'domainDetailsApiComponent';
export const domainDetailsApiStateNameNode = 'domainDetailsApiNodeComponent';
export type DomainDetailNewState = EntityState<{data: DomainDetailsNew; status?:  boolean; errorMessage?: string;loader?:  boolean; }>
export type DomainDetailNewStateNodes = DomainNodes;
export const domainDetailNewAdapter = createEntityAdapter<{data: DomainDetailsNew;status?:  boolean; errorMessage?: string;loader?:  boolean; }>({
  selectId: (domain) => {
    return domain.data.id ?? null;
  }
});


export const domainDetailV2State:  DomainDetailNewState  = domainDetailNewAdapter.getInitialState({
  data: {
    id: null,
    classification: [],
    filter: {}
  },
  status: false,

  errorMessage: '',
  loader: true
});


export const domainDetailV2StateNodes:  DomainDetailNewStateNodes  =  {
  results: [],
  total_count: 0,
  products: []
};
