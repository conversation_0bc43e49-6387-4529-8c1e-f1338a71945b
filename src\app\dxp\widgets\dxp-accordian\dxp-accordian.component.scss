@use "../../../../assets/ifp-styles/abstracts/index" as *;
:host{
  display: block;
}
.ifp-dxp-accordian {
  border: 1px solid $ifp-color-grey-13;
  border-radius: 5px;
  &__head {
    display: flex;
    padding:  $spacer-3;
    align-items: center;
  }
  &__image {
    border-radius: 4px;
    width: 68px;
    height: 46px;
    object-fit: cover;
  }
  &__title-wrapper {
    margin-inline-start: $spacer-2;
  }
  &__entity {
    color: $ifp-color-grey-14;
  }
  &__title {
   font-size: $ifp-fs-5;
   font-weight:$fw-semi-bold ;
   margin-bottom: $spacer-1;
  }
  &__assets {
    padding: $spacer-2 $spacer-3;
    margin-inline-start: $spacer-2;
  }
  &__assets-title {
    font-size: $ifp-fs-4;
    color: $ifp-color-grey-14;
    margin-bottom: $spacer-3;
  }
  &__assets-items {
    margin-bottom: $spacer-3;
    &:last-child {
      margin-bottom: $spacer-2;
    }
  }
  &__minus {
    width: 25px;
    height: 2px;
    background-color: $ifp-color-grey-14;
  }
  &__toggle {
    cursor: pointer;
    border: 1px solid $ifp-color-grey-13;
    border-radius: 50%;
    padding: $spacer-2;
    width: 28px;
    height: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: auto;
  }
}
