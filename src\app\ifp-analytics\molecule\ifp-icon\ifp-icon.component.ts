import { Component, ElementRef, EventEmitter, Input, Output, Renderer2, signal } from '@angular/core';
import { FormControl } from '@angular/forms';

@Component({
  selector: 'app-ifp-icon',
  templateUrl: './ifp-icon.component.html',
  styleUrls: ['./ifp-icon.component.scss'],
  standalone: true
})
export class IfpIconComponent  {
  constructor(public element: ElementRef, public _render: Renderer2 ) {}
  @Input() icon = '';
  @Input() formControls = new FormControl(false);
  @Input() className = '';
  @Input() count = signal(0);
  @Input() totalLenght!:number;
  @Output() iconClick = new EventEmitter();
  onClick() {

    this.formControls.setValue(this.formControls.value ? false: true);
    if (this.formControls.value) {
      this._render.addClass(this.element.nativeElement.parentElement.parentElement.parentElement, this.className);
    } else {
      this._render.removeClass(this.element.nativeElement.parentElement.parentElement.parentElement, this.className);
    }
    this.iconClick.emit(this.formControls.value);
  }
}
