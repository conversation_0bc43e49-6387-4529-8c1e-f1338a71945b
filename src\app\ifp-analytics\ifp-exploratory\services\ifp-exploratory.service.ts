import { Injectable, signal, WritableSignal } from '@angular/core';
import { FileResponePrep } from '../../data-prep/ifp-data-prep/interface/ifp-data-prep.interface';
import { BehaviorSubject } from 'rxjs';

@Injectable()
export class IfpExploratoryService {
  public processDisable = signal(false);
  public showProcessBtn= signal(true);
  public loaderProcess= signal(false);
  public processName = signal('Process');
  public processIcon= signal('');
  public secondaryBtn = signal(false);
  public secondaryBtnDisable = signal(false);
  public secondaryName = signal('Process');
  public secondaryIcon= signal('');
  public processEvent =  new BehaviorSubject('');
  public processEventsecondary =  new BehaviorSubject('');
  public summeryLoaded = signal(false);
  uploadedFile:WritableSignal<FileResponePrep>= signal({});
  autoMlStatus = signal(false);
  public previewTableData: {heading: string[]; data: any} = {heading: [], data: []};

}
