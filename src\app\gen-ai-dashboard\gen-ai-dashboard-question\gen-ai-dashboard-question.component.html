@if (qusEnable()&& this.qusEnableLocal()) {
 <ifp-gen-ai-dashboard-qus-create [enableDelete]="enableDelete()" (changeDetect)="changeDetect.emit($event)" [validation]="validation()" [dropdownObject]="dropdownObject()" (deleteEventEmit)="delete.emit($event)" [dropdown]="dropdown()" [addQuestionEnable]="addQuestionEnable()" [question]="question()" (add)="addQuestion($event)" (save)="save($event)"></ifp-gen-ai-dashboard-qus-create>
}@else if (previewEdit()) {
  <ifp-gen-ai-dashboard-run-preview [question]="question()" (changeDetect)="changeDetectForQuestionChange.emit({event:$event , id: id()})" [dropdownObject]="dropdownObject()" [dropdown]="dropdown()" ></ifp-gen-ai-dashboard-run-preview>
}

@else {
  <div class="ifp-gen-ai-dashboard-question">

  <div  class="ifp-gen-ai-dashboard-question__qus-wrapper">
    <div class="ifp-gen-ai-dashboard-question__qus-outer">
      <p class="ifp-gen-ai-dashboard-question__qus">
        {{question().question| translate}}
    </p>
        @if (feedback() || preview()) {

      <div class="ifp-gen-ai-dashboard-question__feed-resp" [ngClass]="{'ifp-gen-ai-dashboard-question__feed-resp--active': expand()}" (click)="expand.set(!expand())">
@if (isSatisfied() !== null) {
  <div class="ifp-gen-ai-dashboard-question__response-badge">
    @if( isSatisfied()) {
      <em class="ifp-icon ifp-icon-like "></em>
      {{'Satisfied' | translate}}
    } @else {
      <em class="ifp-icon ifp-icon-dislike "></em>
      {{'Not Satisfied' | translate}}
    }

  </div>
}



        <div class="ifp-gen-ai-dashboard-question__feed-resp-inner">
          @if ( expand()) {
            <em class="ifp-icon ifp-icon-up-arrow ifp-gen-ai-dashboard-question__feed-resp-icon"></em>
          }@else {
            <em class="ifp-icon ifp-icon-down-arrow ifp-gen-ai-dashboard-question__feed-resp-icon"></em>
          }
        </div>

      </div>
    }
    @if (qusEnable()) {
      <div class="ifp-gen-ai-dashboard-question__edit-icons">
        <em class="ifp-icon ifp-icon-edit ifp-gen-ai-dashboard-question__edit-icon" [appIfpTooltip]="'Edit' | translate" (click)="edit()"></em>
        <em class="ifp-icon ifp-icon-trash ifp-gen-ai-dashboard-question__edit-icon"  [appIfpTooltip]="'Delete' | translate" (click)="delete.emit(question().object_id)"></em>
      </div>
    }


    </div>
    @if (!feedback() && !preview()) {
    <div class="ifp-gen-ai-dashboard-question__expand">
      <div class="ifp-gen-ai-dashboard-question__exp-resp" [ngClass]="{'ifp-gen-ai-dashboard-question__exp-resp--active': expand()}" (click)="expand.set(!expand())">
        <em class="ifp-icon ifp-icon-flag ifp-gen-ai-dashboard-question__exp-resp-icon"></em>
        {{'Expected Response' | translate}}
        <em class="ifp-icon ifp-icon-down-arrow ifp-gen-ai-dashboard-question__exp-arrow-icon"></em>
      </div>
    </div>
  }
<!--
  <div  class="ifp-gen-ai-dashboard-question__badge-outer">
    <div class="ifp-gen-ai-dashboard-question__badge-wrapper">
      <div class="ifp-gen-ai-dashboard-question__badge-text">
        {{'Type'| translate}} :
          <app-ifp-badge [size]="'large'" [borderRadius]="'20px'" class="ifp-gen-ai-dashboard-question__badge" [enableImage]="false" [badgeData]="{
            name: getDropDownBadgeDataType() , bgColor: 'transparent'
          }"></app-ifp-badge>
      </div>
      <span  class="ifp-gen-ai-dashboard-question__badge-separator">
    |
      </span>

      <div class="ifp-gen-ai-dashboard-question__badge-text">
        {{'Complexity'| translate}} :
          <app-ifp-badge [size]="'large'" [borderRadius]="'20px'" class="ifp-gen-ai-dashboard-question__badge" [enableImage]="false" [badgeData]="{
            name: getDropDownBadgeDataComplexity(), bgColor: 'transparent'
          }"></app-ifp-badge>
      </div>
      <span  class="ifp-gen-ai-dashboard-question__badge-separator">
        |
          </span>

          <div class="ifp-gen-ai-dashboard-question__badge-text">
        {{'Source'| translate}} :
          <span class="ifp-gen-ai-dashboard-question__source">
            {{getDropDownBadgeDataSource()}}
          </span>
      </div>
      <span  class="ifp-gen-ai-dashboard-question__badge-separator">
        |
          </span>

      <div class="ifp-gen-ai-dashboard-question__badge-text">
        {{'Data classification'| translate}} :
          <app-ifp-badge [size]="'large'" [borderRadius]="'20px'" class="ifp-gen-ai-dashboard-question__classification" [enableImage]="false" [badgeData]="{
            name:getDropDownBadgeDataClassification()
          }"></app-ifp-badge>
      </div>

    </div>


    <div>

    </div>
  </div> -->


  </div>
  @if (expand()) {
    <div class="ifp-gen-ai-dashboard-question__response-wrapper" [ngClass]="{'ifp-gen-ai-dashboard-question--feedback': feedback() ||   preview()}">
      <div class="ifp-gen-ai-dashboard-question__response">
        <div class="ifp-gen-ai-dashboard-question__exp-resp-wrapper">
             <p class="ifp-gen-ai-dashboard-question__resp-type">
               <em class="ifp-icon ifp-icon-flag ifp-gen-ai-dashboard-question__exp-resp-icon ifp-gen-ai-dashboard-question__icon-blue"></em>
          {{'Expected Response' | translate}}
             </p>

        </div>
        <div class="ifp-gen-ai-dashboard-question__response-text-wrapper">
          <p class="ifp-gen-ai-dashboard-question__response-text">
            {{question().expected_answer}}
          </p>
        </div>
      </div>
      @if (feedback() ||   preview()) {
        <div class="ifp-gen-ai-dashboard-question__response">
          <div class="ifp-gen-ai-dashboard-question__exp-resp-wrapper">
            <p class="ifp-gen-ai-dashboard-question__resp-type"><em class="ifp-icon ifp-icon-beautify ifp-gen-ai-dashboard-question__exp-resp-icon  ifp-gen-ai-dashboard-question__icon-blue"></em>
            {{'Generated Response' | translate}}</p>
            <p class="ifp-gen-ai-dashboard-question__resp-type ifp-gen-ai-dashboard-question__resp-type--time"><em class="ifp-icon ifp-icon-clock ifp-gen-ai-dashboard-question__exp-resp-icon  ifp-gen-ai-dashboard-question__icon-blue"></em>{{'Loaded in'}}: <strong>{{question().ai_answer?.response_time}}s</strong></p>
          </div>
          <div class="ifp-gen-ai-dashboard-question__response-text-wrapper ifp-gen-ai-dashboard-question__response-text-wrapper--ai">

            <p class="ifp-gen-ai-dashboard-question__response-text">
              @if (question().loader) {
                <app-ifp-card-loader  class="ifp-gen-ai-dashboard-preview__loader-card" [type]="loaderType.smallSingle"></app-ifp-card-loader>
                    <app-ifp-card-loader  class="ifp-gen-ai-dashboard-preview__loader-card" [type]="loaderType.smallSingle"></app-ifp-card-loader>
                        <app-ifp-card-loader  class="ifp-gen-ai-dashboard-preview__loader-card" [type]="loaderType.smallSingle"></app-ifp-card-loader>
              }@else {
                      <ifp-mark-down [data]="question().ai_answer?.answer"></ifp-mark-down>
              }

            </p>
          </div>

        </div>
      }

    </div>
    @if (feedback() ||  preview()) {
    <div  class="ifp-gen-ai-dashboard-question__footer" [ngClass]="{'ifp-gen-ai-dashboard-question__footer--preview': preview()}">
      @if (!preview()) {
      <div class="ifp-gen-ai-dashboard-question__footer-sec-1">
          <div class="ifp-gen-ai-dashboard-question__footer-left">
          <p class="ifp-gen-ai-dashboard-question__footer-text">{{'Evaluate the response (Overall)' | translate}}</p>

          <ifp-button  class="ifp-gen-ai-dashboard-question__footer-item ifp-gen-ai-dashboard-question__footer-btn" [buttonClass]="buttonClass.transpreantBlue+' ' +buttonClass.round + ' ' +buttonIconPosition.left+ ' ' + buttonClass.normalAplabetic+' ' + (isSatisfied() !== null && isSatisfied() ? 'ifp-btn--active':'')" [iconClass]="'ifp-icon-like'" [label]="'Satisfied'" (ifpClick)="openModal(true)"></ifp-button>
          <ifp-button  class="ifp-gen-ai-dashboard-question__footer-item ifp-gen-ai-dashboard-question__footer-btn" [buttonClass]="buttonClass.transpreantBlue +' ' +buttonClass.round + ' ' +buttonIconPosition.left+ ' ' + buttonClass.normalAplabetic+ ' ' + (isSatisfied() !== null && !isSatisfied() ? 'ifp-btn--active':'')"[iconClass]="'ifp-icon-dislike'" [label]="'Not Satisfied'" (ifpClick)="openModal(false)"></ifp-button>

        </div>
        <div class="ifp-gen-ai-dashboard-question__footer-right">
          <p class="ifp-gen-ai-dashboard-question__footer-text">{{'Give your ratings' | translate}}</p>
          <ifp-star-rating [id]="question().object_id" (rating)="rangeChange($event)" class="ifp-gen-ai-dashboard-question__rating"></ifp-star-rating>
          <!-- <p class="ifp-gen-ai-dashboard-question__rating-value">{{statusValue()}}</p> -->


          <!-- <div class="ifp-gen-ai-dashboard-question__footer-slider-wrapper">
          <em class="ifp-icon ifp-icon-star-group ifp-gen-ai-dashboard-question__footer-rating-icon">

          </em>
          <span  class="ifp-gen-ai-dashboard-question__footer-rating-text">
            {{'Ratings' | translate}}
          </span>

          <div class="ifp-gen-ai-dashboard-question__footer-slider-outer">
            <ifp-range-slider [unit]="'%'" [disable]="preview()" [rangeValue]="statusValue()"  (selectRange)="rangeChange($event);"  [sliderTheme]="sliderTheme.green" [decimals]="0" class="ifp-gen-ai-dashboard-question__footer-slider" ></ifp-range-slider>
          </div>
          <div  class="ifp-gen-ai-dashboard-question__footer-status">
            <span>
              {{statusName() | translate}}
            </span>
            <span class="ifp-gen-ai-dashboard-question__footer-value">
              {{statusValue()}}%
            </span>
          </div>
          </div> -->
        </div>
      </div>
      <div class="ifp-gen-ai-dashboard-question__footer-sec-2">
        <p class="ifp-gen-ai-dashboard-question__footer-title">{{'Select your responses given below' | translate}}</p>
        <div class="ifp-gen-ai-dashboard-question__btn-wrapper">
          <div class="ifp-gen-ai-dashboard-question__btn-sec">
            <p class="ifp-gen-ai-dashboard-question__btn-text">
              {{'Accuracy' | translate}}:
            </p>
            @for (accuracy of dropdown()[questionDropDown.evalAnsAccuracy]; let i = $index; track i) {
              <ifp-button  class="ifp-gen-ai-dashboard-question__footer-btn" [buttonClass]="buttonClass.tertiaryBlue+' '+(selectedAccuracyIndex === i ? buttonClass.active : '')+' ' +buttonClass.round + ' ' +buttonIconPosition.left+ ' ' + buttonClass.normalAplabetic" [iconClass]="(selectedAccuracyIndex === i ? 'ifp-icon-tick-round-fill' : 'ifp-icon-tick-round-outline')" [label]="accuracy.display_name" (ifpClick)="onSelectEvalAccuracy(i)"></ifp-button>
            }
          </div>

          <div class="ifp-gen-ai-dashboard-question__btn-sec">
            <p class="ifp-gen-ai-dashboard-question__btn-text">
              {{'Releavancy' | translate}}:
            </p>
            @for (relevance of dropdown()[questionDropDown.evalAnsRelevance]; let i = $index; track i) {
              <ifp-button  class="ifp-gen-ai-dashboard-question__footer-btn" [buttonClass]="buttonClass.tertiaryBlue+' '+(selectedRelevanceIndex === i ? buttonClass.active : '')+' ' +buttonClass.round + ' ' +buttonIconPosition.left+ ' ' + buttonClass.normalAplabetic" [iconClass]="(selectedRelevanceIndex === i ? 'ifp-icon-tick-round-fill' : 'ifp-icon-tick-round-outline')" [label]="relevance.display_name" (ifpClick)="onSelectEvalRelevance(i)"></ifp-button>
            }
          </div>

          <div class="ifp-gen-ai-dashboard-question__btn-sec">
            <p class="ifp-gen-ai-dashboard-question__btn-text">
              {{'Style' | translate}}:
            </p>
            @for (style of dropdown()[questionDropDown.evalAnsStyle]; let i = $index; track i) {
              <ifp-button  class="ifp-gen-ai-dashboard-question__footer-btn" [buttonClass]="buttonClass.tertiaryBlue+' '+(selectedStyleIndex === i ? buttonClass.active : '')+' ' +buttonClass.round + ' ' +buttonIconPosition.left+ ' ' + buttonClass.normalAplabetic" [iconClass]="(selectedStyleIndex === i ? 'ifp-icon-tick-round-fill' : 'ifp-icon-tick-round-outline')" [label]="style.display_name" (ifpClick)="onSelectEvalStyle(i)"></ifp-button>
            }
          </div>
        </div>
      </div>
    } @else {
      <div  class="ifp-gen-ai-dashboard-question__prevew-footer">
        <span  class="ifp-gen-ai-dashboard-question__preview-rating-text">
          {{'Rating' | translate}}
        </span>
  <ifp-star-rating [width]="150" [preview]="true" [starCount]="statusValue()" class="ifp-gen-ai-dashboard-question__rating"></ifp-star-rating>
  <!-- <span  class="ifp-gen-ai-dashboard-question__rating-value">
  {{statusValue()}}
  </span> -->

      </div>
      @if (feedbackValue() && feedbackValue() !== '') {
      <p  class="ifp-gen-ai-dashboard-question__feedback">
        <em class="ifp-icon ifp-icon-blog ifp-gen-ai-dashboard-question__preview-feedback">

        </em>
        {{'Feedback' | translate}}:
        <span  class="ifp-gen-ai-dashboard-question__feedback-value"> {{feedbackValue()}}</span> </p>
      }
           <div class="ifp-gen-ai-dashboard-question__footer-sec-2 ifp-gen-ai-dashboard-question__footer-sec-2--100">
        <div class="ifp-gen-ai-dashboard-question__btn-wrapper">
          <div class="ifp-gen-ai-dashboard-question__btn-sec">
            <p class="ifp-gen-ai-dashboard-question__btn-text">
              {{'Accuracy' | translate}}:
            </p>
                  <ifp-button  class="ifp-gen-ai-dashboard-question__footer-btn" [buttonClass]="buttonClass.tertiaryBlue+' '+buttonClass.active +' ' +buttonClass.round + ' ' + ' ' + buttonClass.normalAplabetic" [label]="question().rating?.metrics?.evaluation_answer_accuracy?.display_name" ></ifp-button>
          </div>

          <div class="ifp-gen-ai-dashboard-question__btn-sec">
            <p class="ifp-gen-ai-dashboard-question__btn-text">
              {{'Releavancy' | translate}}:
            </p>
                    <ifp-button  class="ifp-gen-ai-dashboard-question__footer-btn" [buttonClass]="buttonClass.tertiaryBlue+' '+buttonClass.active +' ' +buttonClass.round + ' ' + ' ' + buttonClass.normalAplabetic" [label]="question().rating?.metrics?.evaluation_answer_relevance?.display_name" ></ifp-button>
          </div>

          <div class="ifp-gen-ai-dashboard-question__btn-sec">
            <p class="ifp-gen-ai-dashboard-question__btn-text">
              {{'Style' | translate}}:
            </p>
           <ifp-button  class="ifp-gen-ai-dashboard-question__footer-btn" [buttonClass]="buttonClass.tertiaryBlue+' '+buttonClass.active +' ' +buttonClass.round + ' ' + ' ' + buttonClass.normalAplabetic" [label]="question().rating?.metrics?.evaluation_answer_response_style?.display_name" ></ifp-button>
          </div>
        </div>
      </div>
    }

    </div>
  }
  }


   @if (addQuestionEnable() && qusEnable()) {
    <div class="ifp-gen-ai-dashboard-question__plus" (click)="addQuestionEvent.emit()">
      <em class="ifp-icon-plus-light ifp-icon ifp-gen-ai-dashboard-question__plus-icon"></em>
     </div>
   }
</div>
}



<app-ifp-modal #modal [overlayType]="'transparent'" >
<ifp-gen-ai-dashboard-feedback (close)="closeModal()" (submit)="submitSurvey($event)"></ifp-gen-ai-dashboard-feedback>
</app-ifp-modal>
