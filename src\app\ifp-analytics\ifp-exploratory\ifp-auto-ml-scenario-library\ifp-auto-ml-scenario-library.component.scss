
@use "../../../../assets/ifp-styles/abstracts/index" as *;
.ifp-scenario {
  &__heading-outer {
    display: flex;
    align-items: center;
    margin-top: $spacer-6;
  }
  &__heading {
      font-size: $spacer-5;
      font-weight: $fw-bold;
      width: 100%;
      text-align: center;
  }
&__sub-heading {
  margin-top: $spacer-5;
  display: flex;
  font-size: $ifp-fs-9;
  font-weight: $fw-bold;
  align-items: center;
  justify-content: space-between;
  margin-bottom: $spacer-3;
}
&__card-wrapper {
  display: flex;
  margin: $spacer-0 (-$spacer-3 - $spacer-1);
  flex-wrap: wrap;
}
&__card {
  width: calc(25% - (2 * $spacer-3 ));
  margin: $spacer-3 $spacer-3 $spacer-3;
  @include tablet  {
    width: calc(33.33% - (2 * $spacer-3 ));
  }
}
&__no-data {
  width: 100%;
  margin: $spacer-0 $spacer-4;
}
&__pagination {
  margin-bottom: $spacer-2;
}
.ifp-active-card {
  width: calc(50% - (2 * $spacer-3 ));
  @include tablet  {
    width: calc(66.66% - (2 * $spacer-3 ));
  }
}
}
