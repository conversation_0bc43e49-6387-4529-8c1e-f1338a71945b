import { Component, EventEmitter, Input, Output, signal, WritableSignal } from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';
import { IfpButtonComponent } from '../../../scad-insights/ifp-widgets/ifp-atoms/ifp-button/ifp-button.component';
import { buttonClass } from 'src/app/scad-insights/core/constants/button.constants';
import { FormsModule } from '@angular/forms';
import { IfpCheckBoxComponent } from '../../../scad-insights/ifp-widgets/ifp-atoms/ifp-check-box/ifp-check-box.component';

@Component({
    selector: 'ifp-prep-save-modal',
    templateUrl: './ifp-prep-save-modal.component.html',
    styleUrl: './ifp-prep-save-modal.component.scss',
    imports: [TranslateModule, IfpButtonComponent, FormsModule, IfpCheckBoxComponent]
})

export class IfpPrepSaveModalComponent  {

  @Output() closeModal = new EventEmitter();
  @Output() saveFile: EventEmitter<{ name: string, type: string }> = new EventEmitter<{ name: string, type: string }>();

  @Input() message: string = 'Save the output file to your Bayaan library.';

  @Input() fileName: string = '';
  @Input() workFlowId: WritableSignal<string> = signal('');
  @Input() title:string='Save';
  @Input() buttonTitle:string='Save';
  @Input() buttonSecondary:string='Cancel';
  public buttonClass = buttonClass;
  public saveType: string = 'new';

  onClose(event: string) {
    this.fileName='';
    this.closeModal.emit(event);
  }

  onSave() {
    if (this.fileName) {
      this.saveFile.emit({ name: this.fileName.trim(), type: this.saveType });
    }
  }

  selectType(type: string) {
    this.saveType = type;
  }
}
