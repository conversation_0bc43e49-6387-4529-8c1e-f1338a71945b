<div class="ifp-correlogram">
  <div class="ifp-correlogram__row">
    <div  class="ifp-correlogram__text">
  </div >
  <div class="ifp-correlogram__text-first">
    {{correlogram[0]}}
  </div>
  </div>
 @for (item of correlogram; track item; let index= $index) {
  <div class="ifp-correlogram__row">
    <div  class="ifp-correlogram__text-wrapper">
    <p   class="ifp-correlogram__text">{{item}}</p>
  </div >
  @for (itemValue of getValue(index+1); track itemValue; let index2= $index) {
    <div   class="ifp-correlogram__rec">

      <div class="ifp-chart-tooltip__wrapper"> <span class="ifp-chart-tooltip__header">{{item}} - {{correlogram[index2]}}</span><span class="ifp-chart-tooltip__value">  {{correlogramData?.[item]?.[correlogram[index2]]?.value}}</span></div>
      <div  class="ifp-correlogram__circle" [ngStyle]="{'background-color':correlogramData?.[item]?.[correlogram[index2]]?.color ?? '' }" ></div>
    </div>
  }
  <div class="ifp-correlogram__text-last">
    <p class="ifp-correlogram__text-last-value">
      {{correlogram[index+1]}}
    </p>

  </div>
  </div>
 }
 <div class="ifp-correlogram__bar-wrap">
  <div  class="ifp-correlogram__text">
  </div >
  <div [style.width]="(correlogram.length * 50)+'px'">
    <div  class="ifp-correlogram__bar">

    </div>
    <div  class="ifp-correlogram__dash-wrapper">
      <div class="ifp-correlogram__dash">-1</div>
      <div class="ifp-correlogram__dash">0</div>
      <div class="ifp-correlogram__dash">1</div>
    </div>
    </div>

</div>

</div>
