@if (loader()) {
  <app-ifp-card-loader class="ifp-loader" [type]="'table'"></app-ifp-card-loader>
} @else {
  <table class="ifp-data-table" aria-label="data-table" [ngClass]="{'ifp-data-table--dashboard': isDashboard(), 'ifp-data-table--capital-title': isTitleUpperCase()}">
    <tr class="ifp-data-table__row ifp-data-table__row--head">
      @for (head of tableHead; track $index) {
      <th class="ifp-data-table__col ">
        <div class="ifp-data-table__heading">
          {{headingTranslate ? (head | translate) : head}}
          @if (headerSettings?.[head]?.sort) {
          <div>
            <em [appIfpTooltip]="'Ascending' | translate" [extraSpaceTop]="30" [zIndex]="1203"
              [ngClass]="{'ifp-data-table__sort-icon--active': acceding == false && head == this.sortValueString}"
              class="ifp-icon ifp-icon-up-arrow ifp-data-table__sort-icon" (click)="sort(head, false)"></em>
            <em [appIfpTooltip]="'Descending' | translate" [extraSpaceTop]="30" [zIndex]="1203"
              [ngClass]="{'ifp-data-table__sort-icon--active': acceding == true && head == this.sortValueString}"
              class="ifp-icon ifp-icon-down-arrow ifp-data-table__sort-icon" (click)="sort(head, true)"></em>

          </div>
          }
        </div>

      </th>

      }
    </tr>
    @for (row of tableData; track row;let index = $index) {
    <tr class="ifp-data-table__row" (click)="onRowClick({row: row},index)"
      [ngClass]="{'ifp-data-table__row--bg-blue': index===selectedIndex && rowClickEnable }">
      @for (col of row; track $index) {
      @if(col.type !=='hidden') {
      <td class="ifp-data-table__col" [ngClass]="{'ifp-data-table__col--click' : col?.click , 'ifp-data-table__col--dynamic': col?.multiComponents?.length}"
        (click)="colClick(col ,row)">
        @if(col.type !=='custom'){
        @if(col.customKey){
        <div class="ifp-data-table__value"
          [title]=" translation ? (col.value[col.customKey] | translate) :col.value[col.customKey] ">
          {{translation ? (col.value[col.customKey] | translate): col.value[col.customKey]}}
        </div>
        } @else {
        <div class="ifp-data-table__value" [title]=" translation ? (col.value | translate) :col.value ">
          {{translation ? (col.value | translate): col.value}}
        </div>
        }
        @if (col?.click) {
        <em class="ifp-icon ifp-icon-right-arrow ifp-data-table__arrow"></em>
        }
        } @else {
        @if(col.multiComponents){
        @for (component of col.multiComponents; track $index) {
        <app-ifp-dynamic class="ifp-data-table__dynamic-cmp" [inputValues]="component.inputValues"
          (customEvent)="onCustomEvent($event, index)" [headValue]="component" [columnSetting]="component"
          [outputs]="component.outputs"></app-ifp-dynamic>
        }
        } @else {
        <app-ifp-dynamic [inputValues]="col.inputValues" (customEvent)="onCustomEvent($event, index)" [headValue]="col"
          [columnSetting]="col" [outputs]="col.outputs"></app-ifp-dynamic>
        }
        }
      </td>
      }
      }
    </tr>
    }@empty {
    <tr>
      <td [attr.colspan]="tableHead?.length">
        <app-ifp-no-data class="ifp-data-table__no-data" [isTransparent]="true" [height]="'150px'"></app-ifp-no-data>
      </td>
    </tr>

    }
  </table>
}
