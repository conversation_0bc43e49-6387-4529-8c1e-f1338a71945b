
const stylelint = require('stylelint');

const { report, ruleMessages, validateOptions } = stylelint.utils;
const ruleName = 'color-plugin/variable';
const messages = ruleMessages(ruleName, {
   expected: (unfixed) => `Expected ${unfixed}`,
});

stylelint.customSyntax= ""
module.exports = stylelint.createPlugin(ruleName, function getPlugin(primaryOption, secondaryOptionObject, context) {

   return function lint(postcssRoot, postcssResult) {
       const validOptions = validateOptions(
           postcssResult,
           ruleName,
           {
               //No options for now...
           }
       );

       if (!validOptions) { //If the options are invalid, don't lint
           return;
       }
       postcssRoot.walkDecls(decl => { //Iterate CSS declarations
        const pattern = /^[$]ifp-/;
        decl.value.match(pattern);
       if (decl.prop.includes('color')) {
         if (!decl.value.match(pattern) && decl.value !== 'none' && decl.value !== 'transparent') {
            report({
              ruleName,
              result: postcssResult,
              message: messages.expected('color ifp variable'), // Build the reported message
              node: decl
          });
         }
       }
       });
   };
});

module.exports.ruleName = ruleName;
module.exports.messages = messages;
module.exports.customSyntax = require("postcss-scss");
