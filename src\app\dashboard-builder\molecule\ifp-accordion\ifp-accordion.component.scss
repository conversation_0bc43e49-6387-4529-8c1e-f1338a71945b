@use "../../../../assets/ifp-styles/abstracts/index" as *;
:host::ng-deep {
  .ifp-accordion__body-content {
    padding: $spacer-0 $spacer-4 $spacer-4;
  }
}
.ifp-accordion {
  &__head {
    display: flex;
    justify-content: space-between;
    border-top: 1px solid $ifp-color-grey-7;
    padding: $spacer-4;
    cursor: pointer;
  }

  &__body {
    max-height: 0;
    // transform-origin: top;
    overflow: hidden;
    transition: 0.3s;
  }

  &__title {
    font-size: $ifp-fs-5;
    font-weight: $fw-semi-bold;
    margin-right: $spacer-2;
  }

  &__button {
    width: 20px;
    min-width: 20px;
    height: 20px;
    border: 1px solid $ifp-color-tertiary-text;
    border-radius: 50%;
    position: relative;
    &::before,
    &::after {
      content: "";
      border-radius: 2px;
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      background-color: $ifp-color-black;
    }
    &::before {
      width: 10px;
      height: 2px;
    }
    &::after {
      width: 2px;
      height: 10px;
      opacity: 1;
      transition: opacity 0.3s;
    }
  }
  &--expanded {
    .ifp-accordion {
      &__body {
        // transform: scaleY(1);
        max-height: 1000px;
        overflow: visible;
      }
      &__button {
        background-color: $ifp-color-black;
        &::before,
        &::after {
          background-color: $ifp-color-white;
        }
        &::after {
          opacity: 0;
        }
      }
    }
  }
}
