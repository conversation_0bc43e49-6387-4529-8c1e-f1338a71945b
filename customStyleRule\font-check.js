
const stylelint = require('stylelint');

const { report, ruleMessages, validateOptions } = stylelint.utils;
const ruleName = 'font-plugin/font-check';
const messages = ruleMessages(ruleName, {
   expected: (unfixed) => `Expected ${unfixed}`,
});

stylelint.customSyntax= ""
module.exports = stylelint.createPlugin(ruleName, function getPlugin(primaryOption, secondaryOptionObject, context) {

   return function lint(postcssRoot, postcssResult) {
       const validOptions = validateOptions(
           postcssResult,
           ruleName,
           {
               //No options for now...
           }
       );

       if (!validOptions) { //If the options are invalid, don't lint
           return;
       }
       postcssRoot.walkDecls(decl => { //Iterate CSS declarations
        const pattern = /rem$/;
        const patternIfp = /^[$]ifp-/;
       if (decl.prop === 'font-size' || decl.prop === 'Font-Size') {
        if (!decl.value.match(patternIfp)) {
         if (!decl.value.match(pattern)) {
            report({
              ruleName,
              result: postcssResult,
              message: messages.expected('rem in font-size'), // Build the reported message
              node: decl
          });
         }
        }
       }
       if (decl.prop === 'Font-Size') {
        report({
          ruleName,
          result: postcssResult,
          message: messages.expected('lower case'), // Build the reported message
          node: decl
      });
       }
       });
   };
});

module.exports.ruleName = ruleName;
module.exports.messages = messages;
module.exports.customSyntax = require("postcss-scss");
