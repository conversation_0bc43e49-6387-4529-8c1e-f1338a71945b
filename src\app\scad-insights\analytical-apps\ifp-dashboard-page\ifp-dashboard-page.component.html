<div class="ifp-container">
  <app-ifp-breadcrumbs [pageData]="pageData"></app-ifp-breadcrumbs>
</div>

<div class="ifp-dashboard-page ifp-module-padding">
  <div class="ifp-container">
    <!-- <ifp-button class="ifp-search-filter__button" (ifpClick)="goBack()" [label]="'Back' | translate" [buttonClass]="buttonClass.primary"></ifp-button> -->
      <app-ifp-dashboard-test *ngIf="dashboardStatus" [hostUrl]="hostUrl" [hideTabs]="tabs"[toolbar]="toolbar" [dashboardSrc]="scriptSrc" [dashboardHeight]="height" class="ifp-dashboard-page__tableau"
      [note]="note"></app-ifp-dashboard-test>
  </div>
</div>
