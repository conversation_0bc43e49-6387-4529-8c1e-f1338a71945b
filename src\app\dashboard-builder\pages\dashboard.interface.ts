export interface DashboadListItem {
  createdDate: string;
  sharedDate?: string;
  recepientEmails?: string[];
  shareEmail?: string;
  id: string;
  logo: {
    content: string;
    type: string;
  },
  logoType: string;
  name: string;
  thumbnailLight: {
    content: string;
    type: string;
  },
  thumbnailDark: {
    content: string;
    type: string;
  },
}

export interface DashboardDetail {
  id: string;
  logo: {
    content: string;
    type: string;
  },
  name: string;
  nodes: [];
}

export interface Columns {
  name: string;
  value: string;
  type: string;
}

export interface DBOptions {
  name: string,
  key: string,
  type: string,
  aggregations: string[],
}

export interface valueCard {
  text_one: string,
  text_two: string | undefined,
  text_three: string,
  text_four: string | undefined,
  key: string,
  value_arrow: boolean,
  title: string
}

export interface columnAggregations {
  name: string,
  type: string,
  x_aggregations: string[],
  y_aggregations: string[],
  [key: string]: any;
}

export interface previewPayload {
  dashboard_object_id?: string;
  page_object_id?: string;
  title: string;
  type: string;
  position: PositionIntrerface,
  source_type: string,
  source: { dataset_id: string, dataset_name: string },
  config: {}
}

export interface PositionIntrerface {
  x: number,
  y: number,
  w: number,
  h: number
}

export interface cardObject {
  id: string,
  title: string,
  type: string,
  x: number,
  y: number,
  cols: number,
  rows: number,
  source_type: string,
  source: {
    dataset_id: string,
    dataset_name: string
  },
  config: {
    x_label: string,
    x_axis: string,
    y_label: string,
    y_axis: string[],
    chart_type: string,
    subtitle: string,
    x_axis_aggregations: {},
    y_axis_aggregations: {},
    x_axis_category?: string[],
    y_axis_series: {name: string, value: number}[]
  }
}


