<div class="ifp-ai-report">
  <img
    [src]="(themeService.defaultTheme$ | async)!=='dark' ? '../../../assets/images/dashboard-builder/landing-bg.png' : '../../../assets/images/prep-help/landing_page_dark.svg'"
    alt="" class="ifp-ai-report__bg">
  @if (!previewExternal()) {
  <div class="ifp-container ifp-ai-report__breadcrumbs">
    <app-ifp-breadcrumbs  [disableTranslate]="disableTranslate()" [pageData]="pageData"></app-ifp-breadcrumbs>
  </div>
  }
  @if (loader()) {
  <app-ifp-spinner class="ifp-ai-report__loader"></app-ifp-spinner>
  }@else {
  @if (!previewExternal()) {
  <div class="ifp-container ifp-container--sm ifp-ai-report__header">
    <h1 class="ifp-ai-report__heading">{{'AI Insight Report Generator' }}</h1>
    <p class="ifp-ai-report__desc">{{'AI Insight Report Generator simplifies data analysis by leveraging artificial
      intelligence to produce clear, actionable insights in comprehensive reports, enabling smarter decision-making with
      ease.' }}</p>
          <app-ifp-stepper class="ifp-ai-report__stepper" [showCount]="true" [pending]="completedSteps()+1" [gradientTheme]="true" [count]="false" [completed]="completedSteps()" [stepData]="stepsData()"></app-ifp-stepper>
  </div>
  }
  <!-- Report content start -->
  <div class="ifp-container ifp-container--sm">
    <div class="ifp-ai-report__main">
      <div class="ifp-ai-report__main-title">
        <div class="ifp-ai-report__main-title-text">
          {{'Consolidated Insight Report' }}
        </div>
        <div class="ifp-ai-report__main-bottom">
          <div class="ifp-ai-report__main-download-wrap">
            <div class="ifp-ai-report__date-badge">{{report().quarter ??''}}</div>
            @if (!previewExternal()) {
            }
          </div>
          <div class="ifp-ai-report__regenerate" [ngClass]="{'ifp-ai-report__regenerate--left': report().status === 'inprogress'}">
            @if (previewNot()&& !previewExternal()) {
            <!-- <ifp-ai-button [iconClass]="'ifp-icon-ai'" [theme]="'ifp-ai-button--round ifp-ai-button--ai'"
              [label]="'Regenerate'" (ifpClick)="regenarateAll()"></ifp-ai-button> -->
            @if (report().status === 'active' || report().status === 'ticket_creation_failed' )  {
                <ifp-button [disableTranslate]="disableTranslate()"  class="ifp-ai-report__btn" [label]="'Send for review'" [buttonClass]="loaderSubmitProgress() ? buttonClass.disabled : buttonClass.primary+' '+ buttonIconPosition.left" [loader]="loaderSubmitProgress()"
                [iconClass]="loaderSubmitProgress() ? '':'ifp-icon-send-triangle'" (ifpClick)="submit()"></ifp-button>
              }
            <ifp-button [disableTranslate]="disableTranslate()"  class="ifp-ai-report__main-download" [label]="'Download'"  [loader]="downloadProgress()" [iconClass]="downloadProgress() ? '':'ifp-icon-download'"
              [buttonClass]="(downloadProgress() ? buttonClass.disabled : buttonClass.primary )+' '+ buttonIconPosition.left"
              (ifpClick)="downloadReport()"></ifp-button>

            }
            @if (genaratedPreview()){
            <ifp-button  [label]="'Cancel'" [buttonClass]="buttonClass.secondary+' '+ buttonIconPosition.left" [disableTranslate]="disableTranslate()"
              (ifpClick)="cancelGenerate()"></ifp-button>
            <ifp-button class="ifp-ai-report__btn" [label]="'Submit'" [buttonClass]="buttonClass.primary" [disableTranslate]="disableTranslate()"
              (ifpClick)="submitGenerate()"></ifp-button>
            }

          </div>
        </div>



      </div>


      <!-- Key drivers module start -->
      <ifp-gen-ai-key-header [consolidated]="true" [preview]="false" (summeryEdit)="callEditHeader()"
        class="ifp-ai-report__key-driver" [summery]="report().key_drivers?.ai_summary ?? ''"
        [drivers]="report().key_drivers?.drivers?? []"></ifp-gen-ai-key-header>

      @for (item of report().domains; track $index) {
      <ifp-ai-insight-report-consolidated-domain
        (callComparisonEdit)="callComparisonEdit($event,item?.domain?.report_id ?? '' )"
        (callImpactEdit)="callKeyInsightsEdit($event,item?.domain?.report_id ?? '' )"
        (callSentimentalEdit)="callSentimentEdit($event,item?.domain?.report_id ?? '' )" [previewNot]="previewNot()"
        [report]="item" [domainSection]="item.domain" [sentimentanalysis]="item.sentiment"
        [compairData]="item.comparison" [impacts]="item.key_insights"></ifp-ai-insight-report-consolidated-domain>
      }

      <ifp-ai-insight-report-footer [date]="report().published_date ?? ''"></ifp-ai-insight-report-footer>
    </div>

  </div>
  <!-- Report content end -->

  }
</div>
<app-ifp-modal #modal [modalClass]="'ifp-modal__template-help'">
  <ifp-ai-insight-report-popup [consolidated]="true" [data]="popupData()" [genarateAllPop]="previewGenaratePopup()"  [modelAnimate]="modelAnimate()"
    [sectionOne]="sectionOne" [sectionTwo]="sectionTwo" #popup (save)="save($event)" (genarate)="genarate($event)"
    (genarateAll)="genarateAll($event)" [title]="title()" [subTitle]="subTitle()" [type]="modalType()"
    (close)="closeModal()"></ifp-ai-insight-report-popup>
</app-ifp-modal>
