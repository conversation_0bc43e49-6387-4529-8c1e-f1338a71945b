import { Component, inject, <PERSON><PERSON>ni<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, QueryList } from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';

import { SubSink } from 'subsink';
import { ApiService } from 'src/app/scad-insights/core/services/api.service';
import { dxpApi } from '../dxp.constants';
import { ListingPageData, ListingPageItem } from '../dxp.interface';
import { IfpDxpWhatsNewCardComponent } from "src/app/scad-insights/ifp-widgets/ifp-molecules/ifp-dxp-whats-new-card/ifp-dxp-whats-new-card.component";
import { IfpSpinnerComponent } from "../../scad-insights/ifp-widgets/ifp-molecules/ifp-spinner/ifp-spinner.component";
import { IfpNoDataComponent } from "../../scad-insights/ifp-widgets/ifp-molecules/ifp-no-data/ifp-no-data.component";
import { PaginationComponent } from "../../scad-insights/ifp-widgets/ifp-molecules/pagination/pagination.component";
import { DxpTabCommunicationService, TabSelectionData } from '../services/dxp-tab-communication.service';
import { IfpTabComponent } from "../../scad-insights/ifp-widgets/ifp-molecules/ifp-tab/ifp-tab.component";
import { LabelData } from "../../scad-insights/core/interface/atom/ifp-category-label.interface";
import { IfpPanelDropdownComponent, PanelDropdownOptions } from "src/app/ifp-analytics/molecule/ifp-panel-dropdown/ifp-panel-dropdown.component";

// Interface for card action button configuration
export interface CardConfigItem {
  label: string;
  value: string;
  icon: string;
}

@Component({
  selector: 'ifp-dxp-indicator',
  imports: [TranslateModule, IfpDxpWhatsNewCardComponent, IfpSpinnerComponent, IfpNoDataComponent, PaginationComponent, IfpTabComponent, IfpPanelDropdownComponent],
  templateUrl: './dxp-indicator.component.html',
  styleUrl: './dxp-indicator.component.scss'
})
export class DxpIndicatorComponent implements OnInit {

  private readonly _subs: SubSink = new SubSink();
  private readonly _apiService = inject(ApiService);
  private readonly _dxpTabCommunicationService = inject(DxpTabCommunicationService);

  // Filter data from service
  public entitiesFilterData: PanelDropdownOptions[] = [];
  public creatorsFilterData: PanelDropdownOptions[] = [];
  public sortOptionsData: PanelDropdownOptions[] = [];

  public indicatorCards: ListingPageItem[] = [];
  public activeCardIndex: number | null = null; // Track which card is currently expanded
  public sideMenuList: any[] = [];
  public isLoading: boolean = false;
  public currentTabData: TabSelectionData | null = null;
  public selectedSubMenuIndex: number = 0;
  public selectedSortOption: any = null;
  public selectedEntityOptions: PanelDropdownOptions[] = [];
  public selectedCreatorOptions: PanelDropdownOptions[] = [];
  public selectedSortOptions: PanelDropdownOptions[] = [];

  // Card configuration for action buttons
  public cardConfig: CardConfigItem[] = [];

  // Sort dropdown data - keeping original format for API calls
  public sortOptions = [
    {
      label: "By Date Desc",
      order: "desc",
      sort: "createdAt",
      icon: "ifp-icon-down-arrow",
      value: "createdAt_desc"
    },
    {
      label: "By Date Asc",
      order: "asc",
      sort: "createdAt",
      icon: "ifp-icon-up-arrow",
      value: "createdAt_asc"
    },
    {
      label: "A-Z",
      order: "desc",
      sort: "title",
      icon: "ifp-icon-down-arrow",
      value: "title_desc"
    },
    {
      label: "Z-A",
      order: "asc",
      sort: "title",
      icon: "ifp-icon-up-arrow",
      value: "title_asc"
    }
  ];

  // Transform submenu data to LabelData format for IfpTabComponent
  get transformedTabData(): LabelData[] {
    if (!this.currentTabData?.subMenus) return [];

    return this.currentTabData.subMenus.map((subMenu: any) => ({
      name: subMenu.label_en,
      param: subMenu.param,
      // Add other properties as needed
    }));
  }



  // Pagination properties
  public offset: number = 0;
  public limit: number = 10;
  public totalCount: number = 0;
  public currentPage: number = 1;

  // Get references to all DXP card components
  @ViewChildren(IfpDxpWhatsNewCardComponent) dxpCards!: QueryList<IfpDxpWhatsNewCardComponent>;



  ngOnInit(): void {
    // Initialize filter options to empty arrays to prevent null errors
    this.selectedSortOption = null;
    this.selectedEntityOptions = [];
    this.selectedCreatorOptions = [];
    this.selectedSortOptions = [];

    // Initialize sort options data for panel dropdown
    this.sortOptionsData = this.sortOptions.map(option => ({
      key: option.value,
      value: option.label,
      checked: false
    }));

    this.getIndicatorList();
    this.getSideMenuList();

    // Listen to tab selection changes from parent component
    this._subs.add(
      this._dxpTabCommunicationService.tabSelection$.subscribe((tabData: TabSelectionData) => {
        this.handleTabSelection(tabData);
      })
    );

    // Listen to filter data changes from parent component
    this._subs.add(
      this._dxpTabCommunicationService.filterData$.subscribe((filterData) => {
        console.log('Filter data received:', filterData);
        this.processFilterData(filterData);
      })
    );
  }

  processFilterData(filterData: any) {
    console.log('Processing filter data:', filterData);

    // Transform entities filter data to PanelDropdownOptions format
    this.entitiesFilterData = (filterData.entitiesFilterData || []).map((item: any) => ({
      key: item.value || item.name || item.label,
      value: item.label || item.name || item.value,
      id: item.id,
      checked: false
    }));

    // Transform creators filter data to PanelDropdownOptions format
    // For creators, we need to preserve the 'id' field for API calls
    this.creatorsFilterData = (filterData.creatorsFilterData || []).map((item: any) => ({
      key: item.value || item.name || item.label,
      value: item.label || item.name || item.value,
      id: item.id, // Preserve the id field for API calls
      checked: false
    }));

    console.log('Processed entities filter data:', this.entitiesFilterData);
    console.log('Processed creators filter data:', this.creatorsFilterData);
  }

  getIndicatorList(statusParam?: string) {
    this.isLoading = true;
    this.currentPage = Math.floor(this.offset / this.limit) + 1;
    const params: any = {
      status: statusParam,
      limit: this.limit,
      page: this.currentPage
    };

    if (this.selectedSortOption) {
      params.sort = this.selectedSortOption.sort;
      params.order = this.selectedSortOption.order;
    }

    if (this.selectedEntityOptions && this.selectedEntityOptions.length > 0) {
      const entityIds = this.selectedEntityOptions.map(option => option.id || option.key);
      params.entity = entityIds.join(',');
      console.log('Entity IDs being sent to API:', entityIds);
    } else {
      // When no entities are selected, explicitly remove the entity parameter
      // This ensures the API is called without entity filtering
      console.log('No entities selected - calling API without entity filter');
    }

    if (this.selectedCreatorOptions && this.selectedCreatorOptions.length > 0) {
      const creatorIds = this.selectedCreatorOptions.map(option => option.id || option.key);
      params.creator = creatorIds.join(',');
      console.log('Creator IDs being sent to API:', creatorIds);
    }

    this._subs.add(
      this._apiService.getMethodRequest(dxpApi.listEntityKpi, params, true).subscribe({
        next: (resp: ListingPageData) => {
          console.log('API Response:', resp);
          this.indicatorCards = resp?.data ?? [];
          this.totalCount = resp?.totalCount ?? 0;
          this.isLoading = false;
        },
        error: (error) => {
          console.error('Error fetching indicator list:', error);
          this.isLoading = false;
          this.indicatorCards = [];
          this.totalCount = 0;
        }
      })
    );
  }

  handleTabSelection(tabData: TabSelectionData) {

    this.currentTabData = tabData;
    this.selectedSubMenuIndex = 0; // Always start with first submenu when tab changes

    this.offset = 0;
    this.currentPage = 1;

    console.log('handleTabSelection - Received tab data:', tabData);
    if (tabData.subMenus && tabData.subMenus.length > 0) {
      console.log('Available submenus:', tabData.subMenus.map(sub => `${sub.label_en} (${sub.param})`));
    }
    this.cardConfig = this.generateCardConfig(tabData.userRole, tabData.tabKey, tabData.subMenuKey);
    const statusToUse = this.getCurrentStatusParam();
    this.getIndicatorList(statusToUse);
  }

  onSubMenuTabClick(event: { event: any, index: number }) {
    console.log('Submenu tab clicked:', event);

    if (this.currentTabData?.subMenus && this.currentTabData.subMenus[event.index]) {
      const selectedSubMenu = this.currentTabData.subMenus[event.index];
      this.selectedSubMenuIndex = event.index;
      this.offset = 0;
      this.currentPage = 1;
      this.cardConfig = this.generateCardConfig(
        this.currentTabData.userRole,
        this.currentTabData.tabKey,
        selectedSubMenu.param
      );
      const statusToUse = this.getCurrentStatusParam();
      this.getIndicatorList(statusToUse);
    }
  }



  onEntityOptionChange(selectedOptions: PanelDropdownOptions[]) {
    this.selectedEntityOptions = selectedOptions || [];
    this.refreshDataWithFilters();
  }

  onCreatorOptionChange(selectedOptions: PanelDropdownOptions[]) {
    console.log('Creator options changed:', selectedOptions);
    this.selectedCreatorOptions = selectedOptions || [];
    console.log('Selected creator options set to:', this.selectedCreatorOptions);
    console.log('Creator IDs from selected options:', this.selectedCreatorOptions.map(option => option.id));
    this.refreshDataWithFilters();
  }

  onSortOptionChange(selectedOptions: PanelDropdownOptions[]) {
    console.log('Sort options changed:', selectedOptions);
    this.selectedSortOptions = selectedOptions || [];

    // For sort, we only use the first selected option
    if (this.selectedSortOptions.length > 0) {
      const selectedSortKey = this.selectedSortOptions[0].key;
      this.selectedSortOption = this.sortOptions.find(option => option.value === selectedSortKey);
    } else {
      this.selectedSortOption = null;
    }

    console.log('Selected sort option set to:', this.selectedSortOption);
    this.refreshDataWithFilters();
  }

  private refreshDataWithFilters() {
    console.log('refreshDataWithFilters called - Current selected entities:', this.selectedEntityOptions);
    this.offset = 0;
    this.currentPage = 1;
    const currentStatus = this.getCurrentStatusParam();
    this.getIndicatorList(currentStatus);
  }

  /**
   * Centralized method to get the current status parameter
   * This ensures consistency across all methods
   */
  private getCurrentStatusParam(): string {
    if (!this.currentTabData) {
      return '';
    }

    // If tab has submenus, use the currently selected submenu's param
    if (this.currentTabData.subMenus && this.currentTabData.subMenus.length > 0) {
      const selectedSubmenu = this.currentTabData.subMenus[this.selectedSubMenuIndex];
      if (selectedSubmenu) {
        return selectedSubmenu.param;
      }
      // Fallback to first submenu if selectedSubMenuIndex is invalid
      return this.currentTabData.subMenus[0].param;
    }

    // If tab has no submenus, use the tab's param (which was calculated by landing page)
    return this.currentTabData.param || '';
  }

  /**
   * Generate card configuration based on user role and tab/submenu keys
   */
  private generateCardConfig(userRole?: string, tabKey?: string, subMenuKey?: string): CardConfigItem[] {
    console.log('generateCardConfig called with:', { userRole, tabKey, subMenuKey });

    // Generate config for BUILDER role
    if (userRole === 'BUILDER') {
      return this.getBuilderConfig(tabKey, subMenuKey);
    }

    // Generate config for APPROVER role
    if (userRole === 'APPROVER') {
      return this.getApproverConfig(tabKey, subMenuKey);
    }

    // No config for other roles
    return [];
  }

  /**
   * Get card configuration for BUILDER role
   */
  private getBuilderConfig(tabKey?: string, subMenuKey?: string): CardConfigItem[] {
    // Check for specific tab and submenu combinations
    if (tabKey === 'myKpis') {
      if (subMenuKey === 'my-kpis-complete') {
        // For completed KPIs - show Send for Approval and Edit buttons
        const config = [
          {
            label: 'Send for Approval',
            value: 'builder_send',
            icon: 'ifp-icon-send'
          },
          {
            label: 'Edit',
            value: 'builder_Edit',
            icon: 'ifp-icon-edit'
          }
        ];
        return config;
      } else if (subMenuKey === 'my-kpis-draft') {
        // For draft KPIs - show only Edit button
        const config = [
          {
            label: 'Edit',
            value: 'builder_Edit',
            icon: 'ifp-icon-edit'
          }
        ];
        return config;
      }
    }


    return [];
  }

  /**
   * Get card configuration for APPROVER role
   */
  private getApproverConfig(tabKey?: string, subMenuKey?: string): CardConfigItem[] {
    // Check for specific tab and submenu combinations
    if (tabKey === 'approval-status' && subMenuKey === 'kpis') {
      // For approval status KPIs - show Unpublish button
      const config = [
        {
          label: 'Unpublish',
          value: 'approver_unpublish',
          icon: 'ifp-icon-delete'
        }
      ];
      return config;
    }

    return [];
  }

  getSideMenuList() {
    this._subs.add(
      this._apiService.getMethodRequest(dxpApi.getSideMenuList, {}, true).subscribe({
        next: (resp: any) => {
          console.log('Side menu list response:', resp);
          this.sideMenuList = resp?.data ?? resp ?? [];
          // Process the side menu list data here
          this.processSideMenuData();
        },
        error: (error) => {
          console.error('Error fetching side menu list:', error);
        }
      })
    );
  }

  processSideMenuData() {
    // Add your logic here to process the side menu data
    console.log('Processing side menu data:', this.sideMenuList);
    // Example: filter, sort, or transform the data as needed
  }

  onPageChange(offset: number) {
    this.offset = offset;
    console.log(`Page changed: offset=${offset}, will calculate page in getIndicatorList()`);
    this.getIndicatorList();
  }

  onLimitChange(newLimit: number) {
    this.limit = newLimit;
    this.offset = 0;
    this.currentPage = 1;
    this.getIndicatorList();
  }

  resetPagination() {
    this.offset = 0;
    this.currentPage = 1;
    this.totalCount = 0;
  }

  onRemoveCard(event: any) {
    console.log('Card removed:', event);
  }

  onSelectIndicator(event: any) {
    console.log('Indicator selected:', event);
  }

  onCardAction(event: {action: string, cardData: any}) {
    console.log('Card action triggered:', event);
    console.log('Current cardConfig:', this.cardConfig);

    // Handle different card actions based on the value
    switch (event.action) {
      case 'builder_send':
        console.log('Send for Approval action triggered');
        console.log('Card data:', event.cardData);
        this.sendForApproval(event.cardData?.objectId);
        break;
      case 'builder_Edit':
        console.log('Edit action triggered');
        // Implement edit logic here
        alert('Edit functionality will be implemented here');
        break;
      case 'approver_unpublish':
        console.log('Unpublish action triggered');
        console.log('Card data:', event.cardData);
        this.unpublishKpi(event.cardData?.objectId);
        break;
      default:
        console.log('Unknown card action:', event.action);
        alert(`Unknown action: ${event.action}`);
    }
  }

  /**
   * Handle card resize events - ensure only one card is expanded at a time
   * @param expandedIndex - Index of the card that was expanded (null if collapsed)
   * @param cardIndex - Index of the current card (from template)
   */
  onCardResized(expandedIndex: number | null, cardIndex: number): void {
    if (expandedIndex !== null) {
      // A card was expanded - expandedIndex should match cardIndex
      if (this.activeCardIndex !== null && this.activeCardIndex !== cardIndex) {
        // Another card is already expanded, collapse it
        this.collapseCard(this.activeCardIndex);
      }
      this.activeCardIndex = cardIndex;
      console.log('Card expanded at index:', cardIndex);
    } else {
      // A card was collapsed
      if (this.activeCardIndex === cardIndex) {
        this.activeCardIndex = null;
        console.log('Card collapsed at index:', cardIndex);
      }
    }
  }

  /**
   * Programmatically collapse a card by its index
   * @param cardIndex - Index of the card to collapse
   */
  private collapseCard(cardIndex: number): void {
    if (this.dxpCards && this.dxpCards.length > cardIndex) {
      const cardToCollapse = this.dxpCards.toArray()[cardIndex];
      if (cardToCollapse) {
        // Call the resize method with true to collapse the card
        cardToCollapse.resize(true, cardIndex);
        console.log('Collapsed card at index:', cardIndex);
      }
    }
  }

  dropDownItemMultiClicked(selectedItems: PanelDropdownOptions[]) {
    console.log('Multi-select dropdown items clicked:', selectedItems);
    this.selectedCreatorOptions = selectedItems || [];
    this.refreshDataWithFilters();
  }

  onUserSearch(searchTerm: string) {
    console.log('User search term:', searchTerm);
    // Handle search functionality if needed
  }

  currentSelection(): PanelDropdownOptions[] {
    return this.selectedCreatorOptions;
  }

  // Getter methods for selected items display
  getSelectedEntityItems(): PanelDropdownOptions[] {
    return this.selectedEntityOptions;
  }

  getSelectedSortItems(): PanelDropdownOptions[] {
    return this.selectedSortOptions;
  }

  /**
   * Send for Approval API call
   * @param objectId - The objectId of the card to send for approval
   */
  sendForApproval(objectId: string) {
    const payload = {
      objectId: objectId,
      objectType: "ENTITY_KPI",
      approverId: null,
      metadata: {}
    };
    this._subs.add(
      this._apiService.postMethodRequest(dxpApi.sendForApproval, payload, undefined, false).subscribe({
        next: (response) => {
          this.refreshDataWithFilters();
        },
        error: (error) => {
          console.error('Error sending for approval:', error);
        }
      })
    );
  }

  /**
   * Unpublish KPI API call
   * @param objectId - The objectId of the card to unpublish
   */
  unpublishKpi(objectId: string) {
    console.log('Unpublishing KPI with objectId:', objectId);
    // TODO: Implement unpublish API call when endpoint is available
    // For now, show a placeholder message
    alert(`Unpublish functionality will be implemented for KPI: ${objectId}`);

    // Uncomment and modify when API endpoint is available:
    /*
    const payload = {
      objectId: objectId,
      objectType: "ENTITY_KPI",
      metadata: {}
    };
    this._subs.add(
      this._apiService.postMethodRequest(dxpApi.unpublishKpi, payload, undefined, false).subscribe({
        next: (response) => {
          this.refreshDataWithFilters();
        },
        error: (error) => {
          console.error('Error unpublishing KPI:', error);
        }
      })
    );
    */
  }

}
