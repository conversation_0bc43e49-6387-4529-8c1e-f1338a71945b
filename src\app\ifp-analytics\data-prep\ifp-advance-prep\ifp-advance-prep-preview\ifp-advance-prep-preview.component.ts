import {  NgClass } from '@angular/common';
import { ChangeDetectorRef, Component, ElementRef, EventEmitter, Input, OnDestroy, OnInit, Output, Renderer2, signal, ViewChild, WritableSignal } from '@angular/core';
import { Store } from '@ngrx/store';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { SubscriptionLike } from 'rxjs';
import { IfpPanelDropdownComponent, PanelDropdownOptions } from 'src/app/ifp-analytics/molecule/ifp-panel-dropdown/ifp-panel-dropdown.component';
import { IfpProbabilityCardComponent } from 'src/app/ifp-analytics/molecule/ifp-probability-card/ifp-probability-card.component';
import { PreivewPrep, MetadataColumnPrep, PreviewNodeData, TypeProbability } from 'src/app/ifp-analytics/organism/ifp-data-preview/interface/ifp-data-prep.intreface';
import { IfpDataTableComponent } from 'src/app/ifp-analytics/organism/ifp-data-table/ifp-data-table.component';
import { IfpTooltipDirective } from 'src/app/scad-insights/core/directives/ifp-tooltip.directive';
import { DownLoadService } from 'src/app/scad-insights/core/services/download-service/download.service';
import { ToasterService } from 'src/app/scad-insights/core/services/tooster/ToastrService.service';
import { IfpNoDataComponent } from 'src/app/scad-insights/ifp-widgets/ifp-molecules/ifp-no-data/ifp-no-data.component';
import { PaginationComponent } from 'src/app/scad-insights/ifp-widgets/ifp-molecules/pagination/pagination.component';
import { SubSink } from 'subsink';
import { FileResponePrep } from '../../ifp-data-prep/interface/ifp-data-prep.interface';
import { ifpColors } from 'src/app/scad-insights/core/constants/color.constants';
import { environment } from 'src/environments/environment';
import { prepsApiEndpoints } from '../../ifp-data-prep/constants/if-preps.constants';
import { IfpAdvancePrepService } from '../service/ifp-advance-prep.service';
import { IfpCardLoaderComponent } from '../../../../scad-insights/ifp-widgets/ifp-atoms/ifp-card-loader/ifp-card-loader.component';
import { StreamingService } from 'src/app/scad-insights/core/services/http/streaming.service';
import { buttonClass } from 'src/app/scad-insights/core/constants/button.constants';
import { IfpTabComponent } from '../../../../scad-insights/ifp-widgets/ifp-molecules/ifp-tab/ifp-tab.component';

@Component({
    selector: 'ifp-advance-prep-preview',
    templateUrl: './ifp-advance-prep-preview.component.html',
    styleUrl: './ifp-advance-prep-preview.component.scss',
    imports: [TranslateModule, IfpPanelDropdownComponent, IfpDataTableComponent, NgClass, IfpTooltipDirective, IfpProbabilityCardComponent, PaginationComponent,
    IfpNoDataComponent, IfpCardLoaderComponent, IfpTabComponent]
})
export class IfpAdvancePrepPreviewComponent implements OnInit, OnDestroy {
  @ViewChild('preview') preview!: ElementRef;
  @ViewChild('table') table!: ElementRef;
  @ViewChild('log') log!: ElementRef;

  @Output() changeView: EventEmitter<string> = new EventEmitter<string>();
  @Output() backEvent = new EventEmitter();

  @Input() showProbabilty: boolean = false;
  @Input() tableHead: string[] = [];
  @Input() tableData!: any[];
  @Input() showRunMessage: boolean = false;
  @Input() probabilityheading = ['Categorical', 'Numeric', 'ID', 'Boolean'];
  @Input() probabilityvalues: number[] = [];
  @Input() fullScreen = false;

  public uniqueValue = 0;
  public uniqueName = '';
  public uniqueColor = this.getColor(0);
  public offset: number = 0;
  public page: number = 1;
  public limit: number = 10;
  public size: number = 10;
  public subs = new SubSink();
  public dataSetResponse!: FileResponePrep | null;
  public dataValue!: PreivewPrep;
  public metaData = false;
  public firstLoad = false;
  public responseTime: string = '0';
  public metaDataValue!: MetadataColumnPrep[];
  public dragged = false;
  public pagination = false;
  public logHeading = ['Message', 'Time', 'status'];


  public columListDropdown: PanelDropdownOptions[] = [];
  public tableHeadNormal: string[] = [];
  // ['Column Name', 'Data Type', 'Unique Values', 'Null Values', 'Sample Value', 'Max Length', 'Min Length']
  public tableHeadDetail: string[] = ['Column Name', 'Data Type'];
  public currentSelectedValue: PanelDropdownOptions[] = [];

  public tableDataNormal: Record<string, string>[][] = [];

  public tableDataDetail: Record<string, any>[][] = [];

  public probalilityName = '';

  public loader: WritableSignal<boolean> = signal(false);
  public previousY: number | null = null;
  public previewApi!: SubscriptionLike;
  public previewApiWorkFlow!: SubscriptionLike;
  public storeSub!: SubscriptionLike;
  public runMessage!: SubscriptionLike;
  public processSub!: SubscriptionLike;
  public source = false;
  public totalRows: number | undefined = 0;
  public previewLimit:number =0;
  public totalKeys: number = 0;
  public currentNodeId: WritableSignal<string | null> = signal(null);
  public anchor: WritableSignal<number | null> = signal(null);
  public summeryLoader: WritableSignal<boolean> = signal(false);
  public summeryStreemSubscription!: SubscriptionLike;
  public modeSelected: string = 'preview';
  public buttonClass = buttonClass;
  public selectedTabView!: { event: { key: string }; index: number };
  public menuTabs = [
    {
      name: 'Preview',
      key: 'preview'
    },
    {
      name: 'Log',
      key: 'log'
    }
  ];

  private currentNodeData!: {
    nodeId: string | null;
    anchor: number;
    source: boolean;
    sourceId: string;
  };

  public controller!: AbortController;
  constructor(private _cdr: ChangeDetectorRef, public _preService: IfpAdvancePrepService, private _downloadService: DownLoadService, private _toast: ToasterService, private store: Store,
    private _render: Renderer2, private _stremService: StreamingService, private _translate: TranslateService) { }

  ngOnInit() {
    if (this.menuTabs.length) {
      this.selectedTabView = {
        event: {
          key: this.menuTabs[0].key ?? ''
        },
        index: 0
      };
    }
    this.subs.add(this._preService.processStatus.subscribe(() => {
      this._cdr.detectChanges();
      if(this.log?.nativeElement) {
        this.log.nativeElement.scrollTo(0, this.log.nativeElement.scrollHeight);
      }
    }));
    this.resetTables();
    this.storeSub = this._preService.currentNodeAndAnchor.subscribe((data) => {
      if (this._preService.previewNode() == data?.nodeId && this._preService.previewAnchor() == data?.anchor && !data?.afterWorkflow) {
        return;
      }

      if (data?.nodeId && !this._preService.availablePreviewNodes?.[data?.nodeId]) {
        this.clearTableData();
        this._preService.previewNode.set(data?.nodeId);
        return;
      }
      if (data) {
        this.showRunMessage = false;
        this.currentNodeData = data;
        this._preService.previewNode.set(data.nodeId ?? null);
        this.source = data.source;
        this._preService.previewAnchor.set(data.anchor);
        this.page = 1;
        this.offset = 0;
        // this.resetTables();
        if (data.source) {

          this.dataSetResponse = { id: data.sourceId };
          if (this.dataSetResponse?.id) {
            this._preService.isNoRecordFound.set(true);
            this.callDatasetPreview();
          }

          /** used to check  if there is current node and work flow is run*/

        } else if (this._preService.previewNode() && this._preService.previewNode() !== '' && this._preService.isWorkFlowStarted()) {
          this.callWorkflow();
        }
      }
    });

  }

  clearTableData() {
    this._preService.previewTableData = {
      heading: [],
      data: []
    };
  }



  setHeight(height: any) {
    this.table.nativeElement.style.maxHeight = `${height}px`;
    this.preview.nativeElement.style.setProperty('--after-height', this.table.nativeElement.scrollHeight < height ? (`${(height - this.table.nativeElement.scrollHeight) / 2}px`) : 0);
    this._cdr.detectChanges();
  }

  callStreaming() {
    this.loader.set(true);
    this.summeryLoader.set(true);
    this._preService.showPreview.set(true);
    this.summeryStreemSubscription = this._stremService.connectToServerSentEvents(`${environment.prepbaseUrl + prepsApiEndpoints.summeryStream + this.dataSetResponse?.id}/`)
      .subscribe({
        next: (data: { data: string, ctrl: AbortController }) => {
          const dataValue = JSON.parse(data.data);
          this.controller = data.ctrl;
          if ('completed' == dataValue.status) {
            data.ctrl.abort();
            this.summeryStreemSubscription.unsubscribe();
            this.callDatasetPreview();
            this.summeryLoader.set(false);
          }
        },
        error: () => {
          this._stremService.close();
          this.summeryStreemSubscription.unsubscribe();
          this.summeryLoader.set(false);
        }
      }
      );
  }




  callDatasetPreview() {
    this.loader.set(true);
    const startTime = performance.now();
    this.previewApi?.unsubscribe();
    this._preService.isNoRecordFound.set(false);
    this.previewApi = this._preService.getMethodRequest(`${prepsApiEndpoints.libraryDataset}${this.dataSetResponse?.id}${prepsApiEndpoints.libraryPreview}`, { limit: this.limit, offset: this.offset, page: this.page, anchor: this._preService.previewAnchor() }).subscribe({
      next: (data: PreivewPrep) => {
        if (data.records.length <= 0) {
          this._preService.isNoRecordFound.set(true);
          this._preService.showPreview.set(true);
        }
        const endTime = performance.now();
        const responseTime = endTime - startTime;
        this.responseTime = responseTime.toFixed(0);
        this.dataValue = data;
        this.loader.set(false);
        if (!data?.metadata?.columns) {
          this.callStreaming();
        }
        this.size = data.count;
        if (data?.metadata?.columns) {
          this.metaData = true;
          this.metaDataValue = this.dataValue.metadata?.columns ?? [];
        }
        this.totalRows = data.total_rows;
        this.previewLimit = data.count;
        this.setUpTableValue();
        this._preService.showPreview.set(true);
        this.setUpMetaDataNormal();
        this.firstLoad = true;
        this._preService.previewTableData = {
          heading: this.tableHeadNormal,
          data: this.tableDataNormal
        };
        this.totalKeys = Object.keys(this._preService.previewTableData.data[0]).length;
        this.setTableView(this._preService.tableView);
        this._cdr.detectChanges();
      }, error: _error => {
        if (!this.dataSetResponse?.id) {
          this.tableDataDetail = [];
          this._preService.showPreview.set(false);
        }
        this.resetTables();
      }
    });
  }

  callWorkflow() {
    const currentNodeId = this._preService.previewNode();
    this.loader.set(true);
    const startTime = performance.now();
    this.previewApiWorkFlow?.unsubscribe();
    this._preService.isNoRecordFound.set(false);
    this.previewApiWorkFlow = this._preService.getMethodRequest(`${prepsApiEndpoints.getNode + currentNodeId + prepsApiEndpoints.workflowPreview}`, { limit: this.limit, offset: this.offset, page: this.page, anchor: this._preService.previewAnchor() }).subscribe({
      next: (data: PreviewNodeData) => {
        if (data?.total_columns) {
          if (data.records.length <= 0) {
            this._preService.isNoRecordFound.set(true);
            this._preService.showPreview.set(true);
          }
          this.loader.set(false);
          const endTime = performance.now();
          const responseTime = endTime - startTime;
          this.responseTime = responseTime.toFixed(0);
          this.dataValue = {
            count: data.count,
            next: '',
            previous: null,
            records: data.records,
            object_id: currentNodeId ?? '',
            owner: '',
            created_at: '',
            deleted_at: null,
            name: data.name,
            description: '',
            storage_backend: '',
            status: '',
            file: ''
          };
          this.metaDataValue = data.result_metadata;
          this.size = this.dataValue.count;
          if (data.result_metadata) {
            this.metaData = true;
          }
          this._preService.showPreview.set(true);
          this.totalRows = data.total_rows;
          this.previewLimit = data.count;
          this.setUpTableValue();
          this.setUpMetaDataNormal();
          this.setTableView(this._preService.tableView);
          this.firstLoad = true;
          this._preService.previewTableData = {
            heading: this.tableHeadNormal,
            data: this.tableDataNormal
          };
          if ((this._preService.previewTableData.data[0])?.length) {
            this.totalKeys = Object?.keys?.(this._preService.previewTableData.data[0]).length;

            this._cdr.detectChanges();
          }
        } else {
          this._preService.isNoRecordFound.set(true);
          this._preService.showPreview.set(true);
        }
      }, error: _error => {
        this._preService.showPreview.set(false);
        this.resetTables();
      }
    });
  }

  resetTables() {
    this._preService.previewTableData = {
      heading: [],
      data: []
    };
    this.responseTime = '';
    this.columListDropdown = [];
    this.loader.set(false);
    this._preService.showPreview.set(true);
  }

  downloadFileClick() {
    if (this.source) {
      this.downloadFile();
    } else {
      this.downloadFileNode();
    }
  }

  downloadFile() {
    this.subs.add(this._preService.getDownloadRequest(`${environment.prepbaseUrl}/v1/dataset/${this.dataSetResponse?.id}/download/csv/`).subscribe({
      next: data => {
        const matches = (data.headers.get('Content-Disposition'))?.match(/"(.*?)"/);
        const nameValue = matches ? matches[1] : '.xlsx';
        this._downloadService.downloadFiles(data.body, nameValue);
      },
      error: err => {
        const error = err?.error;
        this.errorHandler(error);
      }
    }));
  }


  downloadFileNode() {
    const selectedValue = this.currentSelectedValue.map(data => `"${data.value}"`);
    this.subs.add(this._preService.getDownloadRequest(`${environment.prepbaseUrl}${prepsApiEndpoints.getNode}${this._preService.previewNode()}${prepsApiEndpoints.nodePreviewDownload}?anchor=${this._preService.previewAnchor()}&selected_columns=[${encodeURIComponent(selectedValue.toString())}]`).subscribe({
      next: data => {
        const matches = (data.headers.get('Content-Disposition'))?.match(/"(.*?)"/);
        const nameValue = matches ? matches[1] : '.xlsx';
        this._downloadService.downloadFiles(data.body, nameValue);
      },
      error: err => {
        const error = err?.error;
        this.errorHandler(error);
      }
    }));
  }


  humanize(str: string) {
    let i;
    const frags = str.split('_');
    for (i = 0; i < frags.length; i++) {
      frags[i] = frags[i].charAt(0).toUpperCase() + frags[i].slice(1);
    }
    return frags.join(' ');
  }

  onPageChange(offset: { page: number, offset: number }) {
    this.offset = offset.offset;
    this.page = offset.page;
    this.pagination = true;
    this.loader.set(true);
    if (!this.currentNodeData.source) {
      this.callWorkflow();
    } else {
      this.callDatasetPreview();
    }
  }

  limitChanged(event: number) {
    this.offset = 0;
    this.page = 1;
    this.limit = event;
    this.pagination = true;
    this.loader.set(true);
    if (!this.currentNodeData.source) {
      this.callWorkflow();
    } else {
      this.callDatasetPreview();
    }
  }

  errorHandler(error: any) {
    if (typeof error === 'string') {
      if (error !== '') {
        this._toast.error(error);
      }
    } else {
      for (const key in error) {
        if (Object.prototype.hasOwnProperty.call(error, key)) {
          const element = error[key];
          let data = '';
          if (typeof element === 'string') {
            data = element;
          } else {
            element.forEach((elementValue: string) => {
              data = `${data} ${elementValue}`;
            });
          }
          if (data !== '') {
            this._toast.error(data);
          }
        }
      }
    }
  }

  setUpTableValue() {
    if (!this.pagination) {
      this.columListDropdown = [];
      if (this.metaDataValue) {
        const keys = this.metaDataValue;
        keys.forEach((data: MetadataColumnPrep) => {
          this.columListDropdown.push({ key: data.name, value: data.name, checked: true });
        });
      } else {
        const keys = Object.keys(this.dataValue.records[0] ?? {});
        keys.forEach((data: string) => {
          this.columListDropdown.push({ key: data, value: data, checked: true });
        });
      }
      this.currentSelectedValue = this.columListDropdown;
    }
    this.pagination = false;
    this.setTable();
  }

  setTable() {
    this.tableDataNormal = [];
    this.tableHeadNormal = [];
    this.dataValue.records.forEach((element, index) => {
      this.tableDataNormal.push([]);
      this.currentSelectedValue.forEach(selectedValue => {
        this.tableDataNormal[index].push(
          { key: selectedValue.key, title: element[selectedValue.key], value: element[selectedValue.key], type: 'default' }
        );
      });
    });
    this.currentSelectedValue.forEach(data => {
      this.tableHeadNormal.push(data.key);
    });
  }

  multiSelected(event: PanelDropdownOptions[]) {
    this.currentSelectedValue = event;
    this.setTable();
    this.setUpMetaDataNormal();
    this.setTableView(this._preService.tableView);
  }


  setUpMetaDataNormal() {
    this.tableDataDetail = [];
    this.metaDataValue?.forEach((element: MetadataColumnPrep) => {
      this.currentSelectedValue.forEach(data => {
        if (data.key === element.name) {
          this.tableDataDetail.push(
            [
              { key: 'name', title: 'Column Name', value: element.name ?? '-', type: 'default' },
              { key: 'type', title: 'Data Type', value: `${this._translate.instant(element.type ?? '-')} ${element.type_description ? (`(${this._translate.instant(element.type_description ?? ' ')})` ): ''}`, type: 'default' }
              // {
              //   key: 'uniqueValues',
              //   title: 'Unique Values',
              //   type: element.unique_percentage !== undefined ? 'custom' : 'default',
              //   value: '-',
              //   renderComponent: () => import('src/app/ifp-analytics/molecule/ifp-progress-value/ifp-progress-value.component').then(mode => mode.IfpProgressValueComponent),
              //   inputValues: {
              //     progress: element.unique_percentage ?? 0,
              //     progressValue: element.unique_count ?? 0,
              //     color: ifpColors.secondaryBlue
              //   }
              // },
              // {
              //   key: 'nullValues',
              //   title: 'Null Values',
              //   type: element.null_percentage !== undefined ? 'custom' : 'default',
              //   value: '-',
              //   renderComponent: () => import('src/app/ifp-analytics/molecule/ifp-progress-value/ifp-progress-value.component').then(mode => mode.IfpProgressValueComponent),
              //   inputValues: {
              //     progress: element.null_count,
              //     progressValue: element.null_percentage,
              //     color: ifpColors.secondaryBlue
              //   }
              // },
              // {
              //   key: 'sampleValue',
              //   title: 'Sample Value',
              //   value: element.sample_value !== undefined ? element.sample_value : '-',
              //   type: 'default'
              // },
              // { key: 'maxLength', title: 'Max Length', value: element.max_length !== undefined ? element.max_length : '-', type: 'default' },
              // { key: 'minLength', title: 'Min Length', value: element.min_length !== undefined ? element.min_length : '-', type: 1 },
              // { key: 'probability', title: element.name ?? '-', value: element.type_probability, type: 'hidden' }
            ]);
        }

      });
    });


  }

  rowClick(event: { row: any, index: number }) {
    if (this._preService.tableView !== 'normal') {
      this.probabilityvalues = [];
      this.uniqueValue = 0;
      this.uniqueName = '';
      const probabilityData: TypeProbability = event.row[7].value;
      this.probabilityvalues.push(parseFloat(probabilityData.categorical.toFixed(2)));
      this.probabilityvalues.push(parseFloat(probabilityData.numeric.toFixed(2)));
      this.probabilityvalues.push(parseFloat(probabilityData.id.toFixed(2)));
      this.probabilityvalues.push(parseFloat(probabilityData.boolean.toFixed(2)));
      this.uniqueValue = parseFloat(probabilityData.uniqueness.toFixed(2));
      this.uniqueName = probabilityData.uniqueness_category;
      this.uniqueColor = this.getColor(this.uniqueValue);
      this.probalilityName = event.row[7].title;
    }

  }

  getColor(value: number) {
    if (0.66 <= value && value <= 1) {
      return ifpColors.green;
    } else if (0.33 <= value && value <= 0.66) {
      return ifpColors.yellow;
    }
    return ifpColors.red;

  }

  setTableView(type: 'normal' | 'detail') {
    this._preService.tableView = type;
    this.changeView.emit(type);
    this.probalilityName = '';
    this.probabilityvalues = [];
    if (type === 'detail') {
      this._preService.previewTableData = {
        heading: this.tableHeadDetail,
        data: this.tableDataDetail
      };
      return;
    }
    this._preService.previewTableData = {
      heading: this.tableHeadNormal,
      data: this.tableDataNormal
    };

  }

  changeTabView(event: SelectedTabView) {
    this.selectedTabView = event;
    this.modeSelected = event.event.key;
  }

  ngOnDestroy(): void {
    this.subs.unsubscribe();
    this.previewApi?.unsubscribe();
    this.previewApiWorkFlow?.unsubscribe();
    this.storeSub?.unsubscribe();
    this.processSub?.unsubscribe();
  }
}

interface SelectedTabView {
  event: { key: string };
  index: number;
}
