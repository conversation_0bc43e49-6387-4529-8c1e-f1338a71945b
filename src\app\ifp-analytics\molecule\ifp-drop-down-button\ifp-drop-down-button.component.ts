import { ChangeDetectionStrategy, Component, EventEmitter, Input, OnChanges, Output } from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';
import { buttonClass } from 'src/app/scad-insights/core/constants/button.constants';

@Component({
    selector: 'ifp-drop-down-button',
    imports: [TranslateModule, TranslateModule],
    templateUrl: './ifp-drop-down-button.component.html',
    styleUrl: './ifp-drop-down-button.component.scss',
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class IfpDropDownButtonComponent implements OnChanges {
  @Input() dropdownItems: any[] = [
    {
      icon: 'ifp-icon-play-round'
    },
    {
      icon: 'ifp-icon-play-round'
    }
  ];

  @Input() isIcon: boolean = true;
  @Input() defaultItem: any = this.dropdownItems[0];
  @Input() theme: string = buttonClass.primary;
  @Output() itemClicked: EventEmitter<string> = new EventEmitter<string>();
  @Output() defaultItemClicked: EventEmitter<string> = new EventEmitter<string>();

  public isDropdownEnabled: boolean = true;

  ngOnChanges() {
    this.setDropdownItems();
  }

  // Event fired when clicked on dropdown item
  eventFired (event: string) {
    this.itemClicked.emit(event);
  }

  // Event fired when clicked on default button item
  defaultActionClick (event: string = '') {
    this.defaultItemClicked.emit(event);
  }

  setDropdownItems() {
    const accessItems = this.dropdownItems.filter((item: any) => item?.permission !== false ? item : null);
    this.dropdownItems = accessItems;
    if (this.defaultItem?.permission === false || !this.defaultItem) {
      this.defaultItem = accessItems[0];
    }
    this.checkDropdownStatus(accessItems);
  }

  checkDropdownStatus(accessItems: any[]) {
    if (accessItems?.length !== 0) {
      this.isDropdownEnabled = true;
    }
    if (accessItems?.length === 0 || (accessItems?.length === 1 && accessItems[0]?.label === this.defaultItem?.label) ) {
      this.isDropdownEnabled = false;
    }
  }
}
