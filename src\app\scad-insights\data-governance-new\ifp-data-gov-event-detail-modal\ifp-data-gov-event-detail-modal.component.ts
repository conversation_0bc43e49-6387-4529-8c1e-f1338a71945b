import { dateFormat } from 'src/app/scad-insights/core/constants/date.constants';
import { DatePipe } from '@angular/common';
import { Component, input, InputSignal, output, Input } from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';
import { IfpDropdownComponent } from "../../ifp-widgets/ifp-atoms/ifp-dropdown/ifp-dropdown.component";
import { IfpDataTableComponent } from "../../../ifp-analytics/organism/ifp-data-table/ifp-data-table.component";
import { PaginationComponent } from "../../ifp-widgets/ifp-molecules/pagination/pagination.component";

@Component({
  selector: 'ifp-data-gov-event-detail-modal',
  imports: [TranslateModule, DatePipe, IfpDropdownComponent, IfpDataTableComponent, PaginationComponent],
  templateUrl: './ifp-data-gov-event-detail-modal.component.html',
  styleUrl: './ifp-data-gov-event-detail-modal.component.scss',
})
export class IfpDataGovEventDetailModalComponent {

  @Input() public page: number = 1;
  @Input() public perPage: number = 10;
  @Input() public offset: number = 0;

  public closeModalClick = output();
  public sortClicked = output<{ sort: boolean; sortField: string }>();
  public statusChanged = output<{ value: string }>();
  public pageSettingChanged = output<{ limit: number, offset: number, page: number }>();
  public title: InputSignal<string> = input('Event 1');
  public date: InputSignal<string> = input('');
  public totalDataCount: InputSignal<number> = input(0);
  public statusList: InputSignal<{ value: string; key: string }[]> = input<{ value: string; key: string }[]>([])
public dayDetail: InputSignal<{ data: any; head: string[], headerSettings: any }> = input<{ data: any; head: string[], headerSettings: any }>({ data: [], head: [], headerSettings: [] });
  public dateFormat = dateFormat;

  onCLoseModal() {
    this.closeModalClick.emit();
  }

  onPageChange(event: any) {
    this.offset = event.offset ;
    this.page = (event.offset / this.perPage) + 1;
    this.pageSettingChanged.emit({ limit: this.perPage, offset: this.offset, page: this.page })
  }

  limitChanged(event: any) {
    this.page = 1;
    this.offset = 1;
    this.perPage = event;
    this.pageSettingChanged.emit({ limit: this.perPage, offset: this.offset, page: this.page })
  }

  sortData(event: { sortEvent: boolean, sortEventName: string, sortValue: string }) {
    this.sortClicked.emit({ sort: event.sortEvent, sortField: event.sortEventName })
  }

  changeStatus(event: any) {
    this.statusChanged.emit({ value: event.key == 'all' ? '' : event.key })
    this.page = 1;
    this.offset = 0
  }

}
