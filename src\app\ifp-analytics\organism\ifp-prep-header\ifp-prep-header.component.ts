import { ThemeService } from 'src/app/scad-insights/core/services/theme/theme.service';
import {  NgClass } from '@angular/common';
import { ChangeDetectorRef, Component, ElementRef, EventEmitter, HostListener, Input, OnInit, Output, signal, ViewChild, WritableSignal } from '@angular/core';

import { TranslateModule } from '@ngx-translate/core';
import { IfpModalComponent } from '../../../scad-insights/ifp-widgets/ifp-organism/ifp-modal/ifp-modal.component';
import { IfpHelpComponent } from '../ifp-help/ifp-help.component';
import { IfpButtonComponent } from '../../../scad-insights/ifp-widgets/ifp-atoms/ifp-button/ifp-button.component';
import { buttonClass, buttonIconPosition } from 'src/app/scad-insights/core/constants/button.constants';
import { IfpPrepLibraryComponent } from '../../data-prep/ifp-data-prep/ifp-prep-library/ifp-prep-library.component';
import { CanvasTool } from '../../data-prep/ifp-data-prep/interface/ifp-data-prep.interface';
import { canvasTools } from '../../data-prep/ifp-data-prep/ifp-data-prep.constant';
import { IfpTooltipDirective } from 'src/app/scad-insights/core/directives/ifp-tooltip.directive';
import { IfpTabButtonComponent } from 'src/app/scad-insights/ifp-widgets/ifp-molecules/ifp-toggle-button/ifp-tab-button.component';
import { SubSink } from 'subsink';
import { IfpDbDropdownComponent } from 'src/app/dashboard-builder/molecule/ifp-db-dropdown/ifp-db-dropdown.component';
import { IfpPrepService } from '../../data-prep/ifp-data-prep/ifp-prep-service';

@Component({
    selector: 'ifp-prep-header',
    templateUrl: './ifp-prep-header.component.html',
    styleUrl: './ifp-prep-header.component.scss',
    imports: [TranslateModule, NgClass, IfpModalComponent, IfpHelpComponent, IfpButtonComponent, IfpPrepLibraryComponent,
    IfpTooltipDirective, IfpTabButtonComponent, IfpDbDropdownComponent]
})
export class IfpPrepHeaderComponent implements OnInit {
  @Output() changeMode: EventEmitter<boolean> = new EventEmitter<boolean>();
  @Output() openWorkflow: EventEmitter<boolean> = new EventEmitter<boolean>();
  @Output() togggleToolOption: EventEmitter<CanvasTool> = new EventEmitter<CanvasTool>();
  @Input() title: string = 'Source';
  @Input() descriptionEnable = false;
  @Input() description: string = 'Import your data effortlessly from files or databases.';
  @Input() isExpertMode: boolean = false;
  @Input() disableWorkFlow = signal(true);
  @Input() workFlowRunning = signal(false);
  @Input() streamId: WritableSignal<string> = signal('');
  @Input() sourceDataUploaded: WritableSignal<boolean> = signal(false);
  @Input() tabItems: Record<string, string |boolean>[] = [
    {
      label: 'Basic Mode',
      action: 'basic',
      buttonClass: 'primary',
      isActive: true,
      iconClass: 'ifp-icon-zoom-out'
    },
    {
      label: 'Expert Mode',
      action: 'expert',
      isActive: false,
      iconClass: 'ifp-icon-zoom-in'
    }
  ];

  @Input() selectedTab: Record<string, string |boolean> | null = this.tabItems[0];


  @Output() run = new EventEmitter();
  @Output() saveEvent = new EventEmitter();
  @Output() processEmit = new EventEmitter();
  @ViewChild('help') help!: IfpModalComponent;
  @ViewChild('header') header!: ElementRef;
  @ViewChild('libraryListModal') libraryListModal!: IfpModalComponent;
  @ViewChild('tools') tools!: ElementRef;
  @ViewChild('toolsIcon') toolsIcon!: ElementRef;


  public buttonClass = buttonClass;
  public activeFullScreen: boolean = false;
  public zoomSize: number = 5;
  public activeGrid: boolean = false;
  public canvasTools = canvasTools;
  public isLibraryModelOpen: WritableSignal<boolean> = signal(false);
  public postion = buttonIconPosition;
  public basicHelpData = [
    {
      img: '../../../../assets/images/prep-help/Frame1.png',
      darkImg: '../../../../../../assets/images/prep-help/DataPreparation1.png',
      title: 'Upload Your Data',
      desc: ['Choose how you want to import data from the following data source options.'],
      content: [
        {
          title: 'From My Bayaan Library',
          desc: 'Select data files that you have previously saved in your Bayaan library.'
        },
        {
          title: 'From Indicators',
          desc: 'Pick from the indicators available.'
        },
        {
          title: 'Drag & Drop Files',
          desc: 'Simply drag and drop your data file to upload.'
        }
      ]
    },
    {
      img: '../../../../assets/images/prep-help/Frame2.png',
      darkImg: '../../../../../../assets/images/prep-help/DataPreparation2.png',
      title: 'Prepare Your Data',
      desc: ['Get started with managing and optimizing your data to effectively prepare your data for more robust and accurate analysis.'],
      content: [
        {
          title: 'Modify Data Type',
          desc: 'Modify your data type to suit your analysis needs better and ensure your data is represented accurately.'
        },
        {
          title: 'Clean The Data',
          desc: 'Remove in missing values to deal with any gaps in your data.'
        },
        {
          title: 'Add Calculated Column',
          desc: 'Add a new calculated field using existing data to generate new insights and metrics.'
        }
      ]
    },
    {
      img: '../../../../assets/images/prep-help/Frame3.png',
      darkImg: '../../../../../../assets/images/prep-help/DataPreparation3.png',
      title: 'Export and Save Your Data',
      desc: ['Export your data to the tool and format tailored to your requirements easily. Explore the options available for you.'],
      content: [
        {
          title: 'Advanced Analytics tool',
          desc: 'Export your data to the Advanced Analytics tool for in-depth analysis.'
        },
        {
          title: 'Data Exploration tool',
          desc: 'Export your data to the Data Exploration tool for interactive data exploration and visualization.'
        },
        {
          title: 'Dashboard Builder',
          desc: 'Export your data to Dashboard Builder to create and customize dashboards.'
        },
        {
          title: 'Download or Save Your Data',
          desc: 'Download your data output directly to your device or save it within your Bayaan Library for easy access and future use.'
        }
      ]
    }

  ];

  public advancedHelpData = [
    {
      img: '../../../../assets/images/ifp-analytics/adv-frame-1.png',
      darkImg: '../../../../../../assets/images/prep-help/DataPreparationadavanced1.png',
      title: 'Advance Data Preparation Tool',
      desc: ['Click and drag the selected source onto the canvas/workspace provided.', 'The canvas represents the flow of data through various transformations and into the destination.', 'Draw lines from the final transformation step to the destination.', 'This indicates the flow of transformed data into the target destination.', 'Choose the destination where the processed data will be exported.']
    },
    {
      img: '../../../../assets/images/ifp-analytics/adv-frame-2.png',
      darkImg: '../../../../../../assets/images/prep-help/DataPreparationadavanced2.png',
      title: 'Source',
      desc: ['Choose how you want to import data from the following data source options.'],
      content: [
        {
          title: 'From My Bayaan Library',
          desc: 'Select data files that you have previously saved in the Bayaan library.'
        },
        {
          title: 'From Indicators',
          desc: 'Pick from the indicators available.'
        },
        {
          title: 'Drag & Drop Files',
          desc: 'Simply drag and drop or browse your data file to upload.'
        }
      ]
    },
    // {
    //   img: '../../../../assets/images/ifp-analytics/adv-frame-3.png',
    //   title: 'READ/WRITE',
    //   desc: ['Import data and export the processed data to ideal tools plus library.'],
    //   content: [
    //     {
    //       title: 'Source',
    //       desc: 'Select your preferred data source to import your data.'
    //     },
    //     {
    //       title: 'Destination',
    //       desc: 'Unload your data into a connector, export it directly into a range of tools, download the output, or save it to your IFP library.'
    //     }
    //   ]
    // },
    {
      img: '../../../../assets/images/ifp-analytics/adv-frame-3.png',
      darkImg: '../../../../../../assets/images/prep-help/DataPreparationadavanced3.png',
      title: 'Transform',
      desc: ['Once your source is on the canvas, choose the necessary transformation functions. Drag and drop the transformation functions onto the canvas and connect them to the source.'],
      content: [
        {
          title: 'Select',
          desc: 'Select, deselect, and rename existing columns, and change the data type or name. Your selections will be included in the next step of the flow.'
        },
        {
          title: 'Filter',
          desc: 'Apply filters to refine your data into two streams, one where the entry in the column is true based on the condition selected and one where the entry does not match the value.'
        },
        {
          title: 'Data Cleansing',
          desc: 'Perform basic data cleaning tasks like selecting columns, replacing values, and removing null values, characters, and duplicates to ensure data quality and accuracy.'
        },
        {
          title: 'Aggregate',
          desc: 'Combine/group data from different columns to make analysis easier and more efficient.'
        },
        {
          title: 'Sort',
          desc: 'Sort column data to organize table content. Start with a primary sort, then add more parameters for subsequent sorting.'
        },
        {
          title: 'Data Impute',
          desc: 'Automatically fill in missing values in your dataset with another selected value and ensure your data is complete and accurate.'
        },
        {
          title: 'Calculations',
          desc: 'Use calculations to create and update new columns and deploy expressions to perform functions and operations.'
        },
        {
          title: 'Join',
          desc: 'Discover similarities and link columns from two tables. Connect the tables using either a common identifier or their row positions.'
        },
        {
          title: 'Append',
          desc: 'Attach rows from a Source dataset to a Target dataset.'
        }
      ]
    },
    {
      img: '../../../../assets/images/ifp-analytics/adv-frame-4.png',
      darkImg: '../../../../../../assets/images/prep-help/DataPreparationadavanced4.png',
      title: 'Destination',
      desc: ['Export your data to the tool and format tailored to your requirements easily. Explore the options available for you.'],
      content: [
        {
          title: 'Advanced Analytics Tool',
          desc: 'Export your data to the Advanced Analytics tool for in-depth analysis.'
        },
        {
          title: 'Data Exploration Tool',
          desc: 'Export your data to the Data Exploration tool for interactive data exploration and visualization.'
        },
        {
          title: 'Dashboard Builder',
          desc: 'Export your data to Dashboard Builder to create and customize dashboards.'
        },
        {
          title: 'Download or Save Your Data',
          desc: 'Download your data output directly to your device or save it within your Bayaan Library for easy access and future use.'
        }
      ]
    }

  ];

  public isToggletools: boolean = false;
  public showToggletools: boolean = true;
  public subs = new SubSink();


  constructor(private _cdr: ChangeDetectorRef, public themeService: ThemeService, public _prepService:IfpPrepService) {
  }

  ngOnInit(): void {
    this.isToggletools =  window.innerWidth < 1281;
    if (this.isToggletools) {
      this.showToggletools = false;
    }
  }

  @HostListener('window:pointerup', ['$event.target'])
  onOutsideClick(target: HTMLElement) {
    if (this.showToggletools && this.isToggletools && this.isExpertMode) {
      if (!this.tools.nativeElement.contains(target) && !this.toolsIcon.nativeElement.contains(target)) {
        this.showToggletools = false;
      }
    }
  }

  @HostListener('window:resize')
  resize() {
    this.isToggletools =  window.innerWidth < 1281;
    if (this.isToggletools) {
      this.showToggletools = false;
    }
  }

  save() {
    this.saveEvent.emit();
  }

  onModeChange(state: boolean, event?: any) {
    if (state) {
      this.changeMode.emit(state);
    } else if (!event.isActive) {
      this.selectedTab = null;
      this.changeMode.emit(true);
      setTimeout(() => {
        this.selectedTab = this.tabItems[1];
        this._cdr.detectChanges();
      }, 100);
    }
  }

  runEvent() {
    this.run.emit();
  }

  openHelp() {
    this.help.createElement();
  }

  closeHelp() {
    this.help.removeModal();
  }

  goToLaibrary() {
    // this._router.navigate(['/analytics/prep-library'], { queryParams: { advance: true } });
    this.isLibraryModelOpen.set(true);
    this.libraryListModal.createElement();
  }

  selectFile(_event: any) {
    this.isLibraryModelOpen.set(false);
    this.libraryListModal.removeModal();
  }

  closeModel(_event: any) {
    this.libraryListModal.removeModal();
    this.isLibraryModelOpen.set(false);
    this.openWorkflow.emit(true);
  }


  toggleTool(type: string) {
    let value: any;
    switch (type) {
      case this.canvasTools.fullScreen:
        this.activeFullScreen = !this.activeFullScreen;
        value = this.activeFullScreen;
        break;
      case this.canvasTools.grid:
        this.activeGrid = !this.activeGrid;
        value = this.activeGrid;
        break;
      case this.canvasTools.zoomIn:
        this.zoomSize = this.zoomSize + 1;
        value = this.zoomSize;
        break;
      default:
        if (this.zoomSize > 5) {
          this.zoomSize = this.zoomSize - 1;
          value = this.zoomSize;
        } else {
          value = 5;
        }
    }
    this.togggleToolOption.emit({ type: type, value: value });
  }

  process() {
    this.processEmit.emit();
    this._prepService.processEvent.next('process');
  }
}

