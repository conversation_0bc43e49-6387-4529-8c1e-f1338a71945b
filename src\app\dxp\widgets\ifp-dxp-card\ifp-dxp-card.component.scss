@use "../../../../assets/ifp-styles/abstracts/index" as *;

.ifp-dxp-card {
  &__body {
    padding: $spacer-3;
  }
  &__title,
  &__subTitle {
    font-size: $ifp-fs-5;
  }
  &__title {
    font-weight: $fw-semi-bold;
    margin-bottom: $spacer-1;
  }
  &__subTitle {
    color: $ifp-color-grey-14;
  }
  &__image {
    width: 100%;
    object-fit: cover;
    object-position: center;
    transition: 0.3s;
  }
  &__image-container {
    width: 100%;
    height: 185px;
    overflow: hidden;
  }
}

:host {
  display: block;
  border: 1px solid $ifp-color-grey-13;
  background-color: $ifp-color-white;
  border-radius: 10px;
  overflow: hidden;
  cursor: pointer;
  transition: 0.3s;
  &:hover {
    box-shadow: 2px 4px 8px $ifp-color-black-16;
    .ifp-dxp-card {
      &__image {
        transform: scale(1.1);
      }
    }
  }
}

@include desktop-sm {
  .ifp-dxp-card {
    &__title,
    &__subTitle {
      font-size: $ifp-fs-4;
    }
  }
}
