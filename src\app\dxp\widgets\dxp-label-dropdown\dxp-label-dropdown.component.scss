@use "../../../../assets/ifp-styles/abstracts/index" as *;
.ifp-dxp-label-dropdown {
  position: relative;
  background-color: $ifp-color-white;
  min-height: 34px;
  min-width: 50px;
  border-radius: 6px;
  display: flex;
  color:$ifp-color-grey-14;
  padding: $spacer-2 $spacer-3 ;
  align-items: center;
  &__text {
    font-size: $ifp-fs-4;
  }
  &__icon {
    margin-inline-end: $spacer-2;
  }
  &__dropdown {
    margin-inline-start: $spacer-4;
    padding: $spacer-1 $spacer-3;
    background-color: $ifp-color-blue-menu;
    color: $ifp-color-white;
    border-radius:36px ;
    display: flex;
    font-size: $ifp-fs-2;
    align-items: center;
  }
  &__arrow {
    margin-inline-start: $spacer-2;
    color: $ifp-color-white;
    font-size: $ifp-fs-1;
  }
  &__cross {
    margin-inline-start: $spacer-2;
    color: $ifp-color-grey-14;
    font-size: $ifp-fs-1;
    cursor: pointer;
  }
  &__dropdown-list{
    z-index: 150;
    top: 100%;
    right: 0;
    position: absolute;
    background-color: $ifp-color-white;
    color: $ifp-color-black;
    border-radius: 10px;
    padding: $spacer-2 $spacer-0;
    box-shadow: 0 0 8px $ifp-color-black-16;
    margin-top: $spacer-1;
    &--active {
      background-color: $ifp-color-blue-menu;
      color: $ifp-color-white;
    }
  }
  &__dropdown-list-li {
    padding:$spacer-1  $spacer-2;
    margin-bottom: $spacer-3;
    &:last-child {
      margin-bottom: 0;
    }
    cursor: pointer;
    &:hover {
      background-color: $ifp-color-blue-menu;
      color: $ifp-color-white;
    }
  }
  &__dropdown-list-li{
    border-radius: 6px;
    overflow: hidden;
  }
}
