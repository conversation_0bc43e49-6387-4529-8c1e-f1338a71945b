<div class="ifp-templ-list">
  <div class="ifp-templ-list__header">
    <span class="ifp-templ-list__title">{{'New Dashboard' | translate}}</span>
    <em class="ifp-icon ifp-icon-cross" (click)="onCloseModal()"></em>
  </div>

  <div class="ifp-templ-list__body-wrapper">
    <p class="ifp-templ-list__lits-title">{{'My Templates' | translate}}</p>
    <div class="ifp-templ-list__card-wrapper">
      <div class="ifp-templ-list__db-card ifp-templ-list__db-card--blank" (click)="selectedTemplateIndex = -1" [ngClass]="{'ifp-templ-list__db-card--active': selectedTemplateIndex === -1}">
        <div class="ifp-templ-list__stat-card">
          <em class="ifp-icon ifp-icon-new-document ifp-templ-list__file-icon"></em>

          <div class="ifp-templ-list__db-card-text-wrapper">
            <span class="ifp-templ-list__text">Blank Dashboard</span>
          </div>
        </div>
      </div>

      @for (item of [].constructor(3); track $index) {
      <div class="ifp-templ-list__db-card" (click)="selectedTemplateIndex = $index" [ngClass]="{'ifp-templ-list__db-card--active': selectedTemplateIndex === $index}">
        <div class="ifp-templ-list__card-body">
          <img src="../../../../assets/images/dashboard-builder/dashboard_image.png" alt="">
        </div>
        <div class="ifp-templ-list__card-footer">
          <span class="ifp-templ-list__db-name">Finance Dashboard</span>
        </div>
      </div>
      }
    </div>
  </div>
  <div class="ifp-templ-list__footer">
    <ifp-button class="ifp-templ-list__btn" [label]="'Cancel'" [buttonClass]="buttonClass.secondary"
      (ifpClick)="apply()"></ifp-button>
    <ifp-button class="ifp-templ-list__btn" [label]="'Proceed'"
      (ifpClick)="onCloseModal()" [buttonClass]="buttonClass.primary"
      [iconClass]=" 'ifp-icon-rightarrow'"></ifp-button>
  </div>
</div>
