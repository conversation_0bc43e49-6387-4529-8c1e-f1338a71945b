@use "../../../../assets/ifp-styles/abstracts/index" as *;

:host {
  display: block;
  background-color: $ifp-color-section-white;
  padding: $spacer-6 $spacer-5 $spacer-0 $spacer-5;
  overflow: hidden;
  box-shadow: 0 -57px 50px -60px rgba(212, 220, 229, 0.46);
}

.ifp-data-tree {
  display: flex;
  justify-content: center;
  position: relative;
  min-height: 375px;
  &__progress {
    transform: translateY(50%);
  }
  &__box-wrapper {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    padding-bottom: $spacer-6;
    position: absolute;
    top: 0;
    left: 50%;
    width: 100%;
    height: 100%;
    max-width: 1400px;
    transform: translateX(-50%);
  }
  &__box-tag {
    height: 8px;
    width: 52px;
    border-radius: 10px 10px 0 0;
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translate(-50%, 0);
  }
  &__box-value {
    display: flex;
    align-items: center;
  }
  &__value-text {
    font-size: $ifp-fs-9;
    font-weight: $fw-bold;
  }
  &__value-percent {
    font-size: $ifp-fs-5;
    font-weight: $fw-medium;
    color: $ifp-color-green-dark-1;
    margin-inline-start: $spacer-2;
    display: inline-block;
  }
  &__box {
    border: 3px solid $ifp-color-pale-grey;
    border-radius: 20px;
    padding: $spacer-4;
    position: relative;
    min-width: 280px;
    &::before,
    &::after {
      content: "";
      position: absolute;
    }
    &::before {
      width: 6px;
      height: 100%;
      background-color: $ifp-color-pale-grey;
      top: 100%;
      left: 50%;
      transform: translateX(-50%);
    }
    &:first-child,
    &:last-child {
      align-self: flex-end;
      &::before,
      &::after {
        width: 50%;
        height: 26%;
        border: 6px solid $ifp-color-pale-grey;
        background-color: transparent;
        transform: none;
      }
      .ifp-data-tree__box-tag {
        height: 52px;
        width: 8px;
        bottom: 50%;
        transform: translate(0, 50%);
      }
    }
    &:first-child {
      &::before {
        border-inline-start: 0;
        border-bottom: 0;
        top: 50%;
        left: 100%;
        border-top-right-radius: 30px;
      }
      &::after {
        border-inline-end: 0;
        border-top: 0;
        bottom: 0;
        left: 150%;
        border-bottom-left-radius: 30px;
        margin-inline-start: -6px;
      }
      .ifp-data-tree__box-tag {
        border-radius: 10px 0 0 10px;
        left: auto;
        right: 0;
      }
    }
    &:last-child {
      &::before {
        border-inline-end: 0;
        border-bottom: 0;
        top: 50%;
        right: 100%;
        left: auto;
        border-top-left-radius: 30px;
      }
      &::after {
        border-inline-start: 0;
        border-top: 0;
        bottom: 0;
        right: 150%;
        left: auto;
        border-bottom-right-radius: 30px;
        margin-inline-end: -6px;
      }
      .ifp-data-tree__box-tag {
        border-radius: 0 10px 10px 0;
        left: 0;
      }
    }
    &:only-child {
      align-self: flex-start;
      &::before {
        width: 6px;
        height: 100%;
        background-color: $ifp-color-pale-grey;
        top: 100%;
        left: 50%;
        transform: translateX(-50%);
      }
      &::after {
        content: none;
      }
    }
  }

  &__box-title {
    font-size: $ifp-fs-4;
    color: $ifp-color-grey-14;
    font-weight: $fw-medium;
    margin-bottom: $spacer-2;
  }
  &__box-icon {
    font-size: $ifp-fs-7;
    display: inline-block;
    margin-inline-end: $spacer-1;
    position: relative;
    top: 3px;
  }
  &--single {
    .ifp-data-tree {
      &__box-wrapper {
        justify-content: center;
      }
    }
  }
  &--double {
    min-height: 0;
    margin-top: -10.6%;
  }
}
