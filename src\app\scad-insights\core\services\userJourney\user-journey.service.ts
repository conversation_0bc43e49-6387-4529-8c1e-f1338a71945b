import { BehaviorSubject } from 'rxjs';
import { Injectable } from '@angular/core';
import { HttpService } from '../http/http.service';
import { journeyApi } from '../../apiConstants/jounery.api.constants copy';

@Injectable({
  providedIn: 'root'
})
export class UserJourneyService {
  public directiveList: any = {};
  public enableUserJourney = new BehaviorSubject(false);
  public currentElementClick= new BehaviorSubject<null | any>(null);
  public updateDirective= new BehaviorSubject<null | any>(null);
  public step = new BehaviorSubject<number| null>(null);
  public removeBlinker = new BehaviorSubject<boolean>(false);
  public observableActivate = new BehaviorSubject<boolean| null>(null);
  public resumeJourny = new BehaviorSubject<boolean| any>(null);
  public journeyData: any = {};
  public journeyApiData: any = {};
  public exploreClicked = new BehaviorSubject<string| null>(null);
  public isHideBlinker:boolean=false;
  public isExplore:boolean=false;
  // public stepMax!: number | undefined;
  // public stepMin!: number | undefined;
  constructor(private _http: HttpService) {}


  getUserJourneyData(){
    return this._http.get(journeyApi.getStatus);
  }

  setUserJourney () {
    return this._http.post(journeyApi.setStatus);
  }

}
