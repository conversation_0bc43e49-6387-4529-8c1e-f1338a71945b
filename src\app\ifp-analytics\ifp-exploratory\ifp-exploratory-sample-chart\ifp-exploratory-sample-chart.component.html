<div class="ifp-exp-pre">
  @if (chartDataApi) {
    <div  class="ifp-exp-pre__heading-wrapper">
      <h6 class="ifp-exp-pre__heading">{{'Time series analysis for the target variable'| translate}} - <span class="ifp-exp-pre__heading-sub">{{' '+yAxisLabel()}}</span> </h6>
      <!-- <div class="ifp-exp-pre__btn-wrapper">
        <ifp-button  [buttonClass]="buttonClass.secondary"  [label]="'Variable Selection'"class="ifp-exp-pre__btn-analyze" (ifpClick)="backButton.emit()"></ifp-button>
        <ifp-button  [buttonClass]="buttonClass.primary"  [label]="'Run Model'" [iconClass]="'ifp-icon-play-round'"class="ifp-exp-pre__btn-run" (ifpClick)="runButton.emit()"></ifp-button>
      </div> -->
    </div>
    <app-ifp-analytic-line-chart [height]="400" class="ifp-exp-pre__chart" [chartData]="chartData" [isRangeSelect]="false" #chartComponent [yaxisLabel]="yAxisLabel()"  [xAxisLabel]="xAxisLabel()"></app-ifp-analytic-line-chart>

  } @else if(chartLoader) {
    <app-ifp-spinner class="ifp-exp-pre__loader"></app-ifp-spinner>
  } @else {
    <app-ifp-no-data class="ifp-exp-pre__no-data"></app-ifp-no-data>
  }

</div>
