@use "../../../../assets/ifp-styles/abstracts/index" as *;
:host {
  display: block;
}
.ifp-dxp-filter-badge {
  background-color: $ifp-color-white;
  border-radius: 7px;
  padding: $spacer-3;

  &__container {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  &__title {
    font-size: $ifp-fs-5;
    font-weight: $fw-semi-bold;
    margin-inline-end: $spacer-2;
    flex: 1;
  }

&__filter{
  color: $ifp-color-grey-14;
}
  &__value {
    font-size: $ifp-fs-4;
    font-weight: $fw-bold;
    color: $ifp-color-black;
  }
  &__comparator {
    font-size: $ifp-fs-4;
    font-weight: $fw-bold;
    color: $ifp-color-green-dark;
  }
  &__icon-sec {
    margin-inline-start: auto;
    display: flex;
    .ifp-icon {
      font-size: $ifp-fs-5;
      display: inline-block;
      margin-inline-start: $spacer-2;
      cursor: pointer;
      transition: 0.3s;
      &:hover {
        color: $ifp-color-blue-hover;
      }
    }
  }
  &--active {
    box-shadow: 0 0 2px $ifp-color-blue-hover;
  }
}
