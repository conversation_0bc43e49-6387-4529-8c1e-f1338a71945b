export const governanceTab = [
  {
    name: 'HOME',
    iconClass: 'ifp-icon-home-one',
    value: 'home',
    dropdownTitle: 'Home'
  },
  {
    name: 'BAYAAN STATISTICAL PRODUCTS',
    iconClass: 'ifp-icon-usecase',
    value: 'use_case',
    dropdownTitle: 'Use Case Name',
    tabTitle: 'Statistical Product Details'
  },
  // {
  //   name: 'DATA SOURCE',
  //   iconClass: 'ifp-icon-data-source',
  //   value: 'data_source',
  //   dropdownTitle: 'Source Name'
  // },
  // {
  //   name: 'DATA SCEINCE',
  //   iconClass: 'ifp-icon-data-science',
  //   value: 'data_science',
  //   dropdownTitle: 'Use Case Name'
  // },
  // {
  //   name: 'WORKFLOW DETAILS',
  //   iconClass: 'ifp-icon-workflow-details',
  //   value: 'workflow_details',
  //   dropdownTitle: 'Use Case Name'
  // },
  {
    name: 'BAYAAN SV',
    iconClass: 'ifp-icon-analysis',
    value: 'bayaan_svs',
    dropdownTitle: 'SV Name',
    tabTitle: 'Bayaan SV Details'
  },
  {
    name: 'OFFICIAL SV',
    iconClass: 'ifp-icon-statistical-indicators',
    value: 'statistical_indicator',
    dropdownTitle: 'SV Name',
    tabTitle: 'SV Details'
  },
  {
    name: 'CALENDAR',
    iconClass: 'ifp-icon-calender',
    value: 'calendar',
    dropdownTitle: 'SV Name',
    tabTitle: 'Calendar'
  }
];

export const tabKeys = {
  home: 'home',
  use_case: 'use_case',
  data_source: 'data_source',
  data_science: 'data_science',
  workflow_details: 'workflow_details',
  bayaan_svs: 'bayaan_svs',
  statistical_indicator: 'statistical_indicator',
  calender: 'calendar'
}



export const categories = [
  {
    title: 'Total Forcasts',
    icon: 'ifp-icon-graph-progress',
    color: '#ac92eb',
    count: 22
  },
  {
    title: 'Insight Discovery',
    icon: 'ifp-icon-line-bar',
    color: '#4fc1e8',
    count: 11
  },
  {
    title: 'Scenario Drivers',
    icon: 'ifp-icon-app-settings',
    color: '#a0d568',
    count: 5
  },
  {
    title: 'Screeners',
    icon: 'ifp-icon-filter-torch',
    color: '#f8b100',
    count: 5
  },
  {
    title: 'Dashboards',
    icon: 'ifp-icon-round-bar',
    color: '#ed5564',
    count: 13
  },
  {
    title: 'Reports',
    icon: 'ifp-icon-round-bar',
    color: '#A9C46C',
    count: 13
  },
  {
    title: 'Publications',
    icon: 'ifp-icon-round-bar',
    color: '#E7D283',
    count: 13
  },
];

export const chartDataUseCase = [
  {
    'type': 'pie',
    'data': [
      {
        'name': 'Up to date',
        'y': 12,
        'color': '#5DB14E'
      },
      {
        'name': 'Not started',
        'y': 14,
        'color': '#FAC656'
      },
      {
        'name': 'Outdated',
        'y': 23,
        'color': '#F3593A'
      }
    ]
  }
];

export const chartDataStatistical = [
  {
    'type': 'pie',
    'data': [
      {
        'name': 'On time',
        'y': 4,
        'color': '#707070'
      },
      {
        'name': 'Awaiting refresh',
        'y': 2,
        'color': '#565be2'
      },
      {
        'name': 'Delayed',
        'y': 2,
        'color': '#0cefce'
      },
      {
        'name': 'Outdated',
        'y': 2,
        'color': '##e25656'
      }
    ]
  }
];

export const useCaseTableHead = ['Use Case', 'Status', 'Latest Observation Date'];

export const statisticalTableHead = ['Data Source', 'Status', 'Ownership'];

export const useCaseTableData = [
  [
    {
      'key': 'Use Case',
      'title': 'MEED',
      'value': 'MEED',
      'type': 'default'
    },
    {
      'key': 'Status',
      'title': 'Up to date',
      'value': 'Up to date',
      'type': 'default'
    },
    {
      'key': 'Latest Observation Date',
      'title': '10/03/2024',
      'value': '10/03/2024',
      'type': 'default'
    }
  ],
  [
    {
      'key': 'Use Case',
      'title': 'NI',
      'value': 'NI',
      'type': 'default'
    },
    {
      'key': 'Status',
      'title': 'Up to date',
      'value': 'Up to date',
      'type': 'default'
    },
    {
      'key': 'Latest Observation Date',
      'title': '14/12/2023',
      'value': '14/12/2023',
      'type': 'default'
    }
  ],
  [
    {
      'key': 'Use Case',
      'title': 'PMI',
      'value': 'PMI',
      'type': 'default'
    },
    {
      'key': 'Status',
      'title': 'Up to date',
      'value': 'Up to date',
      'type': 'default'
    },
    {
      'key': 'Latest Observation Date',
      'title': '23/01/2019',
      'value': '23/01/2019',
      'type': 'default'
    }
  ],
  [
    {
      'key': 'Use Case',
      'title': 'STR',
      'value': 'STR',
      'type': 'default'
    },
    {
      'key': 'Status',
      'title': 'Outdated',
      'value': 'Outdated',
      'type': 'default'
    },
    {
      'key': 'Latest Observation Date',
      'title': '14/12/2023',
      'value': '14/12/2023',
      'type': 'default'
    }
  ],
  [
    {
      'key': 'Use Case',
      'title': 'MEED',
      'value': 'MEED',
      'type': 'default'
    },
    {
      'key': 'Status',
      'title': 'Outdated',
      'value': 'Outdated',
      'type': 'default'
    },
    {
      'key': 'Latest Observation Date',
      'title': '14/12/2023',
      'value': '14/12/2023',
      'type': 'default'
    }
  ]
];

export const statisticalTableData = [
  [
    {
      'key': 'Data Source',
      'title': 'Experimental',
      'value': 'Experimental',
      'type': 'default'
    },
    {
      'key': 'Status',
      'title': 'Data team',
      'value': 'Data team',
      'type': 'default'
    },
    {
      'key': 'Ownership',
      'title': 'Data team',
      'value': 'Data team',
      'type': 'default'
    }
  ],
  [
    {
      'key': 'Data Source',
      'title': 'Official indicators',
      'value': 'Official indicators',
      'type': 'default'
    },
    {
      'key': 'Status',
      'title': 'Bayaan team',
      'value': 'Bayaan team',
      'type': 'default'
    },
    {
      'key': 'Ownership',
      'title': 'Bayaan team',
      'value': 'Bayaan team',
      'type': 'default'
    }
  ],
  [
    {
      'key': 'Data Source',
      'title': 'Official screener',
      'value': 'Official screener',
      'type': 'default'
    },
    {
      'key': 'Status',
      'title': 'Strategy team',
      'value': 'Strategy team',
      'type': 'default'
    },
    {
      'key': 'Ownership',
      'title': 'Strategy team',
      'value': 'Strategy team',
      'type': 'default'
    }
  ]
];

export const useCaseDetailHead = ['Topic', 'Total Indicators', 'Up-to-date Indicators', 'Awaiting Bayaan Approval', 'Delayed Due to Data Issue'];

export const useCaseDetailData = [
  [
    {
      'key': 'Topic',
      'title': 'Visa Insight Discovery',
      'value': 'Visa Insight Discovery',
      'type': 'default'
    },
    {
      'key': 'Total Indicators',
      'title': '20240601',
      'value': '20240601',
      'type': 'default'
    },
    {
      'key': 'Up-to-date Indicators',
      'title': 'Monthly',
      'value': 'Monthly',
      'type': 'default'
    },
    {
      'key': 'Awaiting Bayaan Approval',
      'title': '20240701',
      'value': '20240701',
      'type': 'default'
    },
    {
      'key': 'Delayed Due to Data Issue',
      'title': 'Up-to-date',
      'value': 'Up-to-date',
      'type': 'default'
    }
  ],
  [
    {
      'key': 'Topic',
      'title': 'Visa Insight Discovery',
      'value': 'Visa Insight Discovery',
      'type': 'default'
    },
    {
      'key': 'Total Indicators',
      'title': '20240601',
      'value': '20240601',
      'type': 'default'
    },
    {
      'key': 'Up-to-date Indicators',
      'title': 'Monthly',
      'value': 'Monthly',
      'type': 'default'
    },
    {
      'key': 'Awaiting Bayaan Approval',
      'title': '20240701',
      'value': '20240701',
      'type': 'default'
    },
    {
      'key': 'Delayed Due to Data Issue',
      'title': 'Up-to-date',
      'value': 'Up-to-date',
      'type': 'default'
    }
  ],
  [
    {
      'key': 'Topic',
      'title': 'Visa Insight Discovery',
      'value': 'Visa Insight Discovery',
      'type': 'default'
    },
    {
      'key': 'Total Indicators',
      'title': '20240601',
      'value': '20240601',
      'type': 'default'
    },
    {
      'key': 'Up-to-date Indicators',
      'title': 'Monthly',
      'value': 'Monthly',
      'type': 'default'
    },
    {
      'key': 'Awaiting Bayaan Approval',
      'title': '20240701',
      'value': '20240701',
      'type': 'default'
    },
    {
      'key': 'Delayed Due to Data Issue',
      'title': 'Up-to-date',
      'value': 'Up-to-date',
      'type': 'default'
    }
  ]
]

export const filters = {
  topic: 'topic',
  theme: 'theme',
  subTheme: 'subTheme',
  product: 'product',
  updateDate: 'updatedDate'
}

export const statusValues = {
  OUTDATED: 'The data has not been refreshed, and the scheduled target date has already passed.',
  'UP TO DATE': 'The data has been refreshed on or before the scheduled target date.',
  'NOT STARTED': 'The data was updated for the previous target date, and we are still within the same month or quarter based on the refresh frequency. There is still time remaining before the current target date to refresh the data',
  Delayed: 'The data is up to date, but the refresh occurred after the scheduled target date had passed.',
  'On Time': 'The data has been refreshed on or before the current target date, aligning with the expected refresh frequency.'
}



export const statisticTableData = [
  {
    name: 'PMI Screener',
    type: 'Bayaan Screener',
    status: 'Not Started',
    Refrence_date: '01 Dec 2024'
  },
  {
    name: 'PMI Screener',
    type: 'Bayaan Screener',
    status: 'Outdated',
    Refrence_date: '01 Dec 2024'
  },
  {
    name: 'PMI Screener',
    type: 'Bayaan Screener',
    status: 'Up to date',
    Refrence_date: '01 Dec 2024'
  },
  {
    name: 'PMI Screener',
    type: 'Bayaan Screener',
    status: 'Not Started',
    Refrence_date: '01 Dec 2024'
  },
  {
    name: 'PMI Screener',
    type: 'Bayaan Screener',
    status: 'Outdated',
    Refrence_date: '01 Dec 2024'
  }
]

export const statisticalValues = [
  {
    source_name: 'GDP Growth',
    values: [
      {
        status: 'Delayed',
        count: 60
      },
      {
        status: 'Up to date',
        count: 10
      },
      {
        status: 'Not started',
        count: 30
      }
    ],
    total: 100
  },
  {
    source_name: 'PMI Screener',
    values: [
      {
        status: 'Up to date',
        count: 30
      },
      {
        status: 'Delayed',
        count: 60
      }
    ],
    total: 90
  }
]


// DO NOT REMOVE //

export const productIcons = {
  dashboards: 'ifp-icon-dashboards',
  reports: 'ifp-icon-draft',
  forecast: 'ifp-icon-forcasts',
  publications: 'ifp-icon-publications',
  bayaanscreener: 'ifp-icon-publications',
  scenariodrivers: 'ifp-icon-app-settings',
  insightdiscovery: 'ifp-icon-insight-discovery',
  outdated: 'ifp-icon-library',
  uptodate: 'ifp-icon-library',
  delayed: 'ifp-icon-library',
  ontime: 'ifp-icon-library',
  inprogress: 'ifp-icon-library',
  notstarted: 'ifp-icon-library',
  correlation: 'ifp-icon-correlation',
}

export const overviewTitles = {
  home: 'Total Indicators',
  use_case: 'Total Statistical Products',
  bayaan_svs: 'Total SVs'
}

export const statusList = [
  {
    name: 'Outdated',
    color: '#F3593A',
    key: 'outdated'
  },
  {
    name: 'Delayed',
    color: '#FAC656',
    key: 'delayed'
  },
  {
    name: 'Not Started',
    color: '#FAC656',
    key:'notstarted'
  },
  {
    name: 'Up to date',
    color: '#5DB14E',
    key:'uptodate'
  }
]



