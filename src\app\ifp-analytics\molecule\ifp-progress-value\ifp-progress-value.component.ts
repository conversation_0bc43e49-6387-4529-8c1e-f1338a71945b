import { IfpTooltipDirective } from 'src/app/scad-insights/core/directives/ifp-tooltip.directive';
import { Component, Input } from '@angular/core';
import { DecimalPipe, NgClass, NgStyle } from '@angular/common';
import { ShortNumberPipe } from 'src/app/scad-insights/core/pipes/shortNumber.pipe';

@Component({
    selector: 'ifp-progress-value',
    imports: [DecimalPipe, ShortNumberPipe, NgClass, NgStyle, IfpTooltipDirective],
    templateUrl: './ifp-progress-value.component.html',
    styleUrl: './ifp-progress-value.component.scss'
})
export class IfpProgressValueComponent {
  @Input() progress: number = 0;
  @Input() progressValue: number = 0;
  @Input() color!: string;
  @Input() bordered = false;
  @Input() rect = false;
  @Input() percentage = false;
  @Input() value = true;
  @Input() fullDivTooltip = '';
  @Input() fullDivTooltipDisable = true;
  @Input() disableTooltip = false;
  @Input() height: string = '8px';
  @Input() type?: string;
  @Input() progressDecimalCount = 1;
}
