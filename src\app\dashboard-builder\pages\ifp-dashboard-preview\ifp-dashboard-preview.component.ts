import { Component } from '@angular/core';
import { IfpDashboardViewComponent } from '../../organism/ifp-dashboard-view/ifp-dashboard-view.component';
import { DashboardService } from 'src/app/scad-insights/core/services/create-dashboard/dashboard.service';

@Component({
    selector: 'ifp-dashboard-preview',
    imports: [IfpDashboardViewComponent],
    templateUrl: './ifp-dashboard-preview.component.html',
    styleUrl: './ifp-dashboard-preview.component.scss'
})
export class IfpDashboardPreviewComponent {

  public selectedCards: any = [];

  constructor(private _dashboardService: DashboardService) {

    if (localStorage.getItem(this._dashboardService.selectedCards)) {
      this.selectedCards = localStorage.getItem(this._dashboardService.selectedCards);
      this.selectedCards = JSON.parse(this.selectedCards);
    }
  }
}
