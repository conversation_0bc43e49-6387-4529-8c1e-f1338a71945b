<div class="ifp-select" [ngClass]="{'ifp-select--modal': isModal()}">
  <em class="ifp-icon ifp-icon-cross ifp-select__close-modal" (click)="onCancel()"></em>
  <div class="ifp-select__wrapper">
    <h2 class="ifp-select__heading-section">{{heading() | translate}}</h2>
    @if (description() !== '') {
      <h4 class="ifp-select__heading-sub">{{description() | translate}}</h4>
    }
    <div class="ifp-select__center">
      @for (card of cardData(); let i = $index; track i) {
      <div class="ifp-select__cta"  [ngClass]="{'ifp-select__cta--active': selectedCardIndex === $index}" (click)="onSelection(i)">
        <em class="ifp-icon ifp-select__box-icon" [class]="card.icon" [style.color]="card.color"></em>
        <h3 class="ifp-select__box-title">{{ card.title | translate}}</h3>
        <p class="ifp-select__box-desc">{{ card.description | translate}}</p>
      </div>
      }
    </div>
    <div class="ifp-select__footer">
      <ifp-button  [label]="'Cancel'"  class="ifp-select__button ifp-select__button--cancel" (ifpClick)="onCancel()" [buttonClass]="buttonClass.secondary"></ifp-button>
      <ifp-button  [label]="'Proceed'"  class="ifp-select__button" (ifpClick)="nextPage()" [buttonClass]="buttonClass.primary" [iconClass]="'ifp-icon-rightarrow'"></ifp-button>
    </div>
  </div>

</div>

