
import { catchError, of, tap } from 'rxjs';
import { CommonApiService } from '../services/common-api/commonApi.service';
import { Injectable, signal } from '@angular/core';
import { ThemeService } from '../services/theme/theme.service';
import { CookieService } from '../services/cookie.service';


@Injectable({
  providedIn: 'root'
})
export class settingGuardService {

  public apiResponse = signal(false);

  constructor( private _commonApiService: CommonApiService, private _themeService: ThemeService,  private _cookie : CookieService) {
  }

  resolve() {
    const lang = this._cookie.getCookie('lang') !== '' ? this._cookie.getCookie('lang'): this._themeService.defaultLang;
    this._themeService.changeLanguage(lang);
    if (this.apiResponse()) {
      return of({status: true});
    }
    return this._commonApiService.getUserSettings().pipe(tap((res:any)=> {
      this.apiResponse.set(true);
      if (res) {
        this._themeService.setDefaultSettings(res);
      } else {
        const settings = {
          fontSize: 'md',
          theme: 'light',
          cursor: 'type1'
        };
        this._themeService.setDefaultSettings(settings);
      }
    }),
    catchError(()=> {
      this.apiResponse.set(true);
      return of({status: true});
    })
    );

  }


}
