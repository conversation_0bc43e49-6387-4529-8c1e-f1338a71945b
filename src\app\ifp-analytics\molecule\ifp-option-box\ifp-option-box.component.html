<!-- <div class="ifp-option-box" [ngClass]="{'ifp-option-box--border': showBorder}">
  <p class="ifp-adv-tool__sub-title">{{title | translate}}</p>
  <div class="ifp-option-box__outer">
    @for (opt of boxOptions; track opt.label) {
      <div class="ifp-option-box__opt-wrapper" [ngClass]="{'ifp-option-box__opt-wrapper--border': opt.isBorder}">
        <app-ifp-check-box class="ifp-option-box__opt-label" [label]="opt.label" [checkedData]="checkSelected(opt.id)"
        (checked)="setChecked($event, opt)"></app-ifp-check-box>
      </div>
    }
  </div>
</div> -->
<div class="ifp-option-box" [ngClass]="{'ifp-option-box--border': showBorder, 'ifp-option-box--header': isHeader,  'ifp-option-box--wrapper': isWrapper, 'ifp-option-box--progress' : reduceLabelWidth}">
  @if (isHeader && type === 'checkbox') {
    <div class="ifp-option-box__opt-wrapper">
      <app-ifp-check-box [disableTooltip]="true"  class="ifp-option-box__opt-label" [label]="'Select All'" [checkedData]="isSelectAll" (checked)="toggleSelectAll()"></app-ifp-check-box>
      @if(showNullPercentage){
      <span class="ifp-option-box__sub-title">Null Values</span>
      }
    </div>
  }
  @for (opt of boxOptions; track opt.label) {
    <div class="ifp-option-box__opt-wrapper" [ngClass]="{'ifp-option-box__opt-label--selected': opt.selected, 'ifp-option-box__opt-label--limit': limit<=selectedValueLength && !opt.selected}" >
      @if (type === 'checkbox') {
        <app-ifp-check-box [translation]="translation" [extraSpaceTop]="opt.extraSpaceTop ?? 20" [disableTooltip]="opt.toolTipDisable  == undefined ? true : opt.toolTipDisable" class="ifp-option-box__opt-label" [label]="opt.label" [checkedData]="opt.selected" (checked)="setChecked($event, opt)"
        ></app-ifp-check-box>
        @if(showNullPercentage){
        <ifp-progress-bar [width]="opt.null_percentage +'%'" [color]="opt.null_percentage && opt.null_percentage >= 50 ? color.red : color.green"></ifp-progress-bar>
        <span class="ifp-option-box__percentage">{{opt.null_percentage+'%'}}</span>
        }
      } @else {
        <app-ifp-check-box   [translation]="translation"  [extraSpaceTop]="opt.extraSpaceTop ?? 20" [disableTooltip]="opt.toolTipDisable  == undefined ? true : opt.toolTipDisable" class="ifp-option-box__opt-label" [type]="type" [label]="opt.label" [name]="name" [checkedData]="opt.selected" (checked)="setChecked($event, opt)"></app-ifp-check-box>
      }
    </div>
  }
</div>
