import { Component, HostListener, OnInit } from '@angular/core';
import { IfpCompareChartCardComponent } from '../../ifp-widgets/ifp-molecules/ifp-compare-chart-card/ifp-compare-chart-card.component';
import { ActivatedRoute, Router } from '@angular/router';
import { CommonModule, Location } from '@angular/common';
import { Store } from '@ngrx/store';
import { PagesService } from 'src/app/scad-insights/core/services/pages/pages.service';
import { cloneDeep } from 'lodash';
import { chartConstants } from 'src/app/scad-insights/core/constants/chart.constants';
import { loadcompareAppsView } from 'src/app/scad-insights/store/compare-apps/compare-apps.action';
import { selectCompareById } from 'src/app/scad-insights/store/compare-apps/compare-apps.selector';



@Component({
    selector: 'app-ifp-compare-chart',
    templateUrl: './ifp-compare-chart.component.html',
    styleUrls: ['./ifp-compare-chart.component.css'],
    imports: [IfpCompareChartCardComponent, CommonModule]
})
export class IfpCompareChartComponent implements OnInit {

  compareIds: any = [];
  contentType!: string;
  prevUrl: any;
  compareChartData: any = [];
  responseData: any = [];
  selectedPeriod: any = [];
  cloneResponse: any = [];
  public payLoad: any;
  public compareChartId: any;
  public title: string = 'Compared Indicators - Result';
  public domain!: string;

  @HostListener('window:beforeunload', ['$event'])
  unloadNotification($event: any): void {
    $event.returnValue = true;
  }

  constructor(private route: ActivatedRoute, private location: Location, private store: Store, private pageService: PagesService,
    private router: Router) {
    this.route.queryParams.subscribe(val => {
      if (val['compareIds']) {
        this.compareIds = JSON.parse(val['compareIds']);
      }
      if (val['compareChartId']) {
        this.compareChartId = val['compareChartId'];
        this.title = val['title'];
      }
      if (this.compareIds?.length > 0 && !this.compareChartId) {
        this.prevUrl = val['prevUrl'];
        this.location.go(this.prevUrl);
        if (this.compareIds?.length > 0) {
          if (this.compareIds.length) {
            this.callComapreApi();
          }
        }
      }
      if (this.compareChartId && this.compareIds?.length <= 0) {
        this.store.dispatch(loadcompareAppsView({ id: this.compareChartId }));
        this.store.select(selectCompareById(this.compareChartId)).subscribe(resp => {
          this.cloneResponse = cloneDeep(resp.data);
          this.responseData = resp.data;
        });
      }
      if (val['domain']) {
        this.domain = val['domain'];
      }
    });
  }

  callComapreApi() {
    this.payLoad = { nodes: [] };
    if (this.compareIds?.length > 0) {
      this.compareIds.forEach((element: { id: any, viewName: string }) => {
        const data = {
          indicatorId: element.id,
          viewName: element.viewName
        };
        this.payLoad.nodes.push(data);
      });

      this.pageService.compareIndicators(this.payLoad).subscribe(resp => {
        this.cloneResponse = cloneDeep(resp);
        this.responseData = resp;
        this.selectedPeriod = {
          id: '',
          unit: chartConstants.RECENT_LABEL
        };
      });
    }

  }

  ngOnInit(): void {
    // this.location.subscribe((_event) => {
    //   this.router.navigate(['/']);
    // });
  }
}




