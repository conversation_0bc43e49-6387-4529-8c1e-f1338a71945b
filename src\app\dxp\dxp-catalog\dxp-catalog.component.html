<div class="ifp-dxp-catalog">
  <div class="ifp-dxp-catalog__wrapper">
    @if (productListLoader) {
      <app-ifp-card-loader class="ifp-dxp-catalog__loader" [type]="loaderType.small"></app-ifp-card-loader>
    } @else {
      @for (card of cardList; track $index) {
        <ifp-dxp-card class="ifp-dxp-catalog__card" [id]="card.id" [title]="card.name" [subTitle]="card.organization.name" (cardClick)="onCLickCard($event)"></ifp-dxp-card>
      }  @empty {
        <app-ifp-no-data [showBorder]="true"></app-ifp-no-data>
      }
    }
  </div>
</div>
