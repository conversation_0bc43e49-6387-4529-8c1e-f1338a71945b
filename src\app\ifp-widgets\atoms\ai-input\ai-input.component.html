<div class="ifp-ai-input" [class]="theme()"  [ngClass]="{'ifp-ai-input--animation': animation(), 'ifp-ai-input--disable': disable()}">
  <div class="ifp-ai-input__outer-wrap"   [ngClass]="{'ifp-ai-input--animation': animation()}">
    <div class="ifp-ai-input__outer"  [ngClass]="{'ifp-ai-input--animation': animation()}">
      @if (input()) {
        <input [formControl]="inputValue" class="ifp-ai-input__inp"  (keydown)="submitClick($event)"/>
      }@else {
        <textarea [formControl]="inputValue"  class="ifp-ai-input__textarea" (keydown)="submitClick($event)" ></textarea>
      }

    </div>
  </div>
</div>
