import { NgClass } from '@angular/common';
import { Component, input, output } from '@angular/core';

@Component({
  selector: 'ifp-dxp-filter-badge',
  imports: [NgClass],
  templateUrl: './dxp-filter-badge.component.html',
  styleUrl: './dxp-filter-badge.component.scss',
})
export class DxpFilterBadgeComponent {
  public title = input('Filter 1');
  public column = input('Nationality');
  public comparator = input('matches');
  public value = input('Non national');
  public id = input<number>(0);
  public showRemove = input(false);
  public removeFilter = output<number>();
  public editFilter = output();
  public isSelected = input(false);

  onEditFilter() {
    this.editFilter.emit();
  }
}
