import { Toolbar, Tools} from '../../data-prep/ifp-data-prep/interface/ifp-data-prep.interface';
import { AfterViewInit, Component, ElementRef, EventEmitter, Input, OnInit, Output, ViewChild } from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';
import { AsyncPipe, NgClass } from '@angular/common';
import { IfpTooltipDirective } from 'src/app/scad-insights/core/directives/ifp-tooltip.directive';
import { IfpInfoComponent } from '../../../scad-insights/ifp-widgets/ifp-molecules/ifp-heading-with-info/ifp-info.component';
import { ThemeService } from 'src/app/scad-insights/core/services/theme/theme.service';

@Component({
    selector: 'ifp-prep-toolbar',
    imports: [TranslateModule, NgClass, IfpTooltipDirective, IfpInfoComponent, AsyncPipe],
    templateUrl: './ifp-prep-toolbar.component.html',
    styleUrl: './ifp-prep-toolbar.component.scss'
})
export class IfpPrepToolbarComponent implements AfterViewInit, OnInit{

  @ViewChild('toolBar') toolBar!: ElementRef;

  @Output() toolSelected: EventEmitter<Tools> = new EventEmitter<Tools>();
  @Output() toolDragged: EventEmitter<any> = new EventEmitter<any>();
  @Output() collapsedEvent: EventEmitter<any> = new EventEmitter<any>();

  @Input() isDisabled: boolean | null = false;
  @Input() tools: Toolbar[] = [];
  @Input() selectedTool!: Tools;
  @Input() isDragable: boolean = false;
  @Input() isAccordian: boolean = false;
  @Input() changeToolBar: boolean = false;

  public isCollapsed = window.innerWidth < 1280;
  public search: string = '';
  public autoClose: boolean = false;
  public mobileItemSelect: string = '';
  public isAccordianExpanded: boolean = false;
  public searchData: Toolbar[]= [];


  constructor(public _themeService : ThemeService) {
  }

  ngOnInit(): void {
    this.searchData =  this.tools;
  }

  ngAfterViewInit() {
    // this.selectedTool = this.tools[0];
  }

  get getElement(): ElementRef {
    return this.toolBar;
  }

  collapsed(){
    this.isCollapsed = !this.isCollapsed;
    this.collapsedEvent.emit(this.isCollapsed);
  }

  searchEvent(key: string) {
    this.searchData = [];
    if (key == '') {
      this.searchData =this.tools;
    } else {
      this.tools.forEach((dataTool: Toolbar) => {
        let included  = true;
        dataTool.menu.forEach(tools => {
          if ((tools.name?.toLowerCase())?.includes(key.toLowerCase())) {
            if (included) {
              this.searchData.push( {key: dataTool.key,
                icon: dataTool.icon,
                color: dataTool.color,
                isSelected: dataTool.isSelected,
                menu: []});
            }
            if (this.searchData.length !== 0) {
              this.searchData[this.searchData.length - 1].menu.push(tools);
            }
            included = false;
          }
        });
      });
    }

  }

  onToolSelected(tool: Tools) {
    if ( this.isDisabled && tool.enabled !== true) {
      return;
    }
    this.selectedTool = tool;
    this.toolSelected.emit(tool);
  }

  drag(ev: any) {
    if (ev.type === 'touchstart') {
      this.mobileItemSelect = ev.target?.closest('.drag-drawflow')?.getAttribute('data-node');
      this.toolDragged.emit(this.mobileItemSelect);
    } else {
      ev.dataTransfer.setData('node', ev.target.getAttribute('data-node'));
    }
  }

  getToolsLength() {
    return Object.keys(this.tools).length;
  }
}
