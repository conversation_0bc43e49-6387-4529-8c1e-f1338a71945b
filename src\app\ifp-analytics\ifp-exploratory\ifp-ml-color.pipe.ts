
import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
  name: 'ifpMlColor',
  standalone: true
})
export class IfpMlColorPipe implements PipeTransform {

  transform(value: string | number, percentage?: boolean, valueType?:string) {
    const toNumber = percentage ? +value : ((+value +1)/2 ) * 100;
    let colors =  {background: '#FFFFFF', border: '1px solid #D1D5DA', color: '#1E2937'};
    if (toNumber > 83.33) {
      colors  = {background: '#6AA85E', border: 'unset', color: '#FFFFFF'};
    } else if (toNumber > 66.66) {
      colors  = {background: '#9ED095', border: 'unset', color: '#1E2937' };
    } else if (toNumber > 49.99) {
      colors  =  {background: '#BEE0B8', border: 'unset', color: '#1E2937'};
    } else if (toNumber > 33.33) {
      colors  =  {background: '#D2EACE', border: 'unset', color: '#1E2937'};
    } else if (toNumber > 16.66) {
      colors  = {background: '#DFEFDC', border: 'unset', color: '#1E2937' };
    }
    switch (valueType) {
    case 'border':
      return colors.border;
    case 'color':
      return colors.color;
    default:
      return colors.background;
    }
  }

}
