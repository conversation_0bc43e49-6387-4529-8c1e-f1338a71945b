@use "../../../../assets/ifp-styles/abstracts/index" as *;
.ifp-save-modal {
  padding: $spacer-0;
  max-width: 600px;
  margin: 0 auto;
  &__body,
  &__footer {
    padding: $spacer-5;
  }
  &__header {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  &__title {
    font-size: $ifp-fs-9;
    font-weight: $fw-bold;
    margin-bottom: $spacer-2;
  }
  &__desc {
    font-size: $ifp-fs-4;
    margin-bottom: $spacer-4;
  }
  &__label {
    font-size: $ifp-fs-4;
    color: $ifp-color-tertiary-text;
    margin-bottom: $spacer-2;
  }
  &__input {
    border: 1px solid $ifp-color-grey-7;
    padding: ($spacer-3 - 4px) $spacer-3;
    border-radius: 7px;
    display: block;
    width: 100%;
    @include placeholder($ifp-color-tertiary-text)
  }
  &__footer {
    display: flex;
    border-top: 1px solid $ifp-color-grey-7;
  }
  &__footer-btn {
    margin-right: $spacer-4;
  }
  &__close-btn {
    cursor: pointer;
    transition: 0.3s;
    :hover {
      opacity: 0.8;
    }
  }
  &__radio {
    margin-top: $spacer-3;
  }
  &__radio-text {
    font-size: $ifp-fs-4;
    margin-bottom: $spacer-3;
  }
  &__radio-outer {
    display: flex;
  }
  &__last-radio {
    margin-left: $spacer-4;
  }
}

:host-context([dir="rtl"]) {
  .ifp-save-modal {
    &__footer-btn {
      margin-left: $spacer-4;
      margin-right: $spacer-0;
    }
  }
}
