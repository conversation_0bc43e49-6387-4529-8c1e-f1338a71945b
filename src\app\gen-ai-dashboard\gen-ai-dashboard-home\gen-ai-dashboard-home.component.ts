import { AsyncPipe } from '@angular/common';
import { Component, inject, signal, viewChild } from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';
import { ThemeService } from 'src/app/scad-insights/core/services/theme/theme.service';
import { IfpTabComponent } from '../../scad-insights/ifp-widgets/ifp-molecules/ifp-tab/ifp-tab.component';
import { LabelData } from 'src/app/scad-insights/core/interface/atom/ifp-category-label.interface';
import { GenAiDashboardUploadComponent } from '../gen-ai-dashboard-upload/gen-ai-dashboard-upload.component';
import { DropdownInterfaceGenAi, GenAiDashboardPreviewComponent } from '../gen-ai-dashboard-preview/gen-ai-dashboard-preview.component';
import { GenAiDashboardLandingPageComponent } from '../gen-ai-dashboard-landing-page/gen-ai-dashboard-landing-page.component';
import { IfpButtonComponent } from '../../scad-insights/ifp-widgets/ifp-atoms/ifp-button/ifp-button.component';
import { buttonClass } from 'src/app/scad-insights/core/constants/button.constants';
import { GenAiDashboardHistoryComponent } from "../gen-ai-dashboard-history/gen-ai-dashboard-history.component";
import { SubSink } from 'subsink';
import { ApiGenAiService } from 'src/app/scad-insights/core/services/api-gen-ai.service';
import { genAiTestingApi } from '../constents/gen-ai-testing.constant';
import { IfpSpinnerComponent } from "../../scad-insights/ifp-widgets/ifp-molecules/ifp-spinner/ifp-spinner.component";
import { PanelDropdownOptions } from 'src/app/ifp-analytics/molecule/ifp-panel-dropdown/ifp-panel-dropdown.component';

@Component({
  selector: 'ifp-gen-ai-dashboard-home',
  standalone: true,
  imports: [AsyncPipe, TranslateModule, IfpTabComponent,
    GenAiDashboardUploadComponent, GenAiDashboardPreviewComponent,
    GenAiDashboardLandingPageComponent, IfpButtonComponent, GenAiDashboardHistoryComponent, IfpSpinnerComponent],
  templateUrl: './gen-ai-dashboard-home.component.html',
  styleUrl: './gen-ai-dashboard-home.component.scss'
})
export class GenAiDashboardHomeComponent{
  public previewId = viewChild<GenAiDashboardPreviewComponent>('preview');
  public subsink= new SubSink();
  public enablePreview = signal(false);
  public historyTable = signal(false);
  public runid = signal('');
  public _apiService = inject(ApiGenAiService);
  public modelName = signal('');
  constructor( public _themeService : ThemeService) {

  }


  public viewsTab: LabelData[] = [
    {
      iconClass: 'ifp-icon-upload',
      name: 'Upload Questionnaire',
      badge: false
    },
    {
      iconClass: 'ifp-icon-document',
      name: 'Create your Questionnaire',
      badge: false
    }
  ];

  public buttonClass = buttonClass;
  public modalDropdown = signal<PanelDropdownOptions[]| undefined>(undefined);

  public qus = signal(false);
  public dataRunEvent = signal(false);
  public selectedTab = signal(0);
  tabClick(event: {event: LabelData, index: number}) {
    this.selectedTab.set(event.index);
  }

  ngOnInit(): void {
    this.callDropdownApi('ai_model');
  }

  goBackEventHistory() {
    this.runid.set('');
    this.enablePreview.set(false);
    this.qus.set(false);
    this.dataRunEvent.set(false);

  }
  fileRemoved() {
    this.runid.set('');
  }
  openHistory() {
    this.historyTable.set(true);
  }

  openPreivew(event: string) {
    this.enablePreview.set(true);
    this.qus.set(false);
    this.runid.set(event);
    this.selectedTab.set(0);

    this.closeHistory();
  }


  callDropdownApi(name:string) {
    this.subsink.add(this._apiService.getMethodRequest(genAiTestingApi.dropdownType+name).subscribe((data:DropdownInterfaceGenAi) => {
      const modelValues:PanelDropdownOptions[]= [];
      data.options.forEach(element => {
        modelValues.push({
            key: element.value,
  value: element.display_name,
  checked: false,
        })
      });
      this.modalDropdown.set(modelValues);
      this.modelName.set(data.options[0].value);
    }));
  }
  closeHistory() {
    this.historyTable.set(false);
  }
}
