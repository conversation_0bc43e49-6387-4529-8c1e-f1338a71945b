@use "../../../../assets/ifp-styles/abstracts/index" as *;

.ifp-prep-help {
  width: 100%;
  max-width: 1050px;
  text-align: center;
  margin-bottom: $spacer-4;
  &__heading,
  &__content-title {
    font-weight: $fw-bold;
  }
  &__heading {
    font-size: $ifp-fs-9;
    margin-bottom: $spacer-5;
  }
  &__content {
    background-color: $ifp-color-section-white;
    border-radius: 20px;
  }
  &__content-header,
  &__content-body {
    position: relative;
    padding: $spacer-5;
  }
  &__content-body {
    display: flex;
    align-items: center;
  }

  &__content-header {
    border-bottom: 1px solid $ifp-color-grey-7;
  }
  &__content-title {
    font-size: $ifp-fs-7;
    margin-bottom: $spacer-2;
  }
  &__content-desc {
    font-size: $ifp-fs-3;
    color: $ifp-color-grey-9;
    margin-bottom: $spacer-2;
    color:  $ifp-color-black;
  }
  &__content-heading {
    font-size: $ifp-fs-6;
    font-weight:$fw-bold;
    margin-bottom: $spacer-2;
  }
  &__content-sub-title {
    font-size: $ifp-fs-3;
    font-weight:$fw-semi-bold;
    padding-bottom: $spacer-2;
    position: sticky;
    top: 0;
    left: 0;
    background-color: $ifp-color-section-white;
  }
  &__content-sub-desc {
    font-size: $ifp-fs-3;
    color: $ifp-color-black;

  }
  &__content-wrapper {
    padding-inline-start: $spacer-5 ;
    width: 50%;
    text-align: left;
  }
  &__content-sub {
    padding: $spacer-3 $spacer-0 ;
    border-bottom: 1px solid $ifp-color-grey-7;
  }
  &__header-icon {
    position: absolute;
    right: 20px;
    top: 20px;
    color:$ifp-color-grey-6 ;
    cursor: pointer;
  }
  &__footer {
    padding: $spacer-3  $spacer-4;
    display: flex;
    justify-content: space-between;
    border-top: 1px solid $ifp-color-grey-7;
  }
  &__carousel {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 20px 0;
    gap: 1rem;
  }
  &__dummy {
    width: 100px;
  }
  &__image{
    background-size: cover; /* Ensure the image covers the div */
    background-position: top right; /* Center the image in the div */
    background-repeat: no-repeat; /* Prevent the image from repeating */
    height: 100%;
    width: 100%;
    min-height: 450px;
  }
  &__image-wrapeper {
    width: 50%;
  }
  &__carousel-dot {
    border: 0;
    border-radius: 50%;
    width: 5px;
    height: 5px;
    background-color: $ifp-color-grey-8;
    cursor: pointer;
    &--active {
      width: 8px;
      height: 8px;
      background-color: $ifp-color-blue-menu;
    }
  }
  &__desc-item {
    padding: $spacer-3 $spacer-0;
    border-bottom: 1px solid $ifp-color-grey-7;
  }
  &__content-inner {
    max-height: 370px;
    @include ifp-scroll-y(transparent, $ifp-color-grey-1, 8px, 8px);
    padding-right: $spacer-2;
  }
}

:host-context(.ifp-dark-theme) {
  .ifp-prep-help {
    &__content-sub-desc {
      color: $ifp-color-white-global;
    }
    &__content-desc {
      color:  $ifp-color-white-global;
    }
  }

}
