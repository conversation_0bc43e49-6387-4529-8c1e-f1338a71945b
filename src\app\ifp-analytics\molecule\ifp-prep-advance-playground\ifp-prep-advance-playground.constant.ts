export const previewData = {
  'name': 'workflow three',
  'description': 'Workflow',
  'nodes': [
    {
      'object_id': 'bafc1f9e-55be-40a7-9301-5fcb5c951736',
      'name': 'Data uploaded successfully',
      'type': 'InputTool',
      'time': '2024-07-26T12:05:35.696Z',
      'settings': {
        'pos_x': 204.75,
        'pos_y': 186.7578125,
        'selectedNodeMainIndex': 0
      },
      'configuration': {
        'connection': {
          'connection_type': 'dataset',
          'path': '1d887ede-6d56-4608-934e-9d3a230eec4d'
        }
      }
    },
    {
      'object_id': 'd168eb24-ec20-4060-b19b-4b9090c7591a',
      'name': 'Data uploaded successfully',
      'type': 'InputTool',
      'time': '2024-07-26T12:05:38.390Z',
      'settings': {
        'pos_x': 138.75,
        'pos_y': 383.7578125,
        'selectedNodeMainIndex': 0
      },
      'configuration': {
        'connection': {
          'connection_type': 'dataset',
          'path': '1d887ede-6d56-4608-934e-9d3a230eec4d'
        }
      }
    },
    {
      'object_id': 'b57385e1-bf3d-4be6-a1cf-67585535643a',
      'name': 'join',
      'type': 'JoinTool',
      'time': '2024-07-26T12:05:39.965Z',
      'configuration': {
        'join_by': {}
      },
      'settings': {
        'pos_x': 333.75,
        'pos_y': 279.7578125,
        'selectedNodeMainIndex': 2
      }
    },
    {
      'object_id': 'af092c64-a770-4100-8395-dac5fec50ae8',
      'name': 'append',
      'type': 'UnionTool',
      'time': '2024-07-26T12:05:46.801Z',
      'configuration': {},
      'settings': {
        'pos_x': 521.75,
        'pos_y': 293.7578125,
        'selectedNodeMainIndex': 2
      }
    }
  ],
  'connections': [
    {
      'source': 'd168eb24-ec20-4060-b19b-4b9090c7591a',
      'source_anchor': 1,
      'destination': 'b57385e1-bf3d-4be6-a1cf-67585535643a',
      'destination_anchor': 1
    },
    {
      'source': 'bafc1f9e-55be-40a7-9301-5fcb5c951736',
      'source_anchor': 1,
      'destination': 'b57385e1-bf3d-4be6-a1cf-67585535643a',
      'destination_anchor': 2
    },
    {
      'source': 'b57385e1-bf3d-4be6-a1cf-67585535643a',
      'source_anchor': 2,
      'destination': 'af092c64-a770-4100-8395-dac5fec50ae8',
      'destination_anchor': 2
    }
  ]
};

export const calculationConfig = {
  newColumn: 'newColumn',
  existingColumn: 'existingColumn'
};
