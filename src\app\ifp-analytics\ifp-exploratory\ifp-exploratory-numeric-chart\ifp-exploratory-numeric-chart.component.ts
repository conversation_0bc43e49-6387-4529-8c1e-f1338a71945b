import { ChangeDetectorRef, Component, Input, OnChanges, signal } from '@angular/core';
import { SubscriptionLike } from 'rxjs';
import { StreamingService } from 'src/app/scad-insights/core/services/http/streaming.service';
import { environment } from 'src/environments/environment';
import { autoMlApiEndpoints } from '../constants/auto-ml.constant';
import { IFPHighChartsComponent } from '../../../scad-insights/ifp-widgets/charts/ifp-highcharts.component';
import { IfpSpinnerComponent } from '../../../scad-insights/ifp-widgets/ifp-molecules/ifp-spinner/ifp-spinner.component';

@Component({
    selector: 'ifp-exploratory-numeric-chart',
    imports: [IFPHighChartsComponent, IfpSpinnerComponent],
    templateUrl: './ifp-exploratory-numeric-chart.component.html',
    styleUrl: './ifp-exploratory-numeric-chart.component.scss'
})
export class IfpExploratoryNumericChartComponent implements  OnChanges{

  @Input() objectId!: string;
  @Input() xAxis?: string;
  @Input() yAxis?: string;

  public controller!: AbortController;
  public summeryStreemSubscription!: SubscriptionLike;
  public dataStreamed: Record<string, number>[] = [];
  public data: {
    name: string;
    color: string;
    data: number[][];
}[] =  [{
      name: 'Dataset 1',
      color: '#3bd6ad',
      data: [
      ]
    }];

  public loaded = signal(false);

  constructor(private _streamService: StreamingService, private _cdr: ChangeDetectorRef) {}


  ngOnChanges(): void {
    if (this.xAxis && this.yAxis) {
      this.callStreaming();
    }
  }

  // used call stream for the scatter plot
  callStreaming() {
    this.loaded.set(false);
    this.dataStreamed = [];
    this.data[0].data = [];
    const chartData:number[][] = [];
    this.summeryStreemSubscription = this._streamService.connectToServerSentEvents(`${environment.prepbaseUrl}/v1${autoMlApiEndpoints.dataset}${this.objectId}${autoMlApiEndpoints.streamData}?filter=[${this.xAxis},${this.yAxis}]`)
      .subscribe({
        next: (data: { data: string, ctrl: AbortController }) => {
          const dataValue = JSON.parse(data.data);
          if (dataValue.data && Array.isArray(dataValue.data)) {
            dataValue.data.forEach((element: Record<string, number>) => {
              this.dataStreamed.push(element); if (this.xAxis && this.yAxis) {
                chartData.push([element[this.xAxis], element[this.yAxis]]);
              }
            });
          }
          this._cdr.detectChanges();
          this.controller = data.ctrl;
          if ('completed' == dataValue.status) {
            data.ctrl.abort();
            this.data = [{
              name: 'Dataset 1',
              color: '#3bd6ad',
              data: chartData
            }];
            this.loaded.set(true);
          }
        }
      }
      );
  }


}
