@use "../../../../assets/ifp-styles/abstracts/index" as *;
.ifp-panel-dropdown {
  // display: flex;
  align-items: center;
  position: relative;
  z-index: 199;
  &__icon {
    margin-inline-end: $spacer-3;
    font-size: $ifp-fs-6;
  }
  &__select-box {
    padding: $spacer-2;
    border: 1px solid $ifp-color-grey-7;
    border-radius: 7px;
    .ifp-icon {
      padding-inline-start: $spacer-2;
      border-inline-start: 1px solid $ifp-color-grey-7;
      margin-inline-start: $spacer-1;
      cursor: pointer;
    }
  }
  &__selected {
    font-size: $ifp-fs-4;
    display: inline-block;
    min-width: 13px;
    cursor: pointer;
  }
  &__select-all {
    padding: $spacer-0 $spacer-3;
  }
  &__list-wrapper {
    position: absolute;
    visibility: hidden;
    opacity: 0;
    top: 100%;
    right: 0;
    background-color: $ifp-color-grey-4;
    box-shadow: 0px 0px 17px 14px $ifp-color-black-16;
    border-radius: 10px;
    margin-top: $spacer-2;
  }
  &__with-value {
    padding: $spacer-2 $spacer-3;
    border: 1px solid $ifp-color-grey-7;
    border-radius: 7px;
    color: $ifp-color-grey-9;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    .ifp-icon {
      margin-inline-start: $spacer-2;
      color: $ifp-color-black-global;
    }
  }
  &__list {

    transition: 0.3s;
    min-width: 200px;
    max-height: 300px;
    padding: $spacer-2 $spacer-0;
    visibility: hidden;
    opacity: 0;
    @include ifp-scroll-y(transparent, $ifp-color-grey-1, 4px, 8px);
  }
  &__list-text {
    padding: $spacer-2 $spacer-3;
    display: block;
    transition: 0.3s;
    &:hover {
      color: $ifp-color-white;
      background-color: $ifp-color-blue-hover;
    }
    &--selected {
      background-color: $ifp-color-grey-4;
    }
    &--selected-single{
      background-color: $ifp-color-secondary-blue;
      color: $ifp-color-white;
    }
  }
  &__select-icon{
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: $ifp-color-grey-13;
    margin: $spacer-0 $spacer-2;
  }
  &__search-input {
    width: calc(100% - 20px);
    color: $ifp-color-black;
    background-color: transparent;
  }

  &__search-icon {
    width: 20px;
  }

  &__search-box {
    align-items: center;
    padding: $spacer-2 $spacer-3;
    display: flex;
    justify-content: center;
  }
  &__selected-count {
    font-size: $ifp-fs-2;
    display: inline-block;
    width: 30px;
    min-width: 30px;
    height: 30px;
    line-height: 30px;
    border-radius: 50%;
    text-align: center;
    background-color: $ifp-color-green;
    color: $ifp-color-white-global;
    margin-inline-start: auto;
  }
  &__list-item {
    &--disabled {
      background-color: $ifp-color-grey-disabled;
      cursor: not-allowed;
      pointer-events: none;
    }
  }
  &__label {
    color: $ifp-color-grey-14;
    font-size: $ifp-fs-4;
    margin-bottom: $spacer-1;
    margin-inline-start: $spacer-2;
  }
  &__user {
    display: flex;
    align-items: center;
    margin: $spacer-0 $spacer-3;
    border-top: 1px solid $ifp-color-grey-7;
    padding: $spacer-2 $spacer-0;
    cursor: pointer;
  }
  &--show {
    .ifp-panel-dropdown {
      &__select-box {
        border: 1px solid $ifp-color-blue-hover;
      }
      &__list {
        opacity: 1;
        visibility: visible;
      }
      &__list-wrapper  {
        opacity: 1;
        visibility: visible;
      }
    }
  }
  &--box {
    .ifp-panel-dropdown {
      &__select-box {
        padding: $spacer-2 $spacer-3;
        background-color: $ifp-color-white;
        display: flex;
        align-items: center;
        .ifp-icon {
          padding-inline-start: $spacer-0;
          border-inline-start: none;
          position: relative;
          top: 2px;
        }
      }
      &__selected {
        width: 100%;
        margin-inline-end: $spacer-1;
      }
      &__list-wrapper {
        left: 0;
        right: auto;
        width: 100%;
      }
    }
    &.ifp-panel-dropdown--show {
      .ifp-panel-dropdown__select-box {
        border: 1px solid $ifp-color-grey-7;
      }
    }
  }
  &--userDropdown {
    .ifp-panel-dropdown{
      &__search-box {

      margin: $spacer-3;
      border: 1px solid $ifp-color-grey-7;
      border-radius: 10px;
    }
      &__list-wrapper ,&__list-text--selected{
        background-color: $ifp-color-white;
      }
      &__list-item--disabled {
             background-color: $ifp-color-grey-disabled;
      }
  }
  }
}
:host::ng-deep {
  .ifp-panel-dropdown__user-tag{
    width: 100%;
    .ifp-name-tag {
      border:0px;
      padding:$spacer-0;
    }
    .ifp-abbr-tag{
      font-size:$ifp-fs-2 ;
      width: 30px;
      height: 30px;
      line-height: 30px;
    }
  }
}
