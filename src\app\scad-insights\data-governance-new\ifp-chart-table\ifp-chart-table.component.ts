import { AfterViewInit, ChangeDetectionStrategy, Component, ElementRef, EventEmitter, inject, input, Input, InputSignal, output, Output, Renderer2, Signal, viewChild } from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';
import { IfpPieChartComponent } from "../../ifp-widgets/ifp-molecules/ifp-pie-chart/ifp-pie-chart.component";
import { DataTableSortEvent, IfpDataTableComponent } from "../../../ifp-analytics/organism/ifp-data-table/ifp-data-table.component";
import { NgClass, TitleCasePipe } from '@angular/common';

@Component({
  selector: 'ifp-chart-table',
  imports: [TranslateModule, IfpPieChartComponent, IfpDataTableComponent, TitleCasePipe, NgClass],
  templateUrl: './ifp-chart-table.component.html',
  styleUrl: './ifp-chart-table.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class IfpChartTableComponent implements AfterViewInit {

  private readonly _table:Signal<ElementRef | undefined> = viewChild('table');
  // private readonly _renderer: Renderer2 = inject(Renderer2);

  @Input({ required: true }) public chartData: any;
  @Input({ required: true }) public tableData: any;
  @Input({ required: true }) public tableHead: any;
  @Input() public headerSettings: any;
  @Input() public selectedStatus!:string;

  public heading:InputSignal<string> = input('');
  public selectedStatusIndex: number = -1;

  @Output() detailClicked = new EventEmitter();
  @Output() tabClicked = new EventEmitter<string>();

  public sortColumn = output<DataTableSortEvent>();
  public scrollBottom = output<void>();

  ngAfterViewInit(): void {
    const table = this._table()?.nativeElement;
    console.log(table);

    // this._renderer.listen(table, 'scroll', () => {
    //   if (table.scrollTop === (table.scrollHeight - table.offsetHeight)) {
    //     console.log('bottom reached');
    //     this.scrollBottom.emit();
    //   }
    // })
  }

  goToDetal() {
    this.detailClicked.emit(true);
  }

  tabDetail(name:string, index: number) {
    this.selectedStatusIndex = index;
    this.tabClicked.emit(name);
  }

  onSortColumn(sort: DataTableSortEvent) {
    this.sortColumn.emit(sort);
  }
}
