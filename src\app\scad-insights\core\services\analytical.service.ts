import { Injectable } from '@angular/core';
import { cloneDeep, groupBy } from 'lodash';
import { ifpColors } from '../constants/color.constants';
import { chartConstants } from '../constants/chart.constants';
import { TranslateService } from '@ngx-translate/core';
import { DashboardService } from './create-dashboard/dashboard.service';
import { DatePipe, TitleCasePipe } from '@angular/common';

@Injectable({
  providedIn: 'root'
})
export class AnalyticalService {

  public tableData: any = [];
  public forecast = `-${this._translateServivce.instant('forecast')}`;

  constructor(private _translateServivce: TranslateService, private _dashboardService: DashboardService, private _datePipe:DatePipe,
    private _titleCase:TitleCasePipe
  ) { }

  // formating rating value ;


  // chart line data creation start ;
  creatingLineChartData(data: any, analyticalId: string, cntType: string = chartConstants.ANALYTICAL_APPS, actualValue: any = {}, isYearly:boolean=false) {
    const chartData: any = [];
    if (data?.length > 0) {
      const groupedData = groupBy(data, item => item?.seriesidentifier ? item?.seriesidentifier : item?.id?.replace('-forecast', ''));
      for (const key of Object.keys(groupedData)) {
        groupedData[key].forEach((element: any) => {
          chartData.push(...this.createChartSeries(element, Object.keys(groupedData).indexOf(key), analyticalId, cntType, actualValue, isYearly));
        });
      }
    }
    return chartData;
  }

  createChartSeries(cnt: any, index: number, analyticalId: string, cntType: string, actualValue: any = {}, isYearly: boolean = false) {
    this.forecast = `-${this._translateServivce.instant('forecast')}`;
    const allSeriesData: any = [];
    const graphData: any = [];
    const actualValues: number[] = [];
    const monthValues: number[] = [];
    const yearValues: number[] = [];
    let forcastLineData: any;
    const forcastLineGraphData: any = [];
    if (cnt?.data && cnt?.data?.length > 0) {
      cnt.data.forEach((element: Record<string, any>) => {
        if (isYearly) {
          const year:any = this._datePipe.transform(element['OBS_DT'], 'yyyy');
          element['OBS_DT'] = `${year}-01-01`;
        }
        const splitDate = element['OBS_DT'].split('-');
        cnt.id.includes('forecast') ? graphData.push([(Date.UTC(splitDate[0], splitDate[1] - 1, 1)), element['VALUE_LL'] ? element['VALUE_LL'] : '', element['VALUE_UL'] ? element['VALUE_UL'] : '']) : graphData.push([(Date.UTC(splitDate[0], splitDate[1] - 1, 1)), element['VALUE']]);
        forcastLineGraphData.push([(Date.UTC(splitDate[0], splitDate[1] - 1, 1)), element['VALUE']]);
        if (actualValue?.path) {
          actualValues.push(element[actualValue.path]);
        }
        monthValues.push(element['CHANGE_QQ']);
        yearValues.push(element['CHANGE_YY']);
      });
    }
    const seriesData: any = {
      name: cnt.seriesidentifier ? cnt.seriesidentifier : cnt.label,
      data: graphData,
      actualValue: actualValues,
      monthValues: monthValues,
      yearValues: yearValues,
      dashStyle: 'Solid',
      type: cnt?.id?.includes('forecast') ? 'arearange' : 'line',
      zIndex: 1,
      identifier: cnt.seriesidentifier ? cnt?.seriesidentifier?.replace('-', '') : cnt?.id?.replace('-forecast', ''),
      lineWidth: 3,
      color: this._dashboardService.getColors(cntType, analyticalId, index),
      marker: {
        fillColor: this._dashboardService.getColors(cntType, analyticalId, index),
        lineWidth: 3,
        lineColor: this._dashboardService.getColors(cntType, analyticalId, index),
        symbol: 'circle',
        enabled: true
      }
    };
    if (cnt?.id?.includes('forecast')) {
      // forcast arerange chart data ;
      seriesData.zones = [{ value: 0, color: '#ffffff' }, { color: this._dashboardService.getColors(cntType, analyticalId, index) }],
      seriesData.zoneAxis = 'x';
      seriesData.lineWidth = 0;
      seriesData.dashStyle = 'Dash',
      seriesData.identifier = cnt.seriesidentifier ? cnt.seriesidentifier.replace('-', '') : cnt.id.replace('-forecast', ''),
      seriesData.linkedTo = ':previous',
      seriesData.fillOpacity = this.checkOpacity(graphData) ? 0.3 : 0,
      seriesData.zIndex = 0;
      seriesData.marker = { enabled: true, symbol: 'square' };
      // seriesData.dataLabels = { enabled: true };
      // forcast line chart data ;
      forcastLineData = {
        name: `${cnt.seriesidentifier ? cnt.seriesidentifier : cnt.label} ${this.forecast}`,
        data: forcastLineGraphData,
        monthValues: monthValues,
        yearValues: yearValues,
        actualValue: actualValues,
        type: 'line',
        dashStyle: 'Dash',
        identifier: cnt.seriesidentifier ? cnt.seriesidentifier.replace('-', '') : cnt.id.replace('-forecast', ''),
        zIndex: 1,
        lineWidth: 3,
        color: this._dashboardService.getColors(cntType, analyticalId, index),
        marker: {
          enabled: true,
          fillColor: this._dashboardService.getColors(cntType, analyticalId, index),
          lineWidth: 3,
          lineColor: this._dashboardService.getColors(cntType, analyticalId, index),
          symbol: 'square'
        }
      };
      allSeriesData.push(forcastLineData);
    }

    allSeriesData.push(seriesData);
    return allSeriesData;
  }

  getColor(index: number) {
    let color: any;
    switch (index) {
    case 0:
      color = ifpColors.green;
      break;
    case 1:
      color = ifpColors.blue;
      break;
    case 2:
      color = ifpColors.lightBlue;
      break;
    default:
      color = ifpColors.skyBlue;
      break;
    }
    return color;
  }

  // creating tree chart data;

  creatingTreeChartData(data: any, analyticalId: string = '', isDashboard: boolean = false) {
    this.tableData = [];
    const treeData: any = [];
    if (data.length > 0) {
      data.forEach((element: { label: any; data: any[]; color: any; }, index: number) => {
        const treeCnt = {
          name: element.label,
          value: element.data[0]?.VALUE_PERC_ECO,
          color: !isDashboard ? element.color : (this._dashboardService.getTreeMapColors(chartConstants.ANALYTICAL_APPS, analyticalId, index, element.color))
        };
        treeData.push(treeCnt);
        this.tableData.push(...element.data);
      });
    }
    this.tableData = [this.tableData];
    return treeData;
  }


  checkOpacity(data: any) {
    let opacity = true;
    if (data.flatMap((innerArr: any[]) => innerArr.filter((_, index) => (index + 1) % 3 === 0))
      .every((value: number) => value === 0)) {
      opacity = false;
    }
    return opacity;
  }

  // chart data creation end ;

  // period filter start;
  analyticPeriodFilter(data: any, filter: any, isReverse: boolean = false) {
    this.tableData = [];
    let chartData: any = [];
    if (data?.length > 0 && filter?.unit != chartConstants.RECENT_LABEL) {
      data.forEach((element: any) => {
        let filterDate: any;
        filterDate = filter.unit == 'years' ? new Date(element.xMax).setFullYear(new Date(element.xMax).getFullYear() - filter.value) : new Date(element.xMax).setMonth(new Date(element.xMax).getMonth() - filter.value);
        if (!element.id.includes('-forecast')) {
          chartData.push(this.filterAnalyticData(element, filterDate));
        } else {
          chartData.push(element);
        }
      });
    }
    if (data?.length > 0 && filter?.unit == chartConstants.RECENT_LABEL) {
      const sliceCount: number = 12;
      data.forEach((element: { data: any[]; id: string }) => {
        if (!element.id.includes('-forecast')) {
          // if (isReverse) {
          //   element.data.sort((a, b) => new Date(b.OBS_DT).getTime() - new Date(a.OBS_DT).getTime());
          // }
          element.data = cloneDeep(element.data.slice(-sliceCount));
          if (isReverse) {
            element.data = element.data.reverse();
          }
        }
      });
      chartData = data;
    }

    if (chartData?.length > 0) {
      // eslint-disable-next-line @typescript-eslint/prefer-for-of
      for (let index = 0; index < chartData.length; index++) {
        this.tableData.push([...chartData[index].data]);
      }
    }

    return chartData;
  }

  filterAnalyticData(data: any, filterDate: any) {
    const filterData = cloneDeep(data);
    filterData.data = [...filterData.data.filter((obj: { OBS_DT: any; }) => new Date(obj.OBS_DT) >= new Date(filterDate))];
    return filterData;
  }

  // period filter end;

  // Filter api call;
  getBoarderColor(value: number) {
    let color: any;
    if (value > 4.5 && value <= 5) {
      color = '#1a9641';
    }
    if (value > 4 && value <= 4.5) {
      color = '#a6d96a';
    }
    if (value > 3.5 && value <= 4) {
      color = '#fee08b';
    }
    if (value >= 3 && value <= 3.5) {
      color = '#fdae61';
    }
    if (value < 3) {
      color = '#d7191c';
    }
    return color;
  }

  convertUtcToDate(data: any) {
    const dates: any = [];
    let date: any;
    if (data?.length > 0) {
      for (const element of data) {
        const convertedDate = this._datePipe.transform(element[0], 'yyyy-MM-dd');
        date = this.convertDateToQuarter(convertedDate);
        dates.push(date);
      }
    }
    return dates;
  }

  convertDateToQuarter(date: any) {
    let label: string = '';
    if ((new Date(date).getMonth() + 1) <= 3 && (new Date(date).getMonth() + 1) > 0) {
      label = `Q1<br>${this._datePipe.transform(new Date(date), 'yyyy')}`;
    } else if ((new Date(date).getMonth() + 1) > 3 && (new Date(date).getMonth() + 1) <= 6) {
      label = `Q2<br>${this._datePipe.transform(new Date(date), 'yyyy')}`;
    } else if ((new Date(date).getMonth() + 1) > 6 && (new Date(date).getMonth() + 1) <= 9) {
      label = `Q3<br>${this._datePipe.transform(new Date(date), 'yyyy')}`;
    } else if ((new Date(date).getMonth() + 1) > 9 && (new Date(date).getMonth() + 1) <= 12) {
      label = `Q4<br>${this._datePipe.transform(new Date(date), 'yyyy')}`;
    } else if ((new Date(date).getMonth() + 1) == 0) {
      label = `Q1<br>${this._datePipe.transform(new Date(date), 'yyyy')}`;
    }
    return label;
  }

  setCorrelationChartData(chartCardData: any) {
    const xAccessor = chartCardData.xAccessor.path;
    const yAccessor = chartCardData.yAccessor.path;
    const tooltipTitleAccess = chartCardData.tooltipAccessor.path;
    const tooltipDescAccess = chartCardData.tooltipAccessor.description;

    const value = chartCardData.data.map((values: any) => {
      return {y: values[yAccessor], color: values[yAccessor] < 0 ? ifpColors.green : ifpColors.purple};
    } );

    // this.plotBandsY = this.generatePlotBands(6);\
    const xAxis = chartCardData.data.map((values: any) => values[xAccessor]);
    const tooltipData = chartCardData.data.map((tooltipValue: any) => {
      return {title: tooltipValue[tooltipTitleAccess], description: tooltipValue[tooltipDescAccess]};
    });
    const chartData =  [{
      name: this._translateServivce.instant(chartCardData.yAccessor.path),
      data: value,
      dashStyle: 'Solid',
      type: 'column',
      color: '#3BD6AD',
      pointWidth: 50,
      spacing: [20, 0, 0, 0],
      marker: {
        fillColor: '#3BD6AD',
        lineWidth: 1,
        lineColor: '#3BD6AD',
        symbol: 'circle',
        enabled: true
      }
    }];
    const plotLinesY = [{
      value: 0,
      color: '#d9dcdd',
      width: 1,
      zIndex: 1
    }];
    return {chartData: chartData, xAxis: xAxis, tooltipData: tooltipData, plotLinesY: plotLinesY};
  }

}
