import { Subscription } from 'rxjs';
import { Directive, ElementRef, Renderer2, OnInit, OnDestroy, Input, AfterViewInit, OnChanges, ChangeDetectorRef } from '@angular/core';
import { UserJourneyService } from '../services/userJourney/user-journey.service';
import { PopService } from '../services/popperService/popper.service';

@Directive({
  selector: '[appUserJourney]',
  standalone: true
})
export class IfpUserJourneyDirective implements OnInit, OnDestroy, AfterViewInit, OnChanges {
  @Input() stepNumber = 1;
  @Input() heading = '';
  @Input() description: string[] = [];
  @Input() enable = true;
  @Input() body = false;
  @Input() fixed = false;
  @Input() customOverlay = false;
  @Input() exploreType = '';
  @Input() route = '';
  @Input() key = '';
  @Input() ctaPostion: string = 'right-start';
  @Input() customOverlayElement !: Element | any;
  @Input() clickEvent !: Element | any;
  @Input() blinkerZindex: number = 998;
  @Input() placement = 'bottom';
  public active = false;
  public popper = new PopService();
  public currentBlinker!: Element;
  public instance: any;
  public listener: any;
  public scrollListener: any;
  public observableRemove!: Subscription;
  public observableActivate!: Subscription;
  constructor(private elRef: ElementRef, private _render: Renderer2, private _UserJourneyService: UserJourneyService, private _cdr: ChangeDetectorRef) {
  }

  ngOnInit(): void {
    this.enableJourney();
  }

  ngAfterViewInit(): void {
    if (this.enable && this.active) {
      this.setPosition();
    }

  }

  enableJourney() {
    if (this.enable) {
      this.observableActivate = this._UserJourneyService.observableActivate.subscribe((event: boolean | null) => {
        if (event) {
          this.enable = true;
          this.active = true;
          this.createBlinker();
          this.valueUpdate();
          this.setPosition();
        }
      });
      this.observableRemove = this._UserJourneyService.removeBlinker.subscribe((event: boolean | null) => {
        if (event) {
          this.removeBlinker();
          this.enable = false;
        }
      });
    }
  }

  calculateFixedPosition() {
    const rect = this.elRef.nativeElement?.getBoundingClientRect();
    const blinkerRect = this.currentBlinker?.getBoundingClientRect();
    this._render.setStyle(this.currentBlinker, 'position', 'fixed');
    this._render.setStyle(this.currentBlinker, 'left', 0);
    this._render.setStyle(this.currentBlinker, 'right', 0);
    this._render.setStyle(this.currentBlinker, 'transform', `translate(${rect.x + rect.width - (blinkerRect.width / 2)}px,${rect.y - (blinkerRect.height / 2)}px)`);
  }

  setPosition() {
    if (this.body) {
      this.instance?.destroy();
      if (this.fixed) {
        this.calculateFixedPosition();
        this.valueUpdate();
      } else {
        this.instance = this.popper.createPopper(this.elRef.nativeElement, this.currentBlinker, {
          placement: this.ctaPostion,
          modifiers: [
            {
              name: 'flip',
              options: {
                flipVariations: false
              }
            },
            {
              name: 'offset',
              options: {
                offset: ({ popper }: { popper: { width: number, height: number } }) => {
                  return [-(popper.width / 2), -(popper.height / 2)];
                }
              }
            }
          ]
        });
      }
    }
  }

  createBlinker() {
    this.currentBlinker = this._render.createElement('span');
    if (this.enable) {
      this._render.addClass(this.currentBlinker, 'ifp-journey__blinker-sp');
      this._render.setStyle(this.currentBlinker, 'z-index', this.blinkerZindex);
      this._render.addClass(this.elRef.nativeElement, 'ifp-journey__blinker-wrapper');
      if (this.body) {
        const container = document.fullscreenElement ? document.fullscreenElement : document.body;
        this._render.appendChild(container, this.currentBlinker);
      } else {
        this._render.appendChild(this.elRef.nativeElement, this.currentBlinker);
      }


      this.listener = this._render.listen(this.currentBlinker, 'click', () => {
        const data = {
          heading: this.heading,
          description: this.description,
          step: this.stepNumber,
          element: this.elRef,
          blinker: this.currentBlinker,
          fixed: this.fixed,
          customOverlay: this.customOverlay,
          customOverlayElement: this.customOverlayElement,
          exploreType: this.exploreType,
          route: this.route,
          overlayZindex: this.blinkerZindex,
          key: this.key,
          placement: this.placement
        };
        this._UserJourneyService.journeyData = { ...data };
        this.valueUpdate();
        this._UserJourneyService.currentElementClick.next({ element: this.elRef, step: this.stepNumber });
      });

    }


    this.valueUpdate();
  }

  valueUpdate() {
    this._UserJourneyService.directiveList[this.stepNumber] = {
      heading: this.heading,
      description: this.description,
      step: this.stepNumber,
      element: this.elRef,
      blinker: this.currentBlinker,
      fixed: this.fixed,
      customOverlay: this.customOverlay,
      customOverlayElement: this.customOverlayElement,
      exploreType: this.exploreType,
      route: this.route,
      overlayZindex: this.blinkerZindex,
      key: this.key,
      placement: this.placement
    };
    this._UserJourneyService.updateDirective.next({ event: true, list: this._UserJourneyService.directiveList });
  }

  ngOnChanges(): void {
    if (this.enable && this.active) {
      this.valueUpdate();
      this.setPosition();
    }
  }

  removeBlinker() {
    if (this.scrollListener) {
      this.scrollListener();
    }
    this.instance?.destroy();

    if (this.body) {
      const container = document.fullscreenElement ? document.fullscreenElement : document.body;
      if (this.currentBlinker) {
        this._render.removeChild(container, this.currentBlinker);
      }
    } else if (this.currentBlinker) {
      this._render.removeChild(this.elRef.nativeElement, this.currentBlinker);
    }
    if (this.listener) {
      this.listener();
    }
    this._UserJourneyService.observableActivate.next(false);
  }

  ngOnDestroy(): void {
    this.removeBlinker();
    this.observableActivate?.unsubscribe();
    this.observableRemove?.unsubscribe();
  }
}
