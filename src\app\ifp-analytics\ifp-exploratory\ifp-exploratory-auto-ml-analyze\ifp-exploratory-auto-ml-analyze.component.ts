import { Component, <PERSON><PERSON><PERSON><PERSON>, OnInit } from '@angular/core';
import { IfpAutoMlIterationCardComponent } from '../ifp-auto-ml-iteration-card/ifp-auto-ml-iteration-card.component';
import { TranslateModule } from '@ngx-translate/core';
import { NgClass } from '@angular/common';
import { IfpAutoMlImportanceScoreCardComponent } from '../ifp-auto-ml-importance-score-card/ifp-auto-ml-importance-score-card.component';
import { IfpAutoMlVariableSelectionComponent } from '../ifp-auto-ml-variable-selection/ifp-auto-ml-variable-selection.component';
import { IfpAutoMlCorrelationChartComponent } from '../ifp-auto-ml-correlation-chart/ifp-auto-ml-correlation-chart.component';
import { ActivatedRoute, Params, Router } from '@angular/router';
import { SubSink } from 'subsink';
import { ApiService } from 'src/app/scad-insights/core/services/api.service';
import { autoMlApiEndpoints } from '../constants/auto-ml.constant';
import { IfpSpinnerComponent } from '../../../scad-insights/ifp-widgets/ifp-molecules/ifp-spinner/ifp-spinner.component';
import { SubscriptionLike } from 'rxjs';
import { StreamingService } from 'src/app/scad-insights/core/services/http/streaming.service';
import { environment } from 'src/environments/environment';
import { ToasterService } from 'src/app/scad-insights/core/services/tooster/ToastrService.service';
import {IfpExploratoryService } from '../services/ifp-exploratory.service';
import {  PreviewChart } from '../ifp-exploratory-sample-chart/ifp-exploratory-sample-chart.component';

@Component({
    selector: 'ifp-exploratory-auto-ml-analyze',
    imports: [IfpAutoMlIterationCardComponent, TranslateModule, NgClass, IfpAutoMlImportanceScoreCardComponent, IfpAutoMlVariableSelectionComponent, IfpAutoMlCorrelationChartComponent, IfpSpinnerComponent],
    templateUrl: './ifp-exploratory-auto-ml-analyze.component.html',
    styleUrl: './ifp-exploratory-auto-ml-analyze.component.scss'
})
export class IfpExploratoryAutoMlAnalyzeComponent implements OnInit, OnDestroy{
  public chartHeading  = ['Categorical', 'Numeric', 'ID', 'Boolean'];
  public chartValues: number[] = [2, 3, 1, 4];
  public legend = [
    {name: 'Selected Variables', value: 'selected'},
    {name: 'Unselected Variables', value: 'unselected'},
    {name: 'Automatically Ignored', value: 'ignore'}
  ];

  public selection = false;
  public subs = new SubSink();
  public currentId = '';
  public iterationList: IterationList[] =[];
  public loaderIteration = true;
  public loaderIterationColumn = true;
  public columnList: string[] = [];
  public correlation: {name: string, value: number}[] = [];
  public metaData?: MetaData;
  public selectedVariableList: string[] = [];
  public iterationStreamSubscription !: SubscriptionLike;
  public scenarioStreamSubscription !: SubscriptionLike;
  public controller!: AbortController;
  public loaderPercentage = 0;
  public iterationLoader = false;
  public selectedIteration = '';
  public expandAccordion = 0;
  public selectedAccordion: string = '';
  public dataChart!:PreviewChart;
  public dataId!:string;


  constructor( private _activatedRoute: ActivatedRoute, private _autoMlService:ApiService,     private _StreamService: StreamingService,
    private _route: Router, private _toaster: ToasterService,
    private _ifpExploratoryService :IfpExploratoryService
  ) {
    // get id of workflow

    this.subs.add(
      this._activatedRoute.queryParams.subscribe((event: Params) => {
        this.currentId = event['id'];
        this.dataId = event['dataId'];
      })
    );
  }

  ngOnInit(): void {
    this._ifpExploratoryService.processDisable.set(false);
    this._ifpExploratoryService.secondaryBtnDisable.set(false);
    this._ifpExploratoryService.secondaryBtn.set(true);
    this.callIteration();
    this.callColumns();
    this.callCorrelation();
    if (!this._route.url.includes('auto-ml')) {
      this._ifpExploratoryService.autoMlStatus.set(true);
    } else {
      this._ifpExploratoryService.autoMlStatus.set(false );
    }
    this.setBtnName();
    this.subs.add(this._ifpExploratoryService.processEvent.subscribe(data => {
      if (data !== '') {
        if (this.selection) {
          this.runModel();
        } else {
          this.runScenario();
        }

      }
    }));
    this.subs.add(this._ifpExploratoryService.processEventsecondary.subscribe(data => {
      if (data !== '') {
        this. reAnalyzeEvent(!this.selection);
      }
    }));
  }

  // set button name
  setBtnName() {
    this._ifpExploratoryService.secondaryIcon.set(' ifp-icon-detial-view');
    if (this.selection) {
      this._ifpExploratoryService.secondaryName.set('Iteration Analysis');
      this._ifpExploratoryService.processName.set('Run Model');
      this._ifpExploratoryService.processDisable.set(true);
      this._ifpExploratoryService.processIcon.set('ifp-icon-play-round');
    } else {
      this._ifpExploratoryService.secondaryName.set('Re Analyze');
      this._ifpExploratoryService.processName.set('I’m satisfied with the Result');
      this._ifpExploratoryService.processDisable.set(false);
      this._ifpExploratoryService.processIcon.set('ifp-icon-rightarrow');
    }

  }

  // get iteration list form api

  callIteration() {
    this.loaderIteration = true;
    this.subs.add(
      this._autoMlService.getMethodRequest(autoMlApiEndpoints.iteration +this.currentId +autoMlApiEndpoints.iterationList, {}, true).subscribe((data:IterationList[]) => {
        this.iterationList = data;
        this.loaderIteration = false;
        this.selectedIteration =  this.iterationList[this.iterationList.length - 1].object_id;
        this.selectedAccordion =   this.selectedIteration;
        if (this.iterationList?.length < 4) {
          this._ifpExploratoryService.secondaryBtn.set(true);
        } else {
          this._ifpExploratoryService.secondaryBtn.set(false);
        }
      })
    );
  }



  // get correlation list form api
  callCorrelation() {
    this.subs.add(
      this._autoMlService.getMethodRequest(autoMlApiEndpoints.correlationWorkFlow +this.currentId +autoMlApiEndpoints.correlation, {}, true).subscribe((data: {name: string, value: number}[]) => {
        this.correlation = data;
      })
    );
  }


  // get column list form api
  callColumns() {
    this.loaderIterationColumn = true;
    this.subs.add(
      this._autoMlService.getMethodRequest(autoMlApiEndpoints.iterationColumns +this.currentId +autoMlApiEndpoints.iterationColumn, {}, true).subscribe((data: {columns:string[], metadata: MetaData}) => {
        this.columnList = data.columns;
        this.columnList = data.columns.filter(e => e !== data.metadata.target_variable && e !== data.metadata.time_stamp_variable);
        this.loaderIterationColumn = false;
        this.metaData = data.metadata;
      })
    );
  }

  // runScenario while api call
  runScenario() {
    this._ifpExploratoryService.processDisable.set(true);
    this._ifpExploratoryService.secondaryBtnDisable.set(true);
    this.loaderIteration = true;
    const formData =  new FormData();
    formData.append('iteration', this.selectedIteration);
    this.subs.add(
      this._autoMlService.postMethodRequest(autoMlApiEndpoints.runScenarioIteration +this.selectedIteration +autoMlApiEndpoints.runScenario, formData, {}, true).subscribe((data: {object_id: string}) => {
        this.callScenarioStream(data.object_id);
      })
    );
  }

  // To get selected Variable list
  selections(event: {
    currentSelectedValue: {
      name: string;
      value: number;
  },
    selectedList: string[],
    selectedIndex: number[]
  }) {
    this.selectedVariableList = event.selectedList;
    if (event.selectedIndex.length !==0 &&  this.iterationList.length <5) {
      this._ifpExploratoryService.processDisable.set(false);
    } else {
      this._ifpExploratoryService.processDisable.set(true);
    }
  }

  // used to edit iteration name

  valueChange(value: string, objectId: string, index: number) {
    const formData =  new FormData();
    formData.append('name', value);
    this.subs.add(
      this._autoMlService.putMethodRequest(autoMlApiEndpoints.iterationUpdate +objectId+autoMlApiEndpoints.iterationUpdate1, formData, true).subscribe(() => {
        this.iterationList[index].name = value;
      })
    );
  }

  // Re analyze button event
  reAnalyzeEvent(value: boolean) {
    this.selection = value;
    this.setBtnName();
  }

  // Run model event emit
  runModel() {
    this._ifpExploratoryService.processDisable.set(true);
    this._ifpExploratoryService.secondaryBtnDisable.set(true);
    this.iterationLoader = true;
    const value  = {selected_variable: this.selectedVariableList };
    this.subs.add(this._autoMlService.postMethodRequest(autoMlApiEndpoints.correlationWorkFlow +this.currentId +autoMlApiEndpoints.correlationIteration, value, {}, true).subscribe((data: {object_id: string, name: string}) => {
      this.callStream(data.object_id);
    }));
  }

  // stream api for check the status of model run
  callStream(objectId: string) {
    this.iterationStreamSubscription = this._StreamService.connectToServerSentEvents(`${environment.prepbaseUrl+ autoMlApiEndpoints.correlationStream +objectId +  autoMlApiEndpoints.streamProcess}`)
      .subscribe({
        next: (data: {data: string, ctrl: AbortController}) => {
          const dataValue = JSON.parse(data.data);
          this.controller  = data.ctrl;
          if ('Running'== dataValue.status) {
            this.loaderPercentage = 90;
          }
          if ('completed'== dataValue.status) {
            data.ctrl.abort();
            this.iterationStreamSubscription.unsubscribe();
            this.loaderPercentage = 100;
            if (this._route.url.includes('auto-ml')) {
              this._route.navigateByUrl(`analytics/auto-ml/re-analysis?id=${this.currentId}&dataId=${this.dataId}`);
            } else {
              this._route.navigateByUrl(`analytics/exploratory/re-analysis?id=${this.currentId}&dataId=${this.dataId}`);
            }
            this.iterationLoader = false;
            this.callIteration();
            this.callColumns();
            this.callCorrelation();
            this.selection = false;
            this.setBtnName();
            this._ifpExploratoryService.processDisable.set(false);
            this._ifpExploratoryService.secondaryBtnDisable.set(false);
          }
        },
        error: () => {
          this.iterationStreamSubscription.unsubscribe();
          this._toaster.error('Iteration Error!');
        }
      }
      );
  }

  // stream api for check the status of model run
  callScenarioStream(objectId: string) {
    this.scenarioStreamSubscription = this._StreamService.connectToServerSentEvents(`${environment.prepbaseUrl+ autoMlApiEndpoints.scenarioStream +objectId +  autoMlApiEndpoints.streamProcess}`)
      .subscribe({
        next: (data: {data: string, ctrl: AbortController}) => {
          const dataValue = JSON.parse(data.data);
          this.controller  = data.ctrl;
          if ('Running'== dataValue.status) {
            this.loaderPercentage = 70;
          }
          if ('completed'== dataValue.status) {
            data.ctrl.abort();
            this.scenarioStreamSubscription.unsubscribe();
            this.loaderPercentage = 100;
            this.loaderIteration = false;
            this._route.navigateByUrl(`scenario-details?id=${objectId}`);
            this._ifpExploratoryService.processDisable.set(false);
            this._ifpExploratoryService.secondaryBtnDisable.set(false);
          }
        },
        error: () => {
          this._StreamService.close();
          this.scenarioStreamSubscription.unsubscribe();
          this.iterationLoader = false;
          this._ifpExploratoryService.processDisable.set(false);
          this._ifpExploratoryService.secondaryBtnDisable.set(false);
          this._toaster.error('Iteration Error!');
        }
      }
      );
  }

  // get selected iteration
  selectedIterationEvent(event:IterationList ) {
    this.selectedIteration = event.object_id;
    this.selectedAccordion = event.object_id;
  }

  // emit Event while clicking accordion and setup selected accordion
  expandedEventEmit(event: boolean, item: IterationList) {
    if (event) {
      this.selectedAccordion = item.object_id;
    } else {
      this.selectedAccordion = '';
    }
  }

  // event for deleting iteration
  deleteEventEmit(event: {id: string}, _index: number){
    this.loaderIteration = true;
    this.subs.add(
      this._autoMlService.getDeleteRequest(autoMlApiEndpoints.iterationUpdate +event.id+autoMlApiEndpoints.iterationDelete, true).subscribe({next: () => {
        this._toaster.success('Iteration deleted successfully!');
        this.callIteration();
      }
      }
      )
    );
  }


  ngOnDestroy(): void {
    this.subs.unsubscribe();
    this.scenarioStreamSubscription?.unsubscribe();
    this.iterationStreamSubscription?.unsubscribe();
    this._ifpExploratoryService.processEvent.next('');
    this._ifpExploratoryService.processEventsecondary.next('');
  }

}

export interface IterationList {
  object_id: string;
  name: string;
  metadata?: MetaData;
  initial_iteration: boolean;
}

interface MetaData {
  score: number;
  best_model: string;
  target_variable?: string;
  time_stamp_variable?: string;
  'model metrics': ModelMetrics;
  'ai explanation': string;
  'importance scores': ImportanceScores;
  'important features': string[];
  'iteration variables': IterationVariable[];
  'selected column meta': SelectedColumnMetaAutoMl[];
}

export interface  SelectedColumnMetaAutoMl {
  rank: number;
  column: string;
  importance_score: number;
}

interface IterationVariable {
  column: string;
  variable_type: string;
}

interface ImportanceScores {
  Daily_Date: number;
  Weekly_Date: number;
  Monthly_Date: number;
  String_Column: number;
  Numeric_Column2: number;
  Numeric_Column3: number;
}

interface ModelMetrics {
  R2: number;
  MAE: number;
  MSE: number;
}
