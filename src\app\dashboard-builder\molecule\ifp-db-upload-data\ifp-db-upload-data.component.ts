import { axisDropDowns, commonKeyword } from './../../organism/ifp-chart-toolbar/ifp-chart-toolbar.constants';
import { AxisDropDown, AxisOptions } from './../../organism/ifp-chart-toolbar/ifp-chart-toolbar.interface';
import { Component, EventEmitter, Input, OnChanges, Output, SimpleChanges } from '@angular/core';
import { fileFormats } from '../ifp-db-file-uploader/ifp-db-file-uploader.constants';
import { cloneDeep } from 'lodash';
import { IfpDbDropdownComponent } from '../ifp-db-dropdown/ifp-db-dropdown.component';


@Component({
    selector: 'ifp-db-upload-data',
    templateUrl: './ifp-db-upload-data.component.html',
    styleUrl: './ifp-db-upload-data.component.scss',
    imports: [IfpDbDropdownComponent]
})
export class IfpDbUploadDataComponent implements OnChanges {

  @Output() getXaxis: EventEmitter<AxisDropDown> = new EventEmitter<AxisDropDown>();
  @Output() getYaxis: EventEmitter<AxisDropDown[]> = new EventEmitter<AxisDropDown[]>();
  @Output() uploadFile: EventEmitter<File[]> = new EventEmitter<File[]>();

  @Input() axisOptions: AxisOptions[] = axisDropDowns;
  @Input() selectedXaxis: AxisDropDown = axisDropDowns[0].options[0];
  @Input() selectedYaxis: AxisDropDown[] = [axisDropDowns[0].options[1]];



  public allowedExtensions = fileFormats.excelFormats;


  constructor() { }


  ngOnChanges(_changes: SimpleChanges): void {
    this.disableXAxis();
    this.disableYaxis();
  }



  onSelectXaxis(xAxis: any, axis: string) {
    if (axis == commonKeyword.Xaxis) {
      this.selectedXaxis = xAxis;
      this.getXaxis.emit(this.selectedXaxis);
      this.disableXAxis();
    }
  }

  disableXAxis() {
    const yaxisIndex = this.axisOptions.findIndex(x => x.key == commonKeyword.Yaxis);
    const xOpts = cloneDeep(this.axisOptions[yaxisIndex].options);
    xOpts.map((x: { disabled: boolean; }) => x.disabled = false);
    const selectedOptIndex = xOpts.findIndex((x: { key: string; }) => x.key == this.selectedXaxis.key);
    if (selectedOptIndex >= 0) {
      xOpts[selectedOptIndex].disabled = true;
    }
    this.axisOptions[yaxisIndex].options = xOpts;
  }

  disableYaxis() {
    const xaxisIndex = this.axisOptions.findIndex(x => x.key == commonKeyword.Xaxis);
    this.axisOptions[xaxisIndex].options.map((x: { disabled: boolean; }) => x.disabled = false);
    if (this.selectedYaxis?.length > 0) {
      const opts = cloneDeep(this.axisOptions[xaxisIndex].options);
      this.selectedYaxis.forEach(element => {
        if (element?.key) {
          const selectedOptIndex = opts.findIndex((x: { key: string; }) => x.key == element.key);
          if (selectedOptIndex >= 0) {
            opts[selectedOptIndex].disabled = true;
          }
        }
      });
      this.axisOptions[xaxisIndex].options = opts;
    }
  }

  onSelectYaxis(yAxis: any, axis: string) {
    if (axis == commonKeyword.Yaxis) {
      this.selectedYaxis = yAxis;
      this.getYaxis.emit(this.selectedYaxis);
      this.disableYaxis();
    }
  }


}
