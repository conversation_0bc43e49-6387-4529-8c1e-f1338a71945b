import { Component, EventEmitter, Output } from '@angular/core';
import { IfpButtonComponent } from '../../../../scad-insights/ifp-widgets/ifp-atoms/ifp-button/ifp-button.component';
import { TranslateModule } from '@ngx-translate/core';
import { buttonClass } from 'src/app/scad-insights/core/constants/button.constants';

@Component({
    selector: 'ifp-ifp-guide-popup',
    templateUrl: './ifp-guide-popup.component.html',
    styleUrl: './ifp-guide-popup.component.scss',
    imports: [IfpButtonComponent, TranslateModule]
})
export class IfpGuidePopupComponent {
  @Output() dismissModel = new EventEmitter();
  public buttonClass = buttonClass;

  closeModel() {
    this.dismissModel.emit(true);
  }


}
