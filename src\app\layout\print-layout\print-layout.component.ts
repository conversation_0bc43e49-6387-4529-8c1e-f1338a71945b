import { AfterViewInit, Component, ViewChild } from '@angular/core';
import { Router } from '@angular/router';
import { DownloadService } from 'src/app/scad-insights/core/services/download.service';
import { IfpPdfTemplateComponent } from 'src/app/scad-insights/ifp-widgets/ifp-molecules/ifp-pdf-template/ifp-pdf-template.component';
import { IfpModalComponent } from 'src/app/scad-insights/ifp-widgets/ifp-organism/ifp-modal/ifp-modal.component';


@Component({
    selector: 'app-print-layout',
    templateUrl: './print-layout.component.html',
    styleUrls: ['./print-layout.component.scss'],
    imports: [IfpModalComponent, IfpPdfTemplateComponent]
})
export class PrintLayoutComponent implements  AfterViewInit {
  @ViewChild('modalSla', { static: true }) modalSla!: IfpModalComponent;

  constructor(private router: Router, public downloadService: DownloadService) {
  }




  ngAfterViewInit(): void {
    this.modalSla.createElement();
    setTimeout(() => {
      window.print();
      this.downloadService.isPrinting = false;
      this.modalSla.removeModal();
      this.router.navigate([{ outlets: { print: null }}]);
    });
  }


}
