<div class="ifp-dxp-popup">
  <div class="ifp-dxp-popup__header">
    <span  class="ifp-dxp-popup__title">
      {{'Select Asset' | translate}}
    </span>
    <em class="ifp-icon ifp-icon-cross ifp-dxp-popup__close" (click)="close.emit()"></em>
  </div>
<div class="ifp-dxp-popup__body">
  <div class="ifp-dxp-popup__search-wrapper">
    <ifp-search class="ifp-dxp-popup__search" [isOnlyBorder]="true" [isKeypress]="true" (searchEvent)="searchResult($event)"
    [onSearch]="searchString()" [boxType]="true" [placeholderText]="'Search'| translate"></ifp-search>
  </div>
  <div  class="ifp-dxp-popup__accordian-wrapper">
    @if (loader()) {
      <app-ifp-card-loader class="ifp-dxp-popup__loader" [type]="loaderType.small"></app-ifp-card-loader>
    } @else {
      @for(asset of assetList(); track $index) {
        <ifp-dxp-accordian [minimize]="!$first" [title]="asset.name" [id]="asset.id" [image]="asset.image" [entity]="asset.organization.name" [assets]="asset.assets" (getSelectedProduct)="onSelecteProduct($event)" class="ifp-dxp-popup__accordian"></ifp-dxp-accordian>
      } @empty {
        <app-ifp-no-data [showBorder]="true"></app-ifp-no-data>
      }
    }
  </div>

</div>
</div>
