@use "../../../../assets/ifp-styles/abstracts/index" as *;

:host {
  display: block;
}

.ifp-upload-model {
  padding: ($spacer-2 + 4);

  &__head {
    display: flex;
    justify-content: flex-end;
    padding-bottom: $spacer-2;
    // border-bottom: 1px solid $ifp-color-grey-7;
    // margin-bottom: $spacer-2;

    .ifp-icon {
      font-size: $ifp-fs-6;
      cursor: pointer;
    }
  }

  &__body {
    padding: $spacer-3;
  }

  &__title {
    font-size: 1.8rem;
    font-weight: 500;
  }

  &__cnt {
    margin-top: $spacer-3;
  }

  &__desc {
    font-size: $ifp-fs-5;
    margin-bottom: $spacer-3;
  }

  &__data-upload {
    margin: $spacer-4 $spacer-0;
  }

  &__footer {
    display: flex;
    justify-content: flex-end;
    padding-top: $spacer-3 ;
  }
  // &__warning, &__info {
  //   color: $ifp-color-red;
  // }
  &__warning {
    margin-bottom: $spacer-2;
    font-weight: $fw-medium;

  }
  &__info {
    margin-inline-end: $spacer-2;
    font-size: $ifp-fs-4;
  }
  &__warning-wrapper {
    display: flex;
    margin-top: $spacer-4;
    padding: $spacer-3;
    background-color: $ifp-color-pale-blue;
    border-radius: 7px;
  }
}
