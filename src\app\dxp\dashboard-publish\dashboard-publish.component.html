<div class="ifp-dashboard-publish">
        <app-ifp-breadcrumbs [pageData]="pageData" class="ifp-dxp-detail__breadcrumb"></app-ifp-breadcrumbs>
  <div class="ifp-dashboard-publish__header-wrapper">
    <h1 class="ifp-dashboard-publish__title">{{'Configure and Publish - ADEO'| translate}}</h1>
    <p class="ifp-dashboard-publish__desc">{{'Power users will have the ability to publish dashboards and assign access to specific users.' | translate}}</p>
  </div>
  <div class="ifp-container">
  <div class="ifp-dashboard-publish__card">
    <div class="ifp-dashboard-publish__card-inner">
       <div class="ifp-dashboard-publish__sec-1">
  <div class="ifp-dashboard-publish__input">
                <label class="ifp-dashboard-publish__input-label">{{'Dashboard Name' | translate}}</label>
                  <input class="ifp-input ifp-dashboard-publish__input-field" placeholder="Enter Value">
                 </div>
                 <app-ifp-dropdown  class="ifp-dashboard-publish__dropdown" [showTitle]="true" [title]="'Select BI Technology'"></app-ifp-dropdown>
                     <app-ifp-dropdown class="ifp-dashboard-publish__dropdown" [showTitle]="true" [title]="'Server'"></app-ifp-dropdown>
                       <div class="ifp-dashboard-publish__input">
                <label class="ifp-dashboard-publish__input-label">{{'Embedded URL for light theme' | translate}}</label>
                  <input class="ifp-input ifp-dashboard-publish__input-field" placeholder="Paste link here">
                 </div>
                   <div class="ifp-dashboard-publish__input">
                <label class="ifp-dashboard-publish__input-label">{{'Embedded URL for dark theme' | translate}}</label>
                  <input class="ifp-input ifp-dashboard-publish__input-field" placeholder="Paste link here">
                 </div>
                    <div class="ifp-dashboard-publish__user">
          <ifp-panel-dropdown  [userDropdown]="true"  [label]="'Add User'" [enableSearch]="true" (multiSelected)="dropDownItemMultiClicked($event)" [isBoxType]="true" [enableSelectAll]="true"
           [multiSelect]="true" [key]="'value'" [multipleSelectedItems]="currentSelection()" [options]="sharedUserList()"  class="ifp-dashboard-publish__user-list"  [title]="'Add users'"></ifp-panel-dropdown>
          @if (sharedUserListSelected().length !==0) {
          <ifp-user-tag-group [isEdit]="true" (removeData)="removeData($event)" [tagList]="sharedUserListSelected()" class="ifp-dashboard-publish__user-group"></ifp-user-tag-group>
        }
        </div>
       </div>
       <div class="ifp-dashboard-publish__sec-2">
        <div class="ifp-dashboard-publish__wrapper">
          <h6 class="ifp-dashboard-publish__preview-title">{{'Preview'| translate}}</h6>
          <img src="../../../assets/images/dashboard-publish.svg" class="ifp-dashboard-publish__img"/>
          <p class="ifp-dashboard-publish__note"><em class="ifp-icon ifp-dashboard-publish__note-icon ifp-icon-info-round"></em><span class="ifp-dashboard-publish__note-text">Note</span>{{'Power users will have the ability to publish dashboards and assign access to specific users.'| translate}}</p>
        </div>
       </div>
    </div>
    <div class="ifp-dashboard-publish__footer">
      <span class="ifp-link ifp-dashboard-publish__btn">Cancel</span>
   <ifp-button class="ifp-dashboard-publish__btn" [label]="'Save as Draft'"
          [buttonClass]="buttonClass.buttonHoverBlueSecondary +' '+ buttonClass.normalAplabetic" [iconClass]="'ifp-icon-save'"></ifp-button>
        <ifp-button class="ifp-dashboard-publish__btn" [label]="'Publish'"
          [buttonClass]="buttonClass.hoverBlue+' '+ buttonClass.normalAplabetic" [iconClass]="'ifp-icon-rightarrow'">
        </ifp-button>
    </div>
  </div>
  </div>

</div>
