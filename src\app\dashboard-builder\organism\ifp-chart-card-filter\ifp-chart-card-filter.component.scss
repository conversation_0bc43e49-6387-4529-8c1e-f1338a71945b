@use "../../../../assets/ifp-styles/abstracts/index" as *;

:host {
  display: block;

  &::ng-deep {
    .ifp-dropdown {
      max-width: 100%;
    }
  }
}

.ifp-card-filter {
  &__header-wrapper {
    display: flex;
    padding: $spacer-4;
    border-bottom: 1px solid $ifp-color-grey-7;
    align-items: center;

    .ifp-icon-filter-2 {
      margin-right: $spacer-2;
      font-size: $ifp-fs-4;
    }
  }

  &__header-title {
    font-weight: $fw-semi-bold;
    color: $ifp-color-black;
    font-size: $ifp-fs-4;
  }

  &__body-wrapper {
    padding: $spacer-4;
  }

  &__footer-wrapper {
    padding: $spacer-4;
    border-top: 1px solid $ifp-color-grey-7;
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    background-color: $ifp-color-white;
  }

  &__filter-item {
    padding: $spacer-4;
  }

  &__cancel-btn {
    margin-right: $spacer-3;
  }
}
