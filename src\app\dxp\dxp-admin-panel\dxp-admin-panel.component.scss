@use "../../../assets/ifp-styles/abstracts/index" as *;

.ifp-node {
  &--dxp-admin {
    border: 0;
    border-radius: 0;
    .ifp-node {
      &__card-left,
      &__card-right {
        border: 1px solid $ifp-color-grey-7;
      }
      &__card-left {
        display: block;
        padding: $spacer-4;
        border-radius: 10px 0 0 10px;
        width: auto;
        min-width: 75%;
      }
      &__card-right {
        display: flex;
        flex-direction: column;
        border-left: 0;
        margin-inline-start: -1px;
        border-radius: 0 10px 10px 0;
        transform: translateX(0);
        transition: 0.3s transform;
      }
      &__tiltle {
        margin-bottom: $spacer-4;
      }
    }
    .ifp-chart-toolbar {
      &__action-box {
        &:last-child {
          margin-top: auto;
          padding-bottom: $spacer-4;
        }
      }
    }
  }
}
.ifp-dxp-admin {
  &__detail {
    border: 1px solid $ifp-color-grey-7;
    border-radius: 10px;
    display: flex;
  }
  &__sec-1 {
    width: 75%;
    background-color: $ifp-color-section-white;
  }
  &__sec-2 {
    width: 25%;
    background-color: $ifp-color-chart-sidebar;
  }
  &__user-detail {
    display: flex;
    align-items: center;
    &:last-child {
      margin-inline-start: $spacer-6;
    }
  }
  &__user-detail-txt {
    color: $ifp-color-tertiary-text;
    margin-inline-end: $spacer-2;
    .ifp-icon {
      font-size: 1.2em;
      display: inline-block;
      margin-inline-end: $spacer-1;
      position: relative;
      top: 2px;
    }
  }
  &__user-group {
    margin-inline-end: $spacer-2;
  }
  &__access-wrapper {
    display: flex;
    align-items: center;
    margin: $spacer-3 $spacer-0;
  }
  &__user-text {
    color: $ifp-color-grey-14;
  }
  &__footer,
  &__btn-sec {
    display: flex;
    justify-content: flex-end;
  }
  &__footer {
    padding: $spacer-4 $spacer-4 $spacer-0;
    border-top: 1px solid $ifp-color-grey-7;
    margin: $spacer-0 (-$spacer-4);
  }
  &__btn {
    display: block;
    margin-inline-start: $spacer-3;
  }
  &__revert-inline {
    font-size: $ifp-fs-5;
    font-weight: $fw-bold;
    display: inline-flex;
    align-items: center;
    margin-bottom: $spacer-2;
    transition: 0.3s;
    cursor: pointer;
    .ifp-icon {
      color: $ifp-color-blue-hover;
      display: inline-block;
      margin-inline-end: $spacer-2;
    }
    &:hover {
      color: $ifp-color-blue-hover;
    }
  }
  &__revert-desc {
    margin-bottom: $spacer-0;
  }
  &__textarea-label {
    color: $ifp-color-grey-6;
    margin-bottom: $spacer-2;
  }
  &__textarea {
    min-height: 120px;
    margin-bottom: $spacer-3;
  }
  &__comment-wrapper {
    height: 360px;
    @include ifp-scroll-y(transparent, $ifp-color-grey-1, 8px, 8px)
  }
  &__comment-name {
    display: flex;
    align-items: center;
    margin-bottom: $spacer-2;
  }
  &__name-tag {
    margin-inline-end: $spacer-2 + 2px;
  }
  &__comment {
    display: inline-block;
    color: $ifp-color-white-global;
    background-color: $ifp-color-blue-hover;
    border-radius: 10px;
    padding: $spacer-2 $spacer-3;
    margin-bottom: $spacer-2;
  }
  &__comment-time {
    color: $ifp-color-grey-14;
  }
  &__comment-item {
    margin-bottom: $spacer-4;
    &:last-child {
      margin-bottom: $spacer-0;
    }
    &--self {
      text-align: right;
      .ifp-dxp-admin {
        &__comment-name {
          flex-direction: row-reverse;
        }
        &__name-tag {
          margin-inline-end: $spacer-0;
          margin-inline-start: $spacer-2 + 2px;
        }
        &__comment {
          color: $ifp-color-black;
          background-color: $ifp-color-white;
        }
      }
    }
  }


  &--collapse {
    .ifp-node {
      &__card-left {
        min-width: 100%;
        border-radius: 10px;
      }
      &__card-right {
        transform: translateX(100%);
        // min-width: 0;
        // max-width: 0;
        // overflow: hidden;
        position: absolute;
        right: 0;
        top: 0;
      }
    }
  }
}
