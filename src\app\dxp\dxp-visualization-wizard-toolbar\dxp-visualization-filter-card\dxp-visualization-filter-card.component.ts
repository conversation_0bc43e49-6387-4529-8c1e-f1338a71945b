import { Component, input, InputSignal, output } from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';

@Component({
  selector: 'ifp-dxp-visualization-filter-card',
  imports: [TranslateModule],
  templateUrl: './dxp-visualization-filter-card.component.html',
  styleUrl: './dxp-visualization-filter-card.component.scss'
})
export class DxpVisualizationFilterCardComponent {
  public removeCard = output();
  public filter: InputSignal<any> = input({
    column: '',
    filterLabel: '',
    defaultValue: null,
    dataType: ''
  });

  onRemoveCard() {
    this.removeCard.emit()
  }
}

interface DxpKpiCustomFilter {
  column: string;
  filterLabel: string;
  defaultValue: string;
}
