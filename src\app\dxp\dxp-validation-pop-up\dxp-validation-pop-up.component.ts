import { Component, input, output } from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';
import { buttonClass } from '../../scad-insights/core/constants/button.constants';
import { IfpButtonComponent } from "../../scad-insights/ifp-widgets/ifp-atoms/ifp-button/ifp-button.component";
import { NgClass } from '@angular/common';

@Component({
  selector: 'ifp-dxp-validation-pop-up',
  imports: [TranslateModule, IfpButtonComponent,NgClass],
  templateUrl: './dxp-validation-pop-up.component.html',
  styleUrl: './dxp-validation-pop-up.component.scss'
})
export class DxpValidationPopUpComponent {
 public buttonClass=buttonClass;
 public heading = input('Validation Error');
 public desc = input('Please address the following issues with the Data Product Owner to ensure your dataset is compliant and ready for visualization in Bayaan');
 public successPopup =  input(false);
 public icon = input('ifp-icon ifp-icon-exclamation-round');
 public close = output();

}
