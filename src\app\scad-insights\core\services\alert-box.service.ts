import { DOCUMENT } from '@angular/common';
import { ElementRef, Inject, Injectable, Renderer2, RendererFactory2 } from '@angular/core';
import { BehaviorSubject } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class AlertBoxService {
  private render: Renderer2;
  constructor(rendererFactory: RendererFactory2, @Inject(DOCUMENT) private _document: Document) {
    this.render = rendererFactory.createRenderer(null, null);
  }

  public childElement!: HTMLElement | undefined;
  public isAlertBoxStatus$ = new BehaviorSubject(false);
  closeAlertBox(alertBox: ElementRef) {
    if (alertBox) {
      this.render.removeChild(this._document.body, alertBox.nativeElement);
    }
  }

  openAlertBox(alertBox: ElementRef, card: HTMLDivElement) {
    this.isAlertBoxStatus$.next(true);
    const clickedElement = card;
    const scrollPos = window.scrollY || document.documentElement.scrollTop || document.body.scrollTop || 0;
    const boundingClientRect = clickedElement.getBoundingClientRect();
    this.render.setStyle(alertBox.nativeElement, 'top', `${boundingClientRect.top + scrollPos + boundingClientRect.height}px`);
    this.render.setStyle(alertBox.nativeElement, 'left', `${boundingClientRect.left}px`);
    this.render.setStyle(alertBox.nativeElement, 'transform', 'translateY(-100%)');
    this.render.appendChild(this._document.body, alertBox.nativeElement);
    this.render.addClass(alertBox.nativeElement, 'active');
  }
}
