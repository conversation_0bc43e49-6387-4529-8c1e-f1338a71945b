import { NgClass } from '@angular/common';
import { Component, model } from '@angular/core';

@Component({
  selector: 'ifp-text-alignment-selector',
  imports: [NgClass],
  templateUrl: './text-alignment-selector.component.html',
  styleUrl: './text-alignment-selector.component.scss'
})
export class TextAlignmentSelectorComponent {

  selectedAlignment = model<string>('left');

  public alignments = [
    {
      type: 'left',
      icon: 'ifp-icon-leftarrow'
    },
    {
      type: 'center',
      icon: 'ifp-icon-sort1'
    },
    {
      type: 'right',
      icon: 'ifp-icon-rightarrow'
    }
  ]
}
