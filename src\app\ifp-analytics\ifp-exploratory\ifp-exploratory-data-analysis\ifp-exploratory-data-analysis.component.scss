@use "../../../../assets/ifp-styles/abstracts/index" as *;
.ifp-exp{
  &__dropdown {
    margin-inline-start: $spacer-2;
  }
  &__wrapper {
    display: flex;
    margin: $spacer-6  $spacer-0;
    @include mobile-tablet {
      position: relative;
      margin:  $spacer-0;
    }
  }
  &__wrapper-table,&__summery {
    background-color: $ifp-color-section-white;
    border-radius: 10px;
    cursor:pointer;
  }
  &__summery {
    display: flex;
    justify-content: space-between;
    padding: $spacer-4 $spacer-5;
    margin-bottom: $spacer-4;
    &--active {
      border-left:$ifp-color-blue-menu solid 4px;
    }
    @include desktop-sm {
      padding: $spacer-3 $spacer-3;
      margin-bottom: $spacer-3;
    }
  }
   &__wrapper-table{
    width: 75%;
    @include mobile-tablet {
      width: calc(100% - 30px);
      margin-inline-start: 30px;
    }
   }
  &__wrapper-section {
    width: calc(25% - $spacer-3);
    margin-inline-end:  $spacer-3;
    @include mobile-tablet {
      overflow: hidden;
      width: 350px;
      position: absolute;
      top: 0;
      margin: $spacer-0;
      left: (-$spacer-3);
      background-color: $ifp-color-grey-bg;
      z-index: 996;
      padding: $spacer-3;
      height: calc(100vh - ($ifp-header-height-inner + 162px));
      transform: translateX(-110%);
      transition: 0.3s;
    }
    &--large {
      transform: translateX(0);
    }
  }
  &__wrapper-overlay {
    position: absolute;
    top:0;
    left: 0;
    width: calc(100% + ($spacer-3));
    height: 100%;
    z-index: 995;
    background-color: $ifp-color-black-50;
    display: none;
    @include mobile-tablet {
      display: block;
    }
  }
  &__hamburger {
    display: none;
    @include mobile-tablet {
    margin-top: $spacer-2;
    padding: $spacer-2;
      display: inline-block;
    }
  }
  &__acc {
    background-color: $ifp-color-section-white;

    border-radius: 10px;
    margin-bottom: $spacer-4;
    &--disable {
      opacity: .5;
      pointer-events: none;
      cursor: not-allowed;
    }
    @include desktop-sm {
      margin-bottom: $spacer-3;
    }
  }
  &__chart-icon {
    margin: $spacer-2 $spacer-2;
    width: calc(33.33% - ($spacer-2 * 2));
  }
  &__acc-wrapper {
    display: flex;
    flex-wrap: wrap;
    transition: .3s;
    margin: $spacer-0 (-$spacer-2);
    overflow: hidden;
  }
  &__title {
    font-size: $ifp-fs-4;
    font-weight: $fw-bold;
    @include desktop-sm {
      font-size: $ifp-fs-3;
    }
  }
  &__title-wrapper {
    display: flex;
    justify-content: space-between;
    margin: $spacer-2 $spacer-0;
  }

  &__acc-radio {
    display: block;
    margin: $spacer-0 $spacer-3 $spacer-3;
    @include desktop-sm {
      margin: $spacer-0;
    }
  }
  &__chart-wrapper {
    border-radius: 10px;
    background-color: $ifp-color-section-white;
  }
  &__chart-header {
    padding: $spacer-5;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid $ifp-color-grey-7;
    @include desktop-sm {
      font-size: $ifp-fs-4;
      padding: $spacer-3;
    }
  }
  &__chart-title {
    font-size: $ifp-fs-6;
    font-weight: $fw-bold;
    @include desktop-sm {
      font-size: $ifp-fs-4;
    }
  }
  &__chart {
    padding: $spacer-5;
    @include desktop-sm {
      padding: $spacer-0;
    }
  }
  &__chart-process {
    padding:$spacer-2 $spacer-5 $spacer-3;
    border-bottom: 1px solid $ifp-color-grey-7;
    @include desktop-sm {
      padding:$spacer-2 $spacer-3 $spacer-3;
    }
  }
  &__chart-download {
      display: flex;
      justify-content: end;
      align-items: center;
      margin-left:(-$spacer-2);
      margin-right: (-$spacer-2);

  }
  &__download-text,&__download-item {
    margin: $spacer-2;
  }
  &__sec-chart{
    padding:$spacer-2 $spacer-5;
    @include desktop-sm {
      padding:$spacer-2 $spacer-3;
    }
  }
  &__download-display{
    display: none;
  }
  &__highchart {
  width: 100%;
  display: block;
  }
  &__download {
    width: 1500px;
  }
  &__download-item {
    position: relative;
    cursor: pointer;
  }
  &__download-dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    opacity: 0;
    visibility: hidden;
    transition: 0.3s;
    background-color: $ifp-color-white;
    box-shadow: 0 0 8px $ifp-color-black-16;
    border-radius: 10px;
    margin-top: $spacer-2;
    z-index: 1;
    &--active {
      opacity: 1;
      visibility: visible;
    }
  }
  &__download-option {
    padding: $spacer-2 $spacer-3;
    transition: 0.3s;
    white-space: nowrap;
    cursor: pointer;
    &:first-child {
      border-radius: 10px 10px 0 0;
    }
    &:last-child {
      border-radius: 0 0 10px 10px;
    }
    &:hover {
      background-color: $ifp-color-grey-7;
    }
  }
  &__subheading-cor {
    color: $ifp-color-grey-6;
    font-size: $ifp-fs-3;
    margin-bottom: $spacer-4;
  }
  &__heading-cor {
    font-weight: $fw-bold;
    font-size: $ifp-fs-4;
    margin-bottom: $spacer-2;
    margin-top: $spacer-4;
    @include desktop-sm {
      margin-top: $spacer-2;
    }
  }
  &__drop-sec {
    display: flex;
    align-items: center;
  }
  &__tag{
    margin-left: $spacer-3;
    margin-right: $spacer-3;
    text-transform: none;
  }
  &__drop-title{
    color: $ifp-color-grey-6;
    font-size: $ifp-fs-3;
    margin-bottom: $spacer-2;
  }
  &__error {
    padding: $spacer-2;
    font-size: $ifp-fs-3;
    margin-top: $spacer-2;
    color:$ifp-color-red;
  }
  &__error-wrapper {
    display: flex;
    align-items: center;
    margin-inline-start: $spacer-3;
    .ifp-icon {
      color: $ifp-color-red;
    }
  }

}
:host::ng-deep {
  .ifp-dropdown__title {
    font-size: $ifp-fs-8;
    }
    app-ifp-dropdown {
      .ifp-dropdown {
        min-width: 200px !important;
        max-width: 200px  !important;
        &__list{
          width: 100%;
        }
      }
    }
    .ifp-exp__tag {
      .ifp-tag {
        padding:  $spacer-1 $spacer-2 !important;
        &__text {
          text-transform: none;
        }
        &__icon {
          font-size: $ifp-fs-5;
        }
      }
    }
 .ifp-exp__chart-icon {
  .ifp-toolbar-icon__icon {
    width: 65px;
    margin: $spacer-0 auto $spacer-1;
    @include desktop-sm {
      width: 55px;
      height: 55px !important;
    }
  }
 }

 @include desktop-sm {
  .ifp-exp__acc {
    .ifp-accord-box{
      &__head {
        padding: $spacer-3 $spacer-3 !important;
    }
    &__title {
      font-size: $ifp-fs-3!important;
    }
  }
  }
  .ifp-exp__acc-radio {
    .ifp-radio{
      &__label {
        span {
          font-size: $ifp-fs-2;
        }
    }}
  }
.ifp-exp__acc-wrapper {
  .ifp-toolbar-icon__name {
    font-size: $ifp-fs-2;
  }
}
.ifp-select-data__button {
   .ifp-btn span {
    font-size:  $ifp-fs-2;
}
}
}
}

@include mobile-tablet {
  .ifp-exp {
    &__wrapper-table {
      margin-inline-start: $spacer-0;
      width: 100%;
    }
  }
}

:host-context([dir="rtl"]) {
  .ifp-exp {
    &__summery{
    &--active {
      border-left:unset;
      border-right:$ifp-color-blue-menu solid 4px;
    }

  }
}
}
:host-context(.ifp-dark-theme) {
  .ifp-exp {
    &__error {
  color: $ifp-color-brick-red;
    }

  }
}
