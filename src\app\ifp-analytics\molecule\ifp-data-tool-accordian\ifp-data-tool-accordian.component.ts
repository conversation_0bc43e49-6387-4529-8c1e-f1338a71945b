import { Ng<PERSON><PERSON> } from '@angular/common';
import { Component, EventEmitter, forwardRef, Input, Output } from '@angular/core';
import { NG_VALUE_ACCESSOR } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';
import { IfpCheckBoxComponent } from 'src/app/scad-insights/ifp-widgets/ifp-atoms/ifp-check-box/ifp-check-box.component';
import { IfpInputEditComponent } from '../../../scad-insights/ifp-widgets/ifp-molecules/ifp-input-edit/ifp-input-edit.component';

@Component({
    selector: 'ifp-data-tool-accordian',
    imports: [NgClass, IfpCheckBoxComponent, TranslateModule, IfpInputEditComponent],
    providers: [
        {
            provide: NG_VALUE_ACCESSOR,
            useExisting: forwardRef(() => IfpDataToolAccordianComponent),
            multi: true
        }
    ],
    templateUrl: './ifp-data-tool-accordian.component.html',
    styleUrl: './ifp-data-tool-accordian.component.scss'
})
export class IfpDataToolAccordianComponent {
  @Input() isAccordianExpanded: boolean = false;
  @Input() isDraggable: boolean = false;
  @Input() isChecked: boolean = false;
  @Input() enableCheckBox: boolean = true;
  @Input() icon!: string;
  @Input() iconColor: string = '#6A7180';
  @Input({ required: true }) title: string = '';
  @Input() selection = false;
  @Input() active = false;
  @Input() isError: boolean = false;
  @Input() isDisabled: boolean=false;
  @Input() isdisableCheckbox:boolean=false;
  @Input() deleteIcon!: string | boolean;
  @Input() isEdit = false;
  @Input() disableTranslation = false;

  @Output() selectItem: EventEmitter<{ isSelected: boolean; item: string }> = new EventEmitter<{ isSelected: boolean; item: string }>();
  @Output() deleteValue =  new EventEmitter();
  @Output() valueChange =  new EventEmitter();
  @Output() expandedEvent =  new EventEmitter();
  onChange = (_value: any) => { };

  onTouched = () => { };

  touched = false;
  public edit = false;

  onClick(event: MouseEvent) {
    event.stopPropagation();
  }

  onSelectItem(event: boolean) {
    this.isChecked = event;
    this.selectItem.emit({ isSelected: event, item: this.title });
    this.onChange(event);
    this.onTouched();
  }

  writeValue(value: any): void {
    this.isChecked = value;
  }

  registerOnChange(fn: any): void {
    this.onChange = fn;
  }

  registerOnTouched(fn: any): void {
    this.onTouched = fn;
  }

  editEvent(event:MouseEvent) {
    event.stopPropagation();
    this.edit = true;
  }

  editInput(event:MouseEvent) {
    event.stopPropagation();
  }

  cancelEdit(event: {event: MouseEvent}) {
    event.event.stopPropagation();
    this.edit = false;
  }

  cancelValueChange(event: {event: MouseEvent, value: string}) {
    event.event.stopPropagation();
    this.valueChange.emit(event.value);
    this.edit = false;
  }

  // used to emit expanded and collapse accordion
  expandedEventEmit() {
    this.expandedEvent.emit(this.isAccordianExpanded);
  }

  deleteEvent(event:MouseEvent) {
    event.stopPropagation();
    this.deleteValue.emit();
  }
}
