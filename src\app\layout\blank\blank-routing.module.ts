import { NgModule, inject } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { BlankComponent } from './blank.component';
import { AuthGuardCheckingService } from 'src/app/scad-insights/core/guard/authChecking.guard';
import { PublicService } from 'src/app/scad-insights/core/guard/public.guard';

const routes: Routes = [
  {
    path: '',
    redirectTo: 'home',
    pathMatch: 'full'
  },
  {
    path: '', component: BlankComponent,
    children: [
      {
        path: 'auth',
        loadComponent:
          () => import('src/app/scad-insights/authentication/auth/auth.component').then((m: typeof import('src/app/scad-insights/authentication/auth/auth.component')) => m.AuthComponent),
        canActivate: [() => {
          return inject(AuthGuardCheckingService).canActivate();
        }]
      },
      {
        path: 'login',
        loadComponent:
          () => import('src/app/scad-insights/authentication/login/login.component').then((m: typeof import('src/app/scad-insights/authentication/login/login.component')) => m.LoginComponent),
        canActivate: [() => {
          return inject(PublicService).canActivate();
        }]
      },
      {
        path: 'sla',
        loadComponent:
          () => import('src/app/scad-insights/authentication/sla-certificate/sla-certificate.component').then((m: typeof import('src/app/scad-insights/authentication/sla-certificate/sla-certificate.component')) => m.SlaCertificateComponent)

      },
      {
        path: 'group_unassigned',
        loadComponent:
          () => import('src/app/scad-insights/authentication/sla-certificate/sla-certificate.component').then((m: typeof import('src/app/scad-insights/authentication/sla-certificate/sla-certificate.component')) => m.SlaCertificateComponent)
      },
      {
        path: 'siteUnderMaintains',
        loadComponent:
          () => import('src/app/scad-insights/authentication/siteUnderMaintains/siteUnderMaintains.component').then((m: typeof import('src/app/scad-insights/authentication/siteUnderMaintains/siteUnderMaintains.component')) => m.SiteUnderMaintainsComponent)
      },
      //   {
      //     path: 'accept-terms-and-conditions',
      //     loadComponent:
      // () => import('src/app/scad-insights/authentication/terms-and-condition-initial/terms-and-condition-initial.component').then((m:typeof import('src/app/scad-insights/authentication/terms-and-condition-initial/terms-and-condition-initial.component')) => m.TermsAndConditionInitialComponent)
      //   },
      {
        path: 'accept-nda',
        loadComponent:
    () => import('src/app/scad-insights/authentication/nda-initial/nda-initial.component').then((m:typeof import('src/app/scad-insights/authentication/nda-initial/nda-initial.component')) => m.NdaInitialComponent)
      },
      {
        path: 'user-register',
        loadComponent: () => import('src/app/scad-insights/user-onboarding/user-onboarding-register/user-onboarding-register.component').then((m:typeof import('src/app/scad-insights/user-onboarding/user-onboarding-register/user-onboarding-register.component')) => m.UserOnboardingRegisterComponent)
      },
      {
        path: 'auth/upass/callback',
        loadComponent:
          () => import('src/app/scad-insights/redirects/ifp-uae-pass-callback/ifp-uae-pass-callback.component').then((m: typeof import('src/app/scad-insights/redirects/ifp-uae-pass-callback/ifp-uae-pass-callback.component')) => m.IfpUaePassCallbackComponent)
      },
      {
        path: 'pre-register-auth',
        loadComponent: () => import('src/app/scad-insights/user-onboarding/ifp-otp-form/ifp-otp-form.component').then((m:typeof import('src/app/scad-insights/user-onboarding/ifp-otp-form/ifp-otp-form.component')) => m.IfpOtpFormComponent)
      },
      {
            path: 'insight-report-pdf-preview/:id',
            loadComponent: () => import('src/app/gen-ai/ai-insight-report/ai-insight-report.component').then((m: typeof import('src/app/gen-ai/ai-insight-report/ai-insight-report.component')) => m.AiInsightReportComponent),
      },
       {
            path: 'consolidated-pdf-preview/:id',
            loadComponent: () => import('src/app/gen-ai/ai-insight-report/gen-ai-consolidated/gen-ai-consolidated.component').then((m: typeof import('src/app/gen-ai/ai-insight-report/gen-ai-consolidated/gen-ai-consolidated.component')) => m.GenAiConsolidatedComponent),

          },
    ]
  }];



@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class BlankRoutingModule { }
