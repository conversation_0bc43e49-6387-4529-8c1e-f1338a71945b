@use "../../../../../assets/ifp-styles/abstracts/index" as *;

.ifp-prep-add {
  margin: $spacer-0 (
    -$spacer-4
  );

&__title {
  font-size: $ifp-fs-4;
  font-weight: $fw-semi-bold;
}

&__header {
  padding: $spacer-0 $spacer-4 $spacer-4;
  border-bottom: 1px solid $ifp-color-grey-7;

  .ifp-prep-add__title {
    margin-bottom: $spacer-2;
  }
}

&__input {
  color: $ifp-color-black;
  display: block;
  width: 100%;
  max-width: 440px;
  border: 1px solid $ifp-color-grey-7;
  padding: ($spacer-3 - 4px) $spacer-3;
  border-radius: 7px;
  background-color: $ifp-color-white;
  @include placeholder($ifp-color-tertiary-text);
}

&__body {
  display: flex;

  .ifp-prep-add__title {
    margin-bottom: $spacer-3;
  }
}

&__body-head {
  padding: $spacer-0 $spacer-4;
  margin-bottom: $spacer-3;
}

&__body-editor {
  width: 80%;
  border-inline-end: 1px solid $ifp-color-grey-7;
  border-inline-start: 1px solid $ifp-color-grey-7;
  padding: $spacer-4;
}

&__body-list {
  width: 20%;
  min-width: 200px;
  position: relative;
  padding: $spacer-4 $spacer-0;
}

&__reset {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: $spacer-3;

  .ifp-prep-add__title {
    margin-bottom: $spacer-0;
    padding: $spacer-0;
  }
}

&__formulas {
  display: flex;
  flex-wrap: wrap;
  margin: $spacer-0 (
    -$spacer-2) $spacer-4;
}

&__tag {
  display: block;
}

&__expressions {
  margin-bottom: $spacer-5;
}

&__code-preview {
  background-color: $ifp-color-section-white;
  border: 1px solid $ifp-color-grey-7;
  border-radius: 7px;
  padding: $spacer-3;
  width: 100%;
  min-height: 180px;
  resize: none;
}

&__field-list {
  padding: $spacer-0 $spacer-4;
  max-height: 280px;
  @include ifp-scroll-y(transparent, $ifp-color-grey-7, 8px, 8px
);
}

&__field-icon {
  opacity: 0;
  transition: 0.3s;
  position: absolute;
  top: 50%;
  right: $spacer-2;
  transform: translateY(-50%);
}

&__field {
  text-transform: uppercase;
  cursor: grab;
  background-color: $ifp-color-section-white;
  padding: $spacer-2 $spacer-4 $spacer-2 $spacer-3;
  border-radius: 7px;
  margin-bottom: $spacer-3;
  position: relative;
  user-select: none;
  transition: 0.3s;
  overflow: hidden;
  text-overflow: ellipsis;
  touch-action: none;

  &:last-child {
    margin-bottom: $spacer-0;
  }

  &--dragging,
  &:hover {
    background-color: $ifp-color-violet-light;

    .ifp-prep-add__field-icon {
      opacity: 1;
    }
  }
}

&__formula-list {
  position: relative;
  margin: $spacer-0 $spacer-2 $spacer-3;

  &::before {
    content: "";
    width: 0;
    height: 0;
    border-inline-end: 15px solid transparent;
    border-inline-start: 15px solid transparent;
    border-bottom: 15px solid $ifp-color-grey-bg-2;
    margin-top: $spacer-1;
    position: absolute;
    top: 100%;
    left: 50%;
    transform: translateX(-50%);
    opacity: 0;
    transition: opacity 0.3s;
  }

  &--active {
    &::before {
      opacity: 1;
    }
  }
}

&__details {
  width: 100%;
  height: 100%;
  min-width: 270px;
  @include ifp-scroll-y(transparent, $ifp-color-grey-7, 8px, 8px);
  position: absolute;
  top: 0;
  right: 100%;
  opacity: 0;
  visibility: hidden;
  transition: 0.3s;
  transform: translateX(20px);
  background-color: $ifp-color-grey-bg-2;
  padding: $spacer-3;

  &--show {
    opacity: 1;
    visibility: visible;
    transform: translateX(0);
  }
}

&__details-name {
  font-size: $ifp-fs-4;
  font-weight: $fw-bold;
  text-transform: uppercase;
  margin-bottom: $spacer-3;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

&__details-name,
&__details-text {
  color: $ifp-color-grey-9;
}

&__details-text {
  margin-bottom: $spacer-3;
  padding-bottom: $spacer-3;
  border-bottom: 1px solid $ifp-color-grey-7;

  &:last-child {
    margin-bottom: $spacer-0;
  }
}

&__details-title {
  font-weight: $fw-semi-bold;
  display: block;
  margin-bottom: $spacer-1;
}

&__detail-close {
  cursor: pointer;
  transition: 0.3s;

  &:hover {
    opacity: 0.8;
  }
}

&__search {
  display: block;
  margin-bottom: $spacer-2;
}

&__header-wrapper {
  display: flex;
  justify-content: space-between;
  margin-bottom: $spacer-3;
  align-items: end;
}

&__title-main {
  font-size: $ifp-fs-6;
  font-weight: $fw-semi-bold;
  margin-bottom: $spacer-1;
}

&__sub-title {
  font-size: $ifp-fs-4;
  font-weight: $fw-medium;
  color: $ifp-color-grey-2;
  margin-inline-end: 12px;
}

&__header-reset {
  display: flex;
  align-items: center;
  justify-content: end;
  margin-bottom: $spacer-4;
}

&__exp-desc {
  margin-top: $spacer-1;
  color: $ifp-color-grey-2;
}

&__formula-field-list {
  display: flex;
  margin-top: $spacer-3;
  overflow: auto;
  margin-inline-start: -$spacer-2;
  margin-inline-end: -$spacer-2;
  user-select: none;
  @include ifp-scroll-x(transition, $ifp-color-grey-7, 5px, 5px);
}

&__formula-list-wrapper {
  margin-bottom: $spacer-4;
}

// &__formula-list-li {
//   margin: $spacer-0 $spacer-2 $spacer-2 $spacer-2;
// }
&__formula-details-inner {
  display: flex;
  padding: $spacer-3 $spacer-0;
}

&__formula-details-title {
  font-weight: $fw-bold;
}

&__formula-details-sub-title {
  color: $ifp-color-grey-9;
  word-break: break-word;
}
&__validation {
  display: inline-block;
  font-size: $ifp-fs-3;
  color: $ifp-color-red;
  margin-top: $spacer-1;
}

&__formula-details-text {
  padding: $spacer-0 $spacer-3;
  border-inline-end: 1px solid $ifp-color-grey-8;
  width: 100%;
  position: relative;

  &:first-child {
    margin-inline-end: $spacer-3;
    border-inline-end: none;

    &::before,
    &::after {
      content: "";
      background-color: $ifp-color-grey-bg-2;
      position: absolute;
      right: 0;
    }

    &::before {
      width: 1px;
      height: calc(100% + 2 * $spacer-3);
      top: (-$spacer-3);
      background-color: $ifp-color-grey-8;
    }

    &::after {
      width: 15px;
      height: 15px;
      border-top: 1px solid $ifp-color-grey-8;
      border-inline-end: 1px solid $ifp-color-grey-8;
      background-color: $ifp-color-grey-bg-2;
      top: 50%;
      transform: translate(50%, -50%) rotate(45deg);
    }
  }

  &:last-child {
    border: none;
  }
}

// &__formula-details-text-divider {
//   border-left: 1px solid  $ifp-color-grey-8;
//   height: 100%;
// }
// &__formula-details-text-divider-wrapper {
//   padding:  $spacer-1 ;
//   margin-top: $spacer-1 ;
//   margin-bottom: $spacer-1 ;
// }
&__formula-details {
  visibility: hidden;
  transition: 0.3s;
  transform: translateY(-50px);
  background-color: $ifp-color-grey-bg-2;
  border-radius: 10px;
  overflow: hidden;

  &--show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
  }
}

&__formula-tag {
  display: block;
}

&__notFound {
  text-align: center;
}

&__title-mandatory {
  color: $ifp-color-red;
}

&__value-bold {
  font-weight: $fw-bold;
}
}

:host::ng-deep {
  .ifp-prep-add {
    &__formula-tag {
      .ifp-btn {
        cursor: grab;
        width: 100%;
      }

      .ifp-btn--secondary {
        // background-color: $ifp-color-grey-bg-2;
        border: 1px solid $ifp-color-grey-7;
        background-color: $ifp-color-grey-bg-2;
        color: $ifp-color-black;

        @include ignore-touch-hover {
          &:hover {
            background-color: $ifp-color-active-blue;
            color: $ifp-color-white-global;
            .ifp-icon {
              color: $ifp-color-white-global;
            }
          }
        }
      }
    }

    &__tag {
      .ifp-btn {
        cursor: grab;
        width: 100%;
      }

      .ifp-btn--secondary {
        // background-color: $ifp-color-grey-bg-2;
        border: 1px solid $ifp-color-grey-7;
        color: $ifp-color-grey-9;

        &:hover {
          background-color: $ifp-color-grey-bg-2;

          // color: $ifp-color-white-global;
          // border: 1px solid $ifp-color-active-blue;
          .ifp-icon {
            color: $ifp-color-white-global;
          }
        }
      }
    }

    &__formula-list {
      &--active {
        .ifp-prep-add__tag {
          .ifp-btn--secondary {
            background-color: $ifp-color-grey-bg-2;
          }
        }
      }
    }

    .CodeMirror {
      padding: $spacer-3;
      height: 146px;
      background-color: $ifp-color-section-white;
    }
    .CodeMirror-secondarycursor {
      display: none;
    }
    .CodeMirror-vscrollbar {
      @include ifp-scroll-y($ifp-color-white, $ifp-color-grey-1, 8px, 8px);
    }
  }

  .ifp-prep-add__code-editor {
    .CodeMirror {
      color: $ifp-color-black-dark;
    }
    .cm {
      &-node,
      &-variable {
        color: $ifp-color-black-dark;
      }
    }
  }
}

:host-context(.ifp-dark-theme) {
  .ifp-prep-add {
    &__formula-list::before {
      border-bottom: 15px solid $ifp-color-section-white;
    }
    &__formula-details {
      background-color: $ifp-color-section-white;
    }
    &__formula-details-sub-title {
      color: $ifp-color-grey-2;
    }
    &__formula-details-text:first-child::after {
      background-color: $ifp-color-section-white;
    }
  }

  ::ng-deep .ifp-prep-add__code-editor {
    .cm {
      &-builtin {
        color: $ifp-color-green-dark-2;
      }
    }
  }
}

:host-context([dir="rtl"]) {
  .ifp-prep-add {
    &__field-icon {
      left: $spacer-2;
      right: auto;
    }

    &__formula-list {
      &::before {
        right: 50%;
        left: auto;
        transform: translateX(50%);
      }
    }

    &__details {
      left: 100%;
      right: auto;
      transform: translateX(-20px);
    }

    &__formula-details-text {
      &:first-child {
        &::before,
        &::after {
          left: 0;
          right: auto;
        }
        &::after {
          transform: translate(-50%, -50%) rotate(-45deg);
        }
      }
    }
    &__formula-details-title {
      direction: ltr;
      display: flex;
      justify-content: flex-end;
    }
  }
}

@include desktop-sm {
  .ifp-prep-add {
    &__body-editor {
      width: 66%;
    }
    &__body-list {
      width: auto;
      min-width: unset;
    }
  }
}

