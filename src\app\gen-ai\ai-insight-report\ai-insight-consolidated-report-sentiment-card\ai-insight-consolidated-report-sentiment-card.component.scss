@use "../../../../assets/ifp-styles/abstracts/index" as *;
:host {
  display: block;
}
.ifp-ai-insight-cr-sentiment-card {
  background-color: $ifp-color-section-white;
  box-shadow: 0px 25px 35px -20px #707b9c29;
  border: 0;
  border-radius: 20px;
  padding: $spacer-3;
  display: flex;
  &--insight {
    padding: $spacer-0;
    box-shadow:none;
    background-color: $ifp-color-yellow-bg;
  }
  &__rating {
    width: 40%;
    min-width: 40%;
    display: flex;
    margin: $spacer-0 $spacer-2;
    align-items: center;
    justify-content: center;
    &--red {
      color: $ifp-color-red-normal;
    }
    &--green {
      color: $ifp-color-green-normal;
    }
    &--yellow {
      color: $ifp-color-orange;
    }
  }
  &__impact {
    background-color: $ifp-color-yellow-bg;
    border-radius: 14px;
    height: 100%;
    width: 60%;
    display: flex;
    align-items: center;
  }
  &__rating-name {
    font-size: $ifp-fs-4;
    color: $ifp-color-primary-grey;
    margin: $spacer-0 $spacer-3;
  }
  &__rating-value {
    font-size: $ifp-fs-4;
    font-weight: $fw-bold;
  }
  &__rating-outer {
    display: flex;
    align-items: center;
      margin-top: $spacer-3;
      &:first-child {
        margin-top: 0;
      }
  }
  &__rating-icon {
    font-size: $ifp-fs-12;
  }

}
@include desktop-sm {
  .ifp-ai-insight-cr-sentiment-card {
    flex-wrap: wrap;
    height: auto;
    &__rating {
      width: 100%;
      margin-bottom: $spacer-3;
    }
    &__impact {
      width: 100%;
      justify-content: center;
    }
  }
}

:host-context(.ifp-dark-theme) {
  .ifp-ai-insight-cr-sentiment-card {
    box-shadow: 0 18.05px 96.14px 7.18px #6f6f6f1a;
   background-color: $ifp-color-black-global;
   &__impact {
    background-color: $ifp-color-black-global;
   }
  }
  }
  :host::ng-deep {
    .highcharts-background {
      fill: transparent !important;
  }
  }
