import { Component, ChangeDetectionStrategy, Input, signal, WritableSignal, EventEmitter, Output, inject, OnInit, OnChanges, OnDestroy } from '@angular/core';
import { ColumnPrep, ToolOptions } from '../../data-prep/ifp-data-prep/ifp-prep-select-data/interface/prep-selection.interface';
import { buttonClass } from 'src/app/scad-insights/core/constants/button.constants';
import { selectNodeStoreAdvance } from '../../data-prep/ifp-advance-prep/store/advance-node-store/advance-node.store';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { ToasterService } from 'src/app/scad-insights/core/services/tooster/ToastrService.service';
import { IfpAdvancePrepService } from '../../data-prep/ifp-advance-prep/service/ifp-advance-prep.service';
import { IfpButtonComponent } from '../../../scad-insights/ifp-widgets/ifp-atoms/ifp-button/ifp-button.component';
import { TranslateModule } from '@ngx-translate/core';
import { IfpDropdownComponent } from 'src/app/scad-insights/ifp-widgets/ifp-atoms/ifp-dropdown/ifp-dropdown.component';
import { IfpSpinnerComponent } from '../../../scad-insights/ifp-widgets/ifp-molecules/ifp-spinner/ifp-spinner.component';
import { NgClass } from '@angular/common';
import { IfpCheckBoxComponent } from '../../../scad-insights/ifp-widgets/ifp-atoms/ifp-check-box/ifp-check-box.component';
import { connectionType } from '../../data-prep/ifp-data-prep/constants/if-preps.constants';
import { IfpNoDataComponent } from '../../../scad-insights/ifp-widgets/ifp-molecules/ifp-no-data/ifp-no-data.component';
import { IfpNumberOnlyDirective } from 'src/app/scad-insights/core/directives/ifp-number-only.directive';
import { IfpDataToolAccordianComponent } from '../../molecule/ifp-data-tool-accordian/ifp-data-tool-accordian.component';

@Component({
    selector: 'ifp-adv-impute',
    imports: [ReactiveFormsModule, IfpButtonComponent, TranslateModule, IfpDropdownComponent, IfpSpinnerComponent, NgClass, IfpCheckBoxComponent,
        IfpNoDataComponent,
        IfpNumberOnlyDirective, IfpDataToolAccordianComponent],
    templateUrl: './ifp-adv-impute.component.html',
    styleUrl: './ifp-adv-impute.component.scss',
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class IfpAdvImputeComponent implements OnInit, OnChanges, OnDestroy {

  @Output() cancel = new EventEmitter();
  @Output() save = new EventEmitter();

  @Input({ required: true }) columnList!: ColumnPrep[];
  @Input() workFlowRunning = signal(false);
  @Input() emptyConnection = signal(true);
  @Input() currentNodeId?: string = '';
  @Input() currentConection: WritableSignal<boolean> = signal(false);
  @Input() sourceData: WritableSignal<boolean> = signal(false);
  @Input() isConfigUpdated: WritableSignal<boolean> = signal(false);
  @Input() errorMessageForTools?: string;

  public buttonClass = buttonClass;
  public readonly advanceStore = inject(selectNodeStoreAdvance);
  public imputeForm!: FormGroup;
  public selectedColumn!: ColumnPrep;
  public selectedImpute!: ToolOptions;
  public imputeList: ImputeData[] = [];
  public submitted: WritableSignal<boolean> = signal(false);
  public selectedDatatype!: string;
  public savedConfig: WritableSignal<boolean> = signal(true);
  public isSelectActive: boolean = false;
  public isNull: boolean = true;
  public isFillCol: boolean = true;
  public selectedIndex!: number;
  public imputeOptsDropdown: ToolOptions[] = [];
  public nonNullableTypes: string[] = ['string', 'datetime'];
  public isNoImputeColumn: boolean = false;
  public imputeOptionList: ToolOptions[] = [
    {
      label: 'Replace with Median',
      method: 'median'
    },
    {
      label: 'Replace with Min',
      method: 'min'
    },
    {
      label: 'Replace with Max',
      method: 'max'
    },
    {
      label: 'Replace with Mean',
      method: 'mean'
    },
    {
      label: 'Replace with Custom',
      method: 'custom'
    }
  ];

  constructor(private _formbuilder: FormBuilder, private _prepService: IfpAdvancePrepService, private _toaster: ToasterService) { }


  ngOnInit() {
    this.imputeForm = this._formbuilder.group({
      column: ['', Validators.required],
      method: ['', Validators.required],
      value: [''],
      user_specified: [''],
      new_column: ['']
    });
  }

  ngOnChanges() {
    if (this.columnList?.length > 0) {
      this.imputeList = [];
      this.patchStoreData();
    }
  }

  patchStoreData() {
    this.resetForm();
    this.imputeList = [];
    const nodeData = this.advanceStore.selectNodeValue().nodeValue(this.currentNodeId ?? '');
    const columns = nodeData?.configuration?.columns?.length ? nodeData.configuration.columns : [];
    this.columnList = this.columnList?.filter((col: ColumnPrep) => !(this.nonNullableTypes.includes(col.type)));
    this.isNoImputeColumn = this.columnList?.every((col: ColumnPrep) => (this.nonNullableTypes.includes(col.type)));
    columns.forEach((element: any) => {
      const columnDataIndex: number = this.columnList?.findIndex(x => x.name === element.column);
      if (columnDataIndex >= 0) {
        const imputeOption = this.columnList[columnDataIndex].impute_options?.find((x: ToolOptions) => x.method === element.method);
        if (imputeOption) {
          this.imputeList.push({
            column: element.column,
            value: element.value,
            user_specified: element.user_specified,
            method: imputeOption,
            new_column: element.new_column
          });
        }
      }
    });
  }

  get getControls() {
    return this.imputeForm.controls;
  }

  selectColumn(event: ColumnPrep | any) {
    this.selectedColumn = event;
    this.imputeForm.controls['column'].setValue(event.name);
    this.selectedDatatype = event.type;
    // this.imputeForm.controls['type'].setValue(this.selectedColumn.type);
    this.imputeOptsDropdown = this.selectedColumn.impute_options ?? this.imputeOptionList;
  }

  updateFormControl(patchValues: Record<string, any>, controlName: string, validator: Validators | null) {
    this.imputeForm.patchValue(patchValues);
    const control = this.imputeForm.get(controlName);
    if (validator) {
      control?.addValidators([Validators.required]);
    } else {
      control?.removeValidators([Validators.required]);
    }
    this.imputeForm.updateValueAndValidity();
  }

  valueToReplace(status: boolean) {
    this.isNull = status;
    const patchValues = {
      isNull: status,
      user_specified: ''
    };
    this.updateFormControl(patchValues, 'user_specified', status ? null : Validators.required);
  }

  onSelectMethod(method: ToolOptions | any) {
    const patchValues = method.method === 'custom' ? { method: method } : { method: method, value: null };
    this.updateFormControl(patchValues, 'value', method.method === 'custom' ? Validators.required : null);
  }

  onSetOutput(status: boolean) {
    this.isFillCol = status;
    const patchValues = {
      isFillCol: status,
      new_column: ''
    };
    this.updateFormControl(patchValues, 'new_column', status ? null : Validators.required);
  }

  addImpute() {
    if (this.imputeForm.invalid) {
      this.submitted.set(true);
      return;
    }
    this.submitted.set(false);
    if (this.isSelectActive) {
      this.imputeList[this.selectedIndex] = this.imputeForm.value;
      this.selectedIndex = -1;
    } else {
      this.imputeList.push(this.imputeForm.value);
    }
    this.resetForm();
  }

  selectimpute(impute: ImputeData, index: number) {
    this.isSelectActive = true;
    this.selectedIndex = index;
    const selectedCol = this.columnList.find((col: ColumnPrep) => col.name === impute.column);
    if (selectedCol) {
      this.selectedColumn = selectedCol;
      this.imputeOptsDropdown = selectedCol.impute_options ?? this.imputeOptionList;
      this.selectedImpute = impute.method;
    }
    this.isNull = !(impute.user_specified?.length);
    this.isFillCol = !(impute.new_column?.length);

    this.imputeForm.setValue({
      column: impute.column,
      method: impute.method,
      value: impute.value,
      user_specified: impute.user_specified,
      new_column: impute.new_column
    });
  }

  removeImpute(index: number) {
    this.imputeList.splice(index, 1);
  }


  resetForm() {
    this.imputeOptsDropdown = [];
    this.isSelectActive = false;
    this.selectedIndex = -1;
    this.imputeForm?.setValue({
      column: '',
      method: '',
      value: '',
      user_specified: '',
      new_column: ''
    });
    const excludeControls = ['column', 'method'];
    if (this.imputeForm) {
      for (const key in this.imputeForm.controls) {
        if (Object.hasOwn(this.imputeForm.controls, key) && !excludeControls.includes(key)) {
          this.imputeForm.get(key)?.clearValidators();
          this.imputeForm.get(key)?.updateValueAndValidity();
        }
      }
    }
    this.isFillCol = true;
    this.isNull = true;
  }

  onSave() {
    const config = {
      columns: this.getImputeOpts()
    };
    this.savedConfig.set(true);
    this.advanceStore.updateNode(this.currentNodeId ?? '', 'Impute Tool updated', connectionType.imputeTool, config, 'Impute Tool updated');
    this._prepService.nodeChangeDetect.next('node updated');
    // this._toaster.success('Data Saved Successfully');
    // this.checkValidation();
    this.save.emit();

    // this.savedConfig.set(true);
    // this.advanceStore.updateNode(this.currentNodeId ?? '', 'Impute Tool updated', connectionType.imputeTool, config, 'Impute Tool updated');
    // this._prepService.nodeChangeDetect.next('node updated');
    // this._toaster.success('Data Saved Successfully');
    // this.save.emit();
    // this.isSaveClicked = true;
    // if (this.imputeForm.valid) {
    //   const config: {columns: Record<string, any>} = {columns: {}};
    //   this.imputeForm.value.columns.forEach((element: any) => {
    //     config.columns[element.name] = {
    //       method: element.method.method,
    //       value: element.customImputeValue,
    //       user_specified: element.user_specified,
    //       new_column: element.new_column
    //     };
    //   });
    //   this.isSaved = true;
    //   this.advanceStore.updateNode(this.currentNodeId ?? '', 'Impute Tool', connectionType.imputeTool, config, 'Impute tool updated', {inputCols: this.columnListView, outputCols: this.formdata.value});
    //   this._prepService.nodeChangeDetect.next('node updated');
    //   this._toaster.success('Data Saved Successfully');
    //   this.save.emit();
    // } else {
    //   this._toaster.error('Invalid Inputs');
    // }
  }

  getImputeOpts() {
    const selectedFilters: any[] = [];
    if (this.imputeList?.length) {
      this.imputeList.forEach((element: ImputeData) => {
        const data = {
          column: element.column,
          user_specified: element.user_specified,
          method: element.method.method,
          value: element.value,
          new_column: element.new_column ?? ''
        };
        selectedFilters.push(data);
      });
    }
    return selectedFilters;
  }

  onCancel() {
    this.cancel.emit();
  }

  getConvertedValue(value: string) {
    return value && value!==null ?  parseFloat(value) : value;
  }

  ngOnDestroy(): void {
    if (!this._prepService.isConfigSave() && this.columnList?.length > 0) {
      this.onSave();
    }
  }
}

interface ImputeData {
  column: string;
  method: ToolOptions;
  value: string;
  user_specified: string;
  new_column: string;
}
