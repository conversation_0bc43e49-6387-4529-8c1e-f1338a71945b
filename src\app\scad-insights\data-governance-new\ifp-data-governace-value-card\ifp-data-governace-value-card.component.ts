import { TitleCasePipe } from '@angular/common';
import { ChangeDetectionStrategy, Component, Input } from '@angular/core';

@Component({
  selector: 'ifp-data-governace-value-card',
  imports: [TitleCasePipe],
  templateUrl: './ifp-data-governace-value-card.component.html',
  styleUrl: './ifp-data-governace-value-card.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class IfpDataGovernaceValueCardComponent {

  @Input({ required: true }) icon:string | undefined='';
  @Input({ required: true }) title:string='';
  @Input({ required: true }) value:number=0;

}
