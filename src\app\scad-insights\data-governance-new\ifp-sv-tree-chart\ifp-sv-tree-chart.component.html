<div class="ifp-sv">

  <div class="ifp-sv__root-main">
    <p class="ifp-sv__text">{{'SCAD Production' | translate}}</p>
    <p class="ifp-sv__box-count">{{data.scad_production}}</p>
    <p class="ifp-sv__text">{{'indicators' | translate}}</p>
  </div>

  <div class="ifp-sv__branch-wrapper">

    <div class="ifp-sv__branch ifp-sv__branch--top">

      <div class="ifp-sv__branch-root">
        <p class="ifp-sv__text">{{'Bayaan Production' | translate}}</p>
        <p class="ifp-sv__box-count">{{data.bayaan_production}} <span class="ifp-sv__text">{{'indicators' |
            translate}}</span></p>
      </div>

      <div class="ifp-sv__sub-branch">
        @for (item of data.bayaan_domain; track $index) {
        <div class="ifp-sv__box">
          <p class="ifp-sv__text">{{item.TOPIC_NAME_ENGLISH | translate}}</p>
          <p class="ifp-sv__box-count">{{item.VALUE}} <span class="ifp-sv__text">{{'indicators' | translate}}</span></p>
        </div>
        }
      </div>

    </div>

    <div class="ifp-sv__branch ifp-sv__branch--bottom">

      <div class="ifp-sv__branch-root">
        <p class="ifp-sv__text">{{'Not Approved' | translate}}</p>
        <p class="ifp-sv__box-count">{{data.not_approved}} <span class="ifp-sv__text">{{'indicators' |
            translate}}</span></p>
      </div>

      <div class="ifp-sv__sub-branch">
        <div class="ifp-sv__box-wrapper">
          @for (item of data.not_approved_list; track $index) {
          <div class="ifp-sv__box">
            <p class="ifp-sv__text">{{item.NOT_APPROVED_STATUS | translate | titlecase}}</p>
            <p class="ifp-sv__box-count">{{item.VALUE}}</p>
          </div>
          }
        </div>

        <div class="ifp-sv__table-wrapper">
          <table class="ifp-sv__table">

            <tr class="ifp-sv__table-row ifp-sv__table-row--head">
              <th class="ifp-sv__table-col">{{'Department' | translate}}</th>
              <th class="ifp-sv__table-col">{{'Ticket Approval' | translate}}</th>
              <th class="ifp-sv__table-col">{{'Business Exception' | translate}}</th>
              <th class="ifp-sv__table-col">{{'Indicators' | translate}}</th>
            </tr>

            @for (item of data.not_approved_table; track $index) {
            <tr class="ifp-sv__table-row">
              <td class="ifp-sv__table-col">{{item.RESPONSIBLE_EN | translate}}</td>
              <td class="ifp-sv__table-col"><em class="ifp-icon {{item.TICKET_APPROVAL == 1 ? 'ifp-icon-tick ifp-sv__table-tick' : 'ifp-icon-cross ifp-sv__table-cross'}}"></em></td>
              <td class="ifp-sv__table-col"><em class="ifp-icon {{item.BUSINESS_EXCEPTION == 1 ? 'ifp-icon-tick ifp-sv__table-tick' : 'ifp-icon-cross ifp-sv__table-cross'}}"></em></td>
              <td class="ifp-sv__table-col">{{item.VALUE}}</td>
            </tr>
            }
          </table>
        </div>
      </div>

    </div>

  </div>
</div>
