import { DatePipe } from '@angular/common';
import { Pipe, PipeTransform } from '@angular/core';


@Pipe({
  name: 'customDate',
  standalone: true
})
export class CustomDatePipe implements PipeTransform {

  constructor(private datePipe: DatePipe) { }

  transform(date: any, args?: string): any {
    let formattedDate= date;
    if (args === 'monthQuotesYear') {
      formattedDate = `${this.datePipe.transform(date, 'MMM')} '${this.datePipe.transform(date, 'yy')}`;
    }
    return formattedDate;
  }

}
