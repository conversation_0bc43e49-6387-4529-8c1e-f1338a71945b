import { buttonClass } from 'src/app/scad-insights/core/constants/button.constants';
import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, ChangeDetectorRef, Component, EventEmitter, Input, Output } from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';
import { IfpTabComponent } from '../../../scad-insights/ifp-widgets/ifp-molecules/ifp-tab/ifp-tab.component';
import { TabOptions } from '../../organism/ifp-chart-toolbar/ifp-chart-toolbar.interface';
import { IfpDbFileUploaderComponent } from '../ifp-db-file-uploader/ifp-db-file-uploader.component';
import { IfpButtonComponent } from '../../../scad-insights/ifp-widgets/ifp-atoms/ifp-button/ifp-button.component';
import { SafeUrl } from '@angular/platform-browser';
import { NgxCSVParserError, NgxCsvParser } from 'ngx-csv-parser';
import { ToasterService } from 'src/app/scad-insights/core/services/tooster/ToastrService.service';
import { CustomCardService } from 'src/app/scad-insights/core/services/create-dashboard/custom-card.service';
import { singleDimentionData } from '../../organism/ifp-chart-toolbar/ifp-chart-toolbar.constants';


@Component({
    selector: 'ifp-sc-upload-model',
    templateUrl: './sc-upload-model.component.html',
    styleUrl: './sc-upload-model.component.scss',
    changeDetection: ChangeDetectionStrategy.OnPush,
    imports: [
        CommonModule, TranslateModule,
        IfpTabComponent,
        IfpDbFileUploaderComponent,
        IfpButtonComponent
    ]
})
export class ScUploadModelComponent {

  @Output() closeUploadModel: EventEmitter<boolean> = new EventEmitter<boolean>();
  @Output() uploadOrAddData: EventEmitter<{ data?: any, type: string, result: any, objectId: string, fileName: string }> = new EventEmitter<{ data?: any, type: string, result: any, objectId: string, fileName: string }>();

  @Input() isCustomInstruction: boolean = false;
  @Input() buttonLabel: string = 'Next';
  @Input() tabData: TabOptions[] = [
    {
      name: 'Upload Data'
    },
    {
      name: 'Insert Data'
    }
  ];

  public selectedTab: TabOptions = this.tabData[0];
  public allowedExcelExtensions: string[] = ['.csv'];
  public buttonClass = buttonClass;
  public uploadedFile!: { data?: { file: File[], url: SafeUrl }, result: any };
  public files: File[] = [];
  public previewUrl!: any;

  constructor(private ngxCsvParser: NgxCsvParser, private _toaster: ToasterService, private _customService:
    CustomCardService, private _cdr: ChangeDetectorRef
  ) {
  }

  closeModel() {
    this.resetVariables();
    this.closeUploadModel.emit(true);
  }


  changeTab(event: { event: string; index: number; }) {
    this.selectedTab = this.tabData[event.index];
  }

  goToData() {
    if (this.isCustomInstruction) {
      this.closeUploadModel.emit(true);
      return;
    }
    if (this.selectedTab.name == 'Insert Data') {
      this.uploadedFile = {
        result: singleDimentionData
      };
    }
    const emitData = {
      data: this.uploadedFile?.data ? this.uploadedFile.data : {},
      type: this.selectedTab.name,
      fileName: this.uploadedFile.data?.file[0].name ? this.uploadedFile.data?.file[0].name : '',
      result: [],
      objectId: ''
    };
    if ((this.selectedTab.name == 'Upload Data' && this.uploadedFile) || this.selectedTab.name == 'Insert Data') {
      this._customService.uploadCustomData(this.uploadedFile?.result).subscribe({
        next: next => {
          emitData.result = next.records;
          emitData.objectId = next.dataId;
          this.uploadOrAddData.emit(emitData);
          this.resetVariables();
        },
        error: error => {
          this._toaster.error(error.message);
          return;
        }
      });
    }
  }

  uploadFile(event: { file: File[], url: SafeUrl }) {
    this.previewUrl = event.url;
    this.files = event.file;
    this.ngxCsvParser.parse(event.file[0], { header: true, delimiter: ',', encoding: 'utf8' })
      .pipe().subscribe({
        next: (result: any): void => {
          if (result && result.length > 200) {
            this._toaster.error('Maximum row limit reached! The maximum raw limit for this upload is 200.');
            this.resetVariables();
            return;
          }
          if (Object.keys(result[0]).length > 25) {
            this._toaster.error('Maximum column limit reached. The maximum column limit for this upload is 25.');
            this.resetVariables();
            return;
          }
          this.uploadedFile = {
            data: event,
            result: result
          };
          this._cdr.detectChanges();
        },
        error: (error: NgxCSVParserError): void => {
          this._toaster.error(error.message);
        }
      });
  }

  removeFile(_event: File[]) {
    this.resetVariables();
  }

  resetVariables() {
    this.uploadedFile = { data: { file: [], url: '' }, result: [] }
    this.files = [];
    this.previewUrl = undefined;
    this.selectedTab = this.tabData[0];
    this._cdr.detectChanges();
  }


}


