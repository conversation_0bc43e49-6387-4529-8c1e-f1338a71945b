import {  createReducer, on } from '@ngrx/store';
import { domainDetailNewAdapter, domainDetailV2State, domainDetailV2StateNodes } from './domain-details-api.state';
import { loadDomainDetailsNewData, loadDomainDetailsSuccessNew } from './domain-details-api.action';


export const domainDetailReducerNew = createReducer(
  domainDetailV2State,
  on(loadDomainDetailsSuccessNew, (state, action) => {

    return domainDetailNewAdapter.addOne({...action}, {
      ...state
    });
  }
  ));

export const domainDetailReducerNode = createReducer(
  domainDetailV2StateNodes,
  on(loadDomainDetailsNewData, (state, action) => {
    return {...action.data};
  }
  ));
