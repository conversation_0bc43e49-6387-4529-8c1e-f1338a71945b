
import { Pipe, PipeTransform } from '@angular/core';


@Pipe({
  name: 'firstLetter',
  standalone: true
})
export class CustomLetter implements PipeTransform {

  constructor() { }

  transform(value: any, caseChange: boolean = false): any {
    let formattedValue = value;
    if (value) {
      formattedValue = value[0]?.toUpperCase() + value?.substr(1);
      if (caseChange) {
        formattedValue = value[0]?.toUpperCase() + value?.substr(1).toLowerCase();
      }
    }
    return formattedValue;
  }

}
