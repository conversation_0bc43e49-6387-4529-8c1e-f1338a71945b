import { TranslateModule } from '@ngx-translate/core';
import { Component, output } from '@angular/core';
import { IfpButtonComponent } from '../../scad-insights/ifp-widgets/ifp-atoms/ifp-button/ifp-button.component';
import { buttonClass } from 'src/app/scad-insights/core/constants/button.constants';
import { FormControl, ReactiveFormsModule } from '@angular/forms';

@Component({
  selector: 'ifp-gen-ai-dashboard-feedback',
  imports: [TranslateModule, IfpButtonComponent, ReactiveFormsModule],
  templateUrl: './gen-ai-dashboard-feedback.component.html',
  styleUrl: './gen-ai-dashboard-feedback.component.scss'
})
export class GenAiDashboardFeedbackComponent {
  public close = output();
  public submit = output<string>();
  public buttonClass = buttonClass;
  public feedback = new FormControl('');

}
