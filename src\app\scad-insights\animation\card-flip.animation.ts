import { trigger, query, stagger, transition, style, group, animate } from '@angular/animations';



export const cardSequenceAnimation = (classData = '') => trigger('cardSequenceAnimations', [
  transition(':enter', [
    query(`.${classData}`, [
      style({transition: 'rotate', rotate: 'y 75deg', opacity: 0}),
      stagger(300, [
        group([
          animate('300ms ease',
            style({ rotate: 'y 0deg'} )
          ),
          animate('500ms ease',
            style({ opacity: 1, transform: 'none'} )
          )
        ])

      ])
    ]
    )
  ])

]);
