<div class="ifp-ai-report-card">
  @if (iconClass() !== "") {
    <!-- <em class="ifp-ai-report-card__icon ifp-icon" [class]="iconClass()"></em> -->
  }
  @if (consolidated()) {
    <div  class="ifp-ai-report-card__domain-card">
      <div class="ifp-ai-report-card__domain-icon-wrapper">
      @if (domainIconSelector$ | async; as icon) {
        @if ((_themeService.defaultTheme$ | async) === 'dark') {
          <img [src]="icon?.body?.light_icon ?? '../../../../assets/images/icon-placeholder.png'" width="15px" height="15px"  alt="" class="ifp-ai-report-card__domain-icon">
        } @else {
          <img [src]="icon?.body?.icon ??'../../../../assets/images/icon-placeholder.png'" alt="" width="15px" height="15px" class="ifp-ai-report-card__domain-icon">

        }
      }
    </div>
      <div class="ifp-ai-report-card__domain-name">{{ name()}}</div>
    </div>
  }


  <p class="ifp-ai-report-card__title">{{indicatorName()| translate}}</p>

      <p class="ifp-ai-report-card__text">{{ title() | translate}}</p>
      <div class="ifp-ai-report-card__bottom">
      <div class="ifp-ai-report-card__bottom-left">
      <p class="ifp-ai-report-card__value">{{percentage() | number:'1.0-3'}}%</p>
    </div>
    <div class="ifp-ai-report-card__bottom-right" >
      <div class="ifp-ai-report-card__arrow-wrapper" [ngClass]="{'ifp-ai-report-card__red': !colorDirection(),'ifp-ai-report-card__green':colorDirection()}">
        <em class="ifp-ai-report-card__arrow " [ngClass]="{'ifp-icon ifp-icon-arrow-us-up': arrowDirection(),'ifp-icon ifp-icon-arrow-us-down':!arrowDirection()}"></em>
      </div>
    </div>

  </div>
  @if (firstPeroid()&&secondPeroid()&&firstPeroid() !=='' &&secondPeroid() !== '') {
    <div  class="ifp-ai-report-card__date">
     {{secondPeroid()}} - {{firstPeroid()}}
    </div>
  }

</div>
