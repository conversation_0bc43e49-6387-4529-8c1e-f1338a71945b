import { createFeatureSelector, createSelector } from '@ngrx/store';
import { DomainDetailFilterState, categoryStateName, domainStateName, newsLetterStateName } from './domain.state';
import { domainDetailName } from './domain.action';




export const selectCategoryState = createFeatureSelector<any>(categoryStateName);
export const selectFilterState = createFeatureSelector<DomainDetailFilterState>(domainDetailName);
export const selectNewsLetterState = createFeatureSelector<any>(newsLetterStateName);


export const selectCategoryResponse = createSelector(
  selectCategoryState,
  (state: any) => {
    return state.data;
  }
);

export const selectDomainResponse = (flag: string) => createSelector(
  selectCategoryState,
  (state: any) => {
    let returnData: any;
    if (state[domainStateName][flag]) {
      returnData = state[domainStateName][flag];
    }
    return returnData;
  }
);

export const selectAllDomainResponse = createSelector(
  selectCategoryState,
  (state: any) => {
    return state[domainStateName];
  }
);

export const selectNewsLetterResponse = createSelector(
  selectNewsLetterState,
  (state: any) => {
    return state.data;
  }
);


