export interface ToolbarTabMenu {
  title_en: string;
  title_ar: string;
  icon: string;
  key: string;
  content: Content[];
}

export interface Content {
  title: string;
  key: string;
  isExpand?: boolean;
}

export interface DbToolbarIcon {
  title?: string;
  icon?: string;
  img?: string;
  key: string;
  disabled: boolean;
  selected: boolean;
}

export interface DockItem {
  key: string;
  icon: string;
  name_en: string;
  name_ar: string;
}

export interface TabChangeEvent {
  event: { badge: boolean, name: string }
  index: number
}

export interface AxisDropDown {
  name: string;
  key: string;
  checked: boolean;
  disabled: boolean;
}

export interface AxisOptions {
  title: string,
  key: string,
  options: AxisDropDown[],
  optKey: string,
  disableOption: boolean;
  multiSelect:boolean;
}

export interface TabOptions {
  name: string;
}
