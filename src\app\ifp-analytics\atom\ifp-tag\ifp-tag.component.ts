import { NgClass } from '@angular/common';
import { Component, Input, OnChanges, OnDestroy, OnInit } from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';
import { ifpColors } from 'src/app/scad-insights/core/constants/color.constants';
import { IfpTooltipDirective } from 'src/app/scad-insights/core/directives/ifp-tooltip.directive';
import { ThemeService } from 'src/app/scad-insights/core/services/theme/theme.service';
import { SubSink } from 'subsink';

@Component({
    selector: 'ifp-tag',
    imports: [TranslateModule, NgClass, IfpTooltipDirective],
    templateUrl: './ifp-tag.component.html',
    styleUrl: './ifp-tag.component.scss'
})
export class IfpTagComponent implements OnInit, OnDestroy, OnChanges{
  @Input() tagName: string = '';
  @Input() color: string = ifpColors.secondaryBlue;
  @Input() background: string = ifpColors.violetLight;
  @Input() colorDark!: string;
  @Input() backgroundDark!: string;
  @Input() border = true;
  @Input() icon!: string;
  @Input() infoHead!: string;
  @Input() info!: string;
  @Input() isBoxView: boolean = false;
  public backgroundValue = '';
  public colorValue = '';
  public subs =  new SubSink();


  constructor(public _theme: ThemeService) {}

  ngOnInit(): void {
    this.subs.add(
      this._theme.defaultTheme$ .subscribe((data)=> {
        if (data === 'dark') {
          if (this.colorDark ) {
            this.colorValue = this.colorDark;
          }
          if (this.backgroundDark ) {
            this.backgroundValue = this.backgroundDark;
          }
        } else {
          if (this.color ) {
            this.colorValue = this.color;
          }
          if (this.background ) {
            this.backgroundValue = this.background;
          }
        }

      }));

  }

  ngOnChanges(): void {
    if (this.color ) {
      this.colorValue = this.color;
    }
    if (this.background ) {
      this.backgroundValue = this.background;
    }
  }

  ngOnDestroy(): void {
    this.subs.unsubscribe();
  }
}
