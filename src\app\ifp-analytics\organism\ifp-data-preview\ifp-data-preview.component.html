
<div class="ifp-data-preview" [ngClass]="{'ifp-data-preview__full': fullScreen}" #preview>
  @if (fullScreen) {
    <div (click)="goBack()" class="ifp-link ifp-data-preview__head-back" [title]="'Back' | translate"><em class="ifp-icon ifp-icon-left-arrow"></em> {{'Back' | translate}}</div>
  }

  <div class="ifp-data-preview__menu">
    @if(!loader) {
      <app-ifp-tab [tabData]="menuTabs" [isSmall]="true" [tooltipDisabled]="true" [selectionType]="'key'" [selectedTab]="selectedTabView.event.key" [showIcon]="false" (selectedTabEvent)="changeTabView($event)" class="ifp-data-preview__switch-view"></app-ifp-tab>
    }

    <!-- <ifp-button class="ifp-data-preview__menu-btn" [buttonClass]="modeSelected === 'preview' ? buttonClass.primary : buttonClass.secondary" [label]="'Preview'" (ifpClick)="modeSelected = 'preview'"></ifp-button>
    <ifp-button class="ifp-data-preview__menu-btn" [buttonClass]="modeSelected === 'log' ? buttonClass.primary : buttonClass.secondary" [label]="'Log'" (ifpClick)="modeSelected = 'log'"></ifp-button> -->
  </div>
  @if (modeSelected === 'preview' && !loader) {
    <div class="ifp-data-preview__table" [ngClass]="{'ifp-data-preview__table--adjust': showProbabilty&&  tableView !== 'normal'}">
      @if (!fullScreen) {   <div #drager >
        <!-- <em class="ifp-icon ifp-icon-vertical-arrows ifp-data-preview__resize" ></em> -->
        </div>}

      <div class="ifp-data-preview__toolbar">
        <h3 class="ifp-data-preview__title">{{'Preview' | translate}}</h3>
        <div class="ifp-data-preview__tools">
          <span class="ifp-data-preview__duration">{{'Loaded in' | translate}} {{responseTime}} ms</span>
          <ifp-panel-dropdown class="ifp-data-preview__dropdown" [options]="columListDropdown" (multiSelected)="multiSelected($event)"
            [key]="'value'"></ifp-panel-dropdown>
            @if(metaData) {
          <em class="ifp-icon ifp-icon-col-view ifp-data-preview__tool"
            [ngClass]="{'ifp-data-preview__tool--active': tableView === 'normal'}" (click)="setTableView('normal')"
            [appIfpTooltip]="'View Data' | translate"></em>

              <em class="ifp-icon ifp-icon-detail-view-fill ifp-data-preview__tool"
              [ngClass]="{'ifp-data-preview__tool--active': tableView === 'detail'}" (click)="setTableView('detail')"
              [appIfpTooltip]="'Column Details' | translate"></em>
            }
            @if (this.tableDataDetail.length !=0 && !loader) {
          <em  [appIfpTooltip]="'Download Preview' | translate" class="ifp-icon ifp-icon-download-line ifp-data-preview__tool" (click)="downloadFileClick()"></em>
            }
        </div>
      </div>
      <div class="ifp-data-preview__inner" [ngClass]="{'ifp-data-preview__full-inner': fullScreen}" #table>
        @if (this.tableDataDetail.length !=0) {
          <ifp-data-table [translation]="tableView === 'detail' ? true :false" [headingTranslate]="tableView === 'detail'"  [rowClickEnable]="tableView !== 'normal'" [tableHead]="_preService.previewTableData.heading" [tableData]="_preService.previewTableData.data" (rowEventEmitter)="rowClick($event)"
          class="ifp-data-preview__table"></ifp-data-table>
        }@else {
          @if(loader) {
            <div class="ifp-center-loader">
              <app-ifp-spinner></app-ifp-spinner>
            </div>
          } @else {
            <app-ifp-no-data></app-ifp-no-data>
          }
        }

      </div>
      @if( tableView === 'normal') {
        <div class="ifp-data-preview__pagination-wrapper">
        <app-pagination class="ifp-data-preview__pagination" [offset]="offset" [limit]="limit" [customPagination]="true"
        [size]="size" (pageChange)="onPageChange($event)"
        (limitChange)="limitChanged($event)"></app-pagination>

        @if (totalRows) {
          <p class="ifp-data-preview__desc">{{'Showing' | translate}} <span class="ifp-data-preview__strong">{{offset+1}}</span> {{'to' | translate}} <span class="ifp-data-preview__strong">{{(page * limit) < size ? (page * limit) : size}}</span> {{'of' | translate}}
            {{size}}{{' entries. The data contains a total of' | translate}} <span class="ifp-data-preview__strong">{{totalRows}}</span> {{'rows and' | translate}} <span class="ifp-data-preview__strong">{{totalKeys}}</span>
            {{'columns.' |translate}}</p>
        }
      </div>
      }
    </div>
    @if (showProbabilty &&  tableView !== 'normal') {
    <ifp-probability-card class="ifp-data-preview__prob" [uniqueColor]="uniqueColor" [uniqueName]="uniqueName" [uniqueValues]="uniqueValue"  [name]="probalilityName" [catogory]="probabilityheading" [chartValues]="probabilityvalues"></ifp-probability-card>
    }
  } @else {
    <div class="ifp-data-preview__toolbar">
      <h3 class="ifp-data-preview__title">{{'Log' | translate}}</h3>
    </div>
    <div class="ifp-data-preview__inner" [ngClass]="{'ifp-data-preview__full-inner': fullScreen}" #log>
      @if (_preService.logs().length !=0 ) {
      <ifp-data-table [translation]="false"
        [tableHead]="logHeading" [tableData]="_preService.logs()"
        class="ifp-data-preview__table ifp-data-preview__table--logs"></ifp-data-table>
      } @else if(loader && _preService.logs().length ==0) {
        <div class="ifp-center-loader">
          <app-ifp-spinner></app-ifp-spinner>
        </div>
      }
       @else {
      <app-ifp-no-data [message]="'Please run the workflow to view the Logs.'"></app-ifp-no-data>
      }
    </div>
  }
</div>
