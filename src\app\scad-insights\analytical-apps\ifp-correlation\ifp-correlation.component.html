<div class="ifp-container">
  <app-ifp-breadcrumbs [pageData]="pageData"></app-ifp-breadcrumbs>
</div>

<div class="ifp-correlation">
  <div class="ifp-container">
    <h2 class="ifp-node__title">{{title | translate}}</h2>
    <div class="ifp-correlation__wrapper">
      <div class="ifp-correlation__header">
        <!-- tab sec start -->
        <div class="ifp-correlation__tab-sec">
          @for (item of tabData | keyvalue; track i; let i = $index) {
            <ul class="ifp-correlation__tab">
              @for (option of item.value.options; track j; let j = $index) {
                <li class="ifp-correlation__tab-item" [ngClass]="{'ifp-correlation__tab-item--active': option.isSelected}" (click)="selectFilter(item.key, option)">{{option.name | translate}}</li>
              }
            </ul>
          }

        </div>
        <!-- tab sec end -->

        <div class="ifp-correlation__download-wrapper">
          <ifp-icon-button class="ifp-correlation__download-btn" [iconClass]="'ifp-icon-download'" [isActive]="showDownload" (btnClick)="showDownload = !showDownload" appOutsideClick (outsideClick)="outsideClick()"></ifp-icon-button>
          <ifp-tool-download [name]="title" [data]="excelData" [termsAndConditions]="true" [tncState]="tncState" [indicatorId]="analyticalId" class="ifp-correlation__download" [ngClass]="{'ifp-correlation__download--active': showDownload}" (downloadEvent)="onDownloadClick()">
            <div class="ifp-correlation__download-filters">
              @for (item of tabData | keyvalue; track i; let i = $index) {
                <p class="ifp-correlation__download-filter-item"><span class="ifp-correlation__download-filter-title">{{item.key}}</span>:
                  @for (option of item.value.options; track j; let j = $index) {
                    {{option.isSelected ? option.name : ''}}
                  }
                </p>
                <!-- <ul class="ifp-correlation__tab">
                  @for (option of item.value.options; track j; let j = $index) {
                    <li class="ifp-correlation__tab-item" [ngClass]="{'ifp-correlation__tab-item--active': option.isSelected}" (click)="selectFilter(item.key, option)">{{option.name | translate}}</li>
                  }
                </ul> -->
              }
            </div>
            <div class="ifp-correlation__card-wrapper">
              @for (card of chartCards; track card; let i = $index) {
                @if (card.seriesMeta[0].data.length) {
                  <ifp-correlation-card [chartCardData]="card.seriesMeta[0]" class="ifp-correlation__card" [ngClass]="{'ifp-correlation__card--full': card.seriesMeta[0].data.length >= 10}"></ifp-correlation-card>
                }
              } @empty {
                <app-ifp-card-loader [type]="loaderType.large" class="ifp-correlation__no-data"></app-ifp-card-loader>
              }
            </div>
          </ifp-tool-download>
        </div>
      </div>
      @if (security) {
      <div class="ifp-correlation__security">
        <ifp-tag class="ifp-correlation__tag" [isBoxView]="true" [background]="'transparent'" [tagName]="security.name" [color]="security.color ?? ''" [infoHead]="('Data classification' | translate) + ': ' + security.name" [info]="security.description ?? ''"></ifp-tag>
      </div>
      }
      <div class="ifp-correlation__card-wrapper">
        @for (card of chartCards; track i; let i = $index) {
          @if (card.seriesMeta[0].data.length) {
            <ifp-correlation-card [chartCardData]="card.seriesMeta[0]" class="ifp-correlation__card" [ngClass]="{'ifp-correlation__card--full': card.seriesMeta[0].data.length >= 10}"></ifp-correlation-card>
          }
        } @empty {
          <app-ifp-card-loader [type]="loaderType.large" class="ifp-correlation__no-data"></app-ifp-card-loader>
        }
      </div>
    </div>
  </div>
</div>
