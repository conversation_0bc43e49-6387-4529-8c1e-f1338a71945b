<div class="ifp-file-upload" [ngClass]="{'ifp-file-upload--lg': !isImage}">
  @if (enableInfo()) {
    <div class="ifp-file-upload__info">
      <em class="ifp-file-upload__info-icon ifp-icon ifp-icon-info-round"></em>
      <div  class="ifp-file-upload__info-tooltip ifp-tooltip">
        <ng-content select=".info">
        </ng-content>
      </div>
    </div>
  }


  @if (!previewUrl || dragOnly) {
  <div (click)="openFileUpload()" class="ifp-file-upload__inner" appDragNDrop (dragover)="handleDragOver($event)" (drop)="onDrop($event)" [ngClass]="{'ifp-file-upload__inner--uploaded': previewUrl}">
      @if (isImage) {
        <svg width="19" height="15" viewBox="0 0 19 15" fill="none" xmlns="http://www.w3.org/2000/svg" class="ifp-file-upload__file-icon"><path d="M0 3.11321H17.5156C18.3354 3.11321 19 3.74677 19 4.5283V13.5849C19 14.3664 18.3354 15 17.5156 15H1.48438C0.664577 15 0 14.3664 0 13.5849V3.11321Z" fill="#4294FF"/><path d="M0 0.566038C0 0.253424 0.265831 0 0.59375 0H6.74679C6.93336 0 7.10908 0.0836033 7.22125 0.225735L9.5 3.11321H0V0.566038Z" fill="#376CFB"/></svg>
      } @else {
        <div [class]="'ifp-file-upload__logo'+ ' ' + iconBackgroung" >
          <em class="ifp-icon ifp-icon-cloud-upload"></em>
        </div>
      }
      <div class="ifp-file-upload__text-wrapper">
        <p class="ifp-file-upload__text">{{dragAndDropText | translate}} <span class="ifp-link">{{'Browse' | translate}}</span></p>
        @if (!isImage) {
          <p class="ifp-file-upload__note">
            @if (supportText !== '' && supportText) {
              {{supportText | translate}}:
            }

            @for(type of allowedExtensions; track $index) {
            {{type}}@if ($index != allowedExtensions.length - 1) {,}
            }&nbsp;&nbsp;|&nbsp;&nbsp;{{'Max size' | translate}}: <span  class="ifp-file-upload__limit">  {{maxSizeLimit / 1024}} MB</span>
          </p>
        }
      </div>
    </div>
    } @else {
      <div class="ifp-file-upload__inner">
        <div class="ifp-file-upload__preview">
          @if (isImage) {
            <img [src]="previewUrl" alt="Preview" class="ifp-file-upload__file-preview">
          } @else {
            <p class="ifp-file-upload__file-name">{{selectedFile?.name}}</p>
            @if (progress) {
              <div class="ifp-file-upload__bar-wrapper">
                <div class="ifp-file-upload__bar" [ngStyle]="{'width': progress + '%'}">{{progress}} % </div>
              </div>
            }
          }
        </div>
      </div>
      @if (!hideDelete) {
      <em class="ifp-icon ifp-icon-trash ifp-file-upload__del-logo" (click)="deleteFile()"></em>
      }
    }
</div>
<input hidden #input type="file" (change)="onBrowse($event)" [accept]="acceptFormat" id="dbLogo" #fileUploader class="ifp-file-upload__input">
<!-- @if (fileName && !previewUrl) {
  <div class="ifp-file-upload__uploaded-file">
    <em class="ifp-icon ifp-icon-text ifp-file-upload__file-icon"></em>
    <p class="ifp-file-upload__file-name">{{fileName}}</p>
  </div>
} -->


<app-ifp-modal #alertModal>
  <app-ifp-remove-card [text]="alertText" (firstButtonEvent)="openOrCloseModel($event)" (secondButtonEvent)="openOrCloseModel($event)">
  </app-ifp-remove-card>
</app-ifp-modal>
