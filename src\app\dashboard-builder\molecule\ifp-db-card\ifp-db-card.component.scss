@use "../../../../assets/ifp-styles/abstracts/index" as *;

:host {
  position: relative;
}

.ifp-db-card {
  border-radius: 10px;
  border: 1px solid $ifp-color-grey-7;
  padding: $spacer-3;
  background-color: $ifp-color-white;
  overflow: hidden;
  position: relative;

  &__head-sec {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: $spacer-3;
  }

  &__icon {
    width: 30px;
    height: 30px;
    padding: $spacer-2;
    border-radius: 5px;
    display: inline-block;
  }

  &__description,
  &__title {
    display: block;
    width: 100%;
    outline: none;
    resize: none;
    background-color: transparent;
  }

  &__title {
    font-size: $ifp-fs-4;
    margin-bottom: $spacer-2;
    min-height: 42px;
    border: 1px solid transparent;
    color: $ifp-color-black;
    &:focus {
      background-color: $ifp-color-violet-light;
      border: 1px solid $ifp-color-blue-med;
      padding-right: $spacer-5;
      outline: none;

      &+.ifp-icon {
        opacity: 1;
        visibility: visible;
      }
    }
  }

  &__description {
    font-size: $ifp-fs-4;
    // min-height: 42px;
    color: inherit;
    border: 0;
  }

  &__description-preview {
    width: 100%;
  }

  &__header-description-container {
    background-color: $ifp-color-grey-bg;
    border-radius: 5px;
    display: flex;
    align-items: center;
    padding: $spacer-2;


    &--placeholder {
      .ifp-db-card__description {
        color: $ifp-color-grey-disabled;
        background-color: transparent;
      }
    }
  }

  &__header-title-container {
    position: relative;
    margin-bottom: $spacer-2;
    .ifp-db-card__title {
      margin-bottom: $spacer-0;
    }
    .ifp-icon {
      font-size: $ifp-fs-5;
      opacity: 0;
      visibility: hidden;
      transition: opacity 0.3s;
      position: absolute;
      top: 50%;
      right: (-$spacer-2);
      transform: translateY(-50%);
    }

    &:hover {
      .ifp-icon {
        opacity: 1;
        visibility: visible;
      }
    }

    &--focus {
      .ifp-db-card__title {
        background-color: $ifp-color-violet-light;
        border: 1px solid $ifp-color-blue-med;
        outline: none;
        padding: $spacer-2 $spacer-4 $spacer-2 $spacer-2;
      }
      .ifp-icon {
        right: $spacer-2;
      }
    }
  }

  &__value {
    font-size: $ifp-fs-8;
    font-weight: $fw-bold;
  }

  &__value-unit {
    font-weight: $fw-regular;
    font-size: $ifp-fs-2;
  }

  &__compared {
    font-weight: $fw-semi-bold;
    margin-left: $spacer-1;
    margin-right: $spacer-1;

    &--red {
      color: $ifp-color-red;
    }

    &--green {
      color: $ifp-color-green-dark;
    }
  }

  &__chart {
    margin-bottom: $spacer-3;
  }

  &__selected-title {
    margin-bottom: $spacer-3;
  }

  &__ifp-icon {
    font-size: $ifp-fs-7;
  }

  // &__description {
  //   margin-top: $spacer-3;
  // }
  &__action-icons {
    margin-left: auto;

    .ifp-icon {
      margin-left: $spacer-2;
      font-size: $ifp-fs-5;
    }
  }

  &__domain-wrapper {
    display: flex;
    width: 100%;

    .ifp-icon {
      cursor: pointer;
    }
  }

  &__arrows {
    display: none;
  }

  &__cust {
    // height: calc(100% - 40px);
    display: flex;
    align-items: center;
    justify-content: center;

  }

  &__cust-inner {
    margin: $spacer-0 auto;
    width: 85%;
    max-width: 270px;

    .ifp-db-card__title {
      text-align: center;
    }
  }

  &__chart-set {
    display: flex;
    flex-wrap: wrap;
    margin: $spacer-0 (
      -$spacer-3
    );
}

&__chart-icon {
  width: calc(33.33% - (2 * $spacer-3));
  margin: $spacer-2 $spacer-3;
}

&--selected {
  border: 1px solid $ifp-color-secondary-blue-dark;

  &~.ifp-db-card__arrows {
    display: inline-block;
    position: absolute;
    background-color: $ifp-color-secondary-blue-dark;
    border-radius: 10px;
    background-color: var(--ifp-color-secondary-blue-dark);
    font-size: $ifp-fs-1;
    color: $ifp-color-white-global;
    padding: $spacer-2 2px $spacer-1;

    &--h {
      top: 50%;

      &:nth-of-type(1) {
        right: 0;
        transform: translate(50%, -50%);
      }

      &:nth-of-type(2) {
        left: 0;
        transform: translate(-50%, -50%);
      }
    }

    &--v {
      left: 50%;

      &:nth-of-type(3) {
        top: 0;
        transform: translate(-50%, -50%) rotate(90deg);
      }

      &:nth-of-type(4) {
        bottom: 0;
        transform: translate(-50%, 50%) rotate(90deg);
      }
    }
  }
}

&--select-mode {
  cursor: pointer;
  transition: 0.3s;

  &:hover {
    transform: scale(1.03);
    box-shadow: 0 0 16px $ifp-color-black-16;
  }
}

&__txt-icon {
  margin-bottom: $spacer-2;
}

&__custom-card {
  width: 100%;
  display: block;
}
}

:host-context([dir="rtl"]) {
  .ifp-db-card {
    &__title {
      &:focus {
        padding-left: $spacer-5;
        padding-right: $spacer-0;
      }
    }

    &__header-title-container {
      .ifp-icon {
        left: $spacer-2;
        right: auto;
      }

      &--focus {
        padding-left: $spacer-5;
        padding-right: $spacer-0;
      }
    }

    &__compared {
      margin-right: $spacer-1;
      margin-right: $spacer-0;
    }

    &__action-icons {
      margin-right: auto;
      margin-left: $spacer-0;

      .ifp-icon {
        margin-right: $spacer-2;
        margin-left: $spacer-0;
      }
    }

    &--selected {
      &--h {
        &:nth-of-type(1) {
          left: 0;
          right: auto;
          transform: translate(-50%, -50%)
        }

        &:nth-of-type(2) {
          right: 0;
          left: auto;
          transform: translate(50%, -50%);
        }
      }

      &--v {
        right: 50%;
        left: auto;

        &:nth-of-type(3) {
          transform: translate(50%, -50%) rotate(90deg);
        }

        &:nth-of-type(4) {
          transform: translate(50%, 50%) rotate(90deg);
        }
      }
    }
  }
}

@include ipad {
  .ifp-db-card {
    &__head-sec,
    &__header-description-container {
      position: relative;
    }
    &__head-sec,
    &__header-description-container,
    &__header-title-container {
      z-index: 5;
    }

  }
}
