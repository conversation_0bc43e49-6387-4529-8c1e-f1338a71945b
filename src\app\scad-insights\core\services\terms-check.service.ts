import { Injectable } from '@angular/core';
import { IFPMsalService } from './IFP-msal.service';
import { HttpService } from './http/http.service';
import { slaApis } from '../apiConstants/sla-api.constants';
import { Router } from '@angular/router';
import { BehaviorSubject } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class TermsCheckService {
  public termsStatus = false;
  public termsData$ = new BehaviorSubject({});
  public termsData: any;
  public username!:string;
  public organization!: string;
  constructor(private _msal: IFPMsalService, private _http: HttpService, private _router:Router) {
    this.username = this._msal.getLoginData?.account?.username;
    this.organization = this.username ? this.username.split('@')[1] : '';
  }

  isTnCAccepted() {
    return this._http.get(`${slaApis.checkTermsConditions}/${this.username}/${this.organization}/1`).subscribe((data) => {
      this.termsStatus = data.tcAcceptStatus;
      if (this.termsStatus) {
        // this._router.navigateByUrl('/home');
      } else {
        this._router.navigateByUrl('/accept-terms-and-conditions');
      }
    });
  }

  onTermsAccept(data: any) {
    return this._http.post(slaApis.acceptTermsConditions, data);
  }
}
