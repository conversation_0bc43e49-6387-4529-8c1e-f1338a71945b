<div class="ifp-auto-imp-score">
  <div class="ifp-auto-imp-score__head-wrapper">
    <h3  class="ifp-auto-imp-score__head">
      {{(result ? 'Results of Target Variable Contribution' : 'Target Variable Contribution')  | translate}}
    </h3>
    <!-- <div  class="ifp-auto-imp-score__head-btn-wrapper">
      @if (iterationList.length < 4) {
        <ifp-button [label]="'Re Analyze'" [iconClass]="'ifp-icon-detial-view'" [buttonClass]="buttonClass.secondary" class="ifp-auto-imp-score__btn-analyze" (ifpClick)="reAnalyze()"></ifp-button>
      }
       <ifp-button [label]="'I’m satisfied with the Result'" [iconClass]="'ifp-icon-rightarrow'" [buttonClass]="buttonClass.primary"  (ifpClick)="runScenario()" class="ifp-auto-imp-score__btn-result"></ifp-button>
    </div> -->

  </div>
  <div class="ifp-auto-imp-score__content">
    <div class="ifp-auto-imp-score__content-head" [ngClass]="{'ifp-auto-imp-score__content-head--align-right': result}">
      @if (!result) {
      <app-ifp-tab [isSmall]="true" [tabData]="labelList" [tooltipDisabled]="true" [selectedTab]="indexTableSelection" (selectedTabEvent)="selectedValue($event)"></app-ifp-tab>
      }
       <app-ifp-tab [isSmall]="true" [tabData]="labelCompare" [tooltipDisabled]="true" [selectedTab]="indexResultSelection" (selectedTabEvent)="selectedValueResult($event)"></app-ifp-tab>
    </div>

      <div  class="ifp-auto-imp-score__sub-head">
        {{'Model Summary' | translate}}
      </div>



  </div>
  @if (result) {
    <div class="ifp-auto-imp-score__itr-wrapper">
      <div  class="ifp-auto-imp-score__result-tabs">
        @for (item of iterationList; track item; let index = $index) {
          <div class="ifp-auto-imp-score__result-tab" (click)="activeTab(item)" [ngClass]="{'ifp-auto-imp-score__result-tab--active': selectedIteration === item.object_id}">
            {{item.name}}
          </div>
        }


      </div>
      <div  class="ifp-auto-imp-score__result-chart">
        <div class="ifp-auto-imp-score__result-name-outer">
        </div>
        <app-ifp-analytic-line-chart [isDatalabel]="false" [enableAnimation]="false" class="ifp-exp__highchart"  [yaxisLabel]="'Target Variable Contribution'| translate" [legend]="false" [chartData]="yAxis" [xAxisCatogory]="xAxis" [xAxisLabelType]="'category'| translate"></app-ifp-analytic-line-chart>
      </div>
      <div class="ifp-auto-imp-score__result-divider"></div>
      <div  class="ifp-auto-imp-score__result-rank">
        <ifp-data-table [translation]="false" [headerSettings]="headingSettings" [tableHead]="previewTableData.heading"
        [tableData]="previewTableData.data" (sortEvent)="sortEvent($event)"
        class="ifp-auto-imp-score__table"></ifp-data-table>
      </div>

    </div>
  }@else {
    <div  class="ifp-auto-imp-score__itr-wrapper" [ngClass]="{'ifp-auto-imp-score--bar': indexTableSelection === 1}">
      <div  class="ifp-auto-imp-score__itr-outer">
        <div class="ifp-auto-imp-score__itr">
          <div class="ifp-auto-imp-score__variable-wrapper">
            <div class="ifp-auto-imp-score__variable-head" #variableHead >
              {{'Variables' | translate}}

            </div>


          </div>

              @for (item of iterationList; track item.name ; let index = $index) {
              <div class="ifp-auto-imp-score__chart" >
                <ifp-auto-ml-chart-data [number]="index+ 1" #chart [chartListValue]="indexTableSelection === 1" (click)="selectIteration(item)" [selection]="selectedIteration === item.object_id" [topLabel]="item.name" class=" ifp-itr-card__content"  [chartHeading]="'Accuracy'" [chartValue]="(item?.metadata?.score ?? 0)/ 100" [chartList]="[
                {name: ('MAE' | translate) + ' ', value: item?.metadata?.['model metrics']?.MAE ?? 0},
                {name: ('MSE' | translate) + ' ', value: item?.metadata?.['model metrics']?.MSE ??0},
                {name: ('R-SQUARED'| translate) +' ', value: item?.metadata?.['model metrics']?.R2 ?? 0},
              ]"></ifp-auto-ml-chart-data>
              </div
              >
            }
            </div>
            <div class="ifp-auto-imp-score__itr ifp-auto-imp-score__itr--variables">
              <div class="ifp-auto-imp-score__variable-wrapper">
            <div class="ifp-auto-imp-score__variables">
              @for (item of columnList; track item ; let index = $index) {
              <div class="ifp-auto-imp-score__variable" [appIfpTooltip]="item">
                  {{item}}
              </div>
            }
            </div>
          </div>
            @for (item of iterationList; track item.name ; let index = $index) {
            <ifp-auto-ml-column-chart   class="ifp-auto-imp-score__chart" [columnList]="columnList"  #columnChart [iterationItem]="item" [bar]="indexTableSelection === 1"
          [topLabel]="item.name"
          [selection]="selectedIteration === item.object_id"
          ></ifp-auto-ml-column-chart>
            }
            </div>
      </div>


    </div>
  }




</div>
