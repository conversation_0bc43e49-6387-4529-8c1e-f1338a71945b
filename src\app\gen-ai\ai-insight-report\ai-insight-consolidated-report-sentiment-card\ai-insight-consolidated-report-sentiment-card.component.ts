import {
  Component,
  inject,
  input,
  signal,
  WritableSignal,
} from '@angular/core';
import { IFPHighChartsComponent } from '../../../scad-insights/ifp-widgets/charts/ifp-highcharts.component';
import { Chart } from 'highcharts/highcharts.src';
import { TranslateModule } from '@ngx-translate/core';
import {
  InsightSentimentanalysis,
  Overallimpactstatus,
} from '../interface/insights.interface';
import { NgClass } from '@angular/common';
import { ThemeService } from 'src/app/scad-insights/core/services/theme/theme.service';

@Component({
  selector: 'ifp-ai-insight-consolidated-report-sentiment-card',
  imports: [IFPHighChartsComponent, TranslateModule, NgClass],
  templateUrl: './ai-insight-consolidated-report-sentiment-card.component.html',
  styleUrl: './ai-insight-consolidated-report-sentiment-card.component.scss',
})
export class AiInsightConsolidatedReportSentimentCardComponent {
  public sentimentanalysis = input<InsightSentimentanalysis>();
  public seriesData: WritableSignal<
    {
      name: string;
      data: {
        name: string;
        y: number;
        color: string;
      }[];
    }[]
  > = signal([
    {
      name: 'impact',
      data: [
        { name: 'Negative', y: 20, color: '#E33C1A' },
        { name: 'Postive', y: 50, color: '#81B76A' },
        { name: 'Neutral', y: 30, color: '#F5AB34' },
      ],
    },
  ]);
  public _themeService = inject(ThemeService);
  public status = status;
  public plot = {
    pie: {
      innerSize: '85%', // Thickness of the donut
      borderWidth: 5,
      borderRadius: 60,
      borderColor:
        this._themeService.defaultTheme === 'light' ? '#fff' : '#000',
      dataLabels: {
        enabled: false, // Disable data labels
      },
    },
    series: {
      animation: false,
    },
  };

  public centerValue = (chart: Chart) => {
    const centerText = `<div text-anchor="middle"  class="ifp-high-chart__donut-center">
<span   class="ifp-high-chart__donut-heading" ></span>
    <br/>
<span dy="10" class="ifp-high-chart__donut-value-bold">Overall</span>
  <br/>
<span dy="10" class="ifp-high-chart__donut-value-bold">PostiveImpact</span>
</div>`;
    if ((chart as any).centerImage) {
      (chart as any).centerImage.destroy();
    }
    const centerImage = '../../../../assets/images/smile/Smiley.svg'; // Replace with your image URL
    const imageWidth = 50; // Width of the image
    const imageHeight = 50; // Height of the image

    (chart as any).centerImage = chart.renderer
      .image(
        centerImage,
        chart.plotLeft + chart.plotWidth / 2 - imageWidth / 2,
        chart.plotTop + chart.plotHeight / 2 - imageHeight / 2 - 20,
        imageWidth,
        imageHeight
      )
      .add();

    if ((chart as any).centerLabel) {
      (chart as any).centerLabel.destroy();
    }
    (chart as any).centerLabel = chart.renderer
      .label(centerText, 0, 0)
      .attr({
        zIndex: 5,
      })
      .add();
    (chart as any).centerLabel.attr({
      x: chart.plotLeft + chart.plotWidth / 2,
      y: chart.plotHeight / 2, // Adjust for vertical centering
    });
  };
  public enableAnimation = input(true);
  public insights = input(false);
  public leagents: Overallimpactstatus[] = [];

  ngOnChanges(): void {
    this.plot = {
      pie: {
        innerSize: '85%', // Thickness of the donut
        borderWidth: 5,
        borderColor:
          this._themeService.defaultTheme === 'light' ? '#fff' : '#000',
        borderRadius: 60,
        dataLabels: {
          enabled: false, // Disable data labels
        },
      },
      series: {
        animation: this.enableAnimation(),
      },
    };

    this._themeService.defaultTheme$.subscribe((data) => {
      this.plot = {
        pie: {
          innerSize: '85%', // Thickness of the donut
          borderWidth: 5,
          borderColor:
            this._themeService.defaultTheme === 'light' ? '#fff' : '#000',
          borderRadius: 60,
          dataLabels: {
            enabled: false, // Disable data labels
          },
        },
        series: {
          animation: this.enableAnimation(),
        },
      };
    });
    this.seriesData.update(() => {
      const value: {
        name: string;
        data: {
          name: string;
          y: number;
          color: string;
        }[];
      }[] = [
        {
          name: 'impact',
          data: [],
        },
      ];
      this.leagents = Object.values(
        this.sentimentanalysis()?.overall_impact_status ?? {}
      );
      this.leagents.forEach((data) => {
        value[0].data.push({
          name: data.status_name,
          y: +(typeof data?.value === 'string'
            ? data.value.replace('%', '')
            : String(data?.value ?? '0')),
          color: '#' + data.color,
        });
      });
      return value;
    });
    this.centerValue = (chart: Chart) => {
      const centerText = `<div text-anchor="middle"  class="ifp-high-chart__donut-center">
<span   class="ifp-high-chart__donut-heading" ></span>
    <br/>
<span dy="10" class="ifp-high-chart__donut-value-bold">Overall</span>
  <br/>
<span dy="10" class="ifp-high-chart__donut-value-bold">${
        this.sentimentanalysis()?.overall_impact
      }</span>
</div>`;
      let centerImage = '';
      if ((chart as any).centerImage) {
        (chart as any).centerImage.destroy();
      }
      if (this.sentimentanalysis()?.key === status.neg) {
        centerImage = '../../../../assets/images/smile/SmileySad.svg';
      } else if (this.sentimentanalysis()?.key === status.neu) {
        centerImage = '../../../../assets/images/smile/SmileyMeh.svg';
      } else if (this.sentimentanalysis()?.key === status.pos) {
        centerImage = '../../../../assets/images/smile/Smiley.svg';
      }
      // Replace with your image URL
      const imageWidth = 50; // Width of the image
      const imageHeight = 50; // Height of the image

      (chart as any).centerImage = chart.renderer
        .image(
          centerImage,
          chart.plotLeft + chart.plotWidth / 2 - imageWidth / 2,
          chart.plotTop + chart.plotHeight / 2 - imageHeight / 2 - 20,
          imageWidth,
          imageHeight
        )
        .add();

      if ((chart as any).centerLabel) {
        (chart as any).centerLabel.destroy();
      }
      (chart as any).centerLabel = chart.renderer
        .label(centerText, 0, 0)
        .attr({
          zIndex: 5,
        })
        .add();
      (chart as any).centerLabel.attr({
        x: chart.plotLeft + chart.plotWidth / 2,
        y: chart.plotHeight / 2, // Adjust for vertical centering
      });
    };
  }
}

const status = {
  neg: 'NEG',
  neu: 'NEU',
  pos: 'POS',
};
