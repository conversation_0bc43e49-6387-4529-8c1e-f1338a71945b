@use "../../../../assets/ifp-styles/abstracts/index" as *;
@use "../../../../assets/ifp-styles/components/ai-insights" as *;
.ifp-ai-report {

  &__key-drivers {
    color: $ifp-color-white-global;
    margin-bottom: $spacer-5;
    border-radius: 10px;
    padding:$spacer-5 $spacer-5 $spacer-3 ;
    background-image: linear-gradient(-7deg, #012556, #0D5499);
  }
  &__date {
    background-color: $ifp-color-white-24;
    font-weight: $fw-medium;
    border-radius: 50px;
    white-space: nowrap;
    padding: $spacer-2 $spacer-4;
  }
  &__cards-wrapper {
    color: $ifp-color-primary-grey;
    display: flex;
    flex-wrap: wrap;

    margin: $spacer-0 (-$spacer-3) ($spacer-0);
    justify-content: center;
  }
  &__desc{
    width: calc(100% - 100px ) ;
    white-space: pre-wrap;
    font-size:$ifp-fs-4 ;
  }
  &__drivers-desc {
    display: flex;
    justify-content: space-between;
  }

  &__indicator-card {
    width: calc(25% - (2 * $spacer-3));
    margin: 16px;
  }
  &__text-area {
    background: transparent;
    width: 100%;
    border: 0px;
    color: $ifp-color-white-global;
    resize: none;
    min-height: 40px;
    padding: 0;
    &--bottom {
      margin-bottom: $spacer-3;
    }
  }
 }
 @include desktop-sm {
  .ifp-ai-report {
    &__indicator-card {
      width: calc(50% - (2 * $spacer-3));
    }
  }
}
 @include mobile {
  .ifp-ai-report {
    &__indicator-card {
      width: 100%;
    }
  }
}
