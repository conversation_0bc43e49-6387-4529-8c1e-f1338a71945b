import { createAction, props } from '@ngrx/store';



export const loadCategoryName = '[categoryComponent] Domain navigation list loaded';
export const loadCategorySuccessName= '[categoryComponent] Domain navigation list loaded success';
export const loadDomainComponentName = '[domainComponent] Domain navigation list loaded';
export const loadDomainComponentSuccessName= '[domainComponent] Domain navigation list loaded success';
export const domainDetailName='[Domain-Detail]';

export const loadCategory = createAction(loadCategoryName, props<{searchString:string}>());
export const loadCategorySuccess = createAction(loadCategoryName, props<{data: any, status?: boolean, errorMessage?: string, loader?:boolean}>());


export const loadNewsLetterName = '[newsLetterComponent] NewsLetter list loaded';
export const loadNewsLetter = createAction(loadNewsLetterName);
export const loadNewsLetterSuccess = createAction(loadNewsLetterName, props<{data: any, status?: boolean, errorMessage?: string, loader?:boolean}>());
