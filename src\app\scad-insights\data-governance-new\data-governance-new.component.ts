import { ChangeDetectionStrategy, ChangeDetectorRef, Component, inject, <PERSON><PERSON><PERSON><PERSON>, OnInit, signal, ViewChild, WritableSignal } from '@angular/core';
import { IfpBreadcrumbsComponent } from "../ifp-widgets/ifp-molecules/ifp-breadcrumbs/ifp-breadcrumbs.component";
import { TranslateModule } from '@ngx-translate/core';
import { governanceTab, overviewTitles, productIcons, statusList, tabKeys } from './ifp-data-governance.constants';
import { IfpTabComponent } from "../ifp-widgets/ifp-molecules/ifp-tab/ifp-tab.component";
import { IfpDataGovernaceValueCardComponent } from "./ifp-data-governace-value-card/ifp-data-governace-value-card.component";
import { IfpChartTableComponent } from "./ifp-chart-table/ifp-chart-table.component";
import { DataGovernanceService } from '../core/services/data-governance.service';
import { ToasterService } from '../core/services/tooster/ToastrService.service';
import { IfpDropdownComponent } from "../ifp-widgets/ifp-atoms/ifp-dropdown/ifp-dropdown.component";
import { IfpDataTreeDiagramComponent } from "./ifp-data-tree-diagram/ifp-data-tree-diagram.component";
import { cloneDeep, groupBy } from 'lodash';
import { IfpDataTableComponent } from "../../ifp-analytics/organism/ifp-data-table/ifp-data-table.component";
import { PaginationComponent } from "../ifp-widgets/ifp-molecules/pagination/pagination.component";
import { IfpStatisticalIndicatorsComponent } from "./ifp-statistical-indicators/ifp-statistical-indicators.component";
import { ProgressData } from './ifp-tree-progress/ifp-tree-progress.component';
import { DatePipe, NgClass } from '@angular/common';
import { IfpSearchComponent } from "../ifp-widgets/ifp-atoms/ifp-search/ifp-search.component";
import { debounceTime, Subject } from 'rxjs';
import { SubSink } from 'subsink';
import { CalenderDays, EventDetails, IfpCalenderComponent } from "./ifp-calender/ifp-calender.component";
import { IfpModalComponent } from "../ifp-widgets/ifp-organism/ifp-modal/ifp-modal.component";
import { IfpDataGovEventDetailModalComponent } from "./ifp-data-gov-event-detail-modal/ifp-data-gov-event-detail-modal.component";
import { ApiService } from '../core/services/api.service';
import { dataGovernanceApiConstatnts } from '../core/apiConstants/common-api.constants';
import { IfpPanelDropdownComponent, PanelDropdownOptions } from "../../ifp-analytics/molecule/ifp-panel-dropdown/ifp-panel-dropdown.component";
import { IfpTitleCasePipe } from '../core/pipes/title-case.pipe';
import { IfpIconButtonComponent } from "../ifp-widgets/ifp-atoms/ifp-icon-button/ifp-icon-button.component";
import { DownLoadService } from '../core/services/download-service/download.service';

@Component({
  selector: 'ifp-data-governance-new',
  imports: [IfpBreadcrumbsComponent, TranslateModule, IfpTabComponent, IfpDataGovernaceValueCardComponent, IfpChartTableComponent, IfpDropdownComponent,
    IfpDataTableComponent, PaginationComponent, IfpDataTreeDiagramComponent, IfpStatisticalIndicatorsComponent, NgClass, IfpSearchComponent, IfpCalenderComponent, IfpModalComponent, IfpDataGovEventDetailModalComponent, IfpPanelDropdownComponent, IfpIconButtonComponent],
  providers: [IfpTitleCasePipe],
  templateUrl: './data-governance-new.component.html',
  styleUrl: './data-governance-new.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class DataGovernanceNewComponent implements OnInit, OnDestroy {

  @ViewChild('viewEventDetailsModal') viewEventDetailsModal!: IfpModalComponent;
  private readonly _ifpTitleCasePipe: IfpTitleCasePipe = inject(IfpTitleCasePipe);
  private readonly _downloadService: DownLoadService = inject(DownLoadService);

  public tab = governanceTab;
  public selectedTab: { event: any, index: number } = { event: governanceTab[0], index: 0 }

  public statisticalValues: WritableSignal<UseCaseData> = signal({
    data: [],
    title: '',
    totalCount: 0
  });

  public indicatorsOverview: Indicators = {
    indicators: [],
    products: signal([])
  }

  public tabDeatilData: WritableSignal<UseCaseData> = signal({
    data: [],
    title: '',
    totalCount: 0
  });

  public detailTableData: WritableSignal<TableData> = signal({
    tableData: [],
    tableHead: [],
    headerSettings: {},
    chartData: [],
  });

  public statisticalValuesTableData: WritableSignal<TableData> = signal({
    chartData: [],
    tableData: [],
    headerSettings: {},
    tableHead: []
  })

  public pageData = [
    {
      title: 'Home',
      route: '/home'
    },
    {
      title: 'Data Governance Console',
      route: ''
    }
  ];

  public page: number = 1;
  public perPage: number = 10;
  public offset: number = 0;
  public tabKeys = tabKeys;
  public selectedFilter: { [key: string]: any } = {};
  public filters: WritableSignal<DropDownData[]> = signal([]);
  public comparisonChartData: WritableSignal<any> = signal('');
  public overviewTotal: number = 0;
  public overviewTitle: string = '';
  public selectedUseCaseStatus!: string;
  public eventDetails: WritableSignal<EventDetails[]> = signal([]);
  public dayDetail: WritableSignal<{ data: any, head: string[], headerSettings: any }> = signal({ data: [], head: [], headerSettings: [] })
  public selectedDateDetail: WritableSignal<CalenderDays> = signal({} as CalenderDays);
  public dayStatusFilter!: string;
  public eventDayDetail: WritableSignal<{ name: string, status: string }[]> = signal([])
  public detailPageSettings: { limit: number, offset: number, page: number } = { limit: 10, offset: 0, page: 1 }
  public statusOptions: { key: string, value: string }[] = [];
  public totalDetailEntry: number = 0;
  public tableLoader: boolean = true;

  public domainOpts: { name: string, children: [] }[] = [];
  public themeOpts: { name: string, children: [] }[] = [];
  public subThemeOpts: { name: string, children: [] }[] = [];
  public productOpts: { name: string, children: [] }[] = [];
  public svStatusOpts: { name: string }[] = [];
  public detailTableColumns: WritableSignal<PanelDropdownOptions[]> = signal([]);
  public detailTableSelectedColumns: string[] = [];
  public exportType: string = '';

  public svDropDown: HirarchyDropDown = {
    domainHirarchy: [],
    domains: [],
    themes: [],
    subthemes: [],
    products: [],
    status: []
  }



  private readonly searchInput = new Subject<string>();
  private readonly subs = new SubSink();
  private readonly _excludeFrequencyCols: string[] = ['home', 'bayaan_svs', 'statistical_indicator'];

  public statusList = statusList;
  public calenderHeaderSortSettings: { sort: boolean, sortField: string } = {
    sort: false,
    sortField: 'name'
  }

  public detailHeaderSortSettings: { sort: boolean, sortField: string } = {
    sort: false,
    sortField: ''
  }

  public statisticalValuesHeaderSortSettings: { sort: boolean, sortField: string } = {
    sort: false,
    sortField: ''
  }

  constructor(private readonly _dataGovernanceService: DataGovernanceService,
    private readonly _toasterService: ToasterService, private readonly _cdr: ChangeDetectorRef, private readonly _datePipe: DatePipe, private _apiService: ApiService) {

    this.subs.add(this.searchInput
      .pipe(debounceTime(800))
      .subscribe((value) => {
        this.selectedFilter['search'] = value;
        this.getTableData(this.selectedTab.event.value, false)
      }));
  }


  ngOnInit(): void {
    this.getInitialData();
  }

  getInitialData() {
    this.getChartData();
    this.getTableData(this.tabKeys.use_case, true);
    this.getHomeStatisticsTableData(this.tabKeys.bayaan_svs);
  }

  tabClick(event: { event: any, index: number }) {
    this.detailTableColumns.set([]);
    this.detailTableSelectedColumns = [];
    this.detailHeaderSortSettings = {sort: false, sortField: ''};
    this.statisticalValuesHeaderSortSettings = {sort: false, sortField: ''};
    this.selectedTab = event;
    this.selectedFilter = {};
    this.page = 1;
    this.offset = 0;
    if (this.selectedTab.event.value === tabKeys.calender) {
      this.getCalendarDetails();
      return;
    }
    if (this.selectedTab.event.value != tabKeys.home) {
      this.getTabDropDownData();
      this.getTableData(this.selectedTab.event.value, false);
      if (this.selectedTab.event.value == tabKeys.statistical_indicator) {
        this.getComparisonReport();
      }
    } else {
      this.getInitialData();
    }
  }



  getChartData(): void {
    this._dataGovernanceService.getChartData().subscribe({
      next: ({ indicators, products }) => {
        this.indicatorsOverview.indicators = this.setIndicatorData(indicators);
        this.indicatorsOverview.products.set(products)
        this.setIcons();
      },
      error: ({ error }) => {
        this._toasterService.error(error?.message || 'An unexpected error occurred.');
      }
    });
  }

  setIndicatorData(data: any) {
    let result: ProgressData[] = [];
    if (data?.length) {
      data.forEach((element: { name: string; value: number; }) => {
        result.push({
          name: element.name,
          value: element.value,
          color: this._dataGovernanceService.checkColor(element.name)
        })
      });
      this.overviewTotal = result.reduce((sum, item) => sum + parseInt(item.value.toString()), 0)
      this.overviewTitle = overviewTitles[this.selectedTab.event.value as keyof typeof overviewTitles];
    }
    return result;
  }

  setIcons() {
    if (this.indicatorsOverview.products()?.length <= 0) { return }
    this.indicatorsOverview.products().forEach(element => {
      element.icon = productIcons[element.name.toLowerCase().replace(/\s/g, "") as keyof typeof productIcons]
    })
  }

  getTableData(tabKey: string, short: boolean = false, exportType: string = '') {
    this.selectedFilter['short'] = short;
    this.selectedFilter['sortColumn'] = this.detailHeaderSortSettings.sortField;
    this.selectedFilter['sort'] =!this.detailHeaderSortSettings.sort;
    this.tableLoader = exportType === '';
    const params = {
      page: this.page,
      per_page: this.perPage,
      exportType: exportType
    }
    this._dataGovernanceService.getDataGovernanceCategoryData(tabKey, this.selectedFilter, params).subscribe({
      next: (resp) => {
        const { tableData, tableValues } = resp;
        if (exportType === '') {
          this.tableLoader = false;
          if (tableData?.data.length <= 0) {
            this.clearTableData();
            return;
          }
          if (this.selectedTab.event.value !== 'calendar') {
            tableData.data = this.setDateFormat(tableData.data, short);
          }
          this.tabDeatilData.set(tableData);
          this.configureTableData();
          if (this.selectedTab.event.value != tabKeys.home) {
            this.indicatorsOverview.indicators = this.setIndicatorData(tableValues);
            this.indicatorsOverview.products.set([]);
            return;
          }
          this.detailTableData().chartData = this.setPieChartData(tableValues);
        } else {
          const matches = (resp.headers.get('Content-Disposition'))?.match(/"(.*?)"/);
          const nameValue = matches ? matches[1] : this.selectedTab.event?.tabTitle;
          this._downloadService.downloadFiles(resp.body, nameValue);
          this.exportType = '';
        }
      },
      error: error => {
        if (exportType === '') {
          this.tableLoader = false;
          this.clearTableData();
        }
        this._toasterService.error(error?.message || 'An unexpected error occurred.');
      }
    });
  }

  clearTableData() {
    this.detailTableData().tableData = [];
    this.detailTableData().tableHead = [];
    this.detailTableData().headerSettings = {};
    this.detailTableColumns.set([]);
    this.detailTableSelectedColumns = [];
    this._cdr.detectChanges();
  }

  setDateFormat(tableData: any, short: boolean = false) {
    if (tableData.length <= 0) {
      return []
    }
    let referenceKey: string = '', freqKey: string = '';
    for (const key in tableData[0]) {
      if (key.toLowerCase() === 'reference date') {
        referenceKey = key;
      }
      if (key.toLowerCase() === 'frequency') {
        freqKey = key;
      }
    }
    tableData.forEach((element: any) => {
      if (referenceKey !== '') {
        element[referenceKey] = this._dataGovernanceService.changeDateFormats(element[referenceKey], element[freqKey]);
      }
      if (element['next reference date']) {
        element['next reference date'] = this._dataGovernanceService.changeDateFormats(element['next reference date'], element[freqKey])
      }
      if (this._excludeFrequencyCols.includes(this.selectedTab.event.value)) {
        delete element[freqKey]
      }
    });
    return tableData;
  }



  getHomeStatisticsTableData(tabKey: string) {
    this.selectedFilter['short'] = true;
    this.selectedFilter['sortColumn'] = this.statisticalValuesHeaderSortSettings.sortField;
    this.selectedFilter['sort'] =!this.statisticalValuesHeaderSortSettings.sort;
    const params = {
      page: this.page,
      per_page: this.perPage,
      exportType: ''
    }
    this._dataGovernanceService.getDataGovernanceCategoryData(tabKey, this.selectedFilter, params).subscribe({
      next: (next) => {
        const groupedSvs = groupBy(next.tableData.data, (a: any) => a.source_name);
        this.statisticalValues.set({
          data: Object.entries(groupedSvs).map(([sourceName, records]: [string, any[]]) => ({
            source_name: sourceName,
            values: records.map(({ status, SV_count }) => ({ status: status, count: +SV_count })),
            total: records.reduce((sum, { SV_count }) => sum + +SV_count, 0),
          })),
          title: next.tableData.title,
          totalCount: next.tableData.totalCount
        });
        this.statisticalValuesTableData().chartData = this.setPieChartData(next.tableValues);
        this.setStatisticalValueTableData();
      },
      error: (error) => {
        this._toasterService.error(error?.message || 'An unexpected error occurred.');
      }
    });
  }

  setStatisticalValueTableData() {
    if (!this.statisticalValues().data?.length) return;
    this.statisticalValuesTableData().tableData = [];
    const dynamicComponents = ['status', 'SV count by status'];
    this.statisticalValues().data = this.statisticalValues().data.map((item: { source_name: any; values: any[]; total: any; }) => ({
      'source': item.source_name,
      'Total SVS': item.total,
      status: item.values.map((value: { status: string; }) => value.status),
      'SV count by status': item.values.map((value: { count: number; }) => value.count),
    }));
    this.statisticalValuesTableData().tableHead = Object.keys(this.statisticalValues().data[0]);
    this.statisticalValuesTableData().headerSettings = Object.fromEntries(Object.keys(this.statisticalValues().data[0]).map(key => [key, { sort: true }]))
    this.statisticalValues().data.forEach((element: { [s: string]: unknown; }) => {
      const tableData = Object.entries(element).map(([key, value]) => {
        if (!dynamicComponents.includes(key)) {
          return {
            key,
            title: key,
            value,
            type: 'default',
            multiComponents: []
          };
        }

        let renderComponentValue = () => import('./ifp-status-tag/ifp-status-tag.component').then(mode => mode.IfpStatusTagComponent)
        const multiComponents = (element[key] as any[]).map((val: any, index: number) => ({
          key,
          title: key,
          value: val,
          type: 'custom',
          renderComponent: renderComponentValue,
          inputValues: { name: val.toString(), color: this._dataGovernanceService.checkColor(key === 'SV count by status' ? (element['status'] as any[])[index] : val) }

        }));

        return {
          key,
          title: key,
          value,
          type: 'custom',
          multiComponents
        };
      });
      this.statisticalValuesTableData().tableData.push(tableData);
    });

    this._cdr.detectChanges();
  }

  setPieChartData(data: any) {
    if (!data?.length) return;
    return [{
      type: 'pie',
      data: data.map((element: { name: string; value: string }) => ({
        name: element.name,
        y: parseInt(element.value),
        color: this._dataGovernanceService.checkColor(element.name),
      }))
    }];
  }



  getTabDropDownData() {
    this._dataGovernanceService.getCatDropDownOpts(this.selectedTab.event.value).subscribe({
      next: next => {
        if (this.selectedTab.event.value == tabKeys.statistical_indicator) {
          this.svDropDown = next;
          this.setHirarchyDropdown()
          return;
        }
        this.filters.set(next);
        this.filters().forEach(element => {
          element.options.unshift({ name: 'All' })
        });
      },
      error: error => {
        this._toasterService.error(error);
        this.filters.set([]);
      }
    })
  }


  setHirarchyDropdown() {
    this.domainOpts = this.setAllOptionInFilter(this.svDropDown.domains);
    this.themeOpts = this.setAllOptionInFilter(this.svDropDown.themes);
    this.subThemeOpts = this.setAllOptionInFilter(this.svDropDown.subthemes);
    this.productOpts = this.setAllOptionInFilter(this.svDropDown.products);
    this.svStatusOpts = this.setAllOptionInFilter(this.svDropDown.status);

  }


  selectHirarchyFilter(event: any, type: string) {
    if (type == 'domain') {
      this.selectedFilter['domain'] = event.name == 'All' ? '' : event.name;
      this.setThemeOptionsAndValue(this.selectedFilter['domain'])
      this.setSubThemeOptionsAndValue(this.selectedFilter['theme'])
      this.setProductOptionsAndValue(this.selectedFilter['subTheme'])
    }
    if (type == 'theme') {
      this.selectedFilter['theme'] = event.name == 'All' ? '' : event.name;
      this.setSubThemeOptionsAndValue(this.selectedFilter['theme'])
      this.setProductOptionsAndValue(this.selectedFilter['subTheme'])
    }
    if (type == 'subTheme') {
      this.selectedFilter['subTheme'] = event.name == 'All' ? '' : event.name;
      this.setProductOptionsAndValue(this.selectedFilter['subTheme'])
    }
    if (type == 'product') {
      this.selectedFilter['product'] = event.name == 'All' ? '' : event.name;
    }
    if (type == 'status') {
      this.selectedFilter['status'] = event.name == 'All' ? '' : event.name;
    }
    this.getTableData(this.selectedTab.event.value, false);
  }

  setThemeOptionsAndValue(domain: string) {
    const domainHierarchy = this.svDropDown.domainHirarchy.find(x => x.name == domain);
    this.themeOpts = domainHierarchy ? domainHierarchy.children : this.setAllOptionInFilter(this.svDropDown.themes);
    this.selectedFilter['theme'] = this.themeOpts[0].name == 'All' ? '' : this.themeOpts[0].name;
  }

  setSubThemeOptionsAndValue(theme: string) {
    const themeHirarchy = this.themeOpts.find(x => x.name == theme);
    this.subThemeOpts = themeHirarchy ? themeHirarchy.children : this.setAllOptionInFilter(this.svDropDown.subthemes);
    this.selectedFilter['subTheme'] = this.subThemeOpts[0].name == 'All' ? '' : this.subThemeOpts[0].name;
  }

  setProductOptionsAndValue(subTheme: string) {
    const subThemeHirarchy = this.subThemeOpts.find(x => x.name == subTheme);
    this.productOpts = subThemeHirarchy ? subThemeHirarchy.children : this.setAllOptionInFilter(this.svDropDown.products);
    this.selectedFilter['product'] = this.productOpts[0].name == 'All' ? '' : this.productOpts[0].name;
  }

  setAllOptionInFilter(options: any) {
    return [{ name: 'All', children: [] }, ...options]
  }

  configureTableData() {
    let customComponents = ['status', 'landing status', 'sv db update status', 'platform status', 'staging db status'];
    if (this.tabDeatilData().data?.length <= 0) { return }
    const filteredData = this.setColumnTitles(this.detailTableSelectedColumns, this.tabDeatilData().data);
    this.detailTableData().tableData = [];
    this.detailTableData().tableHead = Object.keys(filteredData[0]);

    if (this.detailTableColumns().length === 0) {
      this.detailTableColumns.set(this.detailTableData().tableHead.map(key => ({key: key, value: this._ifpTitleCasePipe.transform(key), checked: true})));
    }

    this.detailTableData().headerSettings = Object.fromEntries(Object.keys(filteredData[0]).map(key => [key, { sort: true }]));
    filteredData.forEach((element: any) => {
      let tableData: any[] = [];
      Object.entries(element).forEach(([key, value]) => {
        tableData.push({
          key: key,
          title: key,
          value: value,
          type: !customComponents.includes(key.toLowerCase()) ? 'default' : 'custom',
          renderComponent: !customComponents.includes(key.toLowerCase()) ? '' : () => import('./ifp-status-tag/ifp-status-tag.component').then(mode => mode.IfpStatusTagComponent),
          inputValues: !customComponents.includes(key.toLowerCase()) ? null : {
            name: value,
            color: this._dataGovernanceService.checkColor(value)
          }
        })
      });
      this.detailTableData().tableData.push(tableData);
    });
    this._cdr.detectChanges();
  }

  selectFilter(event: any, path: string) {
    this.selectedFilter[path] = event.name == 'All' ? '' : event.name;
    this.resetPagination();
    this.getTableData(this.selectedTab.event.value, false);
  }

  onPageChange(event: any) {
    this.offset = event + 1;
    this.page = (event / this.perPage) + 1;
    this.getTableData(this.selectedTab.event.value, false);
  }

  limitChanged(event: any) {
    this.page = 1;
    this.offset = 1;
    this.perPage = event;
    this.getTableData(this.selectedTab.event.value, false);
  }

  getComparisonReport() {
    this._dataGovernanceService.getComparisonReport().subscribe({
      next: next => {
        this.comparisonChartData.set(next);
      },
      error: error => {
        this._toasterService.error(error?.message || 'An unexpected error occurred.');
      }
    });
  }

  openDetail(_event: boolean, key: string) {
    const tabIndex = this.tab.findIndex(x => x.value == key);
    this.tabClick({ event: this.tab[tabIndex], index: tabIndex });
    window.scroll(0, 0)
  }

  filterData(status: string, type: string) {
    this.detailTableColumns.set([]);
    this.selectedFilter[type == tabKeys.use_case ? 'STATUS' : 'status'] = status == this.selectedUseCaseStatus ? '' : status;
    if (type != tabKeys.use_case) {
      this.getHomeStatisticsTableData(type)
    } else {
      this.getTableData(type, true);
    }

    this.selectedUseCaseStatus = this.selectedUseCaseStatus == status ? 'All' : status;
  }

  onSearch(event: string) {
    this.searchInput.next(event);
  }


  setTableData(data: any) {
    let tableData: any[] = [];
    data.forEach((element: { [s: string]: unknown; }) => {
      let rowData: any = []
      Object.entries(element).forEach(([key, value]) => {
        let renderComponentValue = () => import('./ifp-status-tag/ifp-status-tag.component').then(mode => mode.IfpStatusTagComponent);
        rowData.push({
          key,
          title: key,
          value,
          type: key === 'status' ? 'custom' : 'defualt',
          renderComponent: key === 'status' ? renderComponentValue : '',
          inputValues: key === 'status'
            ? { name: value, color: this._dataGovernanceService.checkColor(value) }
            : { value: value }
        });
      });
      tableData.push(rowData)
    });
    const head = Object.keys(data[0]);
    return {
      data: tableData,
      head: head,
      headerSettings: Object.fromEntries(head.map(key => [key, { sort: true }]))
    }
  }

  onCloseEventDetails() {
    this.viewEventDetailsModal.removeModal();
  }

  getCalendarDetails(date: Date = new Date()) {
    const startDate = this._datePipe.transform(new Date(date.getFullYear(), date.getMonth(), 1), 'y-MM-dd');
    const endDate = this._datePipe.transform(new Date(date.getFullYear(), date.getMonth() + 1, 0), 'y-MM-dd');
    this.subs.add(
      this._apiService.getMethodRequest(dataGovernanceApiConstatnts['sv_calendar'], {
        startDate: startDate, endDate: endDate,
        sort: !this.calenderHeaderSortSettings.sort, sortField: this.calenderHeaderSortSettings.sortField
      }).subscribe({
        next: (data) => {
          this.eventDetails.set(data);
        },
        error: (error) => {
          this._toasterService.error(error?.error?.message);
        }
      })
    )
  }

  viewDateDetails(data: CalenderDays) {
    this.selectedDateDetail.set(data);
    this.statusOptions = data.isEvent?.status?.map(x => ({ key: x.name, value: x.name })) || [];
    this.statusOptions.unshift({ key: 'all', value: 'All' })
    this.detailPageSettings.limit = 10;
    this.detailPageSettings.offset = 0;
    this.detailPageSettings.page = 1;
    this.getEventDetail();
    this.viewEventDetailsModal.createElement();
  }


  getEventDetail() {
    this.subs.add(
      this._apiService.postMethodRequest(dataGovernanceApiConstatnts['event_detail'], {
        date: this._datePipe.transform(this.selectedDateDetail()?.isEvent?.date, 'yyyy-MM-dd'),
        status: this.dayStatusFilter, sortColumn: this.calenderHeaderSortSettings.sortField,
        sort: !this.calenderHeaderSortSettings.sort
      }, {}, false, { limit: this.detailPageSettings.limit, offset: this.detailPageSettings.offset }).subscribe({
        next: (data) => {
          this.eventDayDetail.set(data.data);
          this.totalDetailEntry = data.totalCount;
          if (this.eventDayDetail()?.length) {
            this.dayDetail.update(() => {
              const val = this.setTableData(this.eventDayDetail());
              return val;
            })
          } else {
            this.resetData();
          }
        },
        error: (error) => {
          this._toasterService.error(error?.error?.message);
          this.resetData();
        }
      })
    )
  }

  resetData() {
    this.dayDetail.set({
      data: [], head: [],
      headerSettings: {}
    })
  }

  sortData(event: any) {
    this.calenderHeaderSortSettings = event;
    this.getEventDetail();
  }

  sortDetailTableData(sort: any, isShort: boolean) {
    this.resetPagination();
    this.detailHeaderSortSettings.sort = sort.sortEvent;
    this.detailHeaderSortSettings.sortField = sort.sortEventName;
    this.getTableData(isShort ? this.tabKeys.use_case : this.selectedTab.event.value, isShort)
  }

  sortStatisticalValuesTableData(sort: any, isShort: boolean) {
    this.statisticalValuesHeaderSortSettings.sort = sort.sortEvent;
    this.statisticalValuesHeaderSortSettings.sortField = sort.sortEventName;
    this.getHomeStatisticsTableData(this.tabKeys.bayaan_svs)
  }

  statusChanged(event: { value: string }) {
    this.dayStatusFilter = event.value;
    this.detailPageSettings.limit = 10;
    this.detailPageSettings.offset = 0;
    this.detailPageSettings.page = 1;
    this.getEventDetail();
  }

  pageSettingChanged(event: { limit: number, offset: number, page: number }) {
    this.detailPageSettings.limit = event.limit;
    this.detailPageSettings.offset = event.offset;
    this.detailPageSettings.page = event.page;
    this.getEventDetail();
  }

  resetPagination(){
    this.page = 1;
    this.offset=0;
    this.perPage = 10;
  }

  selectDetailColumns(event: any) {
    this.detailTableSelectedColumns = event.map((e: any) => e.key);
    this.configureTableData();
    this.setSelectedColumsList(event, this.detailTableColumns());
  }

  setColumnTitles(selectedColumns: any, sourceData: any) {
    let filteredData = cloneDeep(sourceData)
    if (selectedColumns?.length > 0) {
      filteredData = sourceData
        .map((column: { [s: string]: unknown; } | ArrayLike<unknown>) => Object.fromEntries(
          Object.entries(column).filter(([key]) => selectedColumns.includes(key))
        ))
        .filter((user: {}) => Object.keys(user).length);
    }
    return filteredData;
  }

  setSelectedColumsList(selectedCols: PanelDropdownOptions[], actualCols: PanelDropdownOptions[]) {
    actualCols.forEach((title: PanelDropdownOptions) => {
      const selectedColIndex = selectedCols.findIndex((selectedCol: PanelDropdownOptions) => selectedCol.key === title.key);
      title.checked = selectedColIndex >= 0;
    })
  }

  onExportData() {
    this.getTableData(this.selectedTab.event.value, false, 'csv');
  }

  ngOnDestroy() {
    this.subs.unsubscribe();
  }
}



export interface TableData {
  chartData?: any;
  tableData: any;
  headerSettings: Record<string, any>;
  tableHead: string[];
}

export interface Indicators {
  indicators: { name: string, value: number, color: string }[],
  products: WritableSignal<{
    name: string,
    value: number,
    id: number,
    content: string,
    icon?: string
  }[]>
}

export interface DropDownData {
  name: string;
  options: { name: string }[],
  path: string,
  children: [] // Added the 'children' property to match the expected type
}

export interface UseCaseData {
  data: any,
  title: string,
  totalCount: number
}

export interface HirarchyDropDown {
  domainHirarchy: { name: string, children: [] }[],
  domains: { name: string, children: [] }[],
  themes: { name: string, children: [] }[],
  subthemes: { name: string, children: [] }[],
  products: { name: string, children: [] }[],
  status: { name: string, children: [] }[],
}
