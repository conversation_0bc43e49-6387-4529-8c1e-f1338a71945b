@use "../../../../assets/ifp-styles/abstracts/index" as *;
:host {
  background-color: var(--ifp-color-white);
  border-radius: 20px;
  padding: $spacer-3;
  box-shadow: 0 18px 30px 7px rgb(6 50 96);
  display: block;
}
.ifp-ai-report-card {
  display: flex;
    flex-direction: column;
    height: 100%;
  background-color: $ifp-color-white;
  &__icon {
    display: block;
    font-size: $ifp-fs-5;
    color: $ifp-color-blue-hover;
  }
  &__title {
    font-size: $ifp-fs-5;
    font-weight: $fw-bold;
    min-height: 75px;
  }
  &__bottom {
    margin-top: $spacer-2;
    display: flex;
  }

  &__bottom-left {
    margin-inline-end: $spacer-2;
  }
  &__text {
    margin-top: auto;
    margin-bottom: $spacer-2;
    font-size: $ifp-fs-4;
  }
  &__value {
    font-size: $ifp-fs-9;
    font-weight: $fw-bold;
  }
  &__red ,&__green {
    padding: $spacer-1  $spacer-3;
  }
  &__red  {
    background-color: $ifp-color-red-normal;
  }
  &__green  {
    background-color: $ifp-color-green-dark-2;
  }
  &__arrow-wrapper {
    border-radius: 100px;
    color: $ifp-color-white-global;
  }
  &__arrow  {
    font-size: $ifp-fs-5;
    font-weight: $fw-bold;
  }
  &__domain-card {
    display: inline-flex;
    align-items: center;
    background-color: $ifp-color-grey-17;
    border-radius: 110px;
    padding: $spacer-1;
    padding-inline-end: $spacer-3;
    margin-bottom: $spacer-3;
  }
  &__domain-icon-wrapper {
    margin-inline-end: $spacer-2;
    background-color: $ifp-color-blue-hover;
    border-radius: 50%;
    padding: $spacer-2;
  }
  &__date {
    color: $ifp-color-primary-grey;
    padding: $spacer-3 $spacer-4;
    background-color: $ifp-color-pale-grey;
    font-size: $ifp-fs-4;
    margin: $spacer-3  (-$spacer-3 + $spacer-1) (-$spacer-3 + $spacer-1);
    border-radius: 0 0 16px 16px;
  }
}

:host-context(.ifp-dark-theme) {
  .ifp-ai-report-card {
    &__domain-name {
      color: $ifp-color-white;
    }

  }
  }
