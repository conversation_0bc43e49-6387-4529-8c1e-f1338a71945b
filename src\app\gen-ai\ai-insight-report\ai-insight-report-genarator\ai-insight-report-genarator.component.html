<div class="ifp-library ifp-module-padding">
  <div class="ifp-container">
    <div class="ifp-library__head">

      <span (click)="onBack()" class="ifp-link ifp-library__head-back" [title]="'Back' "><em
          class="ifp-icon ifp-icon-leftarrow fp-link ifp-library__head-icon"></em> <span
          class="ifp-library__head-back-text">{{'Back' }}</span></span>
      <h1 class="ifp-module-heading">{{'AI Insight Report Generator' }}</h1>


    </div>
    <p class="ifp-library__desc">{{'AI Insight Report Generator simplifies data analysis by leveraging artificial
      intelligence to produce clear, actionable insights in comprehensive reports, enabling smarter decision-making with
      ease.' }}</p>
    <div class="ifp-library__tab">
      <app-ifp-tab [disbaleTranslaion]="true" [isSmall]="true" [selectedTab]="0" class="ifp-library__tab-items"
        [tabData]="data" (selectedTabEvent)="selectedTabEvent($event)"></app-ifp-tab>
    </div>

    <div class="ifp-library__table">


      <div class="ifp-library__title-sec">
        <ifp-search [boxType]="true" class="ifp-library__search" [disableTranslation]="disableTranslate()"
          [isKeypress]="true" [onSearch]="search" (searchEvent)="searchEvent($event)"></ifp-search>
        <div class="ifp-library__status">
          <span class="ifp-library__status-text">
            {{'Current Stage' }}
          </span>
          @if (currentTabIndex==0 ) {
          <app-ifp-db-dropdown [disableTranslation]="disableTranslate()" class="ifp-library__status-dropdown"
            [key]="'display_name'" [isMultiSelect]="false" [options]=" reportStatus"
            (singleSelected)="selectStatus($event)"></app-ifp-db-dropdown>
          } @else {
          <app-ifp-db-dropdown [disableTranslation]="disableTranslate()" class="ifp-library__status-dropdown"
            [key]="'display_name'" [isMultiSelect]="false" [options]="ConsolidatedStatus "
            (singleSelected)="selectStatusConsolidated($event)"></app-ifp-db-dropdown>
          }



          <span class="ifp-library__status-text">
            {{'Quarter' }}
          </span>

          <app-ifp-db-dropdown [disableTranslation]="disableTranslate()" (singleSelected)="selectQuarter($event)"
            class="ifp-library__status-dropdown" [key]="'value'" [isMultiSelect]="false"
            [options]="quarterOptions"></app-ifp-db-dropdown>

        </div>

      </div>

      <div class="ifp-library__row ifp-library__row--head">
        <div class="ifp-library__col ">{{ 'Report Name'}}
          <div>
            <em [appIfpTooltip]="'Ascending' " [extraSpaceTop]="30" [zIndex]="1203"
              [ngClass]="{'ifp-library__sort-icon--active': acceding == false && tableHeader?.[0]?.key == this.sortValueString}"
              class="ifp-icon ifp-icon-up-arrow ifp-library__sort-icon"
              (click)="sort(tableHeader?.[0]?.key ?? '' , false)"></em>
            <em [appIfpTooltip]="'Descending' " [extraSpaceTop]="30" [zIndex]="1203"
              [ngClass]="{'ifp-library__sort-icon--active':  acceding == true &&tableHeader?.[0]?.key == this.sortValueString}"
              class="ifp-icon ifp-icon-down-arrow ifp-library__sort-icon"
              (click)="sort(tableHeader?.[0]?.key ?? '', true)"></em>
          </div>
        </div>
        <div class="ifp-library__col "><span class="ifp-library__col-name">{{'Current Stage' }}</span>
          <div>
            <em [appIfpTooltip]="'Ascending' " [extraSpaceTop]="30" [zIndex]="1203"
              [ngClass]="{'ifp-library__sort-icon--active': acceding == false && tableHeader?.[2]?.key == this.sortValueString}"
              class="ifp-icon ifp-icon-up-arrow ifp-library__sort-icon"
              (click)="sort(tableHeader?.[2]?.key ?? '', false)"></em>
            <em [appIfpTooltip]="'Descending' " [extraSpaceTop]="30" [zIndex]="1203"
              [ngClass]="{'ifp-library__sort-icon--active': acceding == true && tableHeader?.[2]?.key == this.sortValueString}"
              class="ifp-icon ifp-icon-down-arrow ifp-library__sort-icon"
              (click)="sort(tableHeader?.[2]?.key ?? '', true)"></em>
          </div>
        </div>
        <div class="ifp-library__col ">{{ 'Quarter'}}
          <div>
            <em [appIfpTooltip]="'Ascending' " [extraSpaceTop]="30" [zIndex]="1203"
              [ngClass]="{'ifp-library__sort-icon--active': acceding == false && tableHeader?.[1]?.key == this.sortValueString}"
              class="ifp-icon ifp-icon-up-arrow ifp-library__sort-icon"
              (click)="sort(tableHeader?.[1]?.key ?? '', false)"></em>
            <em [appIfpTooltip]="'Descending' " [extraSpaceTop]="30" [zIndex]="1203"
              [ngClass]="{'ifp-library__sort-icon--active':  acceding == true && tableHeader?.[1]?.key == this.sortValueString}"
              class="ifp-icon ifp-icon-down-arrow ifp-library__sort-icon"
              (click)="sort(tableHeader?.[1]?.key ?? '', true)"></em>
          </div>
        </div>
        <div class="ifp-library__col ">{{ 'Created Date'}}
          <div>
            <em [appIfpTooltip]="'Ascending' " [extraSpaceTop]="30" [zIndex]="1203"
              [ngClass]="{'ifp-library__sort-icon--active': acceding == false && tableHeader?.[3]?.key == this.sortValueString}"
              class="ifp-icon ifp-icon-up-arrow ifp-library__sort-icon"
              (click)="sort(tableHeader?.[3]?.key ?? '', false)"></em>
            <em [appIfpTooltip]="'Descending' " [extraSpaceTop]="30" [zIndex]="1203"
              [ngClass]="{'ifp-library__sort-icon--active':  acceding == true &&tableHeader?.[3]?.key == this.sortValueString}"
              class="ifp-icon ifp-icon-down-arrow ifp-library__sort-icon"
              (click)="sort(tableHeader?.[3]?.key ?? '', true)"></em>
          </div>
        </div>
        <p class="ifp-library__col ifp-library__col--3"></p>
      </div>
      <div class="ifp-library__table-body">
        @for (item of table; track item; let index = $index;) {
        <div class="ifp-library__row">

          <div class="ifp-library__col">
            <div class="ifp-library__item">
              <p class="ifp-library__col-name" [appIfpTooltip]="item.report_name" [zIndex]="2000" [extraSpaceTop]="20">
                {{item.report_name}}</p>
            </div>
          </div>
          <div class="ifp-library__col">
            <div class="ifp-library__item">
              <p class="ifp-library__col-badge" [appIfpTooltip]="item.status.display_name" [zIndex]="2000"
                [extraSpaceTop]="20" [style.background-color]="item.status.color">
                {{item.status.display_name}}
              </p>
            </div>
          </div>
          <div class="ifp-library__col">
            <div class="ifp-library__item">
              <p class="ifp-library__col-name" [appIfpTooltip]="item.report_name" [zIndex]="2000" [extraSpaceTop]="20">
                {{item.quarter}}</p>
            </div>
          </div>
          <div class="ifp-library__col ">
            <div class="ifp-library__item">
              <p class="ifp-library__col-name" [appIfpTooltip]="item.created_date" [zIndex]="2000" [extraSpaceTop]="20">
                {{item.created_date| date: 'd/M/yyyy'}}</p>
              <!-- <p class="ifp-library__name">{{'Lorem ipsum dolor sit amet consectetur. Accumsan turpis.' }}</p> -->
            </div>
          </div>
          <div class="ifp-library__col ifp-library__col--3">
            <div class="ifp-library__btn-sec">
              <ifp-button [label]="'View'" class="ifp-library__button" [disableTranslate]="disableTranslate()"
                [buttonClass]="buttonClass.transpreantBlue" [iconClass]="'ifp-icon-rightarrow'"
                (ifpClick)="gotoPage(item.id)"></ifp-button>
              <!-- <ifp-kebab-menu [options]="kebabOptions" class="ifp-library__options"[position]="'left'"></ifp-kebab-menu> -->
            </div>
          </div>

        </div>


        } @empty {
        @if(loader) {
        <div class="ifp-center-loader">
          <app-ifp-spinner></app-ifp-spinner>
        </div>

        } @else {
        <app-ifp-no-data></app-ifp-no-data>
        }
        }
      </div>
      @if(table.length !==0 ) {
      <app-pagination [size]="10" class="ifp-library__pagination" [customPagination]="true" [offset]="offsetPage"
        [limit]="limit" [size]="totalCount" (pageChange)="onPageChange($event)"></app-pagination>
      }
    </div>
  </div>
</div>

<app-ifp-modal [zIndex]="1205" [overlayIndex]="1204" #modal>
  <app-ifp-remove-card [text]="'Do you want to remove?'" (firstButtonEvent)="closeModalEvent($event)"
    (secondButtonEvent)="closeModalEvent($event)">
  </app-ifp-remove-card>
</app-ifp-modal>
