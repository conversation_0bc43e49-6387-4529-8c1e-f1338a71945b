<div class="ifp-dxp-kpis">
  <div class="ifp-dxp-kpis__section-head">
    @if (currentTabData && currentTabData.subMenus && currentTabData.subMenus.length > 0) {
      <div class="ifp-dxp-kpis__tabs-wrapper">
        <app-ifp-tab
          [tabData]="transformedTabData"
          [selectionType]="'name'"
          [isSmall]="true"
          [selectedTab]="(transformedTabData.length > selectedSubMenuIndex) ? transformedTabData[selectedSubMenuIndex].name : ''"
          (selectedTabEvent)="onSubMenuTabClick($event)">
        </app-ifp-tab>
      </div>
    }
    <div class="ifp-dxp-kpis__recent">
      <div class="ifp-dxp-kpis__filters-wrapper">
        <!-- Entity Filter Dropdown -->
        <div class="ifp-dxp-kpis__filter-item">
          <ifp-panel-dropdown
            [label]="'Select Entity'"
            [enableSearch]="true"
            [isBoxType]="true"
            [enableSelectAll]="false"
            [multiSelect]="true"
            [key]="'value'"
            [multipleSelectedItems]="getSelectedEntityItems()"
            [options]="entitiesFilterData"
            class="ifp-dxp-kpis__entity-dropdown"
            (multiSelected)="onEntityOptionChange($event)">
          </ifp-panel-dropdown>
        </div>

        <!-- Creator Filter Dropdown -->
        <div class="ifp-dxp-kpis__filter-item">
          <ifp-panel-dropdown
            [userDropdown]="true"
            [label]="'Created by'"
            [enableSearch]="true"
            [isBoxType]="true"
            [enableSelectAll]="true"
            [multiSelect]="true"
            [key]="'value'"
            [multipleSelectedItems]="currentSelection()"
            [options]="creatorsFilterData"
            class="ifp-dxp-kpis__creator-dropdown"
            (multiSelected)="onCreatorOptionChange($event)"
            (searchChanged)="onUserSearch($event)">
          </ifp-panel-dropdown>
        </div>

        <!-- Sort Dropdown -->
        <div class="ifp-dxp-kpis__filter-item">
          <ifp-panel-dropdown
            [label]="'Sort'"
            [enableSearch]="false"
            [isBoxType]="true"
            [enableSelectAll]="false"
            [multiSelect]="false"
            [key]="'value'"
            [selectedValue]="selectedSortOptions.length > 0 ? selectedSortOptions[0].key : ''"
            [options]="sortOptionsData"
            class="ifp-dxp-kpis__sort-dropdown"
            (selected)="onSortOptionChange($event ? [$event] : [])">
          </ifp-panel-dropdown>
        </div>
      </div>
    </div>
  </div>
  <!-- <p>sdsdasda</p> -->
  <div class="ifp-dxp-kpis__wrapper">
    <!-- Loading State -->
    @if (isLoading) {
      <div class="ifp-dxp-kpis__loader">
        <app-ifp-spinner></app-ifp-spinner>
      </div>
    }

    <!-- No Data State -->
    @else if (!isLoading && (!indicatorCards || indicatorCards.length === 0)) {
      <div class="ifp-dxp-kpis__no-data">
        <app-ifp-no-data
          [message]="'No KPIs available at the moment' | translate"
          [height]="'400px'"
          [bgColor]="'transparent'">
        </app-ifp-no-data>
      </div>
    }

    <!-- Data State -->
    @else {
      @for (indicator of indicatorCards; let index = $index; track index) {
        <ifp-dxp-whats-new-card
        [data]="indicator"
        [contentType]="'dxp-component'"
        [index]="index"
        [small]="activeCardIndex !== index"
        [remove]="false"
        [isSelected]="false"
        [cardConfig]="cardConfig"
        (crossClick)="onRemoveCard($event)"
        (selectIndicator)="onSelectIndicator($event)"
        (cardAction)="onCardAction($event)"
        (resized)="onCardResized($event, index)">
        </ifp-dxp-whats-new-card>
      }
    }
  </div>

  <!-- Pagination -->
  @if (!isLoading && indicatorCards && indicatorCards.length > 0 && totalCount > limit) {
    <div class="ifp-dxp-kpis__pagination">
      <app-pagination
        [offset]="offset"
        [limit]="limit"
        [size]="totalCount"
        [limitOptions]="[5, 10, 15, 20, 25]"
        (pageChange)="onPageChange($event)"
        (limitChange)="onLimitChange($event)">
      </app-pagination>
    </div>
  }
</div>

