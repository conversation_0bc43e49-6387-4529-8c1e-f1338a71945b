import { AfterViewChecked, ChangeDetector<PERSON>ef, Component, OnInit } from '@angular/core';
import { IfpBreadcrumbsComponent } from '../../ifp-widgets/ifp-molecules/ifp-breadcrumbs/ifp-breadcrumbs.component';
import { PageData } from 'src/app/scad-insights/core/interface/molecule/breadcrumb.interface';
import { TranslateModule } from '@ngx-translate/core';
import { CommonModule } from '@angular/common';
import { IfpDropdownComponent } from '../../ifp-widgets/ifp-atoms/ifp-dropdown/ifp-dropdown.component';
import { Store } from '@ngrx/store';
import { loadGlosseryFilter, loadGlosseryList } from 'src/app/scad-insights/store/pages/page.action';
import { selectGlosseryFilterResponse, selectGlosseryListResponse } from 'src/app/scad-insights/store/pages/page.selector';
import { IfpNoDataComponent } from '../../ifp-widgets/ifp-molecules/ifp-no-data/ifp-no-data.component';
import { ListWithSwitchComponent } from '../../ifp-widgets/ifp-molecules/list-with-switch/list-with-switch.component';
import { PaginationComponent } from '../../ifp-widgets/ifp-molecules/pagination/pagination.component';
import { Subject, debounceTime } from 'rxjs';
import { SubSink } from 'subsink';
import { IfpTooltipDirective } from 'src/app/scad-insights/core/directives/ifp-tooltip.directive';
import { IfpSearchComponent } from 'src/app/scad-insights/ifp-widgets/ifp-atoms/ifp-search/ifp-search.component';
import { Title } from '@angular/platform-browser';
import { ThemeService } from 'src/app/scad-insights/core/services/theme/theme.service';
import { ActivatedRoute } from '@angular/router';
import { IFPMsalService } from '../../core/services/IFP-msal.service';
import { title } from '../../core/constants/header.constants';


@Component({
    selector: 'app-glossery',
    templateUrl: './glossery.component.html',
    styleUrls: ['./glossery.component.scss'],
    imports: [IfpBreadcrumbsComponent, TranslateModule, CommonModule, IfpDropdownComponent,
    IfpNoDataComponent, ListWithSwitchComponent, PaginationComponent, IfpTooltipDirective, IfpSearchComponent]
})
export class GlosseryComponent implements OnInit, AfterViewChecked {

  onSearch: string = '';
  glossoryFilters: any = [];
  domainList: any = [];
  public themeList: any = [];
  public selectedChar!: any;
  public isSelectedDomain: any = [];
  public currentPage: number = 1;
  public perPage: number = 10;
  public selectedTheme: string[] = [];
  public glosseryList: any = [];
  public offset: number = 0;
  public searchString: string = '';
  public totalCount: number = 0;
  public subs = new SubSink();
  searchInput = new Subject<string>();
  public sortSelectedValue: string = 'ASC';
  public characters: any;
  public isSort: boolean = false;
  public search: string = '';
  public isFilter: boolean = false;
  public change: boolean = false;
  public selectedLang: string = '';
  public isCheckedClear: boolean = false;
  public queryParams: any = [];


  pageData: PageData[] = [
    {
      title: 'Home',
      route: '/home'
    },
    {
      title: 'Glossary',
      route: ''
    }
  ];

  public sortValue = {
    label: 'A-Z',
    value: 'ASC',
    key: 'alphabetical'
  };

  public sort = [{
    label: 'A-Z',
    value: 'ASC',
    key: 'alphabetical',
    icon: 'ifp-rotate-270 ifp-icon-rightarrow'
  },
  {
    label: 'Z-A',
    value: 'DESC',
    key: 'alphabetical',
    icon: 'ifp-rotate-90 ifp-icon-rightarrow'
  }
  ];

  constructor(private store: Store, private _cdr: ChangeDetectorRef, private _titleService: Title, private _themeService: ThemeService, private _route: ActivatedRoute,
    private _msalService:IFPMsalService) {
    this.subs.add(this.searchInput
      .pipe(debounceTime(800))
      .subscribe((value) => {
        this.currentPage = 1;
        this.offset = 0;
        this.searchString = value;
        this.isSelectedDomain = [];
        this.selectedTheme = [];
        this.themeList = [];
        this.isFilter = false;
        this.change = true;
        this.getGlosseryList();
      }));
    this._titleService.setTitle(`${title.bayaan} | Glossary`);

    (window as any)?.dataLayer?.push({
      'event': 'page_load',
      'page_title_var': 'Glossary',
      'userId': this._msalService.convertEmail(this._msalService.getLoginData.account.username)
    });

  }

  ngOnInit() {
    this._themeService.defaultLang$.subscribe((lang: string) => {
      this.selectedLang = lang;
    });
    this.isFilter = true;
    this.store.dispatch(loadGlosseryFilter());
    this.store.select(selectGlosseryFilterResponse).subscribe(resp => {
      this.glossoryFilters = resp.resp;
      this.createDomainList();
      this.getGlosseryList();
    });
    if (this.glossoryFilters) {
      this._route.queryParams.subscribe((params: any) => {
        if (params) {
          this.queryParams = params;
          if (this.queryParams?.query) {
            this.searchInput.next(this.queryParams.query);
          }
          if (this.queryParams?.filterBy) {
            this.changeDomain([this.queryParams.filterBy]);
          }
        }
      });
    }
  }

  createDomainList() {
    this.domainList = [];
    if (this.glossoryFilters?.domains?.length > 0) {
      this.glossoryFilters.domains.forEach((element: { name: any; }) => {
        this.domainList.push(element.name);
      });
    }
  }

  setThemeAndDomain(domain: string) {
    this.isSelectedDomain = domain;
    const themeData: any = [];
    this.themeList = [];
    this.isSelectedDomain.forEach((element: string) => {
      if (this.glossoryFilters?.domains) {
        themeData.push(...this.glossoryFilters.domains.find((x: { name: string; }) => x.name == element).items);
      }
    });
    themeData.forEach((element: any) => {
      this.themeList.push(element.name);
    });
    this.getGlosseryList();
  }

  changeDomain(event: any) {
    this.isCheckedClear = true;
    this.currentPage = 1;
    this.offset = 0;
    this.selectedChar = '';
    this.selectedTheme = [];
    this.searchString = '';
    this.setThemeAndDomain(event);
  }

  changeTheme(event: any) {
    this.isCheckedClear = false;
    this.currentPage = 1;
    this.offset = 0;
    this.selectedChar = undefined;
    this.selectedTheme = event;
    this.searchString = '';
    this.getGlosseryList();
  }

  searchResult(event: any) {
    this.searchInput.next(event);
  }

  selectChar(char: any) {
    this.currentPage = 1;
    this.offset = 0;
    this.selectedChar = char;
    this.getGlosseryList();
  }

  getGlosseryList() {
    let filterData: any;
    if (this.selectedLang == 'en') {
      filterData = {
        filters: {
          TOPIC_EN: {},
          THEME_EN: {},
          TITLE_EN: {}
        },
        sortBy: {}
      };
    }

    if (this.selectedLang == 'ar') {
      filterData = {
        filters: {
          TOPIC_AR: {},
          THEME_AR: {},
          TITLE_AR: {}
        },
        sortBy: {}
      };
    }
    const pagination = {
      page: this.currentPage,
      per_page: this.perPage,
      searchString: this.searchString
    };


    if (this.isSelectedDomain && this.selectedLang == 'en') {
      filterData.filters.TOPIC_EN = this.isSelectedDomain;
    }

    if (this.selectedLang == 'en') {
      if (this.selectedTheme?.length > 0) {
        filterData.filters.THEME_EN = this.getSelectedTheme();
      }
    }

    if (this.selectedChar && this.selectedLang == 'en') {
      filterData.filters.TITLE_EN = [this.selectedChar];
    }

    if (this.selectedChar && this.selectedLang == 'ar') {
      filterData.filters.TITLE_AR = [this.selectedChar];
    }

    if (this.isSelectedDomain && this.selectedLang == 'ar') {
      filterData.filters.TOPIC_AR = this.isSelectedDomain;
    }

    if (this.selectedLang == 'ar') {
      if (this.selectedTheme?.length > 0) {
        filterData.filters.THEME_AR = this.getSelectedTheme();
      }
    }

    filterData.sortBy = {
      alphabetical: this.sortSelectedValue
    };

    this.store.dispatch(loadGlosseryList({ pagination: pagination, filter: filterData }));
    this.store.select(selectGlosseryListResponse).subscribe(resp => {
      this.glosseryList = resp.resp.results;
      this.totalCount = resp.resp.totalCount;
      this.characters = resp.resp.alphabets;
    });
  }

  getSelectedTheme() {
    const themes: any = [];
    this.selectedTheme.forEach((element: any) => {
      themes.push(element);
    });
    return themes;
  }


  onPageChange(event: any) {
    this.currentPage = event.page;
    this.offset = event.offset;
    this.getGlosseryList();
  }

  sortClick(event: any) {
    this.sortSelectedValue = event.value;
    this.getGlosseryList();
  }

  objectKeys(obj: any): string[] {
    return obj ? Object.keys(obj) : [];
  }

  ngAfterViewChecked(): void {
    if (this.change) {
      this.isFilter = true;
      this._cdr.detectChanges();
      this.change = false;
    }
  }

  checkSelectAll(type: string) {
    let isSelectAll = false;
    const sortedArray1 = (type == 'domain' ? this.domainList : this.themeList).slice().sort();
    const sortedArray2 = (type == 'domain' ? this.isSelectedDomain : this.selectedTheme).slice().sort();
    isSelectAll = JSON.stringify(sortedArray1) === JSON.stringify(sortedArray2);
    return isSelectAll;
  }
}
