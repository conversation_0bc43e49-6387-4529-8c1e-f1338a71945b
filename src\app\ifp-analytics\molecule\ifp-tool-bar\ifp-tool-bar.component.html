
<div class="ifp-tool-bar" [ngClass]="{'ifp-tool-bar--editor': editDriver}">
  <ifp-tool-download [name]="name" [data]="data" class="ifp-tool-bar__download">
    <ng-content #name></ng-content>

  </ifp-tool-download>
  @if(driverList?.length !==0) {
    <ifp-tool-bar-change-driver></ifp-tool-bar-change-driver>
    @if (enableEditDriver && driverList && driverList.length > 1 ) {
      <div class="ifp-tool-bar__edit">
        <ifp-button [buttonClass]="buttonClass.secondary" [label]="'Edit Drivers'" (ifpClick)="editDriverEvent()" [iconClass]="'ifp-icon-edit'"></ifp-button>
      </div>
    }

    @if (!editDriver) {
      @for (item of driverList; track item; let index = $index) {
        @if (item.selected) {
          <ifp-tool-bar-driver-controller (rangeOut)="rangeSelected($event,item ,index )" [driver]="{ options: item.options}"  class="ifp-tool-bar__stepper"  [name]="item.label" ></ifp-tool-bar-driver-controller>
        }
         }
    }@else {

      <div  class="ifp-tool-bar__edit-list">
        <div  class="ifp-tool-bar__edit-list-text">
          {{'You can select up to' | translate}} 3
        </div>
        <ifp-option-box [limit]="3" [isWrapper]="true" [showBorder]="false" [boxOptions]="currentDriver"></ifp-option-box>

      </div>
    }

    <div class="ifp-chart-toolbar__action-box">
      @if (!editDriver) {
      <ifp-button [label]="'reset to default'" [buttonClass]="buttonClass.secondary" (ifpClick)="ResetToDefault()"></ifp-button>
      }@else {
        <ifp-button [label]="'Save'" [buttonClass]="buttonClass.secondary" (ifpClick)="save()"></ifp-button>
      }
    </div>
  } @else {
    <div class="ifp-tool-bar__no-data">
      {{'Drivers not available' | translate}}
    </div>

  }



</div>
