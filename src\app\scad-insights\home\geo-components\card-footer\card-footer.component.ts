import { ChangeDetectorRef, Component, Input, On<PERSON><PERSON><PERSON>, Renderer2, Output, EventEmitter, ViewChild, OnChanges, OnInit } from '@angular/core';
import { NgbModule } from '@ng-bootstrap/ng-bootstrap';
import { FormsModule } from '@angular/forms';
import { buttonClass } from '../../../core/constants/button.constants';
import { selectGetMyAppsMappedData } from '../../../store/myApps/myAppsGlobal.selector';
import { geoMapContentType, geoMapId } from '../../../core/constants/myapps.constants';
import { Store } from '@ngrx/store';
import { setMyAppsUpdate, unsubscribeMyAppsUpdate } from '../../../store/myApps/myAppsGlobal.action';
import { SubSink } from 'subsink';
import { IfpButtonComponent } from '../../../ifp-widgets/ifp-atoms/ifp-button/ifp-button.component';
import { CommonModule } from '@angular/common';
import { BehaviorSubject, distinctUntilChanged } from 'rxjs';
import { TranslateModule } from '@ngx-translate/core';
import { SharedService } from '../../geo-services/shared.service';
import { setEmailUpdate, setNotificationUpdate, unsubscribeNotificationUpdate } from 'src/app/scad-insights/store/notification/notification.action';
import { IfpModalComponent } from 'src/app/scad-insights/ifp-widgets/ifp-organism/ifp-modal/ifp-modal.component';
import { IfpAlertBoxComponent } from 'src/app/scad-insights/ifp-widgets/ifp-molecules/ifp-alert-box/ifp-alert-box.component';
import { selectGetMappedData } from 'src/app/scad-insights/store/notification/notification.selector';
import { IfpDocumentDownloadComponent } from 'src/app/scad-insights/ifp-widgets/ifp-molecules/ifp-document-download/ifp-document-download.component';
import { IfpCommentsComponent } from 'src/app/scad-insights/ifp-widgets/ifp-organism/ifp-comments/ifp-comments.component';
import { getInsights } from 'src/app/scad-insights/store/chart-insights/chart-insights.action';
import { InsightsService } from 'src/app/scad-insights/core/services/chart-insight/chart-insight.service';
import { ToasterService } from 'src/app/scad-insights/core/services/tooster/ToastrService.service';
import { selectInsightResponse } from 'src/app/scad-insights/store/chart-insights/chart-insights.selector';
import { CommonApiService } from 'src/app/scad-insights/core/services/common-api/commonApi.service';
import { Router } from '@angular/router';
import { DownloadService } from 'src/app/scad-insights/core/services/download.service';
// import * as html2pdf from 'html2pdf.js';




@Component({
    selector: 'app-card-footer',
    templateUrl: './card-footer.component.html',
    styleUrls: ['./card-footer.component.scss'],
    imports: [
    NgbModule,
    FormsModule,
    IfpButtonComponent,
    CommonModule,
    TranslateModule,
    IfpModalComponent,
    IfpAlertBoxComponent,
    IfpDocumentDownloadComponent,
    IfpCommentsComponent
]
})
export class CardFooterComponent implements OnInit, OnChanges, OnDestroy {
  @Input() label!: string;
  @Input() addMyAppsLanding: boolean = false;
  @Input() toolbarComment: boolean = false;
  @Output() remove = new EventEmitter();
  @Input() previewUrl: any = undefined;
  @Input() viewDownload: boolean = false;
  @Output() downloadClicked = new EventEmitter();
  @Output() expandClick: EventEmitter<boolean> = new EventEmitter<boolean>();
  @ViewChild('subscribeNotification') subscribeNotificationModal!: IfpModalComponent;

  isClassActive: boolean = false;

  popTitle = '';
  rangeValue: number = 50;
  public myAppsStatus = new BehaviorSubject(false);
  public buttonClass = buttonClass;
  public subs = new SubSink();
  public notificationSelector!: boolean;
  public isSubscNotifOpen: boolean = false;
  public viewComment: boolean = false;
  public insightData: any = [];
  public geoMapId = geoMapId;
  public tncState!: boolean;
  public fileTypes: string[] = ['pdf', 'png', 'xl'];
  public summaryRecords: any = [];
  public showSummary: boolean = false;
  public isExpanded: boolean = true;

  constructor(private renderer: Renderer2, private store: Store, private _cdr: ChangeDetectorRef, private sharedService: SharedService, private _insightService: InsightsService, private _toasterService: ToasterService, private _commonApiService: CommonApiService,
    private router:Router, private downloadService:DownloadService) {
    this.subs.add(this.store.select(selectGetMyAppsMappedData(geoMapId)).subscribe(myapps => {
      this.myAppsStatus.next(myapps);
      // this._cdr.detectChanges();
    }));
  }

  ngOnInit() {
    this.store.dispatch(getInsights({ nodeId: geoMapId }));
    this.subs.add(
      this.store.select(selectInsightResponse).subscribe((chartInsight: any) => {
        this.insightData = chartInsight;
        this._cdr.detectChanges();
      })
    );
    this.subs.add(this._commonApiService.getDownloadTermsStatus(geoMapId).subscribe((res: any) => {
      if (res) {
        if (res.status) {
          this.tncState = res.status;
        }
      }
    }));

  }

  ngOnChanges() {
    this.subs.add(this.store.select(selectGetMappedData(geoMapId)).pipe(distinctUntilChanged((prev, curr) => prev === curr)).subscribe(data => {
      this.notificationSelector = data[geoMapId]?.isNotification ? data[geoMapId].isNotification : false;
      this._cdr.detectChanges();
    }));
  }

  openPopup(title: any) {
    this.popTitle = title;
  }

  printChart() {
    // this.sharedService.highChartsArray.map((chart: any) => {
    //   chart.mainChart.exportChart(
    //     {
    //       type: 'application/pdf'
    //     }
    //   );
    // });
    // let chart = this.sharedService.highChartsArray[0];
    // let exportOptions = {
    //   type: 'application/pdf',
    //   // scale: 1, // Adjust as needed
    //   chartOptions: {
    //     chart: {
    //       marginLeft: 100, // Adjust padding on the left
    //       marginRight: 100, // Adjust padding on the right
    //       spacingTop: 20, // Adjust spacing on top
    //       spacingBottom: 20 // Adjust spacing on bottom
    //     }
    //   }
    // };
    //
    // chart.mainChart.exportChart(exportOptions);
    // const pdfOptions = {
    //   margin: 10,
    //   filename: 'content.pdf',
    //   image: { type: 'jpeg', quality: 0.98 },
    //   html2canvas: { scale: 2 },
    //   jsPDF: { unit: 'mm', format: 'a4', orientation: 'portrait' }
    // };
    // const pdfElement = document.getElementById('by_citizenship');
    // html2pdf().from(pdfElement).set(pdfOptions).outputPdf();
  }

  onSliderChange(event: Event) {
    const newValue = (event.target as HTMLInputElement).valueAsNumber;
    // this.renderer.setStyle(document.querySelector('.ifp-section__left'), 'width', newValue + "% !important");
    const ifpSectionLeft = document.querySelector('.ifp-section__left');
    // @ts-expect-error
    ifpSectionLeft.style.width = `${newValue}%`;
  }

  removeEvent() {
    this.remove.emit();
  }

  addDataMyApps(status: boolean) {
    if (status) {
      this.store.dispatch(unsubscribeMyAppsUpdate({ id: geoMapId, title: this.label ? this.label : '', contentType: geoMapContentType }));
    } else {
      this.store.dispatch(setMyAppsUpdate({ id: geoMapId, title: this.label ? this.label : '', contentType: geoMapContentType }));
    }
  }

  // Notification enable/disable
  addNotification(current: boolean) {
    if (!current) {
      this.store.dispatch(setNotificationUpdate({ id: geoMapId, title: this.label ? this.label : '', contentType: geoMapContentType }));
      this.subscribeNotificationModal.createElement();
      this.isSubscNotifOpen = true;
      setTimeout(() => {
        this.subscribeNotificationModal.removeModal();
        this.isSubscNotifOpen = false;
      }, 5000);
    } else {
      this.store.dispatch(unsubscribeNotificationUpdate({ id: geoMapId }));
    }
  }

  setEmailNotifStatus(status: boolean) {
    if (status) {
      this.store.dispatch(setEmailUpdate({ id: geoMapId }));
      setTimeout(() => {
        this.subscribeNotificationModal.removeModal();
        this.isSubscNotifOpen = false;
      }, 400);
    } else {
      this.subscribeNotificationModal.removeModal();
    }
  }

  closeModal() {
    this.subscribeNotificationModal.removeModal();
  }

  addInsight(value: string) {
    const data = {
      nodeId: geoMapId,
      nodeTitle: this.label,
      nodeLink: ['/home'],
      insight: value
    };
    this.subs.add(
      this._insightService.addInsights(data).subscribe((res: any) => {
        this.store.dispatch(getInsights({ nodeId: geoMapId }));
      })
    );
  }

  updateInsight(event: { item: any, newVal: string }) {
    const data = {
      nodeId: geoMapId,
      nodeTitle: this.label,
      nodeLink: ['/home'],
      insight: event.newVal
    };
    this.subs.add(
      this._insightService.editInsights(data, event.item.ID).subscribe((res: any) => {
        if (res.status === 'success') {
          this._toasterService.success('Insight Updated successfully');
          this.store.dispatch(getInsights({ nodeId: geoMapId }));
        } else {
          this._toasterService.error('Failed to update insight');
        }
        this._cdr.detectChanges();
      })
    );
  }

  deleteInsight(insightId: string) {
    this.subs.add(
      this._insightService.deleteInsights(insightId).subscribe((res: any) => {
        if (res.status === 'success') {
          this._toasterService.success('Insight deleted successfully');
          this.store.dispatch(getInsights({ nodeId: geoMapId }));
        } else {
          this._toasterService.error('Failed to delete insight');
        }
        this._cdr.detectChanges();
      })
    );
  }

  downloadDoc(event: any) {
    this.downloadClicked.emit(event);
  }

  toggleExpand() {
    this.isExpanded = !this.isExpanded;
    this.expandClick.emit(this.isExpanded);
  }

  printDownload(){
    this.downloadService.printGeoSpatial.set(true);
    this.downloadService.isPrinting=true;
    setTimeout(() => {
      this.router.navigate(['/',
        {
          outlets: {
            'print': ['print']
          }
        }]);
    }, 300);
    setTimeout(() => {
      this.downloadService.isPrinting=false;
      this.downloadService.printGeoSpatial.set(false);
    }, 3000);
  }



  ngOnDestroy(): void {
    this.subs.unsubscribe();
    if (this.isSubscNotifOpen) {
      this.subscribeNotificationModal.removeModal();
    }
  }
}
