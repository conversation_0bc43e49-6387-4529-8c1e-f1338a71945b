@if (!workFlowRunning()) {
<div class="ifp-adv-source">
  @if (showUploadScreen) {
  <span class="ifp-link" (click)="cancelUpload()"><em class="ifp-icon ifp-icon-leftarrow"></em>{{'Upload File' |
    translate}}</span>
  <div class="ifp-adv-tool__item-box ifp-adv-tool__item-box--info ifp-adv-source__detail-box" [ngClass]="{'ifp-adv-source__error-light': fileError}">
    <em class="ifp-icon ifp-icon-cross ifp-adv-source__close" (click)="cancelUpload()"></em>
    <div class="ifp-adv-source__icon-sec">
      <em class="ifp-icon ifp-icon-detial-view ifp-adv-tool__item-box-icon"></em>
      <span class="ifp-adv-source__type">{{fileDetails?.type}}</span>
    </div>
    <div class="ifp-adv-source__detail-sec">
      <p class="ifp-adv-tool__item-box-name">{{fileDetails?.name}}</p>
      <ifp-progress-bar class="ifp-adv-source__progress" [color]="color.greenDark"
        [width]="uploadDataProgess()+'%'"></ifp-progress-bar>
      <div class="ifp-adv-source__upload-detail">
        <p class="ifp-adv-source__uploaded-time">{{fileDetails?.addedOn | date: dateFormat.medium}}</p>
        <!-- <em class="ifp-icon" [class]="success ? 'ifp-icon-tick-border' : 'ifp-icon-round-cross'"
          [style.color]="success ? color.greenDark : color.red"></em> -->
      </div>
    </div>
  </div>
  @if (sheetList && sheetList.length) {
  <div class="ifp-adv-source__meta-sec">
    <p class="ifp-adv-tool__label">{{'Select Worksheet' | translate}}</p>
    <app-ifp-dropdown class="ifp-adv-tool__dropdown" [singleDefaultSelect]="false" [selectedValue]="selectedWorksheet"
      (dropDownItemClicked)="selectWorkSheet($event)" [dropDownItems]="sheetList"></app-ifp-dropdown>
  </div>
  }
  } @else {
  <p class="ifp-adv-tool__desc">{{'Select your preferred data source to import your data.' | translate}}</p>
  <div class="ifp-adv-tool__item-box" (click)="openLibrary()">
    <em class="ifp-icon ifp-icon-library ifp-adv-tool__item-box-icon" [style.backgroundColor]="color.cyan"></em>
    <p class="ifp-adv-tool__item-box-name">{{'From My Bayaan Library' | translate}}</p>
  </div>
  <div class="ifp-adv-tool__item-box" (click)="openImportIndicatorModal()">
    <em class="ifp-icon ifp-icon-browse ifp-adv-tool__item-box-icon" [style.backgroundColor]="color.yellow"></em>
    <p class="ifp-adv-tool__item-box-name">{{'From Indicators' | translate}}</p>
  </div>
  }
  @if (isDataset) {
  @if (showUploadScreen) {
  <ifp-button [label]="'Proceed'" (ifpClick)="saveFile()" class="ifp-adv-source__button"
    [buttonClass]="fileError || !success ? buttonClass.disabled+ ' '+ buttonClass.large : buttonClass.secondary+ ' '+ buttonClass.large"
    [iconClass]="'ifp-icon-rightarrow'"></ifp-button>
  } @else {
  <p class="ifp-adv-tool__divider"><span class="ifp-adv-tool__divider-text">{{'OR' | translate}}</span></p>
  <app-ifp-db-file-uploader class="ifp-adv-source__browse" [iconBackgroung]="'ifp-file-upload__blue'" [supportText]="'Supported file formats'"
    [dragAndDropText]="'Drag & Drop File' | translate" [isImage]="false" [allowedExtensions]="allowedExtensions"
    (fileUpload)="onBrowse($event)"></app-ifp-db-file-uploader>
  }
  }

  @if (fileDetails && !showUploadScreen ) {
  <div class="ifp-adv-tool__content">
    <div class="ifp-adv-tool__item-box ifp-adv-tool__item-box--info">
      <em class="ifp-icon ifp-icon-detial-view ifp-adv-tool__item-box-icon"></em>
      <p class="ifp-adv-tool__item-box-name">{{fileDetails?.name}}</p>
      <div class="ifp-adv-tool__info">
        <em class="ifp-icon ifp-icon-info-round"></em>
        <div class="ifp-adv-tool__info-box">
          <p class="ifp-adv-tool__info-text"><span class="ifp-adv-tool__info-title">{{'Added by' |
              translate}}</span>: {{fileDetails?.addedBy}}</p>
          <p class="ifp-adv-tool__info-text"><span class="ifp-adv-tool__info-title">{{'Added on' |
              translate}}</span>: {{fileDetails?.addedOn | date: dateFormat.medium}}</p>
          <p class="ifp-adv-tool__info-text"><span class="ifp-adv-tool__info-title">{{'Last modified on' |
              translate}}</span>: {{fileDetails?.lastModified | date: dateFormat.medium}}</p>
          @if (fileDetails && fileDetails.size !== 0) {
          <p class="ifp-adv-tool__info-text"><span class="ifp-adv-tool__info-title">{{'File size' |
              translate}}</span>: {{(fileDetails.size/1024) | number: '1.0-1'}} KB</p>
          }
        </div>
      </div>
    </div>
    <!-- } -->
  </div>
  }
</div>

<app-ifp-modal [overlayClass]="'ifp-modal__bg-grey'" [modalClass]="'ifp-modal__import-indicators'" #indicatorModal>
  @if (isImportModelOpen()) {
  <app-ifp-import-indicators [heading]="'Select the data you want to upload'" (cancel)="closeImport()"
    [noDataText]="'No Data Available'" [loader]="loaderProcess" [isSingleSelect]="true"
    [primaryButtonText]="'Add Indicator Data' | translate" (addToDashboard)="addAllIndicators($event)"
    [importType]="'browse'" [isPrep]="true" [isHideOfficialScreener]="false"></app-ifp-import-indicators>
  }
</app-ifp-modal>

<app-ifp-modal [overlayClass]="'ifp-modal__bg-grey'" [modalClass]="'ifp-modal__import-indicators'" [isClose]="true"
  #libraryListModal>
  @if (isLaibraryModelOpen()) {
  <ifp-prep-library [isModal]="true" (selectFileAll)="getFromLibrary($event)"
    (closeModal)="closeLibrary()" [isLibrary]="true"></ifp-prep-library>
  }
</app-ifp-modal>
} @else {
<div class="ifp-center-loader">
  <app-ifp-spinner></app-ifp-spinner>
  <p> {{'Workflow Running'}} </p>
</div>
}
