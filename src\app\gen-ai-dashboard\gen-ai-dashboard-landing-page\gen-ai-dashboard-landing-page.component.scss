@use "../../../assets/ifp-styles/abstracts/index" as *;
:host {
  display: block;
}
.ifp-gen-ai-dashboard-landing-page {
  $gap: $spacer-3;
  background-color: $ifp-color-white;
  border-radius: 40px 40px 0 0;
  padding: $spacer-5;
  &__sec-1{
    display: flex;
    flex-wrap: wrap;
    margin: $spacer-0 (-$gap);
  }
  &__filter-picker {
    margin-inline-start: $spacer-4;
  }
  &__top {
    display: flex;
    justify-content: end;
    width: 100%;
  }
  &__guage {
    margin-top: $spacer-6;
  }
  &__high-chart {
    width: 100%;
    display: block;
  }
  &__card {
    box-shadow: -20px 1px 130px rgba(153, 160, 158,0.09);
    border: 1px solid $ifp-color-pale-grey;
    border-radius: 20px;
    margin: $gap;
    padding: $spacer-2;
    background-color: $ifp-color-section-white;
    &--30 {
      width: calc(30% - (2 * $gap));
    }
    &--66 {
      width: calc(66% - (2 * $gap));
    }
    &--33 {
      width: calc(33.33% - (2 * $gap));
    }
    &--25 {
      width: calc(25% - (2 * $gap));
    }
    &--37 {
      overflow: hidden;
      width: calc(37.5% - (2 * $gap));
    }
    &--100 {
       width:calc(100% - (2 * $spacer-4));
    }
    &--number {
      overflow: hidden;
    }
  }
  &__header {
    font-weight: $fw-bold;
    font-size: $ifp-fs-4;
    padding: $spacer-2 $spacer-3;
  }

&__dropdown-version{
  margin-inline-end: $spacer-3;
}
}
