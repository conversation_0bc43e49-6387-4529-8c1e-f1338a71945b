import { NgClass } from '@angular/common';
import { Component, input, model, OnChanges, output, signal, SimpleChanges, viewChild, WritableSignal } from '@angular/core';
import { OutsideClickDirective } from 'src/app/scad-insights/core/directives/outsideClick.directive';
@Component({
  selector: 'ifp-dxp-label-dropdown',
  imports: [NgClass, OutsideClickDirective],
  templateUrl: './dxp-label-dropdown.component.html',
  styleUrl: './dxp-label-dropdown.component.scss'
})
export class DxpLableDropdownComponent implements OnChanges {

  public dropdownViewChild = viewChild('dropdownViewChild');
  public icon = input('ifp-icon-calender');
  // public label = input('Date');
  public dropdownKey = input<string>()
  public dropdownValue = model<string>();
  public hideCross = input(true);
  public optionArrayKey = input('');

  public multiSelect: WritableSignal<boolean> = signal(false);
  public selectedItems = input<selectedItem[]>([]);
  public isOpen = model<boolean>(false);
  public removeItem = output();


  // outputs //
  public optionSelected = output<string | Record<string, string>>();



  ngOnInit(): void {

  }

  ngOnChanges(changes: SimpleChanges): void {
    const key = this.dropdownKey();
    const optionKey = this.optionArrayKey();
    this.selectedItems()?.forEach(element => {
      if (element.dropdownValue === undefined || element.dropdownValue === null) {
      const options = element[optionKey];
      element.dropdownValue = key
      ? options?.[0]?.[key]
      : options?.[0];
    }
    });
  }
  
  selectItem(item: string | Record<string, string>, index: number) {
    this.selectedItems()[index].dropdownValue = item;
    this.optionSelected.emit(item);
  }


  outsideClick() {
    this.isOpen.set(false);
  }

  close(){
    console.log('asasa');
    this.removeItem.emit()
  }
}


interface selectedItem {
  name: string;
  type: string;
  [key: string]: any;
  isOpen?: boolean;
  dropdownValue?: any;
}
