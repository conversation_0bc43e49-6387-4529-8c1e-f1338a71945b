import { ChangeDete<PERSON><PERSON><PERSON>, <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, OnInit, ViewChild, signal } from '@angular/core';
import { buttonClass } from 'src/app/scad-insights/core/constants/button.constants';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { IfpPrepService } from '../ifp-prep-service';
import { SubSink } from 'subsink';
import { Store } from '@ngrx/store';
import { selectNodePrepResponse } from '../store/node-store/node-store.selectors';
import { prepsApiEndpoints } from '../constants/if-preps.constants';
import { BoxOption, Node } from '../interface/ifp-data-prep.interface';
import { FormArray, FormControl, FormGroup } from '@angular/forms';
import { loadCreateNodeSuccessCleaningCreate, loadCreateNodeSuccessCleaningRemove, loadCreateNodeSuccessCleaningUpdate, loadNode } from '../store/node-store/node-store.actions';
import { prepStatusStore, workFlowState } from '../constants/ifp-state.contants';
import { ColumnUpstream } from '../ifp-prep-select-data/interface/prep-selection.interface';
import { Router } from '@angular/router';
import { IfpNoDataComponent } from '../../../../scad-insights/ifp-widgets/ifp-molecules/ifp-no-data/ifp-no-data.component';
import { IfpSpinnerComponent } from '../../../../scad-insights/ifp-widgets/ifp-molecules/ifp-spinner/ifp-spinner.component';
import { IfpPrepPopupComponent } from '../../../organism/ifp-prep-popup/ifp-prep-popup.component';
import { IfpModalComponent } from '../../../../scad-insights/ifp-widgets/ifp-organism/ifp-modal/ifp-modal.component';
import { CardData } from 'src/app/shared/molecule/ifp-tool-card/ifp-tool-card.component';
import { IfpOptionBoxComponent } from '../../../molecule/ifp-option-box/ifp-option-box.component';
import { NgClass } from '@angular/common';
import { SubscriptionLike } from 'rxjs';
import { NodeState } from '../store/node-store/node-store.state';
@Component({
    selector: 'ifp-ifp-cleaning',
    imports: [TranslateModule, IfpNoDataComponent, IfpSpinnerComponent, IfpPrepPopupComponent, IfpModalComponent, IfpOptionBoxComponent, NgClass],
    templateUrl: './ifp-cleaning.component.html',
    styleUrl: './ifp-cleaning.component.scss'
})
export class IfpCleaningComponent implements OnInit, OnDestroy {
  @ViewChild('modalSucess') modalSucess!: IfpModalComponent;

  public loaderProcess = signal(false);
  public workflow = signal(false);
  public loader = true;
  public processed = false;
  public subs = new SubSink();
  public objectId !: string;
  public formControl: FormArray<FormGroup<{ name: FormControl<string | null>; opreation: FormControl<string | null>; customField: FormControl<string | null>; }>> | FormArray<never> | any = new FormArray([]);
  public resetView = signal(false);
  public nullValueOptions: BoxOption[] = [
    {
      label: this._translate.instant('Remove columns with only null values'),
      selected: false,
      name: 'all_null_columns',
      toolTipDisable: false
    },
    // {
    //   label: 'Remove rows with only NULL values',
    //   selected: false,
    //   name: 'all_null_rows'
    // },
    // {
    //   label: 'Remove columns with any NULL value',
    //   selected: false,
    //   name: 'any_null_columns'
    // },
    {
      label: this._translate.instant('Remove rows with null values from the selected columns.'),
      selected: false,
      name: 'all_null_rows',
      toolTipDisable: false
    },
    {
      label: this._translate.instant('Remove selected columns.'),
      selected: false,
      name: 'remove',
      toolTipDisable: false
    }
  ];


  public nullValues: string[] = [];

  public selectedCols: string[] = [];

  public columnOptions: BoxOption[] = [];
  public buttonClass = buttonClass;
  public progressInitial!: SubscriptionLike;
  public setWorkFlow = signal(false);

  constructor(private _router: Router, public _prepService: IfpPrepService, public _store: Store, private _cdr: ChangeDetectorRef, private _translate: TranslateService) {
  }

  ngOnInit(): void {
    if (!this._prepService.uploadedFileResponse()) {
      this._router.navigate(['analytics/data-preparation/upload-data']);
    } else {
      this._prepService.processDisable.set(true);
      this.subs.add(this._prepService.processEvent.subscribe((data: string) => {
        if (data !== '') {
          this.proceed();
        }
      }));
      this._prepService.processActive.set(true);
      this._prepService.showPreview.set(true);
      let init = true;
      this._store.dispatch(loadNode[loadCreateNodeSuccessCleaningCreate]({ config: { columns: [] } }));
      this.subs.add(
        this._store.select(selectNodePrepResponse).subscribe(dataState => {
          if (dataState.currentStatus === prepStatusStore.cleaningDataLoaded) {
            const nodeLength = dataState.data.nodes.length;
            const lastValue = dataState.data.nodes[nodeLength - 1];
            this.objectId = lastValue.object_id;
            this._prepService.processStatus.next(null);
            this.progressInitial?.unsubscribe();
            if (init) {
              init = false;
              this.progressInitial = this._prepService.processStatus.subscribe(dataWorkFlow => {
                if (dataWorkFlow?.workflow_status === workFlowState.completed) {
                  this.callUpStream(lastValue, dataState);
                  this.workflow.set(true);
                  this._prepService.disableSidebar.set(false);
                  this.progressInitial?.unsubscribe();
                }
              });
            } else if (!init && this.setWorkFlow()) {
              this.setWorkFlow.set(false);
              this.callUpStream(lastValue, dataState);
            }
          }
        })
      );

    }

  }

  /**
   * used to call column api upstream for listing columns
   * @param lastValue
   */
  callUpStream(lastValue: Node, dataState: NodeState) {
    const column = this._prepService.postMethodRequest(`${prepsApiEndpoints.selectNode}${lastValue.object_id}${prepsApiEndpoints.coloumUpstrem}`, dataState.data).subscribe((value: Record<string, ColumnUpstream>) => {
      const dataColumn = Object.values(value)[0];
      this.columnOptions = dataColumn.columns_1.map(data => {
        return { label: data.name, selected: true, toolTipDisable: false, null_percentage: data.null_percentage};
      });
      this.nullValues = [];
      this.getSelectedColumns(this.columnOptions);
      const dataNull: BoxOption[] = [];
      this.nullValueOptions.forEach(data => {
        data.selected = false;
        dataNull.push(data);
      }
      );
      this.nullValueOptions = dataNull;
      this.loader = false;
      if (this.selectedCols.length === 0 || this.nullValues.length === 0) {
        this._prepService.processDisable.set(true);
      }
      column.unsubscribe();
    });
  }



  closeSucessModal() {
    this.modalSucess.removeModal();
  }

  buttonClick(_event: CardData) {
    this.modalSucess?.removeModal();
  }



  getSelectedColumns(selectedCols: BoxOption[]) {
    this.selectedCols = [];
    this.selectedCols = selectedCols.map((col: BoxOption) => col.label);
    if (this.selectedCols.length === 0) {
      const dataNull: BoxOption[] = [];
      this.nullValueOptions.forEach(data => {
        data.selected = false;
        dataNull.push(data);
      }
      );
      this.nullValueOptions = dataNull;
      this.nullValues = [];
    }
    if (this.selectedCols.length === 0 || this.nullValues.length === 0) {
      this._prepService.processDisable.set(true);
    } else {
      this._prepService.processDisable.set(false);
    }
  }

  getSelectedNull(selectedCols: BoxOption[]) {
    this.nullValues = [];
    this.nullValues = selectedCols.map((col: BoxOption) => col.label);
    if (this.selectedCols.length === 0 || this.nullValues.length === 0) {
      this._prepService.processDisable.set(true);
    } else {
      this._prepService.processDisable.set(false);
    }
  }

  proceed() {
    this._prepService.processDisable.set(true);
    const config: CleanInterface = {
      columns: [],
      remove_nulls: {},
      remove_columns: []
    };
    this.nullValueOptions.map(data => {
      return config.remove_nulls[data.name ?? ''] = data.selected;
    });
    this.columnOptions.forEach(data => {
      if (data.selected) {
        config.columns.push(data.label);
      }
    });
    if (this.nullValueOptions.find(x => x.name == 'remove' && x.selected)) {
      config.remove_columns = config.columns;
    } else {
      config.remove_columns = [];
    }
    this._prepService.disableSidebar.set(true);
    this._prepService.processActive.set(true);
    this.loaderProcess.set(true);
    this._prepService.loaderProcess.set(true);
    this._store.dispatch(loadNode[loadCreateNodeSuccessCleaningUpdate]({ config: config, objectId: this.objectId }));
    const process = this._prepService.processStatus.subscribe(data => {
      if (data?.workflow_status === workFlowState.completed) {
        this._prepService.isShowPreview = true;
        // this.processed = true;
        this._prepService.disableSidebar.set(false);
        this.loaderProcess.set(false);
        this._prepService.loaderProcess.set(false);
        process.unsubscribe();
        this.setWorkFlow.set(true);
        this._prepService.processActive.set(false);
        this._store.dispatch(loadNode[loadCreateNodeSuccessCleaningCreate]({ config: { columns: [] } }));
        this.modalSucess.createElement();
        this._cdr.detectChanges();
      } else if (data?.workflow_status === workFlowState.error) {
        this._prepService.isShowPreview = false;
        this.processed = false;
        this._prepService.disableSidebar.set(false);
        this.loaderProcess.set(false);
        this._prepService.loaderProcess.set(false);
        process.unsubscribe();
      }
    });
  }

  ngOnDestroy(): void {
    if (!this.processed) {
      this._store.dispatch(loadNode[loadCreateNodeSuccessCleaningRemove]({ objectId: this.objectId }));
    }
    this.subs.unsubscribe();
    this.modalSucess?.removeModal();
    this.progressInitial?.unsubscribe();
    this._prepService.showPreview.set(false);
    this._prepService.processEvent.next('');
  }
}
interface CleanInterface {
  columns: string[];
  remove_nulls: Record<string, boolean>;
  remove_columns?: string[]
}
