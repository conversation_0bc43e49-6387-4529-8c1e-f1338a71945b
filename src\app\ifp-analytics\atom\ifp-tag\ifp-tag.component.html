<div class="ifp-tag" [ngClass]="{'ifp-tag--box': isBoxView}" [style.backgroundColor]="backgroundValue" [style.border]="border ? ('1px solid '+colorValue) : 'none'" [style.color]="colorValue">
  <div class="ifp-tag__wrapper">@if (icon) {<em [class]="'ifp-icon ifp-tag__icon '  +icon"></em>}@if (info && info !=='') {<div class="ifp-tag__inner"><p class="ifp-tag__text">{{tagName | translate}}</p><em class="ifp-icon ifp-icon-info-round ifp-tag__info" [style.color]="colorValue" [appIfpTooltip]="info" [tooltipHead]="infoHead" [textLeft]="infoHead && infoHead !== '' ? true : false" [extraSpaceTop]="20"></em></div>} @else {<p class="ifp-tag__text">{{tagName | translate}}</p>}</div>

</div>
