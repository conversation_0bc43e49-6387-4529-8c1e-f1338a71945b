export interface PrepLibrary {
  count: number;
  next: string;
  previous: null;
  results: PrepLibraryResult[];
}

export interface PrepLibraryResult {
  object_id: string;
  owner: string;
  created_at: string;
  deleted_at: null;
  name: string;
  description: string;
  metadata?: Metadata;
  storage_backend: string;
  status: string;
  file: string;
}

interface Metadata {
  type?: string;
  columns: Column[];
  read_commence: string;
  read_complete: string;
  write_commence: string;
  write_complete: string;
}

interface Column {
  mean: null | number;
  name: string;
  type: string;
  stddev: null | number;
  max_value: (null | number | string)[];
  min_value: (null | number | string)[];
  max_length: null | number | number;
  min_length: null | number | number;
  null_count: number;
  valid_count: number;
  sample_value: (null | number | string)[];
  unique_count: number;
  null_percentage: number;
  valid_percentage: number;
  unique_percentage: number;
}

export interface WorkFlowStatusStream {
  workflow_status: string;
  nodes?: Record<string, any>;
  history?:  Record<string, any>;
  logs?: {
    timestamp: string;
    log_level: string;
    message: string;
  }[];
}

