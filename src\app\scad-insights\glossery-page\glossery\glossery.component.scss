@use '../../../../assets/ifp-styles/abstracts' as *;

.ifp-glossery {
  &__charecter {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    text-align: center;
    margin: $spacer-3 $spacer-0;
    font-weight: 600;
  }

  &__letter {
    margin: $spacer-1;
    cursor: pointer;
    font-size: $ifp-fs-6;

    &--active {
      color: $ifp-color-secondary-blue-dark;
    }

    &--disabled{
      pointer-events: none;
      color: $ifp-color-grey-7;
    }
  }

  &__pagination{
    margin-top: $spacer-5;
  }

  &__item{
    margin: $spacer-2;
  }

  &__title{
    display: flex;
    margin-bottom: $spacer-4;
  }

  &__heading{
    font-size: $ifp-fs-11;
    font-weight: bold;
    display: flex;
    .ifp-icon {
      font-size: inherit;
    }
  }

  &__heading-img{
    margin-right: $spacer-2;
  }

  &__filter {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    margin: $spacer-0 (-$spacer-2);
  }
  &__filter-item {
    margin: $spacer-0 $spacer-2;
    display: block;

    &--search {
      margin-left: auto;
      min-width: 380px;
    }
  }
}

:host::ng-deep {
  .ifp-glossery {
    &__filter-item {
      .ifp-dropdown {
        max-width: 270px;
        min-width: 300px;
        &--icon {
          min-width: 0;
        }
      }
    }
  }
}

:host-context([dir="rtl"]) {
  .ifp-glossery {
    &__heading-img{
      margin-left: $spacer-2;
      margin-right: $spacer-0;
    }
    &__filter-item {
      &--search {
        margin-right: auto;
        margin-left: $spacer-0;
      }
    }
  }
}

@include mobile-tablet {
  .ifp-glossery {
    &__heading {
      font-size: $ifp-fs-6;
    }
    &__title {
      margin-bottom: $spacer-3;
    }
    &__filter-item--search {
      margin-left: $spacer-2;
      margin-top: $spacer-3;
    }
  }
}

@include mobile {
  .ifp-glossery {
    &__filter-item {
      margin: $spacer-2;
    }
    ifp-search.ifp-glossery__filter-item {
      width: 100%;
    }
  }
}
