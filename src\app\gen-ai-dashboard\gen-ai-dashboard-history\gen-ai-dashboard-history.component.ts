import { TranslateModule } from '@ngx-translate/core';
import { Component, ElementRef, inject, OnDestroy, OnInit, output, signal } from '@angular/core';
import { IfpNoDataComponent } from "../../scad-insights/ifp-widgets/ifp-molecules/ifp-no-data/ifp-no-data.component";
import { IfpSpinnerComponent } from "../../scad-insights/ifp-widgets/ifp-molecules/ifp-spinner/ifp-spinner.component";
import { DatePipe } from '@angular/common';
import { KebabMenuOption } from 'src/app/dashboard-builder/molecule/ifp-kebab-menu/ifp-kebab-menu.interface';
import { genAiTestingApi } from '../constents/gen-ai-testing.constant';
import { SubSink } from 'subsink';
import { InfiniteScrollDirective } from 'ngx-infinite-scroll';
import { ApiGenAiService } from 'src/app/scad-insights/core/services/api-gen-ai.service';

@Component({
  selector: 'ifp-gen-ai-dashboard-history',
  imports: [IfpNoDataComponent, IfpSpinnerComponent,InfiniteScrollDirective,
   TranslateModule, DatePipe],
  templateUrl: './gen-ai-dashboard-history.component.html',
  styleUrl: './gen-ai-dashboard-history.component.scss'
})
export class GenAiDashboardHistoryComponent implements OnInit, OnDestroy{

public _apiService = inject(ApiGenAiService);
public _host = inject(ElementRef);
public back = output();
public prevewDetails = output<string>();
public historyData = signal<HistoryItem[]>([]);
public size = signal(0);
public offset= signal(0);
public page= signal(1);
public limit= signal(25);
public kebabOptions = signal<KebabMenuOption[] >([{
  name: 'Details',
  event: 'detail'
}]
);

public loader = signal(true);
public subs = new SubSink();

ngOnInit(): void {
  this.callHistory();
}

callHistory(page =1) {
  this.loader.set(true);
  this.subs.add(
  this._apiService.getMethodRequest(genAiTestingApi.history,{page: page, limit: this.limit()}).subscribe((data:HistoryGenAi) => {
    this.historyData.update(value => {
      value.push(...data.items);
      return value;
    });
    this.size.set(data.count);
    this.loader.set(false);
  }));
}

optionSelected(object:string) {
  this.prevewDetails.emit(object);
}

onPageChange() {
  const sizeValue = this.page() * this.limit();
  if(this.size() <= sizeValue){
      return;
  }
  this.page.set(this.page() + 1);
  this.callHistory(this.page());
}

ngOnDestroy(): void {
  this.subs.unsubscribe();
}
}

interface HistoryGenAi {
  items: HistoryItem[];
  count: number;
}

interface HistoryItem {
  object_id: string;
  run_name: string;
  created_at: string;
  evaluated_by: string;
}
