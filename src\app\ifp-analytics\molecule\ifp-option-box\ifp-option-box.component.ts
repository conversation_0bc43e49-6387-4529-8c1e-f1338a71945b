import { ChangeDetectionStrategy, Component, EventEmitter, Input, OnChanges, Output } from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';
import { BoxOption } from '../../data-prep/ifp-data-prep/interface/ifp-data-prep.interface';
import { IfpCheckBoxComponent } from '../../../scad-insights/ifp-widgets/ifp-atoms/ifp-check-box/ifp-check-box.component';
import { NgClass } from '@angular/common';
import { IfpProgressBarComponent } from '../../atom/ifp-progress-bar/ifp-progress-bar.component';
import { ifpColors } from 'src/app/scad-insights/core/constants/color.constants';

@Component({
    selector: 'ifp-option-box',
    templateUrl: './ifp-option-box.component.html',
    styleUrl: './ifp-option-box.component.scss',
    changeDetection: ChangeDetectionStrategy.OnPush,
    imports: [
        TranslateModule,
        Ifp<PERSON><PERSON><PERSON><PERSON><PERSON>Component,
        NgClass,
        IfpProgressBarComponent
    ]
})
export class IfpOptionBoxComponent implements OnChanges {

  @Input() showBorder: boolean = true;
  @Input() type: 'checkbox' | 'radio' = 'checkbox';
  @Input() name!: string;
  @Input() isHeader: boolean = false;
  @Input() isWrapper: boolean = false;
  @Input() boxOptions: BoxOption[] = [];
  @Input() limit!: number;
  @Input() selectedValueLength = 0;
  @Input() translation = true;
  @Input() reduceLabelWidth:boolean=false;
  @Input() showNullPercentage:boolean=false;
  @Output() selectOption: EventEmitter<BoxOption[]> = new EventEmitter<BoxOption[]>();
  public selectedOptions: BoxOption[] = [];
  public isSelectAll: boolean = false;
  public color = ifpColors;

  ngOnChanges() {
    this.selectedOptions = this.boxOptions?.filter((col: BoxOption) => col.selected);
    this.checkSelectAll();
  }

  setChecked(event: any, data: BoxOption) {
    if (this.limit<=this.selectedValueLength && !data.selected) {
      return;
    }
    data.selected = event;
    if (this.type === 'checkbox') {
      if (event) {
        this.selectedOptions.push(data);
      } else {
        const index = this.selectedOptions.findIndex(x => x.id == data.id);
        this.selectedOptions.splice(index, 1);
      }
      this.checkSelectAll();
    } else {
      this.boxOptions.forEach((option: BoxOption) => {
        option.selected = data.label === option.label;
      });
      this.selectedOptions = [];
      this.selectedOptions.push(data);
    }
    this.selectOption.emit(this.selectedOptions);
  }

  // To check the selected value
  checkSelectAll() {
    const selectedValue = this.boxOptions.filter(options=> options.selected );
    if (selectedValue.length === this.boxOptions.length) {
      this.isSelectAll= true;
    } else {
      this.isSelectAll = false;
    }
    this.selectedValueLength = selectedValue.length;
  }

  toggleSelectAll() {
    this.isSelectAll = !this.isSelectAll;
    if (this.isSelectAll) {
      this.boxOptions.map((option: BoxOption) => option.selected = true);
      this.selectedOptions = JSON.parse(JSON.stringify(this.boxOptions));
    } else {
      this.boxOptions.map((option: BoxOption) => option.selected = false);
      this.selectedOptions = [];
    }
    this.selectOption.emit(this.selectedOptions);
  }

  // checkSelected(id: number) {
  //   return !!this.selectedOptions.find(x => x.id == id);
  // }

}
