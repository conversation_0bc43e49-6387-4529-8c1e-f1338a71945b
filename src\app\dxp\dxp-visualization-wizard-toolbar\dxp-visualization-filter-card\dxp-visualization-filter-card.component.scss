@use "../../../../assets/ifp-styles/abstracts/index" as *;

:host {
  display: block;
  background-color: $ifp-color-grey-18;
  border-radius: 8px;
  padding: $spacer-3;
  border: 1px solid $ifp-color-grey-3;
  position: relative;
}

.ifp-wizard-filter-card {
  &__item {
    display: flex;
    margin-bottom: $spacer-1;
  }
  &__title {
    font-weight: $fw-medium;
  }
  &__value {
    color: $ifp-color-grey-2;
    margin-inline-start: $spacer-1;
  }
  &__delete {
    font-size: $ifp-fs-4;
    color: $ifp-color-blue-hover;
    position: absolute;
    top: $spacer-2;
    right: $spacer-2;
    cursor: pointer;
  }
}

:host-context([dir="rtl"]) {
  .ifp-wizard-filter-card {
    &__delete {
      right: auto;
      left: $spacer-2;
    }
  }
}
