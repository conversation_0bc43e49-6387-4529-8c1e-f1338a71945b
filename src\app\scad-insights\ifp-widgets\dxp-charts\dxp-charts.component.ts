import { ShortNumberPipe } from './../../core/pipes/shortNumber.pipe';
import { ChangeDetectorRef, Component, ElementRef, Input, OnChanges, OnDestroy, OnInit, Renderer2, SimpleChanges, ViewChild, inject, input } from '@angular/core';
import { DecimalPipe, DatePipe, NgClass } from '@angular/common';
import { HighchartsChartModule } from 'highcharts-angular';
import * as Highcharts from 'highcharts';
import { HighchartService } from 'src/app/scad-insights/core/services/highchart.service';
import { Subscription, fromEvent, SubscriptionLike } from 'rxjs';
import { ifpColors } from 'src/app/scad-insights/core/constants/color.constants';
import { ifpDistinctColors } from 'src/app/scad-insights/core/constants/color.constants';
import { TranslateService } from '@ngx-translate/core';
import { numberChartDatePipe } from 'src/app/scad-insights/core/pipes/numberChart.pipe';
import HighchartsMore from 'highcharts/highcharts-more';
import SolidGauge from 'highcharts/modules/solid-gauge';
HighchartsMore(Highcharts);
SolidGauge(Highcharts);
import { ThemeService } from '../../core/services/theme/theme.service';
import { Chart } from 'highcharts/highcharts.src';
import { IfpDropdownComponent } from '../ifp-atoms/ifp-dropdown/ifp-dropdown.component';
let that: any = this;

/**
 * DxpChartsComponent - A flexible chart component supporting multiple chart types
 *
 * Supported chart types:
 * - Line: Multi-series line charts with datetime x-axis
 * - Column: Multi-series column charts
 * - Bar: Multi-series bar charts
 * - Pie: Single-series pie charts with dropdown selection for multiple series
 * - Donut: Single-series donut charts with dropdown selection for multiple series
 *
 * Data format:
 * - For Line, Column, Bar charts: Array of series objects with {name, data, color?}
 * - For Pie, Donut charts: Array of series objects with {name, data} where data is array of values
 *
 * Features:
 * - Multi-series support for Line, Column, Bar charts
 * - Series selection dropdown for Pie and Donut charts
 * - Theme-aware styling
 * - Customizable tooltips, legends, and styling
 */
@Component({
  selector: 'ifp-dxp-charts',
  imports: [NgClass, HighchartsChartModule, IfpDropdownComponent],
  templateUrl: './dxp-charts.component.html',
  styleUrl: './dxp-charts.component.scss',
  providers: [DecimalPipe, numberChartDatePipe, DatePipe]
})
export class DxpChartsComponent implements OnChanges, OnInit, OnDestroy {

  private readonly datePipe = inject(DatePipe);

  @ViewChild('chart') chart!: any;
  @Input() chartClass: string = 'ifp-line-doted-axis';
  @Input() chartName: string = 'lineChart';
  @Input() comparison: string = 'Y/Y';
  @Input() tickIntervell!: number;
  @Input() height: number | null = 100;
  @Input() width!: number | null;
  @Input() axisXFormat = (value: any) => {
    return value;
  };

  @Input() data: any[] = [];

  @Input() yaxisLabel!: string;
  @Input() xAxisLabel!: string;
  @Input() format!: string;
  @Input() marker = true;
  @Input() enableAnimation = true;
  @Input() category!: string[];
  @Input() borderRadius!: number;
  @Input() tooltipLabel = '';
  @Input() comparisonEnable = true;
  @Input() markerColor: string = '#3bd6ad'
  public yAxis = input<Highcharts.YAxisOptions>();

  public xAxis = input<Highcharts.XAxisOptions>();
  public showXAxisLabel = input<boolean>(true);


  public plotOptions = input<Highcharts.PlotOptions>();

  public legends = input<Highcharts.LegendOptions>();
  public tooltip = input<Highcharts.TooltipOptions>();
  public isCircular = input<boolean>(false);
  public donutCenter = input(0);
  public donutCenterFunc = input<(chart: Chart) => void>();
  public custom = input<Record<string, any>>();

  public ifpColors = ifpColors;
  public plotBand: Record<string, any> = {
    line1: null,
    line2: null
  };

  public chartInstance!: Highcharts.Chart;
  public axisTitleColor: string = ifpColors.primaryGrey;
  public updateFlag = false;
  public clickSubscription: Subscription[] = [];
  public axisGroup!: Element;
  public subscription!: SubscriptionLike;
  public axisStyle = {
    fontSize: '1.2rem',
    color: '#6A7180',
    lineHeight: '1.4rem',
    fontWeight: '500',
    fontFamily: 'Noto Sans'
  }

  // Multi-series support properties
  public selectedSeriesIndex: number = 0;
  public seriesOptions: any[] = [];
  public showSeriesDropdown: boolean = false;
  // Category dropdown properties for pie charts
  public selectedCategoryIndex: number = 0;
  public categoryOptions: any[] = [];
  public showCategoryDropdown: boolean = false;

  public charts: Record<string, any> = {
    lineChart: {
      exporting: {
        enabled: false
      },
      chart: {
        title: {
          enabled: false
        },
        height: 180
      },
      title: {
        text: ''
      },

      credits: {
        enabled: false
      },
      xAxis: {
        lineColor: '#D9DCDD',
        tickColor: '#D9DCDD',
        tickWidth: 10,
        tickLength: 1,
        gridLineWidth: 30,
        lineWidth: 1,
        gridLineColor: '#F8F8F8',
        tickPosition: 'outside',
        tickmarkPlacement: 'outside',
        startOnTick: false,
        endOnTick: false,
        title: {
          enabled: true,
          text: this.xAxisLabel,
          style: {
            fontSize: '14px',
            fontWeight: '600',
          },
        },
        labels: {
          style: this.axisStyle,
          formatter: function (this: Highcharts.AxisLabelsFormatterContextObject): string {
            // Use the category value directly if categories are set
            if (that.category && typeof this.pos === 'number') {
              return that.category[this.pos] ?? String(this.value);
            }
            return String(this.value);
          }
        }
      },

      yAxis: {
        gridLineDashStyle: 'dot',
        gridLineColor: '#e6e6e6',
        tickWidth: 0,
        tickLength: 0,
        title: {
          enabled: false
        },
        labels: {
          formatter: (value: any) => {
            const number = (this._number.transform(+value.value, '1.0-2'))?.replaceAll(',', '');
            return this._shortNumber.transform(number, value.axis.dataMax);
          }
        }
      },
      legend: {
        rtl: this._translate.currentLang === 'ar',
        enabled: true
      },
      tooltip: {
        animation: false,
        hideDelay: 0,
        backgroundColor: '#182234',
        borderRadius: 10,
        shadow: true,
        distance: 0,
        style: {
          color: '#FFFFFF'
        },
        useHTML: true,
        outside: true,
        className: 'ifp-chart-tooltip',
        formatter: function (): any {
          const chartData: any = this;
          // Always use the correct category label for the hovered point
          const category = (that.category && chartData.point && typeof chartData.point.index === 'number')
            ? that.category[chartData.point.index]
            : chartData.key || chartData.x;
          return `<div class="ifp-chart-tooltip__wrapper"><span class="ifp-chart-tooltip__header">${category}</span><span class="ifp-chart-tooltip__value" >${that._number.transform(chartData.y, that.format)}</span></div>`;
        }
      },

      series: []
    },
    columnChart: {
      exporting: {
        enabled: false
      },
      chart: {
        type: 'column',
        backgroundColor: 'transparent',
        width: this.width
      },
      title: {
        text: ''
      },
      xAxis: {
        type: 'category',
        categories: this.category,
        lineWidth: 0,
        startOnTick: false,
        endOnTick: false,
        labels: {
          style: this.axisStyle,
          formatter: function (this: Highcharts.AxisLabelsFormatterContextObject): string {
            if (that.category && typeof this.pos === 'number') {
              return that.category[this.pos] ?? String(this.value);
            }
            return String(this.value);
          }
        },
      },
      yAxis: { visible: false },
      credits: {
        enabled: false
      },
      plotOptions: {
        column: {
          borderRadius: 4,
          color: '#8cbdf7'
        },
        series: {
          animation: this.enableAnimation
        }
      },
      legend: {
        enabled: true,
        rtl: this._translate.currentLang === 'ar',
        itemStyle: {
          fontSize: '1.4rem',
          fontFamily: 'Noto Sans',
          fontWeight: 400,
          color: '#6A7180'
        },
      },
      tooltip: {
        style: {
          zIndex: 9999
        },
        positioner: (boxWidth: number, boxHeight: number, point: { plotX: number, plotY: number }) => {
          return {
            x: point.plotX - (boxWidth / 3),
            y: point.plotY - boxHeight - 10
          };
        },
        animation: false,
        hideDelay: 0,
        backgroundColor: '#182234',
        borderRadius: 10,
        shadow: false,
        distance: 0,
        useHTML: true,
        outside: true,
        formatter: function (this: Highcharts.TooltipFormatterContextObject): string {
          const point = this.point as Highcharts.Point & { index?: number, count?: number };
          let categoryLabel: string = String(point.category);
          if (that.category && typeof point.index === 'number') {
            categoryLabel = that.category[point.index] ?? String(point.category);
          }
          if (that.isCircular()) {
            let html: string = `<div class="ifp-chart-tooltip__usage ifp-chart-tooltip__usage--circular"> <p class="ifp-chart-tooltip__usage-title">${categoryLabel}</p>`;
            if (point.count) {
              html += `<span class="ifp-chart-tooltip__usage-value">${point.count}</span>`;
            }
            html += `<span class="ifp-chart-tooltip__usage-value">${point.y} ${that.tooltipLabel}</span></div>`;
            return html;
          } else {
            return `<div class="ifp-chart-tooltip__usage"> <div>${categoryLabel}</div>  ${point.y}  ${that.tooltipLabel}</div>`;
          }
        }
      },
      series: []
    },
    barChart: {
      exporting: {
        enabled: false
      },
      chart: {
        type: 'bar',
        backgroundColor: 'transparent',
        width: this.width
      },
      title: {
        text: ''
      },
      xAxis: {
        type: 'category',
        categories: this.category,
        lineWidth: 0,
        startOnTick: false,
        endOnTick: false,
        labels: {
          style: this.axisStyle,
          formatter: function (this: Highcharts.AxisLabelsFormatterContextObject): string {
            if (that.category && typeof this.pos === 'number') {
              return that.category[this.pos] ?? String(this.value);
            }
            return String(this.value);
          }
        },
      },
      yAxis: { visible: false },
      credits: {
        enabled: false
      },
      plotOptions: {
        bar: {
          borderRadius: 4,
          color: '#8cbdf7'
        },
        series: {
          animation: this.enableAnimation
        }
      },
      legend: {
        enabled: true,
        rtl: this._translate.currentLang === 'ar',
        itemStyle: {
          fontSize: '1.4rem',
          fontFamily: 'Noto Sans',
          fontWeight: 400,
          color: '#6A7180'
        },
      },
      tooltip: {
        style: {
          zIndex: 9999
        },
        positioner: (boxWidth: number, boxHeight: number, point: { plotX: number, plotY: number }) => {
          return {
            x: point.plotX - (boxWidth / 3),
            y: point.plotY - boxHeight - 10
          };
        },
        animation: false,
        hideDelay: 0,
        backgroundColor: '#182234',
        borderRadius: 10,
        shadow: false,
        distance: 0,
        useHTML: true,
        outside: true,
        formatter: function (this: Highcharts.TooltipFormatterContextObject): string {
          const point = this.point as Highcharts.Point & { index?: number, count?: number };
          let categoryLabel: string = String(point.category);
          if (that.category && typeof point.index === 'number') {
            categoryLabel = that.category[point.index] ?? String(point.category);
          }
          if (that.isCircular()) {
            let html: string = `<div class="ifp-chart-tooltip__usage ifp-chart-tooltip__usage--circular"> <p class="ifp-chart-tooltip__usage-title">${categoryLabel}</p>`;
            if (point.count) {
              html += `<span class="ifp-chart-tooltip__usage-value">${point.count}</span>`;
            }
            html += `<span class="ifp-chart-tooltip__usage-value">${point.y} ${that.tooltipLabel}</span></div>`;
            return html;
          } else {
            return `<div class="ifp-chart-tooltip__usage"> <div>${categoryLabel}</div>  ${point.y}  ${that.tooltipLabel}</div>`;
          }
        }
      },
      series: []
    },
    pieChart: {
      exporting: { enabled: false },
      chart: {
        type: 'pie',
        backgroundColor: 'transparent',
        height: 1000, // Set to 1000
        width: 1000,  // Set to 1000
        spacing: [0, 0, 0, 0],
        margin: [0, 0, 0, 0],
        style: {
          overflow: 'visible'
        }
      },
      colors: ifpDistinctColors, // Use distinct colors for pie chart
      title: { text: '' },
      credits: { enabled: false },
      legend: {
        enabled: true,
        align: 'right',
        verticalAlign: 'middle',
        layout: 'vertical',
        itemStyle: {
          fontSize: '14px',
          fontFamily: 'Noto Sans',
          fontWeight: '400',
          color: '#6A7180'
        }
      },
      tooltip: {
        enabled: true,
        animation: false,
        hideDelay: 0,
        backgroundColor: '#182234',
        borderRadius: 10,
        shadow: true,
        distance: 0,
        style: {
          color: '#FFFFFF'
        },
        useHTML: true,
        outside: true,
        className: 'ifp-chart-tooltip',
        formatter: function () {
          const chartData: any = this;
          return `
            <div class="ifp-chart-tooltip__wrapper">
              <span class="ifp-chart-tooltip__header">${chartData.key}</span>
              <span class="ifp-chart-tooltip__value">${chartData.y}</span>
            </div>
          `;
        }
      },
      plotOptions: {
        pie: {
          allowPointSelect: false,
          cursor: 'default',
          innerSize: '0%',
          borderWidth: 3,
          borderColor: '#ffffff',
          shadow: true,
          shadowOffsetX: 2,
          shadowOffsetY: 2,
          shadowColor: 'rgba(0,0,0,0.1)',
          startAngle: 0,
          endAngle: 360,
          center: ['50%', '50%'],
          size: '85%',
          dataLabels: {
            enabled: true,
            distance: 40,
            allowOverlap: true,
            crop: false,
            style: {
              fontSize: '14px',
              fontFamily: 'Noto Sans',
              fontWeight: '500',
              color: '#182234',
              textOutline: '2px contrast'
            },
            formatter: function() {
              const point: any = this;
              return point.point.name + '<br/><strong>' + point.y.toLocaleString() + '</strong>';
            },
            connectorColor: '#182234',
            connectorWidth: 3,
            softConnector: true,
            padding: 5
          },
          showInLegend: false
        },
        series: {
          animation: this.enableAnimation
        }
      },
      series: [{
        name: 'Data',
        colorByPoint: true,
        data: []
      }]
    },
    donutChart: {
      chart: {
        type: 'pie',
        backgroundColor: 'transparent',
        height: 600, // Increased size
        width: 600,  // Increased size
        events: {
          load: (chart: { target: Chart }) => {
            if (this.donutCenterFunc()) {
              this.donutCenterFunc()?.(chart.target);
            } else {
              this.setCenterValueDonut(chart.target);
            }
          },
          redraw: (chart: { target: Chart }) => {
            if (this.donutCenterFunc()) {
              this.donutCenterFunc()?.(chart.target);
            } else {
              this.setCenterValueDonut(chart.target);
            }
          }
        }
      },
      colors: ifpDistinctColors, // Use distinct colors for donut chart
      title: { text: '' },
      legend: {
        align: 'center',
        verticalAlign: 'bottom',
        layout: 'horizontal'
      },
      tooltip: {
        enabled: true,
        animation: false,
        hideDelay: 0,
        backgroundColor: '#182234',
        borderRadius: 10,
        shadow: true,
        distance: 0,
        style: {
          color: '#FFFFFF'
        },
        useHTML: true,
        outside: true,
        className: 'ifp-chart-tooltip',
        formatter: function () {
          const chartData: any = this;
          return `
            <div class="ifp-chart-tooltip__wrapper">
              <span class="ifp-chart-tooltip__header">${chartData.key}</span>
              <span class="ifp-chart-tooltip__value">${chartData.y}</span>
            </div>
          `;
        }
      },
      plotOptions: {
        pie: {
          innerSize: '85%',
          borderWidth: 1,
          borderRadius: 0,
          dataLabels: {
            enabled: true,
            distance: 25,
            style: {
              fontSize: '14px',
              fontFamily: 'Noto Sans',
              fontWeight: '500',
              color: '#182234',
              textOutline: 'none'
            },
            formatter: function() {
              const point: any = this;
              return point.point.name + '<br/>' + point.y;
            },
            connectorColor: '#6A7180',
            connectorWidth: 2,
            softConnector: true
          },
          series: { animation: this.enableAnimation }
        }
      },
      credits: { enabled: false },
      exporting: { enabled: false },
      series: []
    }
  };

  public chartData: Highcharts.Options | any = this.charts[this.chartName];

  constructor(public _highChart: HighchartService, private readonly _el: ElementRef, private readonly _cdr: ChangeDetectorRef, public _number: DecimalPipe, public _render: Renderer2,
    private readonly _shortNumber: numberChartDatePipe, private readonly _translate: TranslateService, private readonly themeService: ThemeService) {
    that = this;
  }

  ngOnInit() {
    console.log('dxp-charts component initialized');
    console.log('[dxp-charts] ngOnInit inputs:', {
      data: this.data,
      category: this.category,
      chartName: this.chartName,
      yaxisLabel: this.yaxisLabel,
      xAxisLabel: this.xAxisLabel,
      format: this.format,
      marker: this.marker,
      enableAnimation: this.enableAnimation,
      borderRadius: this.borderRadius,
      tooltipLabel: this.tooltipLabel,
      comparisonEnable: this.comparisonEnable,
      markerColor: this.markerColor
    });
    if (this.themeService.defaultTheme === 'dark') {
      this.axisTitleColor = ifpColors.white;
    } else {
      this.axisTitleColor = ifpColors.primaryGrey;
    }

    this.initializeChart();
    this.setupSeriesDropdown();
  }

  ngOnChanges(changes: SimpleChanges): void {

    if (changes['chartName']) {
      this.buildChartOptions();
    }
    if (changes['data']) {
      this.setupSeriesDropdown();
    }
    if (changes['xAxisLabel'] || changes['yaxisLabel']) {
      this.applyInputOverrides();
    }

    if (this.chart) {
      this.updateChartConfiguration();
    }
  }

  private initializeChart() {
    // Only set yAxis for charts that have it (not pieChart/donutChart)
    if (this.charts[this.chartName].yAxis) {
      this.charts[this.chartName].yAxis.title = {
        text: this.yaxisLabel,
        style: {
          fontSize: '15px',
          fontWeight: '500',
          color: this.axisTitleColor
        }
      };
    }

    this.subscription = this.themeService.defaultTheme$.subscribe(() => {
      if (this.yaxisLabel && this.charts[this.chartName].yAxis) {
        this.charts[this.chartName].yAxis.title = {
          text: this.yaxisLabel,
          style: {
            fontSize: '15px',
            fontWeight: '500',
            color: this.axisTitleColor
          }
        };
        this.updateFlag = false;
        this._cdr.detectChanges();
        this.updateFlag = true;
        this._cdr.detectChanges();
      }
    });

    this.applyInputOverrides();
    this.charts[this.chartName].chart.height = this.height;
    this.chartData = this.charts[this.chartName];
    this.updateSeriesData();
    this.updateFlag = true;
  }

  private setupSeriesDropdown() {
    if (this.isPieOrDonutChart()) {
      // For pie/donut charts, show categories as dropdown options
      this.categoryOptions = this.category.map((cat, index) => ({
        name: cat,
        value: index
      }));
      this.showCategoryDropdown = this.category.length > 1;
      this.showSeriesDropdown = false; // Hide series dropdown for pie/donut
    } else {
      this.showSeriesDropdown = false;
      this.showCategoryDropdown = false;
    }
  }

  private isPieOrDonutChart(): boolean {
    return this.chartName === 'pieChart' || this.chartName === 'donutChart';
  }

  private updateChartConfiguration() {
    this.charts[this.chartName].chart.height = this.height;
    if (this.width) {
      this.charts[this.chartName].chart.width = this.width;
    }
    if (this.charts[this.chartName]?.plotOptions?.series?.animation) {
      this.charts[this.chartName].plotOptions.series.animation = this.enableAnimation;
    }
    if (this.yAxis() && this.charts[this.chartName].yAxis) {
      this.charts[this.chartName].yAxis = this.yAxis();
    }
    if (this.xAxis() && this.charts[this.chartName].xAxis) {
      this.charts[this.chartName].xAxis = this.xAxis();
    }
    // Always set xAxis.type and categories after possible xAxis override
    if (this.charts[this.chartName].xAxis) {
      this.charts[this.chartName].xAxis.type = 'category';
      this.charts[this.chartName].xAxis.categories = this.category;
    }
    if (this.plotOptions()) {
      this.charts[this.chartName].plotOptions = this.plotOptions();
    }
    if (this.legends()) {
      this.charts[this.chartName].legend = this.legends();
    }
    if (this.tooltip()) {
      this.charts[this.chartName].tooltip = this.tooltip();
    }
    if (this.isCircular() && this.chartName === 'barChart') {
      this.charts[this.chartName].xAxis.labels['enabled'] = this.showXAxisLabel();
      this.charts[this.chartName].chart.polar = true;
      this.charts[this.chartName].chart.inverted = true;
    }
    if (this.custom()) {
      this.charts[this.chartName] = this.custom();
    }
    if (this.borderRadius && (this.chartName === 'columnChart' || this.chartName === 'barChart')) {
      const plotType = this.chartName === 'columnChart' ? 'column' : 'bar';
      this.charts[this.chartName].plotOptions[plotType].borderRadius = this.borderRadius;
    }
    if (this.yaxisLabel && this.charts[this.chartName].yAxis) {
      this.charts[this.chartName].yAxis.title.text = this.yaxisLabel;
    }
    if (this.xAxisLabel && this.charts[this.chartName].xAxis) {
      this.charts[this.chartName].xAxis.title.text = this.xAxisLabel;
    }

    this.applyInputOverrides();

    if (this.tickIntervell && this.charts[this.chartName].yAxis) {
      this.charts[this.chartName].yAxis.tickInterval = this.tickIntervell;
    }

    this.chartData = this.charts[this.chartName];
    this.updateSeriesData();
    this.updateFlag = true;
    this._cdr.detectChanges();

    if (this.comparisonEnable && !this.isPieOrDonutChart()) {
      if (this.chartData?.series?.[0]?.data?.length > 1) {
        if (this.marker) {
          this.changesComparison();
        }
      }
    }
  }

  private applyInputOverrides() {
    if (this.yAxis() && this.charts[this.chartName].yAxis) {
      this.charts[this.chartName].yAxis = this.yAxis();
    }
    if (this.xAxis() && this.charts[this.chartName].xAxis) {
      this.charts[this.chartName].xAxis = this.xAxis();
    }
    
    // Set axis labels with priority to user input
    if (this.charts[this.chartName].xAxis) {
      if (this.xAxisLabel && this.xAxisLabel.trim() !== '') {
        this.charts[this.chartName].xAxis.title = {
          text: this.xAxisLabel,
          style: {
            fontSize: '14px',
            fontWeight: '600',
            color: this.axisTitleColor
          }
        };
      }
    }
    
    if (this.charts[this.chartName].yAxis) {
      if (this.yaxisLabel && this.yaxisLabel.trim() !== '') {
        this.charts[this.chartName].yAxis.title = {
          text: this.yaxisLabel,
          style: {
            fontSize: '14px',
            fontWeight: '600',
            color: this.axisTitleColor
          }
        };
      }
    }
    
    if (this.plotOptions()) {
      this.charts[this.chartName].plotOptions = this.plotOptions();
    }
    if (this.legends()) {
      this.charts[this.chartName].legend = this.legends();
    }
    if (this.tooltip()) {
      this.charts[this.chartName].tooltip = this.tooltip();
    }
    if (this.isCircular() && this.chartName === 'barChart') {
      this.charts[this.chartName].xAxis.labels['enabled'] = this.showXAxisLabel();
      this.charts[this.chartName].chart.polar = true;
      this.charts[this.chartName].chart.inverted = true;
    }
    if (this.custom()) {
      this.charts[this.chartName] = this.custom();
    }
  }

  private updateSeriesData() {
    if (this.isPieOrDonutChart()) {
      // For pie/donut charts, show data for the selected category across all series
      const selectedCategoryIndex = this.selectedCategoryIndex;
      const selectedCategory = this.category[selectedCategoryIndex];

      if (selectedCategory) {
        let pieData: any[] = [];

        if (this.data.length === 1) {
          // Single series: show the single value for selected category
          const series = this.data[0];
          const value = series.data[selectedCategoryIndex] || 0;
          // Create multiple segments for better visualization
          if (value > 0) {
            pieData = [{
              name: series.name || 'Series 1',
              y: value,
              sliced: false,
              selected: false
            }];
          } else {
            // If value is 0, show a placeholder segment
            pieData = [{
              name: 'No Data Available',
              y: 1,
              sliced: false,
              selected: false
            }];
          }
        } else {
          // Multi-series: show values from all series for the selected category
          pieData = this.data.map((series, index) => ({
            name: series.name || `Series ${index + 1}`,
            y: series.data[selectedCategoryIndex] || 0,
            sliced: false,
            selected: false
          }));
          // Filter out zero values for better visualization
          pieData = pieData.filter(item => item.y > 0);
        }

        // Ensure we have at least one data point with a value
        if (pieData.length === 0 || pieData.every(item => item.y === 0)) {
          pieData = [{
            name: 'No Data',
            y: 1,
            sliced: false,
            selected: false
          }];
        }

        this.chartData.series = [{
          name: selectedCategory,
          colorByPoint: true,
          data: pieData
        }];
      }
    } else {
      // For line, column, bar charts, use all series
      this.chartData.series = this.data.map((series, index) => ({
        name: series.name || `Series ${index + 1}`,
        data: series.data,
        color: series.color || this.getDefaultColor(index)
      }));
    }
  }

  private getDefaultColor(index: number): string {
    const colors = ['#2687FD', '#3bd6ad', '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD'];
    return colors[index % colors.length];
  }

  onSeriesSelectionChange(selectedItem: any) {
    this.selectedSeriesIndex = selectedItem.value;
    this.updateSeriesData();
    this.updateFlag = true;
    this._cdr.detectChanges();
  }

  onCategorySelectionChange(selectedItem: any) {
    this.selectedCategoryIndex = selectedItem.value;
    this.updateSeriesData();
    this.updateFlag = true;
    this._cdr.detectChanges();
  }

  setCenterValueDonut(chart: Chart) {
    // Remove any existing center label
    if ((chart as any).centerLabel) {
      (chart as any).centerLabel.destroy();
    }
  }

  update(data: any[]) {
    this.data = data;
    this.setupSeriesDropdown();
    this.updateSeriesData();
    this.updateFlag = true;
  }

  charData(event: Highcharts.Chart) {
    this.chartInstance = event;
  }

  removeTooltip() {
    if (this.chart.chart.tooltip) {
      this.chart.chart.tooltip.hide();
      this._cdr.detectChanges();
    }
  }

  changesComparison() {
    const chart = this.chart.chart;
    const series = chart.series[0];
    const points = series.points;
    for (let i = 0; i < points.length; i++) {
      const point = points[i];
      point?.update({
        marker: {
          fillColor: this.markerColor,
          lineColor: this.markerColor,
          radius: 4,
          symbol: 'circle'
        }
      }, false);
    }
    const pointLastIndex = points[points.length - 1];
    pointLastIndex?.update({
      marker: {
        fillColor: '#3366fb',
        lineColor: '#3366fb',
        radius: 6,
        symbol: 'url(../../../../../assets/images/trg.png)'
      }
    }, false);

    const pointMidIndex = points[this.comparison == 'Y/Y' ? 0 : (this.comparison == 'Q/Q' ? 8 : 10)];
    pointMidIndex?.update({
      marker: {
        fillColor: '#3366fb',
        lineColor: '#3366fb',
        radius: 6,
        symbol: 'url(../../../../../assets/images/trg.png)'
      }
    }, false);
    chart.redraw();
  }

  buildChartOptions() {
    if (this.chartName === 'pie') {
      this.chartName = 'pieChart';
    } else if (this.chartName === 'donut') {
      this.chartName = 'donutChart';
    } else if (this.chartName === 'column') {
      this.chartName = 'columnChart';
    } else if (this.chartName === 'bar') {
      this.chartName = 'barChart';
    } else if (this.chartName === 'line') {
      this.chartName = 'lineChart';
    }

    this.chartData = this.charts[this.chartName];
    this.updateSeriesData();
    this.updateFlag = true;
  }

  ngOnDestroy(): void {
    this.subscription?.unsubscribe();
  }
}
