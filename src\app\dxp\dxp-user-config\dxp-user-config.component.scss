@use "../../../assets/ifp-styles/abstracts/index" as *;
:host{
  display: block;
}
.ifp-dxp-user-config {
  background-color: $ifp-color-grey-18;
  display: flex;
  &__sec-1 {
    background-color: $ifp-color-white;
    border-radius: 10px 0  0 10px     ;
    padding: $spacer-4;
    border-right:1px solid $ifp-color-grey-7;
    width: 50%;
    text-align: center;

    display: flex;
    align-items: center;
  }
  &__sec{
    width: 100%;
    margin:$spacer-5 $spacer-0;
  }
  &__sec-2{
    width: 50%;
  }
  &__head {
    margin-top: $spacer-3;
    font-size: $ifp-fs-6;
    font-weight: $fw-bold;
  }
  &__title {
    font-size: $ifp-fs-5;
    font-weight: $fw-semi-bold;
  }
  &__title-wrap {
    padding:$spacer-4 $spacer-5 ;
    background-color: $ifp-color-white;
  }
  &__user-text {
    margin: $spacer-3 auto;
    color: $ifp-color-grey-14;
    max-width: 444px;
    font-size: $ifp-fs-3;
  }
  &__user-wrap {

    width: 77px;
    height: 77px;
    border-radius: 50%;
    background-color: $ifp-color-blue-menu;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
    color: $ifp-color-white;

  }
  &__sub-sec {
    text-align: left;
    margin-top: $spacer-5;
  }
  &__chart {
    margin: $spacer-6;
    box-shadow: 0px 34px 85px -20px rgba(98, 110, 106, 0.11);
  }
  &__user-list {
    max-width: 266px;
    display: block;
  }
  &__user-group {
    display: block;
    margin-top: $spacer-3;
  }
  &__user-icon {
    font-size: $ifp-fs-8;
  }
}
