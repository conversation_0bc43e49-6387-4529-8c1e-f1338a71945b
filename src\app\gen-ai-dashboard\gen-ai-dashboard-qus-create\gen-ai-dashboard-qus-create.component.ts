import { IfpTooltipDirective } from 'src/app/scad-insights/core/directives/ifp-tooltip.directive';
import { Component, computed, inject, input, OnChang<PERSON>, OnD<PERSON>roy, OnInit, output } from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';
import { IfpDropdownComponent } from '../../scad-insights/ifp-widgets/ifp-atoms/ifp-dropdown/ifp-dropdown.component';
import { IfpInputAutoResizeDirective } from 'src/app/scad-insights/core/directives/ifp-input-auto-height.directive';
import {  FormControl, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { Aianswer, DropdownOptionGenAi, GenAIQuestion } from '../gen-ai-dashboard-preview/gen-ai-dashboard-preview.component';
import { questionDropDown } from '../constents/gen-ai-testing.constant';
import { ToasterService } from 'src/app/scad-insights/core/services/tooster/ToastrService.service';

@Component({
  selector: 'ifp-gen-ai-dashboard-qus-create',
  imports: [TranslateModule, IfpDropdownComponent,
    IfpInputAutoResizeDirective,
    TranslateModule,IfpTooltipDirective,
    ReactiveFormsModule
  ],
  templateUrl: './gen-ai-dashboard-qus-create.component.html',
  styleUrl: './gen-ai-dashboard-qus-create.component.scss'
})
export class GenAiDashboardQusCreateComponent implements OnChanges {
  public addQuestionEnable =  input(true);
  public question =  input<GenAIQuestion>({
      object_id: '',
      question: '',
      type: '',
      complexity: '',
      source: '',
      data_classification: '',
      expected_answer: ''
    });
  public form = new FormGroup({
    object_id: new FormControl<string | null>(null, Validators.required,),
    question: new FormControl<string | null>(null, [Validators.required, this.noSpacesValidator]),
    type: new FormControl<DropdownOptionGenAi | null>(null, Validators.required),
    expected_answer: new FormControl(''),
    complexity: new FormControl<DropdownOptionGenAi | null>(null, Validators.required),
    source: new FormControl<DropdownOptionGenAi | null>(null, Validators.required),
    data_classification: new FormControl<DropdownOptionGenAi | null>(null, Validators.required),
    save: new FormControl<boolean | null>(null),
  });
  public save = output<GenAiFormQuestion>();
  public add = output<{ ans: GenAiFormQuestion}>();
  public changeDetect = output<GenAiFormQuestion>();
  public dropdown = input<Record<string,DropdownOptionGenAi[]>>({});
  public dropdownObject = input<Record<string,Record<string,DropdownOptionGenAi>>>({});
  public validation = input();
  public enableDelete =  input();
  public deleteEventEmit = output<string>();
  public dropdownConstant= questionDropDown;
  public getDropDownBadgeDataClassification = computed(() => {
      if (typeof this.question().data_classification != "string") {
        return this.dropdownObject()['data_classification'][ this.question().data_classification as number]
      }
      return  undefined ;
  });
  public getDropDownBadgeDataType = computed(() => {
    if (typeof this.question().type != "string") {
      return this.dropdownObject()['reference_q_type'][ this.question().type as number]
    }
    return  undefined ;

  });
  public getDropDownBadgeDataComplexity = computed(() => {
    if (typeof this.question().complexity != "string") {
      return this.dropdownObject()['reference_q_complexity'][ this.question().complexity as number]
    }
    return  undefined ;

  });
  public getDropDownBadgeDataSource = computed(() => {
    if (typeof this.question().complexity != "string") {
      return this.dropdownObject()['source_data'][ this.question().source as number]
    }
    return  undefined ;

  });
  public submit = false;

  public _toaster = inject(ToasterService);


  ngOnChanges(): void {

  }

  deleteEvent() {
      this.deleteEventEmit.emit(this.question().object_id);
  }

  ngOnInit(): void {
       this.submit = false;
    this.form =   new FormGroup({
      object_id: new FormControl(this.question().object_id, Validators.required),
      question: new FormControl(this.question().question,  [Validators.required, this.noSpacesValidator]),
      expected_answer: new FormControl(this.question().expected_answer ??''),
      type: new FormControl(this.getDropDownBadgeDataType()  ?? this.dropdown()[questionDropDown.referenceQType][0], Validators.required),
      complexity: new FormControl(this.getDropDownBadgeDataComplexity() ??this.dropdown()[questionDropDown.referenceQComplexity][0], Validators.required),
      source: new FormControl(this.getDropDownBadgeDataSource() ?? this.dropdown()[questionDropDown.sourceData][0], Validators.required),
      data_classification: new FormControl(this.getDropDownBadgeDataClassification() ?? this.dropdown()[questionDropDown.dataClassification][0], Validators.required),
      save: new FormControl(this.question().save ?? false),
    });
    this.form.valueChanges.subscribe(() => {
      this.changeDetect.emit(this.getCurrentValueNotSaved);
    });
  }

  noSpacesValidator(control: any) {
    if (control.value && control.value.trim().length === 0) {
      return { noSpaces: true };
    }
    return null;
  }


  saveEvent() {
    this.submit = true;
    if(this.form.invalid) {
      this._toaster.error('Mandatory fields missing');
      return;
    }

    this.save.emit(this.getCurrentValue);
  }

  get  getCurrentValue() {
   return {
      object_id: this.form.value.object_id ?? '',
      question:  this.form.value.question ?? '',
      type:  this.form.value.type?.id ?? '',
      complexity: this.form.value.complexity?.id ?? '',
      source:  this.form.value.source?.id ?? '',
      data_classification:  this.form.value.data_classification?.id ?? '',
      expected_answer:  this.form.value.expected_answer ?? '',
      save: true
   }
  }

    get  getCurrentValueNotSaved() {
   return {
      object_id: this.form.value.object_id ?? '',
      question:  this.form.value.question ?? '',
      type:  this.form.value.type?.id ?? '',
      complexity: this.form.value.complexity?.id ?? '',
      source:  this.form.value.source?.id ?? '',
      data_classification:  this.form.value.data_classification?.id ?? '',
      expected_answer:  this.form.value.expected_answer ?? '',
      save: false
   }
  }

  addQuestion() {
    this.submit = true;
    if(this.form.invalid) {
      this._toaster.error('Mandatory fields missing');
      return;
    }
    this.add.emit({ans: this.getCurrentValue});
  }

}



export interface GenAiFormQuestion {
  object_id: string;
  question: string;
  type: string | number;
  complexity: string | number;
  source: string | number;
  data_classification: string | number;
  save?: boolean ;
  expected_answer: string ;
  ai_answer?: Aianswer;
  rating?:Rating
  status?: string;
  id?:number;
}

interface Rating {
  is_satisfied: boolean;
  rating: number;
  additional_feedback: string;
  accuracy: string;
  releavance: string;
}
