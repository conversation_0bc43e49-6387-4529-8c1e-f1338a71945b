
<ifp-button [buttonClass]="buttonClass.secondary+' '+buttonClass.large " [iconClass]="'ifp-icon-summary'" (ifpClick)="openModal()" [label]="'summary'" ></ifp-button>
<app-ifp-modal #modalUpdate [isClose]="false" [modalClass]="'ifp-modal__import-indicators'">
  <div class="ifp-data-preview" >
    <div class="ifp-data-preview__close-wrap">
      <em class="ifp-icon ifp-icon-cross ifp-data-preview__close-btn" (click)="removeModal()"></em>
    </div>

      <div class="ifp-data-preview__table">


      <div class="ifp-data-preview__toolbar">
        <h3 class="ifp-data-preview__title">{{'Preview' | translate}}</h3>
        <div class="ifp-data-preview__tools">

          <ifp-panel-dropdown class="ifp-data-preview__dropdown" [options]="columListDropdown" (multiSelected)="multiSelected($event)"
            [key]="'value'"></ifp-panel-dropdown>
            @if(metaData) {
          <em class="ifp-icon ifp-icon-col-view ifp-data-preview__tool"
            [ngClass]="{'ifp-data-preview__tool--active': tableView === 'normal'}" (click)="setTableView('normal')"
          [zIndex]="1210"   [appIfpTooltip]="'View Data' | translate"></em>

              <em class="ifp-icon ifp-icon-detail-view-fill ifp-data-preview__tool"
              [ngClass]="{'ifp-data-preview__tool--active': tableView === 'detail'}" (click)="setTableView('detail')"
              [zIndex]="1210"  [appIfpTooltip]="'Column Details' | translate"></em>
            }
            @if ( _exploratory.previewTableData.data.length !=0 ) {
          <em   [zIndex]="1210" [appIfpTooltip]="'Download Preview' | translate" class="ifp-icon ifp-icon-download-line ifp-data-preview__tool" (click)="downloadFileClick()"></em>
            }

        </div>
      </div>
      <div class="ifp-data-preview__inner">
        @if (tableView === 'normal') {
        @if (_exploratory.previewTableData.data.length !=0 ) {
          <ifp-data-table [translation]="false"  [rowClickEnable]="tableView !== 'normal'" [tableHead]="_exploratory.previewTableData.heading" [tableData]="_exploratory.previewTableData.data"
          class="ifp-data-preview__table"></ifp-data-table>
        }@else {
          @if(loader) {
            <div class="ifp-center-loader">
              <app-ifp-spinner></app-ifp-spinner>
            </div>
          } @else {
            <app-ifp-no-data></app-ifp-no-data>
          }
        }
      }
        @else if (tableView === 'detail') {
          <ifp-data-summary [summeryPopup]="true" [enableProcessBtn]="false" [totalColumn]="totalColumn" [totalRow]="totalRow" [buttonName]="'Proceed to Advanced Analytics'" [border]="true" [backButton]="false"
          [settings]="summeryTableSettings" [data]="tableDataDetail" [loaderPage]="true" [disableProcessButton]="false"
         ></ifp-data-summary>
        }

      </div>
      @if( tableView === 'normal') {
        <div class="ifp-data-preview__pagination-wrapper">
        <app-pagination class="ifp-data-preview__pagination" [offset]="offset" [limit]="limit" [customPagination]="true"
        [size]="size"
        (pageChange)="onPageChange($event)"></app-pagination>

      </div>
      }
    </div>

  </div>

</app-ifp-modal>

