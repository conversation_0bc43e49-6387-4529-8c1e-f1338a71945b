import { Directive, ElementRef, Renderer2, <PERSON><PERSON><PERSON><PERSON>, OnDestroy, Input } from '@angular/core';

@Directive({
  selector: '[appAppendToBody]',
  standalone: true
})
export class AppendToBodyDirective implements OnInit, OnDestroy {

  @Input() top: number | undefined = 0;
  @Input() left: number | undefined = 0;
  @Input() position: 'left' | 'right' = 'left';
  @Input() parentWidth: number | undefined = 0;
  @Input() marginTop: number = 0;
  @Input() isBodyAppend: boolean = true;

  constructor(private el: ElementRef, private renderer: Renderer2) { }

  ngOnInit() {
    if (!this.isBodyAppend) {
      return;
    }
    this.renderer.setStyle(this.el.nativeElement, 'position', 'absolute');
    this.renderer.setStyle(this.el.nativeElement, 'top', `${((this.top ?? 0) + this.marginTop)}px`);
    const leftPosition = this.position == 'left' ? (((this.left ?? 0) - this.el.nativeElement.offsetWidth) + (this.parentWidth ?? 0)) : this.left;
    this.renderer.setStyle(this.el.nativeElement, 'left', `${leftPosition}px`);
    this.renderer.appendChild(document.body, this.el.nativeElement);
  }

  ngOnDestroy() {
    if (!this.isBodyAppend) {
      return;
    }
    if (document.body.contains(this.el.nativeElement)) {
      this.renderer.removeChild(document.body, this.el.nativeElement);
    }
  }
}
