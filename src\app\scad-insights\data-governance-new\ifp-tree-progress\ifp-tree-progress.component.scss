@use "../../../../assets/ifp-styles/abstracts/index" as *;
.ifp-root {
  width: 375px;
  min-width: 375px;
  height: 375px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  position: relative;
  &::after {
    content: "";
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background-image: linear-gradient($ifp-color-pale-grey, $ifp-color-white);
    box-shadow: inset -5px 3px 4px 0 rgba(120, 135, 165, 0.25);
    filter: blur(6px);
    position: absolute;
    top: 0;
    left: 0;
  }
  &__progress-wrapper,
  &__outer {
    z-index: 1;
  }
  &__progress-wrapper {
    width: 85%;
    height: 85%;
    border-radius: 50%;
    overflow: hidden;
    display: flex;
    position: relative;
  }
  &__progress {
    // border-inline-end: 3px solid $ifp-color-pale-grey;
    &:last-child {
      border: none;
    }
  }
  &__outer {
    width: calc(85% - 5px);
    height: calc(85% - 5px);
    border-radius: 50%;
    display: flex;
    flex-direction: column;
    padding: $spacer-4;
    background-image: linear-gradient($ifp-color-pale-grey, $ifp-color-white);
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }
  &__inner {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    text-align: center;
    border-radius: 50%;
    box-shadow: 0 3px 34px 0 rgba(47, 72, 96, 0.12);
    background-image: linear-gradient($ifp-color-grey-18, $ifp-color-white);
    border: 5px solid $ifp-color-white;
    padding: $spacer-4;
  }
  &__title {
    font-size: $ifp-fs-4;
    color: $ifp-color-grey-14;
    font-weight: $fw-medium;
  }
  &__value-wrapper {
    padding: $spacer-3;
  }
  &__value {
    font-size: $ifp-fs-9;
    font-weight: $fw-bold;
    margin-top: $spacer-1;
  }
  &__progress-ring {
    width: 100%;
    // axis compensation
    transform: rotate(180deg);
  }
  &__progress-circle {
    stroke-width: 5px;
    transition: 0.35s stroke-dashoffset;
    transform-origin: 50% 50%;
  }
  &--vertical {
    .ifp-root {
      &__progress-wrapper {
        flex-direction: column;
      }
      &__progress {
        border-inline-end: 0;
        &:last-child {
          border: none;
        }
      }
      &__inner {
        align-items: center;
        justify-content: flex-end;
        text-align: start;
      }
      &__value-wrapper {
        width: 50%;
      }
    }
  }
}
