<div class="ifp-user-tag-group" [ngClass]="{'ifp-user-tag-group--edit': isEdit()}">
  @for (tag of tagList() | slice:0:limit(); track tag; let index = $index) {
    <ifp-user-tag class="ifp-user-tag-group__item" [name]="tag.name" [email]="tag.email ?? ''" [isEditMode]="isEdit()" (remove)="remove(tag,index)"></ifp-user-tag>
  }
  @if (tagList().length > limit()) {
    <div class="ifp-user-tag-group__count" (click)="limit.set(tagList().length)">+{{tagList().length - limit()}}</div>
    @if (moreText() !== '') {
      <span class="ifp-user-tag-group__more-text">{{moreText() | translate}}</span>
    }
  }
</div>
