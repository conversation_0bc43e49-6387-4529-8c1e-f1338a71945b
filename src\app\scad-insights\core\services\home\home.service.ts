import { Injectable } from '@angular/core';
import { HttpService } from '../http/http.service';
import { homeApi } from '../../apiConstants/home-apis.constants';
import {  tap } from 'rxjs';
import { Store } from '@ngrx/store';
import { getStatisticsInsights } from 'src/app/scad-insights/store/statistics-insights/statistics-insights-list.action';

@Injectable({
  providedIn: 'root'
})
export class homeService {



  constructor(private _http: HttpService, private store:Store) {

  }

  getWhatsNew() {
    return this._http.get(homeApi.whatsNew).pipe(tap( (data) => {

      data?.subsections?.forEach((element: any) => {
        const ids: any[] = [];
        element?.indicatorList?.forEach((node: {indicatorId : number}) => {
          ids.push(node.indicatorId);
        });
        this.store.dispatch(getStatisticsInsights({id: ids, name: element.key}));
      });
    }));
  }

  getAnalyticsApp() {
    return this._http.get(homeApi.analyticApps);
  }

}
