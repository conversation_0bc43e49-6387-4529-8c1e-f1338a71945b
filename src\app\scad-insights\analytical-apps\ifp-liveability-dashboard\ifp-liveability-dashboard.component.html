<div class="ifp-container"><app-ifp-breadcrumbs [pageData]="pageData"></app-ifp-breadcrumbs></div>
<div class="ifp-lvda" *ngIf="cardData?.length > 0">
  <div class="ifp-container">
    <h2 class="ifp-node__title">{{liveabilityResponse?.title}}</h2>
    <div class="ifp-lvda__description">
      <app-ifp-description class="ifp-node__subtitle"
        [description]="liveabilityResponse?.subTitle"></app-ifp-description>
    </div>

    <app-ifp-tab [inverse]="false" [classification]="false" [tabData]="liveabilityOptions"
      (selectedTabEvent)="tabClick($event)" [tooltipDisabled]="true" [selectedTab]="selectedTab" [hideCount]="true"
      [showIcon]="true"></app-ifp-tab>


    <div class="ifp-lvda__dropdown-filters">

      @if (ratingPoints) {
      <div class="ifp-lvda__rating-outer">
        <div class="ifp-lvda__rating">
          @for (rating of ratingPoints; track rating._id) {
          <div class="ifp-lvda__rating-wrapper">
            <span class="ifp-lvda__rating-point" [ngStyle]="{'background-color': rating.color}"></span>
            <p class="ifp-lvda__rating-value">{{rating?.value}}</p>
          </div>
          }
        </div>
      </div>
      }
      <div class="ifp-lvda__drop-set">
        <!-- <app-ifp-dropdown [singleDefaultSelect]="true" [isMulti]="false" [searchEnable]="true"
          class="ifp-lvda__dropdown" [dropDownItems]="chartFormat" [title]="'Select Indicator'" [placeHolder]="''"
          [showTitle]="true" (dropDownItemClicked)="tabClick($event)"></app-ifp-dropdown> -->

        <!-- <app-ifp-dropdown [singleDefaultSelect]="true" [isMulti]="false" [searchEnable]="true"
          class="ifp-lvda__dropdown" [dropDownItems]="weightProfile" [title]="'Select Weight Profile'"
          [placeHolder]="''" [showTitle]="true" (dropDownItemClicked)="tabClick($event)"></app-ifp-dropdown> -->
      </div>
    </div>

    <div class="ifp-lvda__horizontal-tabs"
      [ngClass]="{'ifp-lvda__horizontal-tabs--full': horizontalTabList?.length <= 0}">
      @if (horizontalTabList?.length > 0) {
      <ifp-horizontal-tab [(selected)]="productSelectIndex" [disableTooltip]="true" [list]="horizontalTabList"
        class="ifp-lvda__htab" (selectionClick)="selectTab($event)" [showCount]="true"></ifp-horizontal-tab>
      }

      <div class="ifp-lvda__cnt-wrap">
        <div class="ifp-lvda__map">
          <app-ifp-dashboard-map [keyValues]="liveabilityResponse?.dashboardMapData?.data"
            [title]="liveabilityResponse?.dashboardMapData?.title"
            [subTitle]="liveabilityResponse?.dashboardMapData?.subTitle"
            [isDataLabel]="isDataLabel"></app-ifp-dashboard-map>
        </div>
        @if (cardData?.length > 0){
        <div class="ifp-lvda__dashboard-card-wrap">
          @for ( card of cardData; let i=$index; track i) {
          <div class="ifp-lvda__dashboard-card" [ngClass]="{'ifp-lvda__dashboard-card-exp': !card.isCollapsedView}">
            <app-ifp-dashboard-card [cardData]="card" (isExpand)="checkExpand($event,card)" [domain]="domain"
              [updatedData]="updatedData"></app-ifp-dashboard-card>
          </div>
          }
          @if (compositionData) {
          <div class="ifp-lvda__composition-card">
            <h3 class="ifp-lvda__composition-head">{{'Composition' | translate}}</h3>
            @if (compositionData.description) {
            <p class="ifp-lvda__composition__desc" [innerHTML]="compositionData.description"></p>
            }
            @if (compositionData?.data.length) {
            <div class="ifp-lvda__composition-wrapper">
              @for (item of compositionData.data; let i= $index; track i) {
              <app-ifp-range-chart [category]="[item.activePercent != 0 ? item.category: item.category +' (*)']"
                [chartValues]="[item.value]" class="ifp-lvda__composition-item" [height]="50"></app-ifp-range-chart>
              }
            </div>
            }
            <div class="ifp-lvda__composition-bottom">
              @if (getNoteKey()) {
              <p class="ifp-lvda__composition-source">{{'COMPOSITION_NOTE' | translate}}</p>
              }

              @if (getWeightedKey()) {
              <p class="ifp-lvda__composition-note">{{'COMPOSITION_NOTE_TWO' | translate}}
                {{liveabilityOptions[selectedTab].category}} {{'COMPOSITION_NOTE_THREE' | translate}}</p>
              }
            </div>
          </div>
          }
        </div>
        }
      </div>
    </div>
  </div>


`
</div>

@if (isLoading;) {
<div class="ifp-lvda__flex ifp-container">
  <app-ifp-card-loader class="ifp-loader" [type]="'chart'"></app-ifp-card-loader>
</div>
}


<div class="ifp-container ifp-lvda__no-data">
  <app-ifp-no-data *ngIf="cardData?.length <= 0 && !isLoading"></app-ifp-no-data>
</div>
