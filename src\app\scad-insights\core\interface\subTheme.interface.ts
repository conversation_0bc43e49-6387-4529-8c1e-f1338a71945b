import { DomainPopularDatum } from './domain.interface';

export interface SubThemeDetails {
  subtheme: Subdomain;
  data: SubThemeDatum[];
  popularData: DomainPopularDatum[];
  nodeData: any;
}

export interface SubThemePopularDatum {
  name: string;
  nodes: Node[];
}

export interface SubThemeDatum {
  name: string;
  light_icon: string;
  dark_icon: string;
  count: number;
  nodes: Node[];
  showTree: boolean;
  nodesData: any;
  key: string;
}


export interface SubThemeDetailsProduct {
  name: string;
  nodes: Node[];
}

interface Node {
  id: string;
  content_type: string;
  category: (Subdomain | string);
  name: string;
  products: SubThemeDetailsProduct[];
  title: string;
  subtitle: string;
  domains?: string[];
  domain?: string;
  theme?: any;
  subtheme?: any;
  product?: any;
  classification: string;
  content_classification: string;
}



interface Subdomain {
  id: string;
  name: string;
  dark_icon: string;
  light_icon: string;
}
