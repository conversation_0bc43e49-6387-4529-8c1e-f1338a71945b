@use "../../../../../assets/ifp-styles/abstracts/index" as *;
.card-footer {
  background-color: $ifp-color-grey-4;
  padding: 10px 0;
  color: $ifp-color-black;
  width: 100%;
  border-bottom-left-radius: 10px;
  border-bottom-right-radius: 10px;
  border: 1px solid $ifp-color-grey-7;
  margin-top: -1px;
  .ifp-icon {
    font-size: $ifp-fs-8;
    color: $ifp-color-black;
    transition: 0.3s;
    cursor: pointer;
    &:hover,
    &.active {
      color: $ifp-color-secondary-blue;
    }
  }
}
// .fs-5 {
//   font-size: $ifp-fs-7 !important;
// }

.top-my-app {
  color: $ifp-color-black;
  position: relative;
  top: 3px;
  ::ng-deep .ifp-btn__icon {
    font-size: $ifp-fs-8 !important;
  }
}

ul > li > a {
  text-decoration: underline;
  color: $ifp-color-secondary-blue;
}

.ifp-card-footer {
  &__toolbar {
    background-color: $ifp-color-grey-4;
    padding: $spacer-2;
    margin-top: $spacer-2;
    border-radius: 10px;
    border: 1px solid $ifp-color-grey-7;
    max-height: 200px;
    @include ifp-scroll-y(transparent, $ifp-color-grey-1, 4px, 8px);
    &--comment {
      min-height: 200px;
    }
  }
  &__toolbar-title {
    font-size: $ifp-fs-4;
    font-weight: $fw-bold;
  }
}

:host::ng-deep {
  .ifp-card-footer {
    &__btn {
      display: inline-block;
      .ifp-icon{
        font-size: $ifp-fs-8;
      }
    }
    &__toolbar--comment {
      .ifp-insight__wrapper {
        max-height: none;
      }
    }
  }
}

.download_disable {
  color: $ifp-color-grey-7;
  pointer-events: none;
}

@include mobile-tablet {
  .card-footer {
    margin-top: $spacer-0;
    border-radius: 0;
  }
}

.summary-heading {
  font-size: 1.6rem;
  font-width: 500;
  margin-bottom: 10px;
}
.summary-card {
  background: $ifp-color-white;
  color: $ifp-color-black;
  border-radius: 10px;
}
.summary-card img {
  height: 24px;
}
.summary-card .summary-total {
  font-size: 1.8rem;font-weight: bold;
}
.summary-card .summary-text {
  margin-top: 13px;font-size: 1.5rem;font-weight: bold;
}
