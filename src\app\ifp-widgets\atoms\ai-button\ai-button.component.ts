import { Component, input, output } from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';
import { IfpSpinnerRoundComponent } from '../../../scad-insights/ifp-widgets/ifp-molecules/ifp-spinner-round/ifp-spinner-round.component';
import { NgClass } from '@angular/common';

@Component({
  selector: 'ifp-ai-button',
  imports: [TranslateModule, IfpSpinnerRoundComponent, NgClass],
  templateUrl: './ai-button.component.html',
  styleUrl: './ai-button.component.scss',
})
export class AiButtonComponent {
  public iconClass = input<string>();
  public label = input<string>('');
  public theme = input<string>('');
  public loader = input(false);
  public disabled = input(false);
  public disableTranslate = input(false);
  public ifpClick = output();

  ifpClickEvent() {
    this.ifpClick.emit();
  }
}
