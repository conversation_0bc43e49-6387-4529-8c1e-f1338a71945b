import { NgModule, inject } from '@angular/core';
import { ActivatedRouteSnapshot, RouterModule, RouterStateSnapshot, Routes } from '@angular/router';
import { AuthGuardService } from '../scad-insights/core/guard/auth.guard';
import { IfpPagesComponent } from '../layout/ifp-pages/ifp-pages.component';
import { slaService } from '../scad-insights/core/services/sla/sla.service';
import { permission30 } from '../scad-insights/core/services/sla/permission.contant';

export const ifpStoreroutes: Routes = [{
  path: '', component: IfpPagesComponent,
  resolve: [() => inject(slaService).checkPermission(permission30['dashBoardBuilder'])],
  children: [
    {
      path: 'store/dashboard-builder',
      loadComponent: () => import('src/app/dashboard-builder/pages/ifp-dashboard-builder/ifp-dashboard-builder.component').then((m: typeof import('src/app/dashboard-builder/pages/ifp-dashboard-builder/ifp-dashboard-builder.component')) => m.IfpDashboardBuilderComponent),
      canActivate: [(route: ActivatedRouteSnapshot, state: RouterStateSnapshot) => () => inject(AuthGuardService).canActivate(route, state)],
      resolve: [() => inject(slaService).checkPermission(permission30['dashBoardBuilder'])]
    },
    {
      path: 'store/dashboard-preview',
      loadComponent: () => import('src/app/dashboard-builder/pages/ifp-dashboard-preview/ifp-dashboard-preview.component').then((m: typeof import('src/app/dashboard-builder/pages/ifp-dashboard-preview/ifp-dashboard-preview.component')) => m.IfpDashboardPreviewComponent),
      canActivate: [(route: ActivatedRouteSnapshot, state: RouterStateSnapshot) => () => inject(AuthGuardService).canActivate(route, state)],
      resolve: [() => inject(slaService).checkPermission(permission30['dashBoardBuilder'])]
    },
    {
      path: 'store/dashboard/:id',
      loadComponent: () => import('src/app/dashboard-builder/pages/ifp-dashboard-detail/ifp-dashboard-detail.component').then((m: typeof import('src/app/dashboard-builder/pages/ifp-dashboard-detail/ifp-dashboard-detail.component')) => m.IfpDashboardDetailComponent),
      canActivate: [(route: ActivatedRouteSnapshot, state: RouterStateSnapshot) => () => inject(AuthGuardService).canActivate(route, state)],
      resolve: [() => inject(slaService).checkPermission(permission30['dashBoardBuilder'])]
    },
    {
      path: 'store/dashboards',
      loadComponent: () => import('src/app/dashboard-builder/pages/ifp-dashboard-list/ifp-dashboard-list.component').then((m: typeof import('src/app/dashboard-builder/pages/ifp-dashboard-list/ifp-dashboard-list.component')) => m.IfpDashboardListComponent),
      canActivate: [(route: ActivatedRouteSnapshot, state: RouterStateSnapshot) => () => inject(AuthGuardService).canActivate(route, state)],
      resolve: [() => inject(slaService).checkPermission(permission30['dashBoardBuilder'])]
    }
  ]
}];
@NgModule({
  imports: [RouterModule.forChild(ifpStoreroutes)],
  exports: [RouterModule]
})
export class IfpStoreRoutingModule { }
