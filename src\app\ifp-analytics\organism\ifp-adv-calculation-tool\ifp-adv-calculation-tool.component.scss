@use "../../../../assets/ifp-styles/abstracts/index" as *;

.ifp-adv-tool {
  &__search {
    display: block;
    margin-bottom: $spacer-4;
  }

  &__tab {
    margin-bottom: $spacer-3;
  }

  &__syntax-wrapper {
    border: 1px solid $ifp-color-grey-7;
    border-bottom-left-radius: 4px;
    border-bottom-right-radius: 4px;
    // margin-bottom: $spacer-3;
  }

  &__synatax-name {
    padding: $spacer-3;
  }

  &__syntax-body {
    padding: $spacer-2 $spacer-3;
    background-color: $ifp-color-grey-bg-2;
  }

  &__syntax-function {
    padding: $spacer-3 $spacer-0 $spacer-3 $spacer-0;
    border-bottom: 1px solid $ifp-color-grey-2;

    &:last-child {
      border-bottom: none;
    }
  }

  &__syntax-head {
    font-weight: $fw-extra-bold;
    margin-bottom: $spacer-1;
  }

  &__synatax-value {
    font-weight: $fw-extra-bold;
  }
  &__edit-outer {
    margin-top: $spacer-3;
  }
  &__body-list {
    width: 20%;
    min-width: 200px;
    position: relative;
    padding: $spacer-4 $spacer-0;
  }
  &__body-head {
    padding: $spacer-0 $spacer-4;
    margin-bottom: $spacer-3;
  }
  &__search {
    display: block;
    margin-bottom: $spacer-2;
  }
  &__field-list {
    padding: $spacer-0 $spacer-4;
    max-height: 280px;
    @include ifp-scroll-y(transparent, $ifp-color-grey-7, 8px, 8px);
  }
  &__field-icon {
    position: absolute;
    top: 50%;
    right: $spacer-2;
    transform: translateY(-50%);
  }
  &__field {
    text-transform: uppercase;
    cursor: grab;
    background-color: $ifp-color-section-white;
    padding: $spacer-2 $spacer-4 $spacer-2 $spacer-3;
    border: 1px solid $ifp-color-grey-2;
    border-radius: 7px;
    position: relative;
    user-select: none;
    transition: 0.3s;
    overflow: hidden;
    text-overflow: ellipsis;
    margin-bottom: $spacer-2;
    &:last-child {
      margin-bottom: $spacer-0;
    }
    &--dragging {
      background-color: $ifp-color-violet-light;
    }
  }
  &__notFound {
    text-align: center;
  }
  &__column-desc {
    margin: $spacer-0 $spacer-0 $spacer-3 $spacer-0;
    font-style: italic;
  }
}

.ifp-adv-calc {
  &__accordian {
    margin-bottom: $spacer-0;
  }

  &__editor-outer {
    position: relative;
    z-index: 1;
    margin-bottom: $spacer-3;
    padding: $spacer-0;
  }

  &__formula-wrapper {
    display: flex;
    flex-wrap: wrap;
    margin: $spacer-0 (
      -$spacer-2
    );
  max-height: 150px;
  @include ifp-scroll-y(transparent, $ifp-color-grey-1, 4px, 8px);
}

&__formula-tag {
  display: block;
  margin: $spacer-2;
}

&__preview {
  margin: (-$spacer-2) $spacer-0 $spacer-3;
  border-radius: 6px;
  padding: $spacer-4 $spacer-3 $spacer-3;
}

&__add-btn {
  margin-bottom: $spacer-4;
}

&__preview-head {
  margin: $spacer-3 $spacer-0 $spacer-1 $spacer-0;
  display: flex;
  justify-content: space-between;

  .ifp-adv-tool__sub-title {
    margin-bottom: $spacer-0;
    font-weight: $fw-semi-bold;
  }

  .ifp-link {
    display: inline-flex;
    align-items: center;

    .ifp-icon {
      font-size: $ifp-fs-4;
      margin-inline-end: $spacer-1;
    }
  }
}
&__accordian-columns {
  margin: $spacer-3 $spacer-0;
}
}

:host::ng-deep {
  .ifp-adv-calc__editor-outer {
    .CodeMirror {
      height: 175px;
      border-radius: 6px;
      padding-inline-end: $spacer-2;
    }
  }

  .ifp-adv-tool__tab {
    .ifp-tab__item {
      margin: $spacer-1 $spacer-0;
    }
  }

  .ifp-adv-tool__search {
    .ifp-search-box {
      height: 38px;
    }
  }
  .ifp-adv-tool__accordian {
    .ifp-accord-box {
      border-bottom-left-radius: 0px;
      border-bottom-right-radius: 0px;
    }
  }
  .ifp-adv-calc__accordian-columns {
    .ifp-accord-box__content {
      padding: $spacer-3 $spacer-0;
    }
  }
  .CodeMirror {
    color: $ifp-color-black-dark;
  }
  .CodeMirror-secondarycursor {
    display: none;
  }
  .cm {
    &-node,
    &-variable {
      color: $ifp-color-black-dark;
    }
  }
}
:host-context(.ifp-dark-theme) {
  ::ng-deep {
    .cm {
      &-builtin {
        color: $ifp-color-green-dark-2;
      }
    }
  }
}

:host-context([dir="rtl"]) {
  .ifp-adv-tool {
    &__syntax-head {
      direction: ltr;
      display: flex;
      justify-content: flex-end;
    }
  }
}
