import { buttonClass } from './../../../scad-insights/core/constants/button.constants';
import { TranslateModule } from '@ngx-translate/core';
import { Component, OnInit, OnDestroy, signal } from '@angular/core';
import { IfpDropdownComponent } from '../../../scad-insights/ifp-widgets/ifp-atoms/ifp-dropdown/ifp-dropdown.component';
import { IfpButtonComponent } from '../../../scad-insights/ifp-widgets/ifp-atoms/ifp-button/ifp-button.component';
import { ProgressCircleComponent } from '../../../usage-dashboard/progress-circle/progress-circle.component';
import { SubSink } from 'subsink';
import { ApiService } from 'src/app/scad-insights/core/services/api.service';
import { autoMlApiEndpoints } from '../constants/auto-ml.constant';
import { ActivatedRoute, Params, Router } from '@angular/router';
import { ToasterService } from 'src/app/scad-insights/core/services/tooster/ToastrService.service';
import { IfpNumberOnlyDirective } from 'src/app/scad-insights/core/directives/ifp-number-only.directive';
import {  FormControl, ReactiveFormsModule, Validators } from '@angular/forms';
import { IfpSpinnerComponent } from '../../../scad-insights/ifp-widgets/ifp-molecules/ifp-spinner/ifp-spinner.component';
import { SubscriptionLike } from 'rxjs';
import { environment } from 'src/environments/environment';
import { StreamingService } from 'src/app/scad-insights/core/services/http/streaming.service';
import { IfpInfoComponent } from '../../../scad-insights/ifp-widgets/ifp-molecules/ifp-heading-with-info/ifp-info.component';
import { IfpExploratoryService } from '../services/ifp-exploratory.service';
import { IfpExploratorySampleChartComponent } from '../ifp-exploratory-sample-chart/ifp-exploratory-sample-chart.component';
import { AsyncPipe, NgClass } from '@angular/common';
import { ThemeService } from 'src/app/scad-insights/core/services/theme/theme.service';
import { prepsApiEndpoints } from '../../data-prep/ifp-data-prep/constants/if-preps.constants';

@Component({
    selector: 'ifp-ifp-run-modal',
    imports: [TranslateModule, IfpDropdownComponent, IfpButtonComponent, ProgressCircleComponent, IfpNumberOnlyDirective,
        ReactiveFormsModule, IfpSpinnerComponent, IfpInfoComponent, AsyncPipe,
        IfpExploratorySampleChartComponent, NgClass],
    templateUrl: './ifp-run-modal.component.html',
    styleUrl: './ifp-run-modal.component.scss'
})
export class IfpRunModalComponent implements OnInit, OnDestroy {
  public  frequency = 'Unknown';
  public subs = new SubSink();
  public currentId!: string;
  public data!: RunModelInterface;
  public timeSerious: Timeseriescolumn[] =  [];
  public columns: string[] =  [];
  public unknown = 'Unknown';
  public frequencyValue!: Record<string, string>;
  public disableProcess = true;
  public buttonClass = buttonClass;
  public loader = true;
  public stampVariable ='';
  public selectedTimeSeries?: Timeseriescolumn;
  public controller!: AbortController;
  public iterationStreamSubscription!: SubscriptionLike;
  public summeryStreamSubscription!: SubscriptionLike;
  public range =  new FormControl('', [Validators.required]);
  public showLoader =false;
  public loaderPercentage = 0;
  public error = signal(false);
  public errorMessage = signal('');
  public dataLength = signal(0);
  public datasetChart = false;
  public workFlowId = '';
  public univariate = signal(false);
  public scenarioStreamSubscription !: SubscriptionLike;
  public scenarioId = signal('');

  constructor(private _autoMlService:ApiService, private _activatedRoute: ActivatedRoute,  private _router: Router,
    private _toaster: ToasterService,
    private _StreamService: StreamingService,
    private _ifpExploratoryService :IfpExploratoryService, public _themeService: ThemeService,
    private _stremService: StreamingService
  ) {
    this._ifpExploratoryService.summeryLoaded.set(false);
    // used to get dataset id
    this.subs.add(
      this._activatedRoute.queryParams.subscribe((event: Params) => {
        this.currentId = event['id'];
      })
    );
  }

  ngOnInit(): void {
    this._ifpExploratoryService.processIcon.set('ifp-icon-play-round');
    this._ifpExploratoryService.secondaryIcon.set('');
    this._ifpExploratoryService.processDisable.set(true);
    this._ifpExploratoryService.secondaryBtnDisable.set(true);
    this._ifpExploratoryService.secondaryBtn.set(true);
    this.setBtnName();
    this.subs.add(this._ifpExploratoryService.processEvent.subscribe(data => {
      if (data !== '') {
        if (this.datasetChart) {
          if (this.univariate()) {
            this._router.navigateByUrl(`scenario-details?id=${this.scenarioId()}`);
          } else {
            this. switchIteration();
          }

        } else {
          this.runModel(true);
        }

      }
    }));
    this.subs.add(this._ifpExploratoryService.processEventsecondary.subscribe(data => {
      if (data !== '') {
        if (this.datasetChart) {
          this.backToRun( );
        } else {
          this.runModel();
        }
      }
    }));
    if (this.currentId) {
      this.callSummeryStream();
    } else if (this._router.url.includes('auto-ml')) {
      this._router.navigate(['analytics/auto-ml/upload-data'], { replaceUrl: true, queryParams: { enableAutoMl: 'true' } });
    } else {
      this._router.navigate(['analytics/exploratory/upload-data'], { replaceUrl: true });
    }
    if (!this._router.url.includes('auto-ml')) {
      this._ifpExploratoryService.autoMlStatus.set(true);
    } else {
      this._ifpExploratoryService.autoMlStatus.set(false );
    }

  }

  // switch to dataset chart
  switchChart() {
    this.datasetChart = true;
    this.setBtnName();
  }

  backToRun( ) {
    this.datasetChart = false;
    this.setBtnName();
  }

  // set button name
  setBtnName() {
    this._ifpExploratoryService.processName.set('Run Model');
    if (this.datasetChart) {
      this._ifpExploratoryService.secondaryName.set('Variable Selection');
    } else {
      this._ifpExploratoryService.secondaryName.set('View Time Series Analysis');
    }

  }

  // go to iteration
  switchIteration() {
    if (this._router.url.includes('auto-ml')) {
      this._router.navigateByUrl(`analytics/auto-ml/data-result?id=${this.workFlowId}&dataId=${this.currentId}`);
    } else {
      this._router.navigateByUrl(`analytics/exploratory/data-result?id=${this.workFlowId}&dataId=${this.currentId}`);
    }
  }



  // used to call dropdown Data
  callFrequency() {
    this.subs.add(
      this._autoMlService.getMethodRequest(autoMlApiEndpoints.getFrequencyApi + this.currentId +autoMlApiEndpoints.frequency, {}, true).subscribe({next: (item: RunModelInterface)=> {
        this.data = item;
        this.columns = item.numerical_columns;
        this.timeSerious = item.timeseries_columns;
        this.frequencyValue = item.possible_frequencies ?? {};
        const frequency = this.timeSerious[0].frequency ?? '';
        this.frequency = this.frequencyValue[frequency] ?? this.unknown;
        this.loader = false;
        this.selectedTimeSeries = this.timeSerious[0];
        this.dataLength.set(item.dataset_length);
        this.dataChangeVariable(this.columns[0]);
        if (this.columns && this.columns.length ==1 ) {
          this.univariate.set(true);
        } else if (this.columns && this.columns.length <1 ) {
          this.error.set(true);
          this.errorMessage.set('At least one numerical columns are required to run Auto-ML.');
        }

      },
      error: (data: {error ?:{error:string}}) => {
        this._toaster.error(data?.error?.error);
        this.error.set(true);
        this.loader=false;
        this.errorMessage.set(data?.error?.error ?? '');
      }
      })
    );
  }

  // Back to upload data
  backToDataUpload() {
    this._router.navigateByUrl('/analytics/auto-ml/upload-data?enableAutoMl=true');
  }

  // target variable change detection
  dataChangeVariable(value:string ) {
    this.stampVariable = value;
  }

  // api call for run model
  runModel(run: boolean = false) {
    this.loaderPercentage = 0;
    this.disableProcess= true;
    this._ifpExploratoryService.processDisable.set(true);
    this._ifpExploratoryService.secondaryBtnDisable.set(true);
    this.showLoader =  true;
    const data = {
      dataset: this.currentId,
      metadata: {'target_variable': this.stampVariable, 'time_stamp_variable': this.selectedTimeSeries?.column, 'frequency': this.selectedTimeSeries?.frequency, 'frequency_range': this.range.value ? +this.range.value: 0 }};
    this.loaderPercentage = 10;
    const formData =  new FormData();
    formData.append('data', JSON.stringify(data)  );
    if (this.univariate()) {
      this.univeriantApicall(run, formData);
    } else {
      this.runApiCall(run, formData);
    }

  }

  // run api call
  runApiCall(run: boolean, formData: FormData) {
    this.subs.add(
      this._autoMlService.postMethodRequest(autoMlApiEndpoints.runModel, formData, {}, true).subscribe({next: (item: RunModal)=> {
        this.loaderPercentage = 40;
        this.workFlowId = item.object_id;
        this.callStream(item.object_id, run);
      },
      error: (error: {error ?:{error:string}}) => {
        this.disableProcess= false;
        this._ifpExploratoryService.processDisable.set(false);
        this._ifpExploratoryService.secondaryBtnDisable.set(false);
        this._toaster.error(error?.error?.error);
      }
      })
    );
  }

  // univariateWorkflow api call
  univeriantApicall(run: boolean, formData: FormData)  {
    this.subs.add(
      this._autoMlService.postMethodRequest(autoMlApiEndpoints.univariateWorkflow, formData, {}, true).subscribe({next: (item: {scenario_object_id: string, workflow_object_id: string})=> {
        this.loaderPercentage = 40;
        this.workFlowId = item.workflow_object_id;
        this.scenarioId.set(item.scenario_object_id);
        this.callScenarioStream(item.scenario_object_id, run);
      },
      error: (error: {error ?:{error:string}}) => {
        this.disableProcess= false;
        this._ifpExploratoryService.processDisable.set(false);
        this._ifpExploratoryService.secondaryBtnDisable.set(false);
        this._toaster.error(error?.error?.error);
      }
      })
    );
  }

  // stream api for check the status of model
  callStream(objectId: string, run: boolean) {
    this.iterationStreamSubscription = this._StreamService.connectToServerSentEvents(`${environment.prepbaseUrl+ autoMlApiEndpoints.streamApi +objectId +  autoMlApiEndpoints.streamProcess}`)
      .subscribe({
        next: (data: {data: string, ctrl: AbortController}) => {
          const dataValue = JSON.parse(data.data);
          this.controller  = data.ctrl;
          if ('Running'== dataValue.status) {
            if (this.loaderPercentage < 90) {
              this.loaderPercentage =  this.loaderPercentage + 3;
            }
          }
          if ('completed'== dataValue.status) {
            data.ctrl.abort();
            this.iterationStreamSubscription.unsubscribe();
            this.disableProcess= false;
            this._ifpExploratoryService.processDisable.set(false);
            this._ifpExploratoryService.secondaryBtnDisable.set(false);
            this.showLoader = false;
            this.loaderPercentage = 100;
            if (run) {
              this.switchIteration();
            } else {
              this.datasetChart = true;
              this.setBtnName();
            }

          }
        },
        error: () => {
          this._StreamService.close();
          this.disableProcess= false;
          this._ifpExploratoryService.processDisable.set(false);
          this._ifpExploratoryService.secondaryBtnDisable.set(false);
          this.showLoader = false;
          this.iterationStreamSubscription.unsubscribe();
          this._toaster.error('Iteration Error!');
        }
      }
      );
  }

  // stream api for check the status of model run
  callScenarioStream(objectId: string, run:boolean) {
    this.scenarioStreamSubscription = this._StreamService.connectToServerSentEvents(`${environment.prepbaseUrl+ autoMlApiEndpoints.scenarioStream +objectId +  autoMlApiEndpoints.streamProcess}`)
      .subscribe({
        next: (data: {data: string, ctrl: AbortController}) => {
          const dataValue = JSON.parse(data.data);
          this.controller  = data.ctrl;
          if ('pending'== dataValue.status) {
            if (this.loaderPercentage < 90) {
              this.loaderPercentage =  this.loaderPercentage + 3;
            }
          }
          if ('completed'== dataValue.status) {
            data.ctrl.abort();
            this.scenarioStreamSubscription.unsubscribe();
            this.disableProcess= false;
            this.loaderPercentage = 100;
            this.showLoader = false;
            this._ifpExploratoryService.processDisable.set(false);
            this._ifpExploratoryService.secondaryBtnDisable.set(false);
            if (run) {
              this._router.navigateByUrl(`scenario-details?id=${objectId}`);
            } else {
              this.datasetChart = true;
              this.setBtnName();
            }
          }
        },
        error: () => {
          this._StreamService.close();
          this.scenarioStreamSubscription.unsubscribe();
          this.disableProcess= false;
          this._ifpExploratoryService.processDisable.set(false);
          this._ifpExploratoryService.secondaryBtnDisable.set(false);
          this.showLoader = false;
          this._toaster.error('Scenario Error!');
        }
      }
      );
  }

  // summery stream used check file is loaded or not
  callSummeryStream() {

    this.summeryStreamSubscription = this._stremService.connectToServerSentEvents(`${environment.prepbaseUrl+ prepsApiEndpoints.summeryStream +this.currentId}/`)
      .subscribe({
        next: (data: {data: string, ctrl: AbortController}) => {
          const dataValue = JSON.parse(data.data);
          this.controller  = data.ctrl;
          if ('completed'== dataValue.status) {
            data.ctrl.abort();
            this.summeryStreamSubscription.unsubscribe();
            this.callFrequency();
            this._ifpExploratoryService.summeryLoaded.set(true);
          }
        },
        error: () => {
          this._stremService.close();
          this.summeryStreamSubscription.unsubscribe();
        }
      }
      );
  }



  // function trigger while changing time series dropdown or target
  dataChange(value:Timeseriescolumn | any) {
    this.frequency = this.frequencyValue[value.frequency] ?? this.unknown;
    this.selectedTimeSeries = value;
    this.range.setValue(null);
    this.inputChange();
  }

  // used to validate input field
  inputChange() {
    const value = this.range.value ?  + this.range.value: 0;
    const max= this.selectedTimeSeries?.max_horizon ?? 0;
    const min= this.selectedTimeSeries?.min_horizon ?? 0;
    if (  max >= value && value >= min) {
      this.disableProcess= false;
      this._ifpExploratoryService.processDisable.set(false);
      this._ifpExploratoryService.secondaryBtnDisable.set(false);
      this.setProcessUsingFrequency();
    } else {
      this.disableProcess= true;
      this._ifpExploratoryService.processDisable.set(true);
      this._ifpExploratoryService.secondaryBtnDisable.set(true);
    }

  }

  //  disable process button using frequency variable
  setProcessUsingFrequency() {
    if (this.frequency === this.unknown) {
      this.disableProcess= true;
      this._ifpExploratoryService.processDisable.set(true);
      this._ifpExploratoryService.secondaryBtnDisable.set(true);
    } else {
      this.disableProcess= false;
      this._ifpExploratoryService.processDisable.set(false);
      this._ifpExploratoryService.secondaryBtnDisable.set(false);
    }
  }

  ngOnDestroy(): void {
    this.subs?.unsubscribe();
    this.controller?.abort();
    this.summeryStreamSubscription?.unsubscribe();
    this.iterationStreamSubscription?.unsubscribe();
    this._ifpExploratoryService.processEvent.next('');
    this._ifpExploratoryService.processEventsecondary.next('');
    this._ifpExploratoryService.secondaryBtn.set(false);
  }
}


interface RunModelInterface {

  dataset_length: number;
  timeseries_columns: Timeseriescolumn[];
  numerical_columns: string[];
  possible_frequencies:Record<string, string>;
}



interface Timeseriescolumn {
  timeseries_columns: Timeseriescolumn[];
  numerical_columns: string[];
  possible_frequencies: Possiblefrequencies;
}

interface Possiblefrequencies {
  B: string;
  C: string;
  D: string;
  W: string;
  'W-MON': string;
  'W-TUE': string;
  'W-WED': string;
  'W-THU': string;
  'W-FRI': string;
  'W-SAT': string;
  'W-SUN': string;
  M: string;
  MS: string;
  BM: string;
  BMS: string;
  Q: string;
  QS: string;
  BQ: string;
  BQS: string;
  A: string;
  AS: string;
  BA: string;
  BAS: string;
}

interface Timeseriescolumn {
  column: string;
  frequency: null | string;
  max_horizon: number;
  min_horizon: number;
}
interface RunModal {
  object_id: string;
  dataset: string;
  metadata: Metadata;
}

interface Metadata {
  target_variable: string;
  time_stamp_variable: string;
  frequency: string;
  frequency_range: number;
}
