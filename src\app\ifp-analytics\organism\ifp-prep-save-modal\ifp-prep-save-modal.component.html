<div class="ifp-save-modal ifp-modal__body">
  <div class="ifp-save-modal__body">
    <div class="ifp-save-modal__header">
      <h3 class="ifp-save-modal__title">{{title | translate}}</h3>
      <em class="ifp-icon ifp-icon-cross ifp-save-modal__close-btn" (click)="onClose('close')"></em>
    </div>
    <p class="ifp-save-modal__desc">{{message | translate}}</p>
    <p class="ifp-save-modal__label">{{'File Name' | translate}}</p>
    <input type="text" [placeholder]="'Enter here' | translate" class="ifp-save-modal__input" [(ngModel)]="fileName"
      [maxlength]="200">
    @if (workFlowId() && workFlowId() !== '') {
    <div class="ifp-save-modal__radio">
      <p class="ifp-save-modal__radio-text">{{'Would you like to replace the current workflow or save it as a new one?'
        | translate}}</p>
      <div class="ifp-save-modal__radio-outer">
        <app-ifp-check-box class="ifp-adv-tool__checkbox ifp-adv-tool__checkbox--radio"
          [label]="'Save as new workflow' | translate" [type]="'radio'" [name]="'isReplace'" (checked)="selectType('new')"
          [checkedData]="saveType === 'new'"></app-ifp-check-box>
        <app-ifp-check-box class="ifp-adv-tool__checkbox ifp-adv-tool__checkbox--radio ifp-save-modal__last-radio"
          [label]="'Replace existing workflow' | translate" [type]="'radio'" [name]="'isReplace'" (checked)="selectType('replace')"
          [checkedData]="saveType === 'replace'"></app-ifp-check-box>
      </div>
    </div>
    }

  </div>
  <div class="ifp-save-modal__footer">
    <ifp-button [label]="buttonSecondary" (ifpClick)="onClose('cancel')" class="ifp-save-modal__footer-btn"
      [buttonClass]="buttonClass.secondary"></ifp-button>
    <ifp-button [label]="buttonTitle" (ifpClick)="onSave()" class="ifp-save-modal__footer-btn"
      [buttonClass]="fileName?.trim() === '' ? buttonClass.disabled : buttonClass.primary"></ifp-button>
  </div>
</div>
