<div class="ifp-toggle-sidebar" [ngClass]="{ 'ifp-toggle-sidebar--submenu': submenu, 'ifp-toggle-sidebar--expand': isToolbarExpanded,}">
  <!-- <em class="ifp-icon ifp-icon-left-arrow ifp-toggle-sidebar__btn" [appIfpTooltip]=" (isToolbarExpanded ? 'Collapse' : 'Expand')| translate" (click)="expandOrCollapseToolbar()" [zIndex]="2000"></em> -->
  <div class="ifp-toggle-sidebar__collapse" [ngClass]="{'ifp-toggle-sidebar__collapse--collapse': !isToolbarExpanded}" [appIfpTooltip]=" (isToolbarExpanded ? 'Collapse' : 'Expand')| translate" (click)="expandOrCollapseToolbar()"></div>
  <div class="ifp-toggle-sidebar__body" #siderWidth>
    <ng-content select=".ifp-toggle-sidebar__content"></ng-content>
  </div>
</div>
