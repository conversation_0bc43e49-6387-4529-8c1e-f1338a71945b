
import { environment } from 'src/environments/environment';
import { slaService } from '../services/sla/sla.service';
import { Injectable } from '@angular/core';


@Injectable({
  providedIn: 'root'
})
export class insightGuardService {

  constructor( private _slaService:slaService) {
  }

  resolve() {
       if (environment.env === 'staging' || environment.env === 'dev' ) {
        return this._slaService.checkInsightAccess();
       }
        return false;

  }


}
