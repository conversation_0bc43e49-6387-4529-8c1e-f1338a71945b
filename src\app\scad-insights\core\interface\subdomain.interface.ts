import { DomainPopularDatum } from './domain.interface';

export interface SubDomainDetails {
  subdomain: Subdomain;
  data: SubDomainDatum[];
  popularData: DomainPopularDatum[];
  nodeData: any;
}

export interface SubDomainPopularDatum {
  name: string;
  nodes: Node[];
}

export interface SubDomainDatum {
  name: string;
  light_icon: string;
  dark_icon: string;
  count: number;
  nodes: Node[];
  showTree: boolean;
  nodesData: any;
  key: any;
}


export interface SubDomainDetailsProduct {
  name: string;
  nodes: Node[];
}

interface Node {
  id: string;
  content_type: string;
  category: (Subdomain | string);
  name: string;
  products: SubDomainDetailsProduct[];
  title: string;
  subtitle: string;
  domains?: string[];
  domain?: string;
  theme?: any;
  subtheme?: any;
  product?: any;
  classification: string;
  content_classification: string;
}



interface Subdomain {
  id: string;
  name: string;
  dark_icon: string;
  light_icon: string;
}
