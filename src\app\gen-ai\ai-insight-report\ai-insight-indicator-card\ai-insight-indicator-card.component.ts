import { Async<PERSON><PERSON><PERSON>, DecimalPipe, NgClass } from '@angular/common';
import { Component, inject, input, OnChanges } from '@angular/core';
import { Store } from '@ngrx/store';
import { TranslateModule } from '@ngx-translate/core';
import { ThemeService } from 'src/app/scad-insights/core/services/theme/theme.service';
import { selectDomainIconGetByIds } from 'src/app/scad-insights/store/domain-icon/domain-icon.selector';

@Component({
  selector: 'ifp-ai-insight-indicator-card',
  imports: [TranslateModule, NgClass, AsyncPipe, DecimalPipe],
  templateUrl: './ai-insight-indicator-card.component.html',
  styleUrl: './ai-insight-indicator-card.component.scss',
})
export class AiInsightIndicatorCardComponent implements OnChanges {
  public iconClass = input<string>('ifp-icon-verified-tick');
  public title = input<string>('');
  public indicatorName = input<string>('');
  public percentage = input<number>(0);
  public arrowDirection = input<boolean>(false);
  public colorDirection = input<boolean>(false);
  public consolidated = input<boolean>(false);
  public id = input<string>('');
  public name = input<string>('');
  public firstPeroid = input<string>('Q1 2025');
  public secondPeroid = input<string>('Q4 2024');
  public _themeService = inject(ThemeService);
  private _store = inject(Store);
  public domainIconSelector$ = this._store.select(selectDomainIconGetByIds(0));
  ngOnChanges(): void {
    this.domainIconSelector$ = this._store.select(
      selectDomainIconGetByIds(+this.id())
    );
  }
}
