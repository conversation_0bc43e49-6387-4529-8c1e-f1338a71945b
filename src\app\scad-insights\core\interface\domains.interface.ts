
export interface Classification {
  content_type?: any;
  id: string;
  name: string;
  icon_path: string;
  nodeCount: number;
  isSelected: boolean;
  domains?:any[]
  nodes?:any[]
  showTree?: boolean;
  key?:string;
}

export interface DomainsNav {
  id: string;
  name: string;
  icon: string;
  route: string;
  node_count: number;
  isSelected: boolean;
  subdomain: Subdomain[];
  nodes?: Nodes[];
  parent_name?:string;
}

export interface Subdomain {
  id: string;
  name: string;
  node_count: number;
  route: string;
  isSelected: boolean;
  subtheme?: (Subtheme | Subtheme)[];
  domain?:string;
  title:string;
}

export interface Subtheme {
  id: string;
  name: string;
  node_count: number;
  route: string;
  isSelected: boolean;
}

export interface Nodes {
  id: string;
  content_type: string;
  title: string;
  subtitle: string;
  domains?: string[];
  domain?: string;
  theme?: any;
  subtheme?: any;
  product?: any;
  classification: string;
  category: Subdomain;
  name: string;
  products?: any;
}
