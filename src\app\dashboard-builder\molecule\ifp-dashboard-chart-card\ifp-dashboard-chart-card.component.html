<div class="ifp-db-card" [ngClass]="{'ifp-db-card--active': isActiveCard()}">
  @if (!cardConfig()?.config?.x_axis_category?.length && !cardConfig()?.config?.y_axis_series?.length) {
  <div class="ifp-db-card__configure-wrapper">
    <img class="ifp-db-card__preview-img" [src]="'../../../../assets/images/dashboard-builder/card-preview.svg'">
    <p class="ifp-db-card__configure-txt">{{'Start Creating Your Card' | translate}}</p>
    <p class="ifp-db-card__configure-desc">{{"Configure the X and Y axes, and define the necessary filters and aggregations based on your requirements." | translate}}</p>

    <ifp-button [label]="'Configure Data' | translate" [buttonClass]="buttonClass.primary"
      [iconClass]="'ifp-icon-file'" class="ifp-db-card__configure-btn" (ifpClick)="openConfigurePanel()"></ifp-button>

    <ifp-button [label]="'Remove Card' | translate" [buttonClass]=" buttonClass.secondary"
      class="ifp-db-card__configure-btn" ></ifp-button>
  </div>
}

<!-- for chart card  -->

@if (cardConfig()?.config?.x_axis_category?.length && cardConfig()?.config?.y_axis_series?.length) {
  <div class="ifp-db-card__chart-wrapper">
    <div class="ifp-db-card__chart-header-wrapper">
      <div class="ifp-db-card__card-title-grp">
      <em class="ifp-icon ifp-icon-bar-graph-icon"></em>
      <div class="ifp-db-card__chart-title">{{cardConfig().title}}</div>
      </div>
      <em class="ifp-icon ifp-icon-menu ifp-icon-kebab-menu"></em>
    </div>
    <div class="ifp-db-card__sub-title-wrapper">
      {{cardConfig().config.subtitle}}
    </div>

    <div class="ifp-db-card__chart-wrapper">
      <app-ifp-analytic-line-chart
        [chartData]="cardConfig().config?.y_axis_series" #chartComponent [yaxisLabel]="cardConfig().config.y_label" 
        [xAxisLabelType]="xAxisLabelType"
        [xAxisCatogory]="cardConfig()?.config?.x_axis_category" ></app-ifp-analytic-line-chart>
    </div>
    
  </div>
}
</div>