@use "../../../../assets/ifp-styles/abstracts/index" as *;

.ifp-data-table {
  position: relative;
  width: 100%;

  &__col {
    color: $ifp-color-grey-9;
    padding: $spacer-3 $spacer-2;

    &--click {
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: space-between;

      .ifp-data-table__value {
        .ifp-icon {
          position: relative;
          top: 1px;
          font-size: $ifp-fs-2;
        }
      }
    }
  }

  &__row {
    border-bottom: 1px solid $ifp-color-grey-7;

    &--head {
      border: none;

      .ifp-data-table__col {
        color: $ifp-color-primary-grey;
        font-weight: $fw-semi-bold;
        background-color: $ifp-color-grey-bg-2;
        padding-top: 8px;
        padding-bottom: 8px;
        position: sticky;
        top: 0;
        left: auto;
        z-index: 1;

        &:first-child {
          border-radius: 10px 0 0 10px;
        }

        &:last-child {
          border-radius: 0 10px 10px 0;
        }
      }
    }

    &--bg-red {
      background-color: $ifp-color-pale-red;
    }

    &--bg-blue {
      background-color: $ifp-color-pale-blue;
    }
  }

  &__refresh {
    font-size: $ifp-fs-5;
    cursor: pointer;
    color: $ifp-color-secondary-blue;
    transition: 0.3s;
    display: inline-block;
    position: relative;
    top: 2px;

    &:hover {
      transform: rotate(90deg);
    }
  }

  &__heading {
    text-transform: capitalize;
    display: flex;
    align-items: center;
  }

  &__sort-icon {
    display: block;
    font-size: $spacer-2;
    margin-inline-start: $spacer-8;
    color: $ifp-color-black;
    font-weight: $fw-bold;
    cursor: pointer;

    &--active {
      color: $ifp-color-blue-hover;
      cursor: default;
    }
  }

  &__value {
    max-width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  &__no-data {
    position: sticky;
    display: flex;
    justify-content: center;
    width: 100%;
    left: 0;
    top: 0;
  }
  &--capital-title {
    .ifp-data-table {
      &__row--head {
        .ifp-data-table__heading {
          text-transform: uppercase;
        }
      }
    }
  }
  &--dashboard {
    .ifp-data-table {
      &__col {
        vertical-align: middle;
        padding: $spacer-2;
        &--click {
          display: inline-block;
          .ifp-data-table {
            &__value {
              min-width: 45px;
              text-align: center;
              color: $ifp-color-grey-14;
              background-color: $ifp-color-white;
              padding: $spacer-1 $spacer-2;
              border: 1px solid $ifp-color-grey-13;
              border-radius: 20px;
              cursor: pointer;
              transition: 0.3s;
              &:hover {
                background-color: $ifp-color-grey-14;
                color: $ifp-color-white;
              }
            }
            &__arrow {
              display: none;
            }
          }
        }
      }
    }
  }
}

:host-context(.ifp-dark-theme) {
  .ifp-data-table__value {
    color: $ifp-color-primary-grey;
  }

  .ifp-data-table__row--bg-red {
    background-color: $ifp-color-red-light;

    .ifp-data-table__value {
      color: $ifp-color-white;
    }
  }
}


:host-context([dir="rtl"]) {
  .ifp-data-table {
    &__row {
      &--head {
        .ifp-data-table__col {
          &:first-child {
            border-radius: 0 10px 10px 0;
          }

          &:last-child {
            border-radius: 10px 0 0 10px;
          }
        }
      }
    }
  }
}



:host::ng-deep {
  .ifp-data-table {
    &__dynamic-cmp {
      .ifp-status-tag, .ifp-progress-bar {
        margin-bottom: $spacer-2;
      }
    }
  }
}
