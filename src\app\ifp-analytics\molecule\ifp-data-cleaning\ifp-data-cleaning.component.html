@if (!workFlowRunning() && !emptyConnection() && currentConection() && sourceData() && columnList && columnList.length !==0 && !errorMessageForTools) {
  <!-- <div class="ifp-adv-tool__action">
    <div class="ifp-adv-tool__action-inner">
      <ifp-button [label]="'cancel'" (ifpClick)="onCancel()" class="ifp-adv-tool__button ifp-adv-tool__action-btn" [buttonClass]="buttonClass.secondary"></ifp-button>
      <ifp-button [label]="'Save'" (ifpClick)="onSave()" class="ifp-adv-tool__button ifp-adv-tool__action-btn" [buttonClass]="isConfigUpdated() ?  buttonClass.primary: buttonClass.disabled"></ifp-button>
    </div>
  </div> -->
  <div class="ifp-adv-clean">
    <ifp-data-tool-accordian [title]="'Select Columns to Clean'" [enableCheckBox]="false" [isAccordianExpanded]="true" class="ifp-adv-tool__accordian ifp-adv-tool__accordian--tool-box">
      <div class="ifp-accord-box__content">
        <ifp-option-box [translation]="false" [isHeader]="true" [showBorder]="false" class="ifp-adv-clean__option-box" (selectOption)="getSelectedColumns($event)" [boxOptions]="columnOptions"
        ></ifp-option-box>
      </div>
    </ifp-data-tool-accordian>

    <div [ngClass]="{'ifp-adv-tool__disable': selectedCols.length === 0}">
      <ifp-data-tool-accordian [title]="'Replace Values'" [enableCheckBox]="false" class="ifp-adv-tool__accordian ifp-adv-tool__accordian--tool-box">
        <div class="ifp-accord-box__content">
          <form [formGroup]="replaceForm">
            <div class="ifp-adv-clean__replace-head">
              <app-ifp-check-box class="ifp-adv-clean__opt-label" [checkedData]="enabledFeature['replaceValue']" [label]="''" (checked)="setReplaceValue($event)"></app-ifp-check-box>
              <p class="ifp-adv-clean__replace-label">{{'Replace NULL' | translate}}</p>
              <!-- <app-ifp-dropdown formControlName="type" class="ifp-adv-clean__opts" [isInline]="true" [dropDownItems]="replaceList"></app-ifp-dropdown> -->
              <p>{{'with' | translate}}</p>
              <app-ifp-dropdown formControlName="with" class="ifp-adv-clean__opts"  [isInline]="true"
              [dropDownItems]="replaceValueList" ></app-ifp-dropdown>
            </div>
            <ng-container formGroupName="find_entry">

            <div class="ifp-adv-clean__replace">
              <app-ifp-check-box class="ifp-adv-clean__opt-label"  [checkedData]="enabledFeature['entry']" [label]="'Find any entry as'" (checked)="setReplaceValueEntry($event)"></app-ifp-check-box>
              <input type="text" formControlName="entry" class="ifp-adv-tool__input">
            </div>
            <div class="ifp-adv-clean__replace">
              <p class="ifp-adv-clean__replace-text">{{'and replace with' | translate}}</p>
              <input type="text"  formControlName="with" class="ifp-adv-tool__input">
            </div>
            </ng-container>
          </form>
        </div>
      </ifp-data-tool-accordian>

      <ifp-data-tool-accordian [title]="'Remove NULL Values'" [enableCheckBox]="false" class="ifp-adv-tool__accordian ifp-adv-tool__accordian--tool-box">
        <div class="ifp-accord-box__content">
          <!-- <div class="ifp-adv-clean__flex">
            <div class="ifp-adv-tool__info">
              <em class="ifp-icon ifp-icon-info-round"></em>
              <div class="ifp-adv-tool__info-box ifp-adv-tool__info-box--center">
                <p class="ifp-adv-tool__info-desc">{{'NULL represents the absence of any value or information, which is different than a blank or empty space, which still holds some visual representation in the data' | translate}}.</p>
              </div>
            </div>
          </div> -->
          <ifp-option-box class="ifp-adv-clean__option-box" [boxOptions]="nullValueOptions" [showBorder]="false" ></ifp-option-box>
        </div>
      </ifp-data-tool-accordian>

      <ifp-data-tool-accordian [title]="'Remove Characters'" [enableCheckBox]="false" class="ifp-adv-tool__accordian ifp-adv-tool__accordian--tool-box">
        <div class="ifp-accord-box__content">
          <ifp-option-box class="ifp-adv-clean__option-box" [boxOptions]="characterOptions" [showBorder]="false"></ifp-option-box>
        </div>
      </ifp-data-tool-accordian>

      <ifp-data-tool-accordian [title]="'Remove Duplicates'" [enableCheckBox]="false" class="ifp-adv-tool__accordian ifp-adv-tool__accordian--tool-box">
        <div class="ifp-accord-box__content">
          <div class="ifp-adv-clean__box-dropdown">
            <div class="ifp-adv-tool__checkbox-dropdown" [ngClass]="{'ifp-adv-tool__checkbox-dropdown--active': enabledFeature['removeDuplicates']}" (click)="enabledFeature['removeDuplicates'] = !enabledFeature['removeDuplicates']">
              <app-ifp-check-box class="ifp-adv-tool__checkbox" [label]="'Remove rows with duplicate values' | translate" [checkedData]="enabledFeature['removeDuplicates']"></app-ifp-check-box>
              <em class="ifp-icon ifp-icon-down-arrow"></em>
            </div>
            <div class="ifp-adv-tool__dropdown-list" [formGroup]="removeDuplicateForm">
              <div class="ifp-adv-tool__edit-outer">
                <p class="ifp-adv-tool__label">{{'Select Column' | translate}}</p>
                <app-ifp-dropdown [disableTranslation]="true" formControlName="selectedColumns" class="ifp-adv-tool__dropdown" [isMulti]="true" [dropDownItems]="selectedCols" [placeHolder]="'Select'"
                [disableSingleValue]="true"></app-ifp-dropdown>
              </div>
              <!-- <app-ifp-check-box class="ifp-adv-tool__checkbox" [label]="'Retain a unique value' | translate" [checkedData]="enabledFeature['retainUniqueValue']" (checked)="enabledFeature['retainUniqueValue'] = !enabledFeature['retainUniqueValue']"
                ></app-ifp-check-box> -->
              <p class="ifp-adv-tool__label">{{'Retain a unique value' | translate}}</p>
              <app-ifp-dropdown [key]="'name'"  formControlName="uniqueValue" class="ifp-adv-tool__dropdown"  [dropDownItems]="uniqueValueList"></app-ifp-dropdown>
            </div>
          </div>
        </div>
      </ifp-data-tool-accordian>
    </div>
  </div>
} @else {
  @if(errorMessageForTools) {
    {{errorMessageForTools}}
  } @else if(workFlowRunning()) {
    <div class="ifp-center-loader">
       <app-ifp-spinner></app-ifp-spinner>
       <p>     {{'Workflow Running' | translate}} </p>
     </div>

   } @else if (emptyConnection()){
     <p class="ifp-adv-tool__error"> <em class="ifp-icon ifp-icon-exclamation-round">

     </em>
     {{'Source must have a connection!' | translate}}
     </p>
   } @else if (!sourceData()) {
     <p class="ifp-adv-tool__error"> <em class="ifp-icon ifp-icon-exclamation-round">

     </em>
     {{'You must upload at least one source!' | translate}}
     </p>
   } @else if(!currentConection()){
     <p class="ifp-adv-tool__error"> <em class="ifp-icon ifp-icon-exclamation-round">

     </em>
       {{'Node must have atleast one connection' | translate}}
     </p>
   }
    @else {
     <app-ifp-spinner></app-ifp-spinner>
   }

}
