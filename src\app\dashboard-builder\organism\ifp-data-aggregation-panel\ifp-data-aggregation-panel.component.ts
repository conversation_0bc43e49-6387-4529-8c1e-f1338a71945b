import { ChangeDetectionStrategy, Component, forwardRef, inject, input, InputSignal, OnInit, output, signal, WritableSignal } from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';
import { IfpMultiTagDropdownComponent } from "../../../scad-insights/ifp-widgets/ifp-atoms/ifp-multi-tag-dropdown/ifp-multi-tag-dropdown.component";
import { IfpButtonComponent } from "../../../scad-insights/ifp-widgets/ifp-atoms/ifp-button/ifp-button.component";
import { buttonClass } from 'src/app/scad-insights/core/constants/button.constants';
import { columnAggregations } from '../../pages/dashboard.interface';
import { ControlValueAccessor, FormBuilder, FormGroup, NG_VALUE_ACCESSOR, ReactiveFormsModule, Validators } from '@angular/forms';
import { dashboardConstants } from 'src/app/scad-insights/core/constants/dashboard.constants';

@Component({
  selector: 'ifp-data-aggregation-panel',
  imports: [TranslateModule, IfpMultiTagDropdownComponent, IfpButtonComponent, ReactiveFormsModule],
  templateUrl: './ifp-data-aggregation-panel.component.html',
  styleUrl: './ifp-data-aggregation-panel.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [{
    provide: NG_VALUE_ACCESSOR,
    useExisting: forwardRef(() => IfpDataAggregationPanelComponent),
    multi: true
  }]
})
export class IfpDataAggregationPanelComponent implements OnInit, ControlValueAccessor {


  // inputs //
  possibleAggregationColumns: InputSignal<columnAggregations[]> = input<columnAggregations[]>([]);

  public buttonClass = buttonClass;
  public selectedXaxis: WritableSignal<columnAggregations> = signal({
    name: '',
    type: '',
    x_aggregations: [],
    y_aggregations: []
  })

  // outputs  //
  CreateCardActions = output<{ value: FormGroup, type: string }>()

  public selectedYaxis: WritableSignal<columnAggregations[]> = signal([])

  // formss // 
  private _formBuilder = inject(FormBuilder);
  public aggregationForm!: FormGroup;
  value: any;
  onChange: any = () => { };
  onTouched: any = () => { };



  writeValue(value: any): void {
    this.value = value;
  }

  registerOnChange(fn: any): void {
    this.onChange = fn;
  }

  registerOnTouched(fn: any): void {
    this.onTouched = fn;
  }



  ngOnInit(): void {
    this.aggregationForm = this._formBuilder.group({
      xAxisLabel: [''],
      xAxisValue: ['', Validators.required],
      yAxisLabel: [''],
      yAxisValue: ['', Validators.required],
    })
  }

  preview() {
    this.CreateCardActions.emit({ value: this.aggregationForm, type: dashboardConstants.cardPreview });
  }

  save() {
    this.CreateCardActions.emit({ value: this.aggregationForm, type: dashboardConstants.cardCreate });
  }

  selectXaxis(event: columnAggregations) {
    this.selectedXaxis.set(event);
    this.aggregationForm.controls['xAxisValue'].setValue(this.selectedXaxis());
  }

  selectYaxis(event: columnAggregations[]) {
    this.selectedYaxis.set(event);
    this.aggregationForm.controls['yAxisValue'].setValue(this.selectedYaxis());
  }
}


