@use '../../../../assets/ifp-styles/abstracts' as *;

.ifp-insight-discovery {
  margin: $spacer-5 $spacer-0;
  &__logo-grp {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: $spacer-3;
    .ifp-node__title {
      margin-bottom: $spacer-0;
    }
  }
  &__logo {
    height: 20px;
    margin-left: $spacer-3;
  }
  .ifp-node {
    // &__title {
    //   margin-bottom: $spacer-3;
    // }
    &__subtitle {
      margin-bottom: $spacer-5;
    }
  }
  &__title {
    font-size: $ifp-fs-6;
    font-weight: $fw-bold;
  }
  // &__heading {
  //   font-size: 3rem;
  //   font-weight: $fw-bold;
  //   margin-bottom: $spacer-3;
  // }

  // &__subheading {
  //   margin-bottom: $spacer-5;
  // }
}

@include mobile-tablet {
  .ifp-insight-discovery {
    &__title {
      font-size: $ifp-fs-4;
    }
  }
}
