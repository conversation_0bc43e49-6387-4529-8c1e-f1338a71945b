import { Component, input, signal } from '@angular/core';
import { NgStyle } from '@angular/common';
import { TranslateModule } from '@ngx-translate/core';
import { IfpStarRatingComponent } from "../../scad-insights/ifp-widgets/ifp-molecules/ifp-star-rating/ifp-star-rating.component";

@Component({
  selector: 'ifp-gen-ai-dashboard-rating',
  imports: [ NgStyle, TranslateModule, IfpStarRatingComponent],
  templateUrl: './gen-ai-dashboard-rating.component.html',
  styleUrl: './gen-ai-dashboard-rating.component.scss'
})
export class GenAiDashboardRatingComponent {
  public value = input<number>(0);
    public star = input<number>(0);
  public height = signal(300);
}
