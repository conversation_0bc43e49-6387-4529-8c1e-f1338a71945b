export const breakPoints ={
  0: {
    slidesPerView: 1,
    spaceBetween: 8
  },
  600: {
    slidesPerView: 2,
    // slidesPerGroup: 2,
    spaceBetween: 8
  },
  1024: {
    slidesPerView: 3,
    // slidesPerGroup: 3,
    spaceBetween: 16
  },
  1400: {
    slidesPerView: 3,
    // slidesPerGroup: 4,
    spaceBetween: 16
  },
  1920: {
    slidesPerView: 3,
    // slidesPerGroup: 4,
    spaceBetween: 16
  }
};

export const labelClassCarousel = {
  title: 'ifp-module-title',
  subTitle: 'ifp-module-subtitle',
  heading: 'ifp-module-heading'
};

export const classCarousel = {
  dynamic: 'ifp-carousel--dynamic',
  normal: 'ifp-carousel--normal',
  sixCol: 'ifp-carousel--six-col'
};
