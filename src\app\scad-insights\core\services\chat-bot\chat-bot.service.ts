import { Observable } from 'rxjs';
import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import {
  ThreadPreview,
  ThreadDetails
} from '../../interface/chatbot.interface';
import { environment } from 'src/environments/environment';

@Injectable({
  providedIn: 'root'
})
export class ChatBotApiService {
  public auditReportaApprovals = ['<EMAIL>', 'a<PERSON><PERSON><PERSON>@scad.gov.ae']
  public apiUrl: string = environment.genAiBaseUrl;

  constructor(private http: HttpClient) { }

  /** Get last 5 threads by users (V1) */
  getRecentThreads(searchString: string = ''): Observable<ThreadPreview[]> {
    return this.http.get<ThreadPreview[]>(`${this.apiUrl}/v1/chats?search=${searchString}`);
  }

  /** Get details of given thread along with all messages (V1)*/
  getThread(threadObjectId: string): Observable<ThreadDetails> {
    return this.http.get<ThreadDetails>(
      `${this.apiUrl}/v1/chats/${threadObjectId}/`
    );
  }

  getAccess() {
    return this.http.get(
      `${this.apiUrl}/v1/access`
    );
  }

  downloadAuditReport() {
    const endpoint = `${this.apiUrl}/v2/observability/message-trace-audit/export`;
    return this.http.get(endpoint, { responseType: 'blob', observe: 'response' });
  }

  downloadAuditReportAccessCheck() {
    return this.http.get(`${this.apiUrl}/v2/observability/message-trace-audit/access-check`);
  }
}
