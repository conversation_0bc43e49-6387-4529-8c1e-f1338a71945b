

import { prepStatusStore } from '../../constants/ifp-state.contants';
import { PrepNode } from '../../interface/ifp-data-prep.interface';

export const  createNodeStateName = 'createNodeComponent';
export interface NodeState {data: PrepNode; currentStatus: string}


export const createNodeInitalState:  NodeState  = {
  currentStatus: prepStatusStore.initial,
  data: {
    name: 'Data Processing Workflow',
    description: 'A workflow for processing data',
    nodes: [],
    connections: []
  }
};



