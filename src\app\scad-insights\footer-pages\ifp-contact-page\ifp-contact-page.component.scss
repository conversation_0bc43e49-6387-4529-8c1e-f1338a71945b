@use "../../../../assets/ifp-styles/abstracts/index" as *;

.ifp-contact {
  .ifp-module-heading {
    margin-bottom: $spacer-4;
  }
  &__sec {
    display: flex;
    align-items: center;
    padding: $spacer-4;
    background-color: $ifp-color-section-white;
    border-radius: 10px;
  }
  &__sec-1 {
    padding-right: $spacer-4;
  }
  &__sec-2 {
    padding-left: $spacer-4;
    border-left: 1px solid $ifp-color-grey-7;
    iframe {
      width: 100%;
    }
  }
  &__sec-1,
  &__sec-2 {
    width: 50%;
  }
  &__title {
    font-size: $ifp-fs-5;
    font-weight: $fw-medium;
    margin-bottom: $spacer-4;
  }
  &__radio {
    margin-bottom: $spacer-3;
  }
  &__message-info {
    font-size: $ifp-fs-2;
    margin-top: $spacer-1;
    display: flex;
    .ifp-icon {
      margin-right: $spacer-1;
      color: $ifp-color-secondary-blue;
    }
  }
  &__button {
    display: block;
    margin-top: $spacer-4;
  }
  &__location {
    margin-top: $spacer-4;
  }
  &__address-head {
    font-size: $ifp-fs-4;
    font-weight: $fw-bold;
    margin-bottom: $spacer-2;
  }
  &__address-wrapper {
    display: flex;
    flex-wrap: wrap;
    margin: $spacer-0 (-$spacer-2);
  }
  &__address {
    font-size: $ifp-fs-3;
    line-height: 1.6;
    display: flex;
    &:last-child {
      margin-bottom: $spacer-0;
    }
    .ifp-icon {
      margin-right: $spacer-2;
    }
    &--link {
      transition: 0.3s;
      display: inline-flex;
      margin: $spacer-1 $spacer-2;
      &:first-child {
        padding-right: $spacer-3;
        border-right: 1px solid $ifp-color-grey-7;
      }
      &:hover {
        color: $ifp-color-secondary-blue;
      }
    }
  }

  &__invalid {
    color: $ifp-color-red;
    display: flex;
    align-items: center;
    margin-bottom: $spacer-2;
    img {
      width: 18px;
      height: 18px;
      margin-right: $spacer-2;
    }
  }
  .ifp-textarea {
    display: block;
    resize: none;
    width: 100%;
    border: 1px solid $ifp-color-grey-7;
    border-radius: 10px;
    padding: $spacer-3;
    &:disabled {
      opacity: 0.7;
      pointer-events: none;
    }
  }
}

@include mobile-tablet {
  .ifp-contact {
    &__sec {
      display: block;
    }
    &__sec-1,
    &__sec-2 {
      width: 100%;
    }
    &__sec-1 {
      padding-right: $spacer-0;
    }
    &__sec-2 {
      padding-top: $spacer-4;
      padding-left: $spacer-0;
      border-left: 0;
      border-top: 1px solid $ifp-color-grey-7;
      margin-top: $spacer-4
    }
  }
}

:host-context([dir="rtl"]) {
  .ifp-contact {
    &__sec-1 {
      padding-left: $spacer-4;
      padding-right: $spacer-0;
    }
    &__sec-2 {
      padding-right: $spacer-4;
      padding-left: $spacer-0;
      border-right: 1px solid $ifp-color-grey-7;
      border-left: none;
    }
    &__message-info {
      .ifp-icon {
        margin-left: $spacer-1;
        margin-right: $spacer-0;
      }
    }
    &__address {
      .ifp-icon {
        margin-left: $spacer-2;
        margin-right: $spacer-0;
      }
      &--link {
        &:first-child {
          padding-left: $spacer-3;
          padding-right: $spacer-0;
          border-left: 1px solid $ifp-color-grey-7;
          border-right: none;
        }
      }
    }
  }
  @include mobile-tablet {
    .ifp-contact {
      &__sec-1 {
        padding-left: $spacer-0;
      }
      &__sec-2 {
        padding-right: $spacer-0;
        border-right: 0;
      }
    }
  }
}
