import { Component, EventEmitter, Input, Output } from '@angular/core';
import { Subject, debounceTime } from 'rxjs';
import { SubSink } from 'subsink';

@Component({
    selector: 'ifp-input-counter',
    imports: [],
    templateUrl: './ifp-input-counter.component.html',
    styleUrl: './ifp-input-counter.component.scss'
})
export class IfpInputCounterComponent {
  @Input() value: number = 0;
  @Input() key!: string;
  @Input() selectedCard!: string;
  @Output() updateSpacingValue: EventEmitter<{ value: number, key: string }> = new EventEmitter<{ value: number, key: string }>();

  private updateCount = new Subject<number>();
  private subs = new SubSink();

  constructor() {
    this.subs.add(this.updateCount
      .pipe(debounceTime(800))
      .subscribe((value) => {
        this.updateSpacingValue.emit({ value: value, key: this.key });
      }));
  }



  changeValue(type: string) {
    this.value = type == 'increase' ? (this.value + 1) : (this.value - 1);
    this.updateCount.next(this.value);
  }

  getCount(event: any) {
    this.value = +event.target.value;
    this.updateCount.next(this.value);
  }
}
