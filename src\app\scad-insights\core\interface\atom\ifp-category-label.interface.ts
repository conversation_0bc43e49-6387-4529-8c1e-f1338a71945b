export interface LabelData {
  id?: string;
  name: string;
  icon?: string;
  light_icon_path?: string;
  node_count?: number;
  isSelected?: boolean;
  route?: string;
  type?: string;
  data?: any;
  params?:any;
  tabName?:any;
  subdomain?: any[];
  sub?:any;
  category?:any;
  subId?:any;
  themeId?:any;
  screener?:boolean;
  app_type?:string;
  application_url?:string;
  content_classification?:string;
  field_attachment?:string;
  newsletter_url?:string;
  products?:any[];
  score?:string;
  subtitle?:string;
  contentType?:string;
  machineName?:string;
  iconClass?: string;
  domain?:string;
  parentId?:string;
  subtheme?:any[];
  dark_icon?:string;
  light_icon?:string;
  isExpand?:boolean;
  nodes?:any[];
  showTree?: boolean;
  title?:any;
  subdomains?:any[];
  subthemes?:any[];
  nodeCount?:number;
  categories?:any[];
  content_type?: any;
  theme?:any;
  subDomains?:any[];
  child?:string;
  key?:string;
  disabled?:boolean;
  CATEGORY_ID?:string;
  CATEGORY?:string;
  badge?: boolean;
  count?:number;
  isCustom?:boolean;
  hide?:boolean;
}
