<div class="ifp-gen-ai-dashboard-run-preview" [formGroup]="form" [ngClass]="{'ifp-gen-ai-dashboard-run-preview--close': !expand()}"  >
  <div class="ifp-gen-ai-dashboard-run-preview__question-wrapper">
    <div class="ifp-gen-ai-dashboard-run-preview__question-outer">
  <div class="ifp-gen-ai-dashboard-run-preview__question">
    <label class="ifp-gen-ai-dashboard-run-preview__question-label">
      {{'QUESTION' | translate}}
    </label>
    <input  class="ifp-gen-ai-dashboard-run-preview__file-input" formControlName="question" />
  </div>
  @if (expand()) {
    <div class="ifp-gen-ai-dashboard-run-preview__response">
    <label class="ifp-gen-ai-dashboard-run-preview__question-label">
      {{'ANSWER' | translate}}
    </label>
    <textarea [rows]="5 " formControlName="expected_answer" class="ifp-gen-ai-dashboard-run-preview__file-input" ></textarea>
  </div>
}
 </div>
 <div class="ifp-gen-ai-dashboard-run-preview__question-expand">
   <div class="ifp-gen-ai-dashboard-run-preview__feed-resp-inner"  [ngClass]="{'ifp-gen-ai-dashboard-run-preview__feed-resp--active': expand()}" (click)="expand.set(!expand())">
          @if ( expand()) {
            <em class="ifp-icon ifp-icon-up-arrow ifp-gen-ai-dashboard-run-preview__feed-resp-icon"></em>
          }@else {
            <em class="ifp-icon ifp-icon-down-arrow ifp-gen-ai-dashboard-run-preview__feed-resp-icon"></em>
          }
        </div>

 </div>
  </div>
@if (expand()) {
  <div class="ifp-gen-ai-dashboard-run-preview__footer">
    <div class="ifp-gen-ai-dashboard-run-preview__footer-left">
      <em class="ifp-icon ifp-icon-beautify ifp-gen-ai-dashboard-run-preview__exp-resp-icon  ifp-gen-ai-dashboard-run-preview__icon-blue"></em>
          {{'GENERATED RESPONSE' | translate}}
    </div>
      <ifp-mark-down  class="ifp-gen-ai-dashboard-run-preview__footer-right" [data]="question().ai_answer?.answer"></ifp-mark-down>

  </div>
}


</div>
