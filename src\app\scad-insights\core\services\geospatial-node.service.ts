import { Injectable } from '@angular/core';
import {HttpService} from "./http/http.service";
import {geospatialNodeApi} from "../apiConstants/geospatial-node.api.constants";
import {SubSink} from "subsink";
import { of, Observable } from 'rxjs';
import { map } from 'rxjs/operators';

@Injectable({
  providedIn: 'root'
})
export class GeospatialNodeService {

  subsink: SubSink = new SubSink();

  // addaLayers: any = ['Mosques', 'Quran memorizing centers', 'Airports', 'Bus stations', 'Cinemas', 'Petrol stations', 'Pharmacies', 'Centers for people of determination', 'Parks', 'Theme parks', 'Shopping malls', 'Zoos', 'Hotels', 'Cafes', 'Restaurants' ];
  addaLayers: any = ["Schools", "Hospitals", "Clinics", "Sports Club", "Nurseries", "Mosques", "Quran memorizing centers", "Airports", "Bus stations", "Cinemas", "Petrol stations", "Pharmacies", "Centers for people of determination", "Parks", "Theme parks", "Shopping malls", "Zoos", "Hotels", "Cafes", "Restaurants"];

  constructor(
    private _http: HttpService,
  ) { }

  // getNodeData(){
  //   return this._http.get(geospatialNodeApi.geospatialNode);
  // }

  getNodeData(): Observable<any> {
    return this._http.get(geospatialNodeApi.geospatialNode).pipe(
      map((data: any[]) => {
        return data.map(item => {
          if (item.modules) {
            item.modules = item.modules.filter((module: any) => module.configuration.SHOW_ON_IFP !== false && module.configuration.ACTIVE !== false);
          }
          return item;
        });
      })
    );
  }

  checkStatus() {
    let data = this._http.get(geospatialNodeApi.geospatialNode);
    return data;
  }

  getAllLayerArray(): Observable<string[]> {
    return this.getNodeData().pipe(
      map((nodes: any) => {
        const layersToBeAuthenticated: string[] = [];
        nodes.map((node: any) => {
          // for common layers
          const commonLayers = node.commonLayers;
          commonLayers.map((commonLayer: any) => {
            let isAvailable = this.addaLayers.includes(commonLayer.name);
            if (!isAvailable) {
              layersToBeAuthenticated.push(commonLayer.endpoint);
            }
          });

          // for custom layers
          const customLayers = node.customLayers;
          customLayers.map((customLayer: any) => {
            layersToBeAuthenticated.push(customLayer.endpoint);
          });

          // for all modules
          const modules = node.modules;
          modules.map((module: any) => {
            const layers = module.layers;
            layers.map((layer: any) => {
              layersToBeAuthenticated.push(layer.endpoint);
            })
          });
        });
        return layersToBeAuthenticated;
      })
    );
  }

}
