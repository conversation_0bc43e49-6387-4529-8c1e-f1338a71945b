
import { DecimalPipe } from '@angular/common';
import { Pipe, PipeTransform } from '@angular/core';
import { numberType } from '../constants/numberType.constant';
import { TranslateService } from '@ngx-translate/core';

@Pipe({
  name: 'shortNumber',
  standalone: true
})
export class ShortNumberPipe implements PipeTransform {
  constructor(private _dec: DecimalPipe, private _translate: TranslateService) {
  }

  public keys: Record<string, string> = {
    Q: 'Q',
    T: 'T',
    B: 'B',
    M: 'M',
    k: 'K'
  };

  transform(number: any, args?: string): any {
    if (typeof(number) === 'string') {
      number = number.replaceAll(',', '');
    }
    const value = +number;
    if (value === null || value === undefined ||isNaN(value) ) {

      if (args)  {
        return { neg: '', value: null, key: '' };
      }
      return null;
    }
    if (value === 0) {
      if (args)  {
        return { neg: '', value: '0', key: '' };
      }
      return '0';
    }
    const fractionSize = 1;
    let abs = Math.abs(value);
    const rounder = Math.pow(10, fractionSize);
    const isNegative = value < 0;
    let key = '';
    const powers = [{ key: 'Q', value: Math.pow(10, 15) }, { key: 'T', value: Math.pow(10, 12) }, { key: 'B', value: Math.pow(10, 9) }, { key: 'M', value: Math.pow(10, 6) }, { key: 'k', value: 1000 }];
    for (const element of powers) {
      let reduced = abs / element.value;
      reduced = Math.round(reduced * rounder) / rounder;
      if (reduced >= 1) {
        abs = reduced;
        key = element.key;
        break;
      }
    }
    if (args === numberType.shotKeyFormat)  {
      return { neg: (isNegative ? '-' : ''), value: this._dec.transform(abs, '0.0-2'), key: this.keys?.[key] ? this._translate.instant( this.keys[key]  ): ''  };
    } else if (args === numberType.percentage) {
      return { neg: (isNegative ? '-' : ''), value: this._dec.transform(abs, '0.0-2'), key: '%' };
    }
    return (isNegative ? '-' : '') + this._dec.transform(abs, '0.0-2') + key;
  }

}
