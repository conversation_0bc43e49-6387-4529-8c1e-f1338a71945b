@use "../../../../assets/ifp-styles/abstracts/index" as *;
$max-width-rec: 50px;
$width-text:100px;
.ifp-correlogram {
  display: inline-block;
  &__row  {
    display:flex ;
  }
  &__rec {
    border: 1px solid $ifp-color-grey-3;
    padding: $spacer-2;
    position: relative;
    width: $max-width-rec;
    height: $max-width-rec;
    max-width: $max-width-rec;
    min-width: $max-width-rec;
    white-space: nowrap;
    display: flex;
    justify-content: center;
    align-items: center;
    &:hover {
      .ifp-chart-tooltip {
        &__wrapper {
          visibility: visible;
          opacity: 1;
        }
      }
    }
  }
  &__circle {
    border-radius: 50%;
    width: 100%;
    height: 100%;
  }
  &__text {
    max-width: $width-text;
    min-width: $width-text;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;

  }
  &__text-first {
    margin-top: $spacer-2;
    margin-bottom: $spacer-2;
    text-align: center;
  }
  &__text-wrapper  {
    display: flex;
    align-items: center;
  }
  &__text-last {
    margin-left: $spacer-2;
    margin-right: $spacer-2;
    display: flex;
    align-items: center;
    white-space: nowrap;
  }
  &__bar-wrap {
    display: flex;
    margin-top: $spacer-2;
  }
  &__bar {
	background: #e5405e;
  height: 12px;
  border-radius: 5px;
  display: flex;
  border: solid 1px $ifp-color-grey-3;
	/* Chrome10-25,Safari5.1-6 */
  background: -webkit-gradient(linear, left top, right top, from(rgba(255, 50, 50)), color-stop(50%, rgba(162 162 162)), to(rgb(50, 227, 50)));
  background: linear-gradient(to right, rgba(255, 50, 50) 0%, rgba(162 162 162) 50%, rgb(50, 227, 50) 100%);
  }
  &__dash-wrapper {
    display: flex;
    justify-content: space-between;
  }
&__text-last-value {
  max-width: 100px;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}
}
.ifp-chart-tooltip {
  &__wrapper {
    position: absolute;
    visibility: hidden;
    opacity: 0;
    bottom: 100%;
    margin-bottom: $spacer-2;
    z-index: 1;
    color: $ifp-color-white-global;
    white-space: normal;
    min-width: 250px;
  }
  &__header {
    max-width: 250px;
    word-break: break-all;
  }
  &__value {
    max-width: 250px;
    word-break: break-all;
  }
}

