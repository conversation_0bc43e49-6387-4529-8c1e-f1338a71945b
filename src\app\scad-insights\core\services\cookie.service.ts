import { Injectable } from '@angular/core';
@Injectable({
  providedIn: 'root'
})
export class CookieService {

  // check encoded or decode url and return correct value
  private safeDecodeURIComponent(encodedURIComponent: string): string {
    try {
      return decodeURIComponent(encodedURIComponent);
    } catch {
      return encodedURIComponent;
    }
  }

  // used to get all cookies
  getAll(): Record<string, string> {
    const cookies: Record<string, string> = {};
    if (document.cookie && document.cookie !== '') {
      document.cookie.split(';').forEach((currentCookie: string) => {
        const [cookieName, cookieValue] = currentCookie.split('=');
        cookies[this.safeDecodeURIComponent(cookieName.replace(/^ /, ''))] = this.safeDecodeURIComponent(cookieValue);
      });
    }
    return cookies;
  }

  // used to delete all cookies
  deleteAllCookies(): void {
    const cookies: any = this.getAll();
    for (const cookieName in cookies) {
      if (Object.prototype.hasOwnProperty.call(cookies, cookieName)) {
        const date = new Date('Thu, 01 Jan 1970 00:00:01 GMT');
        const cookieString = `${encodeURIComponent(cookieName)}='';expires=${date.toUTCString()};path=/;`;
        document.cookie = cookieString;
      }
    }
  }

  getCookie(cname: string) {
    const name = `${cname}=`;
    const decodedCookie = decodeURIComponent(document.cookie);
    const ca = decodedCookie.split(';');
    for (const iterator of ca) {
      let c = iterator;
      while (c.startsWith(' ')) {
        c = c.substring(1);
      }
      if (c.startsWith(name)) {
        return c.substring(name.length, c.length);
      }
    }

    return '';
  }

  setCookie(cookieName: string, cookieValue: string, expiry: number = 7) {
    const currentDate = new Date();
    currentDate.setTime(currentDate.getTime() + (expiry * 24 * 60 * 60 * 1000));
    const expires = `expires=${currentDate.toUTCString()}`;
    document.cookie = `${cookieName}=${cookieValue}; ${expires}; path=/`;
  }
}
