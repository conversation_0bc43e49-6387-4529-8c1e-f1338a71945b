import { ChangeDetectionStrategy, Component, Input, input } from '@angular/core';

@Component({
  selector: 'ifp-progress-bar',
  imports: [],
  templateUrl: './ifp-progress-bar.component.html',
  styleUrl: './ifp-progress-bar.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class IfpProgressBarComponent {

  @Input({required : true}) value:number= 0;
  @Input() color: string ='#5DB14E'
}
