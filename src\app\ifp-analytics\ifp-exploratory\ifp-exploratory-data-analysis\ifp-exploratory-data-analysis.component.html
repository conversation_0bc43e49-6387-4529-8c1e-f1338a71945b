<div class="ifp-exp">
  <div class="ifp-container">
    <div class="ifp-exp__hamburger" (click)="toggleData()">
      <em  class="ifp-icon ifp-icon-customize"></em>
    </div>
    <div class="ifp-exp__wrapper">
      @if (
        toggle()
      ) {
           <div class="ifp-exp__wrapper-overlay" (click)="toggleData()"></div>
      }
       <div class="ifp-exp__wrapper-section" [ngClass]="{'ifp-exp__wrapper-section--large' :toggle()}">
        <div class="ifp-exp__summery" [ngClass]="{'ifp-exp__summery--active': summeryActive}" (click)="summeryClick()">
          <h2 class="ifp-exp__title">{{ "View Summary" | translate }}</h2>
          <em class="ifp-icon ifp-icon-right-arrow"></em>
        </div>
        <ifp-data-tool-accordian class="ifp-exp__acc" [active]="chartOpen"
          [ngClass]="{'ifp-exp__acc--disable': summeryLoader}" [isAccordianExpanded]="true" [selection]="true"
          [title]="'Explore Visually'" [enableCheckBox]="false" [isDraggable]="false">
          <div class="ifp-exp__acc-wrapper ifp-accord-box__content">
            @for (chart of customChartTypes(); track chart.key; let i = $index) {
            <ifp-icon-selector [icon]="chart" class="ifp-exp__chart-icon"
              (selectIcon)="chartSelection($event)"></ifp-icon-selector>
            }
          </div>
        </ifp-data-tool-accordian>

        <ifp-data-tool-accordian class="ifp-exp__acc" [active]="correlationValue"
          [ngClass]="{'ifp-exp__acc--disable': summeryLoader || correlationDisable()}" [isAccordianExpanded]="true"
          [selection]="true" [title]="'Correlation Analysis'" [enableCheckBox]="false" [isDraggable]="false">
          <div class="ifp-exp__radio-wrapper ifp-accord-box__content">
            <app-ifp-check-box (checked)="correlationPearson()" [checkedData]="pearson" class="ifp-exp__acc-radio"
              [type]="'radio'" [boarder]="true" [label]="'Pearson'"></app-ifp-check-box>
            <!-- <app-ifp-check-box  (checked)="correlationSpearman()" [checkedData]="spearman" class="ifp-exp__acc-radio" [boarder]="true"  [type]="'radio'" [label]="'Spearman'"></app-ifp-check-box> -->

          </div>
        </ifp-data-tool-accordian>
                    @if (!summeryLoader &&correlationDisable()) {

                    <div class="ifp-exp__error-wrapper">
                      <em class="ifp-icon ifp-icon-exclamation-round"></em>
                      <p class="ifp-exp__error">
                        {{'At-least two non-null numerical columns are required to perform correlation analysis.' | translate}}
                      </p>
                    </div>
            }

      </div>
      <div class="ifp-exp__wrapper-table">
        @if (summeryActive) {
        <ifp-data-summary [enableProcessBtn]="false" [totalColumn]="totalColumn" [totalRow]="totalRow" [buttonName]="'Proceed to Advanced Analytics'" [border]="true" [backButton]="false"
          [settings]="summeryTableSettings" [data]="summery" [loaderPage]="true" [disableProcessButton]="false"
          (proceed)="processClick()"></ifp-data-summary>
        } @else if (chartOpen) {
        <div class="ifp-exp__chart-wrapper">
          <div class="ifp-exp__chart-header">
            <div class="ifp-exp__chart-title">
              @switch (chartType) {
              @case (customChartTypes()[0].key) {
              {{'Explore Visually - Numerical Variables' | translate}}
              }
              @case (customChartTypes()[1].key) {
              {{'Explore Visually - Categorical Variables' | translate}}
              }
              @case (customChartTypes()[2].key) {
              {{'Explore Visually - Unique Values' | translate}}
              }
              @default {
              {{'Explore Visually' | translate}}
              }
              }

            </div>
            <!-- <ifp-button [label]="'Proceed to Advanced Analytics' | translate" class="ifp-select-data__button"
              [buttonClass]="buttonClass.primary" [iconClass]="'ifp-icon-rightarrow'"
              (ifpClick)="processClick()"></ifp-button> -->
          </div>
          <!-- used for box-plot chart -->
          @switch (chartType) {
          @case (chartKeys.column) {
          <div class="ifp-exp__chart-process">
            <p class="ifp-exp__drop-title">{{'Metric Variable' | translate}}</p>
            <div class="ifp-exp__drop-sec">
              <app-ifp-dropdown [disableTranslation]="true" [formControl]="dropdown" [minLimit]="1" [limit]="5"
                [title]="'Metric Variable'" [isMulti]="true" [showTitle]="false" [dropDownItems]="data.variables"
                (dropDownItemMultiClicked)="dropdownChange()"></app-ifp-dropdown>
              <ifp-tag [icon]="'ifp-icon-info-round'" [backgroundDark]="'#020202'" [border]="false" [color]="'#9DA2AE'"
                [background]="'#F3F4F6'" class="ifp-exp__tag"
                [tagName]="'A maximum of five columns can be selected, some may be unavailable if they contain null values.'"></ifp-tag>
            </div>
          </div>
          }

          @case ( chartKeys.box  ) {
          @if (boxChart.length !== 0) {
          <div class="ifp-exp__chart-process">
            <p class="ifp-exp__drop-title">{{'Select the numeric variable' | translate}}</p>
            <div class="ifp-exp__drop-sec">
              <app-ifp-dropdown [disableTranslation]="true" [formControl]="dropdownBoxChart" [title]="'Metric Variable'"
                [key]="'column'" [isMulti]="false" [showTitle]="false" [dropDownItems]="boxChart"
                (dropDownItemClicked)=" CallBoxPlot()"></app-ifp-dropdown>
              <ifp-tag [icon]="'ifp-icon-info-round'" [backgroundDark]="'#020202'" [border]="false" [color]="'#9DA2AE'"
                [background]="'#F3F4F6'" class="ifp-exp__tag"
                [tagName]="'Only one numerical column can be selected at a time'"></ifp-tag>
            </div>
          </div>
          }

          }
          @case (chartKeys.histogram ) {
            @if (boxChart.length !== 0) {
            <div class="ifp-exp__chart-process">
              <p class="ifp-exp__drop-title">{{'Select the numeric variable' | translate}}</p>
              <div class="ifp-exp__drop-sec">
                <app-ifp-dropdown [disableTranslation]="true" [formControl]="dropdownHistogramChart" [title]="'Metric Variable'"
                  [key]="'column'" [isMulti]="false" [showTitle]="false" [dropDownItems]="boxChart"
                  (dropDownItemClicked)=" callHistogram()"></app-ifp-dropdown>
                <ifp-tag [icon]="'ifp-icon-info-round'" [backgroundDark]="'#020202'" [border]="false" [color]="'#9DA2AE'"
                  [background]="'#F3F4F6'" class="ifp-exp__tag"
                  [tagName]="'Only one numerical column can be selected at a time'"></ifp-tag>
              </div>
            </div>
            }

            }
          @case (chartKeys.table ) {
          @if (tableData.length !== 0) {
          <div class="ifp-exp__chart-process">
            <p class="ifp-exp__drop-title">{{'Select the Category variable' | translate}}</p>
            <div class="ifp-exp__drop-sec">
              <app-ifp-dropdown [disableTranslation]="true" [formControl]="tableDropdown" [title]="'Metric Variable'"
                [key]="'name'" [isMulti]="false" [showTitle]="false" [dropDownItems]="tableData"
                (dropDownItemClicked)=" setTableData()"></app-ifp-dropdown>
              <ifp-tag [icon]="'ifp-icon-info-round'" [backgroundDark]="'#020202'" [border]="false" [color]="'#9DA2AE'"
                [background]="'#F3F4F6'" class="ifp-exp__tag"
                [tagName]="'Only one Category column can be selected at a time'"></ifp-tag>
            </div>
          </div>
          }

          }
          @case (chartKeys.scatter  ) {
            <div class="ifp-exp__chart-process" [formGroup]="scatterForm">
              <p class="ifp-exp__drop-title">{{'Select the numeric variable' | translate}}</p>
              <div class="ifp-exp__drop-sec">
                <app-ifp-dropdown   [disableTranslation]="true" [formControl]="scatterForm.controls.scatterXValue" [title]="'Metric Variable'"
                  [key]="'column'" [isMulti]="false" [showTitle]="false" [dropDownItems]="scatterChartDropdownX"
                  (dropDownItemClicked)=" CallScatterX()"></app-ifp-dropdown>
                  <app-ifp-dropdown  class="ifp-exp__dropdown" [disableTranslation]="true" [formControl]="scatterForm.controls.scatterYValue" [title]="'Metric Variable'"
                  [key]="'column'"   (dropDownItemClicked)=" CallScatterY()" [isMulti]="false" [showTitle]="false" [dropDownItems]="scatterChartDropdownY"
                 ></app-ifp-dropdown>

              </div>
            </div>

            }

          }
        </div>

        <div class="ifp-exp__sec-chart">
          <!-- <div class="ifp-exp__chart-download">
                <div class="ifp-exp__download-text" >
                  {{'Download'| translate}}
                </div>
                <div class="ifp-exp__download-item" (click)="downloadExcelOpen()"  appOutsideClick (outsideClick)="outsideClick()">
                  <img src="../../../../assets/images/xlsx-icon.svg" class="ifp-exp__download-icon">
                  <ul class="ifp-exp__download-dropdown" [ngClass]="{'ifp-exp__download-dropdown--active': showDownloadOptions()}">
                    <li class="ifp-exp__download-option"(click)="downloadExcel(false)" >{{'Download full data' | translate}}</li>
                    <li class="ifp-exp__download-option" (click)="downloadExcel(true)">{{'Download selected data' | translate}}</li>
                  </ul>
                </div>
                <div class="ifp-exp__download-item" (click)="downloadImage()">
                  <img src="../../../../assets/images/img-icon.svg" class="ifp-exp__download-icon">
                </div>
                <div>

                </div>
              </div> -->



          <div class="ifp-exp__chart">
            @switch (chartType) {
            @case (chartKeys.column) {

            <app-ifp-analytic-line-chart [yaxisLabel]="'No. of unique values' | translate" [legend]="false" [xAxisLabel]="'Variables'| translate"
              [chartData]="yAxis" [xAxisCatogory]="xAxis"
              [xAxisLabelType]="'category'| translate"></app-ifp-analytic-line-chart>
            }
            @case (chartKeys.box) {
            <!-- <ifp-highcharts [marker]="false"  [yaxisLabel]="'No. of unique values'"   [data]="bubbleData" [chartName]="'bubbleChart'" [height]="400"></ifp-highcharts> -->
            <!-- used for box-plot chart -->
            @if (boxChart.length !== 0) {
            <ifp-highcharts [comparisonEnable]="false" [category]="boxPlotCategory" [data]="chartData"
              [chartName]="'boxChart'" [height]="400" [yaxisLabel]="'Values'| translate"></ifp-highcharts>

            } @else {
            <app-ifp-no-data></app-ifp-no-data>
            }

            }
            @case (chartKeys.table) {

            <app-ifp-table [tableData]="[selectedTableData]"></app-ifp-table>
            }
            @case (chartKeys.histogram ) {
              @if (histogramPlotCategory.length !== 0) {
              <ifp-highcharts [comparisonEnable]="false" [category]="histogramPlotCategory" [data]="histogramData ?? []"
              [chartName]="'histogram'" [height]="400" [yaxisLabel]="'Frequency'| translate"></ifp-highcharts>
              }@else if (histogramLoader()) {
                <app-ifp-spinner></app-ifp-spinner>
              }
              @else {
                <app-ifp-no-data></app-ifp-no-data>
                }
            }
            @case (chartKeys.scatter) {

              <ifp-exploratory-numeric-chart [yAxis]="scatterForm.value.scatterYValue?.column"   [xAxis]="scatterForm.value.scatterXValue?.column" [objectId]="summeryId"></ifp-exploratory-numeric-chart>
              }

            }


          </div>
        </div>
        } @else if ( correlationValue) {
        <div class="ifp-exp__chart-wrapper">
          <div class="ifp-exp__chart-header">
            <div class="ifp-exp__chart-title">
              {{'Correlation' | translate}}
            </div>
            <!-- <ifp-button [label]="'Proceed to Advanced Analytics' | translate" class="ifp-select-data__button"
              [buttonClass]="buttonClass.primary" [iconClass]="'ifp-icon-rightarrow'"
              (ifpClick)="processClick()"></ifp-button> -->
          </div>

          <div class="ifp-exp__chart-process">
            @if (pearson) {
            <h5 class="ifp-exp__heading-cor">{{'Pearson' | translate}}</h5>
            <p class="ifp-exp__subheading-cor">{{'Pearson correlation measures linear association between two numerical variables.'| translate}}</p>
            <p class="ifp-exp__drop-title">{{'Metric Variable' | translate}}</p>
            } @else {
            <h5 class="ifp-exp__heading-cor">{{'Spearman' | translate}}</h5>
            <p class="ifp-exp__subheading-cor">{{'Spearman correlation measure the monotonic relationship between two numerical variables.'| translate}}</p>
            <p class="ifp-exp__drop-title">{{'Metric Variable' | translate}}</p>
            }

            <div class="ifp-exp__drop-sec">
              <app-ifp-dropdown [formControl]="dropdownCorrelation" [minLimit]="1" [limit]="5"
                [title]="'Metric Variable'" [isMulti]="true" [showTitle]="false"
                [dropDownItems]="correlogram"></app-ifp-dropdown>
              <ifp-tag [backgroundDark]="'#020202'" [icon]="'ifp-icon-info-round'" [border]="false" [color]="'#9DA2AE'"
                [background]="'#F3F4F6'" class="ifp-exp__tag"
                [tagName]="'A maximum of 5 columns can be selected'"></ifp-tag>
            </div>
          </div>

        </div>


        <div class="ifp-exp__sec-chart">
          <div class="ifp-exp__heading-cor">
            {{'Correlogram' | translate}}
          </div>
          <ifp-correlogram [correlogramData]="pearson ? correlogramDataChart.pearson:correlogramDataChart.spearman "
            [correlogram]="dropdownCorrelation.value ?? []"> </ifp-correlogram>
        </div>
        }

      </div>

    </div>
  </div>
</div>
@if (showValuePrint()) {
<div class="ifp-exp__download-display ifp-exp__download" #elementChart>
  <app-ifp-pdf-template>
    <div class="ifp-node__left-actions">
      <div class="ifp-node__group-one">
        <h2 class="ifp-node__tiltle">{{'Variable-Total Unique Values'| translate}}</h2>
      </div>


      <div class="ifp-node__chart">
        <ng-container>
          @switch (chartType) {
          @case (customChartTypes()[0].key) {

          <app-ifp-analytic-line-chart [enableAnimation]="false" class="ifp-exp__highchart"
            [yaxisLabel]="'No. of unique values'" [legend]="false" [chartData]="yAxis" [xAxisCatogory]="xAxis"
            [xAxisLabelType]="'category'"></app-ifp-analytic-line-chart>
          }
          @case (customChartTypes()[1].key) {
          <ifp-highcharts [marker]="false" [yaxisLabel]="'No. of unique values'" [enableAnimation]="false"
            class="ifp-exp__highchart" [data]="bubbleData" [chartName]="'bubbleChart'" [height]="400"></ifp-highcharts>

          }
          @case (customChartTypes()[2].key) {

          <app-ifp-table [tableData]="[selectedValues]"></app-ifp-table>
          }

          }
          <div>

          </div>

        </ng-container>
      </div>
    </div>




  </app-ifp-pdf-template>
</div>
}
<app-ifp-modal [overlayClass]="'ifp-modal__bg-grey'" #modalSucess>

  <ifp-prep-popup [processButtonDisabled]="false" [processButton]="true" processText="Proceed to Advanced Analytics"
    [popupButtonList]="popupView" (closePopUp)="closeSucessModal()" [orButton]="true"
    (buttonEventValue)="buttonClick($event)" [heading]="'How do you want to proceed?'"
    [desc]="('features' | translate) + summery.length +' '+ ('columns' | translate)"
    [fileName]="_exploratory.uploadedFile().name ?? ''" [subTitle]="'Refine your dataset'"
    [subDesc]="'Use any of the tools below:'"></ifp-prep-popup>

</app-ifp-modal>
