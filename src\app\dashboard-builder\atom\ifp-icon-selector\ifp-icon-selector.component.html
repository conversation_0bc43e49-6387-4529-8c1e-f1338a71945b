<div class="ifp-toolbar-icon" (click)="selectChartType(icon)" [ngClass]="{'ifp-toolbar-icon--active': icon.selected , 'ifp-toolbar-icon--dsiabled' : icon.disabled, 'ifp-toolbar-icon--small': isSmall}">
  <div class="ifp-toolbar-icon__icon">
    <em class="ifp-icon" [class]="icon.icon"></em>
    <div class="ifp-toolbar-icon__tick"><em class="ifp-icon ifp-icon-tick"></em></div>
  </div>
  @if (icon.title && enableTitle) {
    <p class="ifp-toolbar-icon__name">{{ icon.title | translate }}</p>
  }
</div>
