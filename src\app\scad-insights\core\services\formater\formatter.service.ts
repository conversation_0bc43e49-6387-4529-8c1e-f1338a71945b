import { Injectable } from '@angular/core';
import { appType } from '../../constants/contentType.constants';

@Injectable({
  providedIn: 'root'
})
export class FormatterService {

  dateFormatter(format: string,  value: string| number) {

  }

  setIndicatorUrl(type: string | undefined, id: string | number) {
    if (type === appType.scad_official_indicator.name) {
      return `${appType.scad_official_indicator.urlPrefix}/${type}/${id}`;
    } else if (type === appType.officialInsights.name) {
      return `${appType.officialInsights.urlPrefix}/${appType.officialInsights.type}/${id}`;
    } else if (type === appType.innovativeInsights.name) {
      return `${appType.innovativeInsights.urlPrefix}/${appType.innovativeInsights.type}/${id}`;
    } else if (type === appType.insights_discovery.name) {
      return `${appType.insights_discovery.urlPrefix}/${id}`;
    } else if (type === appType.internal.name) {
      return `${appType.internal.urlPrefix}/${id}`;
    } else if (type === appType.tableau_internal.name) {
      return `${appType.tableau_internal.urlPrefix}/${type}/${id}`;
    } else if (type === appType.what_if.name) {
      return `${appType.what_if.urlPrefix}/${id}`;
    } else if (type === appType.correlation.name) {
      return `${appType.correlation.urlPrefix}/${id}`;
    }
    return;
    // return url !== '' ? url : null;
  }
}
