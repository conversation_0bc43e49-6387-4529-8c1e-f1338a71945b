@use "../../../assets/ifp-styles/abstracts/index" as *;
// .ifp-modalCertificate {
//   margin-left: 8px;
//   margin-right: 8px;
//   background-color: $ifp-color-white;
//   border-radius: 10px;
//   width: 100%;
//   padding: 40px;
//   border: solid $ifp-color-grey-7 1px;
//   overflow: hidden;
//   position: relative;
//   text-align: center;
//   box-shadow: 0 0 30px rgba(0, 0, 0, 0.16);
//   &__card-subsection {
//     width: 100%;
//     border: solid $ifp-color-grey-7 1px;
//     overflow: hidden;
//     position: relative;
//     text-align: center;
//   }
//   &__title {
//     font-size: $ifp-fs-8;
//     font-weight: $fw-bold;
//     margin-bottom:  $spacer-3;
//   }
//   &__sub-title{
//     padding:  $spacer-0 $spacer-5;
//     margin-bottom:  $spacer-3;
//   }
//   &__subsection-title {
//     background-color: $ifp-color-grey-4;
//     border-bottom: solid $ifp-color-grey-7 1px;
//   }
//   &__wrapper {
//     min-height: calc(100vh - 395px);
//   }
// }
.ifp-main {
  &__wrapper {
    min-height: calc(100vh - 242px);
  }
  &__footer {
    // position: relative;
    // z-index: 3;
  }
}
