@use "../../../../../assets/ifp-styles/abstracts/index" as *;
@use "../../../../../assets/ifp-styles/components/data-preview.scss" as *;
:host::ng-deep {
  .ifp-data-preview__table {
    .ifp-back {
      color: inherit;
      .ifp-icon {
        color: $ifp-color-blue-hover;
      }
    }
    &--logs {
      .ifp-data-table__value {
        max-width: none;
      }
    }
  }
  .ifp-data-table__no-data {
    width: 100vw;
  }
}

.ifp-data-preview::after {
  content: '';
  width: 100%;
  height: var(--after-height, 0);
  display: block;
  margin-left: -24px;
}

.ifp-data-preview {
  &__pagination-wrapper {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: $spacer-2;
  }
  &__table {
    width: 100%;
  }
  &__desc {
    margin-inline-start: $spacer-3;
    margin-inline-end: $spacer-3;
    font-size: $ifp-fs-4;
    color: $ifp-color-tertiary-text;
  }
  &__pagination {
    margin-top: $spacer-0;
  }
  &__strong {
    font-weight: $fw-extra-bold;
  }
  &__menu {
    margin-bottom: $spacer-3;
  }
  &__switch-view {
    display: inline-block;
  }
}
