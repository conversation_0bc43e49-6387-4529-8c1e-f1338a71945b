<div class="ifp-p-adv" #fullscreenDiv
  [ngClass]="{'ifp-p-adv--fullscreen': document.fullscreenElement && prepService.showPreview(), 'ifp-p-adv--collapse': collapsed}">
  <ifp-prep-toolbar #toolBar class="ifp-p-adv__toolbar" [tools]="tools" [isAccordian]="true" [isDragable]="true"
    (collapsedEvent)="collapsedEvent($event)"></ifp-prep-toolbar>

  <div class="ifp-p-adv__content" #content>
    <!-- header start -->
    <ifp-prep-header #header [disableWorkFlow]="disableWorkFlow" class="ifp-prep__header" [isExpertMode]="true"
      (run)="startWorkFlow()" (saveEvent)="createSaveWorkflowModel()" [sourceDataUploaded]="sourceDataUploaded"
      [workFlowRunning]="prepService.workflowRunningStatus" [streamId]="prepService.streamId" (changeMode)="openBasicTool($event)"
      (togggleToolOption)="toggleCanvasTool($event)" (openWorkflow)="openWorkFlow()"
      [tabItems]="tabItems" [selectedTab]="selectedTab"></ifp-prep-header>
    <!-- header end -->

    <!-- body start -->
    <!-- <div class="ifp-p-adv__content-wrapper">
      <ifp-prep-advance-playground class="ifp-prep__ply-ground" (toggleFullscreen)="toggleFullscreen()"></ifp-prep-advance-playground>
      <div class="ifp-p-adv__tool">
        <h3 class="ifp-p-adv__tool-title">{{'Select' | translate}}</h3>
        <ifp-adv-select-tool></ifp-adv-select-tool>
      </div>
    </div> -->

    <div class="ifp-p-adv__body-wrapper" #resizableDiv>
      <ifp-prep-advance-playground #playGround [previewSpace]="prepService.currentNodeId() && prepService.showPreview()"
        class="ifp-prep__ply-ground" [removeSelection]="removeSelection" (toggleFullscreen)="toggleFullscreen()"
        (connectionCreated)="onCreateConnection($event)" (nodeClickEnd)="nodeClickEnd($event)"
        (nodeRemoved)="onRemoveNode()" [rightPanelWidth]="rightPanelWidth" (nodeCreated)="nodeCreated($event)"
        [canvasTool]="canvasTool" (selectionRemoved)="selectionRemoved($event)"></ifp-prep-advance-playground>
      <!-- right panel  start-->
      @if (selectedNode) {
      <ifp-advance-right-panel #rightPanel [name]="selectedNode.name" class="ifp-p-adv__right-panel"
        [description]="selectedNode.description ?? ''" (cancel)="removePanel($event)"
        [ngClass]="{'ifp-p-adv__right-panel--expand' : !prepService.showPreview()}">
        <div class="ifp-adv-panel__content">
          @switch (selectedNode.name) {
          @case ('source') {
          <ifp-adv-source-tool [nodeData]="selectedNode" (selectData)="onDataSelect()" (save)="removePanel()"
            (runWorkflow)="callWorkFlow()" [workFlowRunning]="prepService.workflowRunningStatus"
            (resetToolConfigs)="openResetModel($event)"></ifp-adv-source-tool>
          }
          @case ('select') {
          <ifp-adv-select-tool [errorMessageForTools]="errorMessageForTools" #toolRef [sourceData]="sourceDataUploaded" [emptyConnection]="connectionEmpty"
            [currentConection]="currentNodeConnections" [currentNodeId]="activeNode"
            [workFlowRunning]="prepService.workflowRunningStatus" [columnList]="upStreamColumnList"
            [dataTypes]="dataTypes" (cancel)="removePanel()"></ifp-adv-select-tool>
          }
          @case ('aggregate') {
          <ifp-adv-aggregate-tool [errorMessageForTools]="errorMessageForTools" #toolRef [columnList]="upStreamColumnList" [currentNodeId]="activeNode"
            [sourceData]="sourceDataUploaded" [emptyConnection]="connectionEmpty"
            [currentConection]="currentNodeConnections" [workFlowRunning]="prepService.workflowRunningStatus"
            (cancel)="removePanel()"></ifp-adv-aggregate-tool>
          }
          @case ('join') {
          <ifp-adv-join-tool [errorMessageForTools]="errorMessageForTools" #toolRef [columnList]="allSourceColumnList"
            [currentConnectionCount]="currentConnectionCount" [currentNodeId]="activeNode" (cancel)="removePanel()"
            [sourceData]="sourceDataUploaded" [emptyConnection]="connectionEmpty"
            [currentConection]="currentNodeConnections"
            [workFlowRunning]="prepService.workflowRunningStatus"></ifp-adv-join-tool>
          }
          @case ('sort') {

          <ifp-adv-sort [errorMessageForTools]="errorMessageForTools" #toolRef [sourceData]="sourceDataUploaded" [emptyConnection]="connectionEmpty"
            [currentConection]="currentNodeConnections" [currentNodeId]="activeNode"
            [workFlowRunning]="prepService.workflowRunningStatus" [columnList]="upStreamColumnList"
            (cancel)="removePanel()"></ifp-adv-sort>
          }
          @case ('append') {
          <ifp-adv-append-tool [errorMessageForTools]="errorMessageForTools" #toolRef [sourceColumnList]="allSourceColumnList" [currentNodeId]="activeNode"
            [currentConnectionCount]="currentConnectionCount" (cancel)="removePanel()" [sourceData]="sourceDataUploaded"
            [emptyConnection]="connectionEmpty" [currentConection]="currentNodeConnections"
            [workFlowRunning]="prepService.workflowRunningStatus"></ifp-adv-append-tool>
          }
          @case ('clean') {
          <ifp-data-cleaning [errorMessageForTools]="errorMessageForTools" #toolRef [sourceData]="sourceDataUploaded" [emptyConnection]="connectionEmpty"
            [currentConection]="currentNodeConnections" [currentNodeId]="activeNode"
            [workFlowRunning]="prepService.workflowRunningStatus" [columnList]="upStreamColumnList"
            (cancel)="removePanel()"></ifp-data-cleaning>
          }
          @case ('destination') {
          <ifp-adv-destination-tool [currentNodeId]="activeNode" (process)="callWorkFlow($event)"
            (cancel)="removePanel()" [sourceData]="sourceDataUploaded" [emptyConnection]="connectionEmpty"
            [currentConection]="currentNodeConnections"
            [workFlowRunning]="prepService.workflowRunningStatus"></ifp-adv-destination-tool>
          }
          @case ('impute') {
          <ifp-adv-impute [errorMessageForTools]="errorMessageForTools" #toolRef [columnList]="upStreamColumnList" [currentNodeId]="activeNode"
            [sourceData]="sourceDataUploaded" [emptyConnection]="connectionEmpty"
            [currentConection]="currentNodeConnections" [workFlowRunning]="prepService.workflowRunningStatus"
            (cancel)="removePanel()"></ifp-adv-impute>
          }
          @case ('filter') {
          <ifp-adv-filter-tool [errorMessageForTools]="errorMessageForTools" #toolRef [columnList]="upStreamColumnList"
            [currentConnectionCount]="currentConnectionCount" [currentNodeId]="activeNode" (cancel)="removePanel()"
            [sourceData]="sourceDataUploaded" [emptyConnection]="connectionEmpty"
            [currentConection]="currentNodeConnections"
            [workFlowRunning]="prepService.workflowRunningStatus"></ifp-adv-filter-tool>
          }
          @case ('calculations') {
          <ifp-adv-calculation-tool [errorMessageForTools]="errorMessageForTools" [errorMessageForTools]="errorMessageForTools" #toolRef [columnList]="upStreamColumnList" [nodeData]="selectedNode"
            [currentNodeId]="activeNode" [sourceData]="sourceDataUploaded" [emptyConnection]="connectionEmpty"
            [currentConection]="currentNodeConnections" [workFlowRunning]="prepService.workflowRunningStatus"
            (cancel)="removePanel()"></ifp-adv-calculation-tool>
          }
          }
        </div>
      </ifp-advance-right-panel>
      }
    </div>
    @if (prepService.currentNodeId() && prepService.showPreview()) {
    @if (advPreview?.tableDataDetail?.length !=0 || advPreview?.summeryLoader() || prepService.isNoRecordFound()) {
    <em class="ifp-icon ifp-icon-vertical-arrows ifp-prep-data__preview-resize"
      (mousedown)="onMouseDown($event, resizableDiv)" (touchstart)="onTouchStart($event, resizableDiv)"></em>
    }
    <ifp-advance-prep-preview [showProbabilty]="false" class="ifp-prep-data__preview ifp-prep-data__preview--advance"
      #advPreview></ifp-advance-prep-preview>
    }
    <!-- right panel  end-->
  </div>

  <!-- body end -->
</div>

<app-ifp-modal #confirmModal>
  <app-ifp-remove-card [text]="'Proceeding with exit will cause data loss. Do you still want to continue?'"
    [firstButton]="'Cancel'" [secondButton]="'Continue'" (firstButtonEvent)="removeConfirmModal()"
    (secondButtonEvent)="openBasic()">
  </app-ifp-remove-card>
</app-ifp-modal>

<app-ifp-modal [overlayType]="'transparent'" #saveWorkflowModal>
  <ifp-prep-save-modal (closeModal)="closeSaveWorkFlowModel()"
    [message]="'Your workflow will be saved in the library.' | translate" (saveFile)="saveEvent($event)"
    [fileName]="fileName" [workFlowId]="prepService.selectedWorkflow"></ifp-prep-save-modal>
</app-ifp-modal>

<app-ifp-modal #resetModal>
  <app-ifp-remove-card
    [text]="resetErrorMessage"
    [firstButton]="'reset'" (firstButtonEvent)="resetModalClose()" (firstButtonEvent)="resetToolConfig()"
    [hideSecondButton]="true">
  </app-ifp-remove-card>
</app-ifp-modal>
