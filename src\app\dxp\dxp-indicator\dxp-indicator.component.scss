@use "../../../assets/ifp-styles/abstracts/index" as *;
.ifp-dxp-kpis {
  display: flex;
  flex-direction: column;
  width: 100%;
  min-height: auto; // Allow natural height expansion

  &__section-head {
    margin-bottom: $spacer-3;
    flex-shrink: 0; // Don't shrink the header

    // Default styling when no tabs (original style)
    text-align: end;
    // Make space for the positioned button from parent when no tabs
    padding-left: 150px; // Space for the button

    // When tabs are present, use flexbox layout
    &:has(.ifp-dxp-kpis__tabs-wrapper) {
      display: flex;
      justify-content: space-between;
      align-items: center;
      gap: $spacer-4; // Add gap between tabs and filter
      text-align: initial; // Reset text alignment
      padding-left: 0; // Remove left padding when tabs are present
    }
  }
  &__tabs-wrapper {
    flex: 1;
    display: flex;
    align-items: center;
  }

  &__recent {
    // Default styling when no tabs (original style) - right aligned
    text-align: end;
    display: block;

    // When tabs are present, use flex properties for same row layout
    .ifp-dxp-kpis__section-head:has(.ifp-dxp-kpis__tabs-wrapper) & {
      display: flex;
      justify-content: flex-end;
      align-items: center;
      flex-shrink: 0; // Don't shrink the filter
      text-align: initial; // Reset text alignment when in flex mode
    }
  }

  &__filters-wrapper {
    display: flex;
    align-items: center;
    gap: $spacer-4;
    justify-content: flex-end; // Always align to the right
    flex-wrap: wrap; // Allow wrapping on smaller screens
  }

  &__filter-item {
    display: flex;
    align-items: center;
    gap: $spacer-2;
    white-space: nowrap;
  }

  &__filter-label {
    color: $ifp-color-grey-14;
    font-size: $ifp-fs-5;
    white-space: nowrap;
  }

  &__entity-dropdown,
  &__creator-dropdown,
  &__sort-dropdown {
    min-width: 200px;
    width: 200px;
    position: relative;
    overflow: visible; // Allow dropdown to expand beyond container
  }
  &__wrapper {
    display: flex;
    flex-wrap: wrap;
    gap: 0 $spacer-3; // Only horizontal gap, vertical spacing handled by margin
    margin: $spacer-0;
    align-items: flex-start; // Align cards to top instead of stretching
    align-content: flex-start; // Align rows to top
    overflow: visible; // Remove scrolling - let content flow naturally
    padding: $spacer-2; // Add padding around the container
    box-sizing: border-box;
    width: 100%; // Full width
    min-height: auto; // Allow natural height
  }

  // Loader styles
  &__loader {
    width: 100%;
    height: 400px;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: transparent;
  }

  // No data styles
  &__no-data {
    width: 100%;
    height: 400px;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: transparent;
  }

  // Pagination styles
  &__pagination {
    margin-top: $spacer-4;
    padding: $spacer-3 $spacer-2;
    display: flex;
    justify-content: center;
    border-top: 1px solid $ifp-color-grey-12;

    // Custom scrollbar styling
    &::-webkit-scrollbar {
      width: 8px;
    }

    &::-webkit-scrollbar-track {
      background: rgba(0, 0, 0, 0.05);
      border-radius: 4px;
      margin: $spacer-2 0; // Add margin to scrollbar track
    }

    &::-webkit-scrollbar-thumb {
      background: $ifp-color-blue-hover;
      border-radius: 4px;
      border: 1px solid rgba(255, 255, 255, 0.2);

      &:hover {
        background: #0056b3; // Darker blue for hover state
      }
    }

    // Ensure proper spacing between rows
    & > * {
      margin-bottom: 16px; // Remove any default margins
    }
  }

  &__filter-text {
    color: $ifp-color-blue-hover;
    display: inline-flex;
    align-items: center;
    cursor: pointer;
  }
}

// Styling for ifp-panel-dropdown components
:host ::ng-deep .ifp-dxp-kpis__entity-dropdown,
:host ::ng-deep .ifp-dxp-kpis__creator-dropdown,
:host ::ng-deep .ifp-dxp-kpis__sort-dropdown {
  .ifp-panel-dropdown {
    width: 100%;

    &__select-box {
      width: 100%;
      min-width: 200px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    &__selected {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      max-width: calc(100% - 30px); // Account for dropdown arrow
      display: inline-block;
    }

    &__list-wrapper {
      min-width: 250px; // Increased for better option display
      max-width: 350px; // Prevent excessive width
    }

    &__list {
      min-width: 250px;
      max-width: 350px;
    }

    &__list-item {
      padding: 8px 12px;

      .ifp-panel-dropdown__user {
        display: flex;
        align-items: center;
        gap: 8px;
        width: 100%;

        .ifp-panel-dropdown__user-tag {
          flex: 1;
          min-width: 0; // Allow shrinking
        }
      }
    }

    &__list-text {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      max-width: 100%;
    }
  }
}

// Specific styling for creator dropdown with user tags
:host ::ng-deep .ifp-dxp-kpis__creator-dropdown {
  .ifp-panel-dropdown {
    &__user {
      display: flex !important;
      align-items: center !important;
      gap: 8px !important;
      width: 100% !important;
      padding: 4px 0 !important;

      app-ifp-check-box {
        flex-shrink: 0;
      }

      .ifp-panel-dropdown__user-tag {
        flex: 1;
        min-width: 0;

        .ifp-name-tag {
          width: 100% !important;
          justify-content: flex-start !important;

          .ifp-abbr-tag {
            flex-shrink: 0;
            margin-right: 8px;
          }

          .ifp-name-tag__name {
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            flex: 1;
            min-width: 0;
          }
        }
      }
    }

    &__list-item {
      &--disabled {
        opacity: 0.6;
      }
    }
  }
}

// Legacy styling for old ifp-dropdown components (if any remain)
:host ::ng-deep .ifp-dxp-kpis__entity-dropdown,
:host ::ng-deep .ifp-dxp-kpis__creator-dropdown,
:host ::ng-deep .ifp-dxp-kpis__sort-dropdown {
  .ifp-dropdown {
    &__selected {
      padding: $spacer-2 $spacer-3;
      border: 1px solid $ifp-color-grey-7;
      border-radius: 4px;
      background-color: $ifp-color-white;
      font-size: $ifp-fs-5;
      min-height: auto;

      &:hover {
        border-color: $ifp-color-secondary-blue;
      }
    }

    &__list {
      border: 1px solid $ifp-color-grey-7;
      border-radius: 4px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

      &-item {
        padding: $spacer-2 $spacer-3;
        font-size: $ifp-fs-5;
        display: flex;
        align-items: center;
        justify-content: space-between;

        &:hover {
          background-color: $ifp-color-blue-1;
        }

        .ifp-icon {
          font-size: $ifp-fs-6;
          color: $ifp-color-grey-14;
        }
      }
    }
  }
}

// Card sizing and expansion behavior
:host ::ng-deep ifp-dxp-whats-new-card {
  margin: 0 0 34px 0 !important; // Custom 34px bottom margin for row spacing with !important
  flex: 0 0 calc(33.333% - 11px); // 3 cards per row: 33.333% minus gap (16px * 2 / 3 ≈ 11px)
  max-width: calc(33.333% - 11px);
  min-width: 0; // Remove minimum width constraint to allow 3 cards
  width: calc(33.333% - 11px); // Force the width
  min-height: 220px; // Minimum height for all cards
  height: auto; // Allow height to adjust for content
  max-height: 250px; // Maximum height for cards without action buttons
  transition: all 0.3s ease-in-out;
  box-sizing: border-box; // Include padding and border in width calculation
  position: relative; // Ensure proper positioning

  // Cards with action buttons need consistent height in both states
  &:has(.ifp-whats-new-card__actions) {
    max-height: 320px; // Same height for both collapsed and expanded states with action buttons
    min-height: 280px; // Ensure minimum height for action buttons
  }

  // Expanded state - takes up 2/3 of the row width
  &.ifp-active-card {
    flex: 0 0 calc(66.666% - 5px); // 2/3 width with gap adjustment
    max-width: calc(66.666% - 5px);
    width: calc(66.666% - 5px); // Force the width
    min-width: 0; // Remove minimum width constraint
    min-height: 220px; // Same minimum height when expanded
    height: auto; // Allow height to adjust for content
    max-height: 280px; // Slightly more height for expanded content
    z-index: 1; // Ensure expanded card appears above others

    // Cards with action buttons maintain the same height when expanded
    &:has(.ifp-whats-new-card__actions) {
      max-height: 320px; // Same as collapsed state - no height change
      min-height: 280px; // Same as collapsed state - no height change
    }
  }

  // Additional specific targeting for the card wrapper
  .ifp-whats-new-card--dxp {
    margin-bottom: 34px !important;
  }
}

// Responsive styling for filters
@media (max-width: 1200px) {
  .ifp-dxp-kpis {
    &__filters-wrapper {
      gap: $spacer-2; // Reduce gap on smaller screens
    }

    &__filter-item {
      flex-direction: column;
      align-items: flex-start;
      gap: $spacer-1;
    }

    &__entity-dropdown,
    &__creator-dropdown,
    &__sort-dropdown {
      min-width: 180px; // Slightly reduce but keep reasonable width
      width: 180px;
    }
  }
}

@media (max-width: 768px) {
  .ifp-dxp-kpis {
    &__filters-wrapper {
      flex-direction: column;
      align-items: flex-end;
      gap: $spacer-3;
    }

    &__filter-item {
      width: 100%;
      max-width: 250px;
    }

    &__entity-dropdown,
    &__creator-dropdown,
    &__sort-dropdown {
      min-width: 200px; // Maintain good width on mobile
      width: 100%;
      max-width: 250px;
    }
  }
}

// Responsive breakpoints - Only apply 2-card layout on very small screens
@media (max-width: 900px) {
  :host ::ng-deep ifp-dxp-whats-new-card {
    margin: 0 0 34px 0 !important; // Custom 34px bottom margin for row spacing with !important
    flex: 0 0 calc(50% - 8px); // 2 cards per row on smaller screens
    max-width: calc(50% - 8px);
    width: calc(50% - 8px);
    min-width: 0;
    max-height: 250px; // Default max-height

    // Cards with action buttons need consistent height in both states
    &:has(.ifp-whats-new-card__actions) {
      max-height: 320px; // Same height for both collapsed and expanded states
      min-height: 280px; // Ensure minimum height for action buttons
    }

    &.ifp-active-card {
      flex: 0 0 100%; // Full width when expanded
      max-width: 100%;
      width: 100%;
      max-height: 280px; // Default expanded height

      // Cards with action buttons maintain the same height when expanded
      &:has(.ifp-whats-new-card__actions) {
        max-height: 320px; // Same as collapsed state - no height change
        min-height: 280px; // Same as collapsed state - no height change
      }
    }
  }
}

@include mobile-tablet {
  :host ::ng-deep ifp-dxp-whats-new-card {
    margin: 0 0 34px 0 !important; // Custom 34px bottom margin for row spacing with !important
    flex: 0 0 calc((100% - #{$spacer-3}) / 2); // 2 cards per row on tablet
    max-width: calc((100% - #{$spacer-3}) / 2);
    max-height: 250px; // Default max-height

    // Cards with action buttons need consistent height in both states
    &:has(.ifp-whats-new-card__actions) {
      max-height: 320px; // Same height for both collapsed and expanded states
      min-height: 280px; // Ensure minimum height for action buttons
    }

    &.ifp-active-card {
      flex: 0 0 100%; // Full width when expanded on tablet
      max-width: 100%;
      max-height: 280px; // Default expanded height

      // Cards with action buttons maintain the same height when expanded
      &:has(.ifp-whats-new-card__actions) {
        max-height: 320px; // Same as collapsed state - no height change
        min-height: 280px; // Same as collapsed state - no height change
      }
    }
  }
}

@include mobile {
  :host ::ng-deep ifp-dxp-whats-new-card {
    flex: 0 0 100%; // 1 card per row on mobile
    max-width: 100%;
    max-height: 250px; // Default max-height

    // Cards with action buttons need consistent height in both states
    &:has(.ifp-whats-new-card__actions) {
      max-height: 320px; // Same height for both collapsed and expanded states
      min-height: 280px; // Ensure minimum height for action buttons
    }

    &.ifp-active-card {
      flex: 0 0 100%; // Same as normal on mobile
      max-width: 100%;
      max-height: 280px; // Default expanded height

      // Cards with action buttons maintain the same height when expanded
      &:has(.ifp-whats-new-card__actions) {
        max-height: 320px; // Same as collapsed state - no height change
        min-height: 280px; // Same as collapsed state - no height change
      }
    }
  }
}
