<div class="ifp-ai-insight-card " [ngClass]="{'ifp-ai-insight-card--badge': badge(),'ifp-ai-insight-card--shadow': shadow(), 'ifp-ai-insight-card--popup': popup()}">
  @if (popup()) {
    <div class="ifp-ai-insight-card__card">
      <!-- <ifp-highcharts  [width]="null" [data]="[{name: 'Volatile Economic Environment', 'data': [30.5, 31.2, 32.1, 33.0, 33.5, 34.0], 'dashStyle': 'Solid', 'type': 'line', 'zIndex': 1, 'lineWidth': 2, 'color': '#FF5733', 'marker': {'fillColor': '#FF5733', 'lineWidth': 2, 'lineColor': '#FFFFFF', 'symbol': 'circle', 'enabled': true}, 'category': ['2024-09', '2024-10', '2024-11', '2024-12', '2025-01', '2025-02'], 'yAxisLabel': 'Temperature (\u00b0C)', 'xAxisLabel': 'Time (Monthly)'}]"></ifp-highcharts> -->
      <ifp-ai-chart-card [isDatalabel]="true" [enableAnimation]="enableAnimation()" class="ifp-ai-insight-card__chart"  [insightReport]="true" [isCustom]="true" [customChartData]="customChartData()"
        ></ifp-ai-chart-card>
    </div>
  }

    <p class="ifp-ai-insight-card__title">{{title() | translate}}</p>
    <p class="ifp-ai-insight-card__desc">{{description() | translate}}</p>
    @if (!popup()) {
    <div class="ifp-ai-insight-card__card">
      <!-- <ifp-highcharts  [width]="null" [data]="[{name: 'Volatile Economic Environment', 'data': [30.5, 31.2, 32.1, 33.0, 33.5, 34.0], 'dashStyle': 'Solid', 'type': 'line', 'zIndex': 1, 'lineWidth': 2, 'color': '#FF5733', 'marker': {'fillColor': '#FF5733', 'lineWidth': 2, 'lineColor': '#FFFFFF', 'symbol': 'circle', 'enabled': true}, 'category': ['2024-09', '2024-10', '2024-11', '2024-12', '2025-01', '2025-02'], 'yAxisLabel': 'Temperature (\u00b0C)', 'xAxisLabel': 'Time (Monthly)'}]"></ifp-highcharts> -->
      <ifp-ai-chart-card  [isDatalabel]="true" [enableAnimation]="enableAnimation()" class="ifp-ai-insight-card__chart"  [insightReport]="true" [isCustom]="true" [customChartData]="customChartData()"
        ></ifp-ai-chart-card>
    </div>
  }

  @if (badge()) {
    <div class="ifp-ai-insight-card__sec-2" [ngClass]="{' ifp-ai-insight-card__sec-2--red': statusDown()}">
      <em class="ifp-ai-insight-card__icon-arrow ifp-icon ifp-icon-arrow-us-up "></em>
    </div>
  }

  @if (badgeSide()) {
    <div class="ifp-ai-insight-card__sec-3">
      <em class="ifp-ai-insight-card__icon-arrow ifp-icon ifp-icon-arrow-us-up"></em>
      60%
    </div>
  }

</div>
