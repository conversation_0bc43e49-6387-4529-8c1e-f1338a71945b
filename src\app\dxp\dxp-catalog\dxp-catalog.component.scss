@use "../../../assets/ifp-styles/abstracts/index" as *;

.ifp-dxp-catalog {
  padding: $spacer-4;
  background-color: $ifp-color-section-white;
  border-radius: 10px;
  box-shadow: 0 4px 29px $ifp-color-black-04;
  &__wrapper {
    display: flex;
    flex-wrap: wrap;
    margin: $spacer-0 (-$spacer-2);
  }
  &__card {
    width: calc(25% - (2 * $spacer-2));
    margin: $spacer-2;
  }
  &__loader {
    width: 100%;
  }
}

@include desktop-sm {
  .ifp-dxp-catalog {
    &__card {
      width: calc(33.33% - (2 * $spacer-2));
    }
  }
}

@include desktop-sm {
  .ifp-dxp-catalog {
    &__card {
      width: calc(33.33% - (2 * $spacer-2));
    }
  }
}
