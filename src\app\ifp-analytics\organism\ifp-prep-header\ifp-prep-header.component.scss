@use "../../../../assets/ifp-styles/abstracts/index" as *;

.ifp-prep-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: $spacer-3 $spacer-5;
  background-color: $ifp-color-section-white;
  .ifp-beta-icon {
    margin-inline-start: $spacer-0;
    margin-inline-end: $spacer-2;
  }
  &__title {
    font-size: $ifp-fs-6;
    font-weight: $fw-bold;
    margin-bottom: $spacer-2;
  }

  &__desc {
    color: $ifp-color-grey-9;

    .ifp-icon {
      font-size: $ifp-fs-5;
      margin-inline-end: $spacer-2;
      position: relative;
      top: 3px;
    }
  }

  &__sec-2 {
    display: flex;
    align-items: center;
    margin: $spacer-0 (
      -$spacer-3
    );
}

&__btn {
  font-size: $ifp-fs-4;
  font-weight: $fw-medium;
  padding: ($spacer-2 + 2px) $spacer-4;
  display: inline-flex;
  align-items: center;
  border-radius: 5px;
  text-transform: capitalize;
  transition: 0.3s;

  &--rnd {
    border-radius: 15px;
    // min-width: 230px;
    justify-content: space-between;
    color: $ifp-color-grey-9;
    border: 1px solid $ifp-color-grey-3;
    cursor: pointer;

    &:hover {
      background-color: $ifp-color-active-blue;
      color: $ifp-color-section-white;
    }
  }

  &--secondary {
    text-align: center;
    color: $ifp-color-active-blue;
    border: 1px solid $ifp-color-active-blue;
    cursor: pointer;

    // margin: 0;
    &:hover {
      background-color: $ifp-color-active-blue;
      color: $ifp-color-white-global;
    }
  }

  // &--disabled {
  //   min-width: 230px;
  //   justify-content: space-between;
  //   color: $ifp-color-grey-9;
  //   border: 1px solid $ifp-color-grey-3;
  // }
  &--active {
    .ifp-prep-header__btn-icon {
      color: $ifp-color-orange;
    }
  }
}

&__btn-text {
  display: flex;
  align-items: center;
}

&__btn-icon {
  font-size: $ifp-fs-4;
  margin-inline-end: $spacer-2;
  transition: 0.3s;
}

&__icon {
  font-size: $ifp-fs-7;
  cursor: pointer;
}

&__sec-item {
  margin: $spacer-0 12px;

  &--disabled {
    pointer-events: none;
    color: $ifp-color-grey-disabled;
  }
}

// &--adv {
//   justify-content: space-between;
// }

&__icon-wrapper {
  display: flex;
  align-items: center;
  border: 1px solid $ifp-color-secondary-blue;
  padding: $spacer-2 $spacer-0;
  border-radius: 5px;
  // margin-inline-end: ($spacer-1 + 2);
}

&__icon-tool {
  border-inline-end: 1px solid $ifp-color-grey-8;
  padding: $spacer-0 ($spacer-2 + 2);
  font-size: $ifp-fs-5;
  cursor: pointer;
  &::before {
    position: relative;
    top: 1px;
  }
  &:last-child {
    border-inline-end:none;
  }
}
  ::ng-deep {
    .ifp-prep-header__button button {
      text-transform: none;
      padding: ($spacer-2 + 2);
      em {
        font-size: 1.4rem;
        font-weight: 600;
      }
    }
    app-ifp-tab-button .ifp-tab-button {
      margin-right: 12px;
      width: max-content;
    }
  }
  &__sec-item-tab {
    // margin-inline-start: $spacer-3;
  }
  &__process-btn {
    display: block;
    margin-inline-start: $spacer-3;

  }
}

// :host-context([dir="rtl"]) {
//   .ifp-prep-header {
//     justify-content: left;
//     &__btn-icon {
//       margin-left: $spacer-2;
//     }
//     &__icon-wrapper {
//       margin-left: ($spacer-1 + 2);
//     }
//     &__icon-tool {
//       border-left: 1px solid;
//       padding: $spacer-0 $spacer-0 $spacer-0 ($spacer-2 + 2);
//       &:last-child {
//         border-left:none;
//       }
//     }
//   }
//   ::ng-deep {
//     app-ifp-tab-button .ifp-tab-button {
//       margin-left: 12px;
//     }
//   }
// }

@include ipad {
  .ifp-prep-header__icon-tool--hide-ipad {
    display: none;
  }
}

@include desktop-sm {
  .ifp-prep-header {
    // &__btn {
    //   padding: $spacer-2 $spacer-2;
    // }
    &__icon-toggle {
      position: relative;
    }
    &__icon-tool-modal {
      text-align: center;
      color: $ifp-color-active-blue;
      border: 1px solid $ifp-color-active-blue;
      cursor: pointer;
      font-size: 1.6rem;
      font-weight: 500;
      padding: 10px 24px;
      display: inline-flex;
      align-items: center;
      border-radius: 5px;
      transition: 0.3s;
      &-active {
        background-color: var(--ifp-color-active-blue);
        color: var(--ifp-color-white-global);
      }
    }
    &__icon-wrapper-modal {
      position: absolute;
      top: 46px;
      left: -65px;
      z-index: 8;
      background-color: $ifp-color-black-50;
      color: $ifp-color-white-global;
      &::before {
        content: '';
        position: absolute;
        bottom: 100%;
        left: 50%;
        transform: translateX(-50%);
        border-width: 10px;
        border-style: solid;
        border-color: transparent transparent $ifp-color-black-50 transparent;
      }
    }
    &__sec-item-icon em {
      margin-right: $spacer-0;
    }


    ::ng-deep {
      .ifp-db-dropdown {
        margin: $spacer-0 12px $spacer-0 $spacer-0;
        &__selected {
          border: 1px solid $ifp-color-active-blue;
          color: $ifp-color-secondary-blue;
          .ifp-icon-down-arrow {
            margin-top: $spacer-1;
          }
          .ifp-db-dropdown__selected-item {
            color: $ifp-color-secondary-blue !important;
          }
          &-icon {
            margin-left: $spacer-0 !important;
          }
        }
        &__list {
          min-width: auto;
        }
      }
    }
  }
}

:host-context([dir="rtl"]) {
  .ifp-prep-header {
    ::ng-deep {
      .ifp-db-dropdown {
        margin: $spacer-0 $spacer-0 $spacer-0 12px;
        &__selected {
          .ifp-icon-down-arrow {
            margin-left: $spacer-0;
            margin-right: $spacer-2;
          }
          &-icon {
            margin-right: $spacer-0 !important;
            margin-left: $spacer-2 !important;
          }
        }
      }
      app-ifp-tab-button .ifp-tab-button {
        margin-right: $spacer-0;
        margin-left: 12px;
      }
    }
  }
}
