@use "../../../../assets/ifp-styles/abstracts/index" as *;

.ifp-tag {
  padding: $spacer-1 $spacer-3;
  border-radius: 30px;
  font-weight: $fw-medium;
  text-transform: capitalize;
  &__wrapper {
    display: flex;
    align-items: center;
    margin-left: -$spacer-2;
    margin-right: -$spacer-2;
    gap: 0; // Explicitly set gap to 0 to eliminate any spacing
    font-size: 0; // Remove whitespace between inline elements
  }
  &__icon {
    font-weight: $fw-bold;
    color: $ifp-color-secondary-grey;
    margin-left: $spacer-2;
    margin-right: -4px; // More aggressive negative margin to pull text closer
    font-size: 16px; // Restore font size for icon
  }
  &__text {
    margin-left: -4px; // More aggressive negative margin to move closer to icon
    margin-right: $spacer-2;
    font-size: $ifp-fs-3; // Restore font size for text
  }
  &__inner {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  &--box {
    padding: $spacer-1 $spacer-2;
    border-radius: 5px;
    .ifp-tag {
      &__text {
        font-size: $ifp-fs-2;
        text-transform: uppercase;
        margin: $spacer-0;
        margin-left: -3px; // Negative margin for tight spacing in box view
      }
      &__icon {
        font-size: 14px; // Ensure icon has proper size in box view
        margin: $spacer-0; // Remove margins in box view
        margin-right: -3px; // Negative margin for tight spacing in box view
      }
      &__info {
        margin-inline-start: $spacer-2;
        position: relative;
        top: 1px;
      }
      &__wrapper {
        display: flex; // Keep flex for box view
        margin: $spacer-0;
        gap: 0; // Ensure no gap in box view
        font-size: 0; // Remove whitespace in box view
      }
    }
  }
}

