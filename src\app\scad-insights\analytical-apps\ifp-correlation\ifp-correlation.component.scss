@use "../../../../assets/ifp-styles/abstracts/index" as *;

.ifp-node__title {
  text-align: center;
  margin-bottom: $spacer-4;
}
.ifp-correlation {
  margin: $spacer-5 $spacer-0;
  .ifp-node {
    &__title {
      text-align: center;
    }
  }
  &__wrapper {
    padding: $spacer-2 $spacer-4 $spacer-4;
    border-radius: 20px;
    background-color: $ifp-color-section-white;
  }
  &__header {
    display: flex;
    align-items: center;
    padding: $spacer-3 $spacer-4;
    margin: $spacer-0 (-$spacer-4);
    border-radius: 10px 10px 0 0;
    background-color: $ifp-color-section-white;
    z-index: 1;
  }
  &__tab-sec,
  &__download-filters {
    width: 100%;
    display: flex;
    justify-content: space-between;
  }
  &__tab {
    border: 1px solid $ifp-color-grey-5;
    border-radius: 10px;
    padding: $spacer-1;
    display: flex;
  }
  &__tab-item {
    padding: ($spacer-3 - 2px) $spacer-4;
    border-radius: 7px;
    cursor: pointer;
    transition: 0.3s;
    &--active {
      background-color: $ifp-color-active-blue;
      color: $ifp-color-white-global;
      pointer-events: none;
    }
  }
  &__card-wrapper {
    display: flex;
    flex-wrap: wrap;
    margin: $spacer-0 (-$spacer-3);
  }
  &__card {
    width: calc(50% - (2 * $spacer-3));
    margin: $spacer-3;
    &--full {
      width: calc(100% - (2 * $spacer-3));
    }
  }
  &__no-data {
    width: 100%;
  }
  &__download {
    opacity: 0;
    transition: 0.3s;
    margin-top: $spacer-2;
    position: absolute;
    top: 100%;
    right: 0;
    z-index: 1;
    &--active {
      opacity: 1;
    }
  }
  &__download-wrapper {
    position: relative;
    margin-inline-start: $spacer-4;
    .ifp-correlation {
      &__tab {
        display: block;
      }
      &__tab-item {
        display: inline-block;
      }
    }
  }
  &__tag {
    display: inline-block;
  }
  &__download-filters {
    margin: $spacer-6 $spacer-0 $spacer-2;
  }
  &__download-filter-title {
    font-weight: $fw-bold;
    text-transform: uppercase;
  }
}

:host::ng-deep {
  .ifp-correlation {
    &__download {
      .ifp-chart-toolbar {
        &__action-box {
          border: 0 !important;
          padding: $spacer-3 !important;
          background-color: $ifp-color-grey-4;
          border-radius: 7px;
          box-shadow: 0 0 8px $ifp-color-black-16;
          min-width: 320px;
        }
      }
    }
  }
}

:host-context(.ifp-header__fixed) {
  .ifp-correlation {
    &__header {
      position: sticky;
      top: $ifp-header-height-sticky;
      left: 0;
      @include slide-down-below-header();
      box-shadow: 0 4px 16px $ifp-color-black-16;
    }
  }
}

:host-context([dir="rtl"]) {
  .ifp-correlation {
    &__download {
      right: auto;
      left: 0;
    }
  }
}

@include desktop-sm {
  .ifp-correlation {
    &__card-wrapper {
      margin: $spacer-0;
      display: block;
    }
    &__card {
      width: auto;
      margin: $spacer-4 $spacer-0;
    }
  }
}
