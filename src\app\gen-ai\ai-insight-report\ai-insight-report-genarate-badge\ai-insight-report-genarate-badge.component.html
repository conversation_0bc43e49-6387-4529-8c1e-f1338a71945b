@if(enableCardView()) {
  <div class="ifp-ai-insight-report-genarate__ai-icon-wrapper"  >
      <img (click)="enableBadge.set(!enableBadge())" class="ifp-ai-insight-report-genarate__ai-icon" [zIndex]="1203"  [appIfpTooltip]="'Regenerate'" src="../../../../assets/ai-genarate-icon.svg" />
    <!-- <em class="ifp-icon ifp-icon-ai ifp-ai-insight-report-genarate__ai-icon"></em> -->
  </div>
}

@if (enableBadge() || !enableCardView()) {
  <div class="ifp-ai-insight-report-genarate__badge " [ngClass]="{'ifp-ai-insight-report-genarate__badge--card': enableCardView(), 'ifp-ai-insight-report-genarate__animation':animation(), 'ifp-ai-insight-report-genarate--disable': disable()}">
    <ifp-ai-input (submitInput)="genarateValue()" [formControl]="formControl" class="ifp-ai-insight-report-genarate__input" [theme]="'ifp-ai-input--ai--30 ifp-ai-input--ai--bordered ifp-ai-input--ai'" [animation]="animation()"></ifp-ai-input>
    <ifp-ai-button [disabled]="formControl.value?.trim() === ''" [disableTranslate]="disableTranslate()" class="ifp-ai-insight-report-genarate__btn" [label]="'Generate All'" [loader]="animation()"  [iconClass]="'ifp-icon-ai'" (ifpClick)="genarateValue()"  [theme]="'ifp-ai-button--ai-secondary'" ></ifp-ai-button>
    <!-- <div class="ifp-ai-insight-report-genarate__regenrate"><em class="ifp-icon ifp-icon-refresh"></em> {{'Regenerate' | translate}}</div> -->
  </div>
}

