import { ChangeDetectorRef, Component, EventEmitter, inject, Input, OnChanges, OnDestroy, OnInit, Output, signal, SimpleChanges, WritableSignal } from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';
import { IfpDropdownComponent } from '../../../scad-insights/ifp-widgets/ifp-atoms/ifp-dropdown/ifp-dropdown.component';
import { ColumnList, FilterOpts } from '../../data-prep/ifp-advance-prep/interface/ifp-advance-prep.interface';
import { IfpCheckBoxComponent } from '../../../scad-insights/ifp-widgets/ifp-atoms/ifp-check-box/ifp-check-box.component';
import { IfpButtonComponent } from '../../../scad-insights/ifp-widgets/ifp-atoms/ifp-button/ifp-button.component';
import { buttonClass } from 'src/app/scad-insights/core/constants/button.constants';
import { ifpColors } from 'src/app/scad-insights/core/constants/color.constants';
import { FormArray, FormBuilder, FormControl, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { ColumnPrep } from '../../data-prep/ifp-data-prep/ifp-prep-select-data/interface/prep-selection.interface';
import { NgClass } from '@angular/common';
import { selectNodeStoreAdvance } from '../../data-prep/ifp-advance-prep/store/advance-node-store/advance-node.store';
import { connectionType } from '../../data-prep/ifp-data-prep/constants/if-preps.constants';
import { IfpAdvancePrepService } from '../../data-prep/ifp-advance-prep/service/ifp-advance-prep.service';
import { ToasterService } from 'src/app/scad-insights/core/services/tooster/ToastrService.service';
import { IfpSpinnerComponent } from '../../../scad-insights/ifp-widgets/ifp-molecules/ifp-spinner/ifp-spinner.component';
import { IfpDataToolAccordianComponent } from '../../molecule/ifp-data-tool-accordian/ifp-data-tool-accordian.component';


@Component({
    selector: 'ifp-adv-filter-tool',
    templateUrl: './ifp-adv-filter-tool.component.html',
    styleUrl: './ifp-adv-filter-tool.component.scss',
    imports: [TranslateModule, IfpDropdownComponent, IfpCheckBoxComponent, IfpButtonComponent,
        FormsModule, ReactiveFormsModule, NgClass, IfpSpinnerComponent, IfpDataToolAccordianComponent]
})
export class IfpAdvFilterToolComponent implements OnInit, OnChanges, OnDestroy {


  @Input() columnList!: ColumnPrep[];
  @Input() comparatorList: Comparator[] = [];
  @Input({ required: true }) currentConnectionCount: number = 0;
  @Input() currentNodeId?: string = '';
  @Input() sourceData: WritableSignal<boolean> = signal(false);
  @Input() workFlowRunning = signal(false);
  @Input() emptyConnection = signal(true);
  @Input() currentConection: WritableSignal<boolean> = signal(false);
  @Input() errorMessageForTools?: string;

  @Output() cancel = new EventEmitter();
  @Output() save = new EventEmitter();

  public buttonClass = buttonClass;
  public matchPattern: boolean = true;
  public ignoreCase: boolean = true;
  public filterList: Filter[] = [];
  public color = ifpColors;
  public value: string = '';
  public selectedColumn!: ColumnList;
  public selectedComparator!: Comparator;
  public filterForm!: FormGroup;
  public isEditFilter: boolean = false;
  public editFilterIndex: number | null = null;
  private filterItems!: FormArray;
  public compareOpts: FilterOpts[] = [];
  public submitted: WritableSignal<boolean> = signal(false);
  public readonly advanceStore = inject(selectNodeStoreAdvance);
  public isConfigUpdated: WritableSignal<boolean> = signal(false);
  public savedConfig: WritableSignal<boolean> = signal(true);
  public inputType:WritableSignal<string> = signal('text');

  constructor(private _fb: FormBuilder, private _prepService: IfpAdvancePrepService, private _toaster: ToasterService,
    private _cdr: ChangeDetectorRef
  ) {
    // const subsValue = this._prepService.processStatus.subscribe({
    //   next: data => {
    //     if (data?.workflow_status === workFlowState.completed) {
    //       this.resetForm();
    //       subsValue?.unsubscribe();
    //     }
    //   }
    // });
  }


  ngOnChanges(_changes: SimpleChanges): void {
    if (this.columnList?.length !== 0) {
      this.filterList = [];
      this.patchStoreData();
    }
  }


  ngOnInit(): void {
    this.filterForm = this._fb.group({
      column: ['', Validators.required],
      comparator: ['', Validators.required],
      value: ['', Validators.required],
      case: new FormControl(false)
    });
  }



  selectColumn(event: ColumnList | any) {
    this.selectedColumn = event;
    this.inputType.set(this.selectedColumn.type == 'string' ? 'text' : 'number');
    if (this.selectedColumn.filter_options) {
      this.compareOpts = this.selectedColumn.filter_options;
      this.selectedComparator = this.selectedColumn.filter_options[0];
      this.filterForm.controls['comparator'].setValue(this.selectedComparator);
    }
  }

  selectComparator(event: Comparator | any) {
    this.selectedComparator = event;
  }

  addFilter() {
    if (this.filterForm.invalid) {
      this.submitted.set(true);
      return;
    }
    this.submitted.set(false);
    this.editFilterIndex != null && this.isEditFilter ? this.filterList[this.editFilterIndex] = this.filterForm.value : this.filterList.push(this.filterForm.value);
    this.resetForm();
  }


  removeFilter(index: number) {
    this.filterList.splice(index, 1);
  }

  onCancel() {
    this.cancel.emit();
  }

  onSave() {
    const config = {
      columns: this.getFilterOpts()
    };
    this.savedConfig.set(true);
    this.advanceStore.updateNode(this.currentNodeId ?? '', 'Filter Tool updated', connectionType.filterTool, config, 'Filter Tool updated', { configUpdated: true });
    this._prepService.nodeChangeDetect.next('node updated');
    // this._toaster.success('Data Saved Successfully');
    this.isConfigUpdated.set(false);
    this.save.emit();
  }

  getFilterOpts() {
    const selectedFilters: SelectedFilter[] = [];
    if (this.filterList?.length) {
      this.filterList.forEach(element => {
        const data: SelectedFilter = {
          column_name: element.column.name,
          operator: element.comparator.operator,
          value: element.value,
          case_insensitive: element.case
        };
        selectedFilters.push(data);
      });
    }
    return selectedFilters;
  }


  // ** form functions //
  get f() {
    return this.filterForm.controls;
  }

  updateFormData(filterData: Filter) {
    this.filterForm.patchValue({
      column: filterData.column,
      comparator: filterData.comparator,
      value: filterData.value,
      case: filterData.case
    });
  }

  resetForm() {
    this.filterForm.reset({
      column: '',
      comparator: '',
      value: '',
      case: false
    });
    this._cdr.detectChanges();
    this.isEditFilter = false;
    this.editFilterIndex = null;
  }

  patchStoreData() {
    this.filterList = [];
    const nodeData = this.advanceStore.selectNodeValue().nodeValue(this.currentNodeId ?? '');

    const columns = nodeData?.configuration?.columns ?? [];
    if (columns?.length > 0) {
      columns?.forEach((element: { column_name: string; operator: string; value: any; case_insensitive: any; }) => {
        const columnDataIndex: number = this.columnList?.findIndex(x => x.name === element.column_name);
        if (columnDataIndex >= 0) {
          const filterOption = this.columnList[columnDataIndex].filter_options?.find((x: { operator: string; }) => x.operator === element.operator);
          if (filterOption) {
            this.filterList.push({
              column: this.columnList[columnDataIndex],
              comparator: filterOption,
              value: element.value,
              case: element.case_insensitive
            });
          }
        }
      });
    }
  }

  updateFiter(filter: Filter, index: number) {
    this.isEditFilter = true;
    this.editFilterIndex = index;
    this.compareOpts = filter?.column?.filter_options;
    this.inputType.set(filter.column.type == 'string' ? 'text' : 'number');
    this.filterForm.setValue({
      column: filter.column,
      comparator: filter.comparator,
      value: filter.value,
      case: filter.case
    });
    this._cdr.detectChanges();
  }




  ngOnDestroy(): void {
    if (!this._prepService.isConfigSave() && this.columnList.length !== 0) {
      this.onSave();
    }
  }
}





interface Filter {
  column: Column;
  comparator: Comparator;
  value: string;
  case?: boolean;
}
export interface Comparator {
  case_sensitive_flag: boolean;
  label: string;
  operator: string;
}

interface Column {
  dataType?: string;
  id?: number;
  isSelected?: boolean;
  name: string,
  filter_options?: any
  type?:string;
}

interface SelectedFilter {
  column_name: string;
  operator: string;
  value: string | number;
  case_insensitive?: boolean;
}
