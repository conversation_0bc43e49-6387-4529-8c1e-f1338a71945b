@use "../../../../assets/ifp-styles/abstracts/index" as *;

.ifp-products {
  padding: $spacer-5 $spacer-0;
  &__desc,
  &__desc-short {
    line-height: 1.6;
  }

  &__desc {
    font-weight: $fw-medium;
    margin-bottom: $spacer-3;
  }

  &__desc-short {
    color: $ifp-color-tertiary-text;
  }

  &__domain-outer {
    margin-top: $spacer-5;
  }

  &__heading {
    font-weight: $fw-bold;
    font-size: 2.4rem;
    &--main{
      font-size: 2.6rem;
      margin-bottom: $spacer-3;
    }
    .ifp-icon {
      margin-left: $spacer-2;
      color: $ifp-color-blue-hover;
      font-size: 80%;
      position: relative;
      top: 2px;
    }
  }

  &__domain-wrapper {
    display: flex;
    flex-wrap: wrap;
    margin: $spacer-0 (-$spacer-2);
  }

  &__domain {
    background-color: $ifp-color-section-white;
    display: flex;
    align-items: center;
    border: 1px solid $ifp-color-grey-7;
    border-radius: 10px;
    padding: $spacer-2 $spacer-3;
    margin: $spacer-1 $spacer-2;
    white-space: nowrap;
    // width: calc(16.66% - 16px);

  }
  &__domain-name {
    text-transform: uppercase;
    font-weight: $fw-medium;
  }
  &__domain-icon {
    margin-right: $spacer-2;
    height: 20px;
    width: 20px;
  }
  &__wrapper {
    display: flex;
    flex-wrap: wrap;
    margin: $spacer-0 (-$spacer-2);
  }

  &__item {
    background-color: $ifp-color-section-white;
    border: 1px solid $ifp-color-grey-7;
    border-radius: 10px;
    padding: $spacer-3 $spacer-4;
    margin: $spacer-2;
    width: calc(33.33% - 16px);
    .ifp-products {
      &__heading {
        margin-bottom: $spacer-3;
      }
      &__desc {
        margin-bottom: $spacer-3;
      }
    }
  }
  &__img {
    display: block;
    width: 100%;
    &--expand-right {
      animation: expand-right 1.25s ease-in-out forwards;
    }
    &--to-right {
      animation: slide-right 1.25s ease-in-out forwards;
    }
    &--to-top {
      animation: slide-top 1.25s ease-in-out forwards;
    }
  }
  &__carousel {
    max-width: 80%;
    margin: $spacer-6 auto;
  }
  &__carousel-sec {
    display: flex;
    align-items: center;
    .ifp-products__heading {
      margin-bottom: $spacer-4;
    }
  }
  &__sec-1 {
    width: 240px;
    min-width: 240px;
    height: 240px;
    margin-right: -120px;
    background-color: $ifp-color-beige;
    position: relative;
    z-index: 1;
    border-radius: 10px;
  }
  &__carousel-img {
    width: 100%;
    position: relative;
  }
  &__sec-2 {
    padding: $spacer-5 $spacer-5 $spacer-5 140px;
    background-color: $ifp-color-section-white;
    border: 1px solid $ifp-color-grey-7;
    border-radius: 10px;
    min-height: 340px;
    display: flex;
    align-items: center;
    .nav-tabs {
      border-bottom: 0;
      margin: $spacer-0 (-$spacer-3) $spacer-3;
    }
  }
  &__tab-item {
    font-weight: $fw-bold;
    color: $ifp-color-primary-grey;
    border: 0;
    padding: $spacer-0 $spacer-0 $spacer-1 $spacer-0;
    margin: $spacer-0 $spacer-3;
    border-bottom: 3px solid transparent;
    &:hover,
    &.active {
      color: $ifp-color-link;
    }
    &:hover {
      border-bottom: 3px solid transparent;
    }
    &.active {
      background-color: transparent;
      border-bottom: 3px solid $ifp-color-link;
    }
  }
  &__img-wrapper {
    display: flex;
    align-items: flex-end;
    width: 100%;
    height: 123px;
    overflow: hidden;
    background-size: contain;
    background-repeat: no-repeat;
    background-position: left bottom;
    margin-bottom: $spacer-3;
  }
}

:host::ng-deep {
  ifp-img {
    display: block;
    .ifp-domain-icon__img {
      width: 100%;
    }
  }
}

:host-context([dir="rtl"]) {
  .ifp-products {
    &__heading {
      .ifp-icon {
        margin-right: $spacer-2;
        margin-left: $spacer-0;
      }
    }
    &__domain-icon {
      margin-left: $spacer-2;
      margin-right: $spacer-0;
    }
    &__sec-1 {
      margin-left: -120px;
      margin-right: $spacer-0;
    }
    &__sec-2 {
      padding: $spacer-5 140px $spacer-5 $spacer-5;
    }
  }
}

@include mobile-tablet{
  .ifp-products {
    &__carousel {
      max-width: 100%;
      margin: $spacer-4 $spacer-0;
    }
    &__carousel-sec {
      display: block;
      margin-top: $spacer-4;
    }
    &__sec-1 {
      margin: $spacer-0 auto -120px;
    }
    &__sec-2 {
      padding: 140px $spacer-3 $spacer-3 $spacer-3;
      display: block;
      .nav-tabs {
        flex-wrap: nowrap;
        overflow: auto;
        padding-bottom: $spacer-2;
      }
    }
    &__tab-item {
      white-space: nowrap;
    }
    &__item {
      width: calc(100% - 16px);
      padding: $spacer-3;
    }
  }
}

@keyframes expand-right {
  0% {
    transform: scaleX(0);
    transform-origin: left;
  }
  100% {
    transform: scaleX(1);
    transform-origin: left;
  }
}

@keyframes slide-right {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(0);
  }
}

@keyframes slide-top {
  0% {
    transform: translateY(100%);
  }
  100% {
    transform: translateY(0);
  }
}
