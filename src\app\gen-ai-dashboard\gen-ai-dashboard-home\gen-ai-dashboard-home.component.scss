
@use "../../../assets/ifp-styles/abstracts/index" as *;
:host {
  display: block;
  position: relative;
  min-height: calc(100vh - 245px);
}
.ifp-gen-ai-dashboard-home {
  position: relative;
  z-index: 1;
  padding-bottom: $spacer-5;
  &__bg {
    position: absolute;
    width: 100%;
    height: 100%;
    object-position: top;
    top: 0;
    left: 0;
    object-fit: cover;
  }
  &__heading{
    font-size: $ifp-fs-14;
    font-weight: $fw-bold;
    margin: $spacer-5 $spacer-0 $spacer-3;
  }
  &__sub-heading {
    font-size: $ifp-fs-4;
    color: $ifp-color-grey-9;
    text-align: center;
  }
  &__gen-btn {
    margin: $spacer-4 $spacer-0;
    display: block;
  }
&__history {
  position: absolute;
  right: 0;
  top:0;
  color: $ifp-color-blue-hover;
  display: flex;
  align-items: center;
  cursor: pointer;
  .ifp-icon {
    position: relative;
    top: 2px;
    margin-inline-start: $spacer-2;
  }
}
&__history-list {
  display: block;
  position: absolute;
  right: 0;
  top: -30px;
  z-index: 99;
  overflow-y: auto;
  overflow-x: hidden;
  bottom: 0;
  @include ifp-scroll-y($ifp-color-dropdown-select, $ifp-color-grey-1, 8px, 8px);
}
   &__wrapper{
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    position: relative;
   }
   &__head {
    text-align: center;
    width: 100%;
    position: relative;
   }
   &__upload  {
    margin-top: $spacer-5;
   }
   &__tab {
    margin-top: $spacer-5;
   }
   &__preview {

    margin-top: $spacer-5;
   }

}

:host::ng-deep {
  .ifp-gen-ai-dashboard-home {
    &__tab {
      .ifp-tab__item {
        min-width: 230px;
        border: 1px solid $ifp-color-blue-menu  !important;
        .ifp-category-label__inner,.ifp-category-label__txt, .ifp-category-label__icon{
          color:$ifp-color-blue-menu !important;
        }
        &--active {
          background-color: $ifp-color-blue-menu;
          .ifp-category-label__inner,.ifp-category-label__txt , .ifp-category-label__icon{
            color:$ifp-color-white-global !important;
          }
        }


      }
    }
  }

}
.ifp-container {
  &--sm {
    width: 85%;
  }
}
:host-context([dir="rtl"]) {
  .ifp-gen-ai-dashboard-home{
    &__history {
    right: auto;
    left: 0;
  }
  &__history-list {
    left: 0;
    right: auto;
  }
}
}
