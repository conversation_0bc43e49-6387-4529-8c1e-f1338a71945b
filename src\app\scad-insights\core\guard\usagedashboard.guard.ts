

import { Injectable } from '@angular/core';
import { UsageDashboardStatusService } from '../services/usage-dashboard-log/usage-dashboard-status.service';


@Injectable({
  providedIn: 'root'
})
export class UsageDashboardGuardService {

  constructor( private _usageService:UsageDashboardStatusService) {
  }

  resolve() {
    if (!this._usageService.usageDashboardStatus() ){
      return this._usageService.usageDashboardStatusCheck();
    }
    return this._usageService.usageDashboardStatus();

  }


}
