import { DatePipe } from '@angular/common';
import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
  name: 'formatDate',
  standalone: true
})
export class IfpFormatDatePipe implements PipeTransform {

  constructor(private datePipe: DatePipe) { }

  transform(date: any, args?: string): any {
    let formattedDate = date;
    if (args === 'mediumDate') {
      formattedDate = `${this.datePipe.transform(date, 'mediumDate')}`;
    }
    return formattedDate;
  }

}
