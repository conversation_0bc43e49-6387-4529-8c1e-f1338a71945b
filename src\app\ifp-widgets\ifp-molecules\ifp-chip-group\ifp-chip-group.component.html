<div class="ifp-chip-group" [ngClass]="{'ifp-chip-group--arrow': arrow()}">
  <div class="ifp-chip-group__list">
    @for (item of group()| slice:0:limit() ;track $index) {
      <p class="ifp-chip-group__items" [appIfpTooltip]="item "  ><span class="ifp-chip-group__item-text">{{item}}</span></p>

      }
      @if (group().length > limit()) {
        <span class="ifp-chip-group__items-count" (click)="arrowClicked.emit(allData())">+{{group().length - limit()}}</span>
      }

  </div>

  @if(arrow()) {
    <em  (click)="arrowClicked.emit(allData())" class="ifp-icon ifp-icon-right-arrow ifp-chip-group__arrow"></em>
  }

</div>
