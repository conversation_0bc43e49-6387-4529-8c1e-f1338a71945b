<img
  [src]="(_themeService.defaultTheme$ | async)!=='dark' ? '../../../../assets/images/dashboard-builder/landing-bg.png' : '../../../../assets/images/prep-help/landing_page_dark.svg'"
  alt="" class="ifp-gen-ai-dashboard-home__bg">
<div class="ifp-gen-ai-dashboard-home   ifp-container">
  <div class="ifp-gen-ai-dashboard-home__head">
    <h2 class="ifp-gen-ai-dashboard-home__heading">
      {{'Bayaan AI Testing Pane'| translate}}
    </h2>
    @if (!historyTable()) {
    <div class="ifp-gen-ai-dashboard-home__history" (click)="openHistory()">
      {{'Show Evaluation History' | translate}}
      <em class="ifp-icon ifp-icon-clock"></em>
    </div>
    }

  </div>
  <div class="ifp-gen-ai-dashboard-home__wrapper ifp-container  ifp-container--sm">

    <p class="ifp-gen-ai-dashboard-home__sub-heading">
      {{"This interface is designed to evaluate user experience. Assess how the platform provides AI-driven answers by
      entering queries and reviewing the real-time responses. Use the feedback and rating features to assess the
      system's performance and ensure a finer experience for the user." | translate}}
    </p>

    @if(!this.enablePreview()) {
      @if(!dataRunEvent()) {
        @if(qus()) {
          <app-ifp-tab [tabData]="viewsTab" [isSmall]="true" [showIcon]="true" class="ifp-gen-ai-dashboard-home__tab"
            [tooltipDisabled]="true" [selectedTab]="selectedTab()" (selectedTabEvent)="tabClick($event)"></app-ifp-tab>
          @if ( selectedTab() === 0) {

          <ifp-gen-ai-dashboard-upload (runId)="runid.set($event)" (fileRemoved)="fileRemoved()"
            class="ifp-gen-ai-dashboard-home__upload"></ifp-gen-ai-dashboard-upload>
          }

          } @else{
          <ifp-button (ifpClick)="qus.set(!qus())" class="ifp-gen-ai-dashboard-home__gen-btn"
            [buttonClass]="buttonClass.hoverBlue + ' ' +buttonClass.normalAplabetic " [label]="'Evaluate Bayaan AI Response'"
            [iconClass]=" 'ifp-icon-rightarrow'"></ifp-button>
          }
      }

    }

  </div>
      @if(qus() || enablePreview()) {
         @if (modelName() !== '') {
      <ifp-gen-ai-dashboard-preview [modalDropdown]="modalDropdown()" [modelName]="modelName()"  #preview (goBackEvent)="goBackEventHistory()" [runId]="runid()" [preview]="enablePreview()" (dataRunEvent)="dataRunEvent.set(true)"
        [heading]="selectedTab() === 1 ? 'Create your Questionnaire' : 'Preview'"
        [responseOfQuestionEnable]="selectedTab() === 1"
        class="ifp-gen-ai-dashboard-home__preview ifp-container"></ifp-gen-ai-dashboard-preview>
         }
      } @else {
        @if (modelName() !== '') {
          <ifp-gen-ai-dashboard-landing-page  [modalDropdown]="modalDropdown()" [modelName]="modelName()"></ifp-gen-ai-dashboard-landing-page>

        } @else {
          <div class="ifp-center-loader">
            <app-ifp-spinner></app-ifp-spinner>
          </div>
        }
                 }

      </div>

      @if (historyTable()) {
        <ifp-gen-ai-dashboard-history class="ifp-gen-ai-dashboard-home__history-list" (prevewDetails)=" openPreivew($event)" (back)="closeHistory()"></ifp-gen-ai-dashboard-history>
      }
