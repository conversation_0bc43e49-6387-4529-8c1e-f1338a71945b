import { <PERSON>son<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>ty<PERSON>, TitleCasePipe } from '@angular/common';
import { Component, ElementRef, EventEmitter, HostListener, input, Input, model, OnChanges, OnInit, Output, signal, ViewChild, WritableSignal } from '@angular/core';
import { FormControl, ReactiveFormsModule } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';
import { cloneDeep } from 'lodash';
import { IfpTooltipDirective } from 'src/app/scad-insights/core/directives/ifp-tooltip.directive';
import { IfpCheckBoxComponent } from 'src/app/scad-insights/ifp-widgets/ifp-atoms/ifp-check-box/ifp-check-box.component';
import { IfpNoDataComponent } from "../../../scad-insights/ifp-widgets/ifp-molecules/ifp-no-data/ifp-no-data.component";
import { IfpUserTagComponent } from "../../../scad-insights/ifp-widgets/ifp-atoms/ifp-user-tag/ifp-user-tag.component";

@Component({
  selector: 'ifp-panel-dropdown',
  imports: [IfpCheckBoxComponent, TranslateModule, NgClass, NgStyle, IfpTooltipDirective, ReactiveFormsModule, IfpNoDataComponent, TitleCasePipe, IfpUserTagComponent],
  templateUrl: './ifp-panel-dropdown.component.html',
  styleUrl: './ifp-panel-dropdown.component.scss'
})
export class IfpPanelDropdownComponent implements OnInit, OnChanges {
  @ViewChild('drop') drop!: ElementRef;

  @Output() multiSelected = new EventEmitter<any>();
  @Output() selected = new EventEmitter<any>();
  @Output() searchChanged = new EventEmitter<string>();

  @Input() icon!: string;
  @Input() options: PanelDropdownOptions[] = [];
  @Input() multiSelect: boolean = true;
  @Input() key!: 'key' | 'value';
  @Input() iconSize = '30px';
  @Input() selectedValue = '';
  @Input() tooltipText = '';
  @Input() label!: string;;
  @Input() multipleSelectedItems: any[] = [];
  public isBoxType = input<boolean>(false);
  public enableSearch = input(false);
  public textValue = input<string>();
  public enableSelectAll = input(false);
  public selectAll = model(false);
  public changeCheckedDataValue = input(false);
  public userDropdown = input(false);

  public showDropdown: boolean = false;
  public search = new FormControl();
  public searchOptions: WritableSignal<PanelDropdownOptions[]> = signal([]);
  ngOnInit() {
    this.multiSelectionCheck();
  }

  ngOnChanges(): void {
    this.multiSelectionCheck();
  }

 multiSelectionCheck() {
  // Only reset multipleSelectedItems if it's not provided as input
  if (!this.multipleSelectedItems || this.multipleSelectedItems.length === 0) {
    this.multipleSelectedItems = [];
    this.options.forEach((item: PanelDropdownOptions) => {
      if (item.checked) {
        this.multipleSelectedItems.push(item);
      }
    });
  } else {
    // If multipleSelectedItems is provided as input, sync the checked state
    this.options.forEach((item: PanelDropdownOptions) => {
      item.checked = this.multipleSelectedItems.some(selected =>
        selected.key === item.key || selected.id === item.id
      );
    });
  }
  // this.search.setValue('');
  this.searchOptions.set(cloneDeep(this.options));

  // Set selectAll state based on current searchOptions
  const currentSearchOptions = this.searchOptions();
  const selectedInSearch = currentSearchOptions.filter(option => option.checked);
  this.selectAll.set(selectedInSearch.length === currentSearchOptions.length && currentSearchOptions.length > 0);
}

  onSearch() {
    const searchResult = this.search.value.trim();
    this.searchChanged.emit(searchResult);
    const searchArray = this.options.filter((prod) => {
      return ((this.key ? prod[this.key] : prod) as string).toLowerCase().includes(searchResult.toLowerCase());
    });
    this.searchOptions.set(searchArray);

    // Update selectAll state based on new search results
    const selectedInSearch = searchArray.filter(option => option.checked);
    this.selectAll.set(selectedInSearch.length === searchArray.length && searchArray.length > 0);
  }

  @HostListener('window:pointerup', ['$event.target'])
  onOutsideClick(target: HTMLElement) {
    if (this.drop) {
      if (!this.drop.nativeElement.contains(target)) {
        this.showDropdown = false;
        // this.search.setValue('');
        // this.searchOptions.set([]);
        // this.searchChanged.emit('');
      }
    }
  }

  selectAllValues() {
    this.selectAll.set(!this.selectAll());

    if (this.selectAll()) {
      // When selecting all, work with searchOptions (visible/filtered items)
      const currentSearchOptions = this.searchOptions();
      currentSearchOptions.forEach(data => {
        data.checked = true;
        // Add to multipleSelectedItems if not already present
        if (!this.multipleSelectedItems.find(item => item.key === data.key || item.id === data.id)) {
          this.multipleSelectedItems.push(data);
        }
      });
    } else {
      // When deselecting all, work with searchOptions (visible/filtered items)
      const currentSearchOptions = this.searchOptions();
      currentSearchOptions.forEach(data => {
        data.checked = false;
        // Remove from multipleSelectedItems
        const index = this.multipleSelectedItems.findIndex(item =>
          item.key === data.key || item.id === data.id
        );
        if (index >= 0) {
          this.multipleSelectedItems.splice(index, 1);
        }
      });
    }

    // Update searchOptions to reflect the changes
    this.searchOptions.set([...this.searchOptions()]);
    this.multiSelected.emit(this.multipleSelectedItems);
  }


  setChecked(event: boolean, item: any) {
    // Remove the restriction that prevents deselecting when only 1 item is selected
    // This allows users to deselect all items if needed
    item.checked = event;
    if (event) {
      // Add item if not already present
      if (!this.multipleSelectedItems.find(selected => selected.key === item.key || selected.id === item.id)) {
        this.multipleSelectedItems.unshift(item);
      }
    } else {
      const index = this.multipleSelectedItems.findIndex((x: PanelDropdownOptions) =>
        x.key === item.key || x.id === item.id
      );
      if (index >= 0) {
        this.multipleSelectedItems.splice(index, 1);
      }
    }

    // Update selectAll state based on current searchOptions
    const currentSearchOptions = this.searchOptions();
    const selectedInSearch = currentSearchOptions.filter(option => option.checked);
    this.selectAll.set(selectedInSearch.length === currentSearchOptions.length && currentSearchOptions.length > 0);

    this.multiSelected.emit(this.multipleSelectedItems);
  }

  getMutiselectedItem(item: Record<string, any>) {
    let isSelected: boolean = false;
    if (this.multipleSelectedItems?.find((x: Record<string, any>) => x[this.key] == item[this.key])) {
      isSelected = true;
    }
    return isSelected;
  }

  setSingleSelection(item: PanelDropdownOptions) {
    if (item.key === this.selectedValue) {
      this.selected.emit(undefined);
      this.selectedValue = '';
    } else {
      this.selected.emit(item);
      this.selectedValue = item.key;
    }


  }

  checkDisable() {
    // Allow all items to be deselected - remove the restriction
    // This enables proper deselect functionality
    return false;
  }


}

export interface PanelDropdownOptions {
  key: string;
  value: string;
  id?: string;
  shortValue?: string;
  checked: boolean;
}
