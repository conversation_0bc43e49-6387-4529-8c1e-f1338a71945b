import { Directive, ElementRef, Renderer2, HostListener } from '@angular/core';

@Directive({
  selector: '[appCloneDraggable]',
  standalone: true
})
export class CloneDraggableDirective {
  private isDragging = false;

  constructor(private el: ElementRef, private renderer: Renderer2) {}

  @HostListener('mousedown', ['$event'])
  onMouseDown(event: MouseEvent): void {
    event.preventDefault();
    this.isDragging = true;

    // Clone the element
    const clone = this.el.nativeElement.cloneNode(true);
    this.renderer.setStyle(clone, 'position', 'absolute');
    this.renderer.setStyle(clone, 'opacity', '0.5');
    this.renderer.setStyle(clone, 'z-index', '9999999');
    document.body.appendChild(clone);

    // Set up event listeners for dragging
    const moveListener = this.renderer.listen('document', 'mousemove', (e: MouseEvent) => {
      if (this.isDragging) {
        this.renderer.setStyle(clone, 'top', `${e.clientY}px`);
        this.renderer.setStyle(clone, 'left', `${e.clientX}px`);
      }
    });

    const upListener = this.renderer.listen('document', 'mouseup', () => {
      this.isDragging = false;
      moveListener();
      upListener();
      document.body.removeChild(clone);
    });
  }
}
