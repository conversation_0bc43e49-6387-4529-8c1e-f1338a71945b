import { Inject, Injectable, Renderer2, RendererFactory2 } from '@angular/core';
import { ActiveToast, ToastrService } from 'ngx-toastr';
import { TranslateService } from '@ngx-translate/core';
import { DOCUMENT } from '@angular/common';
@Injectable({
  providedIn: 'root'
})
export class ToasterService {

  public timeOut: number = 7000;
  public toastClass: string = 'ifp-toaster';

  private render: Renderer2;

  constructor(
    private _toaster: ToastrService, private _translate: TranslateService, @Inject(DOCUMENT) private document: Document, rendererFactory: RendererFactory2
  ) {
    this.render = rendererFactory.createRenderer(null, null);
  }

  success(text: string = '', timeOut: number = this.timeOut) {
    const toasterElem = this._toaster.success(this._translate.instant(text ?? ''), '', {
      timeOut: timeOut,
      positionClass: 'toast-bottom-right',
      closeButton: true,
      toastClass: this.toastClass
    });
    if (document.fullscreenElement) {
      this.appendToFullScreen(toasterElem);
    }
  }

  error(text: string = '', timeOut: number = this.timeOut, disableTimeout: boolean = false) {
    const toasterElem = this._toaster.error(this._translate.instant(text ?? ''), '', {
      timeOut: disableTimeout ? 0 : timeOut,
      disableTimeOut: disableTimeout,
      positionClass: 'toast-bottom-right',
      closeButton: true,
      toastClass: this.toastClass
    });
    if (document.fullscreenElement) {
      this.appendToFullScreen(toasterElem);
    }
  }

  warning(text: string = '', timeOut: number = this.timeOut) {
    const toasterElem = this._toaster.warning(this._translate.instant(text ?? ''), '', {
      timeOut: timeOut,
      positionClass: 'toast-bottom-right',
      closeButton: true,
      toastClass: this.toastClass
    });
    if (document.fullscreenElement) {
      this.appendToFullScreen(toasterElem);
    }
  }

  networkError(text: string = 'Currently unable to handle the request. HTTP Error 500') {
    const toasterElem = this._toaster.error(text, '', {
      timeOut: this.timeOut,
      positionClass: 'toast-bottom-right',
      closeButton: true,
      toastClass: this.toastClass
    });
    if (document.fullscreenElement) {
      this.appendToFullScreen(toasterElem);
    }
  }

  info(text: string, timeOut: number = this.timeOut) {
    const message = this._toaster.info(this._translate.instant(text ?? ''), '', {
      timeOut: timeOut,
      closeButton: true,
      disableTimeOut: true,
      positionClass: 'toast-bottom-right',
      toastClass: this.toastClass
    });
    if (document.fullscreenElement) {
      this.appendToFullScreen(message);
    }
    return message;
  }

  appendToFullScreen(toasterElem: ActiveToast<any>) {
    const toast = toasterElem.portal.location.nativeElement.parentElement;
    this.render.appendChild(document.fullscreenElement, toast);
  }

  remove(id: number) {
    this._toaster.remove(id);
  }

  setOverlay(container: any) {
    this._toaster.overlayContainer = container;
  }
}
