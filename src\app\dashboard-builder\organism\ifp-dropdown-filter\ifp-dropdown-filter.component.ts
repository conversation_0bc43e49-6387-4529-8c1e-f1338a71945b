import { ChangeDetectionStrategy, Component, input, InputSignal } from '@angular/core';
import { IfpDbDropdownComponent } from "../../molecule/ifp-db-dropdown/ifp-db-dropdown.component";
import { Columns } from '../../pages/dashboard.interface';
import { Columns, Columns } from '../ifp-chart-toolbar/ifp-chart-toolbar.constants';

@Component({
  selector: 'ifp-ifp-dropdown-filter',
  imports: [IfpDbDropdownComponent],
  templateUrl: './ifp-dropdown-filter.component.html',
  styleUrl: './ifp-dropdown-filter.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class IfpDropdownFilterComponent { 

  columns: InputSignal<Columns[]> = input<Columns[]>(Columns);
  
}

