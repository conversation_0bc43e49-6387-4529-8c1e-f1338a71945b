import { Compo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>er2, <PERSON><PERSON><PERSON><PERSON>, ChangeDetectorRef, ChangeDetectionStrategy, ViewChild, Input, Inject, HostListener, inject, linkedSignal } from '@angular/core';
import { IfpCategoryLabelComponent } from 'src/app/scad-insights/ifp-widgets/ifp-atoms/ifp-category-label/ifp-category-label.component';
import { CommonModule, DOCUMENT } from '@angular/common';
import { TranslateModule } from '@ngx-translate/core';
import { Router, RouterModule } from '@angular/router';
import { Store } from '@ngrx/store';
import { Subject, debounceTime } from 'rxjs';
import { SubSink } from 'subsink';
import { LabelData } from 'src/app/scad-insights/core/interface/atom/ifp-category-label.interface';
import { Classification } from 'src/app/scad-insights/core/interface/domains.interface';
import { buttonClass } from 'src/app/scad-insights/core/constants/button.constants';
import { selectCategoryResponse, selectNewsLetterResponse } from '../store/domain.selector';
import { IfpSearchComponent } from 'src/app/scad-insights/ifp-widgets/ifp-atoms/ifp-search/ifp-search.component';
import { IfpNoDataComponent } from 'src/app/scad-insights/ifp-widgets/ifp-molecules/ifp-no-data/ifp-no-data.component';
import { appType, contentType } from 'src/app/scad-insights/core/constants/contentType.constants';
import { ThemeService } from 'src/app/scad-insights/core/services/theme/theme.service';
import { classifications, newsLetterAr, newsLetterEn } from 'src/app/scad-insights/core/constants/domain.constants';
import { HeaderService } from 'src/app/scad-insights/core/services/header/header.service';
import { cloneDeep } from 'lodash';

import { DomainsService } from 'src/app/scad-insights/core/services/domains/domains.service';
import { externalUrlsDarkDashboard, externalUrlsDashboard, externalUrlsDashboardAr } from 'src/app/scad-insights/core/constants/chart.constants';
import { environment } from 'src/environments/environment';
import { analyticsClasses } from 'src/app/scad-insights/core/constants/analytics-class.constants';
import { NewsLetter } from '../../core/interface/domain.interface';
import { loadCategory } from '../store/domain.action';
import { IfpTabComponent } from '../../ifp-widgets/ifp-molecules/ifp-tab/ifp-tab.component';
import { UsageDashboardLogService } from '../../core/services/usage-dashboard-log/usage-dashboard-log.service';
import { logType } from '../../core/services/usage-dashboard-log/usage-dashboard.constants';
import { CommonApiService } from '../../core/services/common-api/commonApi.service';
import { CensusDomainNavComponent } from "./census-domain-nav/census-domain-nav.component";




@Component({
  selector: 'app-domains',
  templateUrl: './domains.component.html',
  styleUrls: ['./domains.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [CommonModule, IfpCategoryLabelComponent, TranslateModule, RouterModule,
    IfpSearchComponent, IfpNoDataComponent, IfpTabComponent, CensusDomainNavComponent]
})
export class DomainsComponent implements OnInit, OnDestroy {

  public subs = new SubSink();
  private _commonApiService: CommonApiService = inject(CommonApiService);
  private tabSwitchSubject = new Subject<Classification>();
  private isTabSwitching = false;
  private currentTabSwitchId = 0;

  constructor(private store: Store, private _cdr: ChangeDetectorRef, private _themeService: ThemeService,
    private headerService: HeaderService, private router: Router, private renderer: Renderer2,
    @Inject(DOCUMENT) private document: Document, private domainService: DomainsService, private log: UsageDashboardLogService) {
    this.subs.add(
      this.searchInput
        .pipe(debounceTime(500))
        .subscribe((value) => {
          // this.setSearchValues(this.classificationData[this.selectedTabIndex], value);
          this.store.dispatch(loadCategory({
            searchString: value
          }));
        })
    );

    // Add debounced tab switching to prevent race conditions
    this.subs.add(
      this.tabSwitchSubject
        .pipe(debounceTime(300))
        .subscribe((data) => {
          this.performTabSwitch(data);
        })
    );

    this.headerService.domainMenuRest$.subscribe(resp => {
      if (resp) {
        this.getCategoryOnLoad();
      }
    });
  }

  @ViewChild('searchBox') searchBox!: IfpSearchComponent;

  @Input() selectedTabIndex: number = -1;

  public subThemeLabelData: LabelData[] = [];
  searchInput = new Subject<string>();
  public domainLabelData: LabelData[] = [];
  public subDomainLabelData: LabelData[] = [];
  public searchData: any = [];
  public classificationData: Classification[] = [];
  public buttonClass = buttonClass;
  public loader: undefined | boolean = true;
  public theme$ = this._themeService.defaultTheme$;
  public lang = localStorage.getItem('lang') ? localStorage.getItem('lang') : 'en';
  public selectedTab!: Classification;
  public selectedSubDomain: any;
  public classification = classifications;
  public selectedDomain!: any;
  public analyticsClasses = analyticsClasses;
  public indicatorData: any = [];
  public productData: any = [];
  public searchString!: any;
  public previousIndicator: any;
  public contentType = contentType;
  public isAccordianActive!: number;
  public screenerInfo = 'A screener is a filter that sorts and selects items based on specific criteria from a larger group';
  public externalUrls: any = externalUrlsDashboard;
  public externalUrlsDark: any = externalUrlsDarkDashboard;
  public externalUrlsAr: any = externalUrlsDashboardAr;
  public isDesktop: boolean = true;
  private newsLetterData: NewsLetter[] = [];
  public types = {
    domain: 'domain',
    subdomain: 'subdomain',
    subTheme: 'subTheme'
  };

  public showCensus = linkedSignal(()=> this._commonApiService.censusData)

  private sessionId!: string;

  @HostListener('window:resize')
  resize() {
    if (window.matchMedia('(min-width: 1025px)').matches) {
      this.isDesktop = true;
    } else {
      this.isDesktop = false;
    }
  }

  ngOnInit() {
    this.getCategoryOnLoad();
    if (window.matchMedia('(min-width: 1025px)').matches) {
      this.isDesktop = true;
    } else {
      this.isDesktop = false;
    }
  }


  // category load ;
  getCategoryOnLoad() {
    this.classificationData = [];
    this.subs.add(this.store.select(selectCategoryResponse).subscribe((data: any) => {
      if (data) {
        this.loader = data?.loader;
        this.classificationData = cloneDeep([...data]);
        this._cdr.detectChanges();
        if (this.selectedTabIndex <= 0) {
          for (let index = 0; index < this.classificationData.length; index++) {
            if (this.classificationData[index].domains?.length) {
              this.selectedTabIndex = index;
              break;
            }
          }
        }

        this.subs.add(this.store.select(selectNewsLetterResponse).subscribe(resp => {
          this.newsLetterData = resp;
          if (resp == 'newsLetterComponent') {
            this.newsLetterData = [];
          }
          if (this.newsLetterData?.length > 0) {
            this.newsLetterData = cloneDeep(resp);
            this.newsLetterData = this.newsLetterData.map(element => ({
              ...element,
              content_classification: this.lang == 'en' ? 'Reports' : 'التقارير',
              content_classification_key: 'reports',
              content_type: 'newsletters',
              isExternal: true
            }));
          }
        }));
        this.getDomainListOnLoad(this.classificationData.length > 0 ? this.classificationData[this.selectedTabIndex] : {
          id: '',
          name: '',
          icon_path: '',
          nodeCount: 0,
          isSelected: false
        });
      }
    }));
  }

  // load , filter and search domains ;
  getDomainListOnLoad(data: Classification) {
    if (!this.searchString) {
      this.domainLabelData = [];
      this.searchString = undefined;
      this.subDomainLabelData = [];
      this.subThemeLabelData = [];
      this.productData = [];
      this.indicatorData = [];
      this.selectedTabIndex = this.classificationData.findIndex(x => x.name == data?.name);
      this.selectedTab = this.classificationData[this.selectedTabIndex];
      this.domainLabelData = data.domains && data.domains.length > 0 ? data.domains : [];
      this.domainLabelData = this.assignSelectedProperty(this.domainLabelData[0], this.domainLabelData);
      if (this.selectedTab.key === 'reports') {
        if (this.newsLetterData?.length > 0) {
          newsLetterEn.nodeCount = this.newsLetterData.length;
          newsLetterAr.nodeCount = this.newsLetterData.length;
          newsLetterEn.nodes = [...this.newsLetterData];
          newsLetterAr.nodes = [...this.newsLetterData];
        }
        if (this.newsLetterData?.length > 0) {
          this.domainLabelData.push(this.lang == 'en' ? newsLetterEn : newsLetterAr);
        }

      }
      // this.domainLabelData.forEach(element => {
      //   element.nodeCount = this.selectedTab?.name == classifications.analyticalApps ? element.nodes?.length : element.subDomains?.length
      // });
      // if (_searchString != '') {
      //   const regex = new RegExp(_searchString, 'i');
      //   this.searchString = _searchString;
      //   this.domainLabelData = this.domainLabelData.filter((element: { name: string; }) => regex.test(element.name));
      //   this.setSearchValues(this.domainLabelData[0], regex);
      // }
      if (this.isDesktop && this.domainLabelData.length > 0) {
        this.selectSubDomain(this.domainLabelData[0], 0);
      }

      if (this.showCensus() && this.classificationData[this.selectedTabIndex].key === this.classification.officialStatistics) {
        const censusData = {
          dark_icon: '../../../../assets/images/census-white.svg',
          id: 'census',
          isSelected: false,
          light_icon: '../../../../assets/images/census-black.svg',
          name: 'Census',
          nodeCount: 100,
          route: '/',
          showTree: false,
          subdomains: [
            {
              id: 'census-official',
              showTree: false
            }
          ],
          tabName: 'Official Statistics'
        }
        this.domainLabelData.push(censusData);
      }

      // Complete the loading process
      this.loader = false;
      this.isTabSwitching = false;
      this._cdr.detectChanges();
    }
  }

  // filter and load subdomains ;
  selectSubDomain(data: LabelData, index: number) {
    if (!this.searchString) {
      this.subThemeLabelData = [];
      this.productData = [];
      this.indicatorData = [];
      // if (this.selectedTab?.name !== this.classification.innovativeStatistics) {
      this.selectedDomain = data;
      if (this.searchString == '') {
        this.subThemeLabelData = [];
      }
      const bindingKey = data?.showTree ? 'subdomains' : 'nodes';
      this.domainLabelData.forEach((element, i) => {
        element.isSelected = i === index;
        element.tabName = this.selectedTab.name;
      });
      if (data.id === 'census') {
        return;
      }
      const filteringData = data?.[bindingKey] ? data?.[bindingKey] : [];
      this.subDomainLabelData = filteringData ? filteringData : [];
      if (this.searchString != '' && this.subDomainLabelData?.length > 0) {
        this.subDomainLabelData = this.subDomainLabelData.filter((sub: any) => new RegExp(this.searchString, 'i').test(bindingKey == 'subdomains' ? sub.name : sub.title));
      }
      this.subDomainLabelData = this.assignSelectedProperty(this.subDomainLabelData[0], this.subDomainLabelData, data.route);
      // this._cdr.detectChanges();
      // this.filterSubtheme(this.subDomainLabelData[0], index);
    }
  }

  // clicked item active ;
  assignSelectedProperty(data: LabelData, content: LabelData[], route: any = null) {
    return content.map((item) => {
      const type = item.app_type ? item.app_type : item.content_type;
      return { ...item, isSelected: false, route: item.app_type ? this.checkType(type, item.id, item.route) : (route ? route : item.route) };
      // return { ...item, isSelected: false, route: type == appType.insights_discovery.name || type == appType.internal.name || type == appType.tableau_internal.name ? this.checkType(type, item.id, item.route) : (route ? route : item.route) };
    });
  }

  checkType(type: string | undefined, id: any, route: string | undefined) {
    if (type) {
      const formattedType = type.replace(/_/g, '-');
      switch (type) {
        case appType.tableau_internal.name:
          return `${appType.tableau_internal.urlPrefix}/${formattedType}/${id}`;
        case appType.insights_discovery.name:
          return `${appType.insights_discovery.urlPrefix}/${id}`;
        case appType.internal.name:
          return `${appType.internal.urlPrefix}/${id}`;
        case appType.scad_official_indicator.name:
          return `${appType.scad_official_indicator.urlPrefix}/${appType.scad_official_indicator.name}/${id}`;
        case appType.what_if.name:
          return `${appType.what_if.urlPrefix}/${id}`;
        case appType.correlation.name:
          return `${appType.correlation.urlPrefix}/${id}`;
        default: return route;
      }
    } else {
      return route;
    }
  }




  // loading subtheme ;
  filterSubtheme(data: LabelData, index: number) {
    if (!this.searchString) {
      this.productData = [];
      this.indicatorData = [];
      if (this.selectedTab?.key !== this.classification.innovativeStatistics) {
        this.subThemeLabelData = data?.subthemes && data?.subthemes?.length > 0 ? data?.subthemes : [];
        if (this.searchString != '' && this.subThemeLabelData?.length > 0) {
          this.subThemeLabelData = this.subThemeLabelData.filter((sub: { name: string }) => new RegExp(this.searchString, 'i').test(sub.name));
        }
        this.subThemeLabelData = this.assignSelectedProperty(this.subThemeLabelData[0], this.subThemeLabelData, data.route);
        this.subDomainLabelData.forEach((element, i) => {
          element.isSelected = i == index ? true : false;
        });
        this._cdr.detectChanges();
      }
      this.selectedSubDomain = data;
      this.subDomainLabelData.forEach(element => {
        element.domain = this.selectedDomain.name;
        element.tabName = this.selectedTab.name;
        element.sub = element.name;
        element.subId = element.id;
      });
    }
  }

  setSubtheme(subData: any) {
    if (!this.searchString) {
      this.subThemeLabelData.map(x => x.isSelected = false);
      subData.isSelected = true;
      this.productData = [];
      this.indicatorData = [];
      this.productData = subData.products;
      if (this.productData?.length > 0) {
        this.productData.map((x: any) => x.isOpen = false);
      }
      if (this.searchString && this.productData?.length > 0) {
        this.productData = this.productData.filter((sub: { name: string }) => new RegExp(this.searchString, 'i').test(sub.name));
      }
      this.subThemeLabelData.forEach(element => {
        element.tabName = this.selectedTab.name;
        element.sub = this.selectedSubDomain.name;
        element.theme = element.name;
        element.themeId = element.id;
        element.subId = this.selectedSubDomain.id;
      });
    }
  }


  setIndicator(indicator: any) {
    this.productData.map((x: { isSelected: boolean; isOpen: boolean, name: string }) => {
      x.isSelected = false, x.isOpen = x.name == indicator.name ? !x.isOpen : false;
    });
    indicator.isSelected = true;
    this.indicatorData = [];
    if (this.searchString) {
      indicator.nodes = indicator.nodes.filter((sub: { title: string }) => new RegExp(this.searchString, 'i').test(sub.title));
    }
    this.indicatorData = indicator.nodes;
    this.previousIndicator = indicator;
  }



  searchValue(_event: any) {
    this.searchInput.next(_event);
  }


  linkClick(labelData: any, _type: string) {
    if (this.showCensus() && labelData.id === 'census') {
      window.open(this.showCensus().url.uri, '_blank');
      return;
    }
    if (labelData?.isExternal) {
      window.open(labelData.publicationAttachment, '_blank');
      return;
    }
    if (labelData.isCustom) {
      this.router.navigate(['/news-letters']);
      this.resetActivities();
      return;
    }
    if (['census', 'التعداد'].includes(labelData?.name?.toLowerCase())) {
      window.open(`${window.location.origin}/census/home`, '_blank');
      return;
    }
    const isExternal = labelData?.app_type === 'basket_insights' || labelData?.app_type === 'eci_insights' || labelData?.app_type === 'eci_insights_optimization';
    if (isExternal) {
      let externalUrl = labelData.app_type ? `${environment.baseUrl}/${this._themeService.defaultTheme === 'light' ? this.externalUrls[labelData.app_type] : this.externalUrlsDark[labelData.app_type]}` : '';
      if (labelData?.app_type === 'eci_insights') {
        externalUrl = externalUrl + this.lang;
      }
      if (labelData?.app_type === 'eci_insights_optimization') {
        const splitUrl = (url: string): [string, string] => [url.replace(/\/$/, "").slice(0, url.replace(/\/$/, "").lastIndexOf("/")), url.replace(/\/$/, "").split("/").pop() || ""];
        const optimizationUrl = splitUrl(externalUrl);
        externalUrl = `${optimizationUrl[0]}/${this.lang}/${optimizationUrl[1]}`;
      }
      if (this.sessionId) {
        this.log.logEnds(this.sessionId, this.log.currentTime);
      }
      this.sessionId = this.log.createUUid;
      this.log.logStart(this.sessionId, logType.indicator, this.log.currentTime, labelData.id).then(() => {
        window.open(externalUrl, '_self');
      });

    } else if (labelData?.app_type == appType.liveability) {
      this.router.navigate(['/liveability-dashboard/', labelData.id]);
    } else {
      if (labelData.content_type == appType.scad_official_indicator.name && this.selectedTab.key == classifications.analyticalApps) {
        labelData.route = `/statistics-insights/${labelData.content_type}/${labelData.id}`;
      }
      if (labelData.content_type === appType.newsletters.name) {
        labelData.route = `${appType.newsletters.urlPrefix}/${labelData.id}`;
      }
      this.router.navigate([labelData.route], { queryParams: labelData.content_type === appType.newsletters.name ? { contentType: labelData.content_type } : { subdomain: labelData.sub, tabName: labelData.tabName, subTheme: labelData.theme, contentType: labelData.content_type, domainId: labelData?.id, key: this.selectedTab?.key, subId: labelData.subId, themeId: labelData.themeId } });
    }
    this.resetActivities();
  }

  resetActivities() {
    this.headerService.hideDomain$.next(true);
    this.headerService.domainMenuRest$.next(true);
    this.domainService.clickDomain$.next('reset');
  }

  // ** for search function start //
  // setSearchValues(data: any, searchString: string) {
  //   this.searchString = searchString;
  //   this.resetLabelData();

  //   if (!this.searchString) {
  //     this.getDomainListOnLoad(this.classificationData[this.selectedTabIndex]);
  //     return;
  //   }

  //   const searchRegex = new RegExp(searchString, 'i');
  //   const key = this.isAnalyticalAppsOrReports(data.name) ? 'nodes' : 'subdomains';

  //   if (data?.domains?.length > 0) {
  //     const domainMap = new Map();
  //     const subDomainMap = new Map();
  //     const subThemeMap = new Map();
  //     const productMap = new Map();

  //     data.domains.forEach((domain: any) => {
  //       this.checkAndAddDomain(domain, searchRegex, key, domainMap, subDomainMap, subThemeMap, productMap);
  //     });
  //   }

  //   this._cdr.detectChanges();
  // }

  resetLabelData() {
    this.domainLabelData = [];
    this.subDomainLabelData = [];
    this.subThemeLabelData = [];
    this.productData = [];
    this.indicatorData = [];
  }

  isAnalyticalAppsOrReports(name: string): boolean {
    const { searchAnalyticalApps, searchReports } = this.classification;
    return name.toLowerCase() === searchAnalyticalApps.toLowerCase() || name.toLowerCase() === searchReports.toLowerCase();
  }

  checkAndAddDomain(domain: any, searchRegex: RegExp, key: string, domainMap: Map<string, any>, subDomainMap: Map<string, any>, subThemeMap: Map<string, any>, productMap: Map<string, any>) {
    if (searchRegex.test(domain.name)) {
      this.addToMapAndList(domainMap, this.domainLabelData, domain.name, domain);
    }

    if (domain[key]?.length > 0) {
      domain[key].forEach((subDomain: any) => {
        this.checkAndAddSubDomain(subDomain, domain, searchRegex, key, domainMap, subDomainMap, subThemeMap, productMap);
      });
    }
  }

  checkAndAddSubDomain(subDomain: any, domain: any, searchRegex: RegExp, key: string, domainMap: Map<string, any>, subDomainMap: Map<string, any>, subThemeMap: Map<string, any>, productMap: Map<string, any>) {
    const subDomainName = key === 'nodes' ? subDomain.title : subDomain.name;

    if (searchRegex.test(subDomainName)) {
      subDomain.route = this.getSubDomainRoute(subDomain, domain);
      this.addToMapAndList(subDomainMap, this.subDomainLabelData, subDomainName, subDomain);
      this.addToMapAndList(domainMap, this.domainLabelData, domain.name, domain);
    }

    if (this.selectedTab?.key !== this.classification.innovativeStatistics && subDomain?.subthemes?.length > 0) {
      subDomain.subthemes.forEach((subTheme: any) => {
        this.checkAndAddSubTheme(subTheme, subDomain, domain, searchRegex, domainMap, subDomainMap, subThemeMap, productMap);
      });
    }
  }

  checkAndAddSubTheme(subTheme: any, subDomain: any, domain: any, searchRegex: RegExp, domainMap: Map<string, any>, subDomainMap: Map<string, any>, subThemeMap: Map<string, any>, productMap: Map<string, any>) {
    if (searchRegex.test(subTheme.name)) {
      subTheme.route = domain.route;
      subDomain.route = this.getSubDomainRoute(subDomain, domain);
      this.addToMapAndList(subThemeMap, this.subThemeLabelData, subTheme.name, subTheme);
      this.addToMapAndList(subDomainMap, this.subDomainLabelData, subDomain.name, subDomain);
      this.addToMapAndList(domainMap, this.domainLabelData, domain.name, domain);
    }

    if (subTheme?.products?.length > 0) {
      subTheme.products.forEach((product: any) => {
        this.checkAndAddProduct(product, subTheme, subDomain, domain, searchRegex, domainMap, subDomainMap, subThemeMap, productMap);
      });
    }
  }

  checkAndAddProduct(product: any, subTheme: any, subDomain: any, domain: any, searchRegex: RegExp, domainMap: Map<string, any>, subDomainMap: Map<string, any>, subThemeMap: Map<string, any>, productMap: Map<string, any>) {
    if (searchRegex.test(product.name)) {
      subTheme.route = domain.route;
      subDomain.route = this.getSubDomainRoute(subDomain, domain);
      this.addToMapAndList(productMap, this.productData, product.name, product);
      this.addToMapAndList(subThemeMap, this.subThemeLabelData, subTheme.name, subTheme);
      this.addToMapAndList(subDomainMap, this.subDomainLabelData, subDomain.name, subDomain);
      this.addToMapAndList(domainMap, this.domainLabelData, domain.name, domain);
    }

    if (product?.nodes?.length > 0) {
      product.nodes.forEach((node: any) => {
        this.checkAndAddNode(node, product, subTheme, subDomain, domain, searchRegex, domainMap, subDomainMap, subThemeMap, productMap);
      });
    }
  }

  checkAndAddNode(node: any, product: any, subTheme: any, subDomain: any, domain: any, searchRegex: RegExp, domainMap: Map<string, any>, subDomainMap: Map<string, any>, subThemeMap: Map<string, any>, productMap: Map<string, any>) {
    if (searchRegex.test(node.title)) {
      subTheme.route = domain.route;
      subDomain.route = this.getSubDomainRoute(subDomain, domain);
      this.indicatorData.push(node);
      this.addToMapAndList(productMap, this.productData, product.name, product);
      this.addToMapAndList(subThemeMap, this.subThemeLabelData, subTheme.name, subTheme);
      this.addToMapAndList(subDomainMap, this.subDomainLabelData, subDomain.name, subDomain);
      this.addToMapAndList(domainMap, this.domainLabelData, domain.name, domain);
    }
  }

  getSubDomainRoute(subDomain: any, domain: any) {
    return this.selectedTab?.name !== this.classification.analyticalApps ? domain.route : this.createAnalyticUrls(subDomain);
  }

  addToMapAndList(map: Map<string, any>, list: any[], key: string, item: any) {
    if (!map.has(key)) {
      map.set(key, item);
      list.push(item);
    }
  }

  // ** for search function end //

  createAnalyticUrls(data: any) {
    let route: any;
    if (data.app_type == appType.insights_discovery.name) {
      route = `${appType.insights_discovery.urlPrefix}/${data.id}`;
    }
    if (data.app_type == appType.internal.name) {
      route = `${appType.internal.urlPrefix}/${data.id}`;
    }
    return route;
  }

  openDetailPage(indicator: any) {
    const selectedDomain = this.domainLabelData.find(x => x.isSelected);
    const queryParams = { contentType: indicator.content_type };
    const url = `/statistics-insights/${indicator.content_type}/${indicator.id}`;
    this.router.navigate([url], { queryParams: queryParams });
    this.renderer.removeClass(this.document.body, 'ifp-modal-open');
    this.headerService.hideDomain$.next(true);
    this.headerService.domainMenuRest$.next(true);
  }

  setNodeCount(data: any, type: string) {
    const returnData = data;
    if (type == 'domain') {
      returnData.nodeCount = this.selectedTab?.name == classifications.analyticalApps ? returnData.nodes?.length : returnData.subDomains?.length;
    }
    if (type != 'domain') {
      returnData.nodeCount = returnData[type]?.length;
    }
    return returnData;
  }

  checkCount(data: any, child: any) {
    let matchingIds: any;
    if (data?.length > 0) {
      matchingIds = data.map((item: { name: any; }) => item.name).filter((name: any) => child.some((item: { name: any; }) => item.name === name));
      data = data.filter((x: { child: string; }) => x.child == child);
    }
    return matchingIds.length;
  }

  checkNodeCount(data: any) {
    let matchingIds: any;
    if (data?.length > 0) {
      matchingIds = data.filter((x: { title: string; }) => new RegExp(this.searchString, 'i').test(x.title));
    }
    return matchingIds?.length;
  }


  // Helper method to clear domain state in a controlled manner
  private clearDomainState(): void {
    this.domainLabelData = [];
    this.subDomainLabelData = [];
    this.subThemeLabelData = [];
    this.productData = [];
    this.indicatorData = [];
  }

  // Debounced tab switching method to prevent race conditions
  private performTabSwitch(data: Classification): void {
    const switchId = ++this.currentTabSwitchId;
    this.isTabSwitching = true;
    this.loader = true;
    this._cdr.detectChanges();

    // Clear existing state immediately to prevent stale data display
    this.clearDomainState();

    // Use a small delay to ensure UI updates are processed
    setTimeout(() => {
      if (switchId === this.currentTabSwitchId) {
        try {
          this.getDomainListOnLoad(data);
        } catch (error) {
          console.error('Error during tab switch:', error);
          this.loader = false;
          this.isTabSwitching = false;
          this._cdr.detectChanges();
        }
      }
    }, 150); // Slightly increased delay for better UX
  }

  // Enhanced tab switching with proper state management
  handleTabSwitch(data: Classification): void {
    if (this.isTabSwitching) {
      console.log('Tab switch already in progress, ignoring request');
      return; // Prevent multiple simultaneous switches
    }

    if (!data) {
      console.error('Invalid classification data provided for tab switch');
      return;
    }

    console.log('Initiating tab switch to:', data.name);
    this.tabSwitchSubject.next(data);
  }

  ngOnDestroy() {
    this.subs.unsubscribe();
    this.tabSwitchSubject.complete();
    this.store.dispatch(loadCategory({
      searchString: ''
    }));
    // this._cdr.detectChanges();
  }
}
