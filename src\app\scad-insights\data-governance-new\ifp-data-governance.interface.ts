


export interface CountData {
  CONTENT: string,
  COUNT: number,
  TYPE_ID: number,
  icon?: string;
  color?:string;
  USECASE_TYPE_EN: string;
}

export interface DataTableData {
  key: string,
  title: string,
  value: string,
  type: string
}

export interface DonutChartData {
  type: string,
  data: {
    name: string,
    y: number,
    color: string
  }[]
}

export interface DataCount {
  field: string,
  count : number
}
