@if (!emptyConnection() && currentConection() && sourceData() && !downloadingFile() && !workFlowRunning() && !errorMessageForTools) {
  @if(this.analyticsTools.length !==0) {
<div class="ifp-adv-tool__content">
  @for (tool of analyticsTools; track $index) {
  <div class="ifp-adv-tool__item-box ifp-adv-tool__item-box--info"
    [ngClass]="{'ifp-adv-tool__disable': tool.isDisabled || !_prepService.isWorkFlowStarted()}" (click)="goTOTool(tool)">
    <em class="ifp-icon ifp-adv-tool__item-box-icon" [class]="tool.icon" [style.backgroundColor]="tool.iconColor"></em>
    <p class="ifp-adv-tool__item-box-name">{{tool.title | translate}}</p>
    <div class="ifp-adv-tool__info">
      <em class="ifp-icon ifp-icon-info-round"></em>
      <div class="ifp-adv-tool__info-box">
        <p class="ifp-adv-tool__info-desc">{{tool.description | translate}}</p>
      </div>
    </div>
  </div>
  }
</div>
  }
@if(this.analyticsTools.length !==0) {
  <p class="ifp-adv-tool__divider"><span class="ifp-adv-tool__divider-text">{{'OR' | translate}}</span></p>
}

<ifp-button [label]="'Download Output'" class="ifp-adv-dest__button"
  [buttonClass]="buttonClass.primary+ ' '+ buttonClass.large" [iconClass]="'ifp-icon-download-line'"
  (ifpClick)="onDownloadClick(true)"></ifp-button>

<ifp-button [label]="'Save to My Bayaan Library'" class="ifp-adv-dest__button"
  [buttonClass]="buttonClass.secondary+ ' '+ buttonClass.large" [iconClass]="'ifp-icon-library'"
  (ifpClick)="onDownloadClick(false)"></ifp-button>

<app-ifp-modal [overlayType]="'transparent'" #saveDataModal>
  <ifp-prep-save-modal [title]="download ? ('Download' | translate) : ('Save'| translate)" (closeModal)="closeModal($event)" (saveFile)="exportData($event)" [fileName]="''"
  [message]="(download ? 'Enter the file name and click Save to download the file.': 'Save the output file to your Bayaan library.') | translate" [buttonSecondary]="toolForward ?  'Don\'t Save' : 'Cancel' " ></ifp-prep-save-modal>
</app-ifp-modal>

<app-ifp-modal [overlayType]="'transparent'" #dashboardModel>
  <ifp-sc-upload-model class="ifp-db__upload-model"
   [tabData]="tabData" [isCustomInstruction]="true" [buttonLabel]="'Ok'" (closeUploadModel)="closeDashBordModel($event)"></ifp-sc-upload-model>
</app-ifp-modal>

} @else {
  @if(errorMessageForTools) {
    {{errorMessageForTools}}
  } @else if(downloadingFile() || workFlowRunning()) {
<div class="ifp-center-loader">
  <app-ifp-spinner></app-ifp-spinner>
  <p> {{'Workflow Running' | translate}} </p>
</div>

} @else if (emptyConnection()){
<p class="ifp-adv-tool__error"> <em class="ifp-icon ifp-icon-exclamation-round">

  </em>
  {{'Source must have a connection!' | translate}}
</p>
} @else if (!sourceData()) {
<p class="ifp-adv-tool__error"> <em class="ifp-icon ifp-icon-exclamation-round">

  </em>
  {{'You must upload at least one source!' | translate}}
</p>
} @else if(!currentConection()){
<p class="ifp-adv-tool__error"> <em class="ifp-icon ifp-icon-exclamation-round">

  </em>
  {{'Node must have atleast one connection' | translate}}
</p>
}
@else {
<app-ifp-spinner></app-ifp-spinner>
}

}
