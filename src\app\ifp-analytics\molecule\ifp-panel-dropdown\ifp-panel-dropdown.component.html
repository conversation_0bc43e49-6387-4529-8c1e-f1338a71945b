<div class="ifp-panel-dropdown"
  [ngClass]="{'ifp-panel-dropdown--show': showDropdown, 'ifp-panel-dropdown--box': isBoxType, 'ifp-panel-dropdown--userDropdown':userDropdown()}" #drop>
  @if (label) {
  <p class="ifp-panel-dropdown__label">{{label}}</p>
  }
  @if(icon && !textValue()) {
  <div class="ifp-panel-dropdown__select-icon" [ngStyle]="{'width': iconSize, 'height': iconSize}"
    (click)="showDropdown = !showDropdown" [appIfpTooltip]="tooltipText | translate" [placement]="'topLeft'">
    <em class="ifp-icon" [class]="icon"></em>
  </div>

  } @else if (textValue() && icon) {
  <div class="ifp-panel-dropdown__with-value" (click)="showDropdown = !showDropdown">
    <span class="ifp-panel-dropdown__text"> {{textValue()}}</span>
    <em class="ifp-icon" [class]="icon"></em>
  </div>
  }
  @else {
  <div class="ifp-panel-dropdown__select-box" (click)="showDropdown = !showDropdown">
    <p class="ifp-panel-dropdown__selected">{{isBoxType() ? (multipleSelectedItems.length ?
      multipleSelectedItems[0]?.value : 'Select') : multipleSelectedItems.length}}</p>
    @if (isBoxType() && multipleSelectedItems.length > 1) {
    <span class="ifp-panel-dropdown__selected-count">{{'+'+(multipleSelectedItems.length - 1)}}</span>
    }
    <em class="ifp-icon ifp-icon-down-arrow"></em>
  </div>
  }


  <div class="ifp-panel-dropdown__list-wrapper">
    @if (enableSearch()) {
    <div class="ifp-panel-dropdown__search-box">
      <em class="ifp-icon ifp-panel-dropdown__search-icon ifp-icon-search"></em>
      <input type="text" (keyup)="onSearch()" [formControl]="search" class="ifp-panel-dropdown__search-input"
        [placeholder]="'Search' | translate">
    </div>
    }

    @if (enableSelectAll()) {
    <div class="ifp-panel-dropdown__select-all">
      <app-ifp-check-box [label]="'Select All'" [checkedData]="selectAll()"
        (checked)="selectAllValues()"></app-ifp-check-box>
    </div>
    }
    <ul class="ifp-panel-dropdown__list">
      @for (item of searchOptions(); track i; let i=$index) {
      <li class="ifp-panel-dropdown__list-item" [ngClass]="{ 'ifp-panel-dropdown__list-item--disabled': checkDisable() && item.checked && userDropdown(),'ifp-panel-dropdown__list-text--selected' : getMutiselectedItem(item)&&userDropdown()}">
        @if (multiSelect) {
          @if(userDropdown()) {
            <div    class="ifp-panel-dropdown__user"     [ngClass]="{'ifp-panel-dropdown__list-text--selected' : getMutiselectedItem(item), 'ifp-panel-dropdown__list-item--disabled': checkDisable() && item.checked}">
<app-ifp-check-box  [label]="''" [checkedData]="item.checked"
          [changeCheckedDataValue]="changeCheckedDataValue()" #checkbox (checked)="setChecked($event, item)"
>   </app-ifp-check-box>
            <ifp-user-tag class="ifp-panel-dropdown__user-tag"  [name]="item.value" (click)="checkbox.onCheck()"></ifp-user-tag>

            </div>


          } @else {
                  <app-ifp-check-box class="ifp-panel-dropdown__list-text" [label]="item.value" [checkedData]="item.checked"
          [changeCheckedDataValue]="changeCheckedDataValue()" (checked)="setChecked($event, item)"
          [ngClass]="{'ifp-panel-dropdown__list-text--selected' : getMutiselectedItem(item), 'ifp-panel-dropdown__list-item--disabled': checkDisable() && item.checked}"></app-ifp-check-box>
          }

        } @else {
        <p class="ifp-panel-dropdown__list-text" (click)="setSingleSelection(item)"
          [ngClass]="{'ifp-panel-dropdown__list-text--selected-single' : selectedValue=== item.key}">{{item.value |
          titlecase }}</p>
        }
      </li>
      }@empty {
      <app-ifp-no-data [isSmall]="true" [height]="'100px'" [isTransparent]="true"></app-ifp-no-data>
      }
    </ul>
  </div>

</div>
