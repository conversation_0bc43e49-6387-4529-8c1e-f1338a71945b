<div class="ifp-chart-style">
  <div class="ifp-chart-style__spacing-box">
    <p class="ifp-chart-style__spacing ifp-chart-style__spacing--y"><span class="ifp-chart-style__spacing-text">{{
        spaceOptions[0].value }}</span></p>
    <p class="ifp-chart-style__spacing ifp-chart-style__spacing--x"><span class="ifp-chart-style__spacing-text">{{
      spaceOptions[2].value }}</span></p>
    <div class="ifp-chart-style__content-box"></div>
    <p class="ifp-chart-style__spacing ifp-chart-style__spacing--x"><span class="ifp-chart-style__spacing-text">10</span></p>
    <p class="ifp-chart-style__spacing ifp-chart-style__spacing--y"><span class="ifp-chart-style__spacing-text">{{
      spaceOptions[1].value }}</span></p>
  </div>

  <div class="ifp-chart-style__props">
    @for (opt of spaceOptions; track opt) {
    <ifp-db-chart-props [propName]="opt.name | translate" class="ifp-chart-style__props-item">
      <div class="ifp-chart-props__type">
        <ifp-input-counter [value]="opt.value" [key]="opt.key" (updateSpacingValue)="updateSpacingValue($event)"
          [selectedCard]="selectedCard"></ifp-input-counter>
      </div>
    </ifp-db-chart-props>
    }

  </div>

  <div class="ifp-chart-style__props">
    <h4 class="ifp-chart-style__props-heading">{{'Line color' | translate}}</h4>
    @if (chartSeies) {
      <ifp-db-chart-props *ngFor="let item of [].constructor(chartSeies); let i = index" [propName]="(seriesTitles[i] | titlecase | slice:0:30)" [isDots]="seriesTitles[i].length > 30" class="ifp-legend-props__props-item">
        <div class="ifp-chart-props__type">
          <input type="color" [value]="seriesColors[i]" (change)="getColor($event, i)">
        </div>
      </ifp-db-chart-props>
    }

    <!-- <ifp-db-chart-props [propName]="'x'" class="ifp-chart-style__props-item">
      <div class="ifp-chart-props__type">
        <ifp-input-counter [value]="0"></ifp-input-counter>
      </div>
    </ifp-db-chart-props>
    <ifp-db-chart-props [propName]="'y'" class="ifp-chart-style__props-item">
      <div class="ifp-chart-props__type">
        <ifp-input-counter [value]="0"></ifp-input-counter>
      </div>
    </ifp-db-chart-props> -->
  </div>

  <div class="ifp-chart-style__props">
    <h4 class="ifp-chart-style__props-heading">{{'Axis' | translate}}</h4>
    <ifp-db-chart-props [propName]="'Align'" class="ifp-chart-style__props-item">
      <div class="ifp-chart-props__type">
        <app-ifp-db-dropdown [isMultiSelect]="false" [options]="alignOptions" (singleSelected)="selectXaxisPos($event)"
          [selectedSingleItem]="selectedXaxis" [defaultSelect]="false" [key]="'name'"></app-ifp-db-dropdown>
      </div>
    </ifp-db-chart-props>
    <!-- <ifp-db-chart-props [propName]="'x'" class="ifp-chart-style__props-item">
      <div class="ifp-chart-props__type">
        <ifp-input-counter [value]="0"></ifp-input-counter>
      </div>
    </ifp-db-chart-props>
    <ifp-db-chart-props [propName]="'y'" class="ifp-chart-style__props-item">
      <div class="ifp-chart-props__type">
        <ifp-input-counter [value]="0"></ifp-input-counter>
      </div>
    </ifp-db-chart-props> -->
  </div>
</div>
