import { IfpModalService } from './../../../../scad-insights/ifp-widgets/ifp-organism/ifp-modal/ifp-modal.service';
import { prepStatusStore, workFlowState } from './../constants/ifp-state.contants';
import { IfpPrepService } from './../ifp-prep-service';
import { ChangeDetectorRef, Component, OnDestroy, OnInit, ViewChild, signal } from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';
import { IfpDbDropdownComponent } from 'src/app/dashboard-builder/molecule/ifp-db-dropdown/ifp-db-dropdown.component';
import { IfpDataTableComponent } from '../../../organism/ifp-data-table/ifp-data-table.component';
import { buttonClass } from 'src/app/scad-insights/core/constants/button.constants';
import {  Router } from '@angular/router';
import { SubSink } from 'subsink';
import { Store } from '@ngrx/store';
import { loadCreateConnectionUploadUpdateName, loadNode, loadRemoveConnectionUploadUpdateName } from '../store/node-store/node-store.actions';
import { selectNodePrepResponse } from '../store/node-store/node-store.selectors';
import { prepsApiEndpoints } from '../constants/if-preps.constants';
import { FormArray, FormControl, FormGroup } from '@angular/forms';
import { IfpSpinnerComponent } from 'src/app/scad-insights/ifp-widgets/ifp-molecules/ifp-spinner/ifp-spinner.component';
import { PrepColumnInterface } from './interface/prep-selection.interface';
import { IfpModalComponent } from '../../../../scad-insights/ifp-widgets/ifp-organism/ifp-modal/ifp-modal.component';
import { IfpPrepPopupComponent } from '../../../organism/ifp-prep-popup/ifp-prep-popup.component';
import { CardData } from 'src/app/shared/molecule/ifp-tool-card/ifp-tool-card.component';
import { cloneDeep } from 'lodash';



@Component({
    selector: 'ifp-prep-select-data',
    templateUrl: './ifp-prep-select-data.component.html',
    styleUrl: './ifp-prep-select-data.component.scss',
    imports: [TranslateModule, IfpDataTableComponent, IfpSpinnerComponent, IfpModalComponent, IfpPrepPopupComponent]
})
export class IfpPrepSelectDataComponent implements OnDestroy, OnInit{
  @ViewChild('modalSucess') modalSucess!: IfpModalComponent;
  public tableHead: string[] = ['Fields', 'Data Type', ' '];
  public buttonClass = buttonClass;
  public subs = new SubSink();
  public formControl: FormArray<FormGroup<{ name: FormControl<string | null>; type: FormControl<string | null>; }>> | FormArray<never>  | any= new FormArray([]);
  public loader = signal(true);
  public objectID!: string;
  public processed = false;
  public columns: PrepColumnInterface[] =[];
  public loaderProcess = signal(false);
  public numberOfChange = 0;
  public deleteCount = signal(0);
  public isChanges: boolean = false;
  public columnDeep:  PrepColumnInterface[] =[];


  public tableData:({ key: string; title: string; value: string; type: string; } | { key: string; title: string; check: boolean; type: string; sortField: string; sortType: string; renderComponent: () => Promise<typeof IfpDbDropdownComponent>; inputValues: Record<string, string>; outputs: string[]; } |  Record<string, any>)[] = [];

  constructor(private _router: Router, public prepService:IfpPrepService,  private _store: Store,
    private _cdr:ChangeDetectorRef, private _modalService: IfpModalService) {
  }

  ngOnInit(): void {
    this.prepService.processDisable.set(true);
    this.subs.add( this.prepService.processEvent.subscribe((data: string) => {
      if (data !== '') {
        this.proceed();
      }

    }));
    if (!this.prepService.uploadedFileResponse()) {
      this._router.navigate(['analytics/data-preparation/upload-data']);
    } else {
      this.prepService.showPreview.set(true);
      this.prepService.processActive.set(true);
      this._store.dispatch(loadNode['[prepComponent]CreateNodeAddDupload']({config: { update: {}, remove: {}}}));
      this.subs.add(
        this._store.select(selectNodePrepResponse).subscribe(dataState => {
          if (dataState.currentStatus === prepStatusStore.selectDataLoaded ) {
            const nodeLength = dataState.data.nodes.length;
            const lastValue = dataState.data.nodes[nodeLength - 1];
            this.objectID =lastValue.object_id;
            this.prepService.processStatus.next(null);
            const progress = this.prepService.processStatus.subscribe(dataWorkFlow=> {
              this.loader.set(true);
              if (dataWorkFlow?.workflow_status === workFlowState.completed) {
                const column = this.prepService.postMethodRequest(`${prepsApiEndpoints.selectNode}${lastValue.object_id}${prepsApiEndpoints.coloumDownsterm}`, dataState.data).subscribe((dataColumn: PrepColumnInterface[])=> {
                  this.isChanges = false;
                  this.prepService.processDisable.set(true);
                  this.formControl=  new FormArray([]);
                  this.loader.set(false);
                  this.tableData = [];
                  this.columns = [];
                  this.deleteCount.set(0);
                  this.columnDeep= cloneDeep(dataColumn);
                  dataColumn.forEach((prepColumn: PrepColumnInterface, indexCloumn) => {
                    let optionsCurrentValue!: {name: string};
                    const optionsValues: Record<string, string>[]= [];
                    prepColumn?.allowed_types?.forEach((element) => {
                      if (element === prepColumn.type) {
                        optionsCurrentValue = {name: element};
                      }
                      optionsValues.push({name: element});
                    });
                    this.columns.push(prepColumn);
                    this.formControl.push(
                      new FormGroup(   {
                        name: new FormControl(prepColumn.name),
                        type: new FormControl( optionsCurrentValue ? optionsCurrentValue :optionsValues[0]),
                        delete: new FormControl(false),
                        options: new FormControl(optionsValues),
                        disableDelete: new FormControl(false)
                      })
                    );
                    this.tableData.push(
                      [
                        { key: 'fields', title: 'Fields', value: prepColumn.name, type: 'defualt' },
                        {
                          key: 'name',
                          title: 'Name',
                          check: true,
                          type: 'custom',
                          sortField: 'name',
                          sortType: 'string',
                          renderComponent: () => import('src/app/dashboard-builder/molecule/ifp-db-dropdown/ifp-db-dropdown.component').then(mode => mode.IfpDbDropdownComponent),
                          inputValues: {
                            options: optionsValues,
                            isMultiSelect: false,
                            key: 'name',
                            selectedSingleItem: optionsCurrentValue ? optionsCurrentValue : optionsValues[0],
                            formControls: this.formControl.controls[indexCloumn].controls.type,
                            optionAlias: true
                          },
                          outputs: ['singleSelected', 'multiSelected']
                        },
                        {
                          key: '',
                          title: '',
                          check: false,
                          type: 'custom',
                          sortField: '',
                          sortType: '',
                          renderComponent: () => import('src/app/ifp-analytics/molecule/ifp-icon/ifp-icon.component').then(mode => mode.IfpIconComponent),
                          inputValues: {
                            count: this.deleteCount,
                            totalLenght: dataColumn.length -1,
                            icon: 'ifp-icon-trash',
                            formControls: this.formControl.controls[indexCloumn].controls.delete,
                            className: 'ifp-data-table__row--bg-red'
                          },
                          outputs: [
                            'iconClick'
                          ]
                        }
                      ]
                    );
                  });
                  column.unsubscribe();
                } );
                progress.unsubscribe();
              }
            });
          }
        })
      );
    }

  }

  closeSucessModal() {
    // this. selectCall();
    this.modalSucess.removeModal();
    this._modalService.removeAllModal();
  }

  buttonClick(event: CardData) {
    if (event.key === 'select') {
      // this.selectCall();
    }
    this.modalSucess?.removeModal();
    this._modalService.removeAllModal();
  }

  customEventEmitter(_event: { value: string, event: string }) {
    let change = false;
    let deleteCount= 0;
    this.columnDeep.forEach((data, index)=> {
      const type = data.type ?? data.allowed_types[0];
      if ( type !== this.formControl.value[index]?.type?.name) {
        change = true;
      }
      if ( this.formControl.value[index]?.delete) {
        deleteCount = deleteCount + 1;
      }
    });
    this.deleteCount.set(deleteCount);
    if (change) {
      this.prepService.processDisable.set(false);
      this.isChanges = true;
    } else {
      this.isChanges = false;
      this.prepService.processDisable.set(true);
    }
    if (this.deleteCount() !== 0 && !this.isChanges) {
      this.isChanges = true;
      this.prepService.processDisable.set(false);
    }
  }

  proceed() {
    const configration: Record<string, any> = {};
    const removed: string[]= [];
    this.formControl.value?.forEach((element: {name: string;
      type: {name: string};
      delete: boolean;}, index:  number) => {
      this.numberOfChange = 0;
      if (this.columns[index].type !== element?.type?.name)  {
        this.numberOfChange =  this.numberOfChange + 1;
      }
      if (!element.delete ) {
        configration[element.name]= {
          'name': element.name,
          'type': element?.type?.name,
          'order': index
        };
      } else  {
        removed.push(element.name);
      }
    });
    this.prepService.disableSidebar.set(true);
    this.prepService.processActive.set(true);
    this.loaderProcess.set(true);
    this.prepService.loaderProcess.set(true);
    this._store.dispatch(loadNode[loadCreateConnectionUploadUpdateName]({config: { update: configration, remove: removed}, objectId: this.objectID}));
    const process = this.prepService.processStatus.subscribe(data=> {
      if (data?.workflow_status === workFlowState.completed) {
        this.prepService.isShowPreview = true;
        this.processed = true;
        this.prepService.disableSidebar.set(false);
        this.loaderProcess.set(false);
        this.prepService.loaderProcess.set(false);
        process.unsubscribe();
        this. selectCall();
        this._modalService.removeAllModal();
        this.modalSucess.createElement();
        this._cdr.detectChanges();
      } else if (data?.workflow_status === workFlowState.error) {
        this.prepService.isShowPreview = false;
        this.processed = false;
        this.prepService.disableSidebar.set(false);
        this.loaderProcess.set(false);
        this.prepService.loaderProcess.set(false);
        process.unsubscribe();
      }
    });
  }

  reset() {
    this.formControl.value?.forEach((data: {name: string;
      type: {content_type: Record<string, string>}; custom: string;
      disabled: boolean; options: {name: string} [];}, index: number) => {
      this.formControl.controls[index?? 0].controls.type.setValue(data.options[0]);
    });
  }

  ngOnDestroy(): void {
    if ( !this.processed) {
      this._store.dispatch(loadNode[loadRemoveConnectionUploadUpdateName]({ objectId: this.objectID}));
    }
    this.prepService.showPreview.set(false);
    this.subs.unsubscribe();
    // this.modalSucess?.removeModal();
    this._modalService.removeAllModal();
    this.prepService.processEvent.next('');
  }

  selectCall() {
    this.prepService.processActive.set(false);
    // this.loader.set(true);
    this.processed = false;
    this._store.dispatch(loadNode['[prepComponent]CreateNodeAddDupload']({config: { update: {}, remove: {}}}));
  }
}

