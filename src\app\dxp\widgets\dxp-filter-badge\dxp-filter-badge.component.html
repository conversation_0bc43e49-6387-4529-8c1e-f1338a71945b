<div class="ifp-dxp-filter-badge" [ngClass]="{ 'ifp-dxp-filter-badge--active': isSelected() }">
  <div class="ifp-dxp-filter-badge__container">
    <h6 class="ifp-dxp-filter-badge__title">{{title()}}</h6>
    <div class="ifp-dxp-filter-badge__icon-sec">
      <em class="ifp-icon ifp-icon-edit" (click)="onEditFilter()"></em>
      @if (showRemove()) {
        <em class="ifp-icon ifp-icon-trash" (click)="removeFilter.emit(id())"></em>
      }
    </div>
  </div>

  <p class="ifp-dxp-filter-badge__filter">{{'If entry in column '}} <span class="ifp-dxp-filter-badge__value">{{column()}} </span> <span class="ifp-dxp-filter-badge__comparator">{{comparator()}}</span> {{'the value'}} <span class="ifp-dxp-filter-badge__value">{{value()}}</span></p>
</div>
