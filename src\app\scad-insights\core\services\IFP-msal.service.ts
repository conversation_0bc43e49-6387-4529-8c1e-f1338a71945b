import { Injectable, Optional, InjectionToken, Inject, WritableSignal, signal } from '@angular/core';
import { AuthenticationResult, IPublicClientApplication } from '@azure/msal-browser';
import { environment } from 'src/environments/environment';
import { Buffer } from 'buffer';
import { CookieService } from './cookie.service';
import { accessAcount, accessToken, cacheToken, currentUrl, idToken, tokenName } from '../constants/msalConfig.constants';
import {  Router } from '@angular/router';
import { HttpClient, HttpUrlEncodingCodec } from '@angular/common/http';
import { BehaviorSubject } from 'rxjs';
import { DomSanitizer } from '@angular/platform-browser';
import { UsageDashboardLogService } from './usage-dashboard-log/usage-dashboard-log.service';
import { UaePassService } from './uae-pass-service/uae-pass-service.service';
import { DecodeJwtTokenService } from './decode-jwt-token.service';
import { authTypes, localStorageKeys } from '../constants/auth.constants';
import { v4 as uuidv4 } from 'uuid';

@Injectable({
  providedIn: 'root'
})
export class IFPMsalService {

  public msalInstance!: IPublicClientApplication;
  private refreshTokenTimeout!: ReturnType<typeof setTimeout>;
  public worker!: Worker;
  public authCheckStatus = false;
  public silentToken = false;
  public profilePic = new BehaviorSubject<any>(null);
  public token: string | null = null;
  public codec = new HttpUrlEncodingCodec();
  public showTour: boolean = true;
  private sessionId!: string;
  public authMethod: string | null = '';
  public msalInit = signal(false);
  public msalProcess = signal(false);
  public wSessionId:WritableSignal<string>= signal(uuidv4());
  constructor(@Optional() @Inject(publicClientApplicationService) config: IPublicClientApplication, public _cookie: CookieService, private _route: Router, private _http: HttpClient, private _sanitizer: DomSanitizer, private log: UsageDashboardLogService, private _uaePassService: UaePassService, private _jwtDecodeService: DecodeJwtTokenService) {
    this.msalInstance = config;
  }

  async handleRedirectPromiselogin() {
    await this.msalInstance.handleRedirectPromise();
    await this.login();
  }

  async handleRedirectPromise() {
    if(this.msalProcess()) {
      return;
    }
    this.msalProcess.set(true);
    await this.msalInstance.handleRedirectPromise().then((authResult: AuthenticationResult | null) => {
      this.msalProcess.set(false);
      // Check if user signed in
      if (authResult) {
        this.setTokenData(authResult);

        this.msalInstance.setActiveAccount(authResult.account);
        this.setAuth();
      } else if (!this.msalInstance.getActiveAccount()) {
        if (this.msalInstance.getAllAccounts()?.length === 1) {
          this.msalInstance.setActiveAccount(this.msalInstance.getAllAccounts()[0]);
        } else if (this.msalInstance.getAllAccounts()?.length > 1) {
          // we want to change it into account selection
          this.msalInstance.setActiveAccount(this.msalInstance.getAllAccounts()[0]);
        } else {
          // this.setLogin();
        }
      }
      this.startRefreshTokenTimer();
      this.silentToken = true;

      if (this.token) {
        this.sessionId = this.log.createUUid;
        (window as any)?.dataLayer?.push({
          'event': 'login',
          'userId': this.convertEmail(this.getLoginData.account.username)
        });
        this._http.get('https://graph.microsoft.com/v1.0/me/photo/$value', { responseType: 'blob' }).subscribe(
          {
            error: () => {
              this.profilePic.next(null);
            },
            next: (data) => {
              this.profilePic.next(this._sanitizer.bypassSecurityTrustUrl(URL.createObjectURL(data)));
            }
          }
        );
      } else {
        this.setLogin();
      }

    }).catch(() => {
      this.msalProcess.set(false);
      this.setLogin();
    });
  }

  async initialize() {
    await this.msalInstance.initialize();
    this.msalInit.set(true);
  }

  convertEmail(mail: any) {
    const specialCharacters = ['!', '`', '#', '$', '%', '^', '&', '*', '(', ')', '+'];
    if (mail) {
      let obfuscatedText = mail.replace('@', '_').replace('.', '-');
      obfuscatedText = `${this.randomChoice(specialCharacters, 3)}${obfuscatedText.split('').map((char: any) => `${char}${this.randomChoice(specialCharacters, 3)}`).join('')}${this.randomChoice(specialCharacters)}`;
      return obfuscatedText;
    }
  }

  randomChoice(arr: string | any[], count: number = 1) {
    const result = [];
    for (let i = 0; i < count; i++) {
      const randomIndex = Math.floor(Math.random() * arr.length);
      result.push(arr[randomIndex]);
    }
    return result.join('');
  }

  setAuth() {
    this.authCheckStatus = false;
    if (this.getCurrentUrl) {
      const url = this.codec.decodeValue(this.getCurrentUrl);
      this._route.navigateByUrl(url);
    } else {
      this._route.navigate(['/']);
    }
    this.removeCurrentUrl();

  }

  setLogin() {
    this.login();
  }

  login() {
    return new Promise(resolve => {
      this.msalInstance.loginRedirect().then(() => {
        resolve(true);
      }).catch((err)=> {
        console.info(err);
        // if(!this.getToken) {
        //   sessionStorage.clear();
        //   localStorage.clear();
        //   this.clearAll();
        //   this.deleteCookie('__Secure-next-auth.session-token');
        //   this._route.navigateByUrl('/login');
        // }

      });
    });
  }

  async logout() {
    const homeAccountId: string | any = this.msalInstance.getActiveAccount()?.homeAccountId;
    const homeId = { account: this.msalInstance.getAccountByHomeId(homeAccountId) };
    if (this.sessionId) {
      this.log.logEnds(this.sessionId, this.log.currentTime);
    }
    this.clearAll();
    this.deleteCookie('__Secure-next-auth.session-token');
    this.removeToken();
    this.stopRefreshTokenTimer();
    sessionStorage.clear();
    localStorage.clear();

    // await axios.get(environment.baseUrl + '/en/post-logout');

    // try {
    // const response = await lastValueFrom(this._http.get(`${environment.baseUrl}/eci/api/eci-logout`, {
    //   withCredentials: true
    // }));
    // } catch (error) {
    //   console.error('Error logging out of Next.js app:', error);
    // }
    this.removeNextAuthCookie();
    if (homeAccountId) {
      this.msalInstance.setActiveAccount(null);
      this.msalInstance.logoutRedirect(homeId);
      this._route.navigateByUrl('/login');
    } else {
      this._uaePassService.uaePassLogout();
      // const baseUrl = environment.env === 'staging' || environment.env === 'prod' ? window.origin : 'https://bayaan-staging.scad.gov.ae';
      // window.location.href = `https://stg-id.uaepass.ae/idshub/logout?redirect_uri=${baseUrl}/login`;
    }
  }

  private removeNextAuthCookie() {
    const cookieName = '__Secure-next-auth.session-token';
    const domains = [
      '.scad.gov.ae',
      'ifp-dev.scad.gov.ae',
      'scad.gov.ae'
    ];
    const paths = ['/', '/eci'];

    domains.forEach(domain => {
      paths.forEach(path => {
        document.cookie = `${cookieName}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=${path}; domain=${domain}; secure; samesite=lax`;
      });
    });
  }

  get getCurrentUser(): any {
    return this.msalInstance.getActiveAccount() ?? (JSON.parse(localStorage.getItem(accessAcount) ?? 'null'));
  }

  get getCurrentUsers() {
    return this.msalInstance.getAllAccounts();
  }

  acquireTokenSilent() {
    return new Promise((resolve) => {
      this.msalInstance.acquireTokenSilent({ scopes: environment.msalConfig.scopes, account: this.getCurrentUser, forceRefresh: true }).then((auth: AuthenticationResult) => {
        this.setTokenData(auth);
        this.setAuth();
        resolve(true);
      }).catch(() => {
        this.login();

      });
    });

  }


  callWorkerRefreshToken() {
    this.msalInstance.acquireTokenSilent({ scopes: environment.msalConfig.scopes, account: this.getCurrentUser, forceRefresh: true }).then((auth: AuthenticationResult) => {
      this.setTokenData(auth);
      if (this.getToken !== '') {
        this.setTokenData(auth);
        this.worker.postMessage({ time: this.experTime });
      } else if (this.worker) {
        this.worker.terminate();
      }
    }).catch(() => {
      this.login();
    });
  }

  setTokenData(data: AuthenticationResult) {
    this.sessionId = this.log.createUUid;
    this.token = data.idToken;
    this._uaePassService.uaePassToken = this.token;
    localStorage.setItem(idToken, data.idToken);
    localStorage.setItem(accessToken, data.accessToken);
    localStorage.setItem(accessAcount, JSON.stringify(data.account));
    localStorage.setItem(tokenName, JSON.stringify(data));
  }

  setCurrentUrl(url: string) {
    localStorage.setItem(currentUrl, url);
  }

  removeCurrentUrl() {
    localStorage.removeItem(currentUrl);
  }

  get getCurrentUrl() {
    return localStorage.getItem(currentUrl) ?? null;
  }

  get getLoginData() {
    return JSON.parse(localStorage.getItem(tokenName) ?? '{}');
  }

  get getToken() {
    if (this.token) {
      return this.token;
    }
    this.token = this.getLoginData?.idToken ?? null;
    return this.token;
  }

  get cacheToken() {
    return localStorage.getItem(cacheToken);
  }

  get getAccessToken() {
    return this.getLoginData?.accessToken ?? null;
  }

  get tokenExp() {
    if (this.getToken) {
      return + this.getTokenData(this.getToken)?.exp * 1000;
    }
    return null;
  }

  getTokenData(token: string) {
    return JSON.parse(Buffer.from(token.split('.')[1], 'base64').toString());
  }

  removeToken() {
    this.token = null;
    this._cookie.deleteAllCookies();
    localStorage.removeItem(tokenName);
    localStorage.removeItem('preventLogout');
  }

  refreshWorker() {
    if (this.experTime !== -1) {
      // if (typeof Worker !== 'undefined') {

      //   this.worker.onmessage = () => {
      //     if (this.getToken) {
      //       this.callWorkerRefreshToken();
      //     } else if (this.worker) {
      //       this.worker.terminate();
      //     }
      //   };
      //   this.worker.postMessage({ time: this.experTime });
      // } else {
      //   this.refereshTimeout();
      // }
      this.refereshTimeout();
    }
  }

  // used to get expery time
  get experTime(): number {
    if (this.tokenExp) {
      const expires = new Date(this.tokenExp);
      const timeout = expires.getTime() - Date.now() - (60 * 1000);
      return timeout;
    }
    return -1;
  }

  // used to start refersh tokern timmmer
  startRefreshTokenTimer() {
    if (this.worker) {
      this.worker.terminate();
    }
    if (this.refreshTokenTimeout) {
      clearTimeout(this.refreshTokenTimeout);
    }
    // if (typeof Worker !== 'undefined') {
    //   this.worker = new Worker(new URL('/src/app/core/workers/background-timer.worker', import.meta.url));
    // }
    this.worker = new Worker('src/app/scad-insights/core/worker/background-timer.worker.ts', { type: 'module' });
    this.refreshWorker();
  }



  stopRefreshTokenTimer() {
    if (this.worker) {
      this.worker.terminate();
    }
    if (this.refreshTokenTimeout) {
      clearTimeout(this.refreshTokenTimeout);
    }
  }

  // used to refresh using normal timer
  refereshTimeout() {
    if (this.experTime !== -1) {
      this.refreshTokenTimeout = setTimeout(() => {
        this.callrefreshtoken();
      }, this.experTime);
    }
  }

  callrefreshtoken() {
    if (this.refreshTokenTimeout) {
      clearTimeout(this.refreshTokenTimeout);
    }

    this.msalInstance.acquireTokenSilent({ scopes: environment.msalConfig.scopes, account: this.getCurrentUser, forceRefresh: true }).then((auth: AuthenticationResult) => {
      this.setTokenData(auth);
      this.refereshTimeout();
    }).catch(() => {
      this.login();
    });
  }

  getAll(): Record<string, string> {
    const cookies: Record<string, string> = {};
    document.cookie.split(';').forEach(cookie => {
      const [name, value] = cookie.split('=').map(c => c.trim());
      cookies[name] = decodeURIComponent(value);
    });
    return cookies;
  }

  clearAll() {
    const cookies = this.getAll();
    for (const cookieName in cookies) {
      if (cookies.hasOwnProperty(cookieName)) {
        this.deleteCookie(cookieName);
      }
    }
  }

  deleteCookie(name: string) {
    document.cookie = `${name}=; Path=/; Expires=Thu, 01 Jan 1970 00:00:01 GMT;`;
  }

  setUaePassUserData(userData: any) {
    const userInfo = this._jwtDecodeService.getTokenDetails(userData?.apiToken);
    const data = {
      authority: userInfo.platform,
      uniqueId: '',
      tenantId: '',
      scopes: [
        'profile',
        'openid',
        'email',
        'User.Read'
      ],
      account: {name: userData.userInfo.firstnameEN ?? userInfo.preferred_username.split('@')[0], username: userInfo.preferred_username, ...userInfo},
      idToken: userData.apiToken,
      idTokenClaims: userInfo,
      accessToken: userData.refreshToken,
      fromCache: false,
      expiresOn: userInfo?.exp,
      tokenType: '',
      correlationId: ''
    };
    localStorage.setItem(localStorageKeys.loginType, authTypes.uaePass);
    this.authMethod = authTypes.uaePass;
    this.setTokenData(data);
    this.setAuth();
    this._uaePassService.startRefreshTokenTimer();
  }
}

export const publicClientApplicationService = new InjectionToken<IPublicClientApplication>('config');
