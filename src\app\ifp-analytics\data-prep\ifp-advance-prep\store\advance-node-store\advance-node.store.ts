import { signalStore, withComputed, withMethods, withState, patchState } from '@ngrx/signals';
import { createNodeInitalStateAdvance } from './advance-node.state';
import { Signal, computed } from '@angular/core';
import { DeepSignal } from '@ngrx/signals/src/deep-signal';
import { PrepNode, PrepSelectConfiguration, Node } from '../../../ifp-data-prep/interface/ifp-data-prep.interface';
import { connectionType, connectionTypeConfigs } from '../../../ifp-data-prep/constants/if-preps.constants';
import { prepStatusStore } from '../../../ifp-data-prep/constants/ifp-state.contants';
import { cloneDeep } from 'lodash';

export const selectNodeStoreAdvance = signalStore(
  { protectedState: false }, withState(createNodeInitalStateAdvance),
  withComputed((data: {
    data: DeepSignal<PrepNode>;
    currentStatus: Signal<string>;
  }) => ({
    selectNodeStoreAdvance: computed(() => data),
    selectNodeValue: computed(() => {
      return {
        nodeValue: (objectId: string): any => {
          const lastIndex = data.data.nodes().findIndex(value => value.object_id === objectId);
          if (lastIndex !== -1) {
            return data.data.nodes()[lastIndex];
          }
          return {};
        }
      };
    }),
    selectNodeSourceConnection: computed(() => {
      return {
        sourceConnections: (objectId: string) => {
          return data.data.connections().filter(value => value.source === objectId);
        }
      };
    }),
    selectNodeDestinationConnection: computed(() => {
      return {
        destinationConnections: (objectId: string) => {
          return data.data.connections().filter(value => value.destination === objectId);
        }
      };
    }),
    selectSourceNodes: computed(() => {
      return { sourceNode: () => data.data.nodes().filter(value => value.type === connectionType.inputTool) };
    })
  })),
  withMethods((storeValue) => ({
    addUploadData(objectId: string, id: string, typeData?: string, settings?: any): void {
      const node = {
        object_id: objectId,
        name: typeData === connectionType.ifp ? 'Indicator Added Succesfully' : 'Data uploaded successfully',
        type: connectionType.inputTool,
        time: new Date(),
        settings: settings,
        configuration: {
          connection: {
            connection_type: typeData === connectionType.ifp ? connectionType.ifp : connectionType.dataset,
            path: id
          }
        }
      };
      patchState(storeValue, (states) => {
        states = cloneDeep(states)
        const lastIndex = states.data.nodes.findIndex(data => data.object_id === objectId);
        if (lastIndex !== -1) {
          const nodeSettings = { ...states.data.nodes[lastIndex].settings, ...node.settings };
          node.settings = nodeSettings;
          states.data.nodes[lastIndex] = node;
        } else {
          const nodeData = states.data.nodes;
          nodeData.push(node);
          states.data.nodes = nodeData;
        }
        states.currentStatus = prepStatusStore.datasetAdd;
        return (states);
      });
    },
    addNode(objectId: string, name: string, type: string, config: PrepSelectConfiguration, status: string, settings?: any): void {
      const node = {
        object_id: objectId,
        name: name,
        type: type,
        time: new Date(),
        configuration: config,
        settings: settings ?? {}
      };
      patchState(storeValue, (states) => {
        states = cloneDeep(states)
        const nodeData = states.data.nodes;
        nodeData.push(node);
        states.data.nodes = nodeData;
        states.currentStatus = status;
        return (states);
      });
    },
    updateNode(objectId: string, name: string, type: string, config: PrepSelectConfiguration, status: string, settings?: any): void {
      if (!objectId || objectId == '') {
        return;
      }
      const node: Node = {
        object_id: objectId,
        name: name,
        type: type,
        time: new Date(),
        configuration: config,
        settings: settings ?? {}
      };
      patchState(storeValue, (states) => {
        states = cloneDeep(states)
        const lastIndex = states.data.nodes.findIndex(data => data.object_id === objectId);
        if (lastIndex !== -1) {
          const nodeSettings = { ...states.data.nodes[lastIndex].settings, ...node.settings };
          node.settings = nodeSettings;
          states.data.nodes[lastIndex] = node;
        }
        states.currentStatus = status;
        return (states);
      });
    },
    removeNode(objectId: string, status: string): void {
      patchState(storeValue, (states) => {
        states = cloneDeep(states)
        const lastIndex = states.data.nodes.findIndex(data => data.object_id === objectId);
        if (lastIndex >= 0) {
          states.data.nodes.splice(lastIndex, 1);
        }
        const connectionDestination = states.data.connections.filter(data => data.destination === objectId);
        const connectionSource = states.data.connections.filter(data => data.source === objectId);
        connectionDestination.forEach(() => {
          const destinationIndex = states.data.connections.findIndex(data => data.destination === objectId);
          if (destinationIndex >= 0) {
            states.data.connections.splice(destinationIndex, 1);
          }
        });
        connectionSource.forEach(() => {
          const sourceIndex = states.data.connections.findIndex(data => data.destination === objectId);
          if (sourceIndex >= 0) {
            states.data.connections.splice(sourceIndex, 1);
          }
        });
        states.currentStatus = status;
        return (states);
      });
    },
    addConnection(objectIdSource: string, objectIdDestination: string, status: string, sourceAnchor: number, destinationAnchor: number, sourceOutputCount: number, destinationInputCount: number) {
      patchState(storeValue, (states) => {
        states = cloneDeep(states)
        const nodeIndex = states.data.nodes.findIndex(data => data.object_id === objectIdDestination);
        if (nodeIndex !== -1) {
          states.data.nodes[nodeIndex].settings = states.data.nodes[nodeIndex].settings ?? {};
        }
        // let connections: any;
        // if (destinationInputCount > 1) {
        const connections = {
          source: objectIdSource,
          source_anchor: sourceAnchor,
          destination: objectIdDestination,
          destination_anchor: destinationAnchor
        };
        // } else {
        //   connections ={
        //     source: objectIdSource,
        //     destination: objectIdDestination
        //   };
        // }
        states.data.connections.push(connections);
        states.currentStatus = status;
        return (states);
      });
    },
    removeConnection(objectIdSource: string, objectIdDestination: string, status: string) {
      patchState(storeValue, (states) => {
        states = cloneDeep(states)
        const connectionIndex = states.data.connections.findIndex(data => data.destination === objectIdDestination && data.source === objectIdSource);
        // const nodeIndex = states.data.nodes.findIndex(data => data.object_id === objectIdDestination);
        if (connectionIndex !== -1) {
          states.data.connections.splice(connectionIndex, 1);
        }
        // console.log("nodeIndex", nodeIndex)
        // if (nodeIndex !== -1) {
        //   states.data.nodes[nodeIndex].settings = {};
        //   states.data.nodes[nodeIndex].configuration = connectionTypeConfigs[states.data.nodes[nodeIndex].type];
        // }
        states.currentStatus = status;
        return (states);
      });
    },
    updateStore(storeData: any) {
      const connections = storeData.connections;
      const nodes = storeData.nodes;
      const name = storeData.name;
      const description = storeData.description;
      patchState(storeValue, (states) => {
        states = cloneDeep(states)
        states.data.nodes = nodes;
        states.data.connections = connections;
        states.data.name = name;
        states.data.description = description;
        states = {
          data: storeData,
          currentStatus: 'update'
        };
        return (states);
      });
    }
  })
  ));


