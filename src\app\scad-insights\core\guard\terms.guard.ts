
import { TermsCheckService } from '../services/terms-check.service';
import { Injectable } from '@angular/core';


@Injectable({
  providedIn: 'root'
})
export class TermsGuardService {

  constructor( private _termsService: TermsCheckService) {
    this._termsService.termsStatus=false;
  }

  resolve() {
    if (!this._termsService.termsStatus ){
      return this._termsService.isTnCAccepted();
    }
    return this._termsService.termsStatus;
  }
}
