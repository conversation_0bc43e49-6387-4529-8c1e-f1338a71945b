import { Component, Input, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, ViewChild } from '@angular/core';
import { SubscriptionLike } from 'rxjs';
import { SubSink } from 'subsink';
import { prepsApiEndpoints } from '../../data-prep/ifp-data-prep/constants/if-preps.constants';
import { IfpPanelDropdownComponent, PanelDropdownOptions } from '../../molecule/ifp-panel-dropdown/ifp-panel-dropdown.component';
import { PreivewPrep, MetadataColumnPrep } from '../../organism/ifp-data-preview/interface/ifp-data-prep.intreface';
import { TranslateModule } from '@ngx-translate/core';

import { ApiService } from 'src/app/scad-insights/core/services/api.service';
import { PaginationComponent } from '../../../scad-insights/ifp-widgets/ifp-molecules/pagination/pagination.component';
import { NgClass } from '@angular/common';
import { IfpTooltipDirective } from 'src/app/scad-insights/core/directives/ifp-tooltip.directive';
import { IfpNoDataComponent } from 'src/app/scad-insights/ifp-widgets/ifp-molecules/ifp-no-data/ifp-no-data.component';
import { IfpSpinnerComponent } from 'src/app/scad-insights/ifp-widgets/ifp-molecules/ifp-spinner/ifp-spinner.component';
import { IfpDataTableComponent } from '../../organism/ifp-data-table/ifp-data-table.component';
import { IfpModalComponent } from '../../../scad-insights/ifp-widgets/ifp-organism/ifp-modal/ifp-modal.component';
import { IfpButtonComponent } from '../../../scad-insights/ifp-widgets/ifp-atoms/ifp-button/ifp-button.component';
import { IfpExploratoryService } from '../services/ifp-exploratory.service';
import { buttonClass } from 'src/app/scad-insights/core/constants/button.constants';
import { DownLoadService } from 'src/app/scad-insights/core/services/download-service/download.service';
import { environment } from 'src/environments/environment';
import { ToasterService } from 'src/app/scad-insights/core/services/tooster/ToastrService.service';
import { IfpModalService } from 'src/app/scad-insights/ifp-widgets/ifp-organism/ifp-modal/ifp-modal.service';
import { TableSettings, tableTypes, IfpDataSummaryComponent } from '../../organism/ifp-data-summary/ifp-data-summary.component';
import { ifpColors } from 'src/app/scad-insights/core/constants/color.constants';

@Component({
    selector: 'ifp-exploratory-summery-preview',
    imports: [PaginationComponent, TranslateModule, IfpPanelDropdownComponent, IfpDataTableComponent, NgClass, IfpTooltipDirective, PaginationComponent, IfpNoDataComponent, IfpSpinnerComponent, IfpModalComponent, IfpButtonComponent, IfpDataSummaryComponent],
    templateUrl: './ifp-exploratory-summery-preview.component.html',
    styleUrl: './ifp-exploratory-summery-preview.component.scss'
})
export class IfpExploratorySummeryPreviewComponent implements OnChanges, OnDestroy{
  @ViewChild('modalUpdate') modalUpdate!: IfpModalComponent;




    @Input() tableHead: string[] = [];
    @Input() tableData!: any[];
    @Input() tableView: 'normal' | 'detail' = 'detail';

    @Input() id!:string;

    public offset: number = 0;
    public page: number = 1;
    public limit: number = 10;
    public size: number = 10;
    public subs = new SubSink();
    public dataValue!:PreivewPrep;
    public metaData = false;
    public responseTime: string = '0';
    public metaDataValue!: MetadataColumnPrep[];
    public pagination = false;


    public columListDropdown: PanelDropdownOptions[] = [];
    public tableHeadNormal: string[] = [];
    public tableHeadDetail: string[] = ['Column Name', 'Data Type'];
    public currentSelectedValue: PanelDropdownOptions[] = [];

    public tableDataNormal:Record<string, string>[][] = [];

    public tableDataDetail:MetadataColumnPrep[] = [];
    public buttonClass = buttonClass;
    public loader = false;
    public previousY: number | null = null;
    public previewApi!:SubscriptionLike;
    public storeSub!:SubscriptionLike;
    public processSub!:SubscriptionLike;
    public totalRow?: number;
    public totalColumn?: number;
    // summery table Settings
    public summeryTableSettings :TableSettings = {
      withId: true,
      setting: [
        {
          key: 'name',
          name: 'Variables'
        },
        {
          key: 'type',
          name: 'Data Type',
          type: tableTypes.bracket,
          secondKey: 'type_description'
        },
        {
          key: 'Stats / Values',
          name: 'Stats / Values',
          type: tableTypes.table,
          tableSettings: [{
            key: 'min_value',
            funName: (column, row) => row.type === 'string' ?   'Total count': 'Min',
            name: '',
            fun: (column, row) => row.type === 'string' ?   row.null_count + row.valid_count : row.min_value
          },
          {
            key: 'median',
            funName: (column, row) => row.type === 'string' ?   'Unique values count' : 'Median',
            name: '',
            fun: (column, row) =>  row.type === 'string' ?   row.unique_count : row.median
          },
          {
            key: 'max_value',
            funName: (column, row) => row.type === 'string' ?   'Unique values percentage' : 'Max',
            name: '',
            fun: (column, row) =>  row.type === 'string' ?  (row.unique_percentage ? `${row.unique_percentage}%` : null) : row.max_value
          }
          ]
        },
        {
          key: 'valid_count',
          name: 'Non-null Values',
          type: tableTypes.progress,
          precentageKey: 'valid_percentage',
          color: ifpColors.green
        },
        {
          key: 'null_count',
          name: 'Null Values',
          type: tableTypes.progress,
          precentageKey: 'null_percentage',
          reverse: true

        }
      ]
    };

    constructor( public _preService:ApiService,   public _exploratory: IfpExploratoryService,   private _downloadService: DownLoadService,  private _toast: ToasterService, private _modalService: IfpModalService) {}

    ngOnChanges(): void {
      if (this.id) {
        this.callDatasetPreview();
      }

    }

    // trigger while we clicking on column dropdown
    multiSelected(event: PanelDropdownOptions[] ){
      this.currentSelectedValue = event;
      this.setTable();
      this.setUpMetaDataNormal();
      this.setTableView(this.tableView);
    }

    // change summery and preview table
    setTableView(type: 'normal' | 'detail') {
      this.tableView = type;
      if (type === 'detail') {
        this._exploratory.previewTableData = {
          heading: this.tableHeadDetail,
          data: this.tableDataDetail
        };
        return;
      }
      this._exploratory.previewTableData = {
        heading: this.tableHeadNormal,
        data: this.tableDataNormal
      };

    }


    // Download preview of the data
    downloadFileClick() {
      this.subs.add(this._preService.getDownloadRequest(`${environment.prepbaseUrl}/v1/dataset/${this.id}/download/csv/`).subscribe( {next: data=> {
        const matches = (data.headers.get('Content-Disposition'))?.match(/"(.*?)"/);
        const nameValue = matches ? matches[1] : '.csv';
        this._downloadService.downloadFiles(data.body, nameValue);
      },
      error: err => {
        const error = err?.error;
        this.errorHandler(error);
      }
      }) );
    }

    // error message popup
    errorHandler(error: any) {
      if (typeof error === 'string' ) {
        if (error!== '') {
          this._toast.error(error);
        }
      } else {
        for (const key in error) {
          if (Object.prototype.hasOwnProperty.call(error, key)) {
            const element = error[key];
            let data = '';
            if (typeof element === 'string' ) {
              data = element;
            } else {
              element.forEach((elementValue:string) => {
                data =  `${data} ${elementValue}`;
              });
            }
            if (data!== '') {
              this._toast.error(data);
            }
          }
        }
      }
    }

    // used to change page of preview
    onPageChange(offset: {page: number, offset: number}) {
      this.offset = offset.offset;
      this.page = offset.page;
      this.pagination = true;
      this.callDatasetPreview();
    }

    // preview and summery table api call
    callDatasetPreview() {
      this.loader = true;
      const startTime = performance.now();
      this.previewApi?.unsubscribe();
      this.previewApi =   this._preService.getMethodRequest(prepsApiEndpoints.libraryDataset  + this.id + prepsApiEndpoints.libraryPreview, {limit: this.limit, offset: this.offset, page: this.page}, true ).subscribe({next: (data:PreivewPrep)=> {
        const endTime = performance.now();
        const responseTime = endTime - startTime;
        this.responseTime = responseTime.toFixed(0);
        this.dataValue = data;
        this.size = data.count;
        if (data?.metadata?.columns) {
          this.metaData = true;
          this.metaDataValue = this.dataValue.metadata?.columns ?? [];
          this.totalRow = this.dataValue.total_rows;
          this.totalColumn = this.dataValue.total_columns;
        }
        this.setUpTableValue();
        this.setUpMetaDataNormal();
        this._exploratory.previewTableData = {
          heading: this.tableHeadNormal,
          data: this.tableDataNormal
        };
        this.loader = false;
      }
      });
    }

    // open modal function
    openModal() {
      this.modalUpdate?.createElement();
      this.tableView= 'detail';
    }

    // close modal function
    removeModal(){
      this.modalUpdate?.removeModal();
      this._modalService.removeAllModal();
    }

    // change to table format
    setUpTableValue(){
      if (!this.pagination) {
        this.columListDropdown = [];
        if (this.metaDataValue) {
          const keys = this.metaDataValue;
          keys.forEach((data: MetadataColumnPrep) => {
            this.columListDropdown.push({key: data.name, value: data.name, checked: true});
          });
        } else {
          const keys =  Object.keys(this.dataValue.records[0]??{});
          keys.forEach((data:string ) => {
            this.columListDropdown.push({key: data, value: data, checked: true});
          });
        }
        this.currentSelectedValue =  this.columListDropdown;
      }
      this.pagination = false;
      this.setTable();
    }

    // setup table data based on the selection dropdown
    setTable() {
      this.tableDataNormal = [];
      this.tableHeadNormal = [];
      this.dataValue.records.forEach((element, index) => {
        this.tableDataNormal.push([]);
        this.currentSelectedValue.forEach(selectedValue => {
          this.tableDataNormal[index].push(
            { key: selectedValue.key, title: element[selectedValue.key], value: element[selectedValue.key], type: 'default' }
          );
        });
      });
      this.currentSelectedValue.forEach(data=> {
        this.tableHeadNormal.push(data.key);
      });
    }

    // setup table data based on the selection dropdown
    setUpMetaDataNormal() {
      this.tableDataDetail = [];
      this.metaDataValue?.forEach((element:MetadataColumnPrep) => {
        this.currentSelectedValue.forEach(data=> {
          if (data.key === element.name) {
            this.tableDataDetail.push(element);
          }

        });
      });


    }

    ngOnDestroy(): void {
      this._modalService.removeAllModal();
      this.subs.unsubscribe();
      this.previewApi?.unsubscribe();
    }

}
