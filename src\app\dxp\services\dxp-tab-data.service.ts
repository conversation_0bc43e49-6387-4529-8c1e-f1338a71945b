import { Injectable, signal } from '@angular/core';
import { BehaviorSubject } from 'rxjs';

export interface TabSelectionData {
  param: string;
  subMenus?: any[];
  selectedMenuItem?: any;
}

@Injectable({
  providedIn: 'root'
})
export class DxpTabDataService {
  // Use BehaviorSubject to emit data to indicator component
  private tabSelectionSubject = new BehaviorSubject<TabSelectionData>({ param: 'pending' });
  public tabSelection$ = this.tabSelectionSubject.asObservable();

  constructor() { }

  // Method to emit tab selection data
  emitTabSelection(data: TabSelectionData) {
    this.tabSelectionSubject.next(data);
  }

  // Method to get current tab selection
  getCurrentTabSelection(): TabSelectionData {
    return this.tabSelectionSubject.value;
  }
}
