import { Component, signal } from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';
import { IfpBreadcrumbsComponent } from "../../scad-insights/ifp-widgets/ifp-molecules/ifp-breadcrumbs/ifp-breadcrumbs.component";
import { IfpDropdownComponent } from "../../scad-insights/ifp-widgets/ifp-atoms/ifp-dropdown/ifp-dropdown.component";
import { IfpPanelDropdownComponent, PanelDropdownOptions } from "../../ifp-analytics/molecule/ifp-panel-dropdown/ifp-panel-dropdown.component";
import { IfpUserTagGroupComponent, UserDetail } from "../../scad-insights/ifp-widgets/ifp-molecules/ifp-user-tag-group/ifp-user-tag-group.component";
import { IfpButtonComponent } from "../../scad-insights/ifp-widgets/ifp-atoms/ifp-button/ifp-button.component";
import { buttonClass } from '../../scad-insights/core/constants/button.constants';
import { PageData } from 'src/app/scad-insights/core/interface/molecule/breadcrumb.interface';

@Component({
  selector: 'ifp-dashboard-publish',
  imports: [TranslateModule, IfpBreadcrumbsComponent, IfpDropdownComponent, IfpPanelDropdownComponent, IfpUserTagGroupComponent, IfpButtonComponent],
  templateUrl: './dashboard-publish.component.html',
  styleUrl: './dashboard-publish.component.scss'
})
export class DashboardPublishComponent {
  public buttonClass = buttonClass;
  public   pageData: PageData[] = [
      {
        title: 'Home',
        route: '/home'
      },
      {
        title: 'Government Affairs',
        route: '/dxp'
      },
      {
        title: 'Abu Dhabi Executive Office (ADEO)',
        route: ''
      }
    ];
  public sharedUserListSelected = signal<UserDetail[]>([{
      name: 'Ahmad Nadheem',
      email: '<EMAIL>'
    }]);
  public sharedUserList = signal<PanelDropdownOptions[]>([
    {
      value: 'Ahmad Nadheem',
      key: '<EMAIL>',
      checked: true
    },
    {
      value: 'Test User',
      key: '<EMAIL>',
       checked: false
    },
    {
      value: 'Nadheem Ahmad',
      key: '<EMAIL>',
       checked: false
    },
    {
      value: 'User Test',
      key: '<EMAIL>',
       checked: false
    }
  ]) ;
  public currentSelection =signal<PanelDropdownOptions[]>([]);

  removeData(event:{tag:UserDetail, index: number}){
    this.sharedUserListSelected.update(data => {
      data.splice(event.index, 1);
      return data;
    })
   this.currentSelection.set(
    this.sharedUserListSelected().map(data => {
      return { key: data.email ?? '', value: data.name, checked: true };
    })
   );
   this.sharedUserList.update(data => {

    data.forEach(user => {
     user.checked = false;
    this.sharedUserListSelected().forEach(value => {

        if(value.email === user.key ) {
            user.checked = true;
        }
    });
    });

    return data;
   })
   console.log(this.sharedUserList(),    this.currentSelection())
  }

  dropDownItemMultiClicked(event: PanelDropdownOptions[]) {
    console.log(event);
    this.sharedUserListSelected.set(event.map(data => {return {name: data.value, email:data.key}}));
      if (this.sharedUserListSelected()?.length <= 0) {
      this.sharedUserList.update(data=> {
        data[0].checked = true
       return  data;
      });
      this.currentSelection.set([this.sharedUserList()[0]]);
      this.sharedUserListSelected.set([{name: this.sharedUserList()[0].value , email:this.sharedUserList()[0].key}]);
    }
    }
  }

