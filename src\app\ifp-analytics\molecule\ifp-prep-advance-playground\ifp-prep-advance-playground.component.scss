@use "../../../../assets/ifp-styles/abstracts/index" as *;

:host {
  display: block;
}

.ifp-p-adv {
  &__body-content {
    height: calc(100vh - 318px);
    position: relative;
    &--disable-preview {
      height: calc(100vh + 100px);
    }
  }

  &__actions {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    margin-top: $spacer-4;
    width: 100%;
    position: absolute;
    top: 32px;
    left: 0;
    z-index: 1;

    .ifp-icon {
      font-size: $ifp-fs-6;
      padding: $spacer-2;
      cursor: pointer;
    }

    &--collpase {
      left: auto;
      right: 415px;
    }
  }

  &__tools {
    display: flex;
    align-items: center;
    cursor: grab;
    padding: $spacer-1;
    background-color: $ifp-color-black-50;
    border: 1px solid $ifp-color-black;
    border-radius: 10px;
    position: fixed;
    top: 80%;
    left: calc(100% - 500px);
    .ifp-icon {
      margin: $spacer-0 $spacer-2;
      color: $ifp-color-white-global;
      font-size: $ifp-fs-7;
      cursor: pointer;

      &-active {
        color: $ifp-color-blue-hover;
      }
    }
  }


  &__range-selector {
    border-inline-start: 1px solid rgba(255, 255, 255, 0.3);
    display: flex;
    align-items: center;
    padding: $spacer-0 $spacer-2;
  }

  &__label {
    color: $ifp-color-white;
    margin-inline-start: $spacer-1;
  }

}

:host::ng-deep {
  .ifp-p-adv {
    &__body {
      position: relative;
      background-color: $ifp-color-grey-bg;

      .them-edit-link {
        position: absolute;
        top: 10px;
        right: 100px;
        color: black;
        font-size: 40px;
      }

      .them-edit-link a {
        text-decoration: none;
      }

      .github-link {
        position: absolute;
        top: 10px;
        right: 20px;
        color: black;
      }

      .wrapper {
        width: 100%;
        height: calc(100vh - 67px);
        display: flex;
      }

      .col {
        overflow: auto;
        width: 300px;
        height: 100%;
        border-inline-end: 1px solid $ifp-color-grey-disabled;
      }

      .drag-drawflow {
        line-height: 50px;
        /* border-bottom: 1px solid $ifp-color-grey-disabled; */
        padding-inline-start: 20px;
        cursor: move;
        user-select: none;
      }

      .menu {
        position: absolute;
        height: 40px;
        display: block;
        background: var(--color);
        width: 100%;
        // background-color: var(--background-color);
      }

      .menu ul {
        padding: 0px;
        margin: 0px;
        line-height: 40px;
      }

      .menu ul li {
        display: inline-block;
        margin-inline-start: 10px;
        /* border-right: 1px solid $ifp-color-grey-disabled; */
        padding-inline-end: 10px;
        line-height: 40px;
        cursor: pointer;
      }

      .menu ul li.selected {
        font-weight: bold;
      }

      .btn-clear {
        float: right;
        position: absolute;
        top: 10px;
        right: 10px;
        color: var(--color);
        font-weight: bold;
        border: 1px solid #0e5ba3;
        background: $ifp-color-grey-disabled;
        padding: 5px 10px;
        border-radius: 4px;
        cursor: pointer;
        z-index: 5;
      }

      .btn-lock {
        float: right;
        position: absolute;
        bottom: 10px;
        right: 140px;
        display: flex;
        font-size: 24px;
        color: var(--color);
        padding: 5px 10px;
        background: #555555;
        border-radius: 4px;
        border-inline-end: 1px solid $ifp-color-grey-disabled;
        z-index: 5;
        cursor: pointer;
      }

      .bar-zoom {
        float: right;
        position: absolute;
        bottom: 10px;
        right: 10px;
        display: flex;
        font-size: 24px;
        color: var(--color);
        padding: 5px 10px;
        background: #555555;
        border-radius: 4px;
        border-inline-end: 1px solid $ifp-color-grey-disabled;
        z-index: 5;
      }

      .bar-zoom svg {
        cursor: pointer;
        padding-inline-start: 10px;
      }

      .bar-zoom svg:nth-child(1) {
        padding-inline-start: 0px;
      }

      #drawflow {
        position: relative;
        width: 100%;
        height: 100% // top: 40px;
          // background: var(--background-color);
      }

      @media only screen and (max-width: 768px) {
        .col {
          width: 50px;
        }

        .col .drag-drawflow span {
          display: none;
        }

        #drawflow {
          width: calc(100vw - 70px);
        }
      }

      /* Editing Drawflow */
      .drawflow .drawflow-node {
        display: flex;
        align-items: center;
        position: absolute;
        z-index: 2;
        // background: var(--background-color);
        // border: 1px solid $ifp-color-grey-disabled;
        // -webkit-box-shadow: 0 2px 15px 2px $ifp-color-grey-disabled;
        // box-shadow: 0 2px 15px 2px $ifp-color-grey-disabled;
        padding: 0px;
        // width: 200px;
        color: var(--color);
      }

      .drawflow .drawflow-node.selected {

        .ifp-p-adv &__source,
        &__select,
        &__impute,
        &__destination,
        &__append {
          border: 1px dashed $ifp-color-blue-hover;
        }

        &__source {
          background-color: $ifp-color-blue-hover;
        }

        &__select {
          background-color: $ifp-color-yellow-menu;
        }

        &__impute {
          background-color: $ifp-color-red;
        }

        &__append {
          background-color: $ifp-color-blue-menu;
        }

        &__destination {
          background-color: $ifp-color-purple;
        }

        // background: var(--color);
        // border: 1px solid $ifp-color-grey-disabled;
        // -webkit-box-shadow: 0 2px 20px 2px $ifp-color-grey-disabled;
        // box-shadow: 0 2px 20px 2px $ifp-color-grey-disabled;
      }

      .drawflow .drawflow-node.selected .title-box {
        color: var(--background-color);
        background: $ifp-color-grey-disabled;
        border-bottom: 1px solid $ifp-color-grey-disabled;
      }

      .drawflow .drawflow-node .output {
        right: 10px;
      }

      .drawflow .drawflow-node .input {
        left: -10px;
      }

      .drawflow>.drawflow-delete {
        border: 2px solid $ifp-color-red;
        background: $ifp-color-pale-red;
        color: $ifp-color-red;
      }

      .drawflow-delete {
        border: 2px solid $ifp-color-grey-disabled;
        background: var(--color);
        color: $ifp-color-grey-disabled;
      }

      .drawflow-node .title-box {
        height: 50px;
        line-height: 50px;
        background: var(--background-box-title);
        /* border-bottom: 1px solid $ifp-color-grey-disabled; */
        border-radius: 4px 4px 0px 0px;
        padding-inline-start: 10px;
      }

      .drawflow .title-box svg {
        position: initial;
      }

      .drawflow-node .box {
        padding: 10px 20px 20px 20px;
        font-size: 14px;
        color: var(--color);
      }

      .drawflow-node .box p {
        margin-top: 5px;
        margin-bottom: 5px;
      }

      .drawflow .drawflow-node.selected .box {
        color: var(--background-color);
      }

      .drawflow-node.welcome {
        width: 250px;
      }

      .drawflow-node.slack .title-box {
        border-radius: 4px;
      }

      .drawflow-node input,
      .drawflow-node select,
      .drawflow-node textarea {
        border-radius: 4px;
        border: 1px solid $ifp-color-grey-disabled;
        height: 30px;
        line-height: 30px;
        font-size: 16px;
        width: 158px;
        color: #555555;
      }

      .drawflow-node textarea {
        height: 100px;
      }

      .drawflow-node.personalized {
        background: red;
        height: 200px;
        text-align: center;
        color: var(--color);
      }

      .drawflow-node.personalized .input {
        background: yellow;
      }

      .drawflow-node.personalized .output {
        background: green;
      }

      .drawflow-node.personalized.selected {
        background: blue;
      }

      .drawflow .connection .point {
        stroke: $ifp-color-grey-disabled;
        stroke-width: 2;
        fill: var(--color);
        transform: translate(-9999px, -9999px);
      }

      .drawflow .connection .point.selected,
      .drawflow .connection .point:hover {
        fill: $ifp-color-grey-disabled;
      }

      /* Modal */
      .modal {
        display: none;
        position: fixed;
        z-index: 7;
        left: 0;
        top: 0;
        width: 100vw;
        height: 100vh;
        overflow: auto;
        /* background-color: rgb(0, 0, 0);
          background-color: rgba(0, 0, 0, 0.7); */
        background-color: #333;
        color: #eee;
      }

      .modal-content {
        position: relative;
        /* background-color: #fefefe; */
        background-color: #333;
        color: #eee;
        margin: 15% auto;
        /* 15% from the top and centered */
        padding: 20px;
        border: 1px solid #888;
        width: 400px;
        /* Could be more or less, depending on screen size */
      }

      /* The Close Button */

      .modal .close {
        color: #aaa;
        float: right;
        font-size: 28px;
        font-weight: bold;
        cursor: pointer;
      }

      @media only screen and (max-width: 768px) {
        .modal-content {
          width: 80%;
        }
      }

      /* -------------------------------------------------------------------------------- */
      /* -------------------------------------------------------------------------------- */
      /* -------------------------------------------------------------------------------- */
      /* -------------------------------------------------------------------------------- */

      .drawflow,
      .drawflow .parent-node {
        position: relative;
      }

      .parent-drawflow {
        display: flex;
        overflow: hidden;
        touch-action: none;
        outline: 0;
      }

      .drawflow {
        width: 100%;
        height: 100%;
        user-select: none;
      }

      .drawflow .drawflow-node:hover {
        cursor: move;
      }

      .drawflow .drawflow-node .inputs,
      .drawflow .drawflow-node .outputs {
        width: 0;
      }

      .drawflow .drawflow-node .drawflow_content_node {
        width: 100%;
        display: block;
      }

      .drawflow .drawflow-node .input,
      .drawflow .drawflow-node .output {}



      .drawflow .drawflow-node {

        .output,
        .input {
          position: relative;
          width: 16px;
          height: 14px;
          cursor: crosshair;
          z-index: 1;
          background-size: contain;
          background-repeat: no-repeat;
          background-position: center;
        }

        .input {
          background-image: url("data:image/svg+xml,%3Csvg width='19' height='16' viewBox='0 0 19 16' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath fill-rule='evenodd' clip-rule='evenodd' d='M5.06304 6.96467C5.44815 7.60117 5.44815 8.39883 5.06304 9.03533L1.45525 14.9982C1.45514 14.9984 1.45508 14.9986 1.45508 14.9988C1.45508 14.9995 1.45561 15 1.45627 15H12.0923C12.7922 15 13.4411 14.6341 13.8035 14.0353L16.8287 9.03533C17.2138 8.39883 17.2138 7.60117 16.8287 6.96467L13.8035 1.96467C13.4411 1.36586 12.7922 1 12.0923 1H1.45627C1.45561 1 1.45508 1.00053 1.45508 1.00119C1.45508 1.0014 1.45514 1.00162 1.45525 1.0018L5.06304 6.96467Z' fill='%23697D92'/%3E%3Cpath d='M1.45525 1.0018L0.599666 1.51947L1.45525 1.0018ZM13.8035 1.96467L14.659 1.44701L13.8035 1.96467ZM16.8287 6.96467L17.6842 6.44701L16.8287 6.96467ZM13.8035 14.0353L12.9479 13.5177L13.8035 14.0353ZM5.06304 9.03533L4.20745 8.51767L5.06304 9.03533ZM1.45525 14.9982L0.599666 14.4805L1.45525 14.9982ZM2.31083 15.5159L5.91862 9.55299L4.20745 8.51767L0.599666 14.4805L2.31083 15.5159ZM12.0923 14H1.45627V16H12.0923V14ZM15.9731 8.51766L12.9479 13.5177L14.659 14.553L17.6842 9.55299L15.9731 8.51766ZM12.9479 2.48234L15.9731 7.48234L17.6842 6.44701L14.659 1.44701L12.9479 2.48234ZM1.45627 2H12.0923V0H1.45627V2ZM5.91862 6.44701L2.31083 0.484138L0.599666 1.51947L4.20745 7.48234L5.91862 6.44701ZM0.455078 1.00119C0.455078 1.18374 0.504996 1.363 0.599666 1.51947L2.31083 0.484138C2.40528 0.640235 2.45508 0.819067 2.45508 1.00119H0.455078ZM1.45627 0C0.903325 0 0.455078 0.448247 0.455078 1.00119H2.45508C2.45508 1.55282 2.00789 2 1.45627 2V0ZM14.659 1.44701C14.1156 0.548787 13.1421 0 12.0923 0V2C12.4422 2 12.7667 2.18293 12.9479 2.48234L14.659 1.44701ZM17.6842 9.55299C18.2619 8.59824 18.2619 7.40176 17.6842 6.44701L15.9731 7.48234C16.1656 7.80059 16.1656 8.19942 15.9731 8.51766L17.6842 9.55299ZM12.0923 16C13.1421 16 14.1156 15.4512 14.659 14.553L12.9479 13.5177C12.7667 13.8171 12.4422 14 12.0923 14V16ZM0.455078 14.9988C0.455078 15.5526 0.904158 16 1.45627 16V14C2.00706 14 2.45508 14.4464 2.45508 14.9988H0.455078ZM5.91862 9.55299C6.49628 8.59824 6.49628 7.40176 5.91862 6.44701L4.20745 7.48234C4.40001 7.80059 4.40001 8.19941 4.20745 8.51767L5.91862 9.55299ZM0.599666 14.4805C0.50508 14.6369 0.455078 14.8161 0.455078 14.9988H2.45508C2.45508 15.1811 2.40519 15.3599 2.31083 15.5159L0.599666 14.4805Z' fill='%23F4F7F6'/%3E%3C/svg%3E%0A");
        }

        .output {
          background-image: url("data:image/svg+xml,%3Csvg width='19' height='16' viewBox='0 0 19 16' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath fill-rule='evenodd' clip-rule='evenodd' d='M5.06304 6.96467C5.44815 7.60117 5.44815 8.39883 5.06304 9.03533L1.45525 14.9982C1.45514 14.9984 1.45508 14.9986 1.45508 14.9988C1.45508 14.9995 1.45561 15 1.45627 15H12.0923C12.7922 15 13.4411 14.6341 13.8035 14.0353L16.8287 9.03533C17.2138 8.39883 17.2138 7.60117 16.8287 6.96467L13.8035 1.96467C13.4411 1.36586 12.7922 1 12.0923 1H1.45627C1.45561 1 1.45508 1.00053 1.45508 1.00119C1.45508 1.0014 1.45514 1.00162 1.45525 1.0018L5.06304 6.96467Z' fill='%23697D92'/%3E%3Cpath d='M1.45525 1.0018L0.599666 1.51947L1.45525 1.0018ZM13.8035 1.96467L14.659 1.44701L13.8035 1.96467ZM16.8287 6.96467L17.6842 6.44701L16.8287 6.96467ZM13.8035 14.0353L12.9479 13.5177L13.8035 14.0353ZM5.06304 9.03533L4.20745 8.51767L5.06304 9.03533ZM1.45525 14.9982L0.599666 14.4805L1.45525 14.9982ZM2.31083 15.5159L5.91862 9.55299L4.20745 8.51767L0.599666 14.4805L2.31083 15.5159ZM12.0923 14H1.45627V16H12.0923V14ZM15.9731 8.51766L12.9479 13.5177L14.659 14.553L17.6842 9.55299L15.9731 8.51766ZM12.9479 2.48234L15.9731 7.48234L17.6842 6.44701L14.659 1.44701L12.9479 2.48234ZM1.45627 2H12.0923V0H1.45627V2ZM5.91862 6.44701L2.31083 0.484138L0.599666 1.51947L4.20745 7.48234L5.91862 6.44701ZM0.455078 1.00119C0.455078 1.18374 0.504996 1.363 0.599666 1.51947L2.31083 0.484138C2.40528 0.640235 2.45508 0.819067 2.45508 1.00119H0.455078ZM1.45627 0C0.903325 0 0.455078 0.448247 0.455078 1.00119H2.45508C2.45508 1.55282 2.00789 2 1.45627 2V0ZM14.659 1.44701C14.1156 0.548787 13.1421 0 12.0923 0V2C12.4422 2 12.7667 2.18293 12.9479 2.48234L14.659 1.44701ZM17.6842 9.55299C18.2619 8.59824 18.2619 7.40176 17.6842 6.44701L15.9731 7.48234C16.1656 7.80059 16.1656 8.19942 15.9731 8.51766L17.6842 9.55299ZM12.0923 16C13.1421 16 14.1156 15.4512 14.659 14.553L12.9479 13.5177C12.7667 13.8171 12.4422 14 12.0923 14V16ZM0.455078 14.9988C0.455078 15.5526 0.904158 16 1.45627 16V14C2.00706 14 2.45508 14.4464 2.45508 14.9988H0.455078ZM5.91862 9.55299C6.49628 8.59824 6.49628 7.40176 5.91862 6.44701L4.20745 7.48234C4.40001 7.80059 4.40001 8.19941 4.20745 8.51767L5.91862 9.55299ZM0.599666 14.4805C0.50508 14.6369 0.455078 14.8161 0.455078 14.9988H2.45508C2.45508 15.1811 2.40519 15.3599 2.31083 15.5159L0.599666 14.4805Z' fill='%23F4F7F6'/%3E%3C/svg%3E%0A");
        }
      }

      .drawflow svg {
        z-index: 0;
        position: absolute;
        overflow: visible !important;
      }

      .drawflow .connection {
        position: absolute;
        transform: translate(9999px, 9999px);
      }

      .drawflow .connection .main-path {
        fill: none;
        stroke-width: 3px;
        stroke: $ifp-color-grey-disabled;
        transform: translate(-9999px, -9999px);
        cursor: pointer;
      }

      .drawflow .connection .main-path:hover {
        stroke: $ifp-color-grey-disabled;
      }

      .drawflow .connection .main-path.selected {
        stroke: $ifp-color-red-light;
      }

      .drawflow .connection .point {
        cursor: move;
        stroke: $ifp-color-black;
        stroke-width: 2;
        fill: $ifp-color-white;
        transform: translate(-9999px, -9999px);
      }

      .drawflow .connection .point.selected,
      .drawflow .connection .point:hover {
        fill: $ifp-color-grey-disabled;
      }

      .drawflow .main-path {
        fill: none;
        stroke-width: 3px;
        stroke: $ifp-color-grey-disabled;
      }

      .drawflow .selectbox {
        z-index: 3;
        position: absolute;
        transform: translate(9999px, 9999px);
      }

      // .drawflow .selectbox rect {
      //   fill: #00f;
      //   opacity: 0.5;
      //   stroke: #ff0;
      //   stroke-width: 3;
      //   stroke-opacity: 0.5;
      //   transform: translate(-9999px, -9999px);
      // }

      .drawflow-delete {
        position: absolute;
        display: block;
        width: 30px;
        height: 30px;
        border: 2px solid $ifp-color-white;
        color: $ifp-color-red;
        background: $ifp-color-black;
        color: $ifp-color-white;
        z-index: 4;
        line-height: 26px;
        font-weight: 700;
        text-align: center;
        border-radius: 50%;
        font-family: monospace;
        cursor: pointer;
      }

      .drawflow>.drawflow-delete {
        margin-inline-start: -15px;
        margin-top: 15px;
      }

      .parent-node .drawflow-delete {
        right: -15px;
        top: -15px;
      }
    }
    &__tool-error {
      display: none;
    }
    &__tool {
      text-align: center;
      border-radius: 12px;
      padding: $spacer-2;
      width: 80px;
      border: 1px solid $ifp-color-grey-disabled;
      background-color: $ifp-color-white-global;

      &--active {
        .ifp-p-adv__icon-txt {
          color: $ifp-color-white;
        }

        .ifp-icon {
          color: $ifp-color-white !important;
        }
      }
    }

    &__icon-txt {
      width: 100%;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      text-transform: capitalize;
      color: $ifp-color-grey-9;
    }

    &__drawflow {
      .ifp-icon {
        font-size: $ifp-fs-11;
      }

      &--grid {
        // background: $ifp-color-white;
        background-size: 25px 25px;
        background-image: linear-gradient(to right, $ifp-color-grey-7 1px, transparent 1px), linear-gradient(to bottom, $ifp-color-grey-7 1px, transparent 1px);
      }
    }

    &__col-right {
      width: 100%;
    }

    &__minimap {
      position: fixed;
      width: 400px;
      height: 150px;
      background-color: $ifp-color-grey-7;
      border: 1px solid $ifp-color-grey-9;
      bottom: 100px;
      right: 10px;
      padding: $spacer-1;
    }

    &__drag-rect {
      height: 140px;
      width: 100px;
      border: 1px solid $ifp-color-red;
      cursor: move;
      position: absolute;
    }
    &__node {
      position: relative;
      &--error {
        .ifp-p-adv {
          &__tool {
            border: 1px solid $ifp-color-red;
          }
          &__tool-error {
            display: block;
            width: 140px;
            font-size: $ifp-fs-2;
            color: $ifp-color-red;
            position: absolute;
            top: 100%;
            left: 50%;
            margin-top: $spacer-1;
            border: 1px solid $ifp-color-red;
            border-radius: 3px;
            padding: $spacer-1;
            transform: translateX(-50%);
            &::after {
              content: attr(data-error);
            }
          }
        }
      }
    }
  }
}

@include desktop-lg {
  .ifp-p-adv {
    &__body-content {
      height: 100vh;
      position: relative;
    }
  }
}

:host-context([dir="rtl"]) {
  .ifp-p-adv {
    &__drawflow {
      direction: ltr;
    }
    &__actions {
      right: 0;
      left: auto;
      &--collpase {
        right: auto;
        left: 415px;
      }
    }
    &__tools {
      right: calc(100% - 500px);
    }
  }

  :host::ng-deep {
    .ifp-p-adv {
      // &__body {
      //   .them-edit-link {
      //     left: 100px;
      //     right: auto;
      //   }
      //   .github-link {
      //     right: auto;
      //     left: 20px;
      //   }
      //   .btn-clear {
      //     left: 10px;
      //     right: auto;
      //   }

      //   .btn-lock {
      //     float: left;
      //     left: 140px;
      //     right: auto;
      //   }

      //   .bar-zoom {
      //     float: left;
      //     left: 10px;
      //     right: auto;
      //   }
      //   .drawflow .drawflow-node .output {
      //     left: 10px;
      //     right: auto;
      //     background-image: url("data:image/svg+xml,%3Csvg width='18' height='16' viewBox='0 0 18 16' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath fill-rule='evenodd' clip-rule='evenodd' d='M13.0546 6.96467C12.6695 7.60117 12.6695 8.39883 13.0546 9.03533L16.6624 14.9982C16.6625 14.9984 16.6626 14.9986 16.6626 14.9988V14.9988C16.6626 14.9995 16.6621 15 16.6614 15H6.02539C5.3255 15 4.67653 14.6341 4.31423 14.0353L1.28901 9.03533C0.903905 8.39883 0.903905 7.60117 1.28902 6.96467L4.31423 1.96467C4.67653 1.36586 5.3255 1 6.02539 1H16.6614C16.6621 1 16.6626 1.00053 16.6626 1.00119V1.00119C16.6626 1.0014 16.6625 1.00162 16.6624 1.0018L13.0546 6.96467Z' fill='%23697D92'/%3E%3Cpath d='M16.6624 1.0018L17.518 1.51947L16.6624 1.0018ZM4.31423 1.96467L3.45864 1.44701L4.31423 1.96467ZM1.28902 6.96467L0.433431 6.44701L1.28902 6.96467ZM4.31423 14.0353L5.16981 13.5177L4.31423 14.0353ZM13.0546 9.03533L13.9102 8.51767L13.0546 9.03533ZM16.6624 14.9982L17.518 14.4805L16.6624 14.9982ZM15.8068 15.5159L12.1991 9.55299L13.9102 8.51767L17.518 14.4805L15.8068 15.5159ZM6.02539 14H16.6614V16H6.02539V14ZM2.1446 8.51767L5.16981 13.5177L3.45864 14.553L0.433431 9.55299L2.1446 8.51767ZM5.16981 2.48234L2.1446 7.48234L0.433431 6.44701L3.45864 1.44701L5.16981 2.48234ZM16.6614 2H6.02539V0H16.6614V2ZM12.1991 6.44701L15.8068 0.484138L17.518 1.51947L13.9102 7.48234L12.1991 6.44701ZM17.6626 1.00119C17.6626 1.18374 17.6127 1.363 17.518 1.51947L15.8068 0.484138C15.7124 0.640235 15.6626 0.819067 15.6626 1.00119H17.6626ZM16.6614 0C17.2144 0 17.6626 0.448247 17.6626 1.00119H15.6626C15.6626 1.55282 16.1098 2 16.6614 2V0ZM3.45864 1.44701C4.0021 0.548787 4.97556 0 6.02539 0V2C5.67545 2 5.35096 2.18293 5.16981 2.48234L3.45864 1.44701ZM0.433431 9.55299C-0.144234 8.59824 -0.144233 7.40176 0.433431 6.44701L2.1446 7.48234C1.95204 7.80059 1.95204 8.19942 2.1446 8.51767L0.433431 9.55299ZM6.02539 16C4.97556 16 4.0021 15.4512 3.45864 14.553L5.16981 13.5177C5.35096 13.8171 5.67545 14 6.02539 14V16ZM17.6626 14.9988C17.6626 15.5526 17.2135 16 16.6614 16V14C16.1106 14 15.6626 14.4464 15.6626 14.9988H17.6626ZM12.1991 9.55299C11.6214 8.59824 11.6214 7.40176 12.1991 6.44701L13.9102 7.48234C13.7177 7.80059 13.7177 8.19941 13.9102 8.51767L12.1991 9.55299ZM17.518 14.4805C17.6126 14.6369 17.6626 14.8161 17.6626 14.9988H15.6626C15.6626 15.1811 15.7125 15.3599 15.8068 15.5159L17.518 14.4805Z' fill='%23F4F7F6'/%3E%3C/svg%3E%0A");
      //   }

      //   .drawflow .drawflow-node .input {
      //     right: -10px;
      //     left: auto;
      //     background-image: url("data:image/svg+xml,%3Csvg width='18' height='16' viewBox='0 0 18 16' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath fill-rule='evenodd' clip-rule='evenodd' d='M13.0546 6.96467C12.6695 7.60117 12.6695 8.39883 13.0546 9.03533L16.6624 14.9982C16.6625 14.9984 16.6626 14.9986 16.6626 14.9988V14.9988C16.6626 14.9995 16.6621 15 16.6614 15H6.02539C5.3255 15 4.67653 14.6341 4.31423 14.0353L1.28901 9.03533C0.903905 8.39883 0.903905 7.60117 1.28902 6.96467L4.31423 1.96467C4.67653 1.36586 5.3255 1 6.02539 1H16.6614C16.6621 1 16.6626 1.00053 16.6626 1.00119V1.00119C16.6626 1.0014 16.6625 1.00162 16.6624 1.0018L13.0546 6.96467Z' fill='%23697D92'/%3E%3Cpath d='M16.6624 1.0018L17.518 1.51947L16.6624 1.0018ZM4.31423 1.96467L3.45864 1.44701L4.31423 1.96467ZM1.28902 6.96467L0.433431 6.44701L1.28902 6.96467ZM4.31423 14.0353L5.16981 13.5177L4.31423 14.0353ZM13.0546 9.03533L13.9102 8.51767L13.0546 9.03533ZM16.6624 14.9982L17.518 14.4805L16.6624 14.9982ZM15.8068 15.5159L12.1991 9.55299L13.9102 8.51767L17.518 14.4805L15.8068 15.5159ZM6.02539 14H16.6614V16H6.02539V14ZM2.1446 8.51767L5.16981 13.5177L3.45864 14.553L0.433431 9.55299L2.1446 8.51767ZM5.16981 2.48234L2.1446 7.48234L0.433431 6.44701L3.45864 1.44701L5.16981 2.48234ZM16.6614 2H6.02539V0H16.6614V2ZM12.1991 6.44701L15.8068 0.484138L17.518 1.51947L13.9102 7.48234L12.1991 6.44701ZM17.6626 1.00119C17.6626 1.18374 17.6127 1.363 17.518 1.51947L15.8068 0.484138C15.7124 0.640235 15.6626 0.819067 15.6626 1.00119H17.6626ZM16.6614 0C17.2144 0 17.6626 0.448247 17.6626 1.00119H15.6626C15.6626 1.55282 16.1098 2 16.6614 2V0ZM3.45864 1.44701C4.0021 0.548787 4.97556 0 6.02539 0V2C5.67545 2 5.35096 2.18293 5.16981 2.48234L3.45864 1.44701ZM0.433431 9.55299C-0.144234 8.59824 -0.144233 7.40176 0.433431 6.44701L2.1446 7.48234C1.95204 7.80059 1.95204 8.19942 2.1446 8.51767L0.433431 9.55299ZM6.02539 16C4.97556 16 4.0021 15.4512 3.45864 14.553L5.16981 13.5177C5.35096 13.8171 5.67545 14 6.02539 14V16ZM17.6626 14.9988C17.6626 15.5526 17.2135 16 16.6614 16V14C16.1106 14 15.6626 14.4464 15.6626 14.9988H17.6626ZM12.1991 9.55299C11.6214 8.59824 11.6214 7.40176 12.1991 6.44701L13.9102 7.48234C13.7177 7.80059 13.7177 8.19941 13.9102 8.51767L12.1991 9.55299ZM17.518 14.4805C17.6126 14.6369 17.6626 14.8161 17.6626 14.9988H15.6626C15.6626 15.1811 15.7125 15.3599 15.8068 15.5159L17.518 14.4805Z' fill='%23F4F7F6'/%3E%3C/svg%3E%0A");
      //   }

      //   .drawflow .connection .point {
      //     transform: translate(9999px, -9999px);
      //   }
      //   /* Modal */
      //   .modal {
      //     right: 0;
      //     left: auto;
      //   }

      //   /* The Close Button */

      //   .modal .close {
      //     float: left;
      //   }
      //   .drawflow .connection {
      //     transform: translate(-9999px, 9999px);
      //   }

      //   .drawflow .connection .main-path {
      //     transform: translate(9999px, -9999px);
      //   }

      //   .drawflow .connection .point {
      //     transform: translate(9999px, -9999px);
      //   }
      //   .drawflow .selectbox {
      //     transform: translate(-9999px, 9999px);
      //   }
      //   .parent-node .drawflow-delete {
      //     left: -15px;
      //     right: auto;
      //   }
      // }
      &__drawflow {
        &--grid {
          background-image: linear-gradient(to left, $ifp-color-grey-7 1px, transparent 1px), linear-gradient(to bottom, $ifp-color-grey-7 1px, transparent 1px);
        }
      }

      &__col-right {
        width: 100%;
      }

      &__minimap {
        left: 10px;
        right: auto;
      }
    }
  }
}

.ifp-dark-theme :host::ng-deep  {
  .ifp-p-adv__body .drawflow .connection .main-path {
    stroke:  $ifp-color-white-global;
  }
}
