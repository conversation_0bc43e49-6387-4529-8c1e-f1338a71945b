@if (!workFlowRunning() && !emptyConnection() && currentConection() && sourceData() && columnList && columnList?.length && !errorMessageForTools) {

<ifp-data-tool-accordian [title]="'New Aggregate'" [enableCheckBox]="false" [isAccordianExpanded]="true" class="ifp-adv-tool__accordian ifp-adv-tool__accordian--tool-box">
  <div class="ifp-accord-box__content">
    <form [formGroup]="aggregateForm" class="ifp-adv-aggr__form">
      <div class="ifp-adv-tool__edit-outer">
        <p class="ifp-adv-tool__label">{{'Select Column' | translate}}</p>
        @if (columnList && columnList.length) {
          <app-ifp-dropdown class="ifp-adv-tool__dropdown" [singleDefaultSelect]="false" [selectedValue]="selectedColumn" [key]="'name'" [dropDownItems]="columnList"
          (dropDownItemClicked)="selectColumn($event)" formControlName="aggregate_field" [disableSingleValue]="true"
          [disableTranslation]="true"></app-ifp-dropdown>
          @if ((submitted()) && getControls['aggregate_field'].invalid) {
            <p class="ifp-adv-tool__error"><em class="ifp-icon ifp-icon-exclamation-round"></em>{{'Please select a column' | translate}}</p>
          }
        }
      </div>

      <div class="ifp-adv-tool__edit-outer">
        <p class="ifp-adv-tool__label">{{'Data Type' | translate}}: <span class="ifp-adv-tool__label-value">{{(getControls['type'].value !== null ? getControls['type'].value : '') | translate}}</span></p>
      </div>

      <div class="ifp-adv-tool__edit-outer" [ngClass]="{'ifp-adv-tool__dropdown--disable': aggregateOpts && aggregateOpts.length <= 0}">
        <p class="ifp-adv-tool__label">{{'Action' | translate}}</p>
        <app-ifp-dropdown class="ifp-adv-tool__dropdown" [singleDefaultSelect]="false" [selectedValue]="selectedAggregate" [key]="'label'" [dropDownItems]="aggregateOpts"
          (dropDownItemClicked)="selectMethod($event)" formControlName="method" [disableSingleValue]="true" [disableTranslation]="true">
        </app-ifp-dropdown>
        @if ((submitted()) && getControls['method'].errors?.['required']) {
          <p class="ifp-adv-tool__error"><em class="ifp-icon ifp-icon-exclamation-round"></em>{{'Please select an aggregation method' | translate}}</p>
        }
      </div>

      <div class="ifp-adv-tool__edit-outer">
        <p class="ifp-adv-tool__label">{{'New Column' | translate}}</p>
        <input type="text" class="ifp-adv-tool__input" [placeholder]="'Enter here' | translate"
          formControlName="column_name">
      </div>
      @if (isSelectActive) {
        <div class="ifp-adv-tool__update">
          <div class="ifp-adv-tool__action-inner">
            <ifp-button [label]="'cancel'" (ifpClick)="resetForm()" class="ifp-adv-tool__button ifp-adv-tool__action-btn" [buttonClass]="buttonClass.secondary"></ifp-button>
            <ifp-button [label]="'Update'" (ifpClick)="addAggregate()" class="ifp-adv-tool__button ifp-adv-tool__action-btn" [buttonClass]="buttonClass.primary"></ifp-button>
          </div>
        </div>
      } @else {
        <ifp-button [label]="'Add Aggregate'" (ifpClick)="addAggregate()" class="ifp-adv-tool__button"
        [buttonClass]="(aggregateForm.valid ? buttonClass.secondary : buttonClass.disabled)+ ' '+ buttonClass.large" [iconClass]="'ifp-icon-plus'"></ifp-button>
      }
    </form>
  </div>
</ifp-data-tool-accordian>

  @if (aggregationList.length) {
  <div class="ifp-adv-tool__content ifp-adv-tool__content--grey">
    <p class="ifp-adv-tool__sub-title">{{'Added Aggregations' | translate}}</p>
    @for (aggr of aggregationList; track aggr) {
      <div class="ifp-adv-tool__card" (click)="selectAggregate(aggr, $index)">
      <div class="ifp-adv-tool__card-head">
        <p class="ifp-adv-tool__card-title">{{aggr.column_name && aggr.column_name !== '' ? aggr.column_name : aggr.aggregate_field | translate}}</p>
        <em class="ifp-icon ifp-icon-minus-round ifp-adv-tool__remove" (click)="removeAggregate($index)"></em>
      </div>
      <p class="ifp-adv-tool__card-text" >{{'Aggregated the field' | translate}} <span
          class="ifp-adv-tool__card-bold">{{aggr.aggregate_field}}</span> {{'with the method' | translate}} <span class="ifp-adv-tool__card-bold">{{aggr.method.label | translate}}</span></p>
    </div>
    }
  </div>
  }

} @else {
  @if(errorMessageForTools) {
    {{errorMessageForTools}}
  } @else if(workFlowRunning()) {
    <div class="ifp-center-loader">
        <app-ifp-spinner></app-ifp-spinner>
        <p>{{'Workflow Running' | translate}} </p>
      </div>
    } @else if (emptyConnection()){
      <p class="ifp-adv-tool__error"><em class="ifp-icon ifp-icon-exclamation-round"></em>
      {{'Source must have a connection!' | translate}}
      </p>
    } @else if (!sourceData()) {
      <p class="ifp-adv-tool__error"><em class="ifp-icon ifp-icon-exclamation-round"></em>
      {{'You must upload at least one source!' | translate}}
      </p>
    } @else if(!currentConection()){
      <p class="ifp-adv-tool__error"><em class="ifp-icon ifp-icon-exclamation-round"></em>
        {{'Node must have atleast one connection' | translate}}
      </p>
    }
    @else {
      <app-ifp-spinner></app-ifp-spinner>
    }
}
