@use "../../../../assets/ifp-styles/abstracts/index" as *;
.ifp-chart-card {
  border: 1px solid $ifp-color-grey-7;
  border-radius: 10px;
  &__chart {
    padding: $spacer-3;
    border-radius: 10px 10px 0 0 ;
  }
  &__ac-measure {
    overflow: hidden;
    background-color:   $ifp-color-pale-grey;
    padding: $spacer-3;
    border-radius: 0 0 10px 10px ;
  }
  &__ac-wrapper {
    display: flex;
    justify-content: space-between;
    margin-bottom: $spacer-2;
    &:last-child{
      margin-bottom: $spacer-0;
    }

  }
  &__name {
    color : $ifp-color-grey-14;
    font-size: $ifp-fs-4;
  }
  &__value {
    color: $ifp-color-secondary-grey;
    font-weight: $fw-semi-bold;
    font-size: $ifp-fs-4;
  }
  &__circle {
    width: 19px;
    min-width: 19px;
    height: 19px;
    border-radius: 50%;
    border: 1px solid $ifp-color-grey-13;
    background-color: $ifp-color-white;
    display: inline-block;
    margin-inline-end: $spacer-3;
  }
  &__inner-circle {
    width: 11px;
    height: 11px;
    border-radius: 50%;
    display: inline-block;
  }
  &__label {
    font-size: $ifp-fs-3;
    font-weight: $fw-semi-bold;
    min-height: 55px;
    overflow: hidden;
    text-overflow: ellipsis;
    width: calc(100% - 55px);
  }
  &__top-label {
    margin-bottom: $spacer-1;
    display: flex;
    position: relative;
  }
  &__score{
    display: flex;
    justify-content: space-between;
    margin-top: $spacer-2;
  }
  &__score-text {
    font-size:$ifp-fs-4;
    color: $ifp-color-secondary-grey;
    font-weight: $fw-semi-bold;
  }
  &--remove  {
    border: unset;
    border-radius: 0;
  }
  &--chart-list {
    border: unset;
    background-color:  $ifp-color-pale-grey;
    .ifp-chart-card__chart  {
      border-radius: 10px;
    }
  }
  &__number {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    color: $ifp-color-white;
    background-color: $ifp-color-link;
    display: inline-flex;
    // position: absolute;
    // right: 0;
    // top:0;
    justify-content: center;
    align-items: center;
  }
  &--selection {
    border: 1px solid $ifp-color-blue-menu;

    .ifp-chart-card {
      &__chart {
        background-color:  $ifp-color-blue-select;
      }
      &__ac-measure {
        background-color:$ifp-color-blue-dark-select
      }
      &__circle {
        text-align: center;
        border: 1px solid $ifp-color-blue-menu;
        display: flex;
        align-items: center;
        justify-content: center;
       }
      &__inner-circle {
        background-color: $ifp-color-blue-menu;
      }
    }
  }
}

:host-context(.ifp-dark-theme) {
  .ifp-chart-card {
      &--chart-list, &__ac-measure{
         background-color:  $ifp-color-grey-7;
      }
}
}
:host-context([dir="rtl"]) {
  .ifp-chart-card__score-text {
    direction: ltr;
    display: inline-block;
  }
  .ifp-chart-card__inner-circle {
    position: relative;
    width: 10px;
    height: 10px;
  }
}
