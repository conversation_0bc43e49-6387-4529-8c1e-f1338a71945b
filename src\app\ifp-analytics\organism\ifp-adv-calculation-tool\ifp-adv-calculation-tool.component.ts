import { ChangeDetectorRef, Component, EventEmitter, Input, OnChanges, OnDestroy, OnInit, Output, ViewChild, WritableSignal, inject, signal } from '@angular/core';
import { Formula, FormulaFunction } from '../../data-prep/ifp-advance-prep/interface/ifp-advance-prep.interface';
import { TranslateModule } from '@ngx-translate/core';
import { IfpDropdownComponent } from '../../../scad-insights/ifp-widgets/ifp-atoms/ifp-dropdown/ifp-dropdown.component';
import { IfpDataToolAccordianComponent } from '../../molecule/ifp-data-tool-accordian/ifp-data-tool-accordian.component';
import { IfpTabComponent } from '../../../scad-insights/ifp-widgets/ifp-molecules/ifp-tab/ifp-tab.component';
import { IfpButtonComponent } from '../../../scad-insights/ifp-widgets/ifp-atoms/ifp-button/ifp-button.component';
import { buttonClass, buttonIconPosition } from 'src/app/scad-insights/core/constants/button.constants';
import { CodemirrorComponent, CodemirrorModule } from '@ctrl/ngx-codemirror';
import { FormBuilder, FormControl, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { debounceTime, Subject } from 'rxjs';
import { ThemeService } from 'src/app/scad-insights/core/services/theme/theme.service';
import { ToasterService } from 'src/app/scad-insights/core/services/tooster/ToastrService.service';
import { SubSink } from 'subsink';
import { prepsApiEndpoints } from '../../data-prep/ifp-data-prep/constants/if-preps.constants';
import CodeMirror, { Editor, EditorFromTextArea } from 'codemirror';
import { IfpSpinnerComponent } from '../../../scad-insights/ifp-widgets/ifp-molecules/ifp-spinner/ifp-spinner.component';
import { ifpColors } from 'src/app/scad-insights/core/constants/color.constants';
import { NodeData } from '../../data-prep/ifp-data-prep/interface/ifp-data-prep.interface';
import { selectNodeStoreAdvance } from '../../data-prep/ifp-advance-prep/store/advance-node-store/advance-node.store';
import { ColumnPrep } from '../../data-prep/ifp-data-prep/ifp-prep-select-data/interface/prep-selection.interface';
import { IfpAdvancePrepService } from '../../data-prep/ifp-advance-prep/service/ifp-advance-prep.service';
import { advanceMessage, dataTypes } from '../../data-prep/ifp-data-prep/ifp-data-prep.constant';
import 'codemirror/addon/hint/show-hint';
import 'codemirror/addon/hint/anyword-hint';
import 'codemirror/addon/lint/lint';
import { IfpCheckBoxComponent } from 'src/app/scad-insights/ifp-widgets/ifp-atoms/ifp-check-box/ifp-check-box.component';
import { calculationConfig } from '../../molecule/ifp-prep-advance-playground/ifp-prep-advance-playground.constant';
import { cloneDeep } from 'lodash';

@Component({
    selector: 'ifp-adv-calculation-tool',
    templateUrl: './ifp-adv-calculation-tool.component.html',
    styleUrl: './ifp-adv-calculation-tool.component.scss',
    imports: [TranslateModule, IfpDropdownComponent, IfpDataToolAccordianComponent, IfpTabComponent, IfpButtonComponent, CodemirrorModule,
    ReactiveFormsModule, IfpSpinnerComponent, IfpCheckBoxComponent]
})
export class IfpAdvCalculationToolComponent implements OnInit, OnChanges, OnDestroy {
  @ViewChild('codeEditor') codeEditor!: CodemirrorComponent;
  @Input({ required: true }) columnList!: ColumnPrep[];
  @Input() nodeData!: NodeData;
  @Input() workFlowRunning = signal(false);
  @Input() emptyConnection = signal(true);
  @Input() currentNodeId?: string = '';
  @Input() currentConection: WritableSignal<boolean> = signal(false);
  @Input() sourceData: WritableSignal<boolean> = signal(false);
  @Input() isConfigUpdated: WritableSignal<boolean> = signal(false);
  @Input() errorMessageForTools?: string;

  @Output() cancel = new EventEmitter();
  @Output() save: EventEmitter<FormGroup> = new EventEmitter<FormGroup>();

  private expresstionChange = new Subject();

  public selectedField!: string | null;
  public searchString!: string;
  public formulasCurrent!: Formula[];

  public operationTypeList!: { name: string }[];

  public selectedType!: string;
  public buttonClass = buttonClass;
  public buttonIconPosition = buttonIconPosition;
  public dataTypeList = dataTypes;
  public calculationList: CalcFormula[] = [];
  public calcForm!: FormGroup<CalcFormData>;
  public selectedDataType!: string;
  public subs: SubSink = new SubSink();
  public fuctionList: string[] = [];
  public fieldList: string[] = [];
  public formulas: string[] = [];
  public formulasToDisplay: FormulaFunction[] = [];
  public editor?: EditorFromTextArea;
  public editorView!: Editor;
  public color = ifpColors;
  public lineDiv!: HTMLDivElement;
  public isColumnSaveType: string | undefined = calculationConfig.newColumn;
  public calculationConfig = calculationConfig;
  public selectedFunction!: any;
  public isFormulaClicked: boolean = false;
  public selectedFormula!: FormulaFunction | null;
  public ogColumns!: ColumnPrep[];
  private editIndex: number = 0;
  public isEditCalculation: boolean = false;
  public customLint = (text: string, updateLinting: any) => {
    this.expresstionChange.next(updateLinting);
  };

  public configCodemirror = {
    mode: 'cypher',
    lint: {
      getAnnotations: this.customLint,
      async: true
    },
    indentWithTabs: true,
    smartIndent: true,
    lineNumbers: true,
    autofocus: true,
    showHint: true,
    dragDrop: true,
    autoCloseBrackets: true,
    lineWrapping: true,
    theme: this._themeService.defaultTheme === 'light' ? 'xq-light' : 'xq-dark'
  };

  // codeMirror variables
  public formulaLoader = signal(false);
  public fieldLoader = signal(false);
  public readonly advanceStore = inject(selectNodeStoreAdvance);
  public formulaValidated = signal(false);
  public nodeDetail!: any;
  public advanceMessage = advanceMessage;

  constructor(private _themeService: ThemeService, private _formBuilder: FormBuilder, private _toasterService: ToasterService, public _prepService: IfpAdvancePrepService,
    private _cdr: ChangeDetectorRef
  ) { }

  ngOnInit(): void {
    this.calcForm = this._formBuilder.group({
      expression: new FormControl('', Validators.required),
      columnName: new FormControl('', Validators.required),
      outputColumn: new FormControl(this.columnList?.length ? this.columnList[0].name : ''),
      dataType: new FormControl('')
    });

    this.subs.add(
      this._themeService.defaultTheme$.subscribe((data) => {
        this.configCodemirror = {
          ...this.configCodemirror,
          theme: data === 'dark' ? 'xq-dark' : 'xq-light'
        };
        this._cdr.detectChanges();
      })
    );

  }


  ngOnChanges() {
    if (this.columnList?.length > 0) {
      this.calculationList = [];
      this.ogColumns = cloneDeep(this.columnList);
      this.nodeDetail = this.advanceStore.selectNodeValue().nodeValue(this.currentNodeId ?? '');
      if (this.columnList?.length) {
        this.fieldList = this.columnList.map((item: ColumnPrep) => item.name);
      }
      if (this.nodeDetail.settings?.calculation?.length) {
        this.calculationList = this.nodeDetail.settings.calculation;
      }
      this.resetColumnFeild();
      this.loadFormulaApi();
      this.subs.add(this.expresstionChange.pipe(debounceTime(800)).subscribe((updateLinting) => {
        this.validationApi(updateLinting);
      }));
      this.fieldLoader.set(true);
    }
  }

  resetColumnFeild() {
    setTimeout(() => {
      this.calcForm.patchValue({ outputColumn: '' });
    }, 50);
  }

  selectColumn(field: any) {
    this.selectedField = field.name;
    this.calcForm.patchValue({ outputColumn: this.selectedField });
    const value = this.calcForm.get('expression')?.value;
    const index = value.lastIndexOf(')');
    const index2 = value.lastIndexOf('(');
    const diff = index - index2;
    const cursorPostion = index !== -1 ? index : 0;
    const newValue = `${value.slice(0, cursorPostion)}${diff <= 1 ? '' : ','}[${field.name}]${value.slice(cursorPostion)}`;

    this.calcForm.get('expression')?.setValue(newValue);
    this.expresstionChange.next(this.calcForm.value);
  }

  selectDataType(type: string) {
    this.selectedDataType = type.toLowerCase();
    this.calcForm.patchValue({ dataType: this.selectedDataType });
  }

  searchResult(event: string) {
    if (event !== '') {
      this.fuctionList = this.fuctionList.filter((item: string) => item.includes(event));
    }
  }

  selectType(item: { event: { name: string }; index: number }) {
    this.selectedType = item.event.name;
    this.formulasCurrent.forEach((formula: Formula) => {
      if (formula.category === item.event.name) {
        this.fuctionList = formula.functions.map((func: FormulaFunction) => func.keyword);
      }
    });
  }

  selectFormula(selectedFunction: string) {
    this.isFormulaClicked = true;
    const selectedTabFormula = this.formulasCurrent.find(x => x.category == this.selectedType);
    this.selectedFunction = selectedTabFormula?.functions.find(x => x.keyword == selectedFunction);
    const value = this.calcForm.get('expression')?.value;
    this.calcForm.get('expression')?.setValue(`${value}${selectedFunction}()`);
    this.expresstionChange.next(this.calcForm.value);
  }

  onMouseMove(item: string) {
    if (!this.isFormulaClicked) {
      const selectedTabFormula = this.formulasCurrent.find(x => x.category == this.selectedType);
      this.selectedFunction = selectedTabFormula?.functions.find(x => x.keyword == item);
    }
  }

  onMouseLeave(): void {
    if (!this.isFormulaClicked) {
      this.selectedFunction = null;
    }
  }

  addCalculation() {
    if (!this.formulaValidated()) {
      this._toasterService.error('Please correct the validation error.');
      return;
    }
    if (this.isColumnSaveType === calculationConfig.newColumn && !this.calcForm.value.columnName.trimStart()) {
      this._toasterService.error('Please provide a valid column name.');
      return;
    }
    if (this.calcForm.valid) {
      const query = (this.codeEditor.codeMirror as any)?.display?.lineDiv.outerHTML;
      const saveColName = this.isColumnSaveType === calculationConfig.newColumn ? this.calcForm.value.columnName : this.calcForm.value.columnName.name;
      if (this.isEditCalculation) {
        this.calculationList[this.editIndex] = { name: saveColName, formula: this.calcForm.value.expression, html: query, type: this.selectedDataType, columnType: this.isColumnSaveType };
      } else {
        this.calculationList.push({ name: saveColName, formula: this.calcForm.value.expression, html: query, type: this.selectedDataType, columnType: this.isColumnSaveType });
      }
      this.isEditCalculation= false;
      this.resetForm();
      return;
    }
    if (this.calcForm.controls['columnName'].errors) {
      this._toasterService.error('Please provide a valid column name');
    }
    if (this.calcForm.controls['expression'].errors) {
      this._toasterService.error('Please enter a valid expression.');
    }
    this.selectedFunction = '';
    this._cdr.detectChanges();
  }

  removeCalc(index: number) {
    this.calculationList.splice(index, 1);
  }

  resetExpression() {
    this.calcForm.get('expression')?.setValue('');
  }

  // CodeMirror functions
  loadFormulaApi() {
    this.formulaLoader.set(false);
    this.subs.add(this._prepService.getMethodRequest(prepsApiEndpoints.selectNode + prepsApiEndpoints.formulaList).subscribe((data: Formula[]) => {
      this.formulasCurrent = data;
      this.formulas = data.flatMap((x: Formula) => x.functions.map((func: FormulaFunction) => func.keyword));
      this.operationTypeList = this.formulasCurrent.map((formula: Formula) => {
        return { name: formula.category };
      });
      this.selectedType = this.operationTypeList[0].name;
      this.loadFormulaData();
    }));
  }

  loadFormulaData() {
    this.fuctionList = [];
    this.formulasCurrent.forEach(formulaData => {
      if (formulaData.category === this.selectedType) {
        formulaData.functions.forEach(data => {
          this.formulasToDisplay.push({ ...data, event: `${data.keyword}()` });
          this.fuctionList.push(data.keyword);
        });
      }
    });
    this.defineCodeMirror();
    this.formulaLoader.set(true);
  }

  defineCodeMirror() {
    const wordRegexp = (words: any[]) => {
      return new RegExp(`^(?:${words.join('|')})$`, 'i');
    };
    CodeMirror.defineMode('cypher', (config) => {
      const tokenBase = (stream: { next: () => any; match: (arg0: RegExp) => void; eat: (arg0: string) => any; skipToEnd: () => void; eatWhile: (arg0: RegExp) => void; current: () => any; }/* , state*/) => {
        curPunc = null;
        const ch = stream.next();
        if (ch === '"') {
          stream.match(/^[^"]*"/);
          return 'string';
        }
        if (ch === '\'') {
          stream.match(/^[^']*'/);
          return 'string';
        }
        if (ch === '[') {
          let ch2;
          while ((ch2 = stream.next()) != null) {
            if (ch2 == ']') {
              const newStr = stream.current().replace(/[\[\]]/g, '');
              if (funcs.test(newStr)) {
                return 'builtin';
              }
              return 'variable';
            }
          }
        }
        if ((/[{}\\(\\),\\.;\\[\]]/).test(ch)) {
          curPunc = ch;
          return 'node';
        } else if (ch === '/' && stream.eat('/')) {
          stream.skipToEnd();
          return 'comment';
        } else if (operatorChars.test(ch)) {
          stream.eatWhile(operatorChars);
          return null;
        }
        stream.eatWhile(/[_\w\d]/);
        if (stream.eat(':')) {
          stream.eatWhile(/[\w\d_\\-]/);
          return 'atom';
        }
        const word = stream.current();
        if (funcs.test(word)) {
          return 'builtin';
        }
        if (preds.test(word)) {
          return 'def';
        }
        if (keywords.test(word) || systemKeywords.test(word)) {
          return 'keyword';
        }
        return 'variable';

      };
      const pushContext = (state: { context: { prev: any; indent: any; col: any; type: any; }; indent: any; }, type: string, col: number) => {
        return state.context = {
          prev: state.context,
          indent: state.indent,
          col: col,
          type: type
        };
      };
      const popContext = (state: { indent: any; context: { indent: any; prev: any; }; }) => {
        state.indent = state.context.indent;
        return state.context = state.context.prev;
      };
      const indentUnit = config.indentUnit;
      let curPunc: any;
      const funcs = wordRegexp(this.fieldList);
      const preds = wordRegexp([]);
      const keywords = wordRegexp(this.formulas);
      const systemKeywords = wordRegexp([]);
      const operatorChars = /[*+\-<>=&|~%^/]/;

      return {
        startState: (/* base*/) => {
          return {
            tokenize: tokenBase,
            context: null,
            indent: 0,
            col: 0
          };
        },
        token: (stream: any, state: any) => {
          if (stream.sol()) {
            if (state.context && (state.context.align == null)) {
              state.context.align = false;
            }
            state.indent = stream.indentation();
          }
          // if (stream.eatSpace()) {
          //   return null;
          // }
          const style = state.tokenize(stream, state);
          if (style !== 'comment' && state.context && (state.context.align == null) && state.context.type !== 'pattern') {
            state.context.align = true;
          }
          if (curPunc === '(') {
            pushContext(state, ')', stream.column());
          } else if (curPunc === '[') {
            pushContext(state, ']', stream.column());
          } else if (curPunc === '{') {
            pushContext(state, '}', stream.column());
          } else if ((/[\]\\}\\)]/).test(curPunc)) {
            while (state.context && state.context.type === 'pattern') {
              popContext(state);
            }
            if (state.context && curPunc === state.context.type) {
              popContext(state);
            }
          } else if (curPunc === '.' && state.context && state.context.type === 'pattern') {
            popContext(state);
          } else if ((/atom|string|variable/).test(style) && state.context) {
            if ((/[\\}\]]/).test(state.context.type)) {
              pushContext(state, 'pattern', stream.column());
            } else if (state.context.type === 'pattern' && !state.context.align) {
              state.context.align = true;
              state.context.col = stream.column();
            }
          }
          return style;
        },
        indent: (state: any, textAfter: any) => {
          const firstChar = textAfter?.charAt(0);
          let context = state.context;
          if ((/[\]\\}]/).test(firstChar)) {
            while (context && context.type === 'pattern') {
              context = context.prev;
            }
          }
          const closing = context && firstChar === context.type;
          if (!context) {
            return 0;
          }
          if (context.type === 'keywords') {
            return CodeMirror.commands.newlineAndIndent;
          }
          if (context.align) {
            return context.col + (closing ? 0 : 1);
          }
          return context.indent + (closing ? 0 : indentUnit);
        }
      };
    });
    CodeMirror.defineMIME('application/x-cypher-query', 'cypher');
  }

  codeMirrorLoaded(event: CodemirrorComponent) {
    this.editor = event.codeMirror;
    this.editor?.on('inputRead', (editor: Editor, change) => {
      this.editorView = editor;
      const tokenKey = editor.getTokenAt(editor.getCursor());
      const hintFn = () => {
        if (tokenKey.string === '[') {
          const fieldList = this.columnList.map((data) => `[${data.name}]`);
          return {
            from: { ch: tokenKey.start, line: editor.getCursor().line },
            to: { ch: tokenKey.end, line: editor.getCursor().line },
            list: fieldList
          };
        }
        const value = this.formulas.filter(data => (data.toLowerCase()).startsWith(tokenKey.string.toLowerCase()));
        const list = value.map((data) => `${data}()`);
        const fieldListValue = this.columnList.filter(data => (data.name.toLowerCase()).startsWith(tokenKey.string.toLowerCase()));
        const fieldList = fieldListValue.map((data) => `[${data.name}]`);
        return {
          from: { ch: tokenKey.start, line: editor.getCursor().line },
          to: { ch: tokenKey.end, line: editor.getCursor().line },
          list: [...list, ...fieldList]
        };
      };
      editor?.showHint({
        completeSingle: false,
        hint: hintFn
      });
      if (change.text[0] === '.') {
        editor?.showHint({
          hint: hintFn
        });
      }
    });
  }

  onCancel() {
    this.calcForm.setValue({
      expression: '',
      columnName: '',
      outputColumn: '',
      dataType: ''
    });
    this.cancel.emit();
  }

  validationApi(updateLinting: any) {
    const formula = {
      formula: this.calcForm.value.expression?.trim(),
      column: this.calcForm.value.columnName,
      workflow: this.advanceStore.selectNodeStoreAdvance().data()
    };

    if (this.calcForm.value.expression && this.calcForm.value.expression?.trim() !== '') {
      const checkValidation = this._prepService.postMethodRequest(`${prepsApiEndpoints.selectNode}formula/${this.nodeData.objectId}/validate`, formula).subscribe({
        next: () => {
          this.formulaValidated.set(true);
          const annotations: [] = [];
          updateLinting(annotations);
          checkValidation.unsubscribe();
        },
        error: (error) => {
          this.formulaValidated.set(false);
          checkValidation.unsubscribe();
          const annotations = [];
          const lineNumber = error.error.line_number - 1;
          annotations.push({
            message: error.error.error,
            severity: 'error',
            from: { line: lineNumber, ch: error.error.column_number - 1 },
            to: { line: lineNumber, ch: error?.error?.highlighted_formula?.length ?? 1 }
          });
          updateLinting(annotations);
          if (!error.error.column_number) {
            this._toasterService.error(error.error.error);
          }
        }
      });
    }
  }

  onSave() {
    const data = this.calculationList.map((item: { name: string, formula: string, type: string }) => {
      return { name: item.name, formula: item.formula, type: item.type };
    });
    this.advanceStore.updateNode(this.nodeData.objectId, this.nodeData.name, this.nodeData.type, { columns: data }, 'completed', { calculation: this.calculationList, configUpdated: true, saveType: this.isColumnSaveType });
    this.save.emit(this.calcForm);
    this._prepService.nodeChangeDetect.next('node added');
  }



  selectSaveType(event: string) {
    this.isColumnSaveType = event;
    this.calcForm.controls['columnName'].setValue('');
  }

  searchField(_event: any) {
    if (_event !== '' && _event) {
      this.columnList = this.ogColumns.filter((item: ColumnPrep) => item.name.toLowerCase().includes(_event.toLowerCase()));
    } else {
      this.columnList = cloneDeep(this.ogColumns);
    }
  }

  onItemDrag(item: any, type: 'field' | 'formula') {
    this.selectedField = null;
    this.selectedFormula = null;
    if (type === 'field') {
      this.selectedField = item;
    } else {
      this.selectedFormula = item;
    }
  }

  drop(event: [Editor, DragEvent]) {
    if (event[1]?.dataTransfer?.effectAllowed != 'copyMove') {
      event[1]?.preventDefault();
    }

    this.expresstionChange.next(this.calcForm.value);
    // Function to provide auto-completion hints
  }


  // function used to prevent drag hover
  onDragOver(event: MouseEvent) {
    event.preventDefault();
    event.stopPropagation();
  }

  // function used to drop fields and functions
  onDrop(event: MouseEvent) {
    // this.currenPostsition=  this.updateCursorPosition(event);
    // this.addValueTolocation();
    if (this.selectedFormula && event.type === 'drop') {
      this.selectOperation(this.selectedFormula);
    } else if (this.selectedField && event.type === 'drop') {
      this.selectField(this.selectedField);
    }
    this.selectedFormula = null;
    this.selectedField = null;
  }

  // Select a field
  selectField(field: string) {
    const value = this.calcForm.get('expression')?.value;
    const index = value.lastIndexOf(')');
    const index2 = value.lastIndexOf('(');
    const diff = index - index2;
    const cursorPostion = index2 !== -1 ? (index !== -1 ? index : 0) : value.length;
    const newValue = `${value.slice(0, cursorPostion)}${diff <= 1 ? '' : ','}[${field}]${value.slice(cursorPostion)}`;

    this.calcForm.get('expression')?.setValue(newValue);
  }

  selectOperation(formula: FormulaFunction) {
    const value = this.calcForm.get('expression')?.value;
    this.calcForm.get('expression')?.setValue(`${value}${formula?.event} `);
    // this.editor.setValue(this.expressionForm.get('expression')?.value);
    this.selectedFormula = null;
  }

  updateCalculation(calc: CalcFormula, index: number) {
    this.calcForm.patchValue({
      expression: calc.formula,
      columnName: calc.columnType == this.calculationConfig.newColumn ? calc.name : this.columnList.find(x => x.name == calc.name),
      dataType: calc.type
    });
    this.editIndex = index;
    this.isEditCalculation = true;
    this.isColumnSaveType = calc.columnType;
  }

  resetCalculation() {
    this.isEditCalculation = false;
    this.editIndex = 0;
    this.resetForm();
  }

  resetForm() {
    this.calcForm.patchValue({
      columnName: '',
      expression: '',
      dataType: ''
    });
    this.isFormulaClicked = false;
    this.selectedFunction = null;
  }

  ngOnDestroy(): void {
    this.subs.unsubscribe();
    if (!this._prepService.isConfigSave() && this.columnList?.length > 0) {
      this.onSave();
    }
  }
}

interface CalcFormData {
  expression: FormControl;
  columnName: FormControl;
  outputColumn: FormControl;
  dataType: FormControl;
}

interface CalcFormula {
  name: string,
  formula: string,
  html: string,
  type: string,
  columnType?: string
}
