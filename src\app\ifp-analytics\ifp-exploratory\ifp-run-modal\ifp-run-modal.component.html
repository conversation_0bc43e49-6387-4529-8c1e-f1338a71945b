@if(datasetChart) {
  <div class="ifp-auto-ml-upload ifp-container">
    <h1  class="ifp-auto-ml-upload__chart-title">
      {{'Exploratory Data Analysis' | translate }}
    </h1>
  <ifp-exploratory-sample-chart class="ifp-auto-ml-upload__chart" [currentId]="workFlowId"  (backButton)="backToRun()" (runButton)="switchIteration()" ></ifp-exploratory-sample-chart>
  </div>
}@else {
<div class="ifp-auto-ml-upload" >
  <div class="ifp-auto-ml-upload__outer ifp-container">
    <div class="ifp-auto-ml-upload__wrapper">
      <h1 class="ifp-auto-ml-upload__title">
        {{'Build Your Prediction Model' |translate }}
      </h1>
      <p  class="ifp-auto-ml-upload__desc">{{'Make your predictions by defining the target variable you want to predict and determine the timestamp variable to capture temporal patterns for the horizon chosen.' | translate}}</p>
      @if (loader) {
        <div class="ifp-center-loader">
          <app-ifp-spinner></app-ifp-spinner>
        </div>

      } @else if(error()) {
        <div class="ifp-auto-ml-upload__error">
          <p class="ifp-auto-ml-upload__error-text">{{errorMessage() | translate}}</p>
          <ifp-button class="ifp-auto-ml-upload__error-btn" [label]="'Back to data upload'" (ifpClick)="backToDataUpload()"></ifp-button>
        </div>

      } @else if(this.dataLength() <10) {
        <div class="ifp-auto-ml-upload__error">
          <p class="ifp-auto-ml-upload__error-text">{{'Data source must have at-least 10 records' | translate}}</p>
          <ifp-button class="ifp-auto-ml-upload__error-btn" [label]="'Back to data upload'" (ifpClick)="backToDataUpload()"></ifp-button>
        </div>


      }@else {
        <div class="ifp-auto-ml-upload__dropdown-wrapper">
          <app-ifp-dropdown [disableTranslation]="true" [showTitle]="true"  [required]="true" [dropDownItems]="columns"  [title]="'Select target variable'"   class="ifp-auto-ml-upload__dropdown ifp-auto-ml-upload__option"  (dropDownItemClicked)="dataChangeVariable($event)"></app-ifp-dropdown>

          <div class="ifp-auto-ml-upload__input-wrapper ifp-auto-ml-upload__option">
            <p class="ifp-auto-ml-upload__input-text">{{'Select timestamp variable'| translate}}<span class="ifp-required-field">*</span><app-ifp-info class="ifp-auto-ml-upload__info" [infoContent]="
              'The timestamp column must be accurate and consistent to ensure reliable time series analysis and to infer proper frequency.' | translate"></app-ifp-info></p>
            <app-ifp-dropdown  [disableTranslation]="true" [showTitle]="false" [required]="true" [dropDownItems]="timeSerious" [key]="'column'" class="ifp-auto-ml-upload__dropdown" (dropDownItemClicked)=" dataChange($event)"></app-ifp-dropdown>
          </div>

              <div class="ifp-auto-ml-upload__input-wrapper ifp-auto-ml-upload__option">
            <p class="ifp-auto-ml-upload__input-text">{{'Select Forecast Horizon'| translate}} {{frequency !==unknown  ?'('+(frequency | translate)+')' : ''}}<span class="ifp-required-field">*</span> <app-ifp-info class="ifp-auto-ml-upload__info" [infoContent]="
              'Set the horizon to define the forecast period based on the frequency inferred. A longer horizon often introduces uncertainty into the predictions.' | translate"></app-ifp-info></p>
            <input  [ngClass]="{'ifp-disable': unknown === frequency }" class="ifp-auto-ml-upload__input" type="text" [number]="true" [min]="selectedTimeSeries?.min_horizon ?? 0" [max]="selectedTimeSeries?.max_horizon ?? 0" [formControl]="range" appNumberOnly (keyup)="inputChange()">
            @if ( selectedTimeSeries?.max_horizon &&selectedTimeSeries?.min_horizon && unknown !== frequency ) {
              <div  class="ifp-auto-ml-upload__input-info">
                {{'autoMlRun' | translate:{min : selectedTimeSeries?.min_horizon , max: selectedTimeSeries?.max_horizon} }}
               </div>
            }

          </div>


        </div>
        <div  class="ifp-auto-ml-upload__freq-wrapper">
          <div class="ifp-auto-ml-upload__freq">
            <em class="ifp-icon ifp-icon-nfc ifp-auto-ml-upload__freq-icon"></em>
            <span  class="ifp-auto-ml-upload__freq-head">
              {{'Fetched Frequency'  |translate}}:
            </span>
            <p class="ifp-auto-ml-upload__freq-text">
              {{frequency | translate}}
            </p>
        </div>

        </div>
        <!-- <div  class="ifp-auto-ml-upload__button-wrapper">
          <ifp-button class="ifp-auto-ml-upload__btn-view" [buttonClass]="disableProcess ? buttonClass.disabled : buttonClass.secondary" [label]="'View Time Series Analysis'"  (ifpClick)="runModel()"></ifp-button>
          <ifp-button  class="ifp-auto-ml-upload__btn" [buttonClass]="disableProcess ? buttonClass.disabled : buttonClass.primary" [label]="'Run Model'" [iconClass]="'ifp-icon-play-round'" (ifpClick)="runModel(true)"></ifp-button>

        </div> -->
        @if (showLoader) {
        <div  class="ifp-auto-ml-upload__popup-wrapper">
          <div class="ifp-auto-ml-upload__popup">
            <div class="ifp-auto-ml-upload__popup-inner">
              <div class="ifp-auto-ml-upload__popup-circle">
                <ifp-progress-circle [icon]="'ifp-icon-clock'" [radius]="14.9155" [classCircle]="'ifp-progress-circle--small'" [tooltipEnable]="false" [value]="this.loaderPercentage" [total]="100" [inactiveValue]="0"></ifp-progress-circle>
              </div>
              <div >

                <div class="ifp-auto-ml-upload__popup-percentage">
                  {{this.loaderPercentage}}%
                 </div>
                 <div class="ifp-auto-ml-upload__popup-text">
                  @if (this.loaderPercentage<40) {
                    {{'Initializing model training!' |translate
              }}...
                  }@else {
                    {{'Running...' |translate
              }}...
                  }

                 </div>
              </div>

            </div>
            </div>
        </div>
      }

      }



  </div>
  </div>

<div class="ifp-auto-ml-upload__image" >
  @if((_themeService.defaultTheme$|async) === 'dark') {
    <object class="ifp-auto-ml-upload__img" type="image/svg+xml" data="../../../../assets/images/Graphsmovingdark.svg" ></object>

  } @else {
    <object class="ifp-auto-ml-upload__img" type="image/svg+xml"  data="../../../../assets/images/Graphsmovinglight.svg" ></object>

  }

   </div>
</div>

}

