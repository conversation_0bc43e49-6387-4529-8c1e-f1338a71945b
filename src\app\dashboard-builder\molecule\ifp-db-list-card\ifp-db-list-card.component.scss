@use "../../../../assets/ifp-styles/abstracts/index" as *;

:host {
  display: block;
}

.ifp-db-list-card {
  $card-border-radius: 15px;
  position: relative;
  border: 6px solid $ifp-color-white;
  border-radius: $card-border-radius;
  background-color: $ifp-color-white;
  // box-shadow: inset 0 39px 44px -20px rgba(203, 212, 209, 0.2), 0 104px 95px -50px rgba(98, 110, 106, 0.16);
  padding-bottom: $spacer-3;

  &__options {
    position: absolute;
    top: 10px;
    right: 10px;
    z-index: 1;
  }

  &__thumb {
    border-radius: $card-border-radius;
    background-color: $ifp-color-grey-bg;
    padding: ($spacer-3 + 4) ($spacer-3 + 4) $spacer-5 (
      $spacer-3 + 4
    );
    position: relative;
}

&__thumb-img {
  width: 100%;
  height: 220px;
  background-size: cover;
  background-position: top left;
}

&__footer {
  padding: $spacer-3 $spacer-3 $spacer-0;
}

&__title {
  font-size: $ifp-fs-5;
  font-weight: $fw-semi-bold;
  @include lineLimit(2);
  // min-height: 40px;
  margin-bottom: $spacer-2;
}

&__text {
  color: $ifp-color-grey-14;
  font-size: $ifp-fs-4;
  font-weight: $fw-semi-bold;
}

&__action-wrapper {
  display: flex;
  justify-content: space-between;
}

&__tools-wrapper {
  display: flex;
  align-items: flex-end;
  justify-content: center;
  width: 100%;
  padding-bottom: $spacer-3;
  height: 100%;
  position: absolute;
  bottom: 0;
  left: 0;
  border-radius: 0 0 20px 20px;
  transition: 0.3s;
  background-image: linear-gradient(
    180deg,
    rgba(0, 0, 0, 0) 0%,
    rgba(0, 0, 0, 0.5) 100%
  );
  opacity: 0;
  .ifp-icon {
    font-size: $ifp-fs-5;
  }
  &:hover {
    opacity: 1;
  }
}

&__icon {
  background-color: $ifp-color-white;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: $spacer-3;
}

}


:host-context(.ifp-dark-theme) {
  .ifp-db-list-card {
    background-color: $ifp-color-grey-7;
    border: 6px solid $ifp-color-grey-7;

    &__text {
      color: $ifp-color-grey-2;
    }
  }
}

:host::ng-deep {
  .ifp-db-list-card {
    &__action-wrapper {
      .ifp-checkbox {
        &__label {
          padding-inline-start: ($spacer-3 + 4);

          &::before {
            border: 3px solid $ifp-color-grey-5;
          }
        }
      }
    }
  }
}
