@use "../../../../assets/ifp-styles/abstracts/index" as *;
.ifp-ml-corelation {
  background-color: $ifp-color-section-white;
  border-radius: 10px;
  overflow: hidden;
  height: 100%;
  &__head-wrapper {
    display: flex;
    padding: $spacer-0 $spacer-5;
  }
  &__head-text {
    padding: $spacer-4 $spacer-0;
    border-bottom: 3px solid $ifp-color-blue-menu;
    font-size: $ifp-fs-4;
    font-weight: $fw-bold;
  }

  &__content {
    border-top: 1px solid $ifp-color-grey-13;
    padding: $spacer-4 $spacer-5;
    max-height: 550px;
    @include ifp-scroll-y(transparent, $ifp-color-grey-1, 4px, 8px);
  }
  &__bar-wrapper{
    background-color: $ifp-color-grey-10;
    border-radius: 10px;
    padding: $spacer-1;
  }
  &__bar-heading{
    color: $ifp-color-grey-14;
    margin-top: $spacer-3;
    margin-bottom: $spacer-1;
  }

}
