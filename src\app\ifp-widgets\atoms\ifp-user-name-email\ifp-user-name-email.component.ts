import { Component, input } from '@angular/core';
import { IfpTooltipDirective } from 'src/app/scad-insights/core/directives/ifp-tooltip.directive';
@Component({
  selector: 'ifp-ifp-user-name-email',
  imports: [IfpTooltipDirective],
  templateUrl: './ifp-user-name-email.component.html',
  styleUrl: './ifp-user-name-email.component.scss'
})
export class IfpUserNameEmailComponent {
  name = input<string>();
  email= input<string>();
}
