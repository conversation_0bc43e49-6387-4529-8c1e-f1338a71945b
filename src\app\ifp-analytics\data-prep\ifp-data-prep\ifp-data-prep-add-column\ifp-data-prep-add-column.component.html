<form class="ifp-prep-add" [formGroup]="expressionForm">

  <div class="ifp-prep-add__header">
    <div class="ifp-prep-add__header-wrapper">
      <div class="ifp-prep-add__header-main">
        <h2 class="ifp-prep-add__title-main"> {{'Add Calculated Columns' | translate}}</h2>
        <h5 class="ifp-prep-add__sub-title"> {{'Create a new column or field based on calculations performed on existing data' | translate}}.</h5>
      </div>
          <!-- <ifp-button [label]="'Process'" [loader]="loader()" (ifpClick)="applyOperations()" [buttonClass]="expressionForm.valid && formulaValidated() ?  buttonClass.primaryLight : buttonClass.disabled" [iconClass]="'ifp-icon-rightarrow'"></ifp-button> -->

    </div>
    <h3 class="ifp-prep-add__title">{{'Column Name' | translate}} <span class="ifp-prep-add__title-mandatory">*</span></h3>
    <input type="text" maxlength="80" [placeholder]="'Enter here' | translate" class="ifp-prep-add__input" [formControlName]="'fieldName'" (keyup)="dataCheck()">
    @if(expressionForm.controls['fieldName'].errors?.['inList']) {
      <span class="ifp-prep-add__validation">
        {{'The column name already exists!' | translate}}
      </span>
    }
    @else if(expressionForm.controls['fieldName'].errors?.['required'] && expressionForm.controls['fieldName'].touched) {
      <span class="ifp-prep-add__validation">
        {{'Column name required' | translate}}
      </span>
    }

  </div>
  @if (formulaLoader() && fieldLoader() && workFlowRunning () && !loader()) {
  <div class="ifp-prep-add__body">


      <div class="ifp-prep-add__body-editor">
        <div class="ifp-prep-add__formula-list-wrapper">
          <div class="ifp-prep-add__formula-head">
            <h3 class="ifp-prep-add__title">{{'Functions' | translate}}</h3>
            <!-- <ifp-search [isOnlyBorder]="true" [showPlaceHolder]="true" [isKeypress]="true" [placeholderText]="'Search Formula' | translate" class="ifp-prep-add__search" (searchEvent)="searchFormula($event)"></ifp-search> -->
          </div>
          <ul class="ifp-prep-add__formula-field-list">
            @for (formula of formulasToDisplay; let i = $index; track i) {
              <li class="ifp-prep-add__formula-list" [ngClass]="{'ifp-prep-add__formula-list--active': selectedFormula === formula}" >
                <div [draggable]="true" (click)="selectOperationClick(formula)" (drag)="onItemDrag(formula, 'formula')">
                  <ifp-button [disableTranslate]="true" class="ifp-prep-add__formula-tag"  [label]="formula.name" (ifpClick)="toggleFormulaInfo(formula)" (mousemove)="onMouseMove(formula)" (mouseleave)="onMouseLeave()" [buttonClass]="(isFormulaClicked && selectedFormula == formula ?  buttonClass.primary : buttonClass.secondary)+' '+ buttonIconPosition.left+' '+ buttonClass.round+ ' ' +  buttonClass.normalAplabetic"></ifp-button>
                </div>
              </li>
            }
          </ul>
          @if (selectedFormula && showDescription) {
            <div class="ifp-prep-add__formula-details" [ngClass]="{'ifp-prep-add__formula-details--show': selectedFormula && showDescription}">
              <div class="ifp-prep-add__formula-details-inner">
                <div class="ifp-prep-add__formula-details-text">
                  <h6 class="ifp-prep-add__formula-details-title">{{selectedFormula.event}} </h6>
                  <!-- <em class="ifp-icon ifp-icon-cross ifp-prep-add__formula-detail-close" (click)="toggleFormulaInfo(selectedFormula)"></em> -->
                 <span class="ifp-prep-add__formula-details-sub-title"> {{selectedFormula.description}}</span>
                </div>
                <div class="ifp-prep-add__formula-details-text"><h6 class="ifp-prep-add__formula-details-title">{{'Syntax' | translate}}</h6>
                  <span class="ifp-prep-add__formula-details-sub-title"> {{selectedFormula.syntax}}</span>
                </div>
                <!-- <div class="ifp-prep-add__formula-details-text-divider-wrapper">
                  <div class="ifp-prep-add__formula-details-text-divider"></div>
                </div> -->
                <div class="ifp-prep-add__formula-details-text"><h6 class="ifp-prep-add__formula-details-title">{{'Example' | translate}}</h6>

                  <span class="ifp-prep-add__formula-details-sub-title"> {{selectedFormula.example}}</span>
                </div>
              </div>
            </div>
          }

        </div>
        <div class="ifp-prep-add__reset">
          <div>
            <h3 class="ifp-prep-add__title">{{'Expressions' | translate}}</h3>
            <h5 class="ifp-prep-add__exp-desc">{{"Drag and drop" | translate }}<span class="ifp-prep-add__value-bold">{{' '}} {{'Functions' | translate}}</span> {{'and' | translate}} <span class="ifp-prep-add__value-bold">{{'Fields' | translate}}</span> {{"here or type them directly to create a new calculated field. As you type, prompts will appear to help you build the expression." | translate}}</h5>
          </div>
          @if (this.expressionForm.value.expression && this.expressionForm.value.expression !== '') {
          <ifp-back-button [isGoBack]="false" [buttonText]="'Clear'" [icon]="'ifp-icon-refresh-round'" (buttonClick)="resetFormula()"></ifp-back-button>
          }
        </div>
        <div class="ifp-prep-add__expressions">

          <div class="ifp-prep-add__code-editor" (dragover)="onDragOver($event)" (drop)="onDrop($event)">

            <ngx-codemirror  (drop)="drop($event)"
            [formControlName]="'expression'"
            [options]="configCodemirror"
            (codeMirrorLoaded)="codeMirrorLoaded($event)"
          ></ngx-codemirror>
            <!-- <textarea class="ifp-prep-add__code-preview" #queryBuilder></textarea> -->
          </div>
        </div>
      </div>

      <div class="ifp-prep-add__body-list">
        <div class="ifp-prep-add__body-head">
          <h3 class="ifp-prep-add__title">{{'Fields' | translate}}</h3>
          <ifp-search [isOnlyBorder]="true" [showPlaceHolder]="true" [isKeypress]="true" [placeholderText]="'Search here' | translate" class="ifp-prep-add__search" (searchEvent)="searchField($event)"></ifp-search>
        </div>
        <ul class="ifp-prep-add__field-list">
          @for (field of fieldsToDisplay; let i = $index; track i) {
            <li class="ifp-prep-add__field" [draggable]="true" (click)="selectFieldClick(field)" (drag)="onItemDrag(field, 'field')"><em class="ifp-icon ifp-icon-dragable ifp-prep-add__field-icon"></em>{{field}}</li>
          } @empty {
            <div  class="ifp-prep-add__notFound">

              {{ 'Not Found' | translate}}!
            </div>
          }
        </ul>
      </div>

    <!-- <div class="ifp-prep-add__body-list">
      <div class="ifp-prep-add__body-head">
        <h3 class="ifp-prep-add__title">{{'Formulas' | translate}}</h3>
        <ifp-search [isOnlyBorder]="true" [showPlaceHolder]="true" [isKeypress]="true" [placeholderText]="'Search Formula' | translate" class="ifp-prep-add__search" (searchEvent)="searchFormula($event)"></ifp-search>
      </div>
      <ul class="ifp-prep-add__field-list">
        @for (formula of formulasToDisplay; let i = $index; track i) {
          <li class="ifp-prep-add__formula-list" [ngClass]="{'ifp-prep-add__formula-list--active': selectedFormula === formula}">
            <ifp-button class="ifp-prep-add__tag" [label]="formula.name" [draggable]="true" (ifpClick)="toggleFormulaInfo(formula)" (dragstart)="onItemDrag(formula, 'formula')" [buttonClass]="buttonClass.secondary+' '+ buttonIconPosition.left+' '+ buttonClass.round"></ifp-button>
          </li>
        }
      </ul>
      <div class="ifp-prep-add__details" [ngClass]="{'ifp-prep-add__details--show': selectedFormula && showDescription}">
        <div class="ifp-prep-add__details-inner">
          <p class="ifp-prep-add__details-name">{{selectedFormula?.event}} <em class="ifp-icon ifp-icon-cross ifp-prep-add__detail-close" (click)="toggleFormulaInfo(selectedFormula)"></em></p>
          <p class="ifp-prep-add__details-text">{{selectedFormula?.description}}</p>
          <p class="ifp-prep-add__details-text"><span class="ifp-prep-add__details-title">{{'Syntax' | translate}}</span>{{selectedFormula?.syntax}}</p>
          <p class="ifp-prep-add__details-text"><span class="ifp-prep-add__details-title">{{'Example' | translate}}</span>{{selectedFormula?.syntax}}</p>
        </div>
      </div>
    </div> -->
  </div>
} @else {
  <div class="ifp-center-loader">
    <app-ifp-spinner></app-ifp-spinner>
  </div>
}
</form>
<app-ifp-modal [overlayClass]="'ifp-modal__bg-grey'" #modalSucess >

  <ifp-prep-popup (closePopUp)="closeSucessModal()"   (buttonEventValue)="buttonClick($event)" [heading]="'Calculated Field(s) Added Successfully!'" [desc]="''"  [fileName]="_prepService.uploadedFileResponse()?.name ?? ''"  [subTitle]="'Explore More'" [subDesc]="'What would you like to do next?'" ></ifp-prep-popup>

    </app-ifp-modal>
