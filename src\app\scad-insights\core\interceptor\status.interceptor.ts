import { HttpErrorResponse, HttpEvent, HttpHandler, HttpInterceptor, HttpRequest } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable, catchError, throwError } from 'rxjs';
import { ApiStatus } from '../constants/api-status.constants';
import { ToastrService } from 'ngx-toastr';
import { IFPMsalService } from '../services/IFP-msal.service';

@Injectable()
export class StatusInterceptor implements HttpInterceptor {

  constructor(private _toaster:  ToastrService, private _msalService: IFPMsalService ) {}
  intercept(request: HttpRequest<unknown>, next: <PERSON>ttpHand<PERSON>): Observable<HttpEvent<unknown>> {
    return next.handle(request).pipe(
      catchError((e: HttpErrorResponse) => {
        if (e instanceof HttpErrorResponse) {
          switch (e.status) {
          case ApiStatus.timeOut:
            // this._toaster.error('Failed to establish connection.', '', {
            //   toastClass: 'ifp-toaster',
            //   positionClass: 'toast-bottom-right'
            // });
            break;
          case ApiStatus.corsError:
            // this._toaster.error('Failed to establish connection.', '', {
            //   toastClass: 'ifp-toaster',
            //   positionClass: 'toast-bottom-right'
            // });
            break;
          case ApiStatus.unAuthorized:
            // this._msalService.callrefreshtoken();
            break;
          case ApiStatus.notFound:
            return  throwError(() => {
              return {status: e.status, message: 'Failed to establish connection.', error: 'Failed to establish connection.'};
            });
            break;
          default:
            return  throwError(() => e);
          }
        }
        return  throwError(() => e);
      }));
  }
}
