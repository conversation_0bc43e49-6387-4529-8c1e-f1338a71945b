@use "../../../../assets/ifp-styles/abstracts/index" as *;

.ifp-adv-tool {
  &__box {
    margin-bottom: $spacer-3;
  }
  &__dropdown {
    &--disable {
      pointer-events: none;
      color: $ifp-color-grey-disabled;
    }
  }
  &__sub-title {
    text-align: center;
  }
  &__side-title{
    text-align: left;
  }
  &__desc{
    font-style: italic;
  }
}
.ifp-adv-filter {
  &__flex {
    margin-bottom: $spacer-2;
    .ifp-adv-tool__checkbox {
      margin-bottom: $spacer-0;
      margin-inline-end: $spacer-2;
    }
  }
  &__flex,
  &__card-head {
    display: flex;
    align-items: center;
  }
  &__card {
    background-color: $ifp-color-white;
    border-radius: 7px;
    padding: $spacer-3;
    margin-bottom: $spacer-3;
    cursor: pointer;
    &:last-child {
      margin-bottom: $spacer-0;
    }
  }
  &__card-head {
    justify-content: space-between;
    margin-bottom: $spacer-2;
  }
  &__card-title,
  &__remove {
    color: $ifp-color-black;
  }
  &__card-title {
    font-weight: $fw-semi-bold;
  }
  &__remove {
    transition: 0.3s;
    cursor: pointer;
    &:hover {
      color: $ifp-color-red;
    }
  }
  &__filter-text {
    color: $ifp-color-grey-9;
  }
  &__filter-bold {
    font-weight: $fw-bold;
    color: $ifp-color-black;
  }
}
