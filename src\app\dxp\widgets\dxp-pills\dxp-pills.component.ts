import { Component, input, output } from '@angular/core';

export interface DxpPill {
  icon: string;
  value: string;
  key: string;
}

@Component({
  selector: 'ifp-dxp-pills',
  imports: [],
  templateUrl: './dxp-pills.component.html',
  styleUrl: './dxp-pills.component.scss'
})
export class DxpPillsComponent {
  public items = input<DxpPill[]>([]);
  pillClick = output<DxpPill>();

  onPillClick(item: DxpPill) {
    this.pillClick.emit(item);
  }
}
