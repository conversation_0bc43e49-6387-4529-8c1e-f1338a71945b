
   @if (bordered || rect) {
    <div class="ifp-progress-value" [disableTooltip]="fullDivTooltipDisable"  [appIfpTooltip]="fullDivTooltip + ' ' +progress "  [ngClass]="{'ifp-progress-value__bordered': bordered || rect , 'ifp-progress-value--rect': rect}">

    <div  [disableTooltip]="!fullDivTooltipDisable || disableTooltip"  [appIfpTooltip]="fullDivTooltip + ' ' + ((progressValue | number: '1.1-1') ?? '')+ ' %'" [extraSpaceTop]="30" [ngClass]="{'ifp-progress-value__progress-bar--bordered': bordered || rect}" class="ifp-progress-value__progress-bar" [style.width]="progressValue+ '%'" [style.height]="height" [style.backgroundColor]="color"></div>
    <div class="ifp-progress-value__wrapper" [ngClass]="{'ifp-progress-value__wrapper--size-sm': !value || !percentage}">
      <div class="ifp-progress-value__percent" >
        @if(value) {
          <span class="ifp-progress-value__value">{{(progress| number: '1.'+progressDecimalCount+'-'+progressDecimalCount )| shortNumber}} {{type}}</span>
        }
        @if (percentage) {
          <span class="ifp-progress-value__percentage" [ngClass]="{'ifp-progress-value__percentage--bd-rem': !value}">{{progressValue | number: '1.1-1'}} %</span>
        }
      </div>
    </div>
</div>
  }@else {
    <div class="ifp-progress-value">
    <div class="ifp-progress-value__bar-outer">
      <div [appIfpTooltip]="((progressValue | number: '1.1-1') ?? '')+ ' %'" [extraSpaceTop]="30" class="ifp-progress-value__progress-bar" [ngStyle]="{'width': value && percentage ? 'calc('+ progressValue  + '% - ' + '120px)' : progressValue+'%'}" [style.height]="height" [style.backgroundColor]="color"></div>
    </div>
    <div class="ifp-progress-value__wrapper">

      <div class="ifp-progress-value__percent">
        @if (percentage) {
        <span class="ifp-progress-value__percentage">{{progressValue | number: '1.1-1'}} %</span>
      }
        @if(value) {
        <span class="ifp-progress-value__value">{{(progress| number: '1.1-2'  )| shortNumber}} {{type}}</span>
        }</div>
    </div>
  </div>
  }


