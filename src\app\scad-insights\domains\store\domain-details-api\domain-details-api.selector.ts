import { createFeatureSelector, createSelector } from '@ngrx/store';
import { RouterStateUrl } from './../../../store/router/custom-serializer';
import { selectCurrentRoute  } from './../../../store/router/router.selector';
import { EntityState } from '@ngrx/entity';
import { DomainDetails, DomainDetailsNew } from 'src/app/scad-insights/core/interface/domain.interface';
import { DomainDetailNewState, domainDetailNewAdapter, domainDetailsApiStateName, domainDetailsApiStateNameNode } from './domain-details-api.state';

const selectDomainDetailsState = createFeatureSelector< DomainDetailNewState>(domainDetailsApiStateName);
export const selectNodeState = createFeatureSelector<any>(domainDetailsApiStateNameNode);
export const selectDomainDetailsSelectors = domainDetailNewAdapter.getSelectors();

export const selectDomainDetails = createSelector(selectDomainDetailsState, (state: Record<string, EntityState<DomainDetailNewState>> | any) => {
  return selectDomainDetailsSelectors.selectAll(state);
});
export const selectDomainDetailsSEntities = createSelector(
  selectDomainDetailsState,
  (state: Record<string, EntityState<DomainDetailNewState>> | any) => {
    return selectDomainDetailsSelectors.selectEntities(state);
  }
);

export const selectCurrentNode =  createSelector(selectNodeState,
  (state: any) => {
    return state;
  }
);

export const selectDomainDetailsNewGetById = createSelector(selectDomainDetailsState,
  selectDomainDetailsSEntities,
  selectCurrentRoute,
  (state:  DomainDetailNewState, entity: any,  route: RouterStateUrl): {
  body?:  DomainDetailsNew,
  status?: boolean,
  errorMessage:string,
  loader: boolean | undefined,
  id: null | string,
  subdomain: null | string,
  tabName: null | string,
  subTheme: null | string,
  key: null | string,
  subdomainId: null | number,
  subThemeId: null | number,
  product: null | string,
} => {
    const data = entity[route.params['id']]?.data;
    const status = entity[route.params['id']]?.status;
    const errorMessage = entity[route.params['id']]?.errorMessage;
    const loader = entity[route.params['id']]?.loader;
    return {
      body: data,
      status: status ? status: false,
      errorMessage: errorMessage ?errorMessage: '',
      loader: loader!== undefined ? loader  : true,
      id: status?  route.params['id']  : null,
      subdomain: route.queryParams['subdomain'] ? route.queryParams['subdomain']: null,
      tabName: route.queryParams['tabName'] ? route.queryParams['tabName']: null,
      subTheme: route.queryParams['subTheme'] ? route.queryParams['subTheme']: null,
      subThemeId: route.queryParams['themeId'] ? route.queryParams['themeId']: null,
      subdomainId: route.queryParams['subId'] ? route.queryParams['subId']: null,
      key: route.queryParams['key'] ? route.queryParams['key']: null,
      product: route.queryParams['product'] ? route.queryParams['product']: null
    };
  });


export const selectDomainDetailsNewGetId = (id: any) => createSelector(selectDomainDetailsState,
  selectDomainDetailsSEntities,
  (state:  DomainDetailNewState, entity: any): {
  body?: DomainDetailsNew,
  status?: boolean,
  errorMessage:string,
  loader: boolean | undefined
} => {
    const data = entity[id]?.data;
    const status = entity[id]?.status;
    const errorMessage = entity[id]?.errorMessage;
    const loader = entity[id]?.loader;
    return {
      body: data,
      status: status ? status: false,
      errorMessage: errorMessage ?errorMessage: '',
      loader: loader!== undefined ? loader  : true
    };
  });
