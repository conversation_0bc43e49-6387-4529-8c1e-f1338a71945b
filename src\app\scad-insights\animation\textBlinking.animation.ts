import { trigger, transition, style, animate, state } from '@angular/animations';

export const textBlinkingAnimation = trigger('textBlinkingAnimation', [
  transition(':enter', [
    style({ width: 0, overflow: 'hidden' }),
    animate('2000ms ease-out',
      style( {
        width: '100%'
      } )
    )
  ])]);

export const typingAnimation = trigger('typingAnimation', [
  state('start', style({
    opacity: 0
  })),
  state('end', style({
    opacity: 1
  })),
  transition('start => end', [
    animate('0ms ease-in')
  ])
]);
