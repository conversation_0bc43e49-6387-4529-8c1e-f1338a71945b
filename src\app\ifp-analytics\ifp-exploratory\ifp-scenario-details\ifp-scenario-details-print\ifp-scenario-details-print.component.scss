@use "../../../../../assets/ifp-styles/abstracts/index" as *;
.ifp-scenario  {
  margin-bottom: $spacer-6;

  &__highchart {
    width: 100%;
    display: block;
    }
    &__loader {
      display: flex;
      align-items: center;
      justify-content: center;
      min-height: 400px;
    }
    &__rating {
      display: flex;
    }
    &__rating-text{
      color:$ifp-color-tertiary-text;
      font-size: $ifp-fs-3;
    }
    &__rating-number {
      font-size: $ifp-fs-8;
      font-weight: $fw-bold;
      color: $ifp-color-black;
      display: flex;
    }
    &__header-icon {
      font-size:$ifp-fs-5;
    }
    &__rating-wrapper  {
      margin: $spacer-2 $spacer-2;
      border: $ifp-color-grey-13 solid 1px;
      padding: $spacer-3;
      border-radius: 10px;
      position: relative;
    }
    &__rating-tooltip {
      position: absolute;
      height: 100%;
      width: 100%;
      top: 0;
      left: 0;
    }
&__icon {
  margin-top: $spacer-1;
  margin-inline-end: $spacer-2;
  &--up {
    color: $ifp-color-green-dark;
  }
  &--down {
    color: $ifp-color-red;
  }
}
&__disclaimer {
  font-size: $ifp-fs-2;
  margin-top: $spacer-3;
&--bold {
  font-weight: $fw-semi-bold;
  font-size: $ifp-fs-3;
  margin-inline-end: $spacer-2;
  .ifp-icon-info-round {
    font-weight: $fw-semi-bold;
    font-size: $ifp-fs-3;
    margin-inline-end: $spacer-2;
  }
}
}


}

