@if(tableData.length !== 0 && !loaderProcess()) {
  <div class="ifp-impute-data">
    <div class="ifp-impute-data__header-wrapper">
      <div class="ifp-impute-data__header">
        <h2 class="ifp-impute-data__title"> {{'Fill Missing Values' | translate}}</h2>
        <h5 class="ifp-impute-data__sub-title"> {{'Replace missing, null or undefined values in a column or field with a specific value to complete the dataset and prevent errors' | translate}}.</h5>
      </div>
      <ifp-button [loader]="loaderProcess()" [label]="'Process' | translate" class="ifp-impute-data__button" [buttonClass]="buttonClass.primaryLight"
      [iconClass]="'ifp-icon-rightarrow'" (ifpClick)="proceed()"></ifp-button>
    </div>
    <div class="ifp-impute-data__header-reset">
      @if (resetView()) {
        <ifp-button  [iconPosition]="postion.left" [label]="'Reset' | translate" class="ifp-impute-data__button" [buttonClass]="buttonClass.inline +'  ifp-btn--light-blue'"
        [iconClass]="'ifp-icon-refresh-round'" (ifpClick)="reset()"></ifp-button>
      }

    </div>
    <div class="ifp-impute-data__table">
      <ifp-data-table [tableData]="tableData"  [tableHead]="tableHead" (customEventEmitter)="eventsCustom($event)"></ifp-data-table>
    </div>

  </div>
} @else {
  @if(loader || loaderProcess()) {
    <div class="ifp-center-loader">
      <app-ifp-spinner></app-ifp-spinner>
    </div>
  } @else {
    <div class="ifp-impute-data">
      <app-ifp-no-data [message]="'No Missing Values'"></app-ifp-no-data>
    </div>

  }

}


<!-- <div class="ifp-impute-data__table-row fp-impute-data__table-row--head">
      <div class="ifp-impute-data__table-col">{{'Fields' | translate}}</div>
      <div class="ifp-impute-data__table-col">{{'Data Type' | translate}}</div>
    </div>
    <div class="ifp-impute-data__table-row fp-impute-data__table-row--head">
      <div class="ifp-impute-data__table-col">{{'Id' | translate}}</div>
      <div class="ifp-impute-data__table-col">
        <div class="ifp-impute-data__table-modify">
          <app-ifp-db-dropdown class="ifp-import__filter-item" [options]="dataTypeList" [key]="'value'"></app-ifp-db-dropdown>
        </div>
        <div class="ifp-impute-data__table-info">

        </div>
      </div>
    </div> -->
<!--
<ifp-data-preview [showProbabilty]="false" class="ifp-prep-data__preview"></ifp-data-preview> -->
<app-ifp-modal [overlayClass]="'ifp-modal__bg-grey'" #modalSucess >

  <ifp-prep-popup (closePopUp)="closeSucessModal()"   (buttonEventValue)="buttonClick($event)" [heading]="'Missing Values Filled in Successfully!'" [desc]="''"  [subTitle]="'Explore More'" [subDesc]="'What would you like to do next?'" ></ifp-prep-popup>

    </app-ifp-modal>
