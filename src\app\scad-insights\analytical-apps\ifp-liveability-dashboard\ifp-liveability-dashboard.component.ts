import { Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core';

import { IfpDashboardCardComponent } from '../../ifp-widgets/ifp-molecules/ifp-dashboard-card/ifp-dashboard-card.component';
import { CommonModule, TitleCasePipe } from '@angular/common';
import { IfpDashboardMapComponent } from 'src/app/scad-insights/ifp-widgets/ifp-molecules/ifp-dashboard-map/ifp-dashboard-map.component';

import { PageData } from 'src/app/scad-insights/core/interface/molecule/breadcrumb.interface';
import { IfpBreadcrumbsComponent } from 'src/app/scad-insights/ifp-widgets/ifp-molecules/ifp-breadcrumbs/ifp-breadcrumbs.component';
import { IfpDescriptionComponent } from 'src/app/scad-insights/ifp-widgets/ifp-molecules/ifp-description/ifp-description.component';
import { IfpHorizontalTabComponent } from 'src/app/scad-insights/ifp-widgets/ifp-molecules/ifp-horizontal-tab/ifp-horizontal-tab.component';
import { IfpTabComponent } from 'src/app/scad-insights/ifp-widgets/ifp-molecules/ifp-tab/ifp-tab.component';
import { DomainsService } from 'src/app/scad-insights/core/services/domains/domains.service';
import { ActivatedRoute } from '@angular/router';
import { IfpRangeChartComponent } from '../../ifp-widgets/ifp-molecules/ifp-range-chart/ifp-range-chart.component';
import { TranslateModule } from '@ngx-translate/core';
import { IfpCardLoaderComponent } from '../../ifp-widgets/ifp-atoms/ifp-card-loader/ifp-card-loader.component';
import { LineBreakPipe } from 'src/app/scad-insights/core/pipes/lineBreak.pipe';
import { IfpNoDataComponent } from '../../ifp-widgets/ifp-molecules/ifp-no-data/ifp-no-data.component';
import { UsageDashboardLogService } from '../../core/services/usage-dashboard-log/usage-dashboard-log.service';
import { logType } from '../../core/services/usage-dashboard-log/usage-dashboard.constants';
import { SubSink } from 'subsink';



@Component({
    selector: 'app-ifp-liveability-dashboard',
    templateUrl: './ifp-liveability-dashboard.component.html',
    styleUrls: ['./ifp-liveability-dashboard.component.scss'],
    providers: [TitleCasePipe, LineBreakPipe],
    imports: [IfpBreadcrumbsComponent, IfpHorizontalTabComponent, IfpDashboardCardComponent, CommonModule, IfpDescriptionComponent, IfpTabComponent, IfpDashboardMapComponent, IfpRangeChartComponent,
    TranslateModule, IfpCardLoaderComponent, IfpNoDataComponent]
})
export class IfpLiveabilityDashboardComponent implements OnInit, OnDestroy {

  public productSelectIndex!: number;
  public readMoreLength: number = 300;
  public selectedTab!: number;
  public liveabilityResponse: any = [];
  public cardData: any = [];
  public isDataLabel:boolean=false;
  public compositionData: any = [];
  public isLoading:boolean=false;
  public compositionSubTitle: string = 'Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industrys standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book';
  public domain!: string;
  public updatedData: any;
  public ratingPoints = [
    {
      color: '#1a9641',
      value: '4.5 - 5.0',
      _id: 1
    },
    {
      color: '#a6d96a',
      value: '4.0 - 4.5',
      _id: 2
    },
    {
      color: '#fee08b',
      value: '3.5 - 4.0',
      _id: 3
    },
    {
      color: '#fdae61',
      value: '3.0 - 3.5',
      _id: 4
    },
    {
      color: '#d7191c',
      value: '<3.0',
      _id: 5
    }
  ];

  pageData: PageData[] = [
    {
      title: 'Home',
      route: '/home'
    },
    {
      title: 'Liveability Dashboard',
      route: ''
    }
  ];

  public ratingData = [];


  public liveabilityOptions: any[] = [];
  public id: any;
  public subs = new SubSink();
  chartFormat = [
    'Grouped',
    'Ranked'
  ];

  public weightProfile = [
    'Economic Liveability Index',
    'Include All Indicators'
  ];

  public horizontalTabList: any = [];
  private sessionId!: string;

  constructor(private titleCase: TitleCasePipe, private _domainService: DomainsService, private route: ActivatedRoute, private LinebreakPipe: LineBreakPipe, private log: UsageDashboardLogService) {
    this.subs.add( this.route.params.subscribe(resp => {
      this.id = resp['id'];
    }));
  }

  ngOnInit() {
    if (this.sessionId) {
      this.log.logEnds(this.sessionId, this.log.currentTime );
    }
    this.sessionId = this.log.createUUid;
    this.log.logStart(this.sessionId, logType.indicator, this.log.currentTime, this.id);
    this.getLiveabilityData();
  }

  getLiveabilityData() {
    this.isDataLabel=false;
    this.isLoading=true;
    const payload: any = {
      id: this.id
    };
    if (this.selectedTab == 0 || this.selectedTab > 0) {
      payload.parent_category = encodeURIComponent(this.liveabilityOptions[this.selectedTab].category);
    }

    if (this.productSelectIndex == 0 || this.productSelectIndex > 0) {
      payload.category = encodeURIComponent(this.horizontalTabList[this.productSelectIndex].category);
    }
    const subs =  this._domainService.getLiveabilityDashboard(payload).subscribe({ next: resp => {
      if (!this.selectedTab) {
        this.selectedTab = 0;
      }
      this.liveabilityResponse = resp;
      this.liveabilityOptions = resp.dashboardPrimaryCategory;
      if (this.liveabilityOptions?.length > 0) {
        this.liveabilityOptions.forEach(element => {
          if (element.category) {
            element.name = this.titleCase.transform(element.category);
            element.disabled = element.isActive == 0 ? true : false;
          }
        });
      }

      this.horizontalTabList = resp.dashboardSecondaryLevels;
      if (this.horizontalTabList?.length > 0) {
        this.horizontalTabList.forEach((element: { name: string; category: string; }) => {
          if (element.category) {
            element.name = this.titleCase.transform(element.category);
          }
        });
      }
      this.cardData = resp.dashboardData;
      if (this.cardData?.length > 0) {
        this.cardData.forEach((element: { isCollapsedView: boolean; }) => {
          element.isCollapsedView = true;
        });
      }
      if (resp.dashboardComposition) {
        this.compositionData = resp.dashboardComposition;
        this.compositionData.description = this.checkData(this.compositionData.description);
      }
      this.domain = resp.domain;
      this.updatedData = resp.updated;
      this.isDataLabel=true;
      this.isLoading=false;
      subs.unsubscribe();
    }, error: _error=>{
      this.isLoading=false;
      subs.unsubscribe();
    }});
  }

  tabClick(event: any) {
    this.selectedTab = event.index;
    this.productSelectIndex = -1;
    this.getLiveabilityData();
  }

  selectTab(event: any) {
    this.productSelectIndex = event.index;
    this.getLiveabilityData();
  }

  checkExpand(event: any, card: any) {
    card.isCollapsedView = event;
  }

  getNoteKey() {
    let isWeight: boolean = false;
    if (this.compositionData?.data?.length > 0) {
      isWeight = this.compositionData.data.some((x: { inLiveabilityIndex: number; }) => x.inLiveabilityIndex == 0);
    }
    return isWeight;
  }

  getWeightedKey() {
    let isWeight: boolean = false;
    if (this.compositionData?.data?.length > 0) {
      isWeight = this.compositionData.data.some((x: { activePercent: number; }) => x.activePercent == 0);
    }
    return isWeight;
  }


  checkData(data: string) {
    const pipeData = this.LinebreakPipe.transform(data);
    const cnt = pipeData.split('<br>');
    let finalCnt: string = '';
    const index = cnt.findIndex((x: string | string[]) => x.includes('Source:'));
    const source = cnt[index];
    cnt.splice(index, 1);
    cnt.forEach((element: any) => {
      finalCnt = `${finalCnt}${element}${element ? '<br><br>' : ''}`;
    });
    return `${finalCnt}<span class="ifp-module-source">${source}</span>`;
  }

  ngOnDestroy(): void {
    this.subs.unsubscribe();
    if (this.sessionId) {
      this.log.logEnds(this.sessionId, this.log.currentTime );
    }
  }
}

