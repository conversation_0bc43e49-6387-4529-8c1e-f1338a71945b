import { ChangeDetectionStrategy, Component, EventEmitter, inject, Input, OnChanges, Output, signal, WritableSignal } from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';
import { IfpCheckBoxComponent } from '../../../scad-insights/ifp-widgets/ifp-atoms/ifp-check-box/ifp-check-box.component';
import { IfpDbDropdownComponent } from '../../../dashboard-builder/molecule/ifp-db-dropdown/ifp-db-dropdown.component';
import { IfpDataToolAccordianComponent } from '../../molecule/ifp-data-tool-accordian/ifp-data-tool-accordian.component';
import { IfpBackButtonComponent } from '../../../ifp-widgets/atoms/ifp-back-button/ifp-back-button.component';
import { NgClass } from '@angular/common';
import { IfpButtonComponent } from '../../../scad-insights/ifp-widgets/ifp-atoms/ifp-button/ifp-button.component';
import { buttonClass } from 'src/app/scad-insights/core/constants/button.constants';
import { FormGroup, FormControl, Validators, ReactiveFormsModule } from '@angular/forms';
import { DbDropDown } from 'src/app/dashboard-builder/molecule/ifp-db-dropdown/ifp-db-dropdown.interface';
import { ToasterService } from 'src/app/scad-insights/core/services/tooster/ToastrService.service';
import { IfpAdvancePrepService } from '../../data-prep/ifp-advance-prep/service/ifp-advance-prep.service';
import { selectNodeStoreAdvance } from '../../data-prep/ifp-advance-prep/store/advance-node-store/advance-node.store';
import { connectionType } from '../../data-prep/ifp-data-prep/constants/if-preps.constants';
import { ColumnPrep } from '../../data-prep/ifp-data-prep/ifp-prep-select-data/interface/prep-selection.interface';
import { IfpSpinnerComponent } from '../../../scad-insights/ifp-widgets/ifp-molecules/ifp-spinner/ifp-spinner.component';

import { CdkDrag, CdkDragDrop, CdkDropList, moveItemInArray } from '@angular/cdk/drag-drop';
@Component({
  selector: 'ifp-adv-sort-tool',
  standalone: true,
  templateUrl: './ifp-adv-sort-tool.component.html',
  styleUrl: './ifp-adv-sort-tool.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [TranslateModule, IfpCheckBoxComponent, IfpDbDropdownComponent, IfpDataToolAccordianComponent, IfpBackButtonComponent, NgClass, IfpButtonComponent, IfpSpinnerComponent, CdkDrag, CdkDropList, ReactiveFormsModule]
})
export class IfpAdvSortToolComponent implements OnChanges{

  @Output() cancel = new EventEmitter();
  @Output() save = new EventEmitter();
  @Input() sortOrder: {name: string, value:string}[] = [
    {
      name: 'Ascending',
      value: 'asc'
    },
    {
      name: 'Descending',
      value: 'desc'

    }
  ];

  @Input({required: true}) columnList!:ColumnPrep[];
  @Input() dataTypes: DbDropDown[] = [];
  @Input() workFlowRunning = signal(false);
  @Input() emptyConnection = signal(true);
  @Input() currentNodeId: WritableSignal<string | null> = signal('');
  @Input() currentConection: WritableSignal<boolean> = signal(false);
  @Input() sourceData: WritableSignal<boolean> = signal(false);
  constructor(public _prepService: IfpAdvancePrepService,  private _toaster: ToasterService) {}
  public selectAll: boolean = true;
  public selectRecords: boolean = true;
  public buttonClass = buttonClass;
  public formGroup:  FormGroup | any=  new FormGroup({});
  public readonly advanceStore = inject(selectNodeStoreAdvance);
  public columnListView:ColumnPrep[] =[];
  public savedValue = false;

  ngOnChanges(): void {
    this.formGroup =  new FormGroup({});
    this.columnListView = [];
    const nodeData = this.advanceStore.selectNodeValue().nodeValue(this.currentNodeId() ?? '');


    this.columnList?.forEach((element:ColumnPrep, index: number) => {

      if (nodeData?.settings?.columns) {
        const formGropCloumn  =  new FormGroup({
          'type': new FormControl(nodeData.settings.columns[element.name].type, Validators.required)
        }
        );
        element['order'] = nodeData.settings.columns[element.name].order;
        element['currentType'] = nodeData.settings.columns[element.name].type;
        this.formGroup.addControl(element.name, formGropCloumn);
      } else {
        const formGropCloumn  =  new FormGroup({
          'type': new FormControl( this.sortOrder[0], Validators.required)
        });
        element['order'] = index;
        element['currentType'] = this.sortOrder[0];
        this.formGroup.addControl(element.name, formGropCloumn);
      }
      this.columnListView.push({...element});
    });
    this.columnList?.sort((a, b) => (a.order ?? 0) - ( b.order ?? 0));
    this.columnListView.sort((a, b) => (a.order ?? 0) - ( b.order ?? 0));
  }

  resetSortOrder() {
    this.columnListView = [];
    this.columnList?.forEach((element:ColumnPrep) => {
      this.columnListView.push({...element});
    });
  }


  drop(event: CdkDragDrop<ColumnPrep[]>) {
    this.savedValue = false;
    moveItemInArray(this.columnListView, event.previousIndex, event.currentIndex);
  }


  onCancel() {
    this.cancel.emit();
  }

  selectedItem(event: {isSelected: boolean; item: string;}, index: number) {
    this.savedValue = false;
    this.columnListView[index]['isSelected'] = event.isSelected;
  }

  checkValidation() {
    let orderChange = false;
    let formValidationChange = false;
    this.columnList.forEach((data, index) => {
      if (this.columnListView[index].name !== data.name ) {
        orderChange = true;
      }
      if (this.formGroup.value[data.name].type.value !==  data.currentType?.value) {
        formValidationChange = true;
      }

    });

    if (this.savedValue) {
      return false;
    }
    return orderChange || formValidationChange;


  }

  onSave() {
    this.savedValue = true;
    const configration: Record<string, any> = {};
    const setting: Record<string, any>  = {};
    this.columnListView.forEach((element:ColumnPrep, index: number) => {
      configration[element.name]= {
        order: this.formGroup.value[element.name].type.value,
        priority: index +1
      };
      setting[element.name] = { type: this.formGroup.value[element.name].type, order: index};
    });
    this.advanceStore.updateNode(this.currentNodeId() ?? '', 'selectool updateded', connectionType.sortTool,  {columns: configration}, 'sorttool updateded', {columns: setting,  configUpdated: true});
    this._prepService.nodeChangeDetect.next('node updated');
    // this._toaster.success('Data Saved Successfully');
    this.save.emit();
  }
}

