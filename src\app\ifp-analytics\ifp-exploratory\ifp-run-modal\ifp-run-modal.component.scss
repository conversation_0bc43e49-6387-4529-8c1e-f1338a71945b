@use "../../../../assets/ifp-styles/abstracts/index" as *;
.ifp-auto-ml-upload {
  display: flex;
  min-height:  calc(100vh - 400px);
  flex-direction: column;

  margin: auto;
  &__title {
    font-weight: $fw-bold;
    font-size: $ifp-fs-12;
    text-align: center;
    padding-top: $spacer-3;
  }
  &__data {
    display: block;
    margin-top: $spacer-6;
    margin-bottom: $spacer-6;
  }
  &__image{
    margin-top: auto;
    border-bottom: 10px solid $ifp-color-section-white;
    &--remove-line {
      border-bottom:none;
    }
  }
  &__img {
    width: 100%;
  }
  &__freq{
    display: flex;
    align-items: center;
  }
  &__freq-wrapper {
    display: flex;
    justify-content: center;
    align-items: center;

    margin-top: $spacer-3;
  }
  &__dropdown-wrapper {
    display: flex;
    justify-content: center;
    margin-top: $spacer-4;
    @include mobile-tablet {
      flex-wrap: wrap;
    }
  }
  &__freq-head {
    color: $ifp-color-grey-14;
    font-size: $ifp-fs-4;
    margin: $spacer-0 $spacer-2;
  }
  &__freq-text {
    font-size: $ifp-fs-4;
    font-weight: $fw-semi-bold;
  }
  &__freq-icon{
    font-size: $ifp-fs-6;
  }
  &__outer {
    display: flex;
    min-height: 400px;
    align-items: center;
    justify-content: center;
  }
  &__button-wrapper {
    display: flex;
    justify-content: center;
    margin-top: $spacer-5;
  }
  &__popup-wrapper{
    display: flex;
    justify-content: center;
  }
  &__popup {
   background-color: $ifp-color-section-white;
   padding: $spacer-3;
    margin-top: $spacer-3;
    border-radius: 10px;
    display: inline-block;
  }
  &__popup-inner {
    display: flex;
  }
  &__popup-circle {
    margin-inline-end: $spacer-3;
  }
  &__popup-percentage {
    font-weight: $fw-semi-bold;
    font-size: $ifp-fs-4;
  }
  &__input-text {
    font-size: $ifp-fs-5;
    margin: $spacer-2 $spacer-0;
    color: $ifp-color-grey-2;
    text-transform: capitalize;
    white-space: nowrap;
    display: inline-flex;
    text-overflow: ellipsis;
    @include desktop-sm  {
      font-size: $ifp-fs-4;
    }
  }
  &__info {
    display: inline-flex;
    margin-inline-start: $spacer-2;
    white-space: break-spaces;
    text-transform: none;
  }
  &__input{
    min-width: 342px;
    max-width: 342px;
    min-height: 55px;
    background: $ifp-color-white;
    border: 1px solid $ifp-color-grey-7;
    border-radius: 7px;
    font-size: $ifp-fs-6;
    padding: $spacer-2 $spacer-3;
    color:$ifp-color-black;
    @include desktop-sm  {
      min-width: 100%;
      max-width: 100%;
    }
  }
  &__input-info {
    margin-top: $spacer-1;
    color: $ifp-color-grey-14;
  }
  &__error{
    min-height: 100px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
  }
  &__error-text {
    font-size: $ifp-fs-6;
      margin-top: $spacer-2;
      color:$ifp-color-red;
  }
  &__error-btn {
    margin-top: $spacer-3;
  }
  &__desc{
    margin-top: $spacer-2;
    font-size: $ifp-fs-3;
    text-align: center;
  }
  &__option {
    margin: $spacer-0 $spacer-3;
    width: calc(33.33% - $spacer-5);
    @include mobile-tablet {
      width: calc(50% - $spacer-5);
      margin: $spacer-2 $spacer-3;
    }
  }
  &__btn-view{
    margin-inline-end: $spacer-3;
  }
  &__chart-title {
    margin: $spacer-3 $spacer-0;
    font-weight: $fw-bold;
    font-size: $ifp-fs-12;
    text-align: center;
  }
  &__chart {
    margin-bottom: $spacer-3;
  }
}
:host::ng-deep {
  .ifp-auto-ml-upload {
    &__dropdown {
      .ifp-dropdown {
        min-width: 342px;
        max-width: 342px;
        min-height: 55px;
        @include desktop-sm  {
          min-width: 100%;
          max-width: 100%;
        }
        &__selected  {
          font-size: $ifp-fs-4;
          font-weight: $fw-semi-bold;
          padding: $spacer-3;
        }
        &__list{
          width: 100%;
        }
      }
      .ifp-dropdown__title {
        font-weight: $fw-regular;
        text-transform: capitalize;
        width: 342px;
        font-size: $ifp-fs-5;
        @include desktop-sm  {
          min-width: 100%;
          max-width: 100%;
          font-size: $ifp-fs-4;
        }
      }
    }
    .ifp-btn {
      padding: $spacer-3 $spacer-6;
      @include desktop-sm {
        padding: $spacer-3;
      }
    }

  }
}

@include desktop-sm {
  .ifp-auto-ml-upload {
    &__chart-title,
    &__title {
      font-size: $ifp-fs-9;
   }
  }
}
