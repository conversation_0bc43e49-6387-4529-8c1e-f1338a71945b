export const autoMlApiEndpoints = {
  // frequency
  getFrequencyApi: '/v1/automl/dataset/',
  frequency: '/infer-frequency',
  // run model
  runModel: '/v1/automl/workflow/create/',
  streamApi: '/v1/automl/workflow/',
  streamProcess: '/stream/progress/',

  // auto ml iteration List
  iteration: '/v1/automl/workflow/',
  iterationList: '/iteration/list/',
  iterationUpdate: '/v1/iteration/',
  iterationUpdate1: '/update/',
  iterationColumns: '/v1/automl/workflow/',
  iterationColumn: '/columns',
  iterationDelete: '/delete/',
  // correlation
  correlation: '/correlation/',
  correlationWorkFlow: '/v1/automl/workflow/',
  correlationIteration: '/iteration/create/',
  correlationStream: '/v1/iteration/',
  // scenario Api
  runScenarioIteration: '/v1/iteration/',
  runScenario: '/scenario/run/',
  scenarioStream: '/v1/scenario/',
  // scenario detail page
  scenarioDetail: '/v1/iteration/scenario/',
  scenarioChart: '/chart/',
  scenarioSave: '/v1/iteration/scenario/',
  scenarioUpdate: '/update/',
  scenarioList: '/v1/scenario/list/',
  exploratoryPreviewChart: '/v1/automl/workflow/',
  exploratoryPreviewChartView: '/chart/',
  scenarioDelete: '/v1/scenario/',
  scenarioDeleteTag: '/delete/',

  // dataset api
  dataset: '/dataset/',
  streamData: '/stream-records/',

  // univariate
  univariateWorkflow: '/v1/automl/workflow-univariate/create/'
};
