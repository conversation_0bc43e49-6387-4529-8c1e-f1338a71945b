trigger: none

variables:
  app_name: 'scad-insights'
  registry: '$(OpenShift.Registry)/azure-devops'
  ouser: '$(OpenShift.Registry.Username)'
  opassword: '$(OpenShift.SA.Token)'
  openshift_url: '$(OpenShift.API.Server)'

  drregistry: '$(OpenShift.Dr.Registry)/azure-devops'
  druser: '$(OpenShift.Dr.Registry.Username)'
  drpassword: '$(OpenShift.Dr.SA.Token)'
  drpenshift_url: '$(OpenShift.Dr.API.Server)'

jobs:
- job: Init
  pool: 'oc-light-agent'
  steps:
  - script: |
        export PATH="$PATH:/opt/app-root/.dotnet/tools"
        SEMVER=$(dotnet-gitversion /config /opt/app-root/app/gitversion.yml /showvariable SemVer)
        echo "##vso[task.setvariable variable=version]$SEMVER"
        echo "##vso[task.setvariable variable=version;isOutput=true]$SEMVER"
        echo "##vso[build.updatebuildnumber]$SEMVER"
    name: setvarStep
    displayName: 'Generating version'
    failOnStderr: "true"


- job: Build
  pool: 'oc-light-agent'
  dependsOn: Init
  variables:
    version: $[ dependencies.Init.outputs['setvarStep.version'] ]
  steps:
  - task: NodeTool@0
    inputs:
      versionSpec: '18.x'
  - script: |
      sed -i 's/$CLIENTID/'"'$CLIENTID'"'/' $(Build.SourcesDirectory)/src/environment.js
      sed -i 's/$TENANTID/'"'$TENANTID'"'/' $(Build.SourcesDirectory)/src/environment.js
      sed -i 's/$DOMAIN/'"'$DOMAIN'"'/' $(Build.SourcesDirectory)/src/environment.js
      sed -i 's/$USERLIST/'"'$USERLIST'"'/' $(Build.SourcesDirectory)/src/environment.js
      cat  $(Build.SourcesDirectory)/src/environment.js
    displayName: 'prepare environment.js'
  - script: |
      export NODE_OPTIONS=--max_old_space_size=8192
      npm install
      npm run prod
    failOnStderr: "false"
    displayName: 'npm install'


  - script: |

    failOnStderr: "true"
    displayName: 'login to buildah registry'
  - script: |
      buildah build --ulimit nofile=4096:4096  -t $(registry)/$(app_name):$(version) -f operations/dockerfiles/dev.dockerfile .

      buildah login --tls-verify=false $(registry) --username $(ouser) --password $(opassword)
      buildah push --tls-verify=false $(registry)/$(app_name):$(version)

      # buildah login --tls-verify=false $(drregistry) --username $(druser) --password $(drpassword)
      # buildah tag $(registry)/$(app_name):$(version) $(drregistry)/$(app_name):$(version)
      # buildah push --tls-verify=false $(drregistry)/$(app_name):$(version)
    displayName: 'Building and pushing image to buildah registry $(registry)/$(app_name):$(version) $(drregistry)/$(app_name):$(version)'




