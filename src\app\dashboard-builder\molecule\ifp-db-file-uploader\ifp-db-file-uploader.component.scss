@use "../../../../assets/ifp-styles/abstracts/index" as *;
.ifp-file-upload {
  display: flex;
  align-items: center;
  position: relative;
  &__blue {
    background-color: $ifp-color-blue-menu;
  }
  &__inner {
    padding: $spacer-3;
    border: 1px dashed $ifp-color-grey-7;
    display: flex;
    align-items: center;
    background-color: $ifp-color-grey-10;
    border-radius: 5px;
    min-width: 200px;
    cursor: pointer;
    width: 100%;
    &--uploaded {
      border: 1px solid $ifp-color-grey-7;
    }
  }
  &__file-icon {
    width: 20px;
    height: auto;
    font-size: $ifp-fs-6;
    color: $ifp-color-tertiary-text;
    margin-inline-end: $spacer-2;
  }
  &__text {
    color: $ifp-color-tertiary-text;
    display: flex;
    margin-inline-end: $spacer-2;
    .ifp-link {
      font-size: inherit;
    }
  }
  &__preview {
    width: 100%;
    padding: $spacer-2;
  }
  &__file-preview {
    max-height: 40px;
    width: auto;
  }
  &__del-logo {
    font-size: $ifp-fs-6;
    display: block;
    margin-inline-start: $spacer-3;
    cursor: pointer;
    transition: 0.3s;
    &:hover {
      color: $ifp-color-red;
    }
  }
  &--lg {
    .ifp-file-upload {
      &__inner {
        padding: $spacer-2 $spacer-0;
        justify-content: center;
        border: 1px dashed $ifp-color-blue-border-light;
        background-color: $ifp-color-blue-bg-light;
      }
      &__logo,
      &__text-wrapper {
        margin: $spacer-0 $spacer-3;
      }
      &__logo {
        width: 46px;
        height: 46px;
        line-height: 55px;
        border-radius: 50%;
        color: $ifp-color-primary-grey;
        text-align: center;
        .ifp-icon {
          font-size: $ifp-fs-13;
          color: $ifp-color-white-global;
        }
      }
      &__text-wrapper {
        text-align: start;
      }
      &__text {
        font-size: $ifp-fs-6;
        font-weight: $fw-medium;
        color: $ifp-color-primary-grey;
        margin: $spacer-0;
        .ifp-link {
          margin: $spacer-0 $spacer-2;
        }
      }
      &__note {
        font-size: $ifp-fs-4;
        color: $ifp-color-tertiary-text;
        margin-top: $spacer-2;
      }
    }
  }

  &__bar-wrapper {
   margin-top: $spacer-2;
   border: 1px solid $ifp-color-grey-7;
   border-radius: 10px;

  }
  &__bar {
    border-radius: 10px;
    color:  $ifp-color-white-global;
    background-color: $ifp-color-secondary-blue;
    text-align: center;
    padding: $spacer-1;
    min-width: 50px ;
  }
  &__uploaded-file {
    display: flex;
    align-items: center;
    margin-top: $spacer-3;
    text-align: start;
  }
  &__file-name {
    color: $ifp-color-tertiary-text;
    max-width: 100%;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
  }
  &__info {
    position: absolute;
    right: 10px;
    top: 10px;
    border-radius: 50%;
    background-color: $ifp-color-link;
    width: 28px;
    height: 28px;
    display: inline-flex;
    justify-content: center;
    align-items: center;
    z-index: 99;

    .ifp-tooltip {
      visibility: hidden;
    }
    &:hover {
      .ifp-tooltip {
        opacity: 1;
        visibility: visible;
      }
    }
  }
  &__info-icon {
    padding: $spacer-4;
    color: $ifp-color-white-global;
    position: relative;
    top: 2px;

  }
  &__info-tooltip {
    width: 500px;
    text-align: start;
    padding: $spacer-3;
    top: 50%;
    right: 100%;
    transform: translateY(-50%);
    margin-right: $spacer-1;
    pointer-events: all;
  }

}
:host-context([dir="rtl"]) {
  .ifp-file-upload__limit {
    direction: ltr;
    display: inline-block
  }
}

@include mobile-tablet {
  .ifp-file-upload--lg {
    .ifp-file-upload__logo {
      margin: $spacer-0 $spacer-2;
      height: 40px;
      width: 40px;
      line-height: 50px;
    }
  }
}
