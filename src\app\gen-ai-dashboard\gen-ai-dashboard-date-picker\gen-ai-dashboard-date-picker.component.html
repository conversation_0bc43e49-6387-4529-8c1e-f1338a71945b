<div  class="ifp-gen-ai-dashboard-date-picker" (click)="picker.open()" (keyup)="picker.open()">
  <!-- <em class="ifp-icon ifp-icon-calender ifp-usage-dashboard__calender"></em> -->
  <!-- <span>
    {{startDate.value | date : 'MMM d, y' }} - {{endDate.value | date : 'MMM d, y' }}
  </span> -->
  <div class="ifp-gen-ai-dashboard-date-picker__wrapper">
    <span class="ifp-gen-ai-dashboard-date-picker__text">
      {{'Date range' | translate}}
    </span>

    <em class="ifp-icon ifp-icon-calender ifp-gen-ai-dashboard-date-picker__calender"></em>
    <mat-date-range-input   [max]="maxDate" [rangePicker]="picker" class="ifp-gen-ai-dashboard-date-picker__picker">
      <input matStartDate   (dateInput)="dateChangeValue()"
    [formControl]="startDate()" placeholder="Start date" >
      <input matEndDate  [formControl]="endDate()" placeholder="End date" >
    </mat-date-range-input>
  </div>



<mat-date-range-picker #picker  (closed)="dateChange()"></mat-date-range-picker>
</div>
