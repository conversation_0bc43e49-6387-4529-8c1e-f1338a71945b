import { buttonClass } from 'src/app/scad-insights/core/constants/button.constants';
import { Component, inject } from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';
import { CommonApiService } from 'src/app/scad-insights/core/services/common-api/commonApi.service';
import { ThemeService } from 'src/app/scad-insights/core/services/theme/theme.service';

@Component({
  selector: 'ifp-census-domain-nav',
  imports: [TranslateModule],
  templateUrl: './census-domain-nav.component.html',
  styleUrl: './census-domain-nav.component.scss'
})
export class CensusDomainNavComponent {

  private readonly _commonApiService: CommonApiService = inject(CommonApiService);
  private readonly _themeService: ThemeService = inject(ThemeService);

  public buttonClass = buttonClass;
  public censusLink = this._commonApiService.censusData?.url.uri;
  public censusLogo = this._commonApiService.censusData?.logo;
  public isDark = localStorage.getItem('appearence') === 'dark';

  constructor() {
    this._themeService.defaultTheme$.subscribe((theme) => {
      this.isDark = theme === 'dark';
    })
  }
}
