
import { ChangeDetectionStrategy, Component, EventEmitter, inject, Input, OnChang<PERSON>, OnDestroy, Output, signal, WritableSignal } from '@angular/core';
import { IfpDataToolAccordianComponent } from '../../molecule/ifp-data-tool-accordian/ifp-data-tool-accordian.component';
import { IfpCheckBoxComponent } from 'src/app/scad-insights/ifp-widgets/ifp-atoms/ifp-check-box/ifp-check-box.component';
import { TranslateModule } from '@ngx-translate/core';
import { DbDropDown } from 'src/app/dashboard-builder/molecule/ifp-db-dropdown/ifp-db-dropdown.interface';
import { IfpButtonComponent } from '../../../scad-insights/ifp-widgets/ifp-atoms/ifp-button/ifp-button.component';
import { buttonClass } from 'src/app/scad-insights/core/constants/button.constants';
import { ColumnPrep, PrepColumnInterface } from '../../data-prep/ifp-data-prep/ifp-prep-select-data/interface/prep-selection.interface';
import { Form<PERSON>rray, FormBuilder, FormControl, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { CdkDrag, CdkDragDrop, CdkDropList } from '@angular/cdk/drag-drop';
import { selectNodeStoreAdvance } from '../../data-prep/ifp-advance-prep/store/advance-node-store/advance-node.store';
import { IfpAdvancePrepService } from '../../data-prep/ifp-advance-prep/service/ifp-advance-prep.service';
import { connectionType } from '../../data-prep/ifp-data-prep/constants/if-preps.constants';
import { IfpSpinnerComponent } from '../../../scad-insights/ifp-widgets/ifp-molecules/ifp-spinner/ifp-spinner.component';
import { ToasterService } from 'src/app/scad-insights/core/services/tooster/ToastrService.service';
import { IfpDropdownComponent } from 'src/app/scad-insights/ifp-widgets/ifp-atoms/ifp-dropdown/ifp-dropdown.component';
import { NgClass } from '@angular/common';
import { cloneDeep } from 'lodash';


@Component({
    selector: 'ifp-adv-select-tool',
    templateUrl: './ifp-adv-select-tool.component.html',
    styleUrl: './ifp-adv-select-tool.component.scss',
    changeDetection: ChangeDetectionStrategy.OnPush,
    providers: [IfpAdvancePrepService],
    imports: [IfpDataToolAccordianComponent, IfpCheckBoxComponent, TranslateModule, IfpButtonComponent, ReactiveFormsModule, CdkDropList, CdkDrag, IfpSpinnerComponent,
    IfpDropdownComponent, NgClass]
})
export class IfpDataSelectToolComponent implements OnChanges, OnDestroy {

  @Input({ required: true }) columnList!: ColumnPrep[];
  @Input() dataTypes: DbDropDown[] = [];
  @Input() workFlowRunning = signal(false);
  @Input() emptyConnection = signal(true);
  @Input() currentNodeId?: string='';
  @Input() currentConection: WritableSignal<boolean> = signal(false);
  @Input() sourceData: WritableSignal<boolean> = signal(false);
  @Input() errorMessageForTools?: string;
  @Output() cancel = new EventEmitter();
  @Output() save = new EventEmitter();
  constructor(public _prepService: IfpAdvancePrepService, private _toaster: ToasterService,
    private _fb: FormBuilder
  ) { }


  public selectAll: boolean = true;
  public selectRecords: boolean = true;
  public buttonClass = buttonClass;
  public formGroup: FormGroup | any = new FormGroup({});
  public readonly advanceStore = inject(selectNodeStoreAdvance);
  public columnListView: ColumnPrep[] = [];
  public submitted: WritableSignal<boolean> = signal(false);
  public savedValue = false;
  public firstTime = false;
  public nodeValue: Record<string, NodeData> = {};
  public allowedTypes: string[] = [];
  public dataTypeList: { column: string, type: string, rename?: string, initiaType?: string }[] = [];
  public disableCoumnDropDown: WritableSignal<boolean> = signal(false);
  public columnSorted: WritableSignal<boolean> = signal(false);
  public isConfigUpdated: WritableSignal<boolean> = signal(false);
  public columnListBackup!: ColumnPrep[];
  public disableChange: boolean = false;

  ngOnChanges(): void {
    this.formGroup = this._fb.group({
      column: ['', Validators.required],
      dataType: ['', Validators.required],
      rename: [''],
      columns: this._fb.array([])
    });
    this.columnSorted.set(false);
    this.columnListBackup = cloneDeep(this.columnList);
    if (this.columnList?.length !== 0) {
      this.dataTypeList = [];
      this.setInitialColumns();
    }
  }


  get columnArray(): FormArray<FormGroup<ColumnsArray>> {
    return this.formGroup?.get('columns') as FormArray<FormGroup<ColumnsArray>>;
  }

  setInitialColumns() {
    const nodeData = this.advanceStore.selectNodeValue().nodeValue(this.currentNodeId ?? '');
    if (nodeData?.configuration?.update) {
      this.columnList = this.setColumnOrder(nodeData?.configuration?.update);
    }
    if (nodeData?.configuration?.update && Object.keys(nodeData?.configuration?.update).length && this.columnList?.length) {
      this.dataTypeList = cloneDeep(nodeData.settings?.dataTypeList) ?? [];
    }
    this.columnList.forEach((element: ColumnPrep, index: number) => {
      element.allowed_types = ['int', 'float', 'double', 'string', 'long'];
      element.checked = nodeData?.configuration?.remove?.includes(element?.name) ? false : true;
      element['order'] = nodeData?.configuration?.update?.[element?.name]?.order ?? index;
      element.hide = (nodeData?.configuration?.remove?.includes(element?.name) || this.dataTypeList?.find(x => x.column == element?.name)) ? true : false;
      element.rename = nodeData?.configuration?.update?.[element?.name]?.name ?? element?.name;
      this.columnArray?.push(this.addColumn(element));
    });
    setTimeout(() => {
      this.resetColumns();
    }, 50);
  }

  setColumnOrder(updates: Record<string, any>) {
    return Object?.keys(updates)?.length ? this.columnList
      .map(column => ({ ...column, order: updates[column.name]?.order }))
      .sort((a, b) => (a.order ?? this.checkHighestOrderCount(a)) - (b.order ?? this.checkHighestOrderCount(b))) : cloneDeep(this.columnList);
  }

  checkHighestOrderCount(column: any) {
    const nodeData = this.advanceStore.selectNodeValue().nodeValue(this.currentNodeId ?? '');
    return nodeData.settings.removedColumns.find((x: { name: any; }) => x.name == column.name)?.order;
  }

  addColumn(element: ColumnPrep) {
    const item = this._fb.group({
      name: this._fb.control(element.name, Validators.required),
      dataType: this._fb.control(element.type),
      checked: this._fb.control(element.checked),
      rename: this._fb.control(element.rename)
    });
    return item;
  }

  selectColumn(event: any) {
    this.disableChange = true;
    this.allowedTypes = event.allowed_types;
    this.disableCoumnDropDown.set(false);
    setTimeout(() => {
      this.formGroup.controls['dataType'].setValue(event.type);
    }, 50);
  }

  get f() {
    return this.formGroup.controls;
  }


  selectType(event: string) {
    const initialType = this.columnList?.find((x: ColumnPrep) => x?.name == this.formGroup?.value?.['column']?.name)?.type;
    if (initialType === event && this.formGroup.value['rename'].trim() === '') {
      this.disableChange = true;
    } else {
      this.disableChange = false;
    }
  }

  renameColumn(event: any) {
    const initialType = this.columnList?.find((x: ColumnPrep) => x.name == this.formGroup?.value?.['column']?.name)?.type;
    if (initialType === this.formGroup?.value?.['dataType'] && event?.target?.value?.trim() === '') {
      this.disableChange = true;
    } else {
      this.disableChange = false;
    }
  }

  addDataType() {
    this.submitted.set(true);
    const initialType = this.columnList?.find((x: ColumnPrep) => x?.name == this.formGroup?.value?.['column']?.name)?.type;
    if (this.formGroup.value['column'].name && this.formGroup.value['dataType']) {
      const data = {
        column: this.formGroup.value['column'].name,
        type: this.formGroup.value['dataType'],
        initiaType: initialType,
        rename: this.formGroup.value['rename'] != '' ? this.formGroup.value['rename'] : undefined
      };
      this.dataTypeList.push(data);
      const index = this.columnList.findIndex(x => x.name == this.formGroup.value['column'].name);
      this.columnList[index].hide = true;
      this.columnList = cloneDeep(this.columnList);
      this.submitted.set(false);
      // this._toaster.success('Data type added successfully!');
      this.disableCoumnDropDown.set(true);
      setTimeout(() => {
        this.resetColumns();
      }, 100);
    }
  }

  resetColumns() {
    this.formGroup.controls['column'].setValue('');
    this.formGroup.controls['dataType'].setValue('');
    this.formGroup.controls['rename'].setValue('');
  }

  removeType(index: number) {
    const columnItem = this.columnList.find(x => x.name === this.dataTypeList[index].column);
    if (columnItem) {
      columnItem.hide = false;
    }
    this.columnList = [...this.columnList];
    this.dataTypeList.splice(index, 1);
    setTimeout(() => {
      this.resetColumns();
    }, 100);
  }

  checkSelection() {
    const form = this.formGroup.get('columns') as FormArray<FormGroup<ColumnsArray>>;
    return form.controls.every(x => x.value.checked);
  }

  checkDisable(column: string) {
    return this.dataTypeList.some(x => x.column == column);
  }





  drop(event: CdkDragDrop<PrepColumnInterface[]>) {
    this.savedValue = false;
    // moveItemInArray(this.columnArray.value, event.previousIndex, event.currentIndex);
    const from = event.previousIndex;
    const to = event.currentIndex;
    this.moveItemInFormArray(this.columnArray, from, to);
    this.columnSorted.set(JSON.stringify(this.columnList) != JSON.stringify(this.columnListBackup) ? true : false);
  }

  moveItemInFormArray(formArray: FormArray, fromIndex: number, toIndex: number): void {
    const from = this.clamp(fromIndex, formArray.length - 1);
    const to = this.clamp(toIndex, formArray.length - 1);
    if (from === to) {
      return;
    }
    const previous = formArray.at(from);
    formArray.removeAt(from);
    formArray.insert(to, previous);
  }

  /** Clamps a number between zero and a maximum. */
  clamp(value: number, max: number): number {
    return Math.max(0, Math.min(max, value));
  }

  onSelectAll(event: boolean) {
    this.selectAll = event;
    const form = this.formGroup.get('columns') as FormArray<FormGroup<ColumnsArray>>;
    form.controls.forEach((control: FormGroup) => {
      if (!this.checkDisable(control.value.name)) {
        control.patchValue({ checked: event });
        const index = this.columnList.findIndex(x => x.name == control.value.name);
        this.columnList[index].hide = event ? false : true;
      }
    });
    this.columnList = cloneDeep(this.columnList);
    setTimeout(() => {
      this.resetColumns();
    }, 100);
  }

  onSelectRecords(event: boolean) {
    this.selectRecords = event;
  }



  onCancel() {
    this.cancel.emit();
  }

  selectedItem(event: { isSelected: boolean; item: string; }, _index: number) {
    const columnIndex = this.columnList.findIndex(x => x.name == event.item);
    this.columnList[columnIndex].hide = event.isSelected ? false : true;
    this.columnList = cloneDeep(this.columnList);
    setTimeout(() => {
      this.resetColumns();
    }, 100);
  }





  onSave() {
    this.savedValue = true;
    const configration: Record<string, any> = {};
    const removed: string[] = [];
    const removedColumns: { columnName: string, order: number }[] = [];
    this.columnArray.value.forEach((element, index: number) => {
      if (element.checked) {
        configration[element.name] = {
          name: this.dataTypeList.find(x => x.column == element.name)?.rename ?? element.name,
          type: this.dataTypeList.find(x => x.column == element.name)?.type ?? element.dataType,
          order: index + 1
        };
      } else {
        removed.push(element.name);
        removedColumns.push({
          columnName: element.name,
          order: index + 1
        });
      }
    });
    const config = { update: configration, remove: removed };
    this.advanceStore.updateNode(this.currentNodeId ?? '', 'selectool updateded', connectionType.selectTool, config, 'selectool updateded', { configUpdated: true, removedColumns: removedColumns, dataTypeList: this.dataTypeList });
    this._prepService.nodeChangeDetect.next('node updated');
    this.disableCoumnDropDown.set(true);
    // this._toaster.success('Data Saved Successfully');
    this.isConfigUpdated.set(false);
    this.save.emit();
  }

  updateDataInForm(type: { column: string, type: string, rename?: string }, index: number) {
    const columnIndex = this.columnList.findIndex(x => x.name == type.column);
    this.formGroup.controls['column'].setValue(this.columnList[columnIndex]);
    this.formGroup.controls['dataType'].setValue(type.type);
    this.formGroup.controls['rename'].setValue(type.rename);
  }

  ngOnDestroy(): void {
    if (!this._prepService.isConfigSave() && this.columnList.length !== 0) {
      this.onSave();
    }
  }

}

interface NodeData {
  orginalName: string;
  currentName: string;
  type: Type;
  order: number;
  selected: boolean;
}

interface Type {
  name: string;
  value: string;
}

export interface ColumnsArray {
  name: FormControl,
  checked: FormControl,
  dataType: FormControl,
  rename: FormControl
}
