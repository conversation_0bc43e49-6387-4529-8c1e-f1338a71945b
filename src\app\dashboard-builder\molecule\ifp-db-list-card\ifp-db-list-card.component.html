<div class="ifp-db-list-card">
  <div class="ifp-db-list-card__wrapper">
    <!-- <ifp-kebab-menu class="ifp-db-list-card__options" (optionSelected)="onSelectOptions($event)"></ifp-kebab-menu> -->
    <div class="ifp-db-list-card__thumb" (click)="onClickCard()">
      <div class="ifp-db-list-card__thumb-img" [style.background-image]="'url('+thumb+')'"></div>
      <!-- <img [src]="thumb" [alt]="title | translate" class="ifp-db-list-card__thumb-img"> -->

      <div class="ifp-db-list-card__tools-wrapper">
        <div class="ifp-db-list-card__icon">
          <em class="ifp-icon ifp-icon-edit"></em>
        </div>

        <div class="ifp-db-list-card__icon">
          <em class="ifp-icon ifp-icon-share"></em>
        </div>
        <div class="ifp-db-list-card__icon">
          <em class="ifp-icon ifp-icon-trash"></em>
        </div>


      </div>
    </div>
    <div class="ifp-db-list-card__footer">
      <div class="ifp-db-list-card__title-wrapper">
        <h3 class="ifp-db-list-card__title">{{title | translate}}</h3>

      </div>
      @if (selectedTab === 'Sent') {
      <h4 class="ifp-db-list-card__sub-title">{{'Total recipients' | translate}} : <span
          class="ifp-db-list-card__sub-title-value">{{recipientsCount}}</span></h4>
      }
      <div class="ifp-db-list-card__action-wrapper">
        <p class="ifp-db-list-card__text">{{date | date: dateFormat.mediumDate}}</p>
        @if (selectedTab === 'Dashboards') {
        <app-ifp-checkbox [appIfpTooltip]="'Check to select dashboard for share' | translate" [extraSpaceTop]="20"
          [type]="'checkbox'" [enableFor]="true" [hideLabel]="true" [id]="id" (checkedEvent)="selectNode($event)"
          [defualtChecked]="getChecked(id)"></app-ifp-checkbox>
        }
      </div>
    </div>
  </div>
