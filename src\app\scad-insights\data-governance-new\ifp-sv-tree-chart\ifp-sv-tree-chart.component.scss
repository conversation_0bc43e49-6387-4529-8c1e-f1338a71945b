@use "../../../../assets/ifp-styles/abstracts/index" as *;
$line-width: $spacer-3;
.ifp-sv {
  display: flex;
  align-items: center;
  border-radius: 10px;
  background-color: $ifp-color-violet-light;
  padding: $spacer-4;
  position: relative;
    &::after {
      content: "";
      width: 118px;
      height: 50%;
      background-image: url(../../../../assets/images/sv-join-line.svg);
      background-size: cover;
      background-position: center;
      position: absolute;
      top: 50%;
      left: 200px + $spacer-4 - 4px;
      transform: translateY(-50%);
    }
  &__box-count {
    font-size: $ifp-fs-6;
    font-weight: $fw-bold;
  }
  &__text {
    font-weight: $fw-regular;
  }
  &__root-main,
  &__branch-root {
    min-width: 200px;
    box-shadow: 0 2px 2px $ifp-color-black-16;
  }
  &__root-main {
    text-align: center;
    background-color: $ifp-color-section-white;
    padding: $spacer-3;
    border-radius: 100px;
    margin-inline-end: 110px;
    position: relative;
    z-index: 1;
  }
  &__branch-wrapper {
    width: 100%;
    position: relative;
    z-index: 1;
  }
  &__branch-root {
    padding: $spacer-3;
    margin-inline-end: $spacer-3;
    border-radius: 10px;
  }
  &__box-wrapper {
    width: calc(33.33% - (2 * $spacer-3));
    margin: $spacer-0 $spacer-3;
    display: flex;
    flex-direction: column;
    justify-content: center;
  }
  &__box {
    padding: $spacer-3;
    border-radius: 10px;
    box-shadow: 0 2px 2px $ifp-color-black-16;
  }
  &__sub-branch {
    display: flex;
    width: 100%;
    .ifp-sv__box-count {
      font-size: $ifp-fs-4;
    }
  }
  &__branch {
    $gap: 24px;
    height: 260px;
    display: flex;
    align-items: center;
    color: $ifp-color-white-global;
    &--top {
      .ifp-sv {
        &__branch-root,
        &__box {
          background: linear-gradient(90deg, #474D81, #9B9DB6);
        }
        &__box {
          width: calc(33.33% - (2 * $spacer-3));
          margin: $gap $spacer-3;
          position: relative;
          &::before {
            content: "";
            width: 25px;
            border-inline-end: 1px solid $ifp-color-grey-5;
            position: absolute;
            left: 50%;
            transform: translateX(-50%);
          }
          &:nth-child(odd)::before {
            height: $gap + 1px;
            border-bottom: 1px solid $ifp-color-grey-5;
            border-bottom-right-radius: 40px;
            top: 100%;
          }
          &:nth-child(even)::before {
            height: $gap;
            bottom: 100%;
            border-top: 1px solid $ifp-color-grey-5;
            border-top-right-radius: 40px;
          }
        }
        &__sub-branch {
          flex-wrap: wrap;
          height: 100%;
          flex-direction: column;
          justify-content: center;
          position: relative;
          &::before {
            content: "";
            width: calc(100% - (33.33%/2) + $spacer-2);
            height: 1px;
            background-color: $ifp-color-grey-5;
            position: absolute;
            top: 50%;
            left: (-$spacer-3);
          }
        }
      }
    }
    &--bottom {
      .ifp-sv {
        &__branch-root,
        &__box {
          background: linear-gradient(90deg, #3A5FF5, #99AEFD);
        }
        &__box {
          margin: $spacer-2 $spacer-0;
          background-color: $ifp-color-violet-2;
          position: relative;
          &::before {
            content: "";
            width: $line-width;
            height: 1px;
            border-top: 1px solid $ifp-color-violet-2;
            position: absolute;
            top: 50%;
            right: 100%;
          }
          &:first-child,
          &:last-child {
            &::before {
              border-inline-start: 1px solid $ifp-color-violet-2;
              transform: translateY(-50%);
              height: 10px;
            }
          }
          &:first-child::before {
            border-top-left-radius: 5px;
          }
          &:last-child::before {
            border-top: none;
            border-bottom: 1px solid $ifp-color-violet-2;
            border-bottom-left-radius: 5px;
          }
        }
        &__branch-root {
          background-color: $ifp-color-violet-2;
        }
        &__box-wrapper {
          position: relative;
          &::before,
          &::after {
            content: "";
            background-color: $ifp-color-violet-2;
            position: absolute;
            top: 50%;
          }
          &::before {
            width: $line-width;
            height: 1px;
            left: (-$spacer-3 * 2);
          }
          &::after {
            width: 1px;
            height: 66.66%;
            left: (-$line-width);
            transform: translateY(-50%);
          }
        }
      }
    }
  }
  &__table-wrapper {
    width: calc(66.66% - (2 * $spacer-3));
    margin: $spacer-2 $spacer-3;
    position: relative;
    max-height: 250px;
    @include ifp-scroll-y(transparent, $ifp-color-grey-7, 10px, 10px)
  }
  &__table {
    width: 100%;
    color: $ifp-color-primary-grey;
    position: relative;
  }
  &__table-row {
    &--head {
      color: $ifp-color-white-global;
      .ifp-sv__table-col {
        font-weight: $fw-semi-bold;
        background-color: $ifp-color-violet-2;
        border-bottom: none;
        position: sticky;
        top: 0;
        left: 0;
        &:first-child {
          border-radius: 10px 0 0 10px;
        }
        &:last-child {
          border-radius: 0 10px 10px 0;
        }
      }
    }
  }
  &__table-col {
    text-align: center;
    padding: $spacer-3;
    border-bottom: 1px solid $ifp-color-grey-5;
    &:first-child {
      text-align: start;
    }
  }
  &__table-tick {
    color: $ifp-color-green-dark;
  }
  &__table-cross {
    color: $ifp-color-red;
  }
}

:host-context([dir="rtl"]) {
  .ifp-sv {
    &::after {
      left: auto;
      right: 200px + $spacer-4 - 4px;
      transform: translateY(-50%) rotateY(180deg);
    }
    &__branch {
      &--top {
        .ifp-sv__branch-root,
        .ifp-sv__box {
          background: linear-gradient(-90deg, #474D81, #9B9DB6);
        }
        .ifp-sv__box {
          &::before {
            left: auto;
            right: 50%;
            transform: translateX(50%);
          }
          &:nth-child(odd)::before {
            border-bottom-right-radius: 0;
            border-bottom-left-radius: 40px;
          }
          &:nth-child(even)::before {
            border-top-right-radius: 0;
            border-top-left-radius: 40px;
          }
        }
        .ifp-sv__sub-branch::before {
          left: auto;
          right: (-$spacer-3);
        }
      }
      &--bottom {
        .ifp-sv {
          &__branch-root,
          &__box {
            background: linear-gradient(-90deg, #3A5FF5, #99AEFD);
          }
          &__box {
            &::before {
              right: auto;
              left: 100%;
            }
            &:first-child::before {
              border-top-left-radius: 0;
              border-top-right-radius: 5px;
            }
            &:last-child::before {
              border-bottom-left-radius: 0;
              border-bottom-right-radius: 5px;
            }
          }
          &__box-wrapper {
            &::before {
              left: auto;
              right: (-$spacer-3 * 2);
            }
            &::after {
              left: auto;
              right: (-$line-width);
              transform: translateY(-50%);
            }
          }
        }
      }
    }
    &__table-row {
      &--head {
        .ifp-sv__table-col {
          left: auto;
          right: 0;
          &:first-child {
            border-radius: 0 10px 10px 0;
          }
          &:last-child {
            border-radius: 10px 0 0 10px;
          }
        }
      }
    }
  }
}
