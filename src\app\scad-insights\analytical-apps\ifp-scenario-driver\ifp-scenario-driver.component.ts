import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, ChangeDetectorRef } from '@angular/core';
import { IfpAnalysisCardComponent } from '../../ifp-widgets/ifp-organism/ifp-analysis-card/ifp-analysis-card.component';
import { IfpScenarioDriverCardComponent } from '../../ifp-widgets/ifp-molecules/ifp-scenario-driver-card/ifp-scenario-driver-card.component';
import { IfpBreadcrumbsComponent } from '../../ifp-widgets/ifp-molecules/ifp-breadcrumbs/ifp-breadcrumbs.component';
import { ActivatedRoute } from '@angular/router';
import { Store } from '@ngrx/store';
import { SubSink } from 'subsink';
import { Title } from '@angular/platform-browser';
import { PageData } from 'src/app/scad-insights/core/interface/molecule/breadcrumb.interface';
import { IfpCarouselComponent } from '../../ifp-widgets/ifp-atoms/ifp-carousel/ifp-carousel.component';
import { IfpWhatsNewCardComponent } from 'src/app/scad-insights/ifp-widgets/ifp-molecules/ifp-whats-new-card/ifp-whats-new-card.component';
import { classCarousel } from 'src/app/scad-insights/core/constants/caroisel.constants';
import { CommonModule } from '@angular/common';
import { IfpCarouselItemDirective } from 'src/app/scad-insights/core/directives/Ifp-carousel-item.directive';
import { TranslateModule } from '@ngx-translate/core';
import { IFPMsalService } from '../../core/services/IFP-msal.service';
import { title } from '../../core/constants/header.constants';


@Component({
    selector: 'app-ifp-scenario-driver',
    templateUrl: './ifp-scenario-driver.component.html',
    styleUrls: ['./ifp-scenario-driver.component.scss'],
    imports: [IfpAnalysisCardComponent, IfpScenarioDriverCardComponent, IfpBreadcrumbsComponent, IfpCarouselComponent, CommonModule, IfpCarouselItemDirective,
        TranslateModule]
})
export class IfpScenarioDriverComponent implements OnDestroy {

  @ViewChild('leftContainer') leftContainer!: IfpCarouselComponent;
  @ViewChildren('leftCard') leftCard!: QueryList<IfpWhatsNewCardComponent>;

  analyticalId!: number;
  analyticalData: any = [];
  public subs = new SubSink();
  public relatedScenarions: any = [];
  public activeLeft: number | null = null;
  public carousalClass = classCarousel.dynamic;
  public active: number | null = null;

  pageData!: PageData[];

  public customOptions = {
    freeMode: true,
    slidesPerView: 'auto',
    updateOnWindowResize: true,
    watchSlidesProgress: true
  };

  constructor(private route: ActivatedRoute, private store: Store, private _titleService: Title, private _cdr: ChangeDetectorRef, private _msalService:IFPMsalService) {
    this.subs.add(
      this.route.params.subscribe(val => {
        this.analyticalId = val['id'];
      })
    );
  }

  createPageData(data: any) {
    if (data?.title) {
      this._titleService.setTitle(`${title.bayaan} | ${data.title}`);
      if (data?.title) {
        (window as any)?.dataLayer?.push({
          'event': 'page_load',
          'page_title_var': data.title,
          'userId': this._msalService.convertEmail(this._msalService.getLoginData.account.username)
        });
      }
      this.pageData = [
        {
          title: 'Home',
          route: '/home'
        },
        {
          title: data.parent,
          route: `/domain-exploration/${data.parent}/${data.id}`,
          queryParams: { key: data.content_classification_key, tabName: data.content_classification }
        },
        {
          title: data.title,
          route: ''
        }
      ];
    }
  }

  getRelatedScenarios(event: any) {
    this.relatedScenarions = [];
    if (event && event[0] != '') {
      this.relatedScenarions = event;
    }
  }

  slideClicked() {
    if (this.leftCard) {
      this.leftCard.toArray().forEach((element) => {
        element.removeTooltip();
      });
    }
  }

  resizedLeft(index: number | null, realIndex: number) {

    this.activeLeft = index;
    this.leftContainer.setActiveIndex(realIndex);
    setTimeout(() => {
      this.leftContainer.update();
      this.leftContainer.setActiveIndex(realIndex);
    }, 300);
  }

  resizedEvent(event: boolean, index: number) {
    if (event !== null || event !== undefined) {
      this.active = index;
      this._cdr.detectChanges();
    } else if (!event && this.active === index) {
      this.active = null;
    }

    if (event !== null || event !== undefined) {
      setTimeout(() => {
        this._cdr.detectChanges();
        this.leftContainer.swiperTranslateTo(event);
      }, 310);
    } else {
      this._cdr.detectChanges();
      this.leftContainer.swiperTranslateTo(event);
    }
  }


  ngOnDestroy(): void {
    this.subs.unsubscribe();
  }


}
