import { Component, inject, input, InputSignal, OnDestroy, output } from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';
import { IfpButtonComponent } from '../../../scad-insights/ifp-widgets/ifp-atoms/ifp-button/ifp-button.component';
import { buttonClass } from 'src/app/scad-insights/core/constants/button.constants';
import { NgClass } from '@angular/common';
import { Router } from '@angular/router';

@Component({
  selector: 'ifp-exploratory-select-page',
  templateUrl: './ifp-exploratory-select-page.component.html',
  styleUrl: './ifp-exploratory-select-page.component.scss',
  imports: [TranslateModule, IfpButtonComponent, NgClass]
})
export class IfpExploratorySelectPageComponent implements OnDestroy {

  public cancelModal = output();
  public cardData: InputSignal<SelectPageCardData[]> = input([{
    id: '',
    title: '',
    description: '',
    url: '',
    color: '',
    icon: '',
    external: false as boolean
  }]);
  public isModal: InputSignal<boolean> = input<boolean>(true);
  public heading = input<string>('');
  public description = input<string>('');

  private readonly _router: Router = inject(Router);

  public buttonClass = buttonClass;
  public selectedCardIndex: number = 0;

  onSelection(index: number) {
    this.selectedCardIndex = index;
  }

  nextPage() {
    if (this.cardData()[this.selectedCardIndex].external) {
      window.open(this.cardData()[this.selectedCardIndex].url);
      return;
    }
    this._router.navigateByUrl(this.cardData()[this.selectedCardIndex].url);
    if (this.isModal()) {
      this.cancelModal.emit();
    }
  }

  onCancel() {
    this.selectedCardIndex = 0;
    if (this.isModal()) {
      this.cancelModal.emit();
    }
  }

  ngOnDestroy(): void {
    if (this.isModal()) {
      this.cancelModal.emit();
    }
  }

}

export interface SelectPageCardData {
  id: string;
  title: string;
  description: string;
  url: string;
  color: string;
  icon: string;
  external: boolean
}
