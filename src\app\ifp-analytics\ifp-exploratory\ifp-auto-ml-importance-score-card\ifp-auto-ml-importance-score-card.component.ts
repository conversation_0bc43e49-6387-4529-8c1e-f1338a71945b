import { After<PERSON>iewInit, ChangeDetector<PERSON>ef, Component, ElementRef, EventEmitter, Input, OnChanges, OnDestroy, Output, QueryList, Renderer2, ViewChild, ViewChildren } from '@angular/core';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { buttonClass } from 'src/app/scad-insights/core/constants/button.constants';
import { IfpTabComponent } from '../../../scad-insights/ifp-widgets/ifp-molecules/ifp-tab/ifp-tab.component';
import { IfpAutoMlColumnChartComponent } from '../ifp-auto-ml-column-chart/ifp-auto-ml-column-chart.component';
import { NgClass } from '@angular/common';
import { ThemeService } from 'src/app/scad-insights/core/services/theme/theme.service';
import { SubSink } from 'subsink';
import { IfpAnalyticLineChartComponent } from '../../../scad-insights/ifp-widgets/ifp-molecules/ifp-analytic-line-chart/ifp-analytic-line-chart.component';
import { IfpDataTableComponent } from '../../organism/ifp-data-table/ifp-data-table.component';
import { IterationList } from '../ifp-exploratory-auto-ml-analyze/ifp-exploratory-auto-ml-analyze.component';
import { IfpTooltipDirective } from 'src/app/scad-insights/core/directives/ifp-tooltip.directive';
import { IfpAutoMlChartDataComponent } from '../ifp-auto-ml-chart-data/ifp-auto-ml-chart-data.component';

@Component({
    selector: 'ifp-auto-ml-importance-score-card',
    imports: [TranslateModule, IfpTabComponent, IfpAutoMlColumnChartComponent,
    NgClass, IfpAnalyticLineChartComponent,
    IfpDataTableComponent, IfpTooltipDirective, IfpAutoMlChartDataComponent],
    templateUrl: './ifp-auto-ml-importance-score-card.component.html',
    styleUrl: './ifp-auto-ml-importance-score-card.component.scss'
})
export class IfpAutoMlImportanceScoreCardComponent implements AfterViewInit, OnDestroy, OnChanges {
  @ViewChildren('chart') columnChartValue!: QueryList< IfpAutoMlChartDataComponent>;
  @ViewChildren('variables')  variables!: QueryList< ElementRef>;
  @ViewChild('variableHead') variableHead!: ElementRef;
  @Input() columnList: string[]= [];


  @Input() iterationList: IterationList[] = [];

  public previewTableData : {heading: string[], data: {
    'key': string;
    'title': string | number;
    'value': string | number;
    'type': string;
  }[][] } = {
      heading: ['Column', 'Rank', 'Importance Score'],
      data: [[]]
    };

  public headingSettings = {
    Column: {
      sort: true
    },
    Rank: {
      sort: true
    },
    'Importance Score': {
      sort: true
    }
  };


  @Input() result = false;
  @Input() selectedIteration  = '';
  @Output() reAnalyzeEvent = new EventEmitter();
  @Output() selectedIterationEvent = new EventEmitter();
  @Output() runScenarioEvent = new EventEmitter();

  public indexTableSelection = 1;
  public indexResultSelection = 1;
  public buttonClass  = buttonClass;
  public labelList: {iconClass: string; event: string;}[] = [
    {
      event: 'table',
      iconClass: 'ifp-icon-detial-view'
    },
    { event: 'chart',
      iconClass: 'ifp-icon-analysis'
    }
  ];

  public labelCompare: {name: string;}[] = [
    {
      name: 'Results'
    },
    { name: 'Comparison'
    }
  ];

  public yAxis= [
    {
      name: this._translate.instant('Importance Score'),
      data: [0
      ],
      'dashStyle': 'Solid',
      'type': 'column',
      'zIndex': 1,
      'lineWidth': 0,
      'color': '#3BD6AD',
      'marker': {
        'fillColor': '#3BD6AD',
        'lineWidth': 0,
        'lineColor': '#3BD6AD',
        'symbol': 'circle',
        'enabled': true
      }
    }
  ];

  public xAxis: string[] =[];

  public subs = new SubSink();


  constructor(private _render: Renderer2, private _cdr: ChangeDetectorRef,
    private _themeService:ThemeService,
    private _translate: TranslateService
  ) {

  }

  ngOnChanges(): void {
    this.setChartValue();
  }

  // setting value to result page
  setChartValue() {
    this.xAxis = [];
    this.yAxis[0].data = [];
    this.previewTableData.data = [];
    const index = this.iterationList.findIndex(data => data.object_id === this.selectedIteration);
    if (index !== -1) {
      this.iterationList?.[index]?.metadata?.['selected column meta'].forEach(data => {
        this.xAxis.push(data.column ?? '');
        this.yAxis[0].data.push(data.importance_score ?? 0);
        this.previewTableData.data.push([{
          'key': 'Column',
          'title': 'Column',
          'value': data.column,
          'type': 'default'
        },
        {
          'key': 'Rank',
          'title': 'Column',
          'value': data.rank,
          'type': 'default'
        },
        {
          'key': 'Importance Score',
          'title': 'Column',
          'value': data.importance_score,
          'type': 'default'
        }]);
      });
    }

  }

  // used to switch  result and comparison
  selectedValue(event: {event: string, index: number}) {
    this.indexTableSelection = event.index;
    this._cdr.detectChanges();
    this.setRectWidth();
  }

  // used to switch table view and bar chart view
  selectedValueResult(event: {event: string, index: number}) {
    this.indexResultSelection = event.index;
    if (this.indexResultSelection === 1) {
      this.result =  false;
      this._cdr.detectChanges();
      this.setRectWidth();
    } else {
      this.result =  true;
    }
  }


  // used to sort value for important score table
  sortEvent(data: {sortValue:string, sortEventName: string, sortEvent: boolean }) {
    const index = this.previewTableData.data[0].findIndex(dataValue => dataValue.key === data.sortEventName);
    if (index !== -1) {
      if (typeof this.previewTableData.data[0][index].value === 'string') {
        if (data.sortEvent) {
          this.previewTableData.data.sort((a, b) =>( b[index].value as string).localeCompare(a[index].value  as string));
        } else {
          this.previewTableData.data.sort((a, b) =>( a[index].value as string).localeCompare(b[index].value  as string));
        }
      } else if (data.sortEvent) {
        this.previewTableData.data.sort((a, b) =>( +b[index].value - +a[index].value ));
      } else {
        this.previewTableData.data.sort((a, b) =>( +a[index].value - +b[index].value ));
      }
    }

  }


  // used to select active tab in the result view
  activeTab(item: IterationList) {
    this.selectedIterationEvent.emit(item);
    this.setChartValue();
  }

  // used set variable rectangle width
  ngAfterViewInit(): void {
    this.setRectWidth();
    this.subs.add( this._themeService.fontChange.subscribe(() => {
      this.setRectWidth();
    }));
  }

  // used set variable rectangle width
  setRectWidth() {
    const offset =  this.columnChartValue.first.getElement.nativeElement.clientHeight;
    this._render.setStyle(this.variableHead.nativeElement, 'height', `${offset}px` );
  }

  // used to emit value while click re analyze
  reAnalyze() {
    this.reAnalyzeEvent.emit();
  }

  // used to select iteration

  selectIteration(item: IterationList) {
    this.selectedIterationEvent.emit(item);
  }

  // run scenarioEvent
  runScenario() {
    this.runScenarioEvent.emit();
  }

  ngOnDestroy(): void {
    this.subs.unsubscribe();
  }
}

