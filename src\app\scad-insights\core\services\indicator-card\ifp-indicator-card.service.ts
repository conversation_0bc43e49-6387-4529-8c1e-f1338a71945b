import { Injectable } from '@angular/core';
import { Security } from '../../interface/indicator.interface';
import { ifpColors } from '../../constants/color.constants';

@Injectable({
  providedIn: 'root'
})
export class IfpIndicatorCardService {
  setSecurity (security: Security) {
    const name = security.name.split('-');
    let color = '';
    if (name[0].trim() === '0') {
      color = ifpColors.greenDark1;
    } else {
      color = ifpColors.red;
    }
    return {...security, color: color, name: security.label ?? name.pop()?.trim(), description: security.description};
  }
}
