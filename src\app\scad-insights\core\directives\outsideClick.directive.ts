import { Directive, ElementRef, HostListener, Output, Renderer2, EventEmitter } from '@angular/core';

@Directive({
  selector: '[appOutsideClick]',
  standalone: true
})
export class OutsideClickDirective {
  @Output() outsideClick = new EventEmitter();
  @HostListener('document:click', ['$event']) toggleOpen(event: Event) {
    if (!this.elRef.nativeElement.contains(event.target)) {
      this.outsideClick.emit(event);
    }
  }

  constructor(private elRef: ElementRef, private render: Renderer2) {}
}
