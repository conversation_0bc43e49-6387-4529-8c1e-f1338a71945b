import { Component, <PERSON><PERSON><PERSON><PERSON>, OnInit } from '@angular/core';
import { Title } from '@angular/platform-browser';
import { TranslateModule } from '@ngx-translate/core';
import { IFPMsalService } from '../../core/services/IFP-msal.service';
import { UsageDashboardLogService } from '../../core/services/usage-dashboard-log/usage-dashboard-log.service';
import { SubSink } from 'subsink';
import { ActivatedRoute } from '@angular/router';
import { Store } from '@ngrx/store';
import { selectAnalyticGetById } from '../../home/<USER>/Analytical apps/analyticalApps.selector';
import { environment } from 'src/environments/environment';
import { Security } from '../../core/interface/indicator.interface';
import { IfpIndicatorCardService } from '../../core/services/indicator-card/ifp-indicator-card.service';
import { getIndicatorDetail } from '../../home/<USER>/Analytical apps/analyticalApps.action';
import { PageData } from '../../core/interface/molecule/breadcrumb.interface';
import { IfpBreadcrumbsComponent } from '../../ifp-widgets/ifp-molecules/ifp-breadcrumbs/ifp-breadcrumbs.component';
import { IfpCorrelationCardComponent } from '../../ifp-widgets/ifp-organism/ifp-correlation-card/ifp-correlation-card.component';
import {  KeyValuePipe, NgClass } from '@angular/common';
import { logType } from '../../core/services/usage-dashboard-log/usage-dashboard.constants';
import { IfpCardLoaderComponent } from '../../ifp-widgets/ifp-atoms/ifp-card-loader/ifp-card-loader.component';
import { loaderType } from '../../core/constants/loader.constants';
import { title } from '../../core/constants/header.constants';
import { IfpToolDownloadComponent } from '../../../ifp-analytics/molecule/ifp-tool-bar/ifp-tool-download/ifp-tool-download.component';
import { buttonClass, buttonIconPosition } from '../../core/constants/button.constants';
import { CommonApiService } from '../../core/services/common-api/commonApi.service';
import { IfpIconButtonComponent } from '../../ifp-widgets/ifp-atoms/ifp-icon-button/ifp-icon-button.component';
import { OutsideClickDirective } from '../../core/directives/outsideClick.directive';
import { IfpTagComponent } from 'src/app/ifp-analytics/atom/ifp-tag/ifp-tag.component';

@Component({
    selector: 'ifp-correlation',
    imports: [IfpCorrelationCardComponent, TranslateModule, IfpBreadcrumbsComponent, IfpCorrelationCardComponent, NgClass, KeyValuePipe, IfpCardLoaderComponent, IfpToolDownloadComponent, IfpIconButtonComponent, OutsideClickDirective, IfpTagComponent],
    templateUrl: './ifp-correlation.component.html',
    styleUrl: './ifp-correlation.component.scss'
})
export class IfpCorrelationComponent implements OnInit, OnDestroy {

  private sessionId!: string;
  public title: string = 'LMO';
  public subsink: SubSink = new SubSink();
  public analyticalId!: string;
  public response: any = [];
  public isChartLoader: boolean | undefined = true;
  public chartCards: any = [];
  public security!: Security;
  public appType!: string;
  public isInitialLoad: boolean = true;
  public loaderType = loaderType;
  public excelData: Record<string, string | number>[] = [];
  public buttonClass = buttonClass;
  public buttonIconPosition = buttonIconPosition;
  public showDownload: boolean = false;
  public pageData: PageData[] = [
    {
      title: 'Home',
      route: '/',
      disable: false
    },
    {
      title: 'Correlation',
      route: '/',
      disable: false
    }
  ];

  public tabData: Record<string, TabData> = {};
  public tncState!: boolean;
  public fileTypes: string[] = ['pdf', 'png', 'xl'];

  constructor (private _titleService: Title,  private _msalService: IFPMsalService, private log: UsageDashboardLogService, private route: ActivatedRoute, private store: Store,  private _cardService: IfpIndicatorCardService, private _commonApiService: CommonApiService) {

    this.subsink.add(
      this.route.params.subscribe(val => {
        this.analyticalId = val['id'];
      })
    );
  }

  ngOnInit() {
    this.getNodeDetails();
    if (this.sessionId) {
      this.log.logEnds(this.sessionId, this.log.currentTime );
    }
    this.sessionId = this.log.createUUid;
    this.log.logStart(this.sessionId, logType.indicator, this.log.currentTime,  +this.analyticalId );
  }

  async getNodeDetails(filters: any = {}) {
    await this.dispatchApp(filters).then(() => {
      this.setNodeDetails();
      this.getTnCDownloadStatus();
    });
  }

  setNodeDetails() {
    this.store.select(selectAnalyticGetById(this.analyticalId.toString())).subscribe(resp => {
      this.response = resp;
      this.title = resp?.body?.component_title;
      this.isChartLoader = resp.loader;
      this.chartCards = resp.body?.indicatorVisualizations?.visualizationsMeta;
      // this.chartCards?.forEach((chart: any) => {
      //   chart.seriesMeta.map((series: any) => series.id = chart.id);
      // });
      if (this.response.body?.domain) {
        this.createPageData();
      }
      if (this.response.body && this.isInitialLoad) {
        this._titleService.setTitle(`${title.bayaan} | ${this.title}`);
        if (this.title) {
          (window as any)?.dataLayer?.push({
            'event': 'page_load',
            'page_title_var': this.title,
            'userId': this._msalService.convertEmail(this._msalService.getLoginData.account.username)
          });
        }
        this.appType = resp.body?.type;
        if (this.response?.body?.security && environment.env !== 'demo') {
          this.security = this._cardService.setSecurity(this.response?.body?.security);
        }
        this.createTabData();
        this.isInitialLoad = false;
      }
      if (this.response.body) {
        this.setExcelData();
      }
    });
  }

  dispatchApp(filters: any): Promise<void> {
    return new Promise((resolve) => {
      this.store.dispatch(getIndicatorDetail({
        id: this.analyticalId.toString(),
        contentType: 'analytical-apps',
        indicatorDrivers: filters
      }));
      resolve();
    });
  }

  createPageData() {
    this.pageData = [
      {
        title: 'Home',
        route: '/home'
      },
      {
        title: this.response.body?.domain,
        route: `/domain-exploration/${this.response.body?.domain}/${this.response.body?.domain_id}`,
        queryParams: { key: 'analytical_apps', tabName: 'Analytical Apps' }
      },
      {
        title: this.response.body?.component_title,
        route: '/',
        disable: false
      }
    ];
  }


  createTabData() {
    this.tabData = {};
    const filterPanel = this.response.body?.filterPanel?.properties;
    if (filterPanel?.length) {
      filterPanel.forEach((element: any) => {
        const tab = {
          [element.label]: {
            default: element.default,
            options: this.getFilterOptions(element.options, element.default),
            key: element.path
          }
        };
        this.tabData = {...this.tabData, ...tab};
      });
    }
  }

  getFilterOptions(options: string[], defaultOption: string) {
    const optionData = options.map((option: string) => {
      return {name: option, isSelected: option === defaultOption};
    });
    return optionData;
  }

  selectFilter(name: string, value: Option) {
    this.tabData[name].options.forEach((option: Option) => {
      if (value.name === option.name) {
        option.isSelected = true;
      } else {
        option.isSelected = false;
      }
    });
    let correlationFilters = {};
    for (const key in this.tabData) {
      if (Object.hasOwn(this.tabData, key)) {
        correlationFilters = {...correlationFilters, ...{[this.tabData[key].key]: this.tabData[key].options.find((x: Option) => x.isSelected)?.name}};
      }
    }
    this.getNodeDetails(correlationFilters);

    // {
    //   "indicatorDrivers":{},
    //   "correlationFilters":{
    //       "CITIZENSHIP":"Citizen",
    //       "TYPE":"Unemployment"
    //   }
    // }
  }

  setExcelData() {
    this.excelData = [];
    let downloadData = {};
    Object.keys(this.tabData).forEach((key: string) => {
      downloadData = {...downloadData, [key]: this.tabData[key].options.find((x: Option) => x.isSelected)?.name};
    });
    this.chartCards.forEach((item: any) => {
      const category = Object.keys(item.filterBy)[0];
      item.seriesMeta[0].data.forEach((data: any) => {
        downloadData = {...downloadData, [category]: item.filterBy[category]};
        downloadData = {...downloadData, ...data};
        this.excelData.push(downloadData);
      });
    });
  }

  getTnCDownloadStatus() {
    this.subsink.add(this._commonApiService.getDownloadTermsStatus(this.analyticalId).subscribe((res: any) => {
      if (res) {
        if (res.status) {
          this.tncState = res.status;
        }
      }
    }));
  }

  onDownloadClick() {
    this.showDownload = false;
  }

  outsideClick() {
    this.showDownload = false;
  }

  ngOnDestroy(): void {
    this.subsink.unsubscribe();
    if (this.sessionId) {
      this.log.logEnds(this.sessionId, this.log.currentTime );
    }
  }
}

interface TabData {
  default: string;
  options: Option[];
  key: string;
}

interface Option {
  name: string;
  isSelected: boolean;
}
